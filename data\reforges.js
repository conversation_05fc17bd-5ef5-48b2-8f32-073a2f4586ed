// Reforge system data for Disblock
// Based on Hypixel SkyBlock reforging mechanics

const REFORGE_DATA = {
  // Weapon reforges (<PERSON><PERSON>, Bows)
  WEAPON: {
    fair: {
      name: "Fair",
      stats: {
        Common: { STRENGTH: 2, CRIT_DAMAGE: 2, INTELLIGENCE: 2 },
        Uncommon: { STRENGTH: 3, CRIT_DAMAGE: 3, INTELLIGENCE: 3 },
        Rare: { STRENGTH: 4, CRIT_DAMAGE: 4, INTELLIGENCE: 4 },
        Epic: { STRENGTH: 6, CRIT_DAMAGE: 6, INTELLIGENCE: 6 },
        Legendary: { STRENGTH: 8, CRIT_DAMAGE: 8, INTELLIGENCE: 8 },
        Mythic: { STRENGTH: 10, CRIT_DAMAGE: 10, INTELLIGENCE: 10 },
      },
    },
    epic: {
      name: "Epic",
      stats: {
        Common: { STRENGTH: 15, CRIT_DAMAGE: 10, INTELLIGENCE: -5 },
        Uncommon: { STRENGTH: 20, CRIT_DAMAGE: 15, INTELLIGENCE: -5 },
        Rare: { STRENGTH: 25, CRIT_DAMAGE: 20, INTELLIGENCE: -5 },
        Epic: { STRENGTH: 32, CRIT_DAMAGE: 25, INTELLIGENCE: -5 },
        Legendary: { STRENGTH: 40, CRIT_DAMAGE: 32, INTELLIGENCE: -5 },
        Mythic: { STRENGTH: 50, CRIT_DAMAGE: 40, INTELLIGENCE: -5 },
      },
    },
    fast: {
      name: "Fast",
      stats: {
        Common: { STRENGTH: 2, CRIT_DAMAGE: 2 },
        Uncommon: { STRENGTH: 3, CRIT_DAMAGE: 3 },
        Rare: { STRENGTH: 4, CRIT_DAMAGE: 4 },
        Epic: { STRENGTH: 6, CRIT_DAMAGE: 6 },
        Legendary: { STRENGTH: 8, CRIT_DAMAGE: 8 },
        Mythic: { STRENGTH: 10, CRIT_DAMAGE: 10 },
      },
    },
    gentle: {
      name: "Gentle",
      stats: {
        Common: { STRENGTH: 3, INTELLIGENCE: 5 },
        Uncommon: { STRENGTH: 4, INTELLIGENCE: 8 },
        Rare: { STRENGTH: 5, INTELLIGENCE: 10 },
        Epic: { STRENGTH: 7, INTELLIGENCE: 15 },
        Legendary: { STRENGTH: 9, INTELLIGENCE: 20 },
        Mythic: { STRENGTH: 11, INTELLIGENCE: 25 },
      },
    },
    heroic: {
      name: "Heroic",
      stats: {
        Common: { STRENGTH: 15, INTELLIGENCE: 40 },
        Uncommon: { STRENGTH: 20, INTELLIGENCE: 50 },
        Rare: { STRENGTH: 25, INTELLIGENCE: 65 },
        Epic: { STRENGTH: 32, INTELLIGENCE: 80 },
        Legendary: { STRENGTH: 40, INTELLIGENCE: 100 },
        Mythic: { STRENGTH: 50, INTELLIGENCE: 125 },
      },
    },
    legendary: {
      name: "Legendary",
      stats: {
        Common: { STRENGTH: 3, CRIT_DAMAGE: 5, INTELLIGENCE: 8 },
        Uncommon: { STRENGTH: 7, CRIT_DAMAGE: 9, INTELLIGENCE: 12 },
        Rare: { STRENGTH: 12, CRIT_DAMAGE: 14, INTELLIGENCE: 18 },
        Epic: { STRENGTH: 18, CRIT_DAMAGE: 20, INTELLIGENCE: 25 },
        Legendary: { STRENGTH: 25, CRIT_DAMAGE: 28, INTELLIGENCE: 35 },
        Mythic: { STRENGTH: 32, CRIT_DAMAGE: 35, INTELLIGENCE: 45 },
      },
    },
    odd: {
      name: "Odd",
      stats: {
        Common: { CRIT_CHANCE: 12, CRIT_DAMAGE: -5, INTELLIGENCE: 10 },
        Uncommon: { CRIT_CHANCE: 15, CRIT_DAMAGE: -5, INTELLIGENCE: 15 },
        Rare: { CRIT_CHANCE: 18, CRIT_DAMAGE: -5, INTELLIGENCE: 20 },
        Epic: { CRIT_CHANCE: 22, CRIT_DAMAGE: -5, INTELLIGENCE: 25 },
        Legendary: { CRIT_CHANCE: 25, CRIT_DAMAGE: -5, INTELLIGENCE: 30 },
        Mythic: { CRIT_CHANCE: 30, CRIT_DAMAGE: -5, INTELLIGENCE: 40 },
      },
    },
    rich: {
      name: "Rich",
      stats: {
        Common: {
          DAMAGE: 1,
          CRIT_CHANCE: 1,
          CRIT_DAMAGE: 1,
          STRENGTH: 1,
          INTELLIGENCE: 1,
        },
        Uncommon: {
          DAMAGE: 2,
          CRIT_CHANCE: 2,
          CRIT_DAMAGE: 2,
          STRENGTH: 2,
          INTELLIGENCE: 2,
        },
        Rare: {
          DAMAGE: 3,
          CRIT_CHANCE: 3,
          CRIT_DAMAGE: 3,
          STRENGTH: 3,
          INTELLIGENCE: 3,
        },
        Epic: {
          DAMAGE: 4,
          CRIT_CHANCE: 4,
          CRIT_DAMAGE: 4,
          STRENGTH: 4,
          INTELLIGENCE: 4,
        },
        Legendary: {
          DAMAGE: 5,
          CRIT_CHANCE: 5,
          CRIT_DAMAGE: 5,
          STRENGTH: 5,
          INTELLIGENCE: 5,
        },
        Mythic: {
          DAMAGE: 7,
          CRIT_CHANCE: 7,
          CRIT_DAMAGE: 7,
          STRENGTH: 7,
          INTELLIGENCE: 7,
        },
      },
    },
    sharp: {
      name: "Sharp",
      stats: {
        Common: { CRIT_CHANCE: 10, CRIT_DAMAGE: 20 },
        Uncommon: { CRIT_CHANCE: 12, CRIT_DAMAGE: 30 },
        Rare: { CRIT_CHANCE: 14, CRIT_DAMAGE: 40 },
        Epic: { CRIT_CHANCE: 17, CRIT_DAMAGE: 55 },
        Legendary: { CRIT_CHANCE: 20, CRIT_DAMAGE: 75 },
        Mythic: { CRIT_CHANCE: 25, CRIT_DAMAGE: 90 },
      },
    },
    spicy: {
      name: "Spicy",
      stats: {
        Common: { STRENGTH: 2, CRIT_CHANCE: 1, CRIT_DAMAGE: 25 },
        Uncommon: { STRENGTH: 3, CRIT_CHANCE: 1, CRIT_DAMAGE: 35 },
        Rare: { STRENGTH: 4, CRIT_CHANCE: 1, CRIT_DAMAGE: 45 },
        Epic: { STRENGTH: 6, CRIT_CHANCE: 1, CRIT_DAMAGE: 60 },
        Legendary: { STRENGTH: 8, CRIT_CHANCE: 1, CRIT_DAMAGE: 80 },
        Mythic: { STRENGTH: 10, CRIT_CHANCE: 1, CRIT_DAMAGE: 100 },
      },
    },
  },

  // Armor reforges
  ARMOR: {
    clean: {
      name: "Clean",
      stats: {
        Common: { HEALTH: 5, DEFENSE: 5, INTELLIGENCE: 5 },
        Uncommon: { HEALTH: 7, DEFENSE: 7, INTELLIGENCE: 7 },
        Rare: { HEALTH: 9, DEFENSE: 9, INTELLIGENCE: 9 },
        Epic: { HEALTH: 12, DEFENSE: 12, INTELLIGENCE: 12 },
        Legendary: { HEALTH: 16, DEFENSE: 16, INTELLIGENCE: 16 },
        Mythic: { HEALTH: 20, DEFENSE: 20, INTELLIGENCE: 20 },
      },
    },
    fierce: {
      name: "Fierce",
      stats: {
        Common: { STRENGTH: 2, CRIT_CHANCE: 2, CRIT_DAMAGE: 4 },
        Uncommon: { STRENGTH: 4, CRIT_CHANCE: 3, CRIT_DAMAGE: 7 },
        Rare: { STRENGTH: 6, CRIT_CHANCE: 4, CRIT_DAMAGE: 10 },
        Epic: { STRENGTH: 8, CRIT_CHANCE: 5, CRIT_DAMAGE: 14 },
        Legendary: { STRENGTH: 10, CRIT_CHANCE: 6, CRIT_DAMAGE: 18 },
        Mythic: { STRENGTH: 12, CRIT_CHANCE: 8, CRIT_DAMAGE: 24 },
      },
    },
    heavy: {
      name: "Heavy",
      stats: {
        Common: { DEFENSE: 25, CRIT_DAMAGE: -15 },
        Uncommon: { DEFENSE: 35, CRIT_DAMAGE: -15 },
        Rare: { DEFENSE: 50, CRIT_DAMAGE: -15 },
        Epic: { DEFENSE: 65, CRIT_DAMAGE: -15 },
        Legendary: { DEFENSE: 85, CRIT_DAMAGE: -15 },
        Mythic: { DEFENSE: 110, CRIT_DAMAGE: -15 },
      },
    },
    light: {
      name: "Light",
      stats: {
        Common: { HEALTH: 5, DEFENSE: 5, CRIT_CHANCE: 1 },
        Uncommon: { HEALTH: 7, DEFENSE: 7, CRIT_CHANCE: 2 },
        Rare: { HEALTH: 10, DEFENSE: 10, CRIT_CHANCE: 2 },
        Epic: { HEALTH: 15, DEFENSE: 15, CRIT_CHANCE: 3 },
        Legendary: { HEALTH: 20, DEFENSE: 20, CRIT_CHANCE: 4 },
        Mythic: { HEALTH: 25, DEFENSE: 25, CRIT_CHANCE: 5 },
      },
    },
    mythic: {
      name: "Mythic",
      stats: {
        Common: {
          HEALTH: 2,
          DEFENSE: 2,
          STRENGTH: 2,
          CRIT_DAMAGE: 2,
          INTELLIGENCE: 2,
        },
        Uncommon: {
          HEALTH: 3,
          DEFENSE: 3,
          STRENGTH: 3,
          CRIT_DAMAGE: 3,
          INTELLIGENCE: 3,
        },
        Rare: {
          HEALTH: 4,
          DEFENSE: 4,
          STRENGTH: 4,
          CRIT_DAMAGE: 4,
          INTELLIGENCE: 4,
        },
        Epic: {
          HEALTH: 6,
          DEFENSE: 6,
          STRENGTH: 6,
          CRIT_DAMAGE: 6,
          INTELLIGENCE: 6,
        },
        Legendary: {
          HEALTH: 8,
          DEFENSE: 8,
          STRENGTH: 8,
          CRIT_DAMAGE: 8,
          INTELLIGENCE: 8,
        },
        Mythic: {
          HEALTH: 10,
          DEFENSE: 10,
          STRENGTH: 10,
          CRIT_DAMAGE: 10,
          INTELLIGENCE: 10,
        },
      },
    },
    pure: {
      name: "Pure",
      stats: {
        Common: {
          INTELLIGENCE: 2,
          CRIT_CHANCE: 2,
          STRENGTH: 1,
          CRIT_DAMAGE: 2,
          HEALTH: 2,
          DEFENSE: 2,
        },
        Uncommon: {
          INTELLIGENCE: 3,
          CRIT_CHANCE: 4,
          STRENGTH: 3,
          CRIT_DAMAGE: 3,
          HEALTH: 3,
          DEFENSE: 3,
        },
        Rare: {
          INTELLIGENCE: 4,
          CRIT_CHANCE: 6,
          STRENGTH: 4,
          CRIT_DAMAGE: 4,
          HEALTH: 4,
          DEFENSE: 4,
        },
        Epic: {
          INTELLIGENCE: 6,
          CRIT_CHANCE: 8,
          STRENGTH: 6,
          CRIT_DAMAGE: 6,
          HEALTH: 6,
          DEFENSE: 6,
        },
        Legendary: {
          INTELLIGENCE: 8,
          CRIT_CHANCE: 10,
          STRENGTH: 8,
          CRIT_DAMAGE: 8,
          HEALTH: 8,
          DEFENSE: 8,
        },
        Mythic: {
          INTELLIGENCE: 10,
          CRIT_CHANCE: 12,
          STRENGTH: 10,
          CRIT_DAMAGE: 10,
          HEALTH: 10,
          DEFENSE: 10,
        },
      },
    },
    smart: {
      name: "Smart",
      stats: {
        Common: { HEALTH: 4, DEFENSE: 1, INTELLIGENCE: 20 },
        Uncommon: { HEALTH: 6, DEFENSE: 2, INTELLIGENCE: 40 },
        Rare: { HEALTH: 9, DEFENSE: 3, INTELLIGENCE: 60 },
        Epic: { HEALTH: 12, DEFENSE: 4, INTELLIGENCE: 80 },
        Legendary: { HEALTH: 16, DEFENSE: 5, INTELLIGENCE: 100 },
        Mythic: { HEALTH: 20, DEFENSE: 6, INTELLIGENCE: 125 },
      },
    },
    titanic: {
      name: "Titanic",
      stats: {
        Common: { HEALTH: 15, DEFENSE: 15 },
        Uncommon: { HEALTH: 20, DEFENSE: 20 },
        Rare: { HEALTH: 25, DEFENSE: 25 },
        Epic: { HEALTH: 35, DEFENSE: 35 },
        Legendary: { HEALTH: 50, DEFENSE: 50 },
        Mythic: { HEALTH: 75, DEFENSE: 75 },
      },
    },
    wise: {
      name: "Wise",
      stats: {
        Common: { HEALTH: 5, INTELLIGENCE: 25 },
        Uncommon: { HEALTH: 7, INTELLIGENCE: 50 },
        Rare: { HEALTH: 10, INTELLIGENCE: 75 },
        Epic: { HEALTH: 15, INTELLIGENCE: 100 },
        Legendary: { HEALTH: 20, INTELLIGENCE: 125 },
        Mythic: { HEALTH: 25, INTELLIGENCE: 150 },
      },
    },
  },

  // Tool reforges (Pickaxes)
  PICKAXE: {
    unyielding: {
      name: "Unyielding",
      stats: {
        Common: { MINING_FORTUNE: 1, HEALTH: 2 },
        Uncommon: { MINING_FORTUNE: 2, HEALTH: 4 },
        Rare: { MINING_FORTUNE: 3, HEALTH: 6 },
        Epic: { MINING_FORTUNE: 4, HEALTH: 8 },
        Legendary: { MINING_FORTUNE: 5, HEALTH: 10 },
        Mythic: { MINING_FORTUNE: 6, HEALTH: 12 },
      },
    },
    excellent: {
      name: "Excellent",
      stats: {
        Common: { MINING_FORTUNE: 2, DODGE_CHANCE: 0.1 },
        Uncommon: { MINING_FORTUNE: 4, DODGE_CHANCE: 0.2 },
        Rare: { MINING_FORTUNE: 6, DODGE_CHANCE: 0.3 },
        Epic: { MINING_FORTUNE: 8, DODGE_CHANCE: 0.4 },
        Legendary: { MINING_FORTUNE: 10, DODGE_CHANCE: 0.5 },
        Mythic: { MINING_FORTUNE: 12, DODGE_CHANCE: 0.6 },
      },
    },
    fortunate: {
      name: "Fortunate",
      stats: {
        Common: { MINING_FORTUNE: 10 },
        Uncommon: { MINING_FORTUNE: 14 },
        Rare: { MINING_FORTUNE: 18 },
        Epic: { MINING_FORTUNE: 22 },
        Legendary: { MINING_FORTUNE: 26 },
        Mythic: { MINING_FORTUNE: 30 },
      },
    },
    sturdy: {
      name: "Sturdy",
      stats: {
        Common: { MINING_FORTUNE: 3, DEFENSE: 3 },
        Uncommon: { MINING_FORTUNE: 6, DEFENSE: 6 },
        Rare: { MINING_FORTUNE: 9, DEFENSE: 9 },
        Epic: { MINING_FORTUNE: 12, DEFENSE: 12 },
        Legendary: { MINING_FORTUNE: 15, DEFENSE: 15 },
        Mythic: { MINING_FORTUNE: 20, DEFENSE: 20 },
      },
    },
  },

  // Axe reforges
  AXE: {
    great: {
      name: "Great",
      stats: {
        Common: { STRENGTH: 2, CRIT_DAMAGE: 2 },
        Uncommon: { STRENGTH: 4, CRIT_DAMAGE: 4 },
        Rare: { STRENGTH: 6, CRIT_DAMAGE: 6 },
        Epic: { STRENGTH: 9, CRIT_DAMAGE: 9 },
        Legendary: { STRENGTH: 12, CRIT_DAMAGE: 12 },
        Mythic: { STRENGTH: 16, CRIT_DAMAGE: 16 },
      },
    },
    lush: {
      name: "Lush",
      stats: {
        Common: { FORAGING_FORTUNE: 2, DODGE_CHANCE: 0.1 },
        Uncommon: { FORAGING_FORTUNE: 2, DODGE_CHANCE: 0.2 },
        Rare: { FORAGING_FORTUNE: 4, DODGE_CHANCE: 0.3 },
        Epic: { FORAGING_FORTUNE: 4, DODGE_CHANCE: 0.4 },
        Legendary: { FORAGING_FORTUNE: 6, DODGE_CHANCE: 0.5 },
        Mythic: { FORAGING_FORTUNE: 10, DODGE_CHANCE: 0.6 },
      },
    },
    rugged: {
      name: "Rugged",
      stats: {
        Common: { STRENGTH: 4, CRIT_DAMAGE: 3 },
        Uncommon: { STRENGTH: 6, CRIT_DAMAGE: 5 },
        Rare: { STRENGTH: 9, CRIT_DAMAGE: 8 },
        Epic: { STRENGTH: 13, CRIT_DAMAGE: 12 },
        Legendary: { STRENGTH: 18, CRIT_DAMAGE: 16 },
        Mythic: { STRENGTH: 24, CRIT_DAMAGE: 22 },
      },
    },
    double_bit: {
      name: "Double-Bit",
      stats: {
        Common: { FORAGING_FORTUNE: 4 },
        Uncommon: { FORAGING_FORTUNE: 8 },
        Rare: { FORAGING_FORTUNE: 12 },
        Epic: { FORAGING_FORTUNE: 16 },
        Legendary: { FORAGING_FORTUNE: 20 },
        Mythic: { FORAGING_FORTUNE: 24 },
      },
    },
  },

  // Hoe reforges
  HOE: {
    green_thumb: {
      name: "Green Thumb",
      stats: {
        Common: { FARMING_FORTUNE: 4 },
        Uncommon: { FARMING_FORTUNE: 8 },
        Rare: { FARMING_FORTUNE: 12 },
        Epic: { FARMING_FORTUNE: 16 },
        Legendary: { FARMING_FORTUNE: 20 },
        Mythic: { FARMING_FORTUNE: 24 },
      },
    },
    robust: {
      name: "Robust",
      stats: {
        Common: { FARMING_FORTUNE: 2, HEALTH: 2 },
        Uncommon: { FARMING_FORTUNE: 4, HEALTH: 4 },
        Rare: { FARMING_FORTUNE: 6, HEALTH: 6 },
        Epic: { FARMING_FORTUNE: 8, HEALTH: 8 },
        Legendary: { FARMING_FORTUNE: 10, HEALTH: 10 },
        Mythic: { FARMING_FORTUNE: 12, HEALTH: 12 },
      },
    },
  },
};

// Reforge costs based on item rarity
const REFORGE_COSTS = {
  Common: 500,
  Uncommon: 1000,
  Rare: 2500,
  Epic: 5000,
  Legendary: 10000,
  Mythic: 25000,
};

/**
 * Get the correct reforge cost based on item rarity with case-insensitive matching
 * @param {string} rarity - The item rarity string
 * @returns {number} - The reforge cost
 */
function getReforgeCost(rarity) {
  //console.log(`[getReforgeCost] Input rarity:`, rarity);

  if (!rarity) {
    //console.log(`[getReforgeCost] Rarity is null/undefined, returning Common cost: ${REFORGE_COSTS.Common}`);
    return REFORGE_COSTS.Common;
  }

  const rarityStr = typeof rarity === "string" ? rarity : String(rarity);
  //console.log(`[getReforgeCost] Normalized rarity string: "${rarityStr}"`);

  // Normalize to proper case (first letter uppercase, rest lowercase)
  const normalizedRarity =
    rarityStr.charAt(0).toUpperCase() + rarityStr.slice(1).toLowerCase();

  const cost = REFORGE_COSTS[normalizedRarity] || REFORGE_COSTS.Common;
  //console.log(`[getReforgeCost] Normalized to: ${normalizedRarity}, Cost: ${cost}`);

  return cost;
}

// Get available reforges for an item type
function getAvailableReforges(itemType, subtype) {
  if (
    itemType === "WEAPON" &&
    (subtype === "SWORD" ||
      subtype === "BOW" ||
      subtype === "AXE" ||
      subtype === "FARMING_AXE")
  ) {
    return Object.keys(REFORGE_DATA.WEAPON);
  }
  if (itemType === "ARMOR") {
    return Object.keys(REFORGE_DATA.ARMOR);
  }
  if (itemType === "TOOL") {
    if (subtype === "PICKAXE") return Object.keys(REFORGE_DATA.PICKAXE);
    if (subtype === "AXE") return Object.keys(REFORGE_DATA.AXE);
    if (subtype === "FARMING_AXE" || subtype === "HOE")
      return Object.keys(REFORGE_DATA.HOE);
  }
  return [];
}

// Get reforge stats for a specific reforge and rarity
function getReforgeStats(reforgeKey, itemType, subtype, rarity) {
  let reforgeCategory;

  if (
    itemType === "WEAPON" &&
    (subtype === "SWORD" ||
      subtype === "BOW" ||
      subtype === "AXE" ||
      subtype === "FARMING_AXE")
  ) {
    reforgeCategory = REFORGE_DATA.WEAPON;
  } else if (itemType === "ARMOR") {
    reforgeCategory = REFORGE_DATA.ARMOR;
  } else if (itemType === "TOOL") {
    if (subtype === "PICKAXE") reforgeCategory = REFORGE_DATA.PICKAXE;
    else if (subtype === "AXE") reforgeCategory = REFORGE_DATA.AXE;
    else if (subtype === "FARMING_AXE" || subtype === "HOE")
      reforgeCategory = REFORGE_DATA.HOE;
  }

  if (!reforgeCategory || !reforgeCategory[reforgeKey]) {
    return null;
  }

  const reforge = reforgeCategory[reforgeKey];
  return {
    name: reforge.name,
    stats: reforge.stats[rarity] || {},
  };
}

// Check if an item can be reforged
function canReforge(item) {
  const itemType = item.type;
  const subtype = item.subtype;

  // Check if item type supports reforging
  if (
    itemType === "WEAPON" &&
    (subtype === "SWORD" ||
      subtype === "BOW" ||
      subtype === "AXE" ||
      subtype === "FARMING_AXE")
  )
    return true;
  if (itemType === "ARMOR") return true;
  if (
    itemType === "TOOL" &&
    ["PICKAXE", "AXE", "FARMING_AXE", "HOE"].includes(subtype)
  )
    return true;

  return false;
}

// Apply reforge to an item
function applyReforge(item, reforgeKey) {
  if (!canReforge(item)) {
    return { success: false, message: "This item cannot be reforged!" };
  }

  const availableReforges = getAvailableReforges(item.type, item.subtype);
  if (!availableReforges.includes(reforgeKey)) {
    return {
      success: false,
      message: "This reforge is not available for this item type!",
    };
  }

  const rarityKey = typeof item.rarity === "string" ? item.rarity : item.rarity;
  const reforgeStats = getReforgeStats(
    reforgeKey,
    item.type,
    item.subtype,
    rarityKey
  );
  if (!reforgeStats) {
    return { success: false, message: "Invalid reforge!" };
  }

  // Create reforged item with new dynamic format (only store the key)
  const reforgedItem = { ...item };
  reforgedItem.reforge = {
    key: reforgeKey,
  };

  // Update item name with reforge prefix
  reforgedItem.name = `${reforgeStats.name} ${item.name}`;

  return { success: true, item: reforgedItem, cost: getReforgeCost(rarityKey) };
}

// Remove reforge from an item
function removeReforge(item) {
  if (!item.reforge) {
    return { success: false, message: "This item is not reforged!" };
  }

  const originalItem = { ...item };
  delete originalItem.reforge;

  // Remove reforge prefix from name
  const reforgePrefix = `${item.reforge.name} `;
  if (originalItem.name.startsWith(reforgePrefix)) {
    originalItem.name = originalItem.name.substring(reforgePrefix.length);
  }

  return { success: true, item: originalItem };
}

module.exports = {
  REFORGE_DATA,
  REFORGE_COSTS,
  getAvailableReforges,
  getReforgeStats,
  getReforgeCost,
  canReforge,
  applyReforge,
  removeReforge,
};
