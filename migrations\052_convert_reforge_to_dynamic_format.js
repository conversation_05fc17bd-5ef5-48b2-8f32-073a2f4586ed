// Migration 052: Convert reforge data from hardcoded stats to dynamic key-only format
// This migration converts existing reforge data from the old format (with stats) to the new format (key only)
// The stats will now be calculated dynamically from the configuration

async function up(db) {
  console.log("[Migration 052] Converting reforge data to dynamic format...");

  // Helper function to convert old reforge format to new format
  function convertReforgeFormat(dataJson) {
    if (!dataJson || typeof dataJson !== "object") {
      return dataJson;
    }

    const data = JSON.parse(JSON.stringify(dataJson)); // Deep clone

    if (
      data.reforge &&
      data.reforge.stats &&
      typeof data.reforge.stats === "object"
    ) {
      // Old format detected - convert to new format (keep only the key)
      const newReforge = {
        key: data.reforge.key,
      };
      data.reforge = newReforge;
      console.log(
        `[Migration 052] Converted reforge from old format to new format: ${newReforge.key}`,
      );
    }

    return data;
  }

  let itemsConverted = 0;

  try {
    // Convert equipment items
    console.log("[Migration 052] Converting equipment reforge data...");

    const equipmentItems = await new Promise((resolve, reject) => {
      db.all(
        "SELECT equipment_id, discord_id, data_json FROM player_equipment WHERE data_json IS NOT NULL AND data_json != ''",
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        },
      );
    });

    for (const item of equipmentItems) {
      try {
        const dataJson = JSON.parse(item.data_json);
        const convertedData = convertReforgeFormat(dataJson);

        // Only update if conversion actually changed something
        if (JSON.stringify(convertedData) !== JSON.stringify(dataJson)) {
          await new Promise((resolve, reject) => {
            db.run(
              "UPDATE player_equipment SET data_json = ? WHERE equipment_id = ? AND discord_id = ?",
              [
                JSON.stringify(convertedData),
                item.equipment_id,
                item.discord_id,
              ],
              (err) => {
                if (err) reject(err);
                else resolve();
              },
            );
          });
          itemsConverted++;
        }
      } catch (parseError) {
        console.warn(
          `[Migration 052] Could not parse data_json for equipment ${item.equipment_id}:`,
          parseError.message,
        );
      }
    }

    // Convert accessory items
    console.log("[Migration 052] Converting accessory reforge data...");

    const accessories = await new Promise((resolve, reject) => {
      db.all(
        "SELECT accessory_id, discord_id, data_json FROM player_accessories WHERE data_json IS NOT NULL AND data_json != ''",
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        },
      );
    });

    for (const accessory of accessories) {
      try {
        const dataJson = JSON.parse(accessory.data_json);
        const convertedData = convertReforgeFormat(dataJson);

        // Only update if conversion actually changed something
        if (JSON.stringify(convertedData) !== JSON.stringify(dataJson)) {
          await new Promise((resolve, reject) => {
            db.run(
              "UPDATE player_accessories SET data_json = ? WHERE accessory_id = ? AND discord_id = ?",
              [
                JSON.stringify(convertedData),
                accessory.accessory_id,
                accessory.discord_id,
              ],
              (err) => {
                if (err) reject(err);
                else resolve();
              },
            );
          });
          itemsConverted++;
        }
      } catch (parseError) {
        console.warn(
          `[Migration 052] Could not parse data_json for accessory ${accessory.accessory_id}:`,
          parseError.message,
        );
      }
    }

    console.log(
      `[Migration 052] Successfully converted ${itemsConverted} items to dynamic reforge format.`,
    );
  } catch (error) {
    console.error(
      "[Migration 052] Error during reforge format conversion:",
      error,
    );
    throw error;
  }
}

async function down() {
  console.log("[Migration 052] Rolling back reforge format conversion...");
  console.log(
    "[Migration 052] Note: Cannot automatically restore old reforge stats format.",
  );
  console.log(
    "[Migration 052] The dynamic calculation system provides the same functionality.",
  );
  // The rollback would require restoring the old hardcoded stats, which is not practical
  // since the new system provides the same functionality dynamically
}

module.exports = { up, down };
