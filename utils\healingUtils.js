const { calculateAllStats } = require("./statCalculations");

/**
 * Check if character has vitality doubler effect from equipped items
 * @param {Object} character - The character to check
 * @returns {boolean} - True if character has vitality doubler effect
 */
function checkForVitalityDoubler(character) {
  if (!character) {
    return false;
  }

  const configManager = require("./configManager");
  const allItems = configManager.getAllItems();
  let equippedItems = [];

  // New system: character.equipment.equipped
  if (character.equipment && character.equipment.equipped) {
    const newSystemItems = Object.values(character.equipment.equipped)
      .filter((item) => item)
      .map((item) => item.itemKey);
    equippedItems = equippedItems.concat(newSystemItems);
  }

  // Old system: character.inventory.equipment (fallback)
  if (character.inventory && character.inventory.equipment) {
    const oldSystemItems = character.inventory.equipment
      .filter((item) => item.isEquipped)
      .map((item) => item.itemKey);
    equippedItems = equippedItems.concat(oldSystemItems);
  }

  // Check for vitality doubler effect
  for (const itemKey of equippedItems) {
    const itemData = allItems[itemKey];
    if (itemData && itemData.effects && itemData.effects.vitalityDoubler) {
      return true;
    }
  }

  return false;
}

/**
 * Applies vitality modifier to healing amounts
 * @param {Object} character - The character receiving healing
 * @param {number} baseHealingAmount - The base healing amount before vitality modifier
 * @returns {number} - The final healing amount after vitality modifier
 */
function applyVitalityHealing(character, baseHealingAmount) {
  if (!character || !baseHealingAmount || baseHealingAmount <= 0) {
    return 0;
  }

  // Get character's total stats (vitality doubler already applied in calculateAllStats)
  const allStats = calculateAllStats(character);
  const vitality = allStats.VITALITY || 100; // Default to 100 if somehow undefined

  // Apply vitality multiplier: Vitality/100
  const vitalityMultiplier = vitality / 100;
  const finalHealingAmount = baseHealingAmount * vitalityMultiplier;

  return finalHealingAmount;
}

/**
 * Applies healing to a character with vitality modifier and caps to max health
 * @param {Object} character - The character receiving healing
 * @param {number} baseHealingAmount - The base healing amount before vitality modifier
 * @returns {Object} - Object with finalHealing amount and newHealth value
 */
function applyHealingToCharacter(character, baseHealingAmount) {
  if (!character || !baseHealingAmount || baseHealingAmount <= 0) {
    return {
      finalHealing: 0,
      newHealth: character?.current_health || 0,
    };
  }

  const finalHealing = applyVitalityHealing(character, baseHealingAmount);
  const allStats = calculateAllStats(character);
  const maxHealth = allStats.HEALTH || 100;
  const currentHealth = character.current_health || 0;

  const newHealth = Math.min(maxHealth, currentHealth + finalHealing);

  return {
    finalHealing,
    newHealth,
    actualHealingDone: newHealth - currentHealth,
  };
}

module.exports = {
  applyVitalityHealing,
  applyHealingToCharacter,
  checkForVitalityDoubler,
};
