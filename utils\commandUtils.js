const { calculateMaxActionAmount } = require("./skillLimits");
const { MessageFlags } = require("discord.js");

/**
 * Parses and validates the 'amount' option from a skill command interaction.
 * Handles "max" input and checks against skill level limits.
 *
 * @param {import('discord.js').CommandInteraction} interaction - The interaction object.
 * @param {string|null} amountInput - The raw string value from interaction.options.getString('amount').
 * @param {number} skillLevel - The player's level in the relevant skill.
 * @param {string} skillName - The name of the skill (e.g., 'Farming', 'Mining') for error messages.
 * @returns {{amount: number}|{error: true, replyOptions: object}} - Returns either the valid amount or an error object with reply options.
 */
function validateAmountInput(interaction, amountInput, skillLevel, skillName) {
  const maxAllowedAmount = calculateMaxActionAmount(skillLevel);
  let amount = maxAllowedAmount; // Default to max allowed amount when no input provided
  let wasMax = false; // Track if "max" was used

  if (amountInput) {
    if (amountInput.toLowerCase() === "max") {
      amount = maxAllowedAmount;
      wasMax = true; // Mark that "max" was used
    } else {
      const parsedAmount = parseInt(amountInput, 10);
      if (isNaN(parsedAmount) || parsedAmount <= 0) {
        return {
          error: true,
          replyOptions: {
            content: `Invalid amount specified: '${amountInput}'. Please enter a positive number or 'max'.`,
            flags: [MessageFlags.Ephemeral],
            components: [],
            embeds: [],
          },
        };
      }
      if (parsedAmount > maxAllowedAmount) {
        return {
          error: true,
          replyOptions: {
            content: `Your ${skillName} level (${skillLevel}) allows you to perform this action a maximum of ${maxAllowedAmount} times at once. You requested ${parsedAmount}.`,
            flags: [MessageFlags.Ephemeral],
            components: [],
            embeds: [],
          },
        };
      }
      amount = parsedAmount;
    }
  }

  // Clamp amount just in case (shouldn't be necessary with validation, but safe)
  amount = Math.max(1, Math.min(amount, maxAllowedAmount));

  return { amount: amount, wasMax: wasMax };
}

/**
 * Extracts and validates parameters for a skill command from a Discord interaction.
 * Handles character fetch, resource key validation, region permissions, and amount validation (including 'max').
 * Sends error replies if invalid, returns null in that case.
 * @param {object} interaction - The Discord interaction
 * @param {object} skillConfig - The skill config (must include skillName, resourceKeyParamName)
 * @param {object} [options] - Optional overrides (e.g., requiredResource, requiredAmount)
 * @returns {Promise<{character, resourceKey, amount}|null>}
 */
async function extractAndValidateSkillCommandParams(
  interaction,
  skillConfig,
  options = {}
) {
  const { getPlayerData } = require("./playerDataManager");
  const { getPlayerSkillLevel } = require("./playerDataManager");
  const { calculateMaxActionAmount } = require("./skillLimits");
  const { checkRegionPermissions } = require("./regionUtils");
  const userId = interaction.user.id;
  const resourceKeyParam = skillConfig.resourceKeyParamName;
  let resourceKey =
    options.requiredResource ||
    (resourceKeyParam ? interaction.options.getString(resourceKeyParam) : null);
  // Normalize resourceKey to uppercase for mining
  if (skillConfig.skillName === "mining" && resourceKey) {
    resourceKey = resourceKey.toUpperCase();
  }

  // debug: log the resourceKey for alchemy
  if (skillConfig.skillName === "alchemy") {
    console.log(
      `[SkillCommandUtils] Extracted resourceKey for alchemy:`,
      resourceKey
    );
  }

  if (resourceKeyParam && (!resourceKey || typeof resourceKey !== "string")) {
    await interaction.editReply({
      content: `Invalid ${resourceKeyParam} type selected.`,
    });
    return null;
  }
  let character = await getPlayerData(userId);
  if (!character) {
    await interaction.editReply({
      content: "Cannot find your character data. Visit the setup channel.",
    });
    return null;
  }

  // AUTO-FIX: Detect and repair broken inventory structures for existing players
  // This fixes players who were affected by the character creation bug
  const { ensureValidInventoryStructure } = require("./inventoryFix");
  character = await ensureValidInventoryStructure(userId, character);

  // Validate region permissions
  const resourceNameSingular = skillConfig.resourceNameSingular || "resource";
  const activityName = skillConfig.skillName;
  if (skillConfig.skillName !== "alchemy") {
    const regionCheck = checkRegionPermissions(
      character,
      activityName,
      null,
      resourceKey,
      resourceNameSingular
    );

    if (!regionCheck.allowed) {
      const replyOptions = {
        embeds: [regionCheck.embed],
      };
      await interaction.editReply(replyOptions);
      return null;
    }
  }

  // Always derive skill level directly from raw experience
  // Don't use the level property, directly get level from raw experience
  const skillLevel = getPlayerSkillLevel(character, skillConfig.skillName) || 0;
  const maxAllowedAmount = calculateMaxActionAmount(skillLevel);
  const amountInputString =
    options.requiredAmount || interaction.options.getString("amount");
  let requestedAmount;
  if (!amountInputString) {
    requestedAmount = 1;
  } else if (amountInputString.toLowerCase() === "max") {
    requestedAmount = maxAllowedAmount;
  } else {
    requestedAmount = parseInt(amountInputString, 10);
    if (isNaN(requestedAmount) || requestedAmount <= 0) {
      await interaction.editReply({
        content:
          'Invalid amount specified. Please enter a positive number or "max".',
      });
      return null;
    }
  }
  const amountResult = require("./commandUtils").validateAmountInput(
    interaction,
    amountInputString,
    skillLevel,
    skillConfig.actionName
  );
  if (amountResult.error) {
    await interaction.editReply(amountResult.replyOptions);
    return null;
  }
  const amount = amountResult.amount;
  const wasMax = amountResult.wasMax || false;

  // Check brewing ingredients for alchemy before proceeding
  if (skillConfig.skillName === "alchemy" && resourceKey) {
    const configManager = require("./configManager");
    const potionData = configManager.getItem(resourceKey);
    if (potionData && potionData.brewing && potionData.brewing.ingredients) {
      let hasMissingIngredients = false;
      let ingredientsDisplay = "";

      for (const [ingredientKey, requiredQty] of Object.entries(
        potionData.brewing.ingredients
      )) {
        const playerQty = character.inventory.items[ingredientKey] || 0;
        const totalRequired = requiredQty * amount;
        const ingredientData = configManager.getItem(ingredientKey);
        const ingredientName = ingredientData
          ? ingredientData.name
          : ingredientKey;
        const hasEnough = playerQty >= totalRequired;

        if (!hasEnough) {
          hasMissingIngredients = true;
        }

        // determine if player has enough
        const checkmark = hasEnough ? "✅" : "❌";

        // format the display with emoji
        const emoji = ingredientData?.emoji || "❓";
        let quantityDisplay = "";
        if (playerQty > 0) {
          quantityDisplay = ` (You have ${playerQty.toLocaleString()})`;
        }

        ingredientsDisplay += `${checkmark} ${emoji} \`${totalRequired.toLocaleString()}x ${ingredientName}\`${quantityDisplay}\n`;
      }

      if (hasMissingIngredients) {
        const potionName = potionData.name || resourceKey;
        const { EmbedBuilder } = require("discord.js");
        const gameConfig = require("../gameConfig");

        const embed = new EmbedBuilder()
          .setColor(gameConfig.EMBED_COLORS.LIGHT_RED)
          .setDescription(
            `✖ Missing ingredients to brew **${amount}x ${potionName}**\n\n${ingredientsDisplay.trim()}`
          );

        await interaction.editReply({
          embeds: [embed],
        });
        return null;
      }
    }
  }

  return { character, resourceKey, amount, wasMax };
}

/**
 * Generic autocomplete handler for skill resource selection (e.g., crop, ore, wood).
 * Usage: In your command file, export as:
 *   async autocomplete(interaction) { return skillResourceAutocomplete(interaction, 'Farming', 'crop'); }
 * @param {object} interaction - The Discord interaction
 * @param {string} skillName - The skill name (e.g., 'Farming', 'Mining', 'Foraging')
 * @param {string} resourceKeyField - The option name for the resource (e.g., 'crop', 'resource', 'wood')
 */
async function skillResourceAutocomplete(
  interaction,
  skillName,
  resourceKeyField
) {
  const focusedOption = interaction.options.getFocused(true);
  const focusedValue = focusedOption.value.toLowerCase();
  if (focusedOption.name !== resourceKeyField) {
    await interaction.respond([]);
    return;
  }
  let character;
  try {
    character = await require("./playerDataManager").getPlayerData(
      interaction.user.id
    );
  } catch (error) {
    console.error(
      `[${skillName} Autocomplete] Error fetching player data:`,
      error
    );
    await interaction.respond([]);
    return;
  }
  if (!character || !character.current_region) {
    await interaction.respond([]);
    return;
  }
  const currentRegionKey = character.current_region;
  const allItems = require("./configManager").getAllItems();
  const { findCollectionForItem } = require("./collectionUtils");

  // helper function to check if an item or its drops have collections
  function itemOrDropsHaveCollection(itemKey, item) {
    // check if the item itself has a collection
    if (findCollectionForItem(itemKey) !== null) {
      return true;
    }

    // check if any of the item's drops have collections
    if (item.drops && Array.isArray(item.drops)) {
      return item.drops.some((drop) => {
        return findCollectionForItem(drop.itemKey) !== null;
      });
    }

    return false;
  }

  // Map skillName to sourceSkill field
  const skillSourceMap = {
    Farming: "Farming",
    Mining: "Mining",
    Foraging: "Foraging",
    Alchemy: "Alchemy",
  };
  const sourceSkill = (skillSourceMap[skillName] || skillName).toLowerCase();

  // Calculate the actual skill level from experience
  const { getLevelFromExp } = require("./expFunctions");
  const skillData = character.skills[sourceSkill];
  const skillLevel = skillData ? getLevelFromExp(skillData.exp || 0).level : 0;

  const availableResources = Object.entries(allItems)
    .filter(
      ([key, item]) =>
        item.sourceSkill &&
        item.sourceSkill.toLowerCase() === sourceSkill &&
        // alchemy items don't need collections since they're crafted items, not gathered resources
        // for other skills, check if the item or its drops have collections
        (sourceSkill === "alchemy" || itemOrDropsHaveCollection(key, item)) &&
        (item.requiredLevel === undefined ||
          item.requiredLevel <= skillLevel) &&
        // Filter by current region - only show resources available in the player's current region
        // Exception: alchemy items don't need region restrictions since they can be brewed anywhere
        (sourceSkill === "alchemy" ||
          (item.foundInRegions &&
            item.foundInRegions.includes(currentRegionKey)))
    )
    .map(([key, item]) => ({
      name: item.name,
      value: key,
    }))
    .filter((choice) => choice.name.toLowerCase().startsWith(focusedValue))
    .slice(0, 25);
  await interaction.respond(availableResources);
}

module.exports = {
  validateAmountInput,
  extractAndValidateSkillCommandParams,
  skillResourceAutocomplete,
};
