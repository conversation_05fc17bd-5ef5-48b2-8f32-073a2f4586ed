// Migration to add placedMinions array to island_json

const MIGRATION_VERSION = 8;

// Helper function to run db.all as a Promise
function dbAllAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve({ lastID: this.lastID, changes: this.changes });
    });
  });
}

// Helper to safely parse JSON
function safeJsonParse(jsonString, defaultValue) {
  if (jsonString == null) return defaultValue;
  try {
    return JSON.parse(jsonString);
  } catch (e) {
    console.error(
      `[Migration ${MIGRATION_VERSION}] Failed to parse JSON:`,
      jsonString,
      e,
    );
    return defaultValue;
  }
}

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add placedMinions to island_json`,
  );

  try {
    // Get all players with their island data
    const players = await dbAllAsync(
      db,
      "SELECT discord_id, island_json FROM players",
    );
    let updatedCount = 0;

    for (const player of players) {
      let islandData = safeJsonParse(player.island_json, {});
      let needsUpdate = false;

      // Ensure islandData is an object
      if (typeof islandData !== "object" || islandData === null) {
        islandData = {};
        needsUpdate = true;
      }

      // Add placedMinions array if it doesn't exist
      if (!Array.isArray(islandData.placedMinions)) {
        islandData.placedMinions = [];
        needsUpdate = true;
      }

      // If changes were needed, update the player row
      if (needsUpdate) {
        const newIslandJson = JSON.stringify(islandData);
        await dbRunAsync(
          db,
          "UPDATE players SET island_json = ? WHERE discord_id = ?",
          [newIslandJson, player.discord_id],
        );
        updatedCount++;
      }
    }

    console.log(
      `[Migration ${MIGRATION_VERSION}] Migration applied successfully. Updated ${updatedCount} players.`,
    );
  } catch (err) {
    console.error(
      `[Migration ${MIGRATION_VERSION}] Error applying migration:`,
      err,
    );
    throw err; // Re-throw error so the main runner can catch and rollback its transaction
  }
}

module.exports = { up };
