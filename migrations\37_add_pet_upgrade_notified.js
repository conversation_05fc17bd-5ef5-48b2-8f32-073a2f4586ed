// migrations/37_add_pet_upgrade_notified.js
// Add pet_upgrade_notified column to player_last_active_channel table

/**
 * Applies the migration to add pet_upgrade_notified column to player_last_active_channel table
 * @param {object} db - The database connection
 * @param {object} utils - Utility functions for player data
 */
async function up(db) {
  console.log(
    "Running migration: Add pet_upgrade_notified column to player_last_active_channel table",
  );

  // Check if the table exists
  const tableExists = await new Promise((resolve, reject) => {
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='player_last_active_channel'",
      (err, row) => {
        if (err) reject(err);
        else resolve(row ? true : false);
      },
    );
  });

  if (!tableExists) {
    console.log("player_last_active_channel table does not exist, creating it");
    await new Promise((resolve, reject) => {
      db.run(
        `
                CREATE TABLE player_last_active_channel (
                    discord_id TEXT PRIMARY KEY,
                    channel_id TEXT,
                    updated_at INTEGER,
                    last_notified INTEGER,
                    pet_upgrade_notified INTEGER
                )
            `,
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });
    console.log(
      "Created player_last_active_channel table with pet_upgrade_notified column",
    );
    return;
  }

  // Check if the column already exists
  const columnExists = await new Promise((resolve, reject) => {
    db.all("PRAGMA table_info(player_last_active_channel)", (err, rows) => {
      if (err) reject(err);
      else resolve(rows.some((col) => col.name === "pet_upgrade_notified"));
    });
  });

  if (!columnExists) {
    // Add the column
    await new Promise((resolve, reject) => {
      db.run(
        "ALTER TABLE player_last_active_channel ADD COLUMN pet_upgrade_notified INTEGER",
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });
    console.log(
      "Added pet_upgrade_notified column to player_last_active_channel table",
    );
  } else {
    console.log(
      "pet_upgrade_notified column already exists in player_last_active_channel table",
    );
  }
}

/**
 * Reverts the migration (removes the pet_upgrade_notified column)
 * @param {object} db - The database connection
 * @param {object} utils - Utility functions for player data
 */
async function down() {
  console.log(
    "This migration cannot be reverted directly in SQLite (cannot drop columns)",
  );
  console.log(
    "To revert, you would need to create a new table without the column and copy data",
  );
}

module.exports = { up, down };
