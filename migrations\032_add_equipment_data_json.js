// Migration 032: Add data_json column to player_equipment for custom equipment properties (e.g., enchants)

const COLUMN_NAME = "data_json";

module.exports.up = async function up(db) {
  // 1. Add the data_json column if it doesn't exist
  await new Promise((resolve, reject) => {
    db.run(
      `ALTER TABLE player_equipment ADD COLUMN ${COLUMN_NAME} TEXT DEFAULT '{}'`,
      [],
      function (err) {
        if (err && !err.message.includes("duplicate column")) {
          // Only error if it's not a duplicate column error
          reject(err);
        } else {
          resolve();
        }
      },
    );
  });
  // 2. Initialize all existing rows to '{}'
  await new Promise((resolve, reject) => {
    db.run(
      `UPDATE player_equipment SET ${COLUMN_NAME} = '{}' WHERE ${COLUMN_NAME} IS NULL OR ${COLUMN_NAME} = ''`,
      [],
      function (err) {
        if (err) reject(err);
        else resolve();
      },
    );
  });
  console.log(
    "[Migration 032] data_json column added and initialized in player_equipment.",
  );
};

module.exports.down = async function down() {
  // SQLite does not support DROP COLUMN directly; skipping down migration.
  console.log(
    "[Migration 032] Down migration not supported (cannot drop column in SQLite).",
  );
};
