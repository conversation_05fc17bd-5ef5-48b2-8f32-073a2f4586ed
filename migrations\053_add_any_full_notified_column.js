/**
 * Adds any_full_notified column to player_last_active_channel table
 * Tracks when a player was last notified that at least one minion is full.
 */

/**
 * @param {import('sqlite3').Database} db
 */
async function up(db) {
  console.log(
    "Running migration: add any_full_notified column to player_last_active_channel",
  );

  // Check if table exists
  const tableExists = await new Promise((resolve, reject) => {
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='player_last_active_channel'",
      (err, row) => {
        if (err) reject(err);
        else resolve(!!row);
      },
    );
  });

  if (!tableExists) {
    console.log(
      "player_last_active_channel table does not exist. Creating with any_full_notified column...",
    );
    await new Promise((resolve, reject) => {
      db.run(
        `CREATE TABLE player_last_active_channel (
          discord_id TEXT PRIMARY KEY,
          channel_id TEXT,
          updated_at INTEGER,
          last_notified INTEGER,
          any_full_notified INTEGER,
          pet_upgrade_notified INTEGER
        )`,
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });
    return;
  }

  // Column already exists?
  const columnExists = await new Promise((resolve, reject) => {
    db.all("PRAGMA table_info(player_last_active_channel)", (err, rows) => {
      if (err) reject(err);
      else resolve(rows.some((col) => col.name === "any_full_notified"));
    });
  });

  if (!columnExists) {
    await new Promise((resolve, reject) => {
      db.run(
        "ALTER TABLE player_last_active_channel ADD COLUMN any_full_notified INTEGER",
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });
    console.log(
      "Added any_full_notified column to player_last_active_channel table",
    );
  } else {
    console.log("any_full_notified column already exists; skipping");
  }
}

async function down() {
  console.log(
    "This migration cannot be reverted automatically in SQLite (cannot drop columns).",
  );
}

module.exports = { up, down };
