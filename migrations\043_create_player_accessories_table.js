// migrations/043_create_player_accessories_table.js

module.exports.up = async function up(db) {
  console.log("[Migration 043] Creating player_accessories table...");

  // 1. Create the player_accessories table
  await new Promise((resolve, reject) => {
    db.run(
      `
            CREATE TABLE IF NOT EXISTS player_accessories (
                accessory_id TEXT PRIMARY KEY,
                discord_id TEXT NOT NULL,
                item_key TEXT NOT NULL,
                is_equipped INTEGER DEFAULT 0,
                data_json TEXT DEFAULT '{}',
                FOREIGN KEY (discord_id) REFERENCES players(discord_id) ON DELETE CASCADE
            )
        `,
      function (err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      },
    );
  });

  console.log("[Migration 043] player_accessories table created successfully.");

  // 2. Migrate existing accessories from player_equipment to player_accessories
  console.log(
    "[Migration 043] Migrating existing accessories from player_equipment...",
  );

  // Get all accessories from player_equipment
  const accessories = await new Promise((resolve, reject) => {
    db.all(
      `
            SELECT pe.equipment_id, pe.discord_id, pe.item_key, pe.is_equipped, pe.data_json
            FROM player_equipment pe
            WHERE pe.item_key IN (
                SELECT DISTINCT item_key 
                FROM player_equipment 
                WHERE item_key LIKE '%TALISMAN%' OR item_key LIKE '%ACCESSORY%'
            )
        `,
      function (err, rows) {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      },
    );
  });

  // Insert accessories into player_accessories table
  for (const accessory of accessories) {
    await new Promise((resolve, reject) => {
      db.run(
        `
                INSERT INTO player_accessories (accessory_id, discord_id, item_key, is_equipped, data_json)
                VALUES (?, ?, ?, ?, ?)
            `,
        [
          accessory.equipment_id, // Use the same ID
          accessory.discord_id,
          accessory.item_key,
          accessory.is_equipped,
          accessory.data_json || "{}",
        ],
        function (err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });
  }

  console.log(
    `[Migration 043] Migrated ${accessories.length} accessories to player_accessories table.`,
  );

  // 3. Remove accessories from player_equipment table
  await new Promise((resolve, reject) => {
    db.run(
      `
            DELETE FROM player_equipment 
            WHERE item_key IN (
                SELECT DISTINCT item_key 
                FROM player_equipment 
                WHERE item_key LIKE '%TALISMAN%' OR item_key LIKE '%ACCESSORY%'
            )
        `,
      function (err) {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      },
    );
  });

  console.log(
    "[Migration 043] Removed accessories from player_equipment table.",
  );
  console.log("[Migration 043] Migration completed successfully.");
};

module.exports.down = async function down(db) {
  console.log(
    "[Migration 043] Rolling back player_accessories table creation...",
  );

  // Move accessories back to player_equipment
  const accessories = await new Promise((resolve, reject) => {
    db.all(
      `
            SELECT accessory_id, discord_id, item_key, is_equipped, data_json
            FROM player_accessories
        `,
      function (err, rows) {
        if (err) {
          reject(err);
        } else {
          resolve(rows || []);
        }
      },
    );
  });

  // Insert back into player_equipment
  for (const accessory of accessories) {
    await new Promise((resolve, reject) => {
      db.run(
        `
                INSERT INTO player_equipment (equipment_id, discord_id, item_key, is_equipped, data_json)
                VALUES (?, ?, ?, ?, ?)
            `,
        [
          accessory.accessory_id,
          accessory.discord_id,
          accessory.item_key,
          accessory.is_equipped,
          accessory.data_json || "{}",
        ],
        function (err) {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });
  }

  // Drop the player_accessories table
  await new Promise((resolve, reject) => {
    db.run("DROP TABLE IF EXISTS player_accessories", function (err) {
      if (err) {
        reject(err);
      } else {
        resolve();
      }
    });
  });

  console.log("[Migration 043] Rollback completed successfully.");
};
