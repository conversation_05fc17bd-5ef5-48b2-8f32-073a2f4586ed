const { EmbedBuilder } = require("discord.js");
const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
const configManager = require("./configManager");
const { formatXP } = require("./formatUtils");

/**
 * Creates a string representation of a progress bar.
 * @param {number} current The current value.
 * @param {number} max The maximum value.
 * @param {Object} options Configuration options
 * @param {number} [options.size=15] The number of characters for the bar itself.
 * @param {boolean} [options.showXpText=false] Whether to include the XP text below the bar
 * @param {boolean} [options.localeString=false] Format numbers with toLocaleString
 * @param {boolean} [options.showPercentage=true] Whether to show percentage in the bar
 * @param {function} [options.formatFn] Optional function to format numbers (e.g. formatXP)
 * @param {boolean} [options.useEmojis=true] Whether to use custom emojis (false uses classic ASCII)
 * @returns {string} Formatted progress bar string with optional XP text
 */
function createProgressBar(current, max, options = {}) {
  // For backwards compatibility, if the third parameter is a number, treat it as size
  if (typeof options === "number") {
    options = { size: options };
  }

  // Extract options with defaults
  const {
    size = 15,
    showXpText = false,
    localeString = false,
    showPercentage = true,
    formatFn = null,
    useEmojis = true,
  } = options;

  const currentVal = current || 0.0;
  const maxVal = max ? Math.max(1, max) : 1;

  // Check for max level or invalid values
  const isMaxLevel =
    maxVal === 0 || maxVal === Infinity || currentVal >= maxVal;

  // Custom emoji definitions
  const emojiLeftEmpty = "<:leftempty:1391013183400968284>";
  const emojiMidEmpty = "<:midempty:1391013187654127677>";
  const emojiRightEmpty = "<:rightempty:1391013190820954184>";
  const emojiLeftFull = "<:leftfull:1391013185909428394>";
  const emojiMidFull = "<:midfull:1391013189004558427>";
  const emojiRightFull = "<:rightfull:1391013197921910814>";
  const emojiLeftHalf = "<:lefthalf:1391030996438089729>";
  const emojiMidHalf = "<:midhalf:1391029324584456202>";
  const emojiRightHalf = "<:righthalf:1391056835905196042>";

  // Calculate percentage and exact progress position
  const percentage = Math.min(Math.max(0, (currentVal / maxVal) * 100), 100);
  const exactProgress = (percentage / 100) * size; // This can be a float

  // Generate the bar
  let bar;

  if (useEmojis) {
    if (isMaxLevel) {
      // If at max level, show all filled segments
      if (size === 1) {
        bar = `${emojiLeftFull}${emojiRightFull}`;
      } else {
        bar = `${emojiLeftFull}${emojiMidFull.repeat(Math.max(0, size - 2))}${emojiRightFull}`;
      }
    } else {
      // Build the progress bar segment by segment
      const segments = [];

      for (let i = 0; i < size; i++) {
        // Calculate how much of this segment should be filled
        const segmentProgress = Math.max(0, Math.min(1, exactProgress - i));

        let segmentEmoji;

        if (segmentProgress === 0) {
          // Empty segment
          if (i === 0) {
            segmentEmoji = emojiLeftEmpty;
          } else if (i === size - 1) {
            segmentEmoji = emojiRightEmpty;
          } else {
            segmentEmoji = emojiMidEmpty;
          }
        } else if (segmentProgress >= 1) {
          // Full segment
          if (i === 0) {
            segmentEmoji = emojiLeftFull;
          } else if (i === size - 1) {
            segmentEmoji = emojiRightFull;
          } else {
            segmentEmoji = emojiMidFull;
          }
        } else {
          // Half segment (between 0 and 1)
          if (i === 0) {
            segmentEmoji = emojiLeftHalf;
          } else if (i === size - 1) {
            segmentEmoji = emojiRightHalf;
          } else {
            segmentEmoji = emojiMidHalf;
          }
        }

        segments.push(segmentEmoji);
      }

      bar = segments.join("");
    }
  } else {
    // Classic ASCII bar with square characters
    if (isMaxLevel) {
      bar = `[${"■".repeat(size)}]`;
    } else if (currentVal < 0) {
      bar = `[${"□".repeat(size)}]`;
    } else {
      const progress = Math.floor((percentage / 100) * size);
      bar = `[${"■".repeat(progress)}${"□".repeat(size - progress)}]`;
    }
  }

  // Format percentage with appropriate decimal places
  let percentageStr = "";
  if (showPercentage) {
    if (percentage === 0) {
      percentageStr = "0%";
    } else if (percentage < 1) {
      percentageStr = `${percentage.toFixed(2)}%`;
    } else if (percentage < 10) {
      percentageStr = `${percentage.toFixed(1)}%`;
    } else {
      percentageStr = `${percentage.toFixed(0)}%`;
    }
  }

  // Format values for display
  function formatValue(value) {
    if (formatFn) {
      return formatFn(value);
    }
    return localeString ? Math.floor(value).toLocaleString() : value;
  }

  // Construct the result
  let result = useEmojis
    ? `${bar}${showPercentage ? ` \`${percentageStr}\`` : ""}`
    : `\`${bar}${showPercentage ? ` ${percentageStr}` : ""}\``;

  // Add XP text if requested
  if (showXpText) {
    result += `\n\`${formatValue(currentVal)}/${formatValue(maxVal)}${formatFn ? " XP" : ""}\``;
  }

  return result;
}

/**
 * Safely get the player's purse and bank coin balances.
 * @param {object} playerData - The player data object.
 * @returns {{ purse: number, bank: number }}
 */
function getPlayerCoinBalances(playerData) {
  // Current structure: coins and bank are directly on the player object
  const purse = playerData?.coins ?? 0;
  const bank = playerData?.bank ?? 0;
  return { purse, bank };
}

/**
 * @deprecated This function uses the old currency structure and should not be used.
 * Use updateCurrencyAtomically from playerDataManager instead.
 * Safely update the player's purse or bank coins, supporting both old and new structures.
 * @param {object} player - The player object (mutated in place).
 * @param {'purse'|'bank'} type - Which balance to update.
 * @param {number} amount - Amount to add (can be negative).
 */
function updatePlayerCoins(_player, _type, _amount) {
  console.warn(
    "updatePlayerCoins is deprecated. Use updateCurrencyAtomically instead."
  );
  // Function deprecated - use updateCurrencyAtomically instead
}

/**
 * Creates a standardized embed for displaying action results.
 * @param {object} data - The data for the embed.
 * @param {string} data.mobEmoji - The emoji of the mob.
 * @param {string} data.mobName - The name of the mob.
 * @param {number} data.killedCount - The count of mobs killed (for multi).
 * @param {string} data.title - The main title for the embed.
 * @param {string} data.color - The hex color code for the embed.
 * @param {string} data.skillName - The name of the skill (e.g., 'farming', 'mining').
 * @param {number} data.expGained - Total primary skill EXP gained.
 * @param {number} data.petExpGained - Total Pet EXP gained.
 * @param {number} data.tamingExpGained - Total Taming EXP gained.
 * @param {number} data.slayerExpGained - Total Slayer EXP gained.
 * @param {string} data.slayerType - The type of the slayer (e.g., 'zombie', 'spider').
 * @param {object} data.itemsGained - Object mapping item *keys* to amounts gained (e.g., { WHEAT: 5, POTATO: 2 }).
 * @param {string} data.petEmoji - Optional. Custom emoji for pet EXP.
 * @param {string} data.resourceName - For non-combat skills (e.g., 'Wheat', 'Iron Ore').
 * @param {number} data.resourceAmount - For non-combat skills (e.g., 5, 10).
 * @param {string} data.seaCreaturesBlock - Optional block for sea creatures defeated.
 * @param {string} data.defeatedMobsBlock - Optional block for defeated mobs (e.g., Endermites from mining).
 * @param {number} data.coinsGained - Optional coins gained.
 * @param {object} data.defeatedBy - Optional { emoji, name } object for the mob that defeated the player.
 * @returns {EmbedBuilder} The configured embed builder instance.
 */
function createActionResultEmbed(data) {
  const {
    mobEmoji, // string
    mobName, // string
    killedCount, // number (for multi)
    defeatedMobs, // object with defeated mob details
    color,
    skillName,
    expGained = 0,
    petExpGained = 0,
    tamingExpGained = 0,
    slayerExpGained = 0, // Add slayerExpGained parameter
    slayerType = null, // Add slayerType parameter
    itemsGained = {},
    itemsConsumed = {}, // Add itemsConsumed parameter
    petsGained = [], // Array of pet instances gained
    accessoriesGained = [], // Array of accessories gained
    finalCharacterData,
    finalSkillData,
    petEmoji,
    resourceName, // For non-combat skills (e.g., 'Wheat', 'Iron Ore')
    resourceAmount, // For non-combat skills (e.g., 5, 10)
    coinsGained = 0,
    defeatedBy,
    killerName,
    title,
    seaCreaturesBlock,
    defeatedMobsBlock,
    defeated,
    duration, // Duration in ms
  } = data;

  // --- Check for defeat first - but ALL skills should show accomplishments instead of defeat embed ---
  if (defeated && defeatedBy && defeatedBy.name) {
    // For ALL skills, show accomplishments in main results instead of defeat embed
    // The ping system will handle the defeat notification
    console.log(
      `[DisplayUtils] ${skillName} defeat detected, but showing accomplishments instead of defeat embed`
    );
    // Fall through to normal result display
  }

  // --- Unified result line/title ---
  let resultLine = "";
  if (resourceName && resourceAmount !== undefined) {
    resultLine = `Gained x${resourceAmount} ${resourceName}`;
  } else if (skillName && skillName !== "combat") {
    // Generic fallback for other skills
    resultLine = `${
      skillName.charAt(0).toUpperCase() + skillName.slice(1)
    } action complete.`;
  }

  // --- EXP block ---
  let skillEmoji = "";
  if (skillName) {
    skillEmoji = skillEmojis[skillName.toLowerCase()] || "";
  }
  let expBlock = "";
  if (skillName === "combat" && expGained > 0) {
    expBlock += `${skillEmoji} \`EXP: Combat +${formatExp(expGained)}\`\n`;
  } else if (expGained > 0) {
    expBlock += `${skillEmoji} \`EXP: ${
      skillName.charAt(0).toUpperCase() + skillName.slice(1)
    } +${formatExp(expGained)}\`\n`;
  }
  // Combat EXP gained from secondary encounters (like Endermites during mining)
  if (data.combatExpGained > 0) {
    const combatEmoji = skillEmojis["combat"] || "⚔️";
    expBlock += `${combatEmoji} \`EXP: Combat +${formatExp(data.combatExpGained)}\`\n`;
  }

  // Add Slayer EXP to exp block if available
  if (
    slayerExpGained > 0 ||
    (data.slayer_xp_jsonByType &&
      Object.keys(data.slayer_xp_jsonByType).length > 0)
  ) {
    // Get slayer emoji based on type
    const SLAYER_EMOJIS = {
      zombie: "<:revenant_horror:1389646540460658970>",
      spider: "<:mob_spider:1370526342927618078>",
    };

    // If we have detailed slayer XP breakdown, show all types
    if (
      data.slayer_xp_jsonByType &&
      Object.keys(data.slayer_xp_jsonByType).length > 0
    ) {
      Object.entries(data.slayer_xp_jsonByType).forEach(([type, xp]) => {
        if (xp > 0) {
          const slayerEmoji = SLAYER_EMOJIS[type.toLowerCase()] || "⭐";
          const slayerTypeCapitalized =
            type.charAt(0).toUpperCase() + type.slice(1);
          expBlock += `${slayerEmoji} \`EXP: ${slayerTypeCapitalized} Slayer +${xp}\`\n`;
        }
      });
    } else if (slayerExpGained > 0 && slayerType) {
      // Legacy fallback for backward compatibility
      const slayerEmoji = SLAYER_EMOJIS[slayerType.toLowerCase()] || "⭐";
      const slayerTypeCapitalized =
        slayerType.charAt(0).toUpperCase() + slayerType.slice(1);
      expBlock += `${slayerEmoji} \`EXP: ${slayerTypeCapitalized} Slayer +${slayerExpGained}\`\n`;
    }
  }

  if (petExpGained > 0) {
    let emoji = petEmoji;
    if (
      !emoji &&
      finalCharacterData &&
      finalCharacterData.active_pet_id &&
      Array.isArray(finalCharacterData.pets)
    ) {
      const activePet = finalCharacterData.pets.find(
        (p) => p.id === finalCharacterData.active_pet_id
      );
      if (activePet) {
        const allItems = configManager.getAllItems();
        const petData = allItems[activePet.petKey];
        emoji = petData?.emoji || "🐾";
      } else {
        emoji = "🐾";
      }
    } else if (!emoji) {
      emoji = "🐾";
    }
    expBlock += `${emoji} \`EXP: Pet +${formatExp(petExpGained)}\`\n`;
  }
  if (tamingExpGained > 0) {
    const tamingEmoji = skillEmojis["taming"] || "🦴";
    expBlock += `${tamingEmoji} \`EXP: Taming +${formatExp(tamingExpGained)}\`\n`;
  }

  if (expBlock.endsWith("\n")) expBlock = expBlock.slice(0, -1);

  // --- Coins lines (purse and bank, always at the top of items section) ---
  const coinsLines = [];
  const purseEmoji = "<:purse_coins:1367849116033482772>";
  const bankEmoji = "<:bank_coins:1367849114242383987>";
  const { purse: purseTotal, bank: bankTotal } =
    getPlayerCoinBalances(finalCharacterData);
  let displayPurseTotal = purseTotal;
  if (typeof displayPurseTotal !== "number") {
    displayPurseTotal = finalCharacterData?.coins ?? 0;
  }
  if (
    typeof coinsGained === "number" &&
    finalCharacterData &&
    coinsGained !== 0
  ) {
    if (coinsGained > 0) {
      coinsLines.push(
        `${purseEmoji} \`Coins: +${Math.abs(coinsGained).toFixed(1)} (${displayPurseTotal.toLocaleString()})\``
      );
    } else if (coinsGained < 0) {
      coinsLines.push(
        `${purseEmoji} \`Coins: -${Math.abs(coinsGained).toLocaleString()} (${displayPurseTotal.toLocaleString()})\``
      );
    }
  }
  if (
    typeof data.bankCoinsGained === "number" &&
    finalCharacterData &&
    typeof bankTotal === "number" &&
    data.bankCoinsGained !== 0
  ) {
    if (data.bankCoinsGained > 0) {
      coinsLines.push(
        `${bankEmoji} \`Bank: +${Math.abs(
          data.bankCoinsGained
        ).toLocaleString()} (${bankTotal.toLocaleString()})\``
      );
    } else if (data.bankCoinsGained < 0) {
      coinsLines.push(
        `${bankEmoji} \`Bank: -${Math.abs(
          data.bankCoinsGained
        ).toLocaleString()} (${bankTotal.toLocaleString()})\``
      );
    }
  }

  // --- Items lines ---
  const gainedItemKeys = Object.keys(itemsGained);
  const consumedItemKeys = Object.keys(itemsConsumed);
  let itemsBlock = "";
  if (coinsLines.length > 0) itemsBlock += coinsLines.join("\n") + "\n";
  if (gainedItemKeys.length > 0) {
    const liveItems = configManager.getAllItems();
    gainedItemKeys.forEach((itemKey) => {
      const itemAmount = itemsGained[itemKey];
      const itemData = liveItems[itemKey];
      if (itemData && itemAmount > 0) {
        const emojiString = (itemData.emoji || "❓").trim();

        // Check if item is unique (equipment) - don't show total count for equipment
        if (itemData.unique) {
          itemsBlock += `${emojiString} \`${
            itemData.name
          }: +${itemAmount.toLocaleString()}\`\n`;
        } else {
          // For stackable items, show the total count in parentheses
          const totalAmount =
            finalCharacterData?.inventory?.items?.[itemKey] || 0;
          itemsBlock += `${emojiString} \`${
            itemData.name
          }: +${itemAmount.toLocaleString()} (${totalAmount.toLocaleString()})\`\n`;
        }
      }
    });
    if (itemsBlock.endsWith("\n")) itemsBlock = itemsBlock.slice(0, -1);
  } else {
    if (itemsBlock.endsWith("\n")) itemsBlock = itemsBlock.slice(0, -1);
  }

  // --- Consumed Items lines ---
  if (consumedItemKeys.length > 0) {
    const liveItems = configManager.getAllItems();
    if (itemsBlock && !itemsBlock.endsWith("\n")) itemsBlock += "\n";
    consumedItemKeys.forEach((itemKey) => {
      const itemAmount = itemsConsumed[itemKey];
      const itemData = liveItems[itemKey];
      if (itemData && itemAmount > 0) {
        const emojiString = (itemData.emoji || "❓").trim();
        const totalAmount =
          finalCharacterData?.inventory?.items?.[itemKey] || 0;
        itemsBlock += `${emojiString} \`${itemData.name}: -${itemAmount.toLocaleString()} (${totalAmount.toLocaleString()})\`\n`;
      }
    });
    if (itemsBlock.endsWith("\n")) itemsBlock = itemsBlock.slice(0, -1);
  }

  // --- Pets lines ---
  if (petsGained && Array.isArray(petsGained) && petsGained.length > 0) {
    const liveItems = configManager.getAllItems();
    petsGained.forEach((pet) => {
      if (pet && pet.petKey && pet.rarity) {
        const petData = liveItems[pet.petKey];
        if (petData) {
          const petEmoji = (petData.emoji || "🐾").trim();
          const petName = petData.name || pet.petKey;
          const rarityDisplay =
            pet.rarity.charAt(0).toUpperCase() +
            pet.rarity.slice(1).toLowerCase();

          if (itemsBlock && !itemsBlock.endsWith("\n")) itemsBlock += "\n";
          itemsBlock += `${petEmoji} \`+1 ${rarityDisplay.toUpperCase()} ${petName}\`\n`;
        }
      }
    });
    if (itemsBlock.endsWith("\n")) itemsBlock = itemsBlock.slice(0, -1);
  }

  // --- Accessories lines ---
  if (
    accessoriesGained &&
    Array.isArray(accessoriesGained) &&
    accessoriesGained.length > 0
  ) {
    const liveItems = configManager.getAllItems();
    accessoriesGained.forEach((accessory) => {
      if (accessory && accessory.itemKey) {
        const accessoryData = liveItems[accessory.itemKey];
        if (accessoryData) {
          const accessoryEmoji = (accessoryData.emoji || "💎").trim();
          const accessoryName = accessoryData.name || accessory.itemKey;

          if (itemsBlock && !itemsBlock.endsWith("\n")) itemsBlock += "\n";
          itemsBlock += `${accessoryEmoji} \`+1 ${accessoryName}\`\n`;
        }
      }
    });
    if (itemsBlock.endsWith("\n")) itemsBlock = itemsBlock.slice(0, -1);
  }

  // --- Compose unified description ---
  let description = "";

  // 1. "Defeated by:" section (if applicable)
  if (defeated && defeatedBy && defeatedBy.name) {
    const defeaterEmoji = defeatedBy.emoji || "💀"; // Fallback emoji
    description += `**Defeated by:**\n${defeaterEmoji} \`${defeatedBy.name}\``;
  } else if (defeated && skillName === "fishing") {
    // Fallback if defeatedBy is missing but it was a fishing death
    description += "**Defeated by a Sea Creature!**\n💀";
  }

  // 2. "Defeated Sea Creatures:" section (if applicable for fishing)
  // This should appear regardless of whether the player themselves was defeated, as long as they defeated some sea creatures.
  if (
    skillName === "fishing" &&
    seaCreaturesBlock &&
    seaCreaturesBlock.trim() !== ""
  ) {
    if (description) description += "\n\n"; // Add space if "Defeated by:" was present
    description += `**Defeated Sea Creatures:**\n${seaCreaturesBlock}`;
  }

  if (defeatedMobsBlock && defeatedMobsBlock.trim() !== "") {
    if (description) description += "\n\n";
    description += `**Defeated:**\n${defeatedMobsBlock}`;
  } else if (
    skillName === "combat" &&
    (defeatedMobs || (killedCount && mobEmoji && mobName))
  ) {
    // For regular combat "Defeated:" block
    // This handles the case where it's a non-fishing combat action that involves defeating mobs.
    // If the player was defeated in combat, the defeatedBy logic above would handle the "Defeated by:" part.
    // This part is for showing *what* was defeated if the player was victorious or the action completed.
    if (description) description += "\n\n";

    if (defeatedMobs && Object.keys(defeatedMobs).length > 0) {
      // Expand defeated mobs to handle both optimized and detailed formats
      const { expandDefeatedMobsForDisplay } = require("./mobDisplayUtils");
      const expandedMobs = expandDefeatedMobsForDisplay(defeatedMobs);

      // Show all defeated mobs
      description += "**Defeated:**\n";
      Object.values(expandedMobs).forEach((mob) => {
        description += `${mob.emoji}  \`x${mob.count} ${mob.name}\`\n`;
      });
      // Remove trailing newline
      if (description.endsWith("\n")) description = description.slice(0, -1);
    } else {
      // Fallback to original single mob display
      description += `**Defeated:**\n${mobEmoji}  \`x${killedCount} ${mobName}\``;
    }
  }

  // EXP block handling, considering if it's a titled summary or simpler action
  if (title) {
    // Usually for final skill summaries like /fish, /combat
    if (expBlock) {
      if (description) description += "\n\n"; // Add space if prior content (Defeated by/Defeated mobs)
      description += expBlock;
    }
  } else {
    // For simpler actions that might not have a main title field from buildFinalSkillResultEmbed
    if (resultLine) {
      if (description) description += "\n\n";
      description += resultLine;
    }
    if (expBlock) {
      if (description && !resultLine) description += "\n\n";
      // Space if description had something but not resultLine
      else if (description && resultLine)
        description += "\n"; // Smaller space if resultLine was just added
      else if (!description) {
        /* expBlock is the first thing */
      }
      description += expBlock;
    }
  }

  if (itemsBlock) {
    if (description) description += "\n\n"; // Add space if prior content
    description += itemsBlock;
  }

  // Specific handling for killerName from direct combat, not really for fishing death by sea creature
  if (killerName && title && title.includes("Defeated")) {
    // This prepends "Killed by:" which might be redundant if defeatedBy is already set.
    // For now, let's assume `defeatedBy` is preferred for fishing sea creature deaths.
    // Consider if `killerName` should override or supplement `defeatedBy` logic.
    if (!defeatedBy) {
      // Only prepend if `defeatedBy` wasn't already handled
      description = `Killed by: ${killerName}\n\n` + description;
    }
  }

  if (!description.trim()) {
    // Fallback if description is still empty after all blocks
    if (defeatedBy && defeatedBy.emoji && defeatedBy.name) {
      // Should have been caught by the first block
      description = `**Defeated by:**\n${defeatedBy.emoji}  \`${defeatedBy.name}\``;
    } else if (resultLine) {
      // From non-combat, non-rewarding actions (e.g. fishing up nothing)
      description = resultLine;
    } else if (skillName === "fishing") {
      // Specific fallback for fishing nothing
      description = "You didn't catch anything this time!";
    } else {
      description = "No significant gains this action."; // More generic
    }
  }

  // --- Progress bar ---
  let displayTitle = title || "Results";
  // For combat, remove mob info from the title
  if (skillName === "combat" && !title) {
    displayTitle = "Combat Results";
  }
  const embed = new EmbedBuilder()
    .setColor(color || EMBED_COLORS.BLUE)
    .setTitle(displayTitle)
    .setDescription(description);
  // Add total time footer if provided and enabled in settings
  if (duration != null && finalCharacterData?.settings?.showRuntimeInfo) {
    embed.setFooter({ text: `Runtime: ${formatDuration(duration)}` });
  }

  if (finalSkillData) {
    // Use the raw exp value directly from finalSkillData.exp for consistent level calculation
    const { getLevelFromExp } = require("./expFunctions");
    const { level, currentLevelExp, requiredExpForNextLevel } = getLevelFromExp(
      finalSkillData.exp || 0
    );

    // Use the enhanced progress bar with XP text
    const skillText = createProgressBar(
      currentLevelExp,
      requiredExpForNextLevel,
      {
        size: 10,
        showXpText: true,
        formatFn: formatXP,
      }
    );

    embed.addFields({
      name: `${
        skillName.charAt(0).toUpperCase() + skillName.slice(1)
      } Level ${level}`,
      value: skillText,
    });

    // Add slayer progress bars if slayer XP was gained
    if (
      finalCharacterData &&
      ((slayerExpGained > 0 && slayerType) ||
        (data.slayer_xp_jsonByType &&
          Object.keys(data.slayer_xp_jsonByType).length > 0))
    ) {
      try {
        // Get slayer XP from player data
        const slayerXpData = finalCharacterData.slayerXp || {};
        const { getSlayerLevelFromExp } = require("./slayerLevelUtils");

        // If we have detailed slayer XP breakdown, show progress bars for all types that gained XP
        if (
          data.slayer_xp_jsonByType &&
          Object.keys(data.slayer_xp_jsonByType).length > 0
        ) {
          Object.entries(data.slayer_xp_jsonByType).forEach(
            ([type, xpGained]) => {
              if (xpGained > 0) {
                const totalSlayerXp = slayerXpData[type] || 0;
                const slayerLevelInfo = getSlayerLevelFromExp(totalSlayerXp);
                const slayerTypeCapitalized =
                  type.charAt(0).toUpperCase() + type.slice(1);

                // Generate progress bar for slayer with XP text using enhanced function
                const slayerText = createProgressBar(
                  slayerLevelInfo.currentLevelExp,
                  slayerLevelInfo.requiredExpForNextLevel,
                  {
                    size: 10,
                    showXpText: true,
                    formatFn: formatXP,
                  }
                );

                // Add field for slayer progress
                embed.addFields({
                  name: `${slayerTypeCapitalized} Slayer Level ${slayerLevelInfo.level}`,
                  value: slayerText,
                });
              }
            }
          );
        } else if (slayerExpGained > 0 && slayerType) {
          // Legacy fallback for backward compatibility
          const totalSlayerXp = slayerXpData[slayerType] || 0;
          const slayerLevelInfo = getSlayerLevelFromExp(totalSlayerXp);
          const slayerTypeCapitalized =
            slayerType.charAt(0).toUpperCase() + slayerType.slice(1);

          // Generate progress bar for slayer with XP text using enhanced function
          const slayerText = createProgressBar(
            slayerLevelInfo.currentLevelExp,
            slayerLevelInfo.requiredExpForNextLevel,
            {
              size: 10,
              showXpText: true,
              formatFn: formatXP,
            }
          );

          // Add field for slayer progress
          embed.addFields({
            name: `${slayerTypeCapitalized} Slayer Level ${slayerLevelInfo.level}`,
            value: slayerText,
          });
        }
      } catch (error) {
        console.error(
          "[displayUtils] Error adding slayer progress bar:",
          error
        );
      }
    }
  }

  return embed;
}

function formatItems(items) {
  if (!items || Object.keys(items).length === 0) return "None";
  return Object.entries(items)
    .map(([key, amount]) => {
      const itemData = configManager.getItem(key);
      const emoji = itemData?.emoji || "";
      const name = itemData?.name || key;
      return `${emoji} ${name} x${amount}`;
    })
    .join("\n");
}

/**
 * Centralized builder for final skill result embeds (all skills).
 * @param {object} result - Standardized result object from any skill.
 * @param {object} [options] - Optional display options.
 * @returns {EmbedBuilder}
 */
function buildFinalSkillResultEmbed(result) {
  const {
    mobEmoji,
    mobName,
    mobLevel,
    killedCount,
    color = "#90EE90",
    skillName = "combat",
    exp,
    combatExp,
    petExp,
    tamingExp,
    items,
    coins,
    petsGained,
    accessoriesGained,
    finalCharacterData,
    finalSkillData,
    defeated,
    stopped,
    title,
    specialDrops,
    petEmoji,
    seaCreaturesBlock,
    slayerExp,
    // ...other fields as needed
  } = result;

  // Build the title correctly for each skill
  let mainTitle = title;
  if (!mainTitle) {
    if (skillName === "combat") {
      mainTitle = "Combat Results"; // No colon, no mob info
    } else {
      // Capitalize skill name
      mainTitle = `${
        skillName.charAt(0).toUpperCase() + skillName.slice(1)
      } Results`;
    }
  }
  let embedColor = color || EMBED_COLORS.GREEN;
  if (defeated) embedColor = EMBED_COLORS.RED;

  // Use slayer type directly from result object if provided, otherwise try to extract from quest
  let slayerType = result.slayerType || null;
  if (
    !slayerType &&
    slayerExp > 0 &&
    finalCharacterData &&
    finalCharacterData.active_slayer_quest
  ) {
    try {
      // Fallback: try to extract slayer type from active quest (for backward compatibility)
      const activeQuest =
        typeof finalCharacterData.active_slayer_quest === "string"
          ? JSON.parse(finalCharacterData.active_slayer_quest)
          : finalCharacterData.active_slayer_quest;

      if (activeQuest && activeQuest.type) {
        // Look up the slayer type from mob data dynamically
        const mobData = configManager.getMob(activeQuest.type);

        if (mobData && mobData.slayer && mobData.slayer.type) {
          slayerType = mobData.slayer.type;
        }
      }
    } catch (error) {
      console.error("[displayUtils] Error extracting slayer type:", error);
    }
  }

  return createActionResultEmbed({
    title: mainTitle,
    mobEmoji: skillName === "combat" ? mobEmoji : undefined,
    mobName: skillName === "combat" ? mobName : undefined,
    mobLevel: skillName === "combat" ? mobLevel : undefined,
    killedCount: skillName === "combat" ? killedCount : undefined,
    defeatedMobs: result.defeatedMobs,
    color: embedColor,
    skillName,
    expGained: exp,
    combatExpGained: combatExp,
    petExpGained: petExp,
    tamingExpGained: tamingExp,
    slayerExpGained: slayerExp,
    slayerXpByType: result.slayer_xp_jsonByType, // Pass detailed slayer XP breakdown
    slayerType: slayerType,
    itemsGained: items,
    itemsConsumed: result.consumedItems, // Pass consumed items
    petsGained: petsGained,
    accessoriesGained: accessoriesGained,
    coinsGained: coins,
    finalCharacterData,
    finalSkillData,
    specialDrops,
    petEmoji: skillName === "combat" ? petEmoji : undefined,
    stopped,
    defeated,
    defeatedBy: result.defeatedBy,
    duration: result.duration,
    // Always include seaCreaturesBlock for fishing
    ...(skillName === "fishing" && seaCreaturesBlock
      ? { seaCreaturesBlock }
      : {}),
    // Include defeatedMobsBlock for any skill that has defeated mobs
    ...(result.defeatedMobsBlock
      ? { defeatedMobsBlock: result.defeatedMobsBlock }
      : {}),

    // ...other fields as needed
  });
}

/**
 * Formats a number with k/m suffixes for readability
 * @param {number} num - The number to format
 * @returns {string} - Formatted number string
 */
function formatNumber(num) {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1).replace(/\.0$/, "") + "B";
  } else if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, "") + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, "") + "k";
  } else {
    return num.toString();
  }
}

/**
 * Formats experience values to avoid long decimal places
 * @param {number} exp - The experience value to format
 * @returns {string} - Formatted experience string
 */
function formatExp(exp) {
  if (typeof exp !== "number" || isNaN(exp)) {
    return "0.00";
  }

  return exp.toFixed(2);
}

/**
 * Formats a duration in milliseconds into a human-readable string (e.g., '1h 2m 3s').
 * @param {number} ms - Duration in milliseconds
 * @returns {string}
 */
function formatDuration(ms) {
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  const parts = [];
  if (hours) parts.push(`${hours}h`);
  if (minutes) parts.push(`${minutes}m`);
  parts.push(`${seconds}s`);
  return parts.join(" ");
}

module.exports = {
  createProgressBar,
  getPlayerCoinBalances,
  updatePlayerCoins,
  createActionResultEmbed,
  buildFinalSkillResultEmbed,
  formatItems,
  formatNumber,
  formatExp,
};
