const { PermissionFlagsBits } = require("discord.js");
const config = require("../config.json");

/**
 * Get the expected permissions for the player channels category
 * @returns {Array} - Array of permission overwrite objects
 */
function getExpectedCategoryPermissions() {
  const allowedRoles = config.channelCreation.allowedRoles || [];

  const permissionOverwrites = [
    // deny @everyone from viewing and using slash commands
    {
      id: config.guildId, // @everyone role has the same ID as the guild
      deny: [
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.UseApplicationCommands,
      ].filter((flag) => flag !== undefined),
    },
  ];

  // add permissions for each allowed role
  allowedRoles.forEach((roleId) => {
    if (roleId && roleId.trim()) {
      permissionOverwrites.push({
        id: roleId,
        allow: [
          PermissionFlagsBits.ViewChannel,
          PermissionFlagsBits.ReadMessageHistory,
          PermissionFlagsBits.SendMessages,
          PermissionFlagsBits.UseExternalEmojis,
          PermissionFlagsBits.UseExternalStickers,
          PermissionFlagsBits.AddReactions,
        ].filter((flag) => flag !== undefined),
        deny: [
          PermissionFlagsBits.UseApplicationCommands,
          PermissionFlagsBits.MentionEveryone,
        ].filter((flag) => flag !== undefined),
      });
    }
  });

  return permissionOverwrites;
}

/**
 * Validate and fix the player channels category permissions
 * @param {Client} client - Discord client
 * @returns {Promise<boolean>} - Whether validation was successful
 */
async function validateCategoryPermissions(client) {
  try {
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(
        "[CategoryValidator] Validating player channels category permissions..."
      );

    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      console.error("[CategoryValidator] Guild not found:", config.guildId);
      return false;
    }

    const categoryId = config.channelCreation.playerChannelsCategoryId;
    if (!categoryId) {
      console.error(
        "[CategoryValidator] playerChannelsCategoryId not configured"
      );
      return false;
    }

    // fetch the category
    const category = await guild.channels.fetch(categoryId).catch(() => null);
    if (!category) {
      console.error(`[CategoryValidator] Category ${categoryId} not found`);
      return false;
    }

    const expectedPermissions = getExpectedCategoryPermissions();
    const currentOverwrites = category.permissionOverwrites.cache;

    let needsUpdate = false;
    const issues = [];

    // check if we need to update permissions
    for (const expectedPerm of expectedPermissions) {
      const currentOverwrite = currentOverwrites.get(expectedPerm.id);

      if (!currentOverwrite) {
        issues.push(`Missing permission overwrite for ${expectedPerm.id}`);
        needsUpdate = true;
        continue;
      }

      // check specific permissions
      if (expectedPerm.allow) {
        for (const permission of expectedPerm.allow) {
          if (!currentOverwrite.allow.has(permission)) {
            issues.push(
              `Missing allow permission ${permission} for ${expectedPerm.id}`
            );
            needsUpdate = true;
          }
        }
      }

      if (expectedPerm.deny) {
        for (const permission of expectedPerm.deny) {
          if (!currentOverwrite.deny.has(permission)) {
            issues.push(
              `Missing deny permission ${permission} for ${expectedPerm.id}`
            );
            needsUpdate = true;
          }
        }
      }
    }

    // check for unexpected overwrites (we'll preserve them but log them)
    for (const [currentId, _currentOverwrite] of currentOverwrites) {
      const isExpected = expectedPermissions.some(
        (perm) => perm.id === currentId
      );
      if (!isExpected) {
        if (!QUIET_BOOT)
          console.log(
            `[CategoryValidator] Found unexpected permission overwrite for ${currentId} (preserving)`
          );
      }
    }

    if (needsUpdate) {
      if (!QUIET_BOOT)
        console.log(
          `[CategoryValidator] Category permissions need updating. Issues found:`,
          issues
        );

      try {
        // validate each permission before applying
        const validPermissions = [];
        for (const perm of expectedPermissions) {
          if (perm.id === config.guildId) {
            // @everyone permission - always valid
            validPermissions.push(perm);
          } else {
            // role permission - check if role exists
            const role = guild.roles.cache.get(perm.id);
            if (role) {
              validPermissions.push(perm);
            } else {
              console.warn(
                `[CategoryValidator] Role ${perm.id} not found, skipping`
              );
            }
          }
        }

        if (validPermissions.length > 0) {
          // merge with existing permissions to preserve any admin/bot permissions
          const mergedPermissions = [...validPermissions];

          // preserve any existing overwrites that aren't in our expected list
          for (const [existingId, existingOverwrite] of currentOverwrites) {
            const isInOurList = validPermissions.some(
              (perm) => perm.id === existingId
            );
            if (!isInOurList) {
              // preserve this existing permission
              mergedPermissions.push({
                id: existingId,
                allow: existingOverwrite.allow.bitfield
                  ? Array.from(existingOverwrite.allow)
                  : [],
                deny: existingOverwrite.deny.bitfield
                  ? Array.from(existingOverwrite.deny)
                  : [],
              });
            }
          }

          await category.permissionOverwrites.set(mergedPermissions);
          if (!QUIET_BOOT)
            console.log(
              `[CategoryValidator] ✅ Successfully updated category permissions`
            );
        } else {
          console.warn(`[CategoryValidator] No valid permissions to apply`);
        }
      } catch (permError) {
        console.error(
          `[CategoryValidator] Failed to update category permissions:`,
          permError.message
        );
        return false;
      }
    } else {
      if (!QUIET_BOOT)
        console.log(
          `[CategoryValidator] ✅ Category permissions are already correct`
        );
    }

    return true;
  } catch (error) {
    console.error(
      "[CategoryValidator] Error validating category permissions:",
      error
    );
    return false;
  }
}

module.exports = {
  validateCategoryPermissions,
  getExpectedCategoryPermissions,
};
