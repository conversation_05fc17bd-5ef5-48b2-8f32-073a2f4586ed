const { PermissionFlagsBits } = require("discord.js");
const config = require("../config.json");

/**
 * Get the standardized permission overwrites for a personal channel
 * @param {string} ownerId - Discord ID of the channel owner
 * @returns {Array} - Array of permission overwrite objects
 */
function getPersonalChannelPermissions(ownerId) {
  // For category sync, we only add the minimal override needed
  // Everything else should inherit from the category
  const permissionOverwrites = [
    // channel owner gets slash command permission and manage messages (overrides category deny)
    {
      id: ownerId,
      allow: [
        PermissionFlagsBits.UseApplicationCommands, // only owner can use slash commands
        PermissionFlagsBits.ManageMessages, // owner can pin messages, delete messages, etc.
        // Note: Discord doesn't have separate permissions for pin vs delete
        // ManageMessages includes: pin/unpin, delete others' messages, manage reactions
      ].filter((flag) => flag !== undefined),
      // Note: No deny permissions and no other allow permissions to maintain category sync
    },
  ];

  return permissionOverwrites;
}

/**
 * Get the standardized channel topic for a personal channel
 * @param {string} characterName - The character's name
 * @returns {string} - The channel topic
 */
function getPersonalChannelTopic(characterName) {
  return `${characterName}'s personal DisBlock channel :)`;
}

/**
 * Get all the personal channel configuration in one place
 * @param {string} characterName - The character's name
 * @param {string} ownerId - Discord ID of the channel owner
 * @returns {Object} - Complete channel configuration
 */
function getPersonalChannelConfig(characterName, ownerId) {
  return {
    topic: getPersonalChannelTopic(characterName),
    permissions: getPersonalChannelPermissions(ownerId),
    allowedRoles: config.channelCreation.allowedRoles || [],
    categoryId: config.channelCreation.playerChannelsCategoryId,
  };
}

module.exports = {
  getPersonalChannelPermissions,
  getPersonalChannelTopic,
  getPersonalChannelConfig,
};
