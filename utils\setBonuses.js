// Data-driven set bonus system for all armor sets and future expansion

// Mapping from bonus property names to stat names
const BONUS_TO_STAT_MAPPING = {
  farmingFortune: "FARMING_FORTUNE",
  farmingSweep: "FARMING_SWEEP",
  miningSweep: "MINING_SWEEP",
  health: "HEALTH",
  defense: "DEFENSE",
  strength: "STRENGTH",
  critDamage: "CRIT_DAMAGE",
  critChance: "CRIT_CHANCE",
  intelligence: "INTELLIGENCE",
  miningFortune: "MINING_FORTUNE",
  foragingFortune: "FORAGING_FORTUNE",
  fishingSpeed: "FISHING_SPEED",
  seaCreatureChance: "SEA_CREATURE_CHANCE",
  // Add more mappings as needed
};

/**
 * Automatically converts bonus properties to stat bonuses using the mapping
 */
function convertBonusToStatBonuses(bonus) {
  const statBonuses = {};

  for (const [bonusKey, bonusValue] of Object.entries(bonus)) {
    const statName = BONUS_TO_STAT_MAPPING[bonusKey];
    if (statName && typeof bonusValue === "number") {
      statBonuses[statName] = bonusValue;
    }
  }

  return statBonuses;
}

const SET_BONUSES = [
  {
    key: "angler",
    itemKeys: [
      "ANGLER_HELMET",
      "ANGLER_CHESTPLATE",
      "ANGLER_LEGGINGS",
      "ANGLER_BOOTS",
      "ANGLER_NECKLACE",
      "ANGLER_CLOAK",
      "ANGLER_BRACELET",
      "ANGLER_BELT",
    ],
    thresholds: [
      { pieces: 2, bonus: { damageReduction: 0.03, healthPerLevel: 6 } },
      { pieces: 3, bonus: { damageReduction: 0.04, healthPerLevel: 8 } },
      { pieces: 4, bonus: { damageReduction: 0.05, healthPerLevel: 10 } },
      { pieces: 6, bonus: { damageReduction: 0.06, healthPerLevel: 12 } },
      { pieces: 8, bonus: { damageReduction: 0.08, healthPerLevel: 15 } },
    ],
  },
  {
    key: "golem",
    itemKeys: [
      "GOLEM_ARMOR_HELMET",
      "GOLEM_ARMOR_CHESTPLATE",
      "GOLEM_ARMOR_LEGGINGS",
      "GOLEM_ARMOR_BOOTS",
    ],
    thresholds: [{ pieces: 4, bonus: { health: 45, defense: 50 } }],
  },
  {
    key: "emerald",
    itemKeys: [
      "EMERALD_ARMOR_HELMET",
      "EMERALD_ARMOR_CHESTPLATE",
      "EMERALD_ARMOR_LEGGINGS",
      "EMERALD_ARMOR_BOOTS",
    ],
    thresholds: [
      {
        pieces: 4,
        bonus: {
          collectionBased: true,
          collectionKey: "EMERALD",
          perAmount: 3000,
          maxBonus: 350,
        },
      },
    ],
  },
  {
    key: "ender",
    itemKeys: [
      "ENDER_ARMOR_HELMET",
      "ENDER_ARMOR_CHESTPLATE",
      "ENDER_ARMOR_LEGGINGS",
      "ENDER_ARMOR_BOOTS",
    ],
    thresholds: [
      { pieces: 1, bonus: { enderBonus: true } },
      { pieces: 2, bonus: { enderBonus: true } },
      { pieces: 3, bonus: { enderBonus: true } },
      { pieces: 4, bonus: { enderBonus: true } },
    ],
  },
  {
    key: "melon",
    itemKeys: [
      "MELON_HELMET",
      "MELON_CHESTPLATE",
      "MELON_LEGGINGS",
      "MELON_BOOTS",
    ],
    thresholds: [
      {
        pieces: 2,
        bonus: {
          farmingFortune: 10,
          farmingSweep: 1,
          cropieDropChance: 0.0003,
        },
      },
      {
        pieces: 3,
        bonus: {
          farmingFortune: 20,
          farmingSweep: 2,
          cropieDropChance: 0.0004,
        },
      },
      {
        pieces: 4,
        bonus: {
          farmingFortune: 30,
          farmingSweep: 2,
          cropieDropChance: 0.0005,
        },
      },
    ],
  },
  {
    key: "cropie",
    itemKeys: [
      "CROPIE_HELMET",
      "CROPIE_CHESTPLATE",
      "CROPIE_LEGGINGS",
      "CROPIE_BOOTS",
    ],
    thresholds: [
      {
        pieces: 2,
        bonus: {
          farmingFortune: 15,
          farmingSweep: 2,
          squashDropChance: 0.0001,
        },
      },
      {
        pieces: 3,
        bonus: {
          farmingFortune: 30,
          farmingSweep: 3,
          squashDropChance: 0.0002,
        },
      },
      {
        pieces: 4,
        bonus: {
          farmingFortune: 45,
          farmingSweep: 3,
          squashDropChance: 0.0003,
        },
      },
    ],
  },
  {
    key: "squash",
    itemKeys: [
      "SQUASH_HELMET",
      "SQUASH_CHESTPLATE",
      "SQUASH_LEGGINGS",
      "SQUASH_BOOTS",
    ],
    thresholds: [
      {
        pieces: 2,
        bonus: {
          farmingFortune: 20,
          farmingSweep: 3,
          fermentoDropChance: 0.00005,
        },
      },
      {
        pieces: 3,
        bonus: {
          farmingFortune: 40,
          farmingSweep: 4,
          fermentoDropChance: 0.00006,
        },
      },
      {
        pieces: 4,
        bonus: {
          farmingFortune: 60,
          farmingSweep: 4,
          fermentoDropChance: 0.00007,
        },
      },
    ],
  },
  {
    key: "fermento",
    itemKeys: [
      "FERMENTO_HELMET",
      "FERMENTO_CHESTPLATE",
      "FERMENTO_LEGGINGS",
      "FERMENTO_BOOTS",
    ],
    // Combines Melon + Cropie + Squash tiered bonuses and adds +1 farming sweep vs Squash
    thresholds: [
      {
        pieces: 2,
        bonus: {
          farmingFortune: 25,
          farmingSweep: 4,
          cropieDropChance: 0.0003,
          squashDropChance: 0.0001,
          fermentoDropChance: 0.00005,
        },
      },
      {
        pieces: 3,
        bonus: {
          farmingFortune: 50,
          farmingSweep: 5,
          cropieDropChance: 0.0004,
          squashDropChance: 0.0002,
          fermentoDropChance: 0.00006,
        },
      },
      {
        pieces: 4,
        bonus: {
          farmingFortune: 75,
          farmingSweep: 5,
          cropieDropChance: 0.0005,
          squashDropChance: 0.0003,
          fermentoDropChance: 0.00007,
        },
      },
    ],
  },
  {
    key: "prospecting",
    itemKeys: [
      "PROSPECTING_HELMET",
      "PROSPECTING_CHESTPLATE",
      "PROSPECTING_LEGGINGS",
      "PROSPECTING_BOOTS",
    ],
    thresholds: [{ pieces: 4, bonus: { miningSweep: 1 } }],
  },
  {
    key: "tarantula",
    itemKeys: [
      "TARANTULA_HELMET",
      "TARANTULA_CHESTPLATE",
      "TARANTULA_LEGGINGS",
      "TARANTULA_BOOTS",
    ],
    thresholds: [{ pieces: 4, bonus: { octodexterity: true } }],
  },
  // Add more sets here as you expand the game!
];

function getActiveSetBonuses(character) {
  const bonuses = {};

  // Check both new and old equipment systems
  let equippedItems = [];

  // New system: character.equipment.equipped
  if (character.equipment && character.equipment.equipped) {
    const newSystemItems = Object.values(character.equipment.equipped)
      .filter((item) => item)
      .map((item) => item.itemKey);
    equippedItems = equippedItems.concat(newSystemItems);
  }

  // Old system: character.inventory.equipment (fallback)
  if (character.inventory && character.inventory.equipment) {
    const oldSystemItems = character.inventory.equipment
      .filter((item) => item.isEquipped)
      .map((item) => item.itemKey);
    equippedItems = equippedItems.concat(oldSystemItems);
  }

  if (equippedItems.length === 0) {
    return {};
  }

  for (const set of SET_BONUSES) {
    let count = 0;
    for (const itemKey of equippedItems) {
      if (set.itemKeys.includes(itemKey)) {
        count++;
      }
    }

    // Find the highest threshold met
    const threshold = [...set.thresholds]
      .reverse()
      .find((t) => count >= t.pieces);
    if (threshold) {
      bonuses[set.key] = {
        pieces: count,
        ...threshold.bonus,
        // Automatically generate stat bonuses from bonus properties
        statBonuses: convertBonusToStatBonuses(threshold.bonus),
      };
    }
  }

  return bonuses;
}

/**
 * Centralized function to apply all set bonuses to character stats
 * This replaces the manual if-statements for each armor set
 */
function applySetBonusesToStats(character, setBonuses) {
  // Initialize all fromSetBonus values to 0 to prevent stale values
  for (const statName in character.stats) {
    character.stats[statName].fromSetBonus = 0;
  }

  // Apply stat bonuses from all active sets
  for (const [, setData] of Object.entries(setBonuses)) {
    if (setData.statBonuses) {
      for (const [statName, bonusValue] of Object.entries(
        setData.statBonuses
      )) {
        if (character.stats[statName]) {
          character.stats[statName].fromSetBonus += bonusValue;
        }
      }
    }
  }

  // Handle special cases that need custom logic
  handleSpecialSetBonuses(character, setBonuses);
}

/**
 * Handle set bonuses that require special logic (like level-based or collection-based bonuses)
 */
function handleSpecialSetBonuses(character, setBonuses) {
  // Angler set bonus - health per fishing level
  if (setBonuses.angler && setBonuses.angler.healthPerLevel) {
    const { getLevelFromExp } = require("./expFunctions");
    const fishingExp = character.skills?.fishing?.exp || 0;
    const fishingLevel = getLevelFromExp(fishingExp).level;
    const healthBonus = setBonuses.angler.healthPerLevel * fishingLevel;

    if (character.stats.HEALTH) {
      character.stats.HEALTH.fromSetBonus += healthBonus;
    }
  }

  // Ender Armor set bonus - handled by separate enderArmorSetBonus.js system
  // This is just a marker in the centralized system, actual logic is in enderArmorSetBonus.js
  // The bonus is applied later in playerDataManager.js after calculateAllStats()

  // Golem set bonus is now handled automatically through the mapping system
  // No special handling needed since health/defense are mapped stats

  // Emerald Armor Tank Set Bonus - Collection-based
  if (setBonuses.emerald && setBonuses.emerald.collectionBased) {
    const collectionKey = setBonuses.emerald.collectionKey;
    const perAmount = setBonuses.emerald.perAmount;
    const maxBonus = setBonuses.emerald.maxBonus;

    // Get player's collection amount for the specified collection
    const collectionAmount = character.collections?.[collectionKey] || 0;

    // Calculate bonus: floor(collectionAmount / perAmount) capped at maxBonus
    const calculatedBonus = Math.min(
      Math.floor(collectionAmount / perAmount),
      maxBonus
    );

    if (calculatedBonus > 0) {
      if (character.stats.HEALTH) {
        character.stats.HEALTH.fromSetBonus += calculatedBonus;
      }
      if (character.stats.DEFENSE) {
        character.stats.DEFENSE.fromSetBonus += calculatedBonus;
      }
    }
  }
}

module.exports = { getActiveSetBonuses, SET_BONUSES, applySetBonusesToStats };
