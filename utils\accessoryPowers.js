// Shared constants for Accessory Power system
// This centralizes values used in multiple modules (<PERSON> command, player stats, XP systems, etc.)
// so they stay perfectly in-sync.

// How much Magical Power an accessory contributes, based on its rarity
const MAGICAL_POWER_VALUES = {
  COMMON: 3,
  UNCOMMON: 5,
  RARE: 8,
  EPIC: 12,
  LEGENDARY: 16,
};

// Stat multipliers used in Hypixel-style accessory power formula
const STAT_MULTIPLIERS = {
  INTELLIGENCE: 1.5,
  HEALTH: 1.4,
  STRENGTH: 1,
  DEFENSE: 1,
  CRIT_DAMAGE: 1,
  CRIT_CHANCE: 0.4,
};

// Full definitions for every selectable Accessory Power.
// Note: name / emoji / description are used by the Maxwell menu, while the
// `stats` object is consumed by the stats-recalculation system.
const ACCESSORY_POWERS = {
  FORTUITOUS: {
    name: "Fortuitous",
    emoji: "🍀",
    description: "Balanced stats with focus on combat",
    stats: {
      STRENGTH: { basePower: 20, multiplier: STAT_MULTIPLIERS.STRENGTH },
      DEFENSE: { basePower: 5, multiplier: STAT_MULTIPLIERS.DEFENSE },
      HEALTH: { basePower: 45, multiplier: STAT_MULTIPLIERS.HEALTH },
      CRIT_CHANCE: { basePower: 20, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 20, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  PRETTY: {
    name: "Pretty",
    emoji: "✨",
    description: "Well-rounded stats for all situations",
    stats: {
      STRENGTH: { basePower: 20, multiplier: STAT_MULTIPLIERS.STRENGTH },
      DEFENSE: { basePower: 5, multiplier: STAT_MULTIPLIERS.DEFENSE },
      HEALTH: { basePower: 5, multiplier: STAT_MULTIPLIERS.HEALTH },
      INTELLIGENCE: {
        basePower: 15,
        multiplier: STAT_MULTIPLIERS.INTELLIGENCE,
      },
      CRIT_CHANCE: { basePower: 20, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 30, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  PROTECTED: {
    name: "Protected",
    emoji: "🛡️",
    description: "Defensive focus with some combat stats",
    stats: {
      HEALTH: { basePower: 10, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 45, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 35, multiplier: STAT_MULTIPLIERS.STRENGTH },
      CRIT_CHANCE: { basePower: 5, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 5, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  SIMPLE: {
    name: "Simple",
    emoji: "⚪",
    description: "Basic balanced stats for beginners",
    stats: {
      HEALTH: { basePower: 15, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 15, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 15, multiplier: STAT_MULTIPLIERS.STRENGTH },
      INTELLIGENCE: {
        basePower: 15,
        multiplier: STAT_MULTIPLIERS.INTELLIGENCE,
      },
      CRIT_CHANCE: { basePower: 15, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 15, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  WARRIOR: {
    name: "Warrior",
    emoji: "⚔️",
    description: "Combat-focused with high strength",
    stats: {
      HEALTH: { basePower: 35, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 5, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 10, multiplier: STAT_MULTIPLIERS.STRENGTH },
      CRIT_CHANCE: { basePower: 25, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 25, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  COMMANDO: {
    name: "Commando",
    emoji: "🎖️",
    description: "Military precision with balanced combat stats",
    stats: {
      HEALTH: { basePower: 35, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 10, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 15, multiplier: STAT_MULTIPLIERS.STRENGTH },
      CRIT_CHANCE: { basePower: 5, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 35, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  DISCIPLINED: {
    name: "Disciplined",
    emoji: "🎯",
    description: "Focused training with defensive benefits",
    stats: {
      HEALTH: { basePower: 30, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 10, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 15, multiplier: STAT_MULTIPLIERS.STRENGTH },
      CRIT_CHANCE: { basePower: 15, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 30, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  INSPIRED: {
    name: "Inspired",
    emoji: "💡",
    description: "Intelligence-focused with magical benefits",
    stats: {
      HEALTH: { basePower: 20, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 5, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 5, multiplier: STAT_MULTIPLIERS.STRENGTH },
      INTELLIGENCE: {
        basePower: 10,
        multiplier: STAT_MULTIPLIERS.INTELLIGENCE,
      },
      CRIT_CHANCE: { basePower: 15, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 45, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  OMINOUS: {
    name: "Ominous",
    emoji: "🌙",
    description: "Dark power with diverse stat bonuses",
    stats: {
      HEALTH: { basePower: 15, multiplier: STAT_MULTIPLIERS.HEALTH },
      STRENGTH: { basePower: 15, multiplier: STAT_MULTIPLIERS.STRENGTH },
      INTELLIGENCE: {
        basePower: 15,
        multiplier: STAT_MULTIPLIERS.INTELLIGENCE,
      },
      CRIT_CHANCE: { basePower: 15, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 12, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
  PREPARED: {
    name: "Prepared",
    emoji: "🎒",
    description: "Ready for anything with defensive focus",
    stats: {
      HEALTH: { basePower: 8, multiplier: STAT_MULTIPLIERS.HEALTH },
      DEFENSE: { basePower: 47, multiplier: STAT_MULTIPLIERS.DEFENSE },
      STRENGTH: { basePower: 37, multiplier: STAT_MULTIPLIERS.STRENGTH },
      CRIT_CHANCE: { basePower: 4, multiplier: STAT_MULTIPLIERS.CRIT_CHANCE },
      CRIT_DAMAGE: { basePower: 4, multiplier: STAT_MULTIPLIERS.CRIT_DAMAGE },
    },
  },
};

// Utility: calculate bonus from accessory power stats using Hypixel-like formula
function calculateAccessoryStat(basePower, statMultiplier, magicalPower) {
  if (magicalPower === 0) return 0;
  const result =
    (basePower / 100) *
    statMultiplier *
    719.28 *
    Math.pow(Math.log(1 + 0.0019 * magicalPower), 1.2);
  return Math.floor(result);
}

module.exports = {
  MAGICAL_POWER_VALUES,
  STAT_MULTIPLIERS,
  ACCESSORY_POWERS,
  calculateAccessoryStat,
};
