const db = require("./database");

// Simple write-queue that batches SQL write statements (INSERT/UPDATE/DELETE)
// submitted within the same event-loop tick. All queued statements are
// executed inside one BEGIN IMMEDIATE … COMMIT transaction, which acquires
// write locks immediately to prevent SQLITE_BUSY conflicts.
//
//  enqueue(sql, params?) returns a Promise that resolves with the usual
//  { lastID, changes } object you would get from db.run.
//
//  The queue auto-flushes on the next tick; if more jobs arrive while a flush
//  is running they will be picked up by the subsequent flush.
//
//  SQLITE_BUSY Prevention Strategy:
//  - Use BEGIN IMMEDIATE to acquire write lock immediately
//  - Retry on SQLITE_BUSY errors with exponential backoff
//  - Process smaller batches (50 operations) to reduce lock time

const queue = [];
let flushing = false;

function enqueue(sql, params = []) {
  return new Promise((resolve, reject) => {
    queue.push({ sql, params, resolve, reject });
    scheduleFlush();
  });
}

function scheduleFlush() {
  if (flushing) return; // A flush is already scheduled/in progress
  // Use setImmediate for better async processing
  setImmediate(flush);
}

async function flush() {
  if (flushing || queue.length === 0) return;
  flushing = true;

  // OPTIMIZATION: Process smaller batches to reduce lock time
  // Take up to 20 jobs at a time to reduce transaction duration and contention
  const batchSize = Math.min(20, queue.length);
  const jobs = queue.splice(0, batchSize);

  // Helper that wraps db.run in a Promise with retry logic for SQLITE_BUSY
  const runP = (sql, params = []) =>
    new Promise((res, rej) => {
      const maxRetries = 5; // total attempts = initial + retries (maxRetries)
      const attemptRun = (attempt) => {
        db.run(sql, params, function (err) {
          if (err) {
            // retry on SQLITE_BUSY errors
            if (err.message.includes("SQLITE_BUSY") && attempt < maxRetries) {
              // Only log when scheduling the final attempt
              if (attempt === maxRetries - 1) {
                console.warn(
                  `WriteQueue SQLITE_BUSY - final retry ${attempt + 1}/${maxRetries} for:`,
                  sql.substring(0, 30) + "..."
                );
              }
              const backoff = Math.min(400, 50 * (attempt + 1));
              setTimeout(() => attemptRun(attempt + 1), backoff);
              return;
            }
            return rej(err);
          }
          // Expose lastID/changes like regular db.run
          res({ lastID: this.lastID, changes: this.changes });
        });
      };
      attemptRun(0);
    });

  try {
    // use IMMEDIATE to acquire write lock immediately and prevent conflicts
    await runP("BEGIN IMMEDIATE");
    for (const job of jobs) {
      try {
        const res = await runP(job.sql, job.params);
        job.resolve(res);
      } catch (stmtErr) {
        job.reject(stmtErr);
        throw stmtErr; // abort the whole txn
      }
    }
    await runP("COMMIT");
  } catch (err) {
    try {
      await runP("ROLLBACK");
    } catch (rollbackError) {
      /* ignore rollback failures */
      console.warn("Rollback failed:", rollbackError.message);
    }
    // Reject any jobs that haven't been resolved yet
    jobs.forEach((job) => {
      if (job.resolve) job.reject(err);
    });
  } finally {
    flushing = false;
    // If more work arrived while we were flushing, process it now.
    if (queue.length > 0) scheduleFlush();
  }
}

// drainQueue: flushes all pending jobs and waits for completion
async function drainQueue() {
  while (queue.length > 0 || flushing) {
    if (!flushing) {
      await flush();
    }
    // wait a tick for any new jobs
    await new Promise((resolve) => setTimeout(resolve, 10));
  }
}

module.exports = {
  enqueue,
  drainQueue,
};
