/**
 * Crash Monitor Utility
 * Provides enhanced monitoring and reporting for bot crashes
 */

const fs = require("fs").promises;
const path = require("path");
let Sentry = null;
try {
  Sentry = require("@sentry/node");
} catch {
  /* optional */
}

class CrashMonitor {
  constructor(options = {}) {
    this.options = {
      logDirectory:
        options.logDirectory || path.join(__dirname, "../logs/crashes"),
      retentionDays: options.retentionDays || 7,
      maxLogSize: options.maxLogSize || 10 * 1024 * 1024, // 10MB
      enableMetrics: options.enableMetrics !== false,
      ...options,
    };

    this.crashHistory = [];
    this.startTime = Date.now();
    this.lastCrashTime = null;
  }

  /**
   * Record a crash event
   */
  async recordCrash(type, error, context = {}) {
    const crashEvent = {
      timestamp: new Date().toISOString(),
      type: type,
      error: {
        message: error.message || error.toString(),
        stack: error.stack,
        code: error.code,
        name: error.name,
      },
      context: {
        ...context,
        pid: process.pid,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        platform: process.platform,
        nodeVersion: process.version,
      },
    };

    if (Sentry) {
      // capture without double-reporting known ignorable patterns
      Sentry.captureException(error, {
        tags: { monitor: "CrashMonitor", type },
        extra: { context },
      });
    }

    // Add to crash history
    this.crashHistory.push(crashEvent);
    this.lastCrashTime = Date.now();

    // Keep only recent crashes
    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours
    this.crashHistory = this.crashHistory.filter(
      (crash) => new Date(crash.timestamp).getTime() > cutoff
    );

    // Log to file
    await this.logCrashToFile(crashEvent);

    // Analyze crash patterns
    const analysis = this.analyzeCrashPatterns();
    if (analysis.warnings.length > 0) {
      console.warn(
        "[CrashMonitor] Pattern analysis warnings:",
        analysis.warnings
      );
    }

    return crashEvent;
  }

  /**
   * Log crash to file
   */
  async logCrashToFile(crashEvent) {
    try {
      await fs.mkdir(this.options.logDirectory, { recursive: true });

      const filename = `crash_${new Date().toISOString().split("T")[0]}.log`;
      const filepath = path.join(this.options.logDirectory, filename);

      const logEntry =
        `${crashEvent.timestamp} [${crashEvent.type}]\n` +
        `Error: ${crashEvent.error.message}\n` +
        `Stack: ${crashEvent.error.stack}\n` +
        `Context: ${JSON.stringify(crashEvent.context, null, 2)}\n` +
        `${"=".repeat(80)}\n`;

      await fs.appendFile(filepath, logEntry);
    } catch (logError) {
      console.error("[CrashMonitor] Failed to log crash to file:", logError);
    }
  }

  /**
   * Analyze crash patterns for insights
   */
  analyzeCrashPatterns() {
    const warnings = [];
    const recentCrashes = this.crashHistory.slice(-10); // Last 10 crashes

    if (recentCrashes.length === 0) {
      return { warnings, patterns: {} };
    }

    // Check for rapid successive crashes
    const rapidCrashes = recentCrashes.filter((crash, index) => {
      if (index === 0) return false;
      const prevCrash = recentCrashes[index - 1];
      const timeDiff =
        new Date(crash.timestamp).getTime() -
        new Date(prevCrash.timestamp).getTime();
      return timeDiff < 60000; // Less than 1 minute apart
    });

    if (rapidCrashes.length > 2) {
      warnings.push(
        "Multiple rapid successive crashes detected - may indicate critical system issue"
      );
    }

    // Check for repeated error types
    const errorTypes = {};
    recentCrashes.forEach((crash) => {
      const key = `${crash.error.name}:${crash.error.code}`;
      errorTypes[key] = (errorTypes[key] || 0) + 1;
    });

    const dominantError = Object.entries(errorTypes).find(
      ([, count]) => count >= 3
    );
    if (dominantError) {
      warnings.push(
        `Repeated error pattern detected: ${dominantError[0]} (${dominantError[1]} occurrences)`
      );
    }

    // Check for memory-related crashes
    const memoryIssues = recentCrashes.filter(
      (crash) =>
        crash.error.message.includes("memory") ||
        crash.error.message.includes("heap") ||
        crash.context.memory?.heapUsed > 500 * 1024 * 1024 // > 500MB
    );

    if (memoryIssues.length > 1) {
      warnings.push(
        "Multiple memory-related crashes detected - potential memory leak"
      );
    }

    return {
      warnings,
      patterns: {
        totalCrashes: this.crashHistory.length,
        recentCrashes: recentCrashes.length,
        rapidCrashes: rapidCrashes.length,
        errorTypes,
        memoryIssues: memoryIssues.length,
      },
    };
  }

  /**
   * Get crash statistics
   */
  getStatistics() {
    const now = Date.now();
    const last24h = this.crashHistory.filter(
      (crash) => now - new Date(crash.timestamp).getTime() < 24 * 60 * 60 * 1000
    );
    const lastHour = this.crashHistory.filter(
      (crash) => now - new Date(crash.timestamp).getTime() < 60 * 60 * 1000
    );

    return {
      uptime: now - this.startTime,
      totalCrashes: this.crashHistory.length,
      crashesLast24h: last24h.length,
      crashesLastHour: lastHour.length,
      lastCrashTime: this.lastCrashTime,
      avgTimeBetweenCrashes: this.calculateAverageTimeBetweenCrashes(),
      stability: this.calculateStabilityScore(),
    };
  }

  /**
   * Calculate average time between crashes
   */
  calculateAverageTimeBetweenCrashes() {
    if (this.crashHistory.length < 2) return null;

    let totalTime = 0;
    for (let i = 1; i < this.crashHistory.length; i++) {
      const current = new Date(this.crashHistory[i].timestamp).getTime();
      const previous = new Date(this.crashHistory[i - 1].timestamp).getTime();
      totalTime += current - previous;
    }

    return totalTime / (this.crashHistory.length - 1);
  }

  /**
   * Calculate stability score (0-100, higher is better)
   */
  calculateStabilityScore() {
    const uptime = Date.now() - this.startTime;
    const recentCrashes = this.crashHistory.filter(
      (crash) =>
        Date.now() - new Date(crash.timestamp).getTime() < 24 * 60 * 60 * 1000
    );

    if (recentCrashes.length === 0) return 100;

    // Base score from uptime stability
    const hoursUp = uptime / (1000 * 60 * 60);
    const crashesPerHour = recentCrashes.length / Math.max(hoursUp, 1);

    // Lower score for more crashes
    const stabilityScore = Math.max(0, 100 - crashesPerHour * 25);

    return Math.round(stabilityScore);
  }

  /**
   * Clean up old log files
   */
  async cleanupOldLogs() {
    try {
      const files = await fs.readdir(this.options.logDirectory);
      const cutoff =
        Date.now() - this.options.retentionDays * 24 * 60 * 60 * 1000;

      for (const file of files) {
        if (!file.startsWith("crash_") || !file.endsWith(".log")) continue;

        const filepath = path.join(this.options.logDirectory, file);
        const stats = await fs.stat(filepath);

        if (stats.mtime.getTime() < cutoff) {
          await fs.unlink(filepath);
          console.log(`[CrashMonitor] Cleaned up old log file: ${file}`);
        }
      }
    } catch (error) {
      console.error("[CrashMonitor] Error cleaning up old logs:", error);
    }
  }
}

// Create singleton instance
const crashMonitor = new CrashMonitor();

module.exports = {
  CrashMonitor,
  crashMonitor,
};
