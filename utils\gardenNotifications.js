const { EmbedBuilder } = require("discord.js");
const { EMBED_COLORS, skillEmojis } = require("../gameConfig");
const configManager = require("./configManager");

/**
 * Convert hex color string to number
 * @param {string} hexColor - Hex color string like "#3498DB"
 * @returns {number} Color as number for Discord embeds
 */
function hexToNumber(hexColor) {
  return parseInt(hexColor.replace("#", ""), 16);
}

/**
 * Creates garden milestone achievement embeds
 * @param {Array} milestones - Array of milestone objects
 * @returns {Array<EmbedBuilder>} Array of milestone embeds
 */
function createGardenMilestoneEmbeds(milestones) {
  if (!milestones || !Array.isArray(milestones) || milestones.length === 0) {
    return [];
  }

  const embeds = [];
  const allItems = configManager.getAllItems();

  for (const milestone of milestones) {
    const cropData = allItems[milestone.cropKey];
    const cropEmoji = cropData?.emoji || "❓";
    const cropName = cropData?.name || milestone.cropKey;

    // Build main content (before separator)
    const mainContent =
      `### <:garden:1394656922623410237> Garden Milestone\n\n` +
      `${cropEmoji} **${cropName} Milestone ${milestone.milestone}**\n` +
      `🔸 Harvested ${milestone.required.toLocaleString()}\n` +
      `🔸 Next milestone at ${milestone.nextRequired.toLocaleString()}\n\n`;

    // Build rewards content (after separator)
    const rewardsContent =
      `**Rewards**\n` +
      `<:garden:1394656922623410237> \`+${milestone.garden_xp} Garden XP\`\n` +
      `${skillEmojis.farming || "👨‍🌾"} \`+${milestone.farmingXp} Farming XP\``;

    const embed = new EmbedBuilder()
      .setColor(hexToNumber(EMBED_COLORS.PASTEL_GREEN))
      .setTitle("Garden Milestone")
      .setDescription(
        `${cropEmoji} **${cropName} Milestone ${milestone.milestone}**\n` +
          `🔸 Harvested ${milestone.required.toLocaleString()}\n` +
          `🔸 Next milestone at ${milestone.nextRequired.toLocaleString()}\n\n` +
          `**Rewards**\n<:garden:1394656922623410237> \`+${milestone.garden_xp} Garden XP\`\n${skillEmojis.farming || "👨‍🌾"} \`+${milestone.farmingXp} Farming XP\``
      );
    embeds.push(embed);
  }

  return embeds;
}

/**
 * Creates garden level-up embed
 * @param {Object} levelUpInfo - Level-up information
 * @returns {EmbedBuilder|null} Level-up embed or null
 */
function createGardenLevelUpEmbed(levelUpInfo) {
  if (!levelUpInfo || !levelUpInfo.leveledUp) {
    return null;
  }

  // Main level up content (before separator)
  const mainContent =
    `### <:garden:1394656922623410237> Garden Level Up!\n\n` +
    `**<:garden:1394656922623410237> GARDEN ${levelUpInfo.oldLevel} → ${levelUpInfo.newLevel}**`;

  const embed = new EmbedBuilder()
    .setColor(hexToNumber(EMBED_COLORS.GOLD))
    .setTitle("Garden Level Up!")
    .setDescription(
      `**<:garden:1394656922623410237> GARDEN ${levelUpInfo.oldLevel} → ${levelUpInfo.newLevel}**`
    );

  // Add unlocked crops section after separator if there are any
  if (levelUpInfo.unlockedCrops && levelUpInfo.unlockedCrops.length > 0) {
    const allItems = configManager.getAllItems();

    let unlockedContent = "**Unlocked Crops:**\n";
    for (const cropKey of levelUpInfo.unlockedCrops) {
      const cropData = allItems[cropKey];
      if (cropData) {
        const cropEmoji = cropData.emoji || "❓";
        unlockedContent += `${cropEmoji} **${cropData.name}**\n`;
      }
    }

    embed.addFields({ name: "Unlocked Crops:", value: unlockedContent });
  }

  return embed;
}

/**
 * Sends garden milestone and level-up notifications to channel
 * @param {Object} interaction - Discord interaction
 * @param {Array} milestones - Array of milestone achievements
 * @param {Object} levelUpInfo - Level-up information
 */
async function sendGardenNotifications(interaction, milestones, levelUpInfo) {
  if (!interaction || !interaction.channel) {
    console.warn("[Garden Notifications] No interaction or channel available");
    return;
  }

  const embeds = [];

  // Add milestone containers
  if (milestones && milestones.length > 0) {
    const milestoneEmbeds = createGardenMilestoneEmbeds(milestones);
    embeds.push(...milestoneEmbeds);
  }

  // Add level-up container
  if (levelUpInfo && levelUpInfo.leveledUp) {
    const levelUpEmbed = createGardenLevelUpEmbed(levelUpInfo);
    if (levelUpEmbed) {
      embeds.push(levelUpEmbed);
    }
  }

  // Send notifications separately with delays if we have any
  if (embeds.length > 0) {
    try {
      console.log(
        `[Garden Notifications] Sending ${embeds.length} garden notification(s) with delays for user ${interaction.user.id}`
      );

      // Send each container separately with 500ms delay
      for (let i = 0; i < embeds.length; i++) {
        await interaction.channel.send({
          embeds: [embeds[i]],
        });

        // Add delay between messages (except after the last one)
        if (i < embeds.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }
    } catch (error) {
      console.error(
        "[Garden Notifications] Error sending garden notifications:",
        error
      );
    }
  }
}

module.exports = {
  createGardenMilestoneEmbeds,
  createGardenLevelUpEmbed,
  sendGardenNotifications,
};
