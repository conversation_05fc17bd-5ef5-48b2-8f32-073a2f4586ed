/**
 * Adds last_notified column to player_last_active_channel table
 * Used to track when a player was last notified about minions being full
 */
module.exports = {
  /**
   * @param {import('sqlite3').Database} db
   * @returns {Promise<void>}
   */
  up: async (db) => {
    return new Promise((resolve, reject) => {
      db.run(
        `
                ALTER TABLE player_last_active_channel 
                ADD COLUMN last_notified INTEGER DEFAULT NULL
            `,
        (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });
  },
};
