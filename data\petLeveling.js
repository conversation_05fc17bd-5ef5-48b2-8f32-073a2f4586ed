// data/petLeveling.js

// XP tables for each rarity, index = level (1-based, 0 = unused)
const PET_XP_TABLES = {
  COMMON: [
    0, // 0 (unused)
    0,
    100,
    110,
    120,
    130,
    145,
    160,
    175,
    190,
    210,
    230,
    250,
    275,
    300,
    330,
    360,
    400,
    440,
    490,
    540,
    600,
    660,
    730,
    800,
    880,
    960,
    1050,
    1150,
    1260,
    1380,
    1510,
    1650,
    1800,
    1960,
    2130,
    2310,
    2500,
    2700,
    2920,
    3160,
    3420,
    3700,
    4000,
    4350,
    4750,
    5200,
    5700,
    6300,
    7000,
    7800,
    8700,
    9700,
    10800,
    12000,
    13300,
    14700,
    16200,
    17800,
    19500,
    21300,
    23200,
    25200,
    27300,
    29500,
    31900,
    34500,
    37300,
    40300,
    43500,
    46900,
    50500,
    54300,
    58300,
    62500,
    66900,
    71500,
    76300,
    81300,
    86500,
    91900,
    97500,
    103300,
    109300,
    115500,
    121900,
    128500,
    135300,
    142300,
    149500,
    156900,
    164500,
    172300,
    180300,
    188500,
    196900,
    205500,
    214300,
    223300,
    232500,
    241900,
    251500,
    261300,
    271300,
    281500,
    291900,
    302500,
  ],
  UNCOMMON: [
    0, 0, 175, 190, 210, 230, 250, 275, 300, 330, 360, 400, 440, 490, 540, 600,
    660, 730, 800, 880, 960, 1050, 1150, 1260, 1380, 1510, 1650, 1800, 1960,
    2130, 2310, 2500, 2700, 2920, 3160, 3420, 3700, 4000, 4350, 4750, 5200,
    5700, 6300, 7000, 7800, 8700, 9700, 10800, 12000, 13300, 14700, 16200,
    17800, 19500, 21300, 23200, 25200, 27300, 29500, 31900, 34500, 37300, 40300,
    43500, 46900, 50500, 54300, 58300, 62500, 66900, 71500, 76300, 81300, 86500,
    91900, 97500, 103300, 109300, 115500, 121900, 128500, 135300, 142300,
    149500, 156900, 164500, 172300, 180300, 188500, 196900, 205500, 214300,
    223300, 232500, 241900, 251500, 261300, 271300, 281500, 291900, 302500,
  ],
  RARE: [
    0, 0, 275, 300, 330, 360, 400, 440, 490, 540, 600, 660, 730, 800, 880, 960,
    1050, 1150, 1260, 1380, 1510, 1650, 1800, 1960, 2130, 2310, 2500, 2700,
    2920, 3160, 3420, 3700, 4000, 4350, 4750, 5200, 5700, 6300, 7000, 7800,
    8700, 9700, 10800, 12000, 13300, 14700, 16200, 17800, 19500, 21300, 23200,
    25200, 27300, 29500, 31900, 34500, 37300, 40300, 43500, 46900, 50500, 54300,
    58300, 62500, 66900, 71500, 76300, 81300, 86500, 91900, 97500, 103300,
    109300, 115500, 121900, 128500, 135300, 142300, 149500, 156900, 164500,
    172300, 180300, 188500, 196900, 205500, 214300, 223300, 232500, 241900,
    251500, 261300, 271300, 281500, 291900, 302500, 313300, 324300, 335500,
    346900, 358500, 370300, 382300,
  ],
  EPIC: [
    0, 0, 440, 490, 540, 600, 660, 730, 800, 880, 960, 1050, 1150, 1260, 1380,
    1510, 1650, 1800, 1960, 2130, 2310, 2500, 2700, 2920, 3160, 3420, 3700,
    4000, 4350, 4750, 5200, 5700, 6300, 7000, 7800, 8700, 9700, 10800, 12000,
    13300, 14700, 16200, 17800, 19500, 21300, 23200, 25200, 27300, 29500, 31900,
    34500, 37300, 40300, 43500, 46900, 50500, 54300, 58300, 62500, 66900, 71500,
    76300, 81300, 86500, 91900, 97500, 103300, 109300, 115500, 121900, 128500,
    135300, 142300, 149500, 156900, 164500, 172300, 180300, 188500, 196900,
    205500, 214300, 223300, 232500, 241900, 251500, 261300, 271300, 281500,
    291900, 302500, 313300, 324300, 335500, 346900, 358500, 370300, 382300,
    394500, 406900, 419500, 432300, 445300,
  ],
  LEGENDARY: [
    0, 0, 660, 730, 800, 880, 960, 1050, 1150, 1260, 1380, 1510, 1650, 1800,
    1960, 2130, 2310, 2500, 2700, 2920, 3160, 3420, 3700, 4000, 4350, 4750,
    5200, 5700, 6300, 7000, 7800, 8700, 9700, 10800, 12000, 13300, 14700, 16200,
    17800, 19500, 21300, 23200, 25200, 27300, 29500, 31900, 34500, 37300, 40300,
    43500, 46900, 50500, 54300, 58300, 62500, 66900, 71500, 76300, 81300, 86500,
    91900, 97500, 103300, 109300, 115500, 121900, 128500, 135300, 142300,
    149500, 156900, 164500, 172300, 180300, 188500, 196900, 205500, 214300,
    223300, 232500, 241900, 251500, 261300, 271300, 281500, 291900, 302500,
    313300, 324300, 335500, 346900, 358500, 370300, 382300, 394500, 406900,
    419500, 432300, 445300, 458500, 471900, 485500,
  ],
};

const MAX_PET_LEVEL = 100;

function getExpRequiredForLevel(rarity, currentLevel) {
  const table = PET_XP_TABLES[rarity];
  if (!table || currentLevel < 1 || currentLevel >= table.length)
    return Infinity;
  return table[currentLevel + 1] || Infinity;
}

function calculatePetLevelUp(
  rarity,
  currentLevel,
  currentExpInLevel,
  addedExp
) {
  const table = PET_XP_TABLES[rarity];
  if (!table)
    return {
      newLevel: currentLevel,
      newExpInLevel: currentExpInLevel,
      levelsGained: 0,
    };
  let level = currentLevel;
  let expInLevel = currentExpInLevel + addedExp;
  let levelsGained = 0;
  while (level < MAX_PET_LEVEL) {
    const required = getExpRequiredForLevel(rarity, level);
    if (expInLevel < required) break;
    expInLevel -= required;
    level++;
    levelsGained++;
  }
  if (level === MAX_PET_LEVEL) expInLevel = 0;
  return { newLevel: level, newExpInLevel: expInLevel, levelsGained };
}

/**
 * Calculate pet level and remaining XP from total XP
 * @param {string} rarity - Pet rarity (COMMON, UNCOMMON, etc.)
 * @param {number} totalXp - Total XP accumulated
 * @returns {object} - { level, expInLevel, totalExpForLevel }
 */
function calculateLevelFromTotalXp(rarity, totalXp) {
  const table = PET_XP_TABLES[rarity];
  if (!table || totalXp < 0) {
    return { level: 1, expInLevel: 0, totalExpForLevel: 0 };
  }

  let level = 1;
  let remainingXp = totalXp;
  let totalExpForLevel = 0;

  // Find the highest level achievable with the given XP
  while (level < MAX_PET_LEVEL && remainingXp > 0) {
    const xpForNextLevel = getExpRequiredForLevel(rarity, level);
    if (xpForNextLevel === Infinity || remainingXp < xpForNextLevel) {
      break;
    }
    remainingXp -= xpForNextLevel;
    totalExpForLevel += xpForNextLevel;
    level++;
  }

  return {
    level: level,
    expInLevel: remainingXp,
    totalExpForLevel: totalExpForLevel,
  };
}

module.exports = {
  getExpRequiredForLevel,
  calculatePetLevelUp,
  calculateLevelFromTotalXp,
  MAX_PET_LEVEL,
  PET_XP_TABLES,
};
