// Migration: 025_add_settings_json_column.js
// Purpose: Add settings_json column to players table for per-player settings (e.g., pingOnMultiAction)

module.exports.up = async function up(db) {
  return new Promise((resolve, reject) => {
    db.run("ALTER TABLE players ADD COLUMN settings_json TEXT", (err) => {
      if (err && !err.message.includes("duplicate column")) {
        console.error(
          "[Migration 025] Failed to add settings_json column:",
          err.message,
        );
        reject(err);
      } else {
        console.log(
          "[Migration 025] settings_json column added (or already exists).",
        );
        resolve();
      }
    });
  });
};
