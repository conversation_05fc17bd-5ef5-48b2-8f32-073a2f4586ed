const regionMetadata = {
  the_hub: {
    name: "The Hub",
    emoji: "<:region_hub:1367836199313735720>",
    allowsWaterFishing: true,
  },
  the_farming_islands: {
    name: "The Farming Islands",
    emoji: "<:warp_farming_island:1390100789829046292> ",
    allowsWaterFishing: true,
  },
  the_park: {
    name: "The Park",
    emoji: "<:warp_park:1390100809785806908> ",
    allowsWaterFishing: true,
  },
  spiders_den: {
    name: "Spider's Den",
    emoji: "<:warp_spider_den:1390100829335191655>",
    allowsWaterFishing: true,
  },
  private_island: {
    name: "Private Island",
    emoji: "<:region_hub:1367836199313735720>",
    allowsWaterFishing: false,
  },
  deep_caverns: {
    name: "Deep Caverns",
    emoji: "<:warp_deep_cavern:1390100751837302916> ",
    allowsWaterFishing: false,
  },
  the_end: {
    name: "The End",
    emoji: "<:warp_end:1390100770984165386> ",
    allowsWaterFishing: false,
    requirements: {
      skills: {
        combat: 12,
      },
    },
  },
  galatea: {
    name: "<PERSON><PERSON><PERSON>",
    emoji: "",
    allowsWaterFishing: true,
    requirements: {
      skills: {
        foraging: 12,
      },
    },
  },
  garden: {
    name: "Garden",
    emoji: "<:garden:1394656922623410237>",
    allowsWaterFishing: false,
    isPersonalInstance: true, // Personal instance like Private Island
  },
  crimson_isle: {
    name: "Crimson Isle",
    emoji: "<:Crimson_Isle:1399334560860213348>",
    allowsWaterFishing: false,
    requirements: {
      skills: {
        farming: 12,
      },
    },
  },
  dwarven_mines: {
    name: "Dwarven Mines",
    emoji: "", // No emoji yet
    allowsWaterFishing: false,
    requirements: {
      skills: {
        mining: 12,
      },
    },
  },
};

module.exports = regionMetadata;
