{"name": "Spider Minion", "emoji": "<:minion_spider:1386237847924641792>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "combat", "drops": [{"itemKey": "STRING", "chance": 1}, {"itemKey": "SPIDER_EYE", "chance": 0.5}], "recipes": [{"ingredients": [{"itemKey": "STRING", "amount": 80}]}], "craftingRequirements": {"collections": {"STRING": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 26, "maxStorage": 192}, {"tier": 2, "generationIntervalSeconds": 26, "maxStorage": 320, "upgradeCost": [{"itemKey": "STRING", "amount": 160}]}, {"tier": 3, "generationIntervalSeconds": 24, "maxStorage": 320, "upgradeCost": [{"itemKey": "STRING", "amount": 320}]}, {"tier": 4, "generationIntervalSeconds": 24, "maxStorage": 448, "upgradeCost": [{"itemKey": "STRING", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 22, "maxStorage": 448, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 22, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 16}]}, {"tier": 7, "generationIntervalSeconds": 20, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 32}]}, {"tier": 8, "generationIntervalSeconds": 20, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 64}]}, {"tier": 9, "generationIntervalSeconds": 17, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 128}]}, {"tier": 10, "generationIntervalSeconds": 17, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 256}]}, {"tier": 11, "generationIntervalSeconds": 13, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_STRING", "amount": 512}]}]}