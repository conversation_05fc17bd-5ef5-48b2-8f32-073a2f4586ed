{"name": "Sugar Cane Minion", "emoji": "<:minion_sugar_cane:1375843134508105758>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "resourceItemKey": "SUGAR_CANE", "recipes": [{"ingredients": [{"itemKey": "SUGAR_CANE", "amount": 128}]}], "craftingRequirements": {"collections": {"SUGAR_CANE": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 18, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 18, "maxStorage": 192, "upgradeCost": [{"itemKey": "SUGAR_CANE", "amount": 256}]}, {"tier": 3, "generationIntervalSeconds": 16, "maxStorage": 192, "upgradeCost": [{"itemKey": "SUGAR_CANE", "amount": 512}]}, {"tier": 4, "generationIntervalSeconds": 16, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR", "amount": 8}]}, {"tier": 5, "generationIntervalSeconds": 14, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR", "amount": 24}]}, {"tier": 6, "generationIntervalSeconds": 14, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR", "amount": 64}]}, {"tier": 7, "generationIntervalSeconds": 12, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR", "amount": 128}]}, {"tier": 8, "generationIntervalSeconds": 12, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR", "amount": 256}]}, {"tier": 9, "generationIntervalSeconds": 10, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR", "amount": 512}]}, {"tier": 10, "generationIntervalSeconds": 10, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR_CANE", "amount": 8}]}, {"tier": 11, "generationIntervalSeconds": 8, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_SUGAR_CANE", "amount": 16}]}], "drops": [{"itemKey": "SUGAR_CANE", "chance": 1, "min": 2, "max": 3}]}