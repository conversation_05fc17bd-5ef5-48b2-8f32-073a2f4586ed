{"name": "Skeleton Minion", "emoji": "<:minion_skeleton:1375851907498774584>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "combat", "resourceItemKey": "BONE", "recipes": [{"ingredients": [{"itemKey": "BONE", "amount": 64}]}], "craftingRequirements": {"collections": {"BONE": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 26, "maxStorage": 192}, {"tier": 2, "generationIntervalSeconds": 26, "maxStorage": 320, "upgradeCost": [{"itemKey": "BONE", "amount": 128}]}, {"tier": 3, "generationIntervalSeconds": 24, "maxStorage": 320, "upgradeCost": [{"itemKey": "BONE", "amount": 256}]}, {"tier": 4, "generationIntervalSeconds": 24, "maxStorage": 448, "upgradeCost": [{"itemKey": "BONE", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 22, "maxStorage": 448, "upgradeCost": [{"itemKey": "ENCHANTED_BONE", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 22, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_BONE", "amount": 24}]}, {"tier": 7, "generationIntervalSeconds": 20, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_BONE", "amount": 64}]}, {"tier": 8, "generationIntervalSeconds": 20, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_BONE", "amount": 128}]}, {"tier": 9, "generationIntervalSeconds": 18, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_BONE", "amount": 256}]}, {"tier": 10, "generationIntervalSeconds": 18, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_BONE", "amount": 512}]}, {"tier": 11, "generationIntervalSeconds": 16, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_BONE_BLOCK", "amount": 8}]}], "drops": [{"itemKey": "BONE", "chance": 1}]}