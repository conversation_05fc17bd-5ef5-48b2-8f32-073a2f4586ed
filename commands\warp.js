const {
  SlashCommandBuilder,
  EmbedBuilder,
  MessageFlags,
} = require("discord.js");
const regionMetadata = require("../data/regionMetadata.js");
const {
  getPlayerData,
  savePlayerData,
  calculateMaxMinionSlots,
  getPlayerSkillLevel,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager");
const configManager = require("../utils/configManager");
const { skillEmojis, EMBED_COLORS, WARP_FOOTER } = require("../gameConfig");
const NPCS = require("../data/npcs.js");
const allMobsData = configManager.getAllMobs();
const { isBakerSpawnWindow } = require("../utils/timeUtils");
const bot = require("../bot.js"); // Added for global access to timeState

const { getActiveVisitors } = require("../utils/gardenVisitors");
const regionChoices = Object.entries(regionMetadata).map(([key, region]) => ({
  name: region.name,
  value: key,
}));

async function buildRegionEmbed(regionKey, regionMeta, character) {
  const allItems = configManager.getAllItems();
  const allMobs = allMobsData; // Already loaded
  const allNpcs = NPCS; // Import location changed

  const embed = new EmbedBuilder();
  let title = "Unknown Location";
  let description = "You seem to be in an unknown place...";
  const defaultEmoji = "📍"; // Define a default emoji

  if (regionKey === "Private Island") {
    const isOwnIsland = character.visiting_island_owner_id === null;
    title = `${regionMeta?.emoji || defaultEmoji} Arrived at Private Island`;
    if (isOwnIsland) {
      const minionsPlaced = character.island?.placedMinions?.length || 0;
      const maxMinions = calculateMaxMinionSlots(character);
      description = `**Minions Placed:** ${minionsPlaced}/${maxMinions}`;

      // Add a line showing each placed minion as <emoji> [Tn]
      const placedMinions = character.island?.placedMinions || [];
      if (placedMinions.length > 0) {
        const minionLine = placedMinions
          .map((m) => {
            const minionDef = allItems[m.itemKey];
            const emoji = minionDef?.emoji || "❓";
            return `${emoji} **[T${m.tier}]**`;
          })
          .join(" ");
        description += `\n${minionLine}\n`;
      }
      description +=
        "\n**Tip:** Use **/minions** to view, collect, or upgrade your minions.";
      embed.setColor(EMBED_COLORS.GOLD);
      embed.setDescription(description);
    } else {
      title = `${defaultEmoji} Visiting Another Island`;
      description = "You are currently visiting another player's island.";
      embed.setColor(EMBED_COLORS.SKY_BLUE);
      embed.setDescription(description);
    }
    embed.setTitle(title);
    embed.setFooter({ text: WARP_FOOTER });
    return embed;
  } else if (regionKey === "garden") {
    const {
      getGardenLevel,
      getGardenMilestones,
      getUnlockedCrops,
      getXpForNextGardenLevel,
    } = require("../utils/gardenSystem");

    title = `${regionMeta?.emoji || "<:garden:1394656922623410237>"} Your Garden`;
    embed.setColor(EMBED_COLORS.PASTEL_GREEN);

    const gardenXp = character.garden_xp || 0;
    const gardenLevel = getGardenLevel(gardenXp);
    const milestones = getGardenMilestones(character);
    const unlockedCrops = getUnlockedCrops(gardenLevel);
    const nextLevelXp = getXpForNextGardenLevel(gardenLevel);

    // Count total milestones achieved
    let totalMilestones = 0;
    for (const cropKey of unlockedCrops) {
      const cropData = milestones[cropKey];
      if (cropData && cropData.milestone) {
        totalMilestones += cropData.milestone;
      }
    }

    // Build Garden level display
    let levelDisplay = `**Garden Level:** ${gardenLevel}`;
    if (nextLevelXp > 0) {
      const xpNeeded = nextLevelXp - gardenXp;
      levelDisplay += ` (${xpNeeded.toLocaleString()} XP to next level)`;
    } else {
      levelDisplay += ` (MAX LEVEL)`;
    }

    description = `${levelDisplay}\n`;
    // Calculate total crops dynamically
    const { CROP_UNLOCKS } = require("../utils/gardenSystem");
    const totalCrops = Object.values(CROP_UNLOCKS).flat().length;
    description += `**Unlocked Crops:** ${unlockedCrops.length}/${totalCrops}\n`;
    // Show visitors waiting with friendly message
    const visitorList = await getActiveVisitors(
      character.discordId || character.discord_id
    );
    description += `\nYou have **${visitorList.length}** visitor${visitorList.length === 1 ? "" : "s"} waiting in your garden!\n\n`;
    // Show available crops
    if (unlockedCrops.length > 0) {
      description += "**Available Crops:**\n";
      for (const cropKey of unlockedCrops) {
        const cropData = allItems[cropKey];
        if (cropData) {
          const emoji = cropData.emoji || "❓";
          description += `${emoji} ${cropData.name}\n`;
        }
      }
      description += "\n";
    }

    embed.setDescription(description);
    embed.setTitle(title);
    embed.setFooter({ text: WARP_FOOTER });
    return embed;
  } else if (regionMeta) {
    title = `${regionMeta.emoji || defaultEmoji} ${regionMeta.name}`;
    embed.setColor(EMBED_COLORS.DARK_GREEN);

    const groupedContent = {
      Combat: [],
      Mining: [],
      Farming: [],
      Foraging: [],
      Fishing: [],
      NPCs: [],
    };

    for (const itemKey in allItems) {
      const item = allItems[itemKey];
      if (item.foundInRegions && item.foundInRegions.includes(regionKey)) {
        const skill = item.sourceSkill;
        if (skill && groupedContent[skill]) {
          groupedContent[skill].push(item);
        }
      }
    }

    for (const mobKey in allMobs) {
      const mob = allMobs[mobKey];
      if (mob.spawnRegions && mob.spawnRegions.includes(regionKey)) {
        groupedContent.Combat.push(mob);
      }
    }

    for (const npcKey in allNpcs) {
      const npc = allNpcs[npcKey];
      if (npc.foundInRegions && npc.foundInRegions.includes(regionKey)) {
        let isAvailable = true;

        // Check NPC appearance conditions
        if (npc.appearance_conditions) {
          // Special handling for Baker's spawn window
          if (
            npc.key === "BAKER" &&
            npc.appearance_conditions.isBakerSpawnWindow
          ) {
            const timeState = bot.getTimeState ? bot.getTimeState() : null;
            const bakerAvailable = timeState && isBakerSpawnWindow(timeState);
            if (!bakerAvailable) {
              isAvailable = false;
            }
          }
          // Add more appearance condition checks here for future NPCs if needed
        }

        if (isAvailable) {
          groupedContent.NPCs.push(npc);
        }
      }
    }

    const embedFields = [];
    const addField = (skillKey, name, dataList) => {
      if (dataList.length > 0) {
        const list = dataList
          .map((d) => {
            let emoji = "❓";
            let entryName = d.name || d.key; // Use d.key as fallback
            if (d.emoji) {
              emoji = d.emoji;
            } // Use d.emoji if it exists directly

            if (skillKey === "Combat") {
              let levelDisplay = "?";
              if (d.levelRange) {
                const minLvl = d.levelRange.min;
                const maxLvl = d.levelRange.max;
                levelDisplay =
                  minLvl === maxLvl ? `${minLvl}` : `${minLvl}-${maxLvl}`;
              } else if (typeof d.level === "number") {
                levelDisplay = `${d.level}`;
              }
              entryName += ` Lv${levelDisplay}`;
            }
            return `${emoji} ${entryName}`;
          })
          .join("\n");
        embedFields.push({
          name: `${skillEmojis[skillKey.toLowerCase()] || "❓"} ${name}`,
          value: list,
          inline: true,
        });
      }
    };

    addField("Combat", "Combat", groupedContent.Combat);
    addField("Mining", "Mining", groupedContent.Mining);
    addField("Farming", "Farming", groupedContent.Farming);
    addField("Foraging", "Foraging", groupedContent.Foraging);
    addField("NPCs", "NPCs", groupedContent.NPCs);

    if (embedFields.length > 0) {
      embed.addFields(embedFields);
      description = null;
    } else {
      description = "This area seems quiet...";
    }
  } else {
    embed.setColor(EMBED_COLORS.LIGHT_GREY);
  }

  embed.setTitle(title);
  if (description) {
    embed.setDescription(description);
  }
  embed.setFooter({ text: WARP_FOOTER });
  return embed;
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("warp")
    .setDescription("Travel to a region or your private island")
    .addStringOption((option) =>
      option
        .setName("destination")
        .setDescription("Choose where to travel")
        .setRequired(false)
        .addChoices(...regionChoices)
    ),
  async execute(interaction) {
    const userId = interaction.user.id;
    let character;
    try {
      character = await getPlayerData(userId);
    } catch (error) {
      console.error(
        `[Warp Command] Error fetching player data for ${userId}: [WRP-DAT-ERR]`,
        error
      );
      return interaction.reply({
        content:
          "[WRP-DAT-ERR] Error fetching your character data. Please try again or report this code.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!character) {
      return interaction.reply(
        "You don't have a character yet! Visit the setup channel to create one."
      );
    }

    if (!checkRankPermission(character, "MEMBER")) {
      return interaction.reply({
        content: "You don't have permission to use this command (Rank Error).",
        flags: [MessageFlags.Ephemeral],
      });
    }

    try {
      const currentActivity = await getCurrentActivity(userId);
      if (currentActivity) {
        return warnUserBusy(interaction, currentActivity, "warp");
      }

      const destination = interaction.options.getString("destination");
      const wasVisiting = character.visiting_island_owner_id !== null;
      const currentRegionKey = character.current_region;

      if (!destination) {
        const displayEmbed = await buildRegionEmbed(
          currentRegionKey,
          regionMetadata[currentRegionKey],
          character
        );
        return interaction.reply({ embeds: [displayEmbed] });
      }

      if (wasVisiting) {
        character.visiting_island_owner_id = null;
      }

      if (destination === "private_island") {
        if (character.current_region === "Private Island" && !wasVisiting) {
          return interaction.reply("You are already on your Private Island!");
        }

        character.current_region = "Private Island";
        character.visiting_island_owner_id = null;
        try {
          // Persist only region fields to avoid overwriting unrelated data
          await savePlayerData(
            userId,
            {
              current_region: character.current_region,
              visiting_island_owner_id: null,
            },
            ["current_region", "visiting_island_owner_id"]
          );

          await recalculateAndSaveStats(userId, character);
        } catch (saveError) {
          console.error(
            `[Warp Command] Error saving player data for ${userId} warping home: [WRP-SAVE-HOME-ERR]`,
            saveError
          );
          return interaction.reply({
            content:
              "[WRP-SAVE-HOME-ERR] Failed to save your location data. Could not warp home. Please report this code.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const islandMeta = regionMetadata["private_island"]; // Use the correct key
        const islandEmbed = await buildRegionEmbed(
          "Private Island",
          islandMeta,
          character
        );

        islandEmbed.setTitle(
          `${islandMeta?.emoji || "🏝️"} Arrived at Private Island`
        );

        return interaction.reply({ embeds: [islandEmbed] });
      }

      if (destination === "garden") {
        if (character.current_region === "garden") {
          return interaction.reply("You are already in your Garden!");
        }

        character.current_region = "garden";
        character.visiting_island_owner_id = null;
        try {
          await savePlayerData(
            userId,
            {
              current_region: character.current_region,
              visiting_island_owner_id: null,
            },
            ["current_region", "visiting_island_owner_id"]
          );
          await recalculateAndSaveStats(userId, character);
        } catch (saveError) {
          console.error(
            `[Warp Command] Error saving player data for ${userId} warping to garden: [WRP-SAVE-GARDEN-ERR]`,
            saveError
          );
          return interaction.reply({
            content:
              "[WRP-SAVE-GARDEN-ERR] Failed to save your location data. Could not warp to Garden. Please report this code.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const gardenMeta = regionMetadata["garden"];
        const gardenEmbed = await buildRegionEmbed(
          "garden",
          gardenMeta,
          character
        );
        gardenEmbed.setTitle(
          `${gardenMeta?.emoji || "<:garden:1394656922623410237>"} Arrived at Garden`
        );

        return interaction.reply({ embeds: [gardenEmbed] });
      }

      const destinationMeta = regionMetadata[destination];
      if (!destinationMeta) {
        return interaction.reply("Invalid destination selected.");
      }

      if (currentRegionKey === destination) {
        return interaction.reply(`You are already in ${destinationMeta.name}!`);
      }

      if (destinationMeta.requirements?.skills) {
        for (const [skillName, requiredLevel] of Object.entries(
          destinationMeta.requirements.skills
        )) {
          const playerSkillLevel = getPlayerSkillLevel(character, skillName);
          if (playerSkillLevel < requiredLevel) {
            return interaction.reply({
              content: `You need ${skillName.charAt(0).toUpperCase() + skillName.slice(1)} Level ${requiredLevel} to travel to ${destinationMeta.name}!`,
              flags: [MessageFlags.Ephemeral],
            });
          }
        }
      }

      character.current_region = destination;
      character.visiting_island_owner_id = null;
      try {
        await savePlayerData(
          userId,
          {
            current_region: character.current_region,
            visiting_island_owner_id: null,
          },
          ["current_region", "visiting_island_owner_id"]
        );

        await recalculateAndSaveStats(userId, character);

        const {
          checkAndNotifyDisblockXP,
        } = require("../utils/disblockXpSystem");
        await checkAndNotifyDisblockXP(userId, interaction);
      } catch (saveError) {
        console.error(
          `[Warp Command] Error saving player data for ${userId} warping to ${destination}: [WRP-SAVE-DEST-ERR]`,
          saveError
        );
        return interaction.reply({
          content: `[WRP-SAVE-DEST-ERR] Failed to save your location data. Could not warp to ${destinationMeta.name}. Please report this code.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      const destinationEmbed = await buildRegionEmbed(
        destination,
        destinationMeta,
        character
      );

      destinationEmbed.setTitle(
        `${destinationMeta.emoji || "📍"} Arrived at ${destinationMeta.name}`
      );

      return interaction.reply({ embeds: [destinationEmbed] });
    } catch (error) {
      console.error("[Warp Command Error] [WRP-EXE-ERR]", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "[WRP-EXE-ERR] An unexpected error occurred during warping. Please report this error code.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      } else {
        await interaction
          .followUp({
            content:
              "[WRP-EXE-ERR] An unexpected error occurred during warping. Please report this error code.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    }
  },
};
