const { ChannelType } = require("discord.js");
const config = require("../config.json");
const { getPersonalChannelConfig } = require("./personalChannelConfig");

/**
 * Sanitize a character name to be Discord channel-friendly
 * @param {string} characterName - The character name to sanitize
 * @returns {string} - Sanitized channel name
 */
function sanitizeChannelName(characterName) {
  return characterName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "-") // replace non-alphanumeric with hyphens
    .replace(/-+/g, "-") // replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ""); // remove leading/trailing hyphens
}

/**
 * Create a personal channel for a player
 * @param {Client} client - Discord client
 * @param {string} characterName - The character's name
 * @param {string} userId - Discord user ID
 * @returns {Promise<Channel>} - The created channel
 */
async function createPersonalChannel(client, characterName, userId) {
  try {
    console.log(
      `[ChannelManager] Creating personal channel for ${characterName} (${userId})`
    );

    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      throw new Error(`Guild with ID ${config.guildId} not found`);
    }

    // get the category for player channels
    const categoryId = config.channelCreation.playerChannelsCategoryId;
    if (!categoryId) {
      throw new Error("playerChannelsCategoryId not configured in config.json");
    }

    // get channel configuration from centralized config
    const channelConfig = getPersonalChannelConfig(characterName, userId);

    const category = guild.channels.cache.get(categoryId);
    if (!category) {
      // debug info to help troubleshoot
      console.error(
        `[ChannelManager] Category ${categoryId} not found in cache`
      );
      console.error(
        `[ChannelManager] Available categories:`,
        guild.channels.cache
          .filter((ch) => ch.type === 4)
          .map((ch) => `${ch.name} (${ch.id})`)
      );

      // try fetching it directly
      try {
        const fetchedCategory = await guild.channels.fetch(categoryId);
        if (fetchedCategory) {
          console.log(
            `[ChannelManager] Found category via fetch: ${fetchedCategory.name}`
          );
        }
      } catch (fetchError) {
        console.error(
          `[ChannelManager] Failed to fetch category ${categoryId}:`,
          fetchError.message
        );
      }

      throw new Error(`Category with ID ${categoryId} not found`);
    }

    // sanitize the character name for channel naming
    const channelName = sanitizeChannelName(characterName);

    // combine category permissions with channel-specific permissions (same logic as validator)
    const combinedPermissions = [];

    // start with category permissions as base
    for (const [id, overwrite] of category.permissionOverwrites.cache) {
      combinedPermissions.push({
        id: id,
        allow: Array.from(overwrite.allow),
        deny: Array.from(overwrite.deny),
      });
    }

    // then add/override with our channel-specific permissions
    for (const perm of channelConfig.permissions) {
      const existingIndex = combinedPermissions.findIndex(
        (p) => p.id === perm.id
      );
      if (existingIndex >= 0) {
        // merge with existing permission
        const existing = combinedPermissions[existingIndex];
        combinedPermissions[existingIndex] = {
          id: perm.id,
          allow: [...new Set([...existing.allow, ...(perm.allow || [])])],
          deny: [...new Set([...existing.deny, ...(perm.deny || [])])],
        };
      } else {
        // add new permission
        combinedPermissions.push(perm);
      }
    }

    // create the channel
    const channel = await guild.channels.create({
      name: channelName,
      topic: channelConfig.topic,
      type: ChannelType.GuildText,
      parent: category,
      permissionOverwrites: combinedPermissions,
    });

    console.log(
      `[ChannelManager] Successfully created channel #${channel.name} (${channel.id}) for ${characterName}`
    );
    return channel;
  } catch (error) {
    console.error(
      `[ChannelManager] Error creating personal channel for ${characterName}:`,
      error
    );
    throw error;
  }
}

/**
 * Check if a user has permission to use commands in the current channel
 * @param {string} userId - Discord user ID
 * @param {string} channelId - Current channel ID
 * @param {string} personalChannelId - User's personal channel ID (from database)
 * @returns {boolean} - Whether user can use commands in this channel
 */
function canUseCommandsInChannel(userId, channelId, personalChannelId) {
  // if they don't have a personal channel set, allow commands anywhere (backwards compatibility)
  if (!personalChannelId) {
    return true;
  }

  // they can use commands in their own channel
  if (channelId === personalChannelId) {
    return true;
  }

  // for now, deny commands in other channels
  // later we could add logic for shared/public channels
  return false;
}

/**
 * Get the setup embed for character creation
 * @returns {Object} - Embed and button row for setup message
 */
function getSetupEmbed() {
  const {
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
  } = require("discord.js");
  const { EMBED_COLORS } = require("../gameConfig");

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle("<:mob_chicken:1367682990041464955> Welcome to Akin's DisBlock!")
    .setDescription(
      "Ready for Adventure? Click the button below to create your character\nand get your own channel to play in.\n\n" +
        "**What happens when you create a character**\n" +
        "• You'll choose your character's name\n" +
        "• A personal channel will be created just for you\n"
    )
    .setFooter({ text: "One character per Discord account (for now)" });

  const button = new ButtonBuilder()
    .setCustomId("create_character_setup")
    .setLabel("Create Character")
    .setStyle(ButtonStyle.Primary)
    .setEmoji("<:mob_chicken:1367682990041464955>");

  const row = new ActionRowBuilder().addComponents(button);

  return { embed, row };
}

/**
 * Rename a player's personal channel to match their new character name
 * @param {Client} client - Discord client
 * @param {string} newCharacterName - The new character name
 * @param {string} userId - Discord user ID
 * @param {string} personalChannelId - The player's personal channel ID
 * @returns {Promise<boolean>} - Whether the rename was successful
 */
async function renamePersonalChannel(
  client,
  newCharacterName,
  userId,
  personalChannelId
) {
  try {
    console.log(
      `[ChannelManager] Renaming personal channel for ${newCharacterName} (${userId})`
    );

    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      console.error(
        `[ChannelManager] Guild with ID ${config.guildId} not found`
      );
      return false;
    }

    const channel = await guild.channels
      .fetch(personalChannelId)
      .catch(() => null);
    if (!channel) {
      console.error(
        `[ChannelManager] Personal channel ${personalChannelId} not found for user ${userId}`
      );
      return false;
    }

    // sanitize the new character name for channel naming
    const newChannelName = sanitizeChannelName(newCharacterName);

    // check if name actually needs to change
    if (channel.name === newChannelName) {
      console.log(
        `[ChannelManager] Channel name already matches new character name: ${newChannelName}`
      );
      return true;
    }

    // rename the channel
    await channel.setName(newChannelName);

    console.log(
      `[ChannelManager] ✅ Successfully renamed channel: "${channel.name}" → "${newChannelName}" for ${newCharacterName}`
    );
    return true;
  } catch (error) {
    console.error(
      `[ChannelManager] Error renaming personal channel for ${newCharacterName}:`,
      error
    );
    return false;
  }
}

module.exports = {
  sanitizeChannelName,
  createPersonalChannel,
  canUseCommandsInChannel,
  getSetupEmbed,
  renamePersonalChannel,
};
