const { BITS_EMOJI } = require("../gameConfig");

/**
 * Generates the booster cookie perks description text
 * @returns {string} The formatted perks description
 */
function getBoosterCookiePerksDescription() {
  return `<a:booster_cookie:1400058756183887932> **Booster Cookie Effects**\n\n<:wisdom_stat:1369101731278880849> \`+25 Global Wisdom\`\n<:stat_magic_find:1399855928894951425> \`+15 Magic Find\`\n<:purse_coins:1367849116033482772> \`Keep coins on death\`\n${BITS_EMOJI} \`Ability to gain Bits\`\n\n**Can use these anywhere:**\n• Banking (\`/bank\`)\n• Player Market (\`/market\`)\n• Enchanting (\`/enchant\`)\n• Anvil (\`/anvil\`)\n• Maxwell (\`/maxwell\`)`;
}

module.exports = {
  getBoosterCookiePerksDescription,
};
