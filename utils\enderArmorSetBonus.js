// Special file for the Ender Armor set bonus
// This set bonus is unique because it multiplies stats in a specific region (The End)

const configManager = require("./configManager");

/**
 * Check if a character is in The End region
 * @param {Object} character - The character object
 * @returns {boolean} - True if the character is in The End region
 */
function isInTheEnd(character) {
  return character.current_region === "the_end";
}

/**
 * Calculate the multiplier for Ender Armor stats based on location
 * @param {Object} character - The character object
 * @returns {number} - The multiplier (2 in The End, 1 elsewhere)
 */
function getEnderArmorMultiplier(character) {
  return isInTheEnd(character) ? 2 : 1;
}

/**
 * Apply the Ender Armor set bonus to a character's stats
 * This function is called from playerDataManager when calculating stats
 * @param {Object} character - The character object
 * @param {Object} stats - The stats to modify
 * @returns {Object} - The modified stats object
 */
function applyEnderArmorBonus(character, stats) {
  // Get equipped Ender Armor pieces from both new and old equipment systems
  let enderArmorPieces = [];

  // New system: character.equipment.equipped
  if (character.equipment && character.equipment.equipped) {
    const newSystemItems = Object.values(character.equipment.equipped).filter(
      (item) => item && item.itemKey && item.itemKey.startsWith("ENDER_ARMOR_")
    );

    // Find the actual equipment items from inventory
    if (
      character.inventory &&
      character.inventory.equipment &&
      newSystemItems.length > 0
    ) {
      for (const equippedItem of newSystemItems) {
        const inventoryItem = character.inventory.equipment.find(
          (eq) => eq.itemKey === equippedItem.itemKey && eq.isEquipped
        );
        if (inventoryItem) {
          enderArmorPieces.push(inventoryItem);
        }
      }
    }
  }

  // Old system: character.inventory.equipment (fallback)
  if (
    enderArmorPieces.length === 0 &&
    character.inventory &&
    character.inventory.equipment
  ) {
    enderArmorPieces = character.inventory.equipment.filter(
      (eq) => eq.isEquipped && eq.itemKey.startsWith("ENDER_ARMOR_")
    );
  }

  if (enderArmorPieces.length === 0) return stats;

  // Only apply bonus if in The End
  if (!isInTheEnd(character)) return stats;

  // Check Ender Armor pieces and location

  const allItems = configManager.getAllItems();

  // Get all stats from Ender Armor pieces (base, enchants, hot potato books, reforges)
  const enderStats = {};

  // Calculate the bonus by doubling the stats from Ender Armor pieces
  for (const piece of enderArmorPieces) {
    const itemData = allItems[piece.itemKey];
    if (!itemData) continue;

    // Process Ender Armor piece

    // Add base stats
    if (itemData.baseStats) {
      // Add base stats from piece
      for (const statName in itemData.baseStats) {
        enderStats[statName] =
          (enderStats[statName] || 0) + itemData.baseStats[statName];
      }
    }

    // Process enchantment stats
    if (piece.data_json) {
      let data;
      if (typeof piece.data_json === "string") {
        try {
          data = JSON.parse(piece.data_json);
        } catch (e) {
          console.error(
            `[EnderArmor] Failed to parse data_json for ${piece.itemKey}:`,
            e
          );
          data = {};
        }
      } else {
        data = piece.data_json;
      }

      // Process Protection enchantment
      if (data?.enchantments?.PROTECTION) {
        const level = data.enchantments.PROTECTION;
        console.log(
          "[DEBUG] Found PROTECTION",
          level,
          "adding",
          level * 4,
          "defense"
        );
        const defenseBonus = level * 4;
        enderStats.DEFENSE = (enderStats.DEFENSE || 0) + defenseBonus;
      }

      // Process Growth enchantment
      if (data?.enchantments?.GROWTH) {
        const level = data.enchantments.GROWTH;
        console.log(
          "[DEBUG] Found GROWTH",
          level,
          "adding",
          level * 15,
          "health"
        );
        const healthBonus = level * 15;
        enderStats.HEALTH = (enderStats.HEALTH || 0) + healthBonus;
      }

      // Process Hot Potato Book bonuses
      if (data?.hotPotatoBooks) {
        const bookCount = data.hotPotatoBooks;
        // Armor gets +4 Health and +2 Defense per Hot Potato Book
        enderStats.HEALTH = (enderStats.HEALTH || 0) + bookCount * 4;
        enderStats.DEFENSE = (enderStats.DEFENSE || 0) + bookCount * 2;
      }

      // Process reforge stats (dynamic calculation)
      if (data?.reforge) {
        const {
          calculateDynamicReforgeStats,
        } = require("./dynamicReforgeStats");
        const reforgeStats = calculateDynamicReforgeStats(
          data.reforge,
          itemData
        );

        for (const statName in reforgeStats) {
          enderStats[statName] =
            (enderStats[statName] || 0) + reforgeStats[statName];
        }
      }
    }
  }

  // Apply the Ender Armor set bonus

  // Apply the bonus (an additional 1x of all stats, for a total of 2x)
  // This means we add the enderStats again to double the effect
  for (const statName in enderStats) {
    if (!stats[statName]) stats[statName] = 0;
    // Add bonus stat
    stats[statName] += enderStats[statName];
  }

  // Bonus applied successfully
  return stats;
}

module.exports = {
  isInTheEnd,
  getEnderArmorMultiplier,
  applyEnderArmorBonus,
};
