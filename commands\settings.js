const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  ButtonBuilder,
  ButtonStyle,
  ActionRowBuilder,
} = require("discord.js");
const {
  getPlayerData,
  updatePlayerSetting,
} = require("../utils/playerDataManager");
const {
  EMBED_COLORS,
  SKILLS,
  STATS,
  DISBLOCK_LEVEL_EMOJI,
  GEMS_EMOJI,
  BITS_EMOJI,
} = require("../gameConfig");
const { formatDisblockLevel } = require("../utils/disblockXpSystem");
const { getLevelFromExp } = require("../utils/expFunctions");

/**
 * Convert hex color string to number
 * @param {string} hexColor - Hex color string like "#3498DB"
 * @returns {number} Color as number for Discord embeds
 */
function hexToNumber(hexColor) {
  return parseInt(hexColor.replace("#", ""), 16);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("settings")
    .setDescription("Open your Disblock menu"),
  async execute(interaction) {
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Character Not Found")
        .setDescription(
          "You need to create a character first using `/setup channel`."
        );
      return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // calculate disblock level with decimals
    const disblockLevel = formatDisblockLevel(character.disblock_xp || 0);
    const disblockLevelNumber = Math.floor(parseFloat(disblockLevel));
    const username = character.name;

    // calculate skill average using centralized excluded skills list
    let totalSkillLevel = 0;
    let includedSkillsCount = 0;
    const skillsExcludedSet = new Set(SKILLS.AVERAGE_EXCLUDED);

    SKILLS.LIST.forEach((skillName) => {
      if (!skillsExcludedSet.has(skillName)) {
        const skillData = character.skills?.[skillName] || { exp: 0 };
        const exp = skillData.exp || 0;
        const levelInfo = getLevelFromExp(exp);
        totalSkillLevel += levelInfo.level;
        includedSkillsCount++;
      }
    });

    const skillAverage =
      includedSkillsCount > 0
        ? (totalSkillLevel / includedSkillsCount).toFixed(2)
        : "0.00";

    // get gems amount
    const gems = character.gems || 0;

    // get bits amounts
    const bits = character.bits || 0;
    const bitsAvailable = character.bits_available || 0;

    // emojis
    const disblockLevelEmoji = DISBLOCK_LEVEL_EMOJI;
    const wisdomStatEmoji = STATS.CRAFTING_WISDOM.emoji;
    const gemEmoji = GEMS_EMOJI;
    const bitsEmoji = BITS_EMOJI;

    // build settings UI using regular embeds + standard components
    const settingsUI = createSettingsEmbedUI(interaction.user.id, character, {
      disblockLevel,
      disblockLevelNumber,
      username,
      skillAverage,
      gems,
      bits,
      bitsAvailable,
      disblockLevelEmoji,
      wisdomStatEmoji,
      gemEmoji,
      bitsEmoji,
    });

    await interaction.reply({
      embeds: [settingsUI.embed],
      components: settingsUI.components,
    });
  },

  // persistent handler for settings buttons
  async handleButton(interaction) {
    const customId = interaction.customId;
    const userId = interaction.user.id;

    console.log(`[Settings] Button clicked: ${customId} by user: ${userId}`);

    // validate user ownership from customId
    if (customId.includes("_") && customId.split("_").pop() !== userId) {
      return interaction.reply({
        content: "You cannot use this button.",
        ephemeral: true,
      });
    }

    try {
      if (customId.startsWith("toggle_action_pings_")) {
        await handleActionPingsToggle(interaction);
      } else if (customId.startsWith("toggle_market_pings_")) {
        await handleMarketPingsToggle(interaction);
      } else if (customId.startsWith("toggle_market_mode_")) {
        console.log(
          `[Settings] Handling market mode toggle for user: ${userId}`
        );
        await handleMarketModeToggle(interaction);
      } else if (customId.startsWith("toggle_visitors_full_ping_")) {
        await handleVisitorsFullPingToggle(interaction);
      } else if (customId.startsWith("toggle_action_runtime_info_")) {
        await handleDurationFooterToggle(interaction);
      } else {
        console.log(`[Settings] Unknown button customId: ${customId}`);
      }
    } catch (error) {
      console.error("[Settings] Error handling button toggle:", error);
      await interaction.reply({
        content: "An error occurred. Please try again.",
        ephemeral: true,
      });
    }
  },

  // persistent handler for settings select menus (legacy - no longer used with button system)
  async handleSelectMenu(interaction) {
    // settings now use buttons only, but keeping this for potential future use
    await interaction.reply({
      content:
        "This menu is no longer used. Please use the setting buttons instead.",
      ephemeral: true,
    });
  },
};

/**
 * Creates an embed-based settings UI with interactive components
 * @param {string} userId - Discord user ID
 * @param {object} playerData - Player data from database
 * @param {object} displayData - Calculated display values
 * @returns {{ embed: EmbedBuilder, components: ActionRowBuilder[] }} Embed and components for settings
 */
function createSettingsEmbedUI(userId, playerData, displayData) {
  const {
    disblockLevel,
    disblockLevelNumber,
    username,
    skillAverage,
    gems,
    bits,
    bitsAvailable,
    disblockLevelEmoji,
    wisdomStatEmoji,
    gemEmoji,
    bitsEmoji,
  } = displayData;

  const actionPingsEnabled = playerData.settings?.pingOnMultiAction || false;
  const marketPingsEnabled = playerData.settings?.marketPings !== false;
  const marketNotificationMode =
    playerData.settings?.marketNotificationMode || "full";
  const visitorsFullPingEnabled =
    playerData.settings?.visitorsFullPing !== false;

  // create toggle buttons
  const actionPingsButton = new ButtonBuilder()
    .setCustomId(`toggle_action_pings_${userId}`)
    .setLabel("Action Pings")
    .setStyle(actionPingsEnabled ? ButtonStyle.Success : ButtonStyle.Danger);

  const marketPingsButton = new ButtonBuilder()
    .setCustomId(`toggle_market_pings_${userId}`)
    .setLabel("Market @")
    .setStyle(marketPingsEnabled ? ButtonStyle.Success : ButtonStyle.Danger);

  // create market mode button (cycles through modes)
  const marketModeLabels = {
    none: "Market: None",
    full: "Market: Full",
    all: "Market: All",
  };
  const marketModeStyles = {
    none: ButtonStyle.Danger, // red for no notifications
    full: ButtonStyle.Primary, // blue for medium notifications
    all: ButtonStyle.Success, // green for all notifications
  };
  const marketModeButton = new ButtonBuilder()
    .setCustomId(`toggle_market_mode_${userId}`)
    .setLabel(marketModeLabels[marketNotificationMode])
    .setStyle(marketModeStyles[marketNotificationMode]);

  const visitorsFullPingButton = new ButtonBuilder()
    .setCustomId(`toggle_visitors_full_ping_${userId}`)
    .setLabel("Visitors Full")
    .setStyle(
      visitorsFullPingEnabled ? ButtonStyle.Success : ButtonStyle.Danger
    );
  // duration footer toggle (off by default)
  const durationFooterEnabled = playerData.settings?.showRuntimeInfo === true;
  const durationFooterButton = new ButtonBuilder()
    .setCustomId(`toggle_action_runtime_info_${userId}`)
    .setLabel("Action Runtime Info")
    .setStyle(durationFooterEnabled ? ButtonStyle.Success : ButtonStyle.Danger);

  // Build embed + components
  const embed = new EmbedBuilder()
    .setColor(hexToNumber(EMBED_COLORS.MID_BLUE))
    .setTitle(`[${disblockLevelNumber}] ${username}'s Disblock Menu`)
    .setDescription(
      `${disblockLevelEmoji} **Disblock Level**: ${disblockLevel}\n` +
        `${wisdomStatEmoji} **Skill Average**: ${skillAverage}\n` +
        `${gemEmoji} **Gems**: ${gems.toLocaleString()}\n` +
        `${bitsEmoji} **Bits**: ${bits.toLocaleString()}\n` +
        `${bitsEmoji} **Bits Available**: ${bitsAvailable.toLocaleString()}`
    );
  const row = new ActionRowBuilder().addComponents(
    actionPingsButton,
    marketPingsButton,
    marketModeButton,
    visitorsFullPingButton,
    durationFooterButton
  );
  return { embed, components: [row] };
}

// legacy function removed - replaced with embed-based implementation

/**
 * Recreates the settings embed UI with updated data (used for button interactions)
 * @param {string} userId - Discord user ID
 * @param {object} playerData - Updated player data
 * @returns {{ embed: EmbedBuilder, components: ActionRowBuilder[] }} Updated settings UI
 */
async function recreateSettingsEmbedUI(userId, playerData) {
  // we need to recalculate display data since it's not stored
  const disblockLevel = formatDisblockLevel(playerData.disblock_xp || 0);
  const disblockLevelNumber = Math.floor(parseFloat(disblockLevel));
  const username = playerData.name;

  // calculate skill average
  let totalSkillLevel = 0;
  let includedSkillsCount = 0;
  const skillsExcludedSet = new Set(SKILLS.AVERAGE_EXCLUDED);

  SKILLS.LIST.forEach((skillName) => {
    if (!skillsExcludedSet.has(skillName)) {
      const skillData = playerData.skills?.[skillName] || { exp: 0 };
      const exp = skillData.exp || 0;
      const levelInfo = getLevelFromExp(exp);
      totalSkillLevel += levelInfo.level;
      includedSkillsCount++;
    }
  });

  const skillAverage =
    includedSkillsCount > 0
      ? (totalSkillLevel / includedSkillsCount).toFixed(2)
      : "0.00";

  const gems = playerData.gems || 0;
  const bits = playerData.bits || 0;
  const bitsAvailable = playerData.bits_available || 0;

  const displayData = {
    disblockLevel,
    disblockLevelNumber,
    username,
    skillAverage,
    gems,
    bits,
    bitsAvailable,
    disblockLevelEmoji: DISBLOCK_LEVEL_EMOJI,
    wisdomStatEmoji: STATS.CRAFTING_WISDOM.emoji,
    gemEmoji: GEMS_EMOJI,
    bitsEmoji: BITS_EMOJI,
  };

  return createSettingsEmbedUI(userId, playerData, displayData);
}

/**
 * Handles toggling action ping setting
 * @param {object} interaction - Discord interaction
 */
async function handleActionPingsToggle(interaction) {
  const userId = interaction.user.id;

  try {
    // get player data
    const playerData = await getPlayerData(userId);

    if (!playerData) {
      await interaction.reply({
        content: "Player data not found.",
        ephemeral: true,
      });
      return;
    }

    // get current setting value, defaulting to false if not set
    const currentActionPingsState =
      playerData.settings?.pingOnMultiAction || false;
    const newActionPingsState = !currentActionPingsState;

    // atomically update the setting to prevent race conditions
    await updatePlayerSetting(userId, "pingOnMultiAction", newActionPingsState);

    // get updated player data and rebuild embed UI
    const updatedPlayerData = await getPlayerData(userId);
    const updatedUI = await recreateSettingsEmbedUI(userId, updatedPlayerData);

    // update message with new embed + components
    await interaction.update({
      embeds: [updatedUI.embed],
      components: updatedUI.components,
    });
  } catch (error) {
    console.error("[Settings] Error toggling action pings:", error);
    await interaction.reply({
      content: "An error occurred while updating your action ping settings.",
      ephemeral: true,
    });
  }
}

/**
 * Handles toggling market ping setting
 * @param {object} interaction - Discord interaction
 */
async function handleMarketPingsToggle(interaction) {
  const userId = interaction.user.id;

  try {
    // get player data
    const playerData = await getPlayerData(userId);

    if (!playerData) {
      await interaction.reply({
        content: "Player data not found.",
        ephemeral: true,
      });
      return;
    }

    // get current setting value, defaulting to true if not set
    const currentMarketPingsState = playerData.settings?.marketPings !== false;
    const newMarketPingsState = !currentMarketPingsState;

    // atomically update the setting to prevent race conditions
    await updatePlayerSetting(userId, "marketPings", newMarketPingsState);

    // get updated player data and rebuild embed UI
    const updatedPlayerData = await getPlayerData(userId);
    const updatedUI = await recreateSettingsEmbedUI(userId, updatedPlayerData);

    await interaction.update({
      embeds: [updatedUI.embed],
      components: updatedUI.components,
    });
  } catch (error) {
    console.error("[Settings] Error toggling market pings:", error);
    await interaction.reply({
      content: "An error occurred while updating your market ping settings.",
      ephemeral: true,
    });
  }
}

/**
 * Handles toggling market mode button (cycles through none -> full -> all -> none)
 * @param {object} interaction - Discord interaction
 */
async function handleMarketModeToggle(interaction) {
  const userId = interaction.user.id;

  console.log(`[Settings] handleMarketModeToggle called for user: ${userId}`);

  try {
    // get player data
    const playerData = await getPlayerData(userId);

    if (!playerData) {
      console.log(`[Settings] Player data not found for user: ${userId}`);
      await interaction.reply({
        content: "Player data not found.",
        ephemeral: true,
      });
      return;
    }

    // get current mode and cycle to next
    const currentMode = playerData.settings?.marketNotificationMode || "full";
    const modeOrder = ["none", "full", "all"];
    const currentIndex = modeOrder.indexOf(currentMode);
    const nextIndex = (currentIndex + 1) % modeOrder.length;
    const newMode = modeOrder[nextIndex];

    console.log(
      `[Settings] Market mode changing from ${currentMode} to ${newMode} for user: ${userId}`
    );

    // atomically update the setting to prevent race conditions
    await updatePlayerSetting(userId, "marketNotificationMode", newMode);

    // get updated player data and rebuild embed UI
    const updatedPlayerData = await getPlayerData(userId);
    const updatedUI = await recreateSettingsEmbedUI(userId, updatedPlayerData);

    await interaction.update({
      embeds: [updatedUI.embed],
      components: updatedUI.components,
    });

    console.log(
      `[Settings] Market mode toggle completed successfully for user: ${userId}`
    );
  } catch (error) {
    console.error("[Settings] Error toggling market mode:", error);
    await interaction.reply({
      content: "An error occurred while updating your market mode settings.",
      ephemeral: true,
    });
  }
}
async function _handleMarketModeSelection(interaction, selectedMode) {
  const userId = interaction.user.id;

  try {
    // get player data
    const playerData = await getPlayerData(userId);

    if (!playerData) {
      await interaction.reply({
        content: "Player data not found.",
        ephemeral: true,
      });
      return;
    }

    // atomically update the setting to prevent race conditions
    await updatePlayerSetting(userId, "marketNotificationMode", selectedMode);

    // get updated player data and rebuild embed UI
    const updatedPlayerData = await getPlayerData(userId);
    const updatedUI = await recreateSettingsEmbedUI(userId, updatedPlayerData);

    await interaction.update({
      embeds: [updatedUI.embed],
      components: updatedUI.components,
    });
  } catch (error) {
    console.error("[Settings] Error handling market mode selection:", error);
    await interaction.reply({
      content:
        "An error occurred while updating your market notification settings.",
      ephemeral: true,
    });
  }
}

/**
 * Handles toggling visitors full ping setting
 * @param {object} interaction - Discord interaction
 */
async function handleVisitorsFullPingToggle(interaction) {
  const userId = interaction.user.id;

  try {
    // get player data
    const playerData = await getPlayerData(userId);

    if (!playerData) {
      await interaction.reply({
        content: "Player data not found.",
        ephemeral: true,
      });
      return;
    }

    // get current setting value, defaulting to true if not set
    const currentVisitorsFullPingState =
      playerData.settings?.visitorsFullPing !== false;
    const newVisitorsFullPingState = !currentVisitorsFullPingState;

    // atomically update the setting to prevent race conditions
    await updatePlayerSetting(
      userId,
      "visitorsFullPing",
      newVisitorsFullPingState
    );

    // get updated player data and rebuild embed UI
    const updatedPlayerData = await getPlayerData(userId);
    const updatedUI = await recreateSettingsEmbedUI(userId, updatedPlayerData);

    // update message with new embed UI
    await interaction.update({
      embeds: [updatedUI.embed],
      components: updatedUI.components,
    });
  } catch (error) {
    console.error("[Settings] Error toggling visitors full ping:", error);
    await interaction.reply({
      content: "An error occurred while updating your visitors ping settings.",
      ephemeral: true,
    });
  }
}
/**
 * Handles toggling total time footer display
 * @param {object} interaction - Discord interaction
 */
async function handleDurationFooterToggle(interaction) {
  const userId = interaction.user.id;
  try {
    const playerData = await getPlayerData(userId);
    if (!playerData) {
      await interaction.reply({
        content: "Player data not found.",
        ephemeral: true,
      });
      return;
    }
    const currentState = playerData.settings?.showRuntimeInfo === true;
    const newState = !currentState;
    await updatePlayerSetting(userId, "showRuntimeInfo", newState);
    const updatedPlayerData = await getPlayerData(userId);
    const updatedUI = await recreateSettingsEmbedUI(userId, updatedPlayerData);
    await interaction.update({
      embeds: [updatedUI.embed],
      components: updatedUI.components,
    });
  } catch (error) {
    console.error("[Settings] Error toggling duration footer setting:", error);
    await interaction.reply({
      content: "An error occurred while updating your duration footer setting.",
      ephemeral: true,
    });
  }
}
