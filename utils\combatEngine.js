const { EmbedBuilder } = require("discord.js");
const { calculateHealthRegen } = require("./healthRegen.js");
const { getPlayerAccessories } = require("./accessoryManager.js");
const configManager = require("./configManager.js");
const { calculateAllStats } = require("./statCalculations.js");
const { getActiveSetBonuses } = require("./setBonuses.js");
const { ENCHANTMENTS } = require("../commands/enchant.js");
const { STATS, EMBED_COLORS } = require("../gameConfig.js");
const { formatXP } = require("./formatUtils.js");
const {
  calculateAccessoryDamageReduction,
  calculateDamage,
  calculateDamageWithContext,
  calculateDamageTaken,
} = require("./damage.js");
const {
  createDamageCalculationContext,
} = require("./damageCalculationContext.js");
const { createCustomHealthBar } = require("./healthBar.js");
const { processMobAbilities } = require("./mobAbilities.js");
const { hexToNumber } = require("./animationContainers.js");

// --- Unified Turn-Based Fight Helper ---
async function runTurnBasedFight({
  interaction,
  mobInstanceData,
  character,
  currentFightNum,
  totalFights,
  restoreState = null,
  actionId = null, // Re-added actionId parameter for stop button functionality
  isSeaCreatureSubCombat = false, // Add parameter to determine if this is sea creature combat during fishing
  preCalculatedStats = null, // Add parameter for pre-calculated stats
  slayerQuestData = null, // Add parameter for slayer quest tracking
  messageId = null, // Add parameter for message ID to edit
  channel = null, // Add parameter for channel to send messages
  lastUpdateTime = 0, // Add parameter for shared timing tracker
  /* isResumption = false, */ // Add parameter for resumption status
}) {
  // Create a local copy of the character to prevent object corruption
  const localCharacter = JSON.parse(JSON.stringify(character));

  // -------------------------------------------------------------------
  // Fetch the original Discord message once so subsequent edits use
  // message.edit().  Using message.edit keeps existing components intact
  // (unlike channel.messages.edit which clears them when the components
  // field is omitted), eliminating the visible "flicker" of the Stop
  // Combat button on every embed refresh.
  // -------------------------------------------------------------------
  let battleMessage = null;
  // If we cannot safely edit the provided message, create a separate standard
  // message for the battle UI and update that instead.
  let createdStandaloneBattleMessage = false;
  // Preserve non-container rows (e.g., stop/bait buttons) from the original message
  let preservedActionRows = null;
  if (channel && messageId) {
    try {
      // Prefer cached version to avoid an API call when possible.
      battleMessage =
        channel.messages.cache.get(messageId) ||
        (await channel.messages.fetch(messageId));
    } catch (fetchErr) {
      console.warn(
        `[runTurnBasedFight] Could not fetch battle message ${messageId}:`,
        fetchErr?.message || fetchErr
      );
      battleMessage = null; // Fallback – we will use channel.messages.edit as before.
    }
  }

  const mobStats = mobInstanceData.stats;
  const mobName = mobInstanceData.name;
  let mobHealth = mobStats.health;
  let characterHealth = Math.max(1, localCharacter.current_health);
  const battleLog = { player: [], mob: [] };

  // Apply Frail enchantment effect for sea creatures
  if (isSeaCreatureSubCombat) {
    const userId = character.discordId;
    try {
      const { getEquippedFishingRod } = require("./equipmentUtils.js");
      const fishingRod = await getEquippedFishingRod(userId);
      if (fishingRod && fishingRod.data_json) {
        const rodData = JSON.parse(fishingRod.data_json);
        if (rodData.enchantments && rodData.enchantments.FRAIL) {
          const frailLevel = rodData.enchantments.FRAIL;
          const frailHealthReduction = frailLevel * 2.5; // 2.5% per level
          const healthReductionMultiplier = 1 - frailHealthReduction / 100;
          mobHealth = Math.floor(mobHealth * healthReductionMultiplier);
        }
      }
    } catch (error) {
      console.error("[COMBAT] Error checking Frail enchantment:", error);
    }
  }

  // Initialize turn events collector
  let turnEvents = { player: [], mob: [] };

  let lastRegenTime = process.hrtime.bigint();
  let victory = false;
  let turn = 1;
  const mobAttackInterval = 2;

  // Initialize rewards variable
  let rewards = {};

  // Track Combat Wisdom bonuses from abilities
  const totalCombatWisdomBonus = 0;

  // Get abort signal for stop button functionality
  const { getAbortSignal } = require("./instantStopUtils.js");
  const signal = actionId ? getAbortSignal(actionId) : null;

  // Components will be preserved from existing message, not recreated here

  // Use pre-calculated stats if provided, otherwise calculate locally
  const characterTotalStats =
    preCalculatedStats?.allStats || calculateAllStats(localCharacter);
  const activeSetBonuses = getActiveSetBonuses(localCharacter);
  const isSeaCreature = !!mobInstanceData.seaCreature;
  const anglerReduction =
    isSeaCreature && activeSetBonuses.angler
      ? activeSetBonuses.angler.damageReduction
      : 0;

  if (restoreState) {
    mobHealth = restoreState.mobHealth;
    characterHealth = restoreState.characterHealth;
    turn = restoreState.turn;
    if (restoreState.battleLog) {
      battleLog.player = restoreState.battleLog.player || battleLog.player;
      battleLog.mob = restoreState.battleLog.mob || battleLog.mob;
    }
  }

  const STRICT_UPDATE_INTERVAL = 1000; // Minimum time between updates in milliseconds (increased to prevent Discord rate limiting)
  const TURNS_PER_UPDATE = 1; // Number of turns to process per update interval

  async function updateBattleEmbed(titleColor = "#FFA500") {
    if (!interaction) return;

    // Removed combat stop functionality - abort signal check before updating

    // Add any pending turn events to the battle log
    if (turnEvents.player.length > 0 || turnEvents.mob.length > 0) {
      battleLog.player.push(...turnEvents.player);
      battleLog.mob.push(...turnEvents.mob);
      turnEvents = { player: [], mob: [] }; // Reset for next batch
    }

    localCharacter.current_health = characterHealth;

    // Determine title based on mob type and slayer quest status
    let title;

    // Check if this is a slayer boss or mini-boss (no cycle tracking)
    if (mobInstanceData.slayer || mobInstanceData.miniBoss) {
      title = `Fighting ${mobName}...`;
    } else if (slayerQuestData && slayerQuestData.isActive) {
      // Fighting regular mobs during slayer quest, show cycle tracking
      title = `Fighting ${mobName} (${currentFightNum}/${totalFights})`;
    } else {
      // Regular combat, show cycle tracking
      title = `Fighting ${mobName} (${currentFightNum}/${totalFights})`;
    }

    // Add slayer boss/mini-boss labels to title
    if (mobInstanceData.slayer) {
      title += " (Slayer Boss)";
    } else if (mobInstanceData.miniBoss) {
      title += " (Slayer Mini Boss)";
    }

    // Create custom health bars
    const playerHealthBar = createCustomHealthBar(
      characterHealth,
      localCharacter.totalStats.HEALTH
    );
    const mobHealthBar = createCustomHealthBar(
      mobHealth,
      mobInstanceData.stats.health
    );

    // Create battle log
    // Ensure the diff block always has exactly 6 lines (3 player, 3 mob lines) to
    // keep the embed height stable and avoid visual "pop" that caused the stop
    // button to appear to flicker when the block grew.

    const playerLines = battleLog.player.slice(-3).map((a) => "+ " + a);
    const mobLines = battleLog.mob.slice(-3).map((a) => "- " + a);

    let combinedLines = [...playerLines, ...mobLines];
    // Keep only the last 5 lines to stabilise height
    if (combinedLines.length > 5) {
      combinedLines = combinedLines.slice(-5);
    }

    // Pad to exactly 5 lines with placeholders
    while (combinedLines.length < 5) {
      combinedLines.push(" ");
    }

    const battleLogText = `\`\`\`diff\n${combinedLines.join("\n")}\n\`\`\``;

    // Build regular embed for battle UI with side-by-side health bars using inline fields
    const colorNum =
      typeof titleColor === "string" && titleColor.startsWith("#")
        ? hexToNumber(titleColor)
        : 0xffa500;
    const playerFieldName = `👤 ${localCharacter.name} [${formatXP(Math.round(characterHealth))}/${formatXP(localCharacter.totalStats.HEALTH)}]`;
    const mobFieldName = `${mobInstanceData.emoji || "👾"} ${mobName} [${formatXP(Math.round(mobHealth))}/${formatXP(mobInstanceData.stats.health)}]`;

    const embed = new EmbedBuilder()
      .setColor(colorNum)
      .setTitle(title)
      .addFields(
        {
          name: playerFieldName,
          value: playerHealthBar || "` `",
          inline: true,
        },
        { name: mobFieldName, value: mobHealthBar || "` `", inline: true },
        { name: "", value: battleLogText, inline: false }
      )
      .setFooter({
        text:
          slayerQuestData &&
          slayerQuestData.isActive &&
          slayerQuestData.isValidMob &&
          slayerQuestData.killsRemaining > 0
            ? `${slayerQuestData.killsRemaining} more kills until ${slayerQuestData.bossName} spawns`
            : "\u200B",
      });

    // ENFORCE STRICT 500MS DELAY - NO EXCEPTIONS
    const now = Date.now();
    const timeSinceLastUpdate = now - lastUpdateTime;

    if (timeSinceLastUpdate < STRICT_UPDATE_INTERVAL) {
      const waitTime = Math.max(
        1,
        STRICT_UPDATE_INTERVAL - timeSinceLastUpdate
      );

      // Removed combat stop functionality - cancellable delay
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    } else {
      // No delay needed, update immediately
    }

    // Update the message with enhanced rate limit detection
    try {
      // Prepare preserved button rows once
      if (battleMessage && preservedActionRows === null) {
        const existing = battleMessage.components || [];
        preservedActionRows = existing; // keep existing rows as-is
      }

      const componentsPayload = preservedActionRows || [];

      if (battleMessage) {
        const updateStartTime = Date.now();
        await battleMessage.edit({
          embeds: [embed],
          components: componentsPayload,
        });
        lastUpdateTime = Date.now();
        const updateDuration = lastUpdateTime - updateStartTime;
        if (updateDuration > 2000) {
          console.warn(
            `[Combat] ⚠️ SLOW UPDATE via message.edit (took ${updateDuration}ms)`
          );
        }
      } else if (messageId && channel) {
        const updateStartTime = Date.now();
        await channel.messages.edit(messageId, {
          embeds: [embed],
          components: componentsPayload,
        });
        lastUpdateTime = Date.now();
        const updateDuration = lastUpdateTime - updateStartTime;
        if (updateDuration > 2000) {
          console.warn(
            `[Combat] ⚠️ SLOW UPDATE via channel.messages.edit (took ${updateDuration}ms)`
          );
        }
      }
    } catch (editError) {
      // Proceed with existing error handling
      // Handle message edit errors gracefully
      if (
        editError.message ===
        "Message update timeout - possible internal rate limiting"
      ) {
        console.error(
          `🚨 [RATE LIMIT] Message update timed out after 5 seconds - Discord.js likely handling rate limits internally`
        );
        console.error(
          `🚨 [RATE LIMIT] This explains the freezing behavior - Discord.js is waiting for rate limits to clear`
        );
        // Still update lastUpdateTime to prevent immediate retry
        lastUpdateTime = Date.now();
      } else if (editError.code === 10008) {
        // Message was deleted, ignore
      } else if (editError.code === 50013) {
        // Missing permissions, ignore
      } else if (editError.code === 50005) {
        // Cannot edit a message authored by another user - this happens during resumption
        // when a different worker tries to edit a message created by the original worker
        console.log(
          `[runTurnBasedFight] Cannot edit message authored by another user (resumption scenario) - battle will continue without visual updates`
        );
        // Don't spam the logs with this error - it's expected during resumption
        lastUpdateTime = Date.now();
      } else if (editError.code === 429) {
        // Rate limit error - this is what we want to detect!
        console.error(
          `🚨 [RATE LIMIT] Discord API rate limited! Retry after: ${editError.retryAfter}ms`
        );
        console.error(`🚨 [RATE LIMIT] Error details:`, {
          message: editError.message,
          code: editError.code,
          retryAfter: editError.retryAfter,
          global: editError.global,
          method: editError.method,
          url: editError.url,
        });
        // Update lastUpdateTime to prevent immediate retry
        lastUpdateTime = Date.now();
      } else if (editError.status === 429) {
        // Alternative rate limit detection
        console.error(`🚨 [RATE LIMIT] Discord API rate limited (status 429)!`);
        console.error(`🚨 [RATE LIMIT] Full error:`, editError);
        // Update lastUpdateTime to prevent immediate retry
        lastUpdateTime = Date.now();
      } else {
        console.warn(
          `[runTurnBasedFight] Error updating battle embed: ${editError.message} (code: ${editError.code}, status: ${editError.status})`
        );
      }
    }

    return lastUpdateTime;
  }

  // No initial embed - start directly with first turn
  lastUpdateTime = Date.now(); // Initialize timing

  try {
    let immediateUpdateOccurred = false; // Track if we just did an immediate update

    while (mobHealth > 0 && characterHealth > 0) {
      immediateUpdateOccurred = false; // Reset flag for each cycle

      // Process multiple turns per update cycle
      for (
        let turnInCycle = 0;
        turnInCycle < TURNS_PER_UPDATE && mobHealth > 0 && characterHealth > 0;
        turnInCycle++
      ) {
        // Add mob appears message at the very start of the first turn
        if (turn === 1 && turnInCycle === 0) {
          turnEvents.mob.push(`${mobName} appears!`);
        }

        // --- Instant Abort Check during individual fights ---
        if (signal && signal.aborted) {
          return {
            victory: false,
            fled: true,
            finalCharacterState: localCharacter,
            rewards: null,
            lastUpdateTime: lastUpdateTime,
          };
        }
        // --- End Instant Abort Check ---

        const currentTime = process.hrtime.bigint();
        // Handle health regen without updating the embed
        if (Number(currentTime - lastRegenTime) >= **********n) {
          // 2 seconds in nanoseconds
          const healthRegen = calculateHealthRegen(localCharacter);
          if (healthRegen > 0 && characterHealth < characterTotalStats.HEALTH) {
            characterHealth = Math.min(
              characterTotalStats.HEALTH,
              characterHealth + healthRegen
            );
            // Don't update embed here, just update the health value
            // The next regular update will show the new health
          }
          lastRegenTime = currentTime;
        }

        const characterStats = characterTotalStats;

        // Check for abort signal before damage calculations
        if (signal && signal.aborted) {
          return {
            victory: false,
            fled: true,
            finalCharacterState: localCharacter,
            rewards: null,
            lastUpdateTime: lastUpdateTime,
          };
        }

        // --- CLEAN DAMAGE CALCULATION SYSTEM ---
        // Use the inventory equipment system as the single source of truth for all weapon lookups.
        // The character.equipment.equipped system has fundamental flaws (multiple tools overwrite each other)
        // so we stick with the proven character.inventory.equipment with isEquipped flags.

        // Determine weapon base damage
        let weaponBaseDamage = 0;
        const allItems = configManager.getAllItems();
        // Find equipped weapon using the inventory equipment system (single source of truth)
        const equippedWeapon = localCharacter.inventory?.equipment?.find(
          (eq) =>
            eq.isEquipped &&
            eq.itemKey &&
            allItems[eq.itemKey]?.type === "WEAPON"
        );

        if (equippedWeapon) {
          const weaponDef = allItems[equippedWeapon.itemKey];
          if (weaponDef && typeof weaponDef.baseStats?.DAMAGE === "number") {
            weaponBaseDamage = weaponDef.baseStats.DAMAGE;

            // Special ability: Emerald Blade coin-based damage
            if (equippedWeapon.itemKey === "EMERALD_BLADE") {
              const playerCoins = localCharacter.coins || 0;
              const cappedCoins = Math.min(playerCoins, **********);
              const bonusDamage = Math.pow(cappedCoins, 1 / 4) * 2.5;
              weaponBaseDamage += bonusDamage;
            }
          } else if (weaponDef && typeof weaponDef.damage === "number") {
            // Fallback for older format
            weaponBaseDamage = weaponDef.damage;
          }
        }

        // Create damage calculation context with current mob data for conditional bonuses
        const mobDataForContext = {
          ...mobInstanceData,
          current_health: mobHealth, // Include current health for Execute enchantment
        };

        const damageContext = createDamageCalculationContext(
          localCharacter,
          mobDataForContext
        );

        // Calculate damage using new context system
        const calculatedDamage = calculateDamageWithContext(
          weaponBaseDamage,
          characterStats.STRENGTH || 0,
          characterStats.CRIT_CHANCE || 0,
          characterStats.CRIT_DAMAGE || 0,
          damageContext,
          mobDataForContext
        );

        let damageDealt = calculatedDamage.damage;
        const isCritical = calculatedDamage.isCritical;

        // Check for Octodexterity set bonus (double damage every 4th turn)
        const { getActiveSetBonuses } = require("./setBonuses");
        const activeSetBonuses = getActiveSetBonuses(localCharacter);
        let octodexterityBonus = false;

        if (
          activeSetBonuses.tarantula &&
          activeSetBonuses.tarantula.octodexterity &&
          turn % 4 === 0
        ) {
          damageDealt *= 2;
          octodexterityBonus = true;
        }

        const finalDamageDealt = damageDealt; // damageDealt now includes GK/Execute via calculateDamage

        mobHealth = Math.max(0, mobHealth - finalDamageDealt);

        // Build damage message with special effect indicators
        let damageMessage = `You deal ${Math.round(finalDamageDealt)} damage`;
        if (isCritical) {
          damageMessage += " (Crit!)";
        }
        if (octodexterityBonus) {
          damageMessage += " (Octodexterity!)";
        }

        turnEvents.player.push(damageMessage);

        // Removed combat stop functionality - abort signal check after dealing damage

        // Apply Life Steal effect if equipped weapon has it
        if (equippedWeapon?.data_json?.enchantments?.LIFE_STEAL) {
          const lifeStealLevel =
            equippedWeapon.data_json.enchantments.LIFE_STEAL;
          const maxHealth = characterTotalStats.HEALTH;
          const baseHealAmount = maxHealth * (0.005 * lifeStealLevel); // 0.5% per level

          if (baseHealAmount > 0) {
            const { applyVitalityHealing } = require("./healingUtils");
            const finalHealAmount = applyVitalityHealing(
              localCharacter,
              baseHealAmount
            );
            characterHealth = Math.min(
              characterTotalStats.HEALTH,
              characterHealth + finalHealAmount
            );
            // Life steal healing is applied silently - no combat log message
          }
        }

        // Apply weapon abilities that heal on hit
        if (equippedWeapon) {
          const weaponDef = allItems[equippedWeapon.itemKey];
          if (weaponDef?.abilities) {
            for (const [, ability] of Object.entries(weaponDef.abilities)) {
              if (ability.type === "HEAL_ON_HIT") {
                const baseHealAmount = ability.healAmount || 0;
                if (baseHealAmount > 0) {
                  const { applyVitalityHealing } = require("./healingUtils");
                  const finalHealAmount = applyVitalityHealing(
                    localCharacter,
                    baseHealAmount
                  );
                  characterHealth = Math.min(
                    characterTotalStats.HEALTH,
                    characterHealth + finalHealAmount
                  );
                  /*turnEvents.player.push(
                  `💚 ${ability.name || "Healing"} restored ${finalHealAmount} health!`
                );*/
                }
              }
            }
          }
        }

        // --- Thorns Enchantment Logic (Per Piece) ---
        let totalReflectedDamagePercent = 0; // Initialize total reflected percentage
        if (
          localCharacter.inventory &&
          Array.isArray(localCharacter.inventory.equipment)
        ) {
          for (const eq of localCharacter.inventory.equipment) {
            if (eq.isEquipped) {
              const itemDef =
                require("./configManager.js").getAllItems()[eq.itemKey];
              // Check if it's an armor piece
              const isArmorPiece =
                itemDef &&
                (itemDef.type === "HELMET" ||
                  itemDef.type === "CHESTPLATE" ||
                  itemDef.type === "LEGGINGS" ||
                  itemDef.type === "BOOTS" ||
                  (typeof itemDef.type === "object" &&
                    (itemDef.type.name === "ARMOR" ||
                      (itemDef.type.subtypes &&
                        Object.values(itemDef.type.subtypes).some((sub) =>
                          [
                            "HELMET",
                            "CHESTPLATE",
                            "LEGGINGS",
                            "BOOTS",
                          ].includes(sub)
                        )))));

              if (isArmorPiece && eq.data_json?.enchantments?.THORNS) {
                const thornsLevel = eq.data_json.enchantments.THORNS;
                const thornsChance = 0.5;
                const damageReflectPercentPerLevel = 0.03;

                if (Math.random() < thornsChance) {
                  const reflectedPercentFromPiece =
                    thornsLevel * damageReflectPercentPerLevel;
                  totalReflectedDamagePercent += reflectedPercentFromPiece;
                }
              }
            }
          }
        }

        if (totalReflectedDamagePercent > 0) {
          const reflectedDamage =
            finalDamageDealt * totalReflectedDamagePercent;
          if (reflectedDamage > 0) {
            mobHealth = Math.max(0, mobHealth - reflectedDamage);
          }
        }

        if (mobHealth <= 0) {
          victory = true;
          turnEvents.player.push(`${mobName} defeated!`);

          // Track mob kills for Bulwark armor pieces
          if (localCharacter?.inventory?.equipment) {
            try {
              const { updateInventoryAtomically } = require("./inventory.js");
              const allItems = configManager.getAllItems();
              const equipmentDataUpdates = [];

              // Check each equipped armor piece for Bulwark ability
              for (const equipment of localCharacter.inventory.equipment) {
                if (equipment.isEquipped) {
                  const itemData = allItems[equipment.itemKey];

                  // Check if this is armor with Bulwark ability
                  if (
                    itemData?.type === "ARMOR" &&
                    (itemData.ability?.type === "BULWARK" ||
                      itemData.ability?.type === "ZOMBIE_BULWARK" ||
                      Object.values(itemData.abilities || {}).some(
                        (ability) =>
                          ability.type === "BULWARK" ||
                          ability.type === "ZOMBIE_BULWARK"
                      ))
                  ) {
                    // Get the bulwark ability definition
                    const bulwarkAbility =
                      itemData.ability?.type === "BULWARK" ||
                      itemData.ability?.type === "ZOMBIE_BULWARK"
                        ? itemData.ability
                        : Object.values(itemData.abilities || {}).find(
                            (ability) =>
                              ability.type === "BULWARK" ||
                              ability.type === "ZOMBIE_BULWARK"
                          );

                    // Check if the killed mob matches the target type for this bulwark ability
                    const targetMobType =
                      bulwarkAbility?.targetMobType || "Undead";
                    if (mobInstanceData.mobType !== targetMobType) {
                      continue; // Skip this equipment piece if mob type doesn't match
                    }

                    // Parse existing data_json or initialize
                    let equipmentData = {};
                    if (equipment.data_json) {
                      try {
                        equipmentData =
                          typeof equipment.data_json === "string"
                            ? JSON.parse(equipment.data_json)
                            : equipment.data_json;
                      } catch {
                        console.warn(
                          `[ZombieBulwark] Failed to parse data_json for ${equipment.itemKey}`
                        );
                        equipmentData = {};
                      }
                    }

                    // Use a generic property name that works for all mob types
                    const killPropertyName =
                      targetMobType === "Undead"
                        ? "zombieKills"
                        : "bulwarkKills";
                    const prevKills = equipmentData[killPropertyName] || 0;

                    // Increment kill count
                    equipmentData[killPropertyName] = prevKills + 1;

                    // --------------------------------------------------
                    // Bulwark Level-Up Detection & Notification
                    // --------------------------------------------------
                    try {
                      if (
                        bulwarkAbility &&
                        Array.isArray(bulwarkAbility.thresholds)
                      ) {
                        // Helper to get bonus for a given kill count
                        const getBonusForKills = (kills) => {
                          let bonus = 0;
                          for (const threshold of bulwarkAbility.thresholds) {
                            if (kills >= threshold.kills) {
                              bonus = threshold.bonus;
                            } else {
                              break;
                            }
                          }
                          return bonus;
                        };

                        const oldBonus = getBonusForKills(prevKills);
                        const newBonus = getBonusForKills(prevKills + 1);

                        if (newBonus > oldBonus) {
                          // Compose notification message similar to Slayer XP gain
                          const statKey = bulwarkAbility.stat || "DEFENSE";
                          const statConf = STATS[statKey] || {
                            name: statKey,
                            emoji: "",
                          };
                          const statEmoji = statConf.emoji || "";
                          const statNameReadable =
                            statConf.name ||
                            statKey
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (c) => c.toUpperCase());

                          const abilityTarget =
                            bulwarkAbility.targetMobType || "Undead";

                          const messageLines = [
                            `Your ${itemData.emoji || ""} **${itemData.name}**'s ${bulwarkAbility.name || "Bulwark"} leveled up!\n`,
                            "**New Stats**",
                            `${statEmoji} **${oldBonus} > ${newBonus} ${statNameReadable}** against ${abilityTarget} mobs`,
                          ];

                          try {
                            const embed = new EmbedBuilder()
                              .setColor(EMBED_COLORS.GOLD) // Gold for achievements/level-ups
                              .setDescription(messageLines.join("\n"));

                            // Only send notification if channel is available
                            if (channel) {
                              const levelUpMsg = await channel.send({
                                embeds: [embed],
                              });

                              // Delete after 20 seconds
                              setTimeout(async () => {
                                try {
                                  await levelUpMsg.delete();
                                } catch {
                                  // Ignore deletion errors
                                }
                              }, 20000);
                            } else {
                              // No channel available, skip notification
                            }
                          } catch (notifyErr) {
                            console.error(
                              "[Bulwark Level-Up] Error sending notification:",
                              notifyErr
                            );
                          }
                        }
                      }
                    } catch (bulwarkNotifyErr) {
                      console.error(
                        "[Bulwark Level-Up] Error processing notification:",
                        bulwarkNotifyErr
                      );
                    }

                    // Queue data_json update for persistence
                    const updatedDataJson = JSON.stringify(equipmentData);
                    equipmentDataUpdates.push({
                      equipmentId: equipment.id,
                      dataJson: updatedDataJson,
                    });
                    equipment.data_json = updatedDataJson;
                  }
                }
              }

              // Update all equipment data atomically if there were changes
              if (equipmentDataUpdates.length > 0) {
                await updateInventoryAtomically(
                  localCharacter.discordId,
                  0, // No coin change
                  [], // No items to change
                  [], // No equipment to add
                  [], // No equipment to remove
                  0, // No bank coins
                  equipmentDataUpdates // Equipment data updates
                );
              }
            } catch (zombieTrackingError) {
              console.error(
                `[ZombieBulwark] Error tracking zombie kills:`,
                zombieTrackingError
              );
            }
          }

          // Track total kills for KILL_TRACKER abilities (e.g., Raider Sword)
          if (localCharacter?.inventory?.equipment) {
            try {
              const { updateInventoryAtomically } = require("./inventory.js");
              const allItems = configManager.getAllItems();
              const killTrackerUpdates = [];

              // Check each equipped item for KILL_TRACKER ability
              for (const equipment of localCharacter.inventory.equipment) {
                if (equipment.isEquipped) {
                  const itemData = allItems[equipment.itemKey];

                  // Check if this item has a KILL_TRACKER ability
                  const abilities =
                    itemData?.abilities ||
                    (itemData?.ability
                      ? { singleAbility: itemData.ability }
                      : {});
                  const killTrackerAbility = Object.values(abilities).find(
                    (ability) => ability.type === "KILL_TRACKER"
                  );

                  if (killTrackerAbility) {
                    // Parse existing data_json or initialize
                    let equipmentData = {};
                    if (equipment.data_json) {
                      try {
                        equipmentData =
                          typeof equipment.data_json === "string"
                            ? JSON.parse(equipment.data_json)
                            : equipment.data_json;
                      } catch {
                        console.warn(
                          `[KillTracker] Failed to parse data_json for ${equipment.itemKey}`
                        );
                        equipmentData = {};
                      }
                    }

                    const prevKills = equipmentData.totalKills || 0;
                    equipmentData.totalKills = prevKills + 1;

                    // Check for level-up notification
                    try {
                      const killsPerBonus =
                        killTrackerAbility.killsPerBonus || 500;
                      const maxBonus = killTrackerAbility.maxBonus || 35;

                      const oldBonus = Math.min(
                        Math.floor(prevKills / killsPerBonus),
                        maxBonus
                      );
                      const newBonus = Math.min(
                        Math.floor((prevKills + 1) / killsPerBonus),
                        maxBonus
                      );

                      if (newBonus > oldBonus && newBonus <= maxBonus) {
                        // Send level-up notification
                        const statKey = killTrackerAbility.stat || "DAMAGE";
                        const statConf = STATS[statKey] || {
                          name: statKey,
                          emoji: "",
                        };
                        const statEmoji = statConf.emoji || "";
                        const statNameReadable =
                          statConf.name ||
                          statKey
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (c) => c.toUpperCase());

                        const messageLines = [
                          `Your ${itemData.emoji || "⚔️"} **${itemData.name}**'s ${killTrackerAbility.name} leveled up!\n`,
                          "**New Stats**",
                          `${statEmoji} **${oldBonus} > ${newBonus} ${statNameReadable}** (${(prevKills + 1).toLocaleString()} total kills)`,
                        ];

                        try {
                          const embed = new EmbedBuilder()
                            .setColor(EMBED_COLORS.GOLD)
                            .setDescription(messageLines.join("\n"));

                          if (channel) {
                            const levelUpMsg = await channel.send({
                              embeds: [embed],
                            });

                            // delete after 20 seconds
                            setTimeout(async () => {
                              try {
                                await levelUpMsg.delete();
                              } catch {
                                // ignore deletion errors
                              }
                            }, 20000);
                          }
                        } catch (notifyErr) {
                          console.error(
                            "[KillTracker Level-Up] Error sending notification:",
                            notifyErr
                          );
                        }
                      }
                    } catch (killTrackerNotifyErr) {
                      console.error(
                        "[KillTracker Level-Up] Error processing notification:",
                        killTrackerNotifyErr
                      );
                    }

                    // Queue data_json update for persistence
                    const updatedDataJson = JSON.stringify(equipmentData);
                    killTrackerUpdates.push({
                      equipmentId: equipment.id,
                      dataJson: updatedDataJson,
                    });
                    equipment.data_json = updatedDataJson;
                  }
                }
              }

              // Update all equipment data atomically if there were changes
              if (killTrackerUpdates.length > 0) {
                await updateInventoryAtomically(
                  localCharacter.discordId,
                  0, // No coin change
                  [], // No items to change
                  [], // No equipment to add
                  [], // No equipment to remove
                  0, // No bank coins
                  killTrackerUpdates // Equipment data updates
                );
              }
            } catch (killTrackerError) {
              console.error(
                `[KillTracker] Error tracking total kills:`,
                killTrackerError
              );
            }
          }

          // Calculate rewards for defeating the mob
          rewards = await calculateRewardsFromMob(
            mobInstanceData,
            localCharacter
          );

          // Trigger onKill enchantment effects
          if (equippedWeapon && equippedWeapon.data_json) {
            const data =
              typeof equippedWeapon.data_json === "string"
                ? JSON.parse(equippedWeapon.data_json)
                : equippedWeapon.data_json;

            if (data && data.enchantments) {
              // Check for Vampirism enchantment
              if (data.enchantments.VAMPIRISM) {
                const vampirismLevel = data.enchantments.VAMPIRISM;

                if (ENCHANTMENTS.VAMPIRISM && ENCHANTMENTS.VAMPIRISM.onKill) {
                  const healingResult = ENCHANTMENTS.VAMPIRISM.onKill(
                    vampirismLevel,
                    localCharacter
                  );
                  if (healingResult && healingResult.healing > 0) {
                    const { applyVitalityHealing } = require("./healingUtils");
                    const finalHealing = applyVitalityHealing(
                      localCharacter,
                      healingResult.healing
                    );
                    characterHealth = Math.min(
                      characterTotalStats.HEALTH,
                      characterHealth + finalHealing
                    );
                    turnEvents.player.push(
                      `🩸 Vampirism healed you for ${finalHealing.toFixed(1)} health!`
                    );
                  }
                }
              }

              // Check for other onKill enchantments here if needed
            }
          }

          // Check for accessory healing effects (like Devour Ring)
          const playerAccessories = await getPlayerAccessories(
            localCharacter.discordId
          );
          const equippedAccessories = playerAccessories.filter(
            (acc) => acc.isEquipped
          );
          const allItems = configManager.getAllItems();

          for (const accessory of equippedAccessories) {
            const itemData = allItems[accessory.itemKey];
            if (
              itemData?.type === "ACCESSORY" &&
              itemData.effects?.devourHealing
            ) {
              const baseHealAmount = itemData.effects.devourHealing;
              const { applyVitalityHealing } = require("./healingUtils");
              const finalHealAmount = applyVitalityHealing(
                localCharacter,
                baseHealAmount
              );
              characterHealth = Math.min(
                characterTotalStats.HEALTH,
                characterHealth + finalHealAmount
              );
              /*         turnEvents.player.push(
              `🍽️ Devour Ring healed you for ${finalHealAmount} health!`
            );*/
            }
          }

          lastUpdateTime = await updateBattleEmbed("#00FF00");
          immediateUpdateOccurred = true; // Flag that we just did an immediate update

          // Rewards are now calculated before this block
          break;
        }

        // Rewards are now calculated before this block

        if (turn % mobAttackInterval === 0) {
          // Check for abort signal before mob attacks
          if (signal && signal.aborted) {
            return {
              victory: false,
              fled: true,
              finalCharacterState: localCharacter,
              rewards: null,
              lastUpdateTime: lastUpdateTime,
            };
          }

          const baseDodge = 0.2; // The original static dodge chance
          const playerDodgeStat = (characterTotalStats.DODGE_CHANCE || 0) / 100; // Convert stat (e.g., 10) to percentage (0.10)
          const totalDodgeChance = Math.min(0.75, baseDodge + playerDodgeStat); // Additive, capped at 75%

          if (Math.random() < totalDodgeChance) {
            turnEvents.mob.push(`Attacks! But you dodged!`);
          } else {
            // Calculate effective defense including Bulwark bonus against target mobs
            let effectiveDefense = characterStats.DEFENSE;
            if (
              mobInstanceData.mobType === "Undead" &&
              localCharacter?.inventory?.equipment
            ) {
              let bulwarkBonus = 0;
              const allItems = configManager.getAllItems();

              // Check each equipped armor piece for Bulwark ability
              for (const equipment of localCharacter.inventory.equipment) {
                if (equipment.isEquipped) {
                  const itemData = allItems[equipment.itemKey];

                  // Check if this is armor with Bulwark ability targeting undead
                  if (
                    itemData?.type === "ARMOR" &&
                    (itemData.ability?.type === "BULWARK" ||
                      itemData.ability?.type === "ZOMBIE_BULWARK" ||
                      Object.values(itemData.abilities || {}).some(
                        (ability) =>
                          ability.type === "BULWARK" ||
                          ability.type === "ZOMBIE_BULWARK"
                      ))
                  ) {
                    // Parse existing data_json to get kill count
                    let equipmentData = {};
                    if (equipment.data_json) {
                      try {
                        equipmentData =
                          typeof equipment.data_json === "string"
                            ? JSON.parse(equipment.data_json)
                            : equipment.data_json;
                      } catch {
                        equipmentData = {};
                      }
                    }

                    const ability =
                      itemData.ability?.type === "BULWARK" ||
                      itemData.ability?.type === "ZOMBIE_BULWARK"
                        ? itemData.ability
                        : Object.values(itemData.abilities || {}).find(
                            (ability) =>
                              ability.type === "BULWARK" ||
                              ability.type === "ZOMBIE_BULWARK"
                          );

                    const mobKills = (() => {
                      const targetMobType = ability?.targetMobType || "Undead";
                      const killPropertyName =
                        targetMobType === "Undead"
                          ? "zombieKills"
                          : "bulwarkKills";
                      return equipmentData[killPropertyName] || 0;
                    })();

                    if (ability && ability.thresholds) {
                      // Find the highest threshold met
                      let pieceBonus = 0;
                      for (const threshold of ability.thresholds) {
                        if (mobKills >= threshold.kills) {
                          pieceBonus = threshold.bonus;
                        } else {
                          break;
                        }
                      }
                      bulwarkBonus += pieceBonus;
                    }
                  }
                }
              }

              effectiveDefense += bulwarkBonus;
            }

            let { damage: damageTaken } = calculateDamageTaken(
              mobStats.damage,
              characterStats.HEALTH,
              effectiveDefense
            );
            if (anglerReduction > 0) {
              const reducedDmg = damageTaken * (1 - anglerReduction);
              turnEvents.mob.push(
                `Angler Armor reduced damage by ${(
                  anglerReduction * 100
                ).toFixed(0)}%.`
              );
              damageTaken = reducedDmg;
            }

            // Calculate damage reduction from accessories for specific mob types
            const accessoryDamageReduction =
              await calculateAccessoryDamageReduction(
                localCharacter,
                mobInstanceData,
                isSeaCreature
              );

            if (accessoryDamageReduction > 0) {
              damageTaken = damageTaken * (1 - accessoryDamageReduction);
            }

            characterHealth = Math.max(0, characterHealth - damageTaken);

            if (damageTaken > 0) {
              turnEvents.mob.push(
                `Attacks! You take ${Math.round(damageTaken)} damage`
              );
            } else {
              turnEvents.mob.push(`Attacks! But it deals no damage`);
            }

            if (characterHealth <= 0) {
              victory = false;
              turnEvents.mob.push("You have been defeated!");
              lastUpdateTime = await updateBattleEmbed("#FF0000");
              immediateUpdateOccurred = true; // Flag that we just did an immediate update
              break;
            }
          }
        } else {
          turnEvents.mob.push(`Prepares to attack...`);
        }

        // Process mob abilities (every turn, abilities handle their own timing)
        try {
          const abilityEffects = await processMobAbilities(
            mobInstanceData,
            localCharacter,
            turn,
            turnEvents
          );

          if (abilityEffects.messages.length > 0) {
            // Add ability messages to turn events
            abilityEffects.messages.forEach((message) => {
              turnEvents.mob.push(message);
            });
          }

          // Apply ability damage to player
          if (abilityEffects.damageDealt > 0) {
            characterHealth = Math.max(
              0,
              characterHealth - abilityEffects.damageDealt
            );

            if (characterHealth <= 0) {
              victory = false;
              turnEvents.mob.push(
                "You have been defeated by the mob's ability!"
              );
              lastUpdateTime = await updateBattleEmbed("#FF0000");
              immediateUpdateOccurred = true;
              break;
            }
          }

          // Apply ability healing to mob
          if (abilityEffects.healingDone > 0) {
            mobHealth = Math.min(
              mobStats.health,
              mobHealth + abilityEffects.healingDone
            );
          }
        } catch (abilityError) {
          console.error(
            `[Combat] Error processing mob abilities for ${mobName}:`,
            abilityError
          );
          // Continue combat even if abilities fail
        }

        // Check for abort signal before turn ends
        if (signal && signal.aborted) {
          return {
            victory: false,
            fled: true,
            finalCharacterState: localCharacter,
            rewards: null,
            lastUpdateTime: lastUpdateTime,
          };
        }

        turn++;
      }

      // Update after processing all turns in this cycle (only if no immediate update occurred)
      if (!immediateUpdateOccurred) {
        lastUpdateTime = await updateBattleEmbed();
      }
    }
  } catch (error) {
    console.error("Error during turn-based fight:", error);
    victory = false;
    characterHealth = 0;
    battleLog.mob.push("An error occurred in the battle!");
    lastUpdateTime = await updateBattleEmbed("#FF0000");
  }

  // Return the original character with only the health updated to prevent object corruption
  const finalCharacterState = { ...character };
  finalCharacterState.current_health = characterHealth;

  // Preserve equipment data from localCharacter to maintain kill tracking updates
  // Clean up standalone battle message created for sea creature sub-combat to avoid clutter
  if (
    isSeaCreatureSubCombat &&
    createdStandaloneBattleMessage &&
    battleMessage
  ) {
    // Delete after a short delay so users can see the outcome
    setTimeout(async () => {
      try {
        await battleMessage.delete();
      } catch {
        // ignore deletion errors
      }
    }, 3000);
  }

  if (localCharacter.inventory && localCharacter.inventory.equipment) {
    finalCharacterState.inventory = { ...finalCharacterState.inventory };
    finalCharacterState.inventory.equipment =
      localCharacter.inventory.equipment;
  }

  // Save health immediately after each fight for real-time updates (both regular combat and sea creature combat)
  if (characterHealth !== character.current_health) {
    try {
      const { savePlayerData } = require("./playerDataManager.js");
      // Only save if health actually changed to avoid "No valid fields to update" warnings
      if (Math.abs(characterHealth - character.current_health) > 0.001) {
        await savePlayerData(
          character.discordId,
          { current_health: characterHealth },
          ["current_health"]
        );
      }
    } catch (healthSaveError) {
      console.error(
        `[Combat] Failed to save health after ${
          isSeaCreatureSubCombat ? "sea creature" : "regular"
        } combat:`,
        healthSaveError
      );
    }
  }

  return {
    victory,
    finalCharacterState,
    fled: false,
    rewards,
    combatWisdomBonus: totalCombatWisdomBonus,
    lastUpdateTime: lastUpdateTime,
  };
}

async function calculateRewardsFromMob(mobInstanceData, character = null) {
  // Calculate coins using the pre-calculated range from the instance

  // Initialize coins from the mob instance data
  let coins = mobInstanceData.coins ?? mobInstanceData.baseExp?.coins ?? 0;

  // Handle baseCoins for Sea Creatures (with min/max range)
  if (
    mobInstanceData.baseCoins &&
    typeof mobInstanceData.baseCoins === "object"
  ) {
    const { min, max } = mobInstanceData.baseCoins;
    coins = Math.floor(Math.random() * (max - min + 1)) + min;
  }
  const allItems = configManager.getAllItems();

  // Calculate character stats to get Magic Find
  const characterTotalStats = character ? calculateAllStats(character) : {};

  // Get equipped weapon to check for enchantments
  const equippedWeapon = character?.inventory?.equipment?.find((eq) => {
    if (!eq.isEquipped) return false;
    const itemDef = allItems[eq.itemKey];
    return (
      itemDef &&
      (itemDef.type === "WEAPON" || itemDef.type?.name === "WEAPON") &&
      !(itemDef.type === "TOOL" && itemDef.subtype === "AXE")
    );
  });

  // Parse weapon enchantments
  const weaponEnchantments = equippedWeapon?.data_json
    ? (typeof equippedWeapon.data_json === "string"
        ? JSON.parse(equippedWeapon.data_json)
        : equippedWeapon.data_json
      )?.enchantments || {}
    : {};

  const lootingLevel = weaponEnchantments.LOOTING || 0;
  const scavengerLevel = weaponEnchantments.SCAVENGER || 0;

  // Calculate Scavenger bonus coins
  let extraScavengerCoins = 0;
  if (scavengerLevel > 0) {
    const mobLevel = mobInstanceData.level || 1;
    const coinsPerMobLevel =
      ENCHANTMENTS.SCAVENGER.coinsPerMobLevel?.[scavengerLevel] || 0;
    extraScavengerCoins = coinsPerMobLevel * mobLevel;
  }

  // --- Scavenger Talisman Logic ---
  let extraScavengerTalismanCoins = 0;
  if (character && character.discordId) {
    const playerAccessories = await getPlayerAccessories(character.discordId);
    const equippedAccessories = playerAccessories.filter(
      (acc) => acc.isEquipped
    );

    // Load item definitions
    const allItems = require("./configManager.js").getAllItems();

    for (const accessory of equippedAccessories) {
      const itemData = allItems[accessory.itemKey];

      if (
        itemData &&
        itemData.type === "ACCESSORY" &&
        itemData.effects?.scavengerBonus
      ) {
        const mobLevel = mobInstanceData.level || 1;
        const coinsPerMobLevel = itemData.effects.scavengerBonus;
        extraScavengerTalismanCoins += coinsPerMobLevel * mobLevel;
        // Scavenger talisman bonus applied
      }
    }
  }
  // --- End Scavenger Talisman Logic ---

  // Animal axe bonus calculation
  const equippedAxe = character?.inventory?.equipment?.find((eq) => {
    if (!eq.isEquipped) return false;
    const itemDef = allItems[eq.itemKey];
    return itemDef?.type === "TOOL" && itemDef?.subtype === "AXE";
  });

  let animalAxeBonus = 0;
  if (equippedAxe) {
    const mobKey = (
      mobInstanceData.mobKey ||
      mobInstanceData.key ||
      ""
    ).toLowerCase();
    const animalAxeMap = {
      SHEEP_AXE: "sheep",
      CHICKEN_AXE: "chicken",
      COW_AXE: "cow",
      PIG_AXE: "pig",
      RABBIT_AXE: "rabbit",
    };

    if (
      animalAxeMap[equippedAxe.itemKey] === mobKey ||
      (equippedAxe.itemKey === "COW_AXE" && mobKey === "mushroom_cow")
    ) {
      animalAxeBonus = 2;
    }
  }

  // Process flat item drops based on chance (using instance loot data) - UNIFIED APPROACH
  const droppedItems = []; // Single array for all dropped items

  if (mobInstanceData.loot && Array.isArray(mobInstanceData.loot.drops)) {
    for (const dropRule of mobInstanceData.loot.drops) {
      const itemDef = allItems[dropRule.itemKey];
      const isCoins = dropRule.itemKey === "COINS";

      if (!isCoins) {
        // UNIFIED DROP PROCESSING - All items use the same drop logic
        const baseChance = dropRule.chance || 0;
        let effectiveChance = baseChance;
        let amount = 0;

        // Determine if Looting applies (only to non-unique items)
        const isUniqueItem =
          itemDef?.type === "ARMOR" ||
          itemDef?.type === "PET" ||
          itemDef?.type === "ACCESSORY";
        const lootingApplies = !isUniqueItem;

        if (baseChance === 1.0) {
          // Guaranteed drop
          amount =
            typeof dropRule.amount === "number"
              ? dropRule.amount
              : dropRule.amount?.min || 1;

          // Looting adds extra quantity chance for non-unique items
          if (lootingApplies && lootingLevel > 0) {
            const extraChancePerLevel = 0.15;
            const totalExtraChance = lootingLevel * extraChancePerLevel;
            if (Math.random() < totalExtraChance) {
              amount +=
                typeof dropRule.amount === "number"
                  ? dropRule.amount
                  : dropRule.amount?.min || 1;
            }
          }
        } else {
          // Non-guaranteed drop - apply chance
          if (lootingApplies && lootingLevel > 0) {
            effectiveChance = Math.min(
              baseChance * (1 + lootingLevel * 0.15),
              1.0
            );
          }

          // Apply Magic Find bonus only to drops with 1% base chance or lower
          if (baseChance <= 0.01) {
            const magicFindStat = characterTotalStats.MAGIC_FIND || 0;
            effectiveChance = effectiveChance * (1 + magicFindStat / 100);
          }

          if (Math.random() < effectiveChance) {
            if (typeof dropRule.amount === "number") {
              amount = dropRule.amount;
            } else if (
              typeof dropRule.amount === "object" &&
              dropRule.amount.min !== undefined &&
              dropRule.amount.max !== undefined
            ) {
              amount =
                Math.floor(
                  Math.random() *
                    (dropRule.amount.max - dropRule.amount.min + 1)
                ) + dropRule.amount.min;
            }
          }
        }

        // Apply animal axe bonus to non-unique items
        if (!isUniqueItem && animalAxeBonus > 0) {
          const isCommonLoot = baseChance >= 0.8;
          const isChickenAxeEggDrop =
            equippedAxe &&
            equippedAxe.itemKey === "CHICKEN_AXE" &&
            dropRule.itemKey === "EGG";
          if (isCommonLoot || isChickenAxeEggDrop) {
            amount += animalAxeBonus;
          }
        }

        // Add to unified drops array if item dropped
        if (amount > 0 && dropRule.itemKey) {
          droppedItems.push({
            itemKey: dropRule.itemKey,
            amount: amount,
            itemType: itemDef?.type || "REGULAR",
          });
        }
      } else if (isCoins) {
        // Coin drops (unchanged)
        coins +=
          typeof dropRule.amount === "number"
            ? dropRule.amount
            : dropRule.amount?.min || 0;
      }
      // Other unique items (PET) are still ignored by Looting effect but could be added here if needed
    }
  }

  // Process new loot table system (individual item chances with mutual exclusivity)
  if (mobInstanceData.loot && mobInstanceData.loot.lootTables) {
    for (const [, lootTable] of Object.entries(
      mobInstanceData.loot.lootTables
    )) {
      if (!lootTable.items || !Array.isArray(lootTable.items)) continue;

      const eligibleItems = [];

      // Roll for each item individually and collect eligible items
      for (const item of lootTable.items) {
        const itemDef = allItems[item.itemKey];
        const isUniqueItem =
          itemDef?.type === "ARMOR" ||
          itemDef?.type === "PET" ||
          itemDef?.type === "ACCESSORY";
        const isCoins = item.itemKey === "COINS";

        const baseChance = item.chance || 0;
        let effectiveChance = baseChance;

        // Apply Looting bonus to non-unique, non-coin items
        if (!isUniqueItem && !isCoins && lootingLevel > 0) {
          effectiveChance = baseChance * (1 + lootingLevel * 0.15);
          effectiveChance = Math.min(effectiveChance, 1.0);
        }

        // Apply Magic Find bonus only to drops with 1% base chance or lower
        if (baseChance <= 0.01) {
          const magicFindStat = characterTotalStats.MAGIC_FIND || 0;
          effectiveChance = effectiveChance * (1 + magicFindStat / 100);
        }

        // Roll for this item
        if (Math.random() < effectiveChance) {
          eligibleItems.push({
            ...item,
            effectiveChance: effectiveChance,
            isUniqueItem: isUniqueItem,
            isCoins: isCoins,
          });
        }
      }

      // If multiple items are eligible, pick the rarest one (lowest chance)
      // This preserves the intended drop rates for rare items
      if (eligibleItems.length > 0) {
        const selectedItem = eligibleItems.reduce((rarest, current) =>
          current.chance < rarest.chance ? current : rarest
        );

        // Calculate amount
        let amount = 1;
        if (typeof selectedItem.amount === "number") {
          amount = selectedItem.amount;
        } else if (
          typeof selectedItem.amount === "object" &&
          selectedItem.amount.min !== undefined &&
          selectedItem.amount.max !== undefined
        ) {
          amount =
            Math.floor(
              Math.random() *
                (selectedItem.amount.max - selectedItem.amount.min + 1)
            ) + selectedItem.amount.min;
        }

        // Apply animal axe bonus to common loot in loot tables
        if (
          !selectedItem.isUniqueItem &&
          !selectedItem.isCoins &&
          animalAxeBonus > 0
        ) {
          const baseChance = selectedItem.chance || 0;
          const isCommonLoot = baseChance >= 0.8;
          const isChickenAxeEggDrop =
            equippedAxe &&
            equippedAxe.itemKey === "CHICKEN_AXE" &&
            selectedItem.itemKey === "EGG";

          if (isCommonLoot || isChickenAxeEggDrop) {
            amount += animalAxeBonus;
          }
        }

        // Handle the selected item - UNIFIED APPROACH
        if (selectedItem.isCoins) {
          coins += amount;
        } else if (selectedItem.itemKey) {
          // Add to unified drops array
          const itemDef = allItems[selectedItem.itemKey];
          droppedItems.push({
            itemKey: selectedItem.itemKey,
            amount: amount,
            itemType: itemDef?.type || "REGULAR",
          });
        }
      }
    }
  }

  // Process legacy drop groups (only one item per group can drop) - for backward compatibility
  if (mobInstanceData.loot && Array.isArray(mobInstanceData.loot.dropGroups)) {
    for (const dropGroup of mobInstanceData.loot.dropGroups) {
      // Check if any item in the group is unique or coins
      const hasUniqueOrCoinItem = dropGroup.items.some((item) => {
        const itemDef = allItems[item.itemKey];
        return (
          itemDef?.type === "ARMOR" ||
          itemDef?.type === "PET" ||
          item.itemKey === "COINS"
        );
      });

      // For all drop groups, calculate chance and roll for drops
      const baseGroupChance = dropGroup.chance || 0;
      let effectiveGroupChance = baseGroupChance;

      // Only apply Looting bonus to non-unique, non-coin item groups
      if (!hasUniqueOrCoinItem && lootingLevel > 0) {
        effectiveGroupChance = baseGroupChance * (1 + lootingLevel * 0.15); // Multiplicative bonus
        // Ensure chance doesn't exceed 1.0 (100%)
        effectiveGroupChance = Math.min(effectiveGroupChance, 1.0);
      }

      // Apply Magic Find bonus only to drop groups with < 1% base chance
      if (baseGroupChance < 0.01) {
        const magicFindStat = characterTotalStats.MAGIC_FIND || 0;
        effectiveGroupChance = effectiveGroupChance * (1 + magicFindStat / 100);
      }

      if (Math.random() < effectiveGroupChance) {
        // Check if the group drops
        // Calculate total weight
        const totalWeight = dropGroup.items.reduce(
          (sum, item) => sum + (item.weight || 1),
          0
        );

        // Roll for which item in the group drops
        const roll = Math.random() * totalWeight;
        let currentWeight = 0;

        for (const item of dropGroup.items) {
          currentWeight += item.weight || 1;
          if (roll <= currentWeight) {
            // This item was selected
            let amount = item.amount || 1;

            // Apply animal axe bonus to common loot in drop groups
            if (animalAxeBonus > 0 && item.itemKey !== "COINS") {
              const itemDef = allItems[item.itemKey];
              const isUniqueItem =
                itemDef?.type === "ARMOR" ||
                itemDef?.type === "PET" ||
                itemDef?.type === "ACCESSORY";

              if (!isUniqueItem) {
                const baseChance = baseGroupChance || 0;
                const isCommonLoot = baseChance >= 0.8;
                const isChickenAxeEggDrop =
                  equippedAxe &&
                  equippedAxe.itemKey === "CHICKEN_AXE" &&
                  item.itemKey === "EGG";

                if (isCommonLoot || isChickenAxeEggDrop) {
                  amount += animalAxeBonus;
                }
              }
            }

            if (amount > 0 && item.itemKey) {
              // Add to unified drops array - UNIFIED APPROACH
              const itemDef = allItems[item.itemKey];
              droppedItems.push({
                itemKey: item.itemKey,
                amount: amount,
                itemType: itemDef?.type || "REGULAR",
              });
            }
            break; // Only one item per group
          }
        }
      }
      // Note: Unique items (like armor) use their base drop chance, unaffected by Looting
    }
  }

  // Add extra coins from Scavenger
  coins += extraScavengerCoins;

  // Add extra coins from Scavenger Talisman
  coins += extraScavengerTalismanCoins;

  // Calculate base experience (Combat Wisdom will be applied at the end of the action)
  const baseExp =
    mobInstanceData.baseExp?.amount ?? mobInstanceData.baseExp?.combat ?? 0;

  // Convert droppedItems array to items object for compatibility with combat system
  const items = {};
  for (const drop of droppedItems) {
    if (drop.itemKey && drop.amount > 0) {
      items[drop.itemKey] = (items[drop.itemKey] || 0) + drop.amount;
    }
  }

  return {
    exp: baseExp,
    coins: coins,
    items: items, // Convert to object format expected by combat system
    droppedItems: droppedItems, // Keep array format for other systems that might need it
  };
}

// --- Combat Engine (Shared) ---
// This engine provides shared combat mechanics for both Combat and Fishing systems

// --- Combat Engine Exports ---
module.exports = {
  runTurnBasedFight,
  calculateRewardsFromMob,
  calculateDamage,
  calculateDamageTaken,
  createCustomHealthBar,
};
