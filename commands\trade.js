const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
} = require("discord.js");
const { dbGet, dbRunQueued } = require("../utils/dbUtils");
const {
  getFilteredPlayers,
  getPlayerByUsername,
  getAllPlayersWithUsernames,
} = require("../utils/playerCache");
const { getPlayerData } = require("../utils/playerDataManager");
const { getCurrentActivity } = require("../utils/activityManager");
const configManager = require("../utils/configManager");
const { ITEM_RARITY, EMBED_COLORS } = require("../gameConfig");
const ITEMS = configManager.getAllItems();
const { getPetLevel } = require("../utils/petUtils");
const { parseShorthandAmount } = require("../utils/formatUtils");

function createTradeEmbed(
  playerName,
  playerOffer,
  isCurrentPlayer = false,
  isReady = false
) {
  // Add ready status indicator to the title
  const readyIndicator = isReady ? "✅" : "❌";

  const embed = new EmbedBuilder()
    .setColor(isCurrentPlayer ? 0x00ae86 : 0x3498db)
    .setTitle(`${readyIndicator} ${playerName}`)
    .setDescription(
      playerOffer.length > 0
        ? playerOffer
            .map((item) => {
              if (item.type === "coins") {
                return `<:purse_coins:1367849116033482772> **${item.amount.toLocaleString()} coins**`;
              } else if (item.type === "gear") {
                const equipmentIdPrefix = item.equipmentId
                  ? `\`${item.equipmentId.toString().slice(0, 4)}\``
                  : "";
                return `${item.emoji} **${item.amount}x ${item.name}** ${equipmentIdPrefix}`;
              } else if (item.type === "pet") {
                return `${item.emoji} **${item.name} (Lvl ${item.level}) [${item.rarity}]** \`${item.id.toString().slice(0, 4)}\``;
              } else {
                return `${item.emoji} **${item.amount}x ${item.name}**`;
              }
            })
            .join("\n")
        : "No items offered yet"
    );

  return embed;
}

function createTradeButtons(
  playerId,
  isReady = false,
  itemPage = 0,
  gearPage = 0,
  petPage = 0
) {
  const row1 = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=items;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel("Items")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("📦"),
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=gear;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel("Gear")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("<:Iron_Helmet:1376108901203968093>"),
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=pets;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel("Pets")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("🐾"),
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=coins;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel("Coins")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("<:purse_coins:1367849116033482772>"),
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=clear;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel("Clear Offer")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("🗑️")
  );

  const row2 = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=ready;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel(isReady ? "Not Ready" : "Ready")
      .setStyle(isReady ? ButtonStyle.Danger : ButtonStyle.Success)
      .setEmoji(isReady ? "❌" : "✅"),
    new ButtonBuilder()
      .setCustomId(
        `trade_toggle:action=cancel;player=${playerId};itemPage=${itemPage};gearPage=${gearPage};petPage=${petPage}`
      )
      .setLabel("Cancel")
      .setStyle(ButtonStyle.Danger)
      .setEmoji("🚫")
  );

  return [row1, row2];
}

const activeTrades = new Map();

module.exports = {
  data: new SlashCommandBuilder()
    .setName("trade")
    .setDescription("Start a trade with another player")
    .addStringOption((option) =>
      option
        .setName("player")
        .setDescription("Name of the player you want to trade with")
        .setRequired(true)
        .setAutocomplete(true)
    ),

  async execute(interaction) {
    const userId = interaction.user.id;
    const targetUsername = interaction.options.getString("player");

    try {
      await interaction.deferReply();

      const initiatorActionType = await getCurrentActivity(userId);
      if (initiatorActionType) {
        const busyEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Cannot Start Trade")
          .setDescription(
            `You cannot start a trade while performing an action (${initiatorActionType}). Please finish your current action first.`
          );
        return interaction.editReply({ embeds: [busyEmbed] });
      }

      const targetPlayer = getPlayerByUsername(targetUsername);
      if (!targetPlayer) {
        const notFoundEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Player Not Found")
          .setDescription(
            `Could not find a character named '${targetUsername}'.`
          );
        return interaction.editReply({ embeds: [notFoundEmbed] });
      }

      const targetUserId = targetPlayer.discord_id;
      const targetDisplayName = targetPlayer.name;

      if (userId === targetUserId) {
        const selfTradeEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Cannot Trade With Yourself")
          .setDescription("You cannot trade with yourself!");
        return interaction.editReply({ embeds: [selfTradeEmbed] });
      }

      if (activeTrades.has(userId) || activeTrades.has(targetUserId)) {
        const alreadyTradingEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Trade Unavailable")
          .setDescription("One of the players is already in an active trade!");
        return interaction.editReply({ embeds: [alreadyTradingEmbed] });
      }

      // Check if the target player is currently in an action (before sending request)
      const targetActionType = await getCurrentActivity(targetUserId);
      if (targetActionType) {
        const targetBusyEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Player Busy")
          .setDescription(
            `That player is busy doing ${targetActionType}. Please try again later.`
          );
        return interaction.editReply({ embeds: [targetBusyEmbed] });
      }

      // Get both players' data
      const player1Data = await getPlayerData(userId);
      const player2Data = await getPlayerData(targetUserId);

      if (!player1Data || !player2Data) {
        const dataErrorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Player Data Error")
          .setDescription("Error loading player data.");
        return interaction.editReply({ embeds: [dataErrorEmbed] });
      }

      // Get target player's last active channel
      const channelRow = await dbGet(
        "SELECT channel_id FROM player_last_active_channel WHERE discord_id = ?",
        [targetUserId]
      );
      if (!channelRow || !channelRow.channel_id) {
        const noChannelEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Cannot Trade")
          .setDescription(
            `${targetDisplayName} hasn't used any commands recently. Cannot send trade request.`
          );
        return interaction.editReply({ embeds: [noChannelEmbed] });
      }

      const targetChannelId = channelRow.channel_id;
      const targetChannel = await interaction.client.channels
        .fetch(targetChannelId)
        .catch(() => null);
      if (!targetChannel) {
        const channelErrorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Cannot Trade")
          .setDescription(
            `Cannot access ${targetDisplayName}'s last active channel.`
          );
        return interaction.editReply({ embeds: [channelErrorEmbed] });
      }

      // Prevent trading if target player is in the same channel as initiator
      if (targetChannelId === interaction.channel.id) {
        const sameChannelEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Cannot Trade In Same Channel")
          .setDescription(
            `You cannot trade with ${targetDisplayName} because they are in the same channel as you. Please ask them to move to a different channel.`
          );
        return interaction.editReply({ embeds: [sameChannelEmbed] });
      }

      // Create trade request embed for target player
      const tradeRequestEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.TEAL)
        .setTitle("📦 Trade Request")
        .setDescription(`**${player1Data.name}** wants to trade with you!`)
        .setFooter({ text: "This trade request will expire in 30 seconds." });

      const acceptButton = new ButtonBuilder()
        .setCustomId(`trade_accept_${userId}_${targetUserId}_${Date.now()}`)
        .setLabel("Accept Trade")
        .setStyle(ButtonStyle.Success)
        .setEmoji("✅");

      const declineButton = new ButtonBuilder()
        .setCustomId(`trade_decline_${userId}_${targetUserId}_${Date.now()}`)
        .setLabel("Decline Trade")
        .setStyle(ButtonStyle.Danger)
        .setEmoji("❌");

      const requestActionRow = new ActionRowBuilder().addComponents(
        acceptButton,
        declineButton
      );

      // Send trade request to target player
      const tradeMessage = await targetChannel.send({
        content: `<@${targetUserId}>`,
        embeds: [tradeRequestEmbed],
        components: [requestActionRow],
      });

      // Confirm to initiating player
      const confirmEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.TEAL)
        .setTitle("📤 Trade Request Sent")
        .setDescription(
          `Trade request sent to **${targetDisplayName}**! They have 30 seconds to accept.`
        );

      await interaction.editReply({ embeds: [confirmEmbed] });

      // Set up collector for the accept/decline buttons
      const collector = tradeMessage.createMessageComponentCollector({
        filter: (i) =>
          (i.customId.includes("trade_accept_") ||
            i.customId.includes("trade_decline_")) &&
          i.user.id === targetUserId,
        time: 30000, // 30 seconds
        max: 1,
      });

      collector.on("collect", async (buttonInteraction) => {
        await buttonInteraction.deferUpdate();

        if (buttonInteraction.customId.includes("trade_decline_")) {
          // Trade declined
          const declineEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Trade Declined")
            .setDescription(
              `Trade request from **${player1Data.name}** was declined.`
            );

          await tradeMessage.edit({ embeds: [declineEmbed], components: [] });

          const initiatorDeclineEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Trade Declined")
            .setDescription(
              `**${targetDisplayName}** declined your trade request.`
            );

          await interaction.editReply({ embeds: [initiatorDeclineEmbed] });
          return;
        }

        // Check if the target player is currently in an action before accepting
        const targetActionType = await getCurrentActivity(targetUserId);
        if (targetActionType) {
          const actionBlockEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Cannot Accept Trade")
            .setDescription(
              `You cannot accept a trade while performing an action (${targetActionType}). Please finish your current action first.`
            );

          await tradeMessage.edit({
            embeds: [actionBlockEmbed],
            components: [],
          });

          const initiatorActionBlockEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Trade Cannot Be Accepted")
            .setDescription(
              `**${targetDisplayName}** cannot accept the trade because they are currently performing an action (${targetActionType}).`
            );

          await interaction.editReply({ embeds: [initiatorActionBlockEmbed] });
          return;
        }

        // Trade accepted - start interactive trading session
        await startInteractiveTrade(
          interaction,
          tradeMessage,
          userId,
          targetUserId,
          player1Data,
          player2Data
        );
      });

      collector.on("end", async (collected, reason) => {
        if (reason === "time" && collected.size === 0) {
          // Timeout
          const timeoutEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("⏰ Trade Request Expired")
            .setDescription(
              `Trade request from **${player1Data.name}** has expired.`
            );

          await tradeMessage.edit({ embeds: [timeoutEmbed], components: [] });

          const timeoutInitiatorEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("⏰ Trade Timed Out")
            .setDescription(
              `Trade request to **${targetDisplayName}** timed out.`
            );

          await interaction.editReply({ embeds: [timeoutInitiatorEmbed] });
        }
      });
    } catch (error) {
      console.error("Error in trade command:", error);
      await interaction.editReply({
        content: "An error occurred while processing the trade request.",
      });
    }
  },

  async autocomplete(interaction) {
    const focusedOption = interaction.options.getFocused(true);

    if (focusedOption.name === "player") {
      const focusedValue = focusedOption.value.toLowerCase();

      try {
        // Ensure the player cache is loaded
        await getAllPlayersWithUsernames();
        const choices = getFilteredPlayers(focusedValue, 25);
        await interaction.respond(choices);
      } catch (error) {
        console.error("Error in trade player autocomplete:", error);
        await interaction.respond([]);
      }
    }
  },
};

// Function to start the interactive trading session
async function startInteractiveTrade(
  originalInteraction,
  tradeMessage,
  player1Id,
  player2Id,
  player1Data,
  player2Data
) {
  // Initialize trade state
  const tradeId = `${player1Id}_${player2Id}_${Date.now()}`;
  const tradeState = {
    player1: {
      id: player1Id,
      name: player1Data.name,
      offer: [],
      ready: false,
    },
    player2: {
      id: player2Id,
      name: player2Data.name,
      offer: [],
      ready: false,
    },
    active: true,
  };

  activeTrades.set(player1Id, tradeId);
  activeTrades.set(player2Id, tradeId);

  // Add pagination state to trade state
  tradeState.pagination = {
    player1: { itemPage: 0, gearPage: 0, petPage: 0 },
    player2: { itemPage: 0, gearPage: 0, petPage: 0 },
  };

  // Create initial trade interface for Player 1 (original interaction)
  // Player 1 sees Player 2's offer on top, their own offer on the bottom
  const p1_otherPlayerEmbed = createTradeEmbed(
    tradeState.player2.name,
    tradeState.player2.offer,
    false,
    tradeState.player2.ready
  ); // Player 2's embed from P1's perspective
  const p1_currentPlayerEmbed = createTradeEmbed(
    tradeState.player1.name,
    tradeState.player1.offer,
    true,
    tradeState.player1.ready
  ); // Player 1's embed from P1's perspective
  const player1Buttons = createTradeButtons(
    tradeState.player1.id,
    tradeState.player1.ready,
    tradeState.pagination.player1.itemPage,
    tradeState.pagination.player1.gearPage,
    tradeState.pagination.player1.petPage
  );
  const player1Message = await originalInteraction.editReply({
    embeds: [p1_otherPlayerEmbed, p1_currentPlayerEmbed],
    components: player1Buttons,
  });

  // Create initial trade interface for Player 2 (tradeMessage)
  // Player 2 sees Player 1's offer on top, their own offer on the bottom
  const p2_otherPlayerEmbed = createTradeEmbed(
    tradeState.player1.name,
    tradeState.player1.offer,
    false,
    tradeState.player1.ready
  ); // Player 1's embed from P2's perspective
  const p2_currentPlayerEmbed = createTradeEmbed(
    tradeState.player2.name,
    tradeState.player2.offer,
    true,
    tradeState.player2.ready
  ); // Player 2's embed from P2's perspective
  const player2Buttons = createTradeButtons(
    tradeState.player2.id,
    tradeState.player2.ready,
    tradeState.pagination.player2.itemPage,
    tradeState.pagination.player2.gearPage,
    tradeState.pagination.player2.petPage
  );
  const player2Message = await tradeMessage.edit({
    content: "", // remove @ mention during trade
    embeds: [p2_otherPlayerEmbed, p2_currentPlayerEmbed],
    components: player2Buttons,
  });

  // Set up collectors for both messages
  // player1Message is in player1's channel, tradeMessage is in player2's channel
  setupTradeCollectors(
    originalInteraction,
    player1Message,
    player2Message,
    tradeState,
    tradeId
  );
}

// Function to set up collectors for trade interactions
// player1Message = message in player1's channel (originalInteraction.editReply)
// player2Message = message in player2's channel (tradeMessage)
function setupTradeCollectors(
  originalInteraction,
  player1Message,
  player2Message,
  tradeState,
  tradeId
) {
  // Filter for trade interactions available if needed

  // Collector for player1 message
  const collector1 = player1Message.createMessageComponentCollector({
    filter: (i) => {
      // Only allow player1 to interact with their own buttons
      if (i.user.id !== tradeState.player1.id) return false;
      // Must be a trade-related button
      if (!i.customId.startsWith("trade_toggle:")) return false;
      // Verify the player parameter in customId matches this player
      const playerMatch = i.customId.match(/player=([^;]+)/);
      return playerMatch && playerMatch[1] === tradeState.player1.id;
    },
    time: 300000, // 5 minutes
  });

  // Collector for player2 message
  const collector2 = player2Message.createMessageComponentCollector({
    filter: (i) => {
      // Only allow player2 to interact with their own buttons
      if (i.user.id !== tradeState.player2.id) return false;
      // Must be a trade-related button
      if (!i.customId.startsWith("trade_toggle:")) return false;
      // Verify the player parameter in customId matches this player
      const playerMatch = i.customId.match(/player=([^;]+)/);
      return playerMatch && playerMatch[1] === tradeState.player2.id;
    },
    time: 300000, // 5 minutes
  });

  // Handle interactions for both collectors
  // In setupTradeCollectors, always use the original player1Message/player2Message for updateTradeDisplay
  [collector1, collector2].forEach((collector, index) => {
    const isPlayer1 = index === 0;
    const currentPlayer = isPlayer1 ? tradeState.player1 : tradeState.player2;
    const otherPlayer = isPlayer1 ? tradeState.player2 : tradeState.player1;
    // Message references available for handler functions
    const playerKey = isPlayer1 ? "player1" : "player2";

    console.log(
      `[TRADE DEBUG] Setting up collector ${index} for ${playerKey} (${currentPlayer.id})`
    );
    console.log(
      `[TRADE DEBUG] Collector ${index} will handle message for player ${currentPlayer.id}`
    );

    collector.on("collect", async (i) => {
      // Handle trade interaction
      if (!tradeState.active) {
        // Trade not active, ignoring interaction
        return;
      }

      try {
        // Parse the customId to extract action and pagination info
        // Format: trade_toggle:action=items;player=123;itemPage=0;gearPage=0
        const customIdParts = i.customId.split(/[;:]/);
        const actionPart = customIdParts.find((part) =>
          part.startsWith("action=")
        );
        const itemPagePart = customIdParts.find((part) =>
          part.startsWith("itemPage=")
        );
        const gearPagePart = customIdParts.find((part) =>
          part.startsWith("gearPage=")
        );
        const petPagePart = customIdParts.find((part) =>
          part.startsWith("petPage=")
        );

        const action = actionPart ? actionPart.split("=")[1] : "";
        const itemPage = itemPagePart
          ? parseInt(itemPagePart.split("=")[1])
          : 0;
        const gearPage = gearPagePart
          ? parseInt(gearPagePart.split("=")[1])
          : 0;
        const petPage = petPagePart ? parseInt(petPagePart.split("=")[1]) : 0;

        console.log(
          `[TRADE DEBUG] Parsed action: ${action}, itemPage: ${itemPage}, gearPage: ${gearPage}, petPage: ${petPage}`
        );

        // Update pagination state
        tradeState.pagination[playerKey].itemPage = itemPage;
        tradeState.pagination[playerKey].gearPage = gearPage;
        tradeState.pagination[playerKey].petPage = petPage;

        if (action === "items") {
          await handleItemSelection(
            i,
            currentPlayer,
            tradeState,
            player1Message,
            player2Message,
            playerKey
          );
        } else if (action === "gear") {
          await handleGearSelection(
            i,
            currentPlayer,
            tradeState,
            player1Message,
            player2Message,
            playerKey
          );
        } else if (action === "pets") {
          await handlePetSelection(
            i,
            currentPlayer,
            tradeState,
            player1Message,
            player2Message,
            playerKey
          );
        } else if (action === "item_prev" || action === "item_next") {
          tradeState.pagination[playerKey].itemPage = parseInt(itemPage);
          try {
            await i.deferUpdate();
            await handleItemSelection(
              i,
              currentPlayer,
              tradeState,
              player1Message,
              player2Message,
              playerKey
            );
          } catch (err) {
            console.error(
              `[TRADE ERROR] Error in item pagination: ${err.message}\n${err.stack}`
            );
          }
        } else if (action === "gear_prev" || action === "gear_next") {
          tradeState.pagination[playerKey].gearPage = parseInt(gearPage);
          try {
            await i.deferUpdate();
            await handleGearSelection(
              i,
              currentPlayer,
              tradeState,
              player1Message,
              player2Message,
              playerKey
            );
          } catch (err) {
            console.error(
              `[TRADE ERROR] Error in gear pagination: ${err.message}\n${err.stack}`
            );
          }
        } else if (action === "coins") {
          await handleCoinSelection(
            i,
            currentPlayer,
            tradeState,
            player1Message,
            player2Message,
            playerKey
          );
        } else if (action === "ready") {
          await handleReadyToggle(
            i,
            currentPlayer,
            otherPlayer,
            tradeState,
            player1Message,
            player2Message
          );
        } else if (action === "clear") {
          await handleClearOffer(
            i,
            currentPlayer,
            tradeState,
            player1Message,
            player2Message,
            playerKey
          );
        } else if (action === "cancel") {
          await handleTradeCancel(
            i,
            tradeState,
            player1Message,
            player2Message,
            tradeId
          );
        }
      } catch (error) {
        console.error("Error handling trade interaction:", error);
        await i
          .reply({
            content: "An error occurred while processing your action.",
            ephemeral: true,
          })
          .catch(() => {});
      }
    });
  });

  // Clean up on timeout
  setTimeout(() => {
    if (tradeState.active) {
      tradeState.active = false;
      activeTrades.delete(tradeState.player1.id);
      activeTrades.delete(tradeState.player2.id);
    }
  }, 300000);
}

// Handle item selection (stackable items)
async function handleItemSelection(
  interaction,
  currentPlayer,
  tradeState,
  currentMessage,
  otherMessage,
  playerKey
) {
  try {
    // Check if this is a direct button click or a pagination button
    const isPaginationButton =
      interaction.customId.includes("item_prev") ||
      interaction.customId.includes("item_next");

    // Only defer if not already deferred and not a pagination button
    // Pagination buttons are already deferred with deferUpdate() in the collector
    if (!interaction.deferred && !interaction.replied && !isPaginationButton) {
      await interaction.deferReply({ ephemeral: false });
      console.log(
        `[TRADE DEBUG] handleItemSelection deferred reply, direct button click`
      );
    } else {
      console.log(
        `[TRADE DEBUG] handleItemSelection called from pagination, deferred: ${interaction.deferred}, replied: ${interaction.replied}`
      );
    }

    // Get player's inventory
    const playerData = await getPlayerData(currentPlayer.id);
    console.log(
      `[TRADE DEBUG] Player ${currentPlayer.id} pets:`,
      playerData.pets
    );
    if (!playerData) {
      const errorContent = { content: "Error loading your inventory." };
      if (interaction.deferred) {
        return interaction.editReply(errorContent);
      } else {
        return interaction.reply(errorContent);
      }
    }

    const inventory = playerData.inventory?.items || {};
    const allItems = configManager.getAllItems();

    // Filter for stackable items only and subtract already traded amounts
    const alreadyTradedAmounts = {};
    currentPlayer.offer
      .filter((item) => item.type === "item")
      .forEach((item) => {
        alreadyTradedAmounts[item.key] =
          (alreadyTradedAmounts[item.key] || 0) + item.amount;
      });

    const allStackableItems = Object.entries(inventory)
      .map(([itemKey, amount]) => {
        const itemData = allItems[itemKey];
        const tradedAmount = alreadyTradedAmounts[itemKey] || 0;
        const available = amount - tradedAmount;
        return { itemKey, available, itemData };
      })
      .filter(
        ({ itemData, available }) =>
          itemData && !itemData.unique && !itemData.untradeable && available > 0
      );

    if (allStackableItems.length === 0) {
      return interaction.editReply({
        content: "You don't have any stackable items to trade.",
      });
    }

    const currentPage = tradeState.pagination[playerKey].itemPage;
    const itemsPerPage = 25;
    const totalPages = Math.ceil(allStackableItems.length / itemsPerPage);
    const startIndex = currentPage * itemsPerPage;
    const endIndex = Math.min(
      startIndex + itemsPerPage,
      allStackableItems.length
    );
    const stackableItems = allStackableItems.slice(startIndex, endIndex);

    // Create select menu for items
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`item_select_${currentPlayer.id}_${currentPage}`)
      .setPlaceholder(
        `Choose an item to add to trade (Page ${currentPage + 1}/${totalPages})`
      )
      .addOptions(
        stackableItems.map(({ itemKey, available, itemData }) => {
          return {
            label: `${itemData.name} (${available} available)`,
            description: `Add ${itemData.name} to your trade offer`,
            value: itemKey,
            emoji: itemData.emoji,
          };
        })
      );

    const components = [new ActionRowBuilder().addComponents(selectMenu)];

    // Add pagination buttons if there are multiple pages
    if (totalPages > 1) {
      const paginationRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId(
            `trade_toggle:action=item_prev;player=${currentPlayer.id};itemPage=${Math.max(0, currentPage - 1)};gearPage=${tradeState.pagination[playerKey].gearPage}`
          )
          .setLabel("Previous")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("⬅️")
          .setDisabled(currentPage === 0),
        new ButtonBuilder()
          .setCustomId(
            `trade_toggle:action=item_next;player=${currentPlayer.id};itemPage=${Math.min(totalPages - 1, currentPage + 1)};gearPage=${tradeState.pagination[playerKey].gearPage}`
          )
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("➡️")
          .setDisabled(currentPage === totalPages - 1)
      );
      components.push(paginationRow);
    }

    // Always use editReply - deferUpdate allows this to work for pagination
    const selectMessage = await interaction.editReply({
      content: "Select an item to add to your trade:",
      components: components,
    });

    // Set up collector for item selection and pagination
    const selectCollector = selectMessage.createMessageComponentCollector({
      filter: (i) => {
        // Handle item selection dropdown
        if (
          i.customId === `item_select_${currentPlayer.id}_${currentPage}` &&
          i.user.id === currentPlayer.id
        ) {
          return true;
        }
        // Handle pagination buttons
        if (
          i.customId.includes("trade_toggle:action=item_") &&
          i.user.id === currentPlayer.id
        ) {
          return true;
        }
        return false;
      },
      time: 60000,
      // Remove max: 1 to allow pagination interactions
    });

    selectCollector.on("collect", async (selectInteraction) => {
      // Handle pagination buttons
      if (selectInteraction.customId.includes("trade_toggle:action=item_")) {
        console.log(
          `[TRADE DEBUG] Pagination button caught by item collector: ${selectInteraction.customId}`
        );

        // Parse pagination info
        const customIdParts = selectInteraction.customId.split(/[;:]/);
        const actionPart = customIdParts.find((part) =>
          part.startsWith("action=")
        );
        const itemPagePart = customIdParts.find((part) =>
          part.startsWith("itemPage=")
        );
        const gearPagePart = customIdParts.find((part) =>
          part.startsWith("gearPage=")
        );

        const action = actionPart ? actionPart.split("=")[1] : "";
        const newItemPage = itemPagePart
          ? parseInt(itemPagePart.split("=")[1])
          : 0;
        const gearPage = gearPagePart
          ? parseInt(gearPagePart.split("=")[1])
          : 0;

        // Update pagination state
        tradeState.pagination[playerKey].itemPage = newItemPage;
        tradeState.pagination[playerKey].gearPage = gearPage;

        try {
          await selectInteraction.deferUpdate();
          console.log(
            `[TRADE DEBUG] Item pagination button deferred: ${action}`
          );

          // Recursively call handleItemSelection with updated pagination
          await handleItemSelection(
            selectInteraction,
            currentPlayer,
            tradeState,
            currentMessage,
            otherMessage,
            playerKey
          );
        } catch (err) {
          console.error(
            `[TRADE ERROR] Error in item pagination from collector: ${err.message}\n${err.stack}`
          );
        }
        return;
      }

      // Handle item selection dropdown
      const selectedItemKey = selectInteraction.values[0];
      const itemData = allItems[selectedItemKey];
      const availableAmount = inventory[selectedItemKey] || 0;

      // Show modal for quantity
      const modal = new ModalBuilder()
        .setCustomId(`item_quantity_${currentPlayer.id}_${selectedItemKey}`)
        .setTitle(`Add ${itemData.name} to Trade`);

      const quantityInput = new TextInputBuilder()
        .setCustomId("quantity_input")
        .setLabel(`Quantity (1-${availableAmount}, or "all")`)
        .setStyle(TextInputStyle.Short)
        .setRequired(true)
        .setMaxLength(10);

      const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
      modal.addComponents(quantityRow);

      await selectInteraction.showModal(modal);

      try {
        const modalInteraction = await selectInteraction.awaitModalSubmit({
          filter: (mi) =>
            mi.customId ===
              `item_quantity_${currentPlayer.id}_${selectedItemKey}` &&
            mi.user.id === currentPlayer.id,
          time: 120000,
        });

        const quantityStr =
          modalInteraction.fields.getTextInputValue("quantity_input");
        const quantity = parseShorthandAmount(quantityStr, availableAmount);

        if (isNaN(quantity) || quantity <= 0 || quantity > availableAmount) {
          return modalInteraction.reply({
            content: `Invalid quantity. Please enter a number between 1 and ${availableAmount}.`,
            ephemeral: true,
          });
        }

        // Add item to trade offer
        const existingItemIndex = currentPlayer.offer.findIndex(
          (item) => item.type === "item" && item.key === selectedItemKey
        );

        if (existingItemIndex >= 0) {
          currentPlayer.offer[existingItemIndex].amount += quantity;
        } else {
          currentPlayer.offer.push({
            type: "item",
            key: selectedItemKey,
            name: itemData.name,
            emoji: itemData.emoji,
            amount: quantity,
          });
        }

        // Reset ready status
        currentPlayer.ready = false;
        tradeState.player1.ready = false;
        tradeState.player2.ready = false;

        await updateTradeDisplay(tradeState, currentMessage, otherMessage);
        await modalInteraction.reply({
          content: `Added ${quantity}x ${itemData.name} to your trade offer!`,
          ephemeral: false,
        });

        setTimeout(async () => {
          try {
            await modalInteraction.deleteReply();
          } catch {
            // Ignore errors if message is already deleted
          }
        }, 3000);
      } catch (error) {
        console.error("Error in item quantity modal:", error);
      }

      // Clean up the selection message
      await interaction.deleteReply().catch(() => {});
    });

    selectCollector.on("end", async (collected) => {
      if (collected.size === 0) {
        await interaction.deleteReply().catch(() => {});
      }
    });
  } catch (error) {
    console.error("[TRADE ERROR] Error in handleItemSelection:", error);
    try {
      if (interaction.deferred || interaction.replied) {
        await interaction.editReply({
          content: "An error occurred while loading items.",
        });
      } else {
        await interaction.reply({
          content: "An error occurred while loading items.",
          ephemeral: true,
        });
      }
    } catch (replyError) {
      console.error("[TRADE ERROR] Failed to send error message:", replyError);
    }
  }
}

// Handle gear selection (unique items)
async function handleGearSelection(
  interaction,
  currentPlayer,
  tradeState,
  currentMessage,
  otherMessage,
  playerKey
) {
  try {
    // Check if this is a direct button click or a pagination button
    const isPaginationButton =
      interaction.customId.includes("gear_prev") ||
      interaction.customId.includes("gear_next");

    // Only defer if not already deferred and not a pagination button
    // Pagination buttons are already deferred with deferUpdate() in the collector
    if (!interaction.deferred && !interaction.replied && !isPaginationButton) {
      await interaction.deferReply({ ephemeral: false });
      console.log(
        `[TRADE DEBUG] handleGearSelection deferred reply, direct button click`
      );
    } else {
      console.log(
        `[TRADE DEBUG] handleGearSelection called from pagination, deferred: ${interaction.deferred}, replied: ${interaction.replied}`
      );
    }

    // Get player's inventory
    const playerData = await getPlayerData(currentPlayer.id);
    console.log(
      `[TRADE DEBUG] Player ${currentPlayer.id} pets:`,
      playerData.pets
    );
    if (!playerData) {
      return interaction.editReply({
        content: "Error loading your inventory.",
      });
    }

    const allItems = configManager.getAllItems();

    // Get unique items from storage (unequipped items in inventory.equipment array)
    const allUniqueItems = [];

    // Get already traded gear equipment IDs and count per ID
    const alreadyTradedGearCounts = {};
    currentPlayer.offer
      .filter((item) => item.type === "gear")
      .forEach((item) => {
        alreadyTradedGearCounts[item.equipmentId] =
          (alreadyTradedGearCounts[item.equipmentId] || 0) + 1;
      });

    // Add items from storage (inventory.equipment array where isEquipped is false)
    const storageItems =
      playerData.inventory?.equipment?.filter((eq) => !eq.isEquipped) || [];
    storageItems.forEach((eq, index) => {
      const itemData = allItems[eq.itemKey];
      // Only include if not already fully traded (unique by equipmentId)
      const tradedCount = alreadyTradedGearCounts[eq.id] || 0;
      if (itemData && tradedCount === 0) {
        allUniqueItems.push({
          key: eq.itemKey,
          name: itemData.name,
          emoji: itemData.emoji || "⚔️",
          equipmentId: eq.id,
          index: index,
        });
      }
    });

    if (allUniqueItems.length === 0) {
      return interaction.editReply({
        content: "You don't have any gear in storage to trade.",
      });
    }

    const currentPage = tradeState.pagination[playerKey].gearPage;
    const itemsPerPage = 25;
    const totalPages = Math.ceil(allUniqueItems.length / itemsPerPage);
    const startIndex = currentPage * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, allUniqueItems.length);
    const uniqueItems = allUniqueItems.slice(startIndex, endIndex);

    // Create select menu for gear
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`gear_select_${currentPlayer.id}_${currentPage}`)
      .setPlaceholder(
        `Choose gear to add to trade (Page ${currentPage + 1}/${totalPages})`
      )
      .addOptions(
        uniqueItems.map((item, index) => ({
          label: `${item.name} (Storage)`,
          description: `Add ${item.name} to your trade offer`,
          value: `${startIndex + index}`,
          emoji: item.emoji,
        }))
      );

    const components = [new ActionRowBuilder().addComponents(selectMenu)];

    // Add pagination buttons if there are multiple pages
    if (totalPages > 1) {
      const paginationRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId(
            `trade_toggle:action=gear_prev;player=${currentPlayer.id};itemPage=${tradeState.pagination[playerKey].itemPage};gearPage=${Math.max(0, currentPage - 1)}`
          )
          .setLabel("Previous")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("⬅️")
          .setDisabled(currentPage === 0),
        new ButtonBuilder()
          .setCustomId(
            `trade_toggle:action=gear_next;player=${currentPlayer.id};itemPage=${tradeState.pagination[playerKey].itemPage};gearPage=${Math.min(totalPages - 1, currentPage + 1)}`
          )
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("➡️")
          .setDisabled(currentPage === totalPages - 1)
      );
      components.push(paginationRow);
    }

    // Always use editReply - deferUpdate allows this to work for pagination
    const selectMessage = await interaction.editReply({
      content: "Select gear to add to your trade:",
      components: components,
    });

    // Set up collector for gear selection and pagination
    const selectCollector = selectMessage.createMessageComponentCollector({
      filter: (i) => {
        // Handle gear selection dropdown
        if (
          i.customId === `gear_select_${currentPlayer.id}_${currentPage}` &&
          i.user.id === currentPlayer.id
        ) {
          return true;
        }
        // Handle pagination buttons
        if (
          i.customId.includes("trade_toggle:action=gear_") &&
          i.user.id === currentPlayer.id
        ) {
          return true;
        }
        return false;
      },
      time: 60000,
      // Remove max: 1 to allow pagination interactions
    });

    selectCollector.on("collect", async (selectInteraction) => {
      // Handle pagination buttons
      if (selectInteraction.customId.includes("trade_toggle:action=gear_")) {
        console.log(
          `[TRADE DEBUG] Gear pagination button caught by gear collector: ${selectInteraction.customId}`
        );

        // Parse pagination info
        const customIdParts = selectInteraction.customId.split(/[;:]/);
        const actionPart = customIdParts.find((part) =>
          part.startsWith("action=")
        );
        const itemPagePart = customIdParts.find((part) =>
          part.startsWith("itemPage=")
        );
        const gearPagePart = customIdParts.find((part) =>
          part.startsWith("gearPage=")
        );

        const action = actionPart ? actionPart.split("=")[1] : "";
        const itemPage = itemPagePart
          ? parseInt(itemPagePart.split("=")[1])
          : 0;
        const newGearPage = gearPagePart
          ? parseInt(gearPagePart.split("=")[1])
          : 0;

        // Update pagination state
        tradeState.pagination[playerKey].itemPage = itemPage;
        tradeState.pagination[playerKey].gearPage = newGearPage;

        try {
          await selectInteraction.deferUpdate();
          console.log(
            `[TRADE DEBUG] Gear pagination button deferred: ${action}`
          );

          // Recursively call handleGearSelection with updated pagination
          await handleGearSelection(
            selectInteraction,
            currentPlayer,
            tradeState,
            currentMessage,
            otherMessage,
            playerKey
          );
        } catch (err) {
          console.error(
            `[TRADE ERROR] Error in gear pagination from collector: ${err.message}\n${err.stack}`
          );
        }
        return;
      }

      // Handle gear selection dropdown
      const selectedIndex = parseInt(selectInteraction.values[0]);
      const selectedItem = allUniqueItems[selectedIndex];

      // Add gear to trade offer
      currentPlayer.offer.push({
        type: "gear",
        key: selectedItem.key,
        name: selectedItem.name,
        emoji: selectedItem.emoji,
        amount: 1,
        source: selectedItem.source,
        storageIndex: selectedItem.storageIndex,
        equipmentId: selectedItem.equipmentId,
      });

      // Reset ready status
      currentPlayer.ready = false;
      tradeState.player1.ready = false;
      tradeState.player2.ready = false;

      await updateTradeDisplay(tradeState, currentMessage, otherMessage);
      await selectInteraction.reply({
        content: `Added ${selectedItem.name} to your trade offer!`,
        ephemeral: false,
      });

      setTimeout(async () => {
        try {
          await selectInteraction.deleteReply();
        } catch {
          // Ignore errors if message is already deleted
        }
      }, 3000);

      // Clean up the selection message
      await interaction.deleteReply().catch(() => {});
    });

    selectCollector.on("end", async (collected) => {
      if (collected.size === 0) {
        await interaction.deleteReply().catch(() => {});
      }
    });
  } catch (error) {
    console.error("[TRADE ERROR] Error in handleGearSelection:", error);
    try {
      if (interaction.deferred || interaction.replied) {
        await interaction.editReply({
          content: "An error occurred while loading gear.",
        });
      } else {
        await interaction.reply({
          content: "An error occurred while loading gear.",
          ephemeral: true,
        });
      }
    } catch (replyError) {
      console.error("[TRADE ERROR] Failed to send error message:", replyError);
    }
  }
}

// Handle pet selection
async function handlePetSelection(
  interaction,
  currentPlayer,
  tradeState,
  currentMessage,
  otherMessage,
  playerKey
) {
  try {
    const isPaginationButton =
      interaction.customId.includes("pet_prev") ||
      interaction.customId.includes("pet_next");

    if (!interaction.deferred && !interaction.replied && !isPaginationButton) {
      await interaction.deferReply({ ephemeral: false });
      console.log(
        `[TRADE DEBUG] handlePetSelection deferred reply, direct button click`
      );
    } else {
      console.log(
        `[TRADE DEBUG] handlePetSelection called from pagination, deferred: ${interaction.deferred}, replied: ${interaction.replied}`
      );
    }

    const playerData = await getPlayerData(currentPlayer.id);
    console.log(
      `[TRADE DEBUG] Player ${currentPlayer.id} pets:`,
      playerData.pets
    );
    if (!playerData || !playerData.pets || playerData.pets.length === 0) {
      return interaction.editReply({
        content: "You don't have any pets to trade.",
      });
    }

    const allPets = playerData.pets;

    const currentPage = tradeState.pagination[playerKey].petPage;
    const petsPerPage = 25;
    const totalPages = Math.ceil(allPets.length / petsPerPage);
    const startIndex = currentPage * petsPerPage;
    const endIndex = Math.min(startIndex + petsPerPage, allPets.length);
    const petsToShow = allPets
      .slice(startIndex, endIndex)
      .filter((pet) => pet.id !== playerData.active_pet_id);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`pet_select_${currentPlayer.id}_${currentPage}`)
      .setPlaceholder(
        `Choose a pet to add to trade (Page ${currentPage + 1}/${totalPages})`
      )
      .addOptions(
        petsToShow.map((pet, index) => ({
          label: `${ITEMS[pet.petKey]?.name || "Unknown Pet"} (Lvl ${getPetLevel(pet)}) [${pet.rarity ? ITEM_RARITY[pet.rarity]?.name : "Unknown Rarity"}] (${pet.id.toString().slice(0, 4)})`,
          description: `Add ${ITEMS[pet.petKey]?.name || "Unknown Pet"} to your trade offer`,
          value: `${startIndex + index}`,
          emoji: ITEMS[pet.petKey]?.emoji || "🐾",
        }))
      );

    const components = [new ActionRowBuilder().addComponents(selectMenu)];

    if (totalPages > 1) {
      const paginationRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId(
            `trade_toggle:action=pet_prev;player=${currentPlayer.id};itemPage=${tradeState.pagination[playerKey].itemPage};gearPage=${tradeState.pagination[playerKey].gearPage};petPage=${Math.max(0, currentPage - 1)}`
          )
          .setLabel("Previous")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("⬅️")
          .setDisabled(currentPage === 0),
        new ButtonBuilder()
          .setCustomId(
            `trade_toggle:action=pet_next;player=${currentPlayer.id};itemPage=${tradeState.pagination[playerKey].itemPage};gearPage=${tradeState.pagination[playerKey].gearPage};petPage=${Math.min(totalPages - 1, currentPage + 1)}`
          )
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("➡️")
          .setDisabled(currentPage === totalPages - 1)
      );
      components.push(paginationRow);
    }

    const selectMessage = await interaction.editReply({
      content: "Select a pet to add to your trade:",
      components: components,
    });

    const selectCollector = selectMessage.createMessageComponentCollector({
      filter: (i) => {
        if (
          i.customId === `pet_select_${currentPlayer.id}_${currentPage}` &&
          i.user.id === currentPlayer.id
        ) {
          return true;
        }
        if (
          i.customId.includes("trade_toggle:action=pet_") &&
          i.user.id === currentPlayer.id
        ) {
          return true;
        }
        return false;
      },
      time: 60000,
    });

    selectCollector.on("collect", async (selectInteraction) => {
      if (selectInteraction.customId.includes("trade_toggle:action=pet_")) {
        console.log(
          `[TRADE DEBUG] Pet pagination button caught by pet collector: ${selectInteraction.customId}`
        );

        const customIdParts = selectInteraction.customId.split(/[;:]/);
        const actionPart = customIdParts.find((part) =>
          part.startsWith("action=")
        );
        const itemPagePart = customIdParts.find((part) =>
          part.startsWith("itemPage=")
        );
        const gearPagePart = customIdParts.find((part) =>
          part.startsWith("gearPage=")
        );
        const petPagePart = customIdParts.find((part) =>
          part.startsWith("petPage=")
        );

        const action = actionPart ? actionPart.split("=")[1] : "";
        const itemPage = itemPagePart
          ? parseInt(itemPagePart.split("=")[1])
          : 0;
        const gearPage = gearPagePart
          ? parseInt(gearPagePart.split("=")[1])
          : 0;
        const newPetPage = petPagePart
          ? parseInt(petPagePart.split("=")[1])
          : 0;

        tradeState.pagination[playerKey].itemPage = itemPage;
        tradeState.pagination[playerKey].gearPage = gearPage;
        tradeState.pagination[playerKey].petPage = newPetPage;

        try {
          await selectInteraction.deferUpdate();
          console.log(
            `[TRADE DEBUG] Pet pagination button deferred: ${action}`
          );
          await handlePetSelection(
            selectInteraction,
            currentPlayer,
            tradeState,
            currentMessage,
            otherMessage,
            playerKey
          );
        } catch (err) {
          console.error(
            `[TRADE ERROR] Error in pet pagination from collector: ${err.message}\n${err.stack}`
          );
        }
        return;
      }

      const selectedIndex = parseInt(selectInteraction.values[0]);
      const selectedPet = allPets[selectedIndex];

      const petDetails = ITEMS[selectedPet.petKey];
      const petLevel = getPetLevel(selectedPet);
      const petRarity = selectedPet.rarity
        ? ITEM_RARITY[selectedPet.rarity]?.name
        : "Unknown Rarity";

      currentPlayer.offer.push({
        type: "pet",
        id: selectedPet.id, // Assuming pets have a unique ID
        petKey: selectedPet.petKey, // Store petKey for later lookup
        name: petDetails?.name || "Unknown Pet",
        level: petLevel,
        rarity: petRarity,
        emoji: petDetails?.emoji || "🐾",
        amount: 1, // Always 1 for pets
      });

      currentPlayer.ready = false;
      tradeState.player1.ready = false;
      tradeState.player2.ready = false;

      await updateTradeDisplay(tradeState, currentMessage, otherMessage);

      const petDisplayName = `${petDetails?.emoji || "🐾"} ${petDetails?.name || "Unknown Pet"} (Lvl ${petLevel}) [${selectedPet.rarity ? ITEM_RARITY[selectedPet.rarity]?.name : "Unknown Rarity"}] \`${selectedPet.id.toString().slice(0, 4)}\``;

      await selectInteraction.reply({
        content: `Added ${petDisplayName} to your trade offer!`,
        ephemeral: false,
      });

      setTimeout(async () => {
        try {
          await selectInteraction.deleteReply();
        } catch {
          // Ignore errors if message is already deleted
        }
      }, 3000);

      await interaction.deleteReply().catch(() => {});
    });

    selectCollector.on("end", async (collected) => {
      if (collected.size === 0) {
        await interaction.deleteReply().catch(() => {});
      }
    });
  } catch (error) {
    console.error("[TRADE ERROR] Error in handlePetSelection:", error);
    try {
      if (interaction.deferred || interaction.replied) {
        await interaction.editReply({
          content: "An error occurred while loading pets.",
        });
      } else {
        await interaction.reply({
          content: "An error occurred while loading pets.",
          ephemeral: true,
        });
      }
    } catch (replyError) {
      console.error("[TRADE ERROR] Failed to send error message:", replyError);
    }
  }
}

// Handle coin selection
async function handleCoinSelection(
  interaction,
  currentPlayer,
  tradeState,
  currentMessage,
  otherMessage
) {
  // Get player's current coins
  const playerData = await getPlayerData(currentPlayer.id);
  if (!playerData) {
    return interaction.reply({
      content: "Error loading your coin balance.",
      ephemeral: true,
    });
  }

  const availableCoins = playerData.coins || 0;

  // Show modal for coin amount
  const modal = new ModalBuilder()
    .setCustomId(`coin_amount_${currentPlayer.id}`)
    .setTitle("Add Coins to Trade");

  const amountInput = new TextInputBuilder()
    .setCustomId("amount_input")
    .setLabel(`Coins (1-${availableCoins.toLocaleString()})`)
    .setPlaceholder("Enter amount: 500, 10k, 1m, all")
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setMaxLength(15);

  const amountRow = new ActionRowBuilder().addComponents(amountInput);
  modal.addComponents(amountRow);

  await interaction.showModal(modal);

  try {
    const modalInteraction = await interaction.awaitModalSubmit({
      filter: (mi) =>
        mi.customId === `coin_amount_${currentPlayer.id}` &&
        mi.user.id === currentPlayer.id,
      time: 120000,
    });

    const amountStr = modalInteraction.fields.getTextInputValue("amount_input");
    const amount = parseShorthandAmount(amountStr, availableCoins);

    if (isNaN(amount) || amount <= 0 || amount > availableCoins) {
      await modalInteraction.reply({
        content: `Invalid amount. Please enter a number between 1 and ${availableCoins.toLocaleString()}.`,
        ephemeral: true,
      });
      return;
    }

    // Add coins to trade offer (replace existing coin offer)
    const existingCoinIndex = currentPlayer.offer.findIndex(
      (item) => item.type === "coins"
    );

    if (existingCoinIndex >= 0) {
      currentPlayer.offer[existingCoinIndex].amount = amount;
    } else {
      currentPlayer.offer.push({
        type: "coins",
        amount: amount,
      });
    }

    // Reset ready status
    currentPlayer.ready = false;
    tradeState.player1.ready = false;
    tradeState.player2.ready = false;

    await updateTradeDisplay(tradeState, currentMessage, otherMessage);
    await modalInteraction.reply({
      content: `Added ${amount.toLocaleString()} coins to your trade offer!`,
      ephemeral: false,
    });

    setTimeout(async () => {
      try {
        await modalInteraction.deleteReply();
      } catch {
        // Ignore errors if message is already deleted
      }
    }, 3000);
  } catch {
    console.error("Error in coin amount modal:");
  }
}

// Handle ready toggle
async function handleReadyToggle(
  interaction,
  currentPlayer,
  otherPlayer,
  tradeState,
  currentMessage,
  otherMessage
) {
  await interaction.deferUpdate();

  // Handle ready toggle for trade
  console.log(
    `[TRADE DEBUG] Before modification - tradeState.player2.ready: ${tradeState.player2.ready}`
  );

  currentPlayer.ready = !currentPlayer.ready;

  console.log(
    `[TRADE DEBUG] After modification - currentPlayer.ready: ${currentPlayer.ready}`
  );
  console.log(
    `[TRADE DEBUG] Player ${currentPlayer.id} ready state changed to: ${currentPlayer.ready}`
  );
  console.log(
    `[TRADE DEBUG] After modification - tradeState.player1.ready: ${tradeState.player1.ready}`
  );
  console.log(
    `[TRADE DEBUG] After modification - tradeState.player2.ready: ${tradeState.player2.ready}`
  );
  console.log(
    `[TRADE DEBUG] Player1 ready: ${tradeState.player1.ready}, Player2 ready: ${tradeState.player2.ready}`
  );

  // Check if both players are ready
  if (tradeState.player1.ready && tradeState.player2.ready) {
    // Execute the trade
    await executeTrade(tradeState, currentMessage, otherMessage);
  } else {
    // Update display
    await updateTradeDisplay(tradeState, currentMessage, otherMessage);
  }
}

// Handle clear offer
async function handleClearOffer(
  interaction,
  currentPlayer,
  tradeState,
  currentMessage,
  otherMessage
) {
  await interaction.deferUpdate();

  // Clear the current player's offer
  currentPlayer.offer = [];
  currentPlayer.ready = false;

  // Reset both players' ready status
  tradeState.player1.ready = false;
  tradeState.player2.ready = false;

  await updateTradeDisplay(tradeState, currentMessage, otherMessage);
}

// Handle trade cancellation
async function handleTradeCancel(
  interaction,
  tradeState,
  currentMessage,
  otherMessage
  // tradeId
) {
  await interaction.deferUpdate();

  tradeState.active = false;
  activeTrades.delete(tradeState.player1.id);
  activeTrades.delete(tradeState.player2.id);

  const cancelEmbed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ERROR)
    .setTitle("🚫 Trade Cancelled")
    .setDescription("The trade has been cancelled.");

  await currentMessage.edit({ embeds: [cancelEmbed], components: [] });
  await otherMessage.edit({ embeds: [cancelEmbed], components: [] });
}

// Update trade display for both players
async function updateTradeDisplay(tradeState, player1Message, player2Message) {
  // Update trade display for both players
  // For Player 1's message (player1Message)
  // Player 1 sees Player 2's offer on top, their own offer on the bottom
  const p1_otherPlayerEmbed = createTradeEmbed(
    tradeState.player2.name,
    tradeState.player2.offer,
    false,
    tradeState.player2.ready
  ); // Player 2's embed from P1's perspective
  const p1_currentPlayerEmbed = createTradeEmbed(
    tradeState.player1.name,
    tradeState.player1.offer,
    true,
    tradeState.player1.ready
  ); // Player 1's embed from P1's perspective
  const player1Buttons = createTradeButtons(
    tradeState.player1.id,
    tradeState.player1.ready,
    tradeState.pagination.player1.itemPage,
    tradeState.pagination.player1.gearPage,
    tradeState.pagination.player1.petPage
  );
  await player1Message.edit({
    embeds: [p1_otherPlayerEmbed, p1_currentPlayerEmbed],
    components: player1Buttons,
  });

  // For Player 2's message (player2Message)
  // Player 2 sees Player 1's offer on top, their own offer on the bottom
  const p2_otherPlayerEmbed = createTradeEmbed(
    tradeState.player1.name,
    tradeState.player1.offer,
    false,
    tradeState.player1.ready
  ); // Player 1's embed from P2's perspective
  const p2_currentPlayerEmbed = createTradeEmbed(
    tradeState.player2.name,
    tradeState.player2.offer,
    true,
    tradeState.player2.ready
  ); // Player 2's embed from P2's perspective
  const player2Buttons = createTradeButtons(
    tradeState.player2.id,
    tradeState.player2.ready,
    tradeState.pagination.player2.itemPage,
    tradeState.pagination.player2.gearPage,
    tradeState.pagination.player2.petPage
  );
  await player2Message.edit({
    embeds: [p2_otherPlayerEmbed, p2_currentPlayerEmbed],
    components: player2Buttons,
  });
}

// Execute the trade when both players are ready
async function executeTrade(tradeState, player1Message, player2Message) {
  try {
    const allItems = configManager.getAllItems();
    // Block trading untradeable stackable items defensively
    const hasBanned = [
      ...tradeState.player1.offer,
      ...tradeState.player2.offer,
    ].some((it) => {
      if (it.type !== "item") return false;
      const meta = allItems[it.key];
      return meta && meta.untradeable === true;
    });
    if (hasBanned) {
      throw new Error("One or more offered items cannot be traded.");
    }

    // Validate both players still have the items they're offering
    const player1Data = await getPlayerData(tradeState.player1.id);
    const player2Data = await getPlayerData(tradeState.player2.id);

    if (!player1Data || !player2Data) {
      throw new Error("Could not load player data");
    }

    // Validate player1's offer
    for (const item of tradeState.player1.offer) {
      if (item.type === "coins") {
        if ((player1Data.coins || 0) < item.amount) {
          throw new Error(
            `${tradeState.player1.name} doesn't have enough coins`
          );
        }
      } else if (item.type === "item") {
        const available = player1Data.inventory?.items?.[item.key] || 0;
        if (available < item.amount) {
          throw new Error(
            `${tradeState.player1.name} doesn't have enough ${item.name}`
          );
        }
      } else if (item.type === "gear") {
        const storageItems =
          player1Data.inventory?.equipment?.filter((eq) => !eq.isEquipped) ||
          [];
        const gearItem = storageItems.find((eq) => eq.id === item.equipmentId);
        if (!gearItem) {
          throw new Error(
            `${tradeState.player1.name} no longer has ${item.name} in storage`
          );
        }
      } else if (item.type === "pet") {
        const pet = player1Data.pets?.find((p) => p.id === item.id);
        if (!pet) {
          throw new Error(
            `${tradeState.player1.name} no longer has the ${item.name}`
          );
        }
      }
    }

    // Validate player2's offer
    for (const item of tradeState.player2.offer) {
      if (item.type === "coins") {
        if ((player2Data.coins || 0) < item.amount) {
          throw new Error(
            `${tradeState.player2.name} doesn't have enough coins`
          );
        }
      } else if (item.type === "item") {
        const available = player2Data.inventory?.items?.[item.key] || 0;
        if (available < item.amount) {
          throw new Error(
            `${tradeState.player2.name} doesn't have enough ${item.name}`
          );
        }
      } else if (item.type === "gear") {
        const storageItems =
          player2Data.inventory?.equipment?.filter((eq) => !eq.isEquipped) ||
          [];
        const gearItem = storageItems.find((eq) => eq.id === item.equipmentId);
        if (!gearItem) {
          throw new Error(
            `${tradeState.player2.name} no longer has ${item.name} in storage`
          );
        }
      } else if (item.type === "pet") {
        const pet = player2Data.pets?.find((p) => p.id === item.id);
        if (!pet) {
          throw new Error(
            `${tradeState.player2.name} no longer has the ${item.name}`
          );
        }
      }
    }

    // Execute the trade transfers
    // Transfer player1's offer to player2
    for (const item of tradeState.player1.offer) {
      if (item.type === "coins") {
        await dbRunQueued(
          "UPDATE players SET coins = coins - ? WHERE discord_id = ?",
          [item.amount, tradeState.player1.id]
        );
        await dbRunQueued(
          "UPDATE players SET coins = coins + ? WHERE discord_id = ?",
          [item.amount, tradeState.player2.id]
        );
      } else if (item.type === "item") {
        await dbRunQueued(
          "UPDATE player_inventory_items SET amount = amount - ? WHERE discord_id = ? AND item_name = ?",
          [item.amount, tradeState.player1.id, item.key]
        );
        await dbRunQueued(
          "INSERT INTO player_inventory_items (discord_id, item_name, amount) VALUES (?, ?, ?) ON CONFLICT(discord_id, item_name) DO UPDATE SET amount = amount + excluded.amount",
          [tradeState.player2.id, item.key, item.amount]
        );
      } else if (item.type === "gear") {
        // Transfer gear item by updating the owner in the player_equipment table
        await dbRunQueued(
          "UPDATE player_equipment SET discord_id = ? WHERE equipment_id = ? AND discord_id = ?",
          [tradeState.player2.id, item.equipmentId, tradeState.player1.id]
        );
      } else if (item.type === "pet") {
        // Remove pet from player1
        const player1Pets = player1Data.pets || [];
        const updatedPlayer1Pets = player1Pets.filter((p) => p.id !== item.id);
        await dbRunQueued(
          "UPDATE players SET pets_json = ? WHERE discord_id = ?",
          [JSON.stringify(updatedPlayer1Pets), tradeState.player1.id]
        );

        // Add pet to player2
        const player2Pets = player2Data.pets || [];
        const petToTransfer = player1Pets.find((p) => p.id === item.id);
        if (petToTransfer) {
          player2Pets.push(petToTransfer);
          await dbRunQueued(
            "UPDATE players SET pets_json = ? WHERE discord_id = ?",
            [JSON.stringify(player2Pets), tradeState.player2.id]
          );
        }
      }
    }

    // Transfer player2's offer to player1
    for (const item of tradeState.player2.offer) {
      if (item.type === "coins") {
        await dbRunQueued(
          "UPDATE players SET coins = coins - ? WHERE discord_id = ?",
          [item.amount, tradeState.player2.id]
        );
        await dbRunQueued(
          "UPDATE players SET coins = coins + ? WHERE discord_id = ?",
          [item.amount, tradeState.player1.id]
        );
      } else if (item.type === "item") {
        await dbRunQueued(
          "UPDATE player_inventory_items SET amount = amount - ? WHERE discord_id = ? AND item_name = ?",
          [item.amount, tradeState.player2.id, item.key]
        );
        await dbRunQueued(
          "INSERT INTO player_inventory_items (discord_id, item_name, amount) VALUES (?, ?, ?) ON CONFLICT(discord_id, item_name) DO UPDATE SET amount = amount + excluded.amount",
          [tradeState.player1.id, item.key, item.amount]
        );
      } else if (item.type === "gear") {
        // Transfer gear item by updating the owner in the player_equipment table
        await dbRunQueued(
          "UPDATE player_equipment SET discord_id = ? WHERE equipment_id = ? AND discord_id = ?",
          [tradeState.player1.id, item.equipmentId, tradeState.player2.id]
        );
      } else if (item.type === "pet") {
        // Remove pet from player2
        const player2Pets = player2Data.pets || [];
        const updatedPlayer2Pets = player2Pets.filter((p) => p.id !== item.id);
        await dbRunQueued(
          "UPDATE players SET pets_json = ? WHERE discord_id = ?",
          [JSON.stringify(updatedPlayer2Pets), tradeState.player2.id]
        );

        // Add pet to player1
        const player1Pets = player1Data.pets || [];
        const petToTransfer = player2Pets.find((p) => p.id === item.id);
        if (petToTransfer) {
          player1Pets.push(petToTransfer);
          await dbRunQueued(
            "UPDATE players SET pets_json = ? WHERE discord_id = ?",
            [JSON.stringify(player1Pets), tradeState.player1.id]
          );
        }
      }
    }

    // Clean up zero amounts
    await dbRunQueued("DELETE FROM player_inventory_items WHERE amount <= 0");

    // Clean up trade state
    tradeState.active = false;
    activeTrades.delete(tradeState.player1.id);
    activeTrades.delete(tradeState.player2.id);

    // Create trade summary
    const formatOffer = (offer) => {
      if (offer.length === 0) return "Nothing";

      return offer
        .map((item) => {
          if (item.type === "coins") {
            return `<:purse_coins:1367849116033482772> **${item.amount.toLocaleString()} coins**`;
          } else if (item.type === "gear") {
            const equipmentIdPrefix = item.equipmentId
              ? `\`${item.equipmentId.toString().slice(0, 4)}\``
              : "";
            return `${item.emoji} **${item.name}** ${equipmentIdPrefix}`;
          } else if (item.type === "pet") {
            return `${item.emoji} **${item.name} (Lvl ${item.level}) [${item.rarity}]** \`${item.id.toString().slice(0, 4)}\``;
          } else {
            return `${item.emoji} **${item.amount}x ${item.name}**`;
          }
        })
        .join("\n");
    };

    const tradeSummary1 = `**You gave:**\n${formatOffer(tradeState.player1.offer, tradeState.player1.name)}\n\n**You received:**\n${formatOffer(tradeState.player2.offer, tradeState.player2.name)}`;
    const tradeSummary2 = `**You gave:**\n${formatOffer(tradeState.player2.offer, tradeState.player2.name)}\n\n**You received:**\n${formatOffer(tradeState.player1.offer, tradeState.player1.name)}`;

    // Create personalized success embeds
    const successEmbed1 = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Trade Completed")
      .setDescription(tradeSummary1);

    const successEmbed2 = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Trade Completed")
      .setDescription(tradeSummary2);

    await player1Message.edit({ embeds: [successEmbed1], components: [] });
    await player2Message.edit({ embeds: [successEmbed2], components: [] });
  } catch (error) {
    console.error("Error executing trade:", error);

    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ Trade Failed")
      .setDescription(`Trade failed: ${error.message}`);

    await player1Message.edit({ embeds: [errorEmbed], components: [] });
    await player2Message.edit({ embeds: [errorEmbed], components: [] });

    // Clean up trade state
    tradeState.active = false;
    activeTrades.delete(tradeState.player1.id);
    activeTrades.delete(tradeState.player2.id);
  }
}
