=== CRASH LOG ===
{
  "timestamp": "2025-07-26T11:52:01.818Z",
  "crashType": "skill_action",
  "error": {
    "name": "DiscordAPIError[10062]",
    "message": "Unknown interaction",
    "stack": "DiscordAPIError[10062]: Unknown interaction\n    at handleErrors (C:\\Users\\<USER>\\Desktop\\disblock\\node_modules\\@discordjs\\rest\\dist\\index.js:748:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BurstHandler.runRequest (C:\\Users\\<USER>\\Desktop\\disblock\\node_modules\\@discordjs\\rest\\dist\\index.js:852:23)\n    at async _REST.request (C:\\Users\\<USER>\\Desktop\\disblock\\node_modules\\@discordjs\\rest\\dist\\index.js:1293:22)\n    at async ChatInputCommandInteraction.deferReply (C:\\Users\\<USER>\\Desktop\\disblock\\node_modules\\discord.js\\src\\structures\\interfaces\\InteractionResponses.js:129:22)\n    at async setupNewMultiAction (C:\\Users\\<USER>\\Desktop\\disblock\\utils\\skillActionUtils.js:780:9)\n    at async handleSkillActionExecution (C:\\Users\\<USER>\\Desktop\\disblock\\utils\\skillActionUtils.js:1598:37)\n    at async handleGenericSkillAction (C:\\Users\\<USER>\\Desktop\\disblock\\utils\\genericSkillHandler.js:27:7)\n    at async handleSkillCommand (C:\\Users\\<USER>\\Desktop\\disblock\\utils\\skillCommandUtils.js:78:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\disblock\\commands\\farm.js:156:5)"
  },
  "context": {
    "userId": "342349683320029194",
    "skillName": "farming",
    "actionId": "N/A",
    "crashPoint": "top_level_handler",
    "errorCode": "[FARMING-HNDL-UNK-001]",
    "category": "skill_execution"
  },
  "nodeVersion": "v22.14.0",
  "platform": "win32"
}

=== STACK TRACE ===
DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ChatInputCommandInteraction.deferReply (C:\Users\<USER>\Desktop\disblock\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:129:22)
    at async setupNewMultiAction (C:\Users\<USER>\Desktop\disblock\utils\skillActionUtils.js:780:9)
    at async handleSkillActionExecution (C:\Users\<USER>\Desktop\disblock\utils\skillActionUtils.js:1598:37)
    at async handleGenericSkillAction (C:\Users\<USER>\Desktop\disblock\utils\genericSkillHandler.js:27:7)
    at async handleSkillCommand (C:\Users\<USER>\Desktop\disblock\utils\skillCommandUtils.js:78:5)
    at async Object.execute (C:\Users\<USER>\Desktop\disblock\commands\farm.js:156:5)
