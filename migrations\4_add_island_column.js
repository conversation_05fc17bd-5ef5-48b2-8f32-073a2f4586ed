// Migration to add island data storage to players table

const MIGRATION_VERSION = 4;

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add island_json column`,
  );

  // Use db.run which is suitable for ALTER TABLE
  // Add the column with a default value for existing rows
  await new Promise((resolve, reject) => {
    db.run(
      "ALTER TABLE players ADD COLUMN island_json TEXT DEFAULT '{}'",
      function (err) {
        if (err) {
          // Check if the error is because the column already exists (useful for rerunning)
          if (err.message.includes("duplicate column name")) {
            console.log(
              `[Migration ${MIGRATION_VERSION}] Column island_json already exists, skipping.`,
            );
            resolve();
          } else {
            console.error(
              `[Migration ${MIGRATION_VERSION}] Error adding island_json column:`,
              err,
            );
            reject(err);
          }
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully added island_json column.`,
          );
          resolve();
        }
      },
    );
  });

  // Optional: Update metadata version (The main migration runner in bot.js should handle this)
  // Silent migration completion
}

module.exports = { up /*, down */ };
