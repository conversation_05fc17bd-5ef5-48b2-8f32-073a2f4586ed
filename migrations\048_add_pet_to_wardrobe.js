const { dbRun, dbAll } = require("../utils/dbUtils");

async function up() {
  console.log(
    "[Migration 048] Adding pet support to wardrobe_presets table...",
  );

  try {
    // Check if pet_id column already exists
    const columns = await dbAll("PRAGMA table_info(wardrobe_presets)");
    const petColumnExists = columns.some((col) => col.name === "pet_id");

    if (petColumnExists) {
      console.log("[Migration 048] pet_id column already exists, skipping...");
      return;
    }

    console.log(
      "[Migration 048] Adding pet_id column to wardrobe_presets table...",
    );
    await dbRun(`
      ALTER TABLE wardrobe_presets 
      ADD COLUMN pet_id TEXT
    `);

    console.log(
      "[Migration 048] Successfully added pet_id column to wardrobe_presets table",
    );
  } catch (error) {
    console.error(
      "[Migration 048] Error adding pet_id column to wardrobe_presets table:",
      error,
    );
    throw error;
  }
}

async function down() {
  console.log(
    "[Migration 048] Removing pet_id column from wardrobe_presets table...",
  );

  try {
    // SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
    console.log("[Migration 048] Creating backup of wardrobe_presets...");
    await dbRun(`
      CREATE TABLE wardrobe_presets_backup AS 
      SELECT id, user_id, slot_number, helmet_item_id, chestplate_item_id, 
             leggings_item_id, boots_item_id, weapon_item_id, axe_item_id, 
             pickaxe_item_id, shovel_item_id, hoe_item_id, fishing_rod_item_id, 
             necklace_item_id, cloak_item_id, belt_item_id, gloves_item_id, 
             preset_name, created_at, updated_at
      FROM wardrobe_presets
    `);

    console.log("[Migration 048] Dropping original wardrobe_presets table...");
    await dbRun("DROP TABLE wardrobe_presets");

    console.log(
      "[Migration 048] Recreating wardrobe_presets table without pet_id...",
    );
    await dbRun(`
      CREATE TABLE wardrobe_presets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        slot_number INTEGER NOT NULL,
        helmet_item_id TEXT,
        chestplate_item_id TEXT,
        leggings_item_id TEXT,
        boots_item_id TEXT,
        weapon_item_id TEXT,
        axe_item_id TEXT,
        pickaxe_item_id TEXT,
        shovel_item_id TEXT,
        hoe_item_id TEXT,
        fishing_rod_item_id TEXT,
        necklace_item_id TEXT,
        cloak_item_id TEXT,
        belt_item_id TEXT,
        gloves_item_id TEXT,
        preset_name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, slot_number)
      )
    `);

    console.log("[Migration 048] Restoring data from backup...");
    await dbRun(`
      INSERT INTO wardrobe_presets 
      SELECT * FROM wardrobe_presets_backup
    `);

    console.log("[Migration 048] Dropping backup table...");
    await dbRun("DROP TABLE wardrobe_presets_backup");

    console.log(
      "[Migration 048] Successfully removed pet_id column from wardrobe_presets table",
    );
  } catch (error) {
    console.error(
      "[Migration 048] Error removing pet_id column from wardrobe_presets table:",
      error,
    );
    throw error;
  }
}

module.exports = { up, down };
