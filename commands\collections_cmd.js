const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MessageFlags,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");
const { COLLECTIONS } = require("../data/collections");
const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
const { getRankPrefix } = require("../utils/rankUtils");
const { checkRankPermission } = require("../utils/permissionUtils");
const configManager = require("../utils/configManager");
const { createProgressBar } = require("../utils/displayUtils");
const { formatXP } = require("../utils/formatUtils");

// Helper function to estimate embed size
function estimateEmbedSize(embed) {
  let size = 0;

  // Title
  if (embed.data.title) size += embed.data.title.length;

  // Description
  if (embed.data.description) size += embed.data.description.length;

  // Fields
  if (embed.data.fields) {
    for (const field of embed.data.fields) {
      size += (field.name || "").length;
      size += (field.value || "").length;
    }
  }

  // Footer
  if (embed.data.footer?.text) size += embed.data.footer.text.length;

  return size;
}

// Helper function to create collection field data
function createCollectionFieldData(
  collectionDef,
  _itemKey,
  currentAmount,
  itemEmoji
) {
  let currentTierLevel = 0;
  let nextTierAmount = 0;

  collectionDef.tiers.forEach((tier) => {
    if (currentAmount >= tier.amount) {
      currentTierLevel = Math.max(currentTierLevel, tier.level);
    }
  });

  const nextTier = collectionDef.tiers.find(
    (tier) => tier.level === currentTierLevel + 1
  );
  nextTierAmount = nextTier ? nextTier.amount : 0;

  const progressStartAmount =
    currentTierLevel > 0
      ? collectionDef.tiers.find((tier) => tier.level === currentTierLevel)
          ?.amount || 0
      : 0;
  const progressCurrent = currentAmount - progressStartAmount;
  const progressMax =
    nextTierAmount > 0 ? nextTierAmount - progressStartAmount : 0;

  const progressBarString = createProgressBar(progressCurrent, progressMax, {
    showPercentage: true,
    size: 10,
    useEmojis: true,
  });

  const isMaxed = progressMax === 0;
  // determine highest available tier
  const maxTier = collectionDef.tiers.reduce(
    (max, t) => Math.max(max, t.level),
    0
  );
  let displayValue;
  if (isMaxed) {
    displayValue = [
      `${progressBarString}`,
      `**Total: ${formatXP(currentAmount)}**`,
    ].join("\n");
  } else {
    displayValue = [
      `${progressBarString}`,
      `**${formatXP(progressCurrent)} / ${formatXP(progressMax)}**`,
    ].join("\n");
  }

  // build name with new tier formatting
  const fieldName = isMaxed
    ? `${itemEmoji} **${collectionDef.name}** **(MAX)**`
    : `${itemEmoji} **${collectionDef.name}** (Tier ${currentTierLevel}/${maxTier})`;
  return {
    name: fieldName,
    value: displayValue,
    inline: true,
    estimatedSize:
      (itemEmoji + collectionDef.name + fieldName.length).length +
      displayValue.length +
      20,
  };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("collections")
    .setDescription("View your collection progress")
    .addStringOption(
      (
        option // Add category option
      ) =>
        option
          .setName("category")
          .setDescription("Select a specific collection category to view")
          .setRequired(true) // Make the category required
          .setAutocomplete(true)
    ),
  async execute(interaction) {
    try {
      await interaction.deferReply();
      const playerData = await getPlayerData(interaction.user.id);

      if (!playerData) {
        return interaction.editReply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Update Disblock XP based on current progress
      try {
        const {
          checkAndNotifyDisblockXP,
        } = require("../utils/disblockXpSystem");
        await checkAndNotifyDisblockXP(interaction.user.id, interaction);
      } catch (xpError) {
        console.error("[Collections] Error updating Disblock XP:", xpError);
        // Continue with collections display even if XP update fails
      }

      // Create a minimal character object with only needed properties
      const character = {
        collections: playerData.collections || {},
        rank: playerData.rank,
        name: playerData.name,
      };

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.editReply({
          content:
            "You don't have permission to use this command (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const selectedCategoryKey = interaction.options.getString("category");

      // Find the category key case-insensitively from COLLECTIONS keys to get the correct casing for display
      const categoryKey = Object.keys(COLLECTIONS).find(
        (key) => key.toLowerCase() === selectedCategoryKey.toLowerCase()
      );

      if (!categoryKey) {
        return interaction.reply({
          content: `Invalid category "${selectedCategoryKey}". Please choose a valid category.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Use the same format as showCategoryOverview
      await this.showCategoryOverview(interaction, categoryKey, character);
    } catch (error) {
      console.error("[collections.js] Error in execute:", error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: "An error occurred while processing your request.",
            flags: [MessageFlags.Ephemeral],
          });
        } else {
          await interaction.followUp({
            content: "An error occurred while processing your request.",
            flags: [MessageFlags.Ephemeral],
          });
        }
      } catch (e) {
        console.error("[collections.js] Error sending error message:", e);
      }
    }
  },

  // Handle select menu interactions
  async handleSelectMenu(interaction) {
    try {
      const customId = interaction.customId;
      if (!customId.startsWith("collections_select_")) return;

      const categoryKey = customId.replace("collections_select_", "");
      const selectedItemKey = interaction.values[0];

      await interaction.deferUpdate();

      const playerData = await getPlayerData(interaction.user.id, [
        "collections",
        "rank",
        "name",
      ]);

      if (!playerData) {
        return interaction.followUp({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const character = {
        collections: playerData.collections || {},
        rank: playerData.rank,
        name: playerData.name,
      };

      await this.showCollectionRewards(
        interaction,
        categoryKey,
        selectedItemKey,
        character
      );
    } catch (error) {
      console.error("[Collections] Error in handleSelectMenu:", error);
      await interaction.followUp({
        content: "An error occurred while processing your request.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },

  // Handle button interactions
  async handleButton(interaction) {
    try {
      const customId = interaction.customId;

      // Handle back button
      if (customId.startsWith("collections_back_")) {
        const categoryKey = customId.split("_")[2];

        if (!interaction.deferred && !interaction.replied) {
          await interaction.deferUpdate();
        }

        const playerData = await getPlayerData(interaction.user.id, [
          "collections",
          "rank",
          "name",
        ]);

        if (!playerData) {
          return interaction.followUp({
            content:
              "You don't have a character yet! Visit the setup channel to create one.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const character = {
          collections: playerData.collections || {},
          rank: playerData.rank,
          name: playerData.name,
        };

        // Show the category overview again
        await this.showCategoryOverview(interaction, categoryKey, character);
        return;
      }

      // Handle pagination buttons
      if (customId.startsWith("collections_page_")) {
        const parts = customId.split("_");
        const categoryKey = parts[2];
        const page = parseInt(parts[3], 10);

        if (!interaction.deferred && !interaction.replied) {
          await interaction.deferUpdate();
        }

        const playerData = await getPlayerData(interaction.user.id, [
          "collections",
          "rank",
          "name",
        ]);

        if (!playerData) {
          return interaction.followUp({
            content:
              "You don't have a character yet! Visit the setup channel to create one.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const character = {
          collections: playerData.collections || {},
          rank: playerData.rank,
          name: playerData.name,
        };

        // Show the category overview with the requested page
        await this.showCategoryOverview(
          interaction,
          categoryKey,
          character,
          page
        );
        return;
      }
    } catch (error) {
      console.error("[Collections] Error in handleButton:", error);
      await interaction.followUp({
        content: "An error occurred while processing your request.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },

  // Show collection rewards for a specific item
  async showCollectionRewards(interaction, categoryKey, itemKey, character) {
    const collectionDef = COLLECTIONS[categoryKey][itemKey];
    if (!collectionDef) return;

    const rankPrefix = getRankPrefix(character);
    const categoryEmoji = skillEmojis[categoryKey.toLowerCase()] || "❓";
    const itemEmoji =
      collectionDef.emoji ||
      configManager.getItem(collectionDef.itemKey || itemKey)?.emoji ||
      "❓";

    // Get player's current progress for this collection
    const currentAmount = character.collections[itemKey] || 0;
    let currentTierLevel = 0;
    collectionDef.tiers.forEach((tier) => {
      if (currentAmount >= tier.amount) {
        currentTierLevel = Math.max(currentTierLevel, tier.level);
      }
    });

    // Find next tier
    const nextTier = collectionDef.tiers.find(
      (tier) => tier.level === currentTierLevel + 1
    );
    const nextTierAmount = nextTier ? nextTier.amount : 0;

    // Calculate progress for current tier
    const progressStartAmount =
      currentTierLevel > 0
        ? collectionDef.tiers.find((tier) => tier.level === currentTierLevel)
            ?.amount || 0
        : 0;
    const progressCurrent = currentAmount - progressStartAmount;
    const progressMax =
      nextTierAmount > 0 ? nextTierAmount - progressStartAmount : 0;

    const progressBarString = createProgressBar(progressCurrent, progressMax, {
      showPercentage: true,
      size: 10,
      useEmojis: true,
    });

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.DARK_GOLD)
      .setTitle(
        `${categoryEmoji} ${rankPrefix}${character.name}'s ${collectionDef.name} Collection Rewards`
      );

    // Show current progress as a field with the new format
    const isMaxed = progressMax === 0;
    let displayValue;
    if (isMaxed) {
      displayValue = [
        `${progressBarString}`,
        `**Total: ${formatXP(currentAmount)}**`,
      ].join("\n");
    } else {
      displayValue = [
        `${progressBarString}`,
        `**${formatXP(progressCurrent)} / ${formatXP(progressMax)}**`,
      ].join("\n");
    }

    // Format the collection name based on max status
    const tierDisplay = isMaxed ? "MAX" : currentTierLevel;

    embed.addFields({
      name: `${itemEmoji} **${collectionDef.name}** (Tier ${tierDisplay})`,
      value: displayValue,
      inline: false,
    });

    // Find all items that are unlocked by this collection
    const itemsData = configManager.getAllItems();
    const rewardsByTier = {};

    for (const [, rewardItemData] of Object.entries(itemsData)) {
      if (rewardItemData.craftingRequirements?.collections?.[itemKey]) {
        const requiredTier =
          rewardItemData.craftingRequirements.collections[itemKey];
        if (!rewardsByTier[requiredTier]) {
          rewardsByTier[requiredTier] = [];
        }
        rewardsByTier[requiredTier].push({
          name: rewardItemData.name,
          emoji: rewardItemData.emoji || "❓",
          rarity: rewardItemData.rarity?.name || "Common",
          type: "recipe",
        });
      }
    }

    // Add special collection rewards (like accessory bag upgrades for redstone)
    if (itemKey === "REDSTONE") {
      const { ACCESSORY_BAG_TIERS } = require("../commands/accessories");

      // Add accessory bag upgrades as rewards
      for (const [tierLevel, tierData] of Object.entries(ACCESSORY_BAG_TIERS)) {
        const tier = parseInt(tierLevel);
        if (tier > 0) {
          // Skip tier 0 (no bag)
          if (!rewardsByTier[tier]) {
            rewardsByTier[tier] = [];
          }
          rewardsByTier[tier].push({
            name: `${tierData.name} (${tierData.slots} Slots)`,
            emoji: "<:accessory_bag:1384159284123664516>",
            rarity: "Special",
            type: "accessory_bag",
          });
        }
      }
    }

    // Add rewards fields
    if (Object.keys(rewardsByTier).length === 0) {
      embed.addFields({
        name: "Collection Rewards",
        value: "*No rewards found for this collection.*",
        inline: false,
      });
    } else {
      // Sort tiers numerically
      const sortedTiers = Object.keys(rewardsByTier).sort(
        (a, b) => parseInt(a) - parseInt(b)
      );

      embed.addFields({
        name: "Collection Rewards",
        value: "Unlock rewards by reaching collection tiers:",
        inline: false,
      });

      for (const tier of sortedTiers) {
        const tierNum = parseInt(tier);
        const tierAmount =
          collectionDef.tiers.find((t) => t.level === tierNum)?.amount || 0;
        const isUnlocked = currentTierLevel >= tierNum;
        const statusEmoji = isUnlocked ? "✅" : "🔒";

        let rewardsText = "";
        for (const reward of rewardsByTier[tier]) {
          if (reward.type === "accessory_bag") {
            rewardsText += `${reward.emoji} **${reward.name}**\n`;
          } else {
            rewardsText += `${reward.emoji} ${reward.name}\n`;
          }
        }

        embed.addFields({
          name: `${statusEmoji} Tier ${tier} (${formatXP(tierAmount)} ${collectionDef.name})`,
          value: rewardsText || "*No rewards*",
          inline: true,
        });

        // Add spacer after every 2 tiers for better layout
        if (sortedTiers.indexOf(tier) % 2 !== 0) {
          embed.addFields({
            name: "\u200b",
            value: "\u200b",
            inline: true,
          });
        }
      }

      // If we have an odd number of tiers, add an empty field to maintain layout
      if (sortedTiers.length % 2 !== 0) {
        embed.addFields({
          name: "\u200b",
          value: "\u200b",
          inline: true,
        });
      }
    }

    const backButton = new ButtonBuilder()
      .setCustomId(`collections_back_${categoryKey}`)
      .setLabel("← Back to Category")
      .setStyle(ButtonStyle.Secondary);

    const category = COLLECTIONS[categoryKey];
    const selectMenuOptions = [];
    for (const otherItemKey in category) {
      if (
        typeof category[otherItemKey] !== "object" ||
        !category[otherItemKey].tiers
      ) {
        continue;
      }
      const otherCollectionDef = category[otherItemKey];
      const otherItemEmoji =
        otherCollectionDef.emoji ||
        configManager.getItem(otherCollectionDef.itemKey || otherItemKey)
          ?.emoji ||
        "❓";
      selectMenuOptions.push({
        label: otherCollectionDef.name,
        value: otherItemKey,
        emoji: otherItemEmoji,
        description: `View rewards for ${otherCollectionDef.name} collection`,
      });
    }

    const components = [new ActionRowBuilder().addComponents(backButton)];

    if (selectMenuOptions.length > 0) {
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`collections_select_${categoryKey}`)
        .setPlaceholder("Select another collection to view rewards")
        .addOptions(selectMenuOptions.slice(0, 25));

      components.push(new ActionRowBuilder().addComponents(selectMenu));
    }

    await interaction.editReply({ embeds: [embed], components });
  },

  async showCategoryOverview(interaction, categoryKey, character, page = 0) {
    const rankPrefix = getRankPrefix(character);
    const categoryDisplayName =
      categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1).toLowerCase();
    const categoryEmoji = skillEmojis[categoryKey.toLowerCase()] || "❓";

    const playerCollections = character.collections || {};
    const category = COLLECTIONS[categoryKey];

    // Process all collections in this category
    const allCollectionFields = [];
    for (const itemKey in category) {
      if (typeof category[itemKey] !== "object" || !category[itemKey].tiers) {
        continue;
      }
      const collectionDef = category[itemKey];
      const currentAmount = playerCollections[itemKey] || 0;
      const itemEmoji =
        collectionDef.emoji ||
        configManager.getItem(collectionDef.itemKey || itemKey)?.emoji ||
        "❓";

      const fieldData = createCollectionFieldData(
        collectionDef,
        itemKey,
        currentAmount,
        itemEmoji
      );
      allCollectionFields.push(fieldData);
    }

    // Smart pagination - calculate how many collections fit per page
    const MAX_EMBED_SIZE = 5500; // Leave some buffer under 6000
    const baseEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.DARK_GOLD)
      .setTitle(
        `${categoryEmoji} ${rankPrefix}${character.name}'s ${categoryDisplayName} Collections`
      );

    const baseSize = estimateEmbedSize(baseEmbed);

    // Calculate pages
    const pages = [];
    let currentPageFields = [];
    let currentPageSize = baseSize;

    for (let i = 0; i < allCollectionFields.length; i++) {
      const field = allCollectionFields[i];
      const fieldSize = field.estimatedSize;

      // Check if adding this field would exceed the limit
      if (
        currentPageSize + fieldSize > MAX_EMBED_SIZE &&
        currentPageFields.length > 0
      ) {
        // Start a new page
        pages.push([...currentPageFields]);
        currentPageFields = [field];
        currentPageSize = baseSize + fieldSize;
      } else {
        // Add to current page
        currentPageFields.push(field);
        currentPageSize += fieldSize;
      }
    }

    // Add the last page if it has content
    if (currentPageFields.length > 0) {
      pages.push(currentPageFields);
    }

    // Ensure we have at least one page
    if (pages.length === 0) {
      pages.push([]);
    }

    // Get the current page and add proper spacers for 2-column layout
    const totalPages = pages.length;
    const currentPage = Math.max(0, Math.min(page, totalPages - 1));
    const pageFields = pages[currentPage] || [];

    // Add spacers to force 2-column layout (Discord shows 3 per row, so we need spacers)
    const finalFields = [];
    for (let i = 0; i < pageFields.length; i++) {
      finalFields.push(pageFields[i]);

      // Add spacer after every 2 fields to force new row (creating 2-column effect)
      if ((i + 1) % 2 === 0) {
        finalFields.push({
          name: "\u200b",
          value: "\u200b",
          inline: true,
          estimatedSize: 20,
        });
      }
    }

    // Create the embed for this page
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.DARK_GOLD)
      .setTitle(
        `${categoryEmoji} ${rankPrefix}${character.name}'s ${categoryDisplayName} Collections`
      );

    if (totalPages > 1) {
      embed.setFooter({ text: `Page ${currentPage + 1} of ${totalPages}` });
    }

    // Add fields to embed (remove estimatedSize property)
    const embedFields = finalFields.map((field) => ({
      name: field.name,
      value: field.value,
      inline: field.inline,
    }));

    if (embedFields.length > 0) {
      embed.addFields(embedFields);
    } else {
      embed.setDescription("No collections found in this category.");
    }

    // Create components
    const components = [];

    // Add pagination buttons if needed
    if (totalPages > 1) {
      const paginationRow = new ActionRowBuilder();

      paginationRow.addComponents(
        new ButtonBuilder()
          .setCustomId(
            `collections_page_${categoryKey}_${Math.max(0, currentPage - 1)}`
          )
          .setLabel("Previous")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(currentPage === 0),
        new ButtonBuilder()
          .setCustomId(
            `collections_page_${categoryKey}_${Math.min(totalPages - 1, currentPage + 1)}`
          )
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(currentPage === totalPages - 1)
      );

      components.push(paginationRow);
    }

    // Create select menu for collection details
    const selectMenuOptions = [];
    for (const itemKey in category) {
      if (typeof category[itemKey] !== "object" || !category[itemKey].tiers) {
        continue;
      }
      const collectionDef = category[itemKey];
      const itemEmoji =
        collectionDef.emoji ||
        configManager.getItem(collectionDef.itemKey || itemKey)?.emoji ||
        "❓";
      selectMenuOptions.push({
        label: collectionDef.name,
        value: itemKey,
        emoji: itemEmoji,
        description: `View rewards for ${collectionDef.name} collection`,
      });
    }

    if (selectMenuOptions.length > 0) {
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`collections_select_${categoryKey}`)
        .setPlaceholder("Select a collection to view rewards")
        .addOptions(selectMenuOptions.slice(0, 25));

      components.push(new ActionRowBuilder().addComponents(selectMenu));
    }

    await interaction.editReply({ embeds: [embed], components });
  },

  async autocomplete(interaction) {
    const focusedOption = interaction.options.getFocused(true);
    let choices = [];

    if (focusedOption.name === "category") {
      choices = Object.keys(COLLECTIONS) // Get category names (MINING, FARMING, etc.)
        .filter((key) =>
          key.toLowerCase().startsWith(focusedOption.value.toLowerCase())
        )
        .map((key) => ({
          name: key.charAt(0).toUpperCase() + key.slice(1).toLowerCase(),
          value: key,
        }));
    }

    await interaction.respond(choices.slice(0, 25));
  },
};
