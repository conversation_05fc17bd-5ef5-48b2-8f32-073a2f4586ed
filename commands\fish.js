const { SlashCommandBuilder } = require("discord.js");
const { handleSkillActionExecution } = require("../utils/skillActionUtils");
const { fishingSkillConfig } = require("../data/skillConfigs");

// conditional imports for worker bot compatibility
const isWorkerBot = process.env.IS_WORKER_BOT === "true";
const playerDataModule = isWorkerBot
  ? require("../utils/workerDatabaseProxy")
  : require("../utils/playerDataManager");

async function handleFishAction(
  interaction,
  _resourceKey,
  amount,
  _isAgainCommandOrResumption = false,
  originalTotalAmountFromResumption = null,
  character,
  wasMax = false
) {
  if (!character) {
    console.error("[handleFishAction] Character object is missing!");
    return;
  }

  const isResumption = interaction.isResumption || false;
  let actualAmount = amount;

  if (isResumption) {
    actualAmount = originalTotalAmountFromResumption;
  }

  // Note: Bait consumption is now handled by the fishingSkillConfig.consumeResourceFn
  // The enhanced bait manager will automatically select and consume the appropriate bait

  return handleSkillActionExecution(
    interaction,
    character,
    null,
    actualAmount,
    isResumption ? false : _isAgainCommandOrResumption,
    fishingSkillConfig,
    wasMax
  );
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("fish")
    .setDescription("Go fishing in the current region's waters")
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'Number of repetitions (e.g., 5, or type "max"). Defaults to your max allowed amount.'
        )
        .setRequired(false)
    ),

  async execute(interaction) {
    // Fish command doesn't use resourceKey - amount is the only parameter
    const amountInput = interaction.options.getString("amount");
    let amount = 1;
    let wasMax = false;

    if (amountInput) {
      if (amountInput.toLowerCase() === "max") {
        // Calculate max allowed amount for fishing
        const { getPlayerData, getPlayerSkillLevel } = playerDataModule;
        const { calculateMaxActionAmount } = require("../utils/skillLimits");

        const character = await getPlayerData(interaction.user.id);
        if (!character) {
          return interaction.reply({
            content:
              "Cannot find your character data. Visit the setup channel.",
            ephemeral: true,
          });
        }

        const skillLevel = getPlayerSkillLevel(character, "fishing") || 0;
        amount = calculateMaxActionAmount(skillLevel);
        wasMax = true;
      } else {
        const parsedAmount = parseInt(amountInput, 10);
        if (isNaN(parsedAmount) || parsedAmount <= 0) {
          return interaction.reply({
            content: "Please enter a valid positive number or 'max'.",
            ephemeral: true,
          });
        }
        amount = parsedAmount;
      }
    }

    // Delegate to worker bot
    const { workerManager } = require("../utils/workerManager");
    await workerManager.delegateAction(
      interaction,
      "fish",
      amount,
      null, // fishing doesn't use resourceKey
      wasMax
    );
  },

  // handle fishing button interactions (bait swap, bait selection)
  async handleButton(interaction) {
    const {
      handleBaitSwapInteraction,
      handleBaitSelectionInteraction,
    } = require("../utils/fishingButtons");

    if (interaction.customId.startsWith("bait_swap:")) {
      return await handleBaitSwapInteraction(interaction);
    }

    if (interaction.customId.startsWith("bait_select:")) {
      return await handleBaitSelectionInteraction(interaction);
    }

    return false; // not handled
  },

  handleFishAction,
  async autocomplete() {
    return [];
  },
};
