const { dbAll, dbRun } = require("../utils/dbUtils");

module.exports = {
  version: 30,
  description:
    "Normalize all item keys in player_inventory_items to uppercase for correct inventory display.",
  async up() {
    console.log(
      "[Migration 30] Starting normalization of inventory item keys...",
    );
    const rows = await dbAll(
      "SELECT discord_id, item_name FROM player_inventory_items",
      [],
    );
    let updated = 0;
    for (const row of rows) {
      const upperKey = row.item_name.toUpperCase();
      if (row.item_name !== upperKey) {
        await dbRun(
          "UPDATE player_inventory_items SET item_name = ? WHERE discord_id = ? AND item_name = ?",
          [upperKey, row.discord_id, row.item_name],
        );
        updated++;
      }
    }
    console.log(
      `[Migration 30] Normalized ${updated} inventory item keys to uppercase.`,
    );
  },
  async down() {
    // No rollback needed for normalization
    console.log("[Migration 30] No rollback for normalization.");
  },
};
