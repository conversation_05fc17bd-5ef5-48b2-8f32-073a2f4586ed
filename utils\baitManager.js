const { getInventory, updateInventoryAtomically } = require("./inventory");

/**
 * Manages bait consumption for fishing actions with optimized database operations
 */
class BaitManager {
  constructor() {
    this.baitCache = new Map(); // userId -> { amount, lastUpdated }
    this.pendingConsumption = new Map(); // userId -> amount consumed since last DB write
    this.flushInterval = 30000; // Flush every 30 seconds
    this.maxPendingConsumption = 10; // Flush after 10 bait consumed
    this.timers = new Map(); // userId -> timer
  }

  /**
   * Checks if user has bait and reserves it for consumption
   * @param {string} userId - The user ID
   * @returns {Promise<boolean>} - True if bait is available and reserved
   */
  async reserveBait(userId) {
    try {
      // Check cache first
      let cachedBait = this.baitCache.get(userId);

      // If cache is stale or doesn't exist, fetch from database
      if (!cachedBait || this.isCacheStale(cachedBait)) {
        const inventory = await getInventory(userId);
        const actualBaitAmount = inventory.items.FISH_BAIT || 0;

        cachedBait = {
          amount: actualBaitAmount,
          lastUpdated: Date.now(),
        };
        this.baitCache.set(userId, cachedBait);
      }

      // Check if user has bait (accounting for pending consumption)
      const pendingConsumption = this.pendingConsumption.get(userId) || 0;
      const availableBait = cachedBait.amount - pendingConsumption;

      if (availableBait > 0) {
        // Reserve bait by adding to pending consumption
        this.pendingConsumption.set(userId, pendingConsumption + 1);

        // Check if we should flush consumption to database
        if (pendingConsumption + 1 >= this.maxPendingConsumption) {
          await this.flushConsumption(userId);
        } else {
          // Set timer to flush later
          this.setFlushTimer(userId);
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error(
        `[BaitManager] Error reserving bait for user ${userId}:`,
        error
      );
      // On error, fall back to individual database check
      return await this.fallbackBaitCheck(userId);
    }
  }

  /**
   * Flushes pending bait consumption to database
   * @param {string} userId - The user ID
   */
  async flushConsumption(userId) {
    const pendingConsumption = this.pendingConsumption.get(userId);
    if (!pendingConsumption || pendingConsumption === 0) return;

    try {
      // Clear timer
      if (this.timers.has(userId)) {
        clearTimeout(this.timers.get(userId));
        this.timers.delete(userId);
      }

      // Update database
      await updateInventoryAtomically(
        userId,
        0, // coinsToAdd
        [{ itemKey: "FISH_BAIT", amount: -pendingConsumption }],
        [], // equipmentToAdd
        [], // equipmentIdsToRemove
        0, // bankCoinsToAdd
        [], // equipmentDataUpdates
        null, // islandJsonString
        null, // collectionsJsonString
        true // useExistingTransaction
      );

      // Update cache
      const cachedBait = this.baitCache.get(userId);
      if (cachedBait) {
        cachedBait.amount -= pendingConsumption;
        cachedBait.lastUpdated = Date.now();
      }

      // Clear pending consumption
      this.pendingConsumption.delete(userId);
    } catch (error) {
      console.error(
        `[BaitManager] Error flushing consumption for user ${userId}:`,
        error
      );
      // On error, reset cache to force fresh fetch next time
      this.baitCache.delete(userId);
      this.pendingConsumption.delete(userId);
    }
  }

  /**
   * Sets a timer to flush consumption later
   * @param {string} userId - The user ID
   */
  setFlushTimer(userId) {
    if (this.timers.has(userId)) {
      clearTimeout(this.timers.get(userId));
    }

    const timer = setTimeout(() => {
      this.flushConsumption(userId);
    }, this.flushInterval);

    this.timers.set(userId, timer);
  }

  /**
   * Checks if cache is stale
   * @param {object} cachedBait - The cached bait data
   * @returns {boolean} - True if stale
   */
  isCacheStale(cachedBait) {
    const cacheMaxAge = 60000; // 1 minute
    return Date.now() - cachedBait.lastUpdated > cacheMaxAge;
  }

  /**
   * Fallback method for individual bait check and consumption
   * @param {string} userId - The user ID
   * @returns {Promise<boolean>} - True if bait was consumed
   */
  async fallbackBaitCheck(userId) {
    try {
      const inventory = await getInventory(userId);
      const baitAmount = inventory.items.FISH_BAIT || 0;

      if (baitAmount > 0) {
        await updateInventoryAtomically(
          userId,
          0,
          [{ itemKey: "FISH_BAIT", amount: -1 }],
          [],
          [],
          0,
          [],
          null,
          null,
          true
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error(
        `[BaitManager] Error in fallback bait check for user ${userId}:`,
        error
      );
      return false;
    }
  }

  /**
   * Flushes all pending consumptions
   */
  async flushAllConsumptions() {
    const userIds = Array.from(this.pendingConsumption.keys());
    const promises = userIds.map((userId) => this.flushConsumption(userId));
    await Promise.all(promises);
  }

  /**
   * Forces immediate flush for a specific user
   * @param {string} userId - The user ID
   */
  async forceFlush(userId) {
    await this.flushConsumption(userId);
  }

  /**
   * Clears all cache and timers
   */
  cleanup() {
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();
    this.baitCache.clear();
    this.pendingConsumption.clear();
  }

  /**
   * Invalidates cache for a specific user (call when user inventory changes externally)
   * @param {string} userId - The user ID
   */
  invalidateCache(userId) {
    this.baitCache.delete(userId);
  }
}

// Create singleton instance
const baitManager = new BaitManager();

module.exports = {
  BaitManager,
  baitManager,
};
