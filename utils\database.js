const sqlite3 = require("sqlite3").verbose();
const path = require("path");
const fs = require("fs");

const dbPath = path.resolve(__dirname, "..", "data");
const dbFile = path.join(dbPath, "database.db");

// Ensure the data directory exists
if (!fs.existsSync(dbPath)) {
  fs.mkdirSync(dbPath, { recursive: true });
}

// Initialize the database connection with optimized settings
const shouldLogDbInit =
  process.env.IS_MAIN_BOT === "true" && process.env.DB_INIT_LOGS === "true";
const db = new sqlite3.Database(
  dbFile,
  sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE,
  (err) => {
    if (err) {
      console.error("Error opening database:", err.message);
    } else {
      if (process.env.IS_MAIN_BOT === "true")
        console.log("Connected to the SQLite database.");
      initializeDatabase(); // Call initialization after connection is established
    }
  }
);

// Function to initialize database tables
function initializeDatabase() {
  db.serialize(() => {
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log("Initializing database tables...");

    // Enable Foreign Key support
    db.run("PRAGMA foreign_keys = ON;", (err) => {
      if (err) console.error("Error enabling foreign keys:", err.message);
    });

    // OPTIMIZATION: Configure SQLite for better performance under load
    db.run("PRAGMA busy_timeout = 10000;", (err) => {
      if (err) console.error("Error setting busy timeout:", err.message);
      else if (shouldLogDbInit)
        console.log("SQLite busy timeout set to 10 seconds.");
    });

    // Use WAL mode for better concurrency
    db.run("PRAGMA journal_mode = WAL;", (err) => {
      if (err) console.error("Error setting journal mode:", err.message);
      else if (shouldLogDbInit)
        console.log("SQLite journal mode set to WAL for better concurrency.");
    });

    // Add additional SQLite optimizations for Windows
    db.run("PRAGMA synchronous = NORMAL;", (err) => {
      if (err) console.error("Error setting synchronous mode:", err.message);
      else if (shouldLogDbInit)
        console.log("SQLite synchronous mode set to NORMAL.");
    });

    db.run("PRAGMA temp_store = MEMORY;", (err) => {
      if (err) console.error("Error setting temp store:", err.message);
      else if (shouldLogDbInit) console.log("SQLite temp store set to MEMORY.");
    });

    // OPTIMIZATION: Increase cache size for better performance
    db.run("PRAGMA cache_size = -64000;", (err) => {
      if (err) console.error("Error setting cache size:", err.message);
      else if (shouldLogDbInit) console.log("SQLite cache size set to 64MB.");
    });

    // OPTIMIZATION: Enable memory-mapped I/O for better performance
    db.run("PRAGMA mmap_size = 268435456;", (err) => {
      if (err) console.error("Error setting mmap size:", err.message);
      else if (shouldLogDbInit) console.log("SQLite mmap size set to 256MB.");
    });

    // Players Table (Modified with JSON columns)
    db.run(
      `
            CREATE TABLE IF NOT EXISTS players (
                discord_id TEXT PRIMARY KEY,    -- Stores Discord id of the Player
                name TEXT UNIQUE NOT NULL,      -- Stores Character name of the player
                current_region TEXT,            -- Stores current region of the player
                current_health REAL DEFAULT 100,    -- Stores current health of the player
                coins INTEGER DEFAULT 0,    -- Stores coins of the Player
                bank INTEGER DEFAULT 0,     -- Stores bank of the player
                rank TEXT DEFAULT 'MEMBER', -- Stores rank of the player
                skills_json TEXT,         -- Stores skills object as JSON
                stats_json TEXT,          -- Stores stats object as JSON
                inventory_items_json TEXT,-- Stores inventory items object as JSON
                equipment_json TEXT,      -- Stores equipment array as JSON
                last_actions_json TEXT,   -- Stores last actions object as JSON
                collections_json TEXT,    -- Stores collections object as JSON
                pets_json TEXT,           -- Stores pets array as JSON
                active_pet_id TEXT,       -- Stores the UUID of the active pet
                island_json TEXT,         -- Stores island object (placed minions, etc.) as JSON
                visiting_island_owner_id TEXT, -- Stores ID of island owner being visited
                minion_storage_json TEXT, -- Stores minion storage array as JSON
                crafted_minions_json TEXT, -- Stores crafted minions array as JSON
                settings_json TEXT,       -- Stores player settings as JSON
                -- ++ New Player Columns for Baker/Cakes ++
                cakeBag TEXT,             -- Stores cakeBag array as JSON (Corrected Name)
                lastClaimedYear INTEGER -- Stores the last SB year a cake was claimed for (Corrected Name)
                -- -- End New Columns --
            );
        `,
      (err) => {
        if (err) {
          console.error("Error creating players table:", err.message);
        } else {
          if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
            console.log("Players table checked/created with JSON columns.");
          // Add new columns if they don't exist (simple alter for compatibility)
          db.run(
            "ALTER TABLE players ADD COLUMN cakeBag TEXT DEFAULT '[]'",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error("Error adding cakeBag column:", alterErr.message);
              }
            }
          );
          db.run(
            "ALTER TABLE players ADD COLUMN lastClaimedYear INTEGER DEFAULT 0",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error(
                  "Error adding lastClaimedYear column:",
                  alterErr.message
                );
              }
            }
          );
          db.run(
            "ALTER TABLE players ADD COLUMN disblock_xp INTEGER DEFAULT 0",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error(
                  "Error adding disblock_xp column:",
                  alterErr.message
                );
              }
            }
          );
          db.run(
            "ALTER TABLE players ADD COLUMN disblockMilestones TEXT DEFAULT NULL",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error(
                  "Error adding disblockMilestones column:",
                  alterErr.message
                );
              }
            }
          );
          db.run(
            "ALTER TABLE players ADD COLUMN accessory_power TEXT DEFAULT NULL",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error(
                  "Error adding accessory_power column:",
                  alterErr.message
                );
              }
            }
          );
          // Add slayer-related columns
          db.run(
            "ALTER TABLE players ADD COLUMN active_slayer_quest TEXT DEFAULT NULL",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error(
                  "Error adding active_slayer_quest column:",
                  alterErr.message
                );
              }
            }
          );
          db.run(
            "ALTER TABLE players ADD COLUMN slayer_xp_json TEXT DEFAULT '{}'",
            (alterErr) => {
              if (
                alterErr &&
                !alterErr.message.includes("duplicate column name")
              ) {
                console.error(
                  "Error adding slayer_xp_json column:",
                  alterErr.message
                );
              }
            }
          );
        }
      }
    );

    // --- Metadata Table for Versioning ---
    db.run(
      `
            CREATE TABLE IF NOT EXISTS db_metadata (
                key TEXT PRIMARY KEY,
                value INTEGER NOT NULL
            );
        `,
      (err) => {
        if (err)
          console.error("Error creating db_metadata table:", err.message);
        else {
          if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
            console.log("db_metadata table checked/created.");
          // Ensure initial version is set if table is new or empty
          db.run(
            "INSERT OR IGNORE INTO db_metadata (key, value) VALUES (?, ?)",
            ["schema_version", 0],
            (insertErr) => {
              if (insertErr)
                console.error(
                  "Error setting initial schema version:",
                  insertErr.message
                );
              else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
                console.log("Initial schema version ensured.");
            }
          );
        }
      }
    );

    // Player Inventory Items Table (Stackable)
    db.run(
      `
            CREATE TABLE IF NOT EXISTS player_inventory_items (
                discord_id TEXT NOT NULL,
                item_name TEXT NOT NULL,
                amount INTEGER NOT NULL,
                PRIMARY KEY (discord_id, item_name),
                FOREIGN KEY (discord_id) REFERENCES players(discord_id) ON DELETE CASCADE
            );
        `,
      (err) => {
        if (err)
          console.error(
            "Error creating player_inventory_items table:",
            err.message
          );
        else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
          console.log("Player Inventory Items table checked/created.");
      }
    );

    // Player Equipment Table (Unique Items)
    db.run(
      `
            CREATE TABLE IF NOT EXISTS player_equipment (
                equipment_id TEXT PRIMARY KEY,
                discord_id TEXT NOT NULL,
                item_key TEXT NOT NULL,
                is_equipped INTEGER DEFAULT 0,
                FOREIGN KEY (discord_id) REFERENCES players(discord_id) ON DELETE CASCADE
            );
        `,
      (err) => {
        if (err)
          console.error("Error creating player_equipment table:", err.message);
        else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
          console.log("Player Equipment table checked/created.");
      }
    );

    // Create indexes for better query performance
    db.run(
      "CREATE INDEX IF NOT EXISTS idx_active_actions_user_id ON active_actions (user_id);",
      (err) => {
        if (err) console.error("Error creating user_id index:", err.message);
        else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
          console.log("User ID index created for active_actions.");
      }
    );

    db.run(
      "CREATE INDEX IF NOT EXISTS idx_active_actions_start_timestamp ON active_actions (start_timestamp);",
      (err) => {
        if (err)
          console.error("Error creating start_timestamp index:", err.message);
        else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
          console.log("Start timestamp index created for active_actions.");
      }
    );

    db.run(
      "CREATE INDEX IF NOT EXISTS idx_player_inventory_items_discord_id ON player_inventory_items (discord_id);",
      (err) => {
        if (err)
          console.error(
            "Error creating inventory discord_id index:",
            err.message
          );
        else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
          console.log("Discord ID index created for player_inventory_items.");
      }
    );

    db.run(
      "CREATE INDEX IF NOT EXISTS idx_player_equipment_discord_id ON player_equipment (discord_id);",
      (err) => {
        if (err)
          console.error(
            "Error creating equipment discord_id index:",
            err.message
          );
        else if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
          console.log("Discord ID index created for player_equipment.");
      }
    );

    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log("Database initialization complete (using JSON columns).");

    // database maintenance removed from startup to prevent lock conflicts
    // these operations should be run manually during maintenance windows if needed
  });
}

// Export the database connection object
module.exports = db;
