function getSeaCreatureUnlocksForLevel(skill, level) {
  if (skill !== "fishing") return [];

  const unlocks = [];

  Object.values(configManager.getAllMobs()).forEach((mob) => {
    if (mob.seaCreature && mob.fishingLevelReq === level) {
      unlocks.push(`${mob.emoji} **${mob.name}**`);
    }
  });

  return unlocks;
}

function getPotionUnlocksForLevel(skill, level) {
  if (skill !== "alchemy") return [];

  const allItems = configManager.getAllItems();
  const unlockedPotions = [];

  for (const [, itemData] of Object.entries(allItems)) {
    if (
      itemData.sourceSkill === "Alchemy" &&
      itemData.requiredLevel === level &&
      itemData.brewing
    ) {
      unlockedPotions.push(`${itemData.emoji || "🧪"} **${itemData.name}**`);
    }
  }

  return unlockedPotions;
}

const { EmbedBuilder } = require("discord.js");
const { calculateLevelFromTotalXp } = require("../data/petLeveling");
const { MAX_PET_LEVEL } = require("../data/petLeveling");
const { getPetLevel, getPetTotalExp } = require("./petUtils");
const { ITEM_RARITY, EMBED_COLORS } = require("../gameConfig");
const configManager = require("./configManager");
const { skillEmojis } = require("../gameConfig");

const { getLevelFromExp } = require("./expFunctions");
const {
  getSkillRewardsForLevel,
  formatRewardsText,
} = require("../data/skillRewards");

// ... other imports ...

/**
 * Calculates the Taming EXP gained based on Pet EXP gained.
 * Uses a fixed conversion rate.
 * @param {number} petExpGained The amount of EXP the active pet gained.
 * @returns {number} The amount of Taming EXP earned.
 */
function getTamingExpFromPetExp(petExpGained) {
  const TAMING_EXP_RATE = 0.4; // Example rate (adjust as needed)
  return petExpGained * TAMING_EXP_RATE;
}

/**
 * Adds skill EXP and handles level ups. Now stores EXP as REAL.
 * Modifies the passed character object directly.
 * NOTE: This function only handles skill XP, not pet XP. Use addSkillExp from skillExpUtils for full XP handling.
 * @param {object} character - The character object to modify.
 * @param {string} skill - The skill name.
 * @param {number} expGained - The amount of EXP gained.
 * @returns {Promise<object>} Updated skill data and level up embeds.
 */
async function addSkillExpOnly(character, skill, expGained) {
  if (!character) {
    console.error(
      `[addSkillExp] Received null character object for skill ${skill}`
    );
    return { exp: 0.0, levelUpEmbeds: null };
  }
  if (!character.skills) {
    console.warn(
      `[addSkillExp] Character object for ${character.discordId} missing 'skills' property. Initializing.`
    );
    character.skills = {};
  }
  if (!character.skills[skill]) {
    character.skills[skill] = { exp: 0.0 };
  }
  if (
    typeof character.skills[skill].exp !== "number" ||
    isNaN(character.skills[skill].exp)
  ) {
    console.warn(
      `[addSkillExp] Invalid non-numeric EXP found for ${character.discordId}, skill ${skill}. Resetting to 0.0.`
    );
    character.skills[skill].exp = 0.0;
  }

  // Simplified XP logging - only log significant events

  // Calculate old level from exp before adding
  const oldTotalExp = character.skills[skill].exp;
  const oldLevelInfo = getLevelFromExp(oldTotalExp);
  const oldLevel = oldLevelInfo.level;

  // Add exp
  character.skills[skill].exp += expGained;

  // Prevent XP from going below 0 (clamp to minimum of 0)
  if (character.skills[skill].exp < 0) {
    character.skills[skill].exp = 0;
  }

  const newTotalExp = character.skills[skill].exp;
  const newLevelInfo = getLevelFromExp(newTotalExp);
  const newLevel = newLevelInfo.level;

  // Only log level ups, not routine XP gains

  // IMPORTANT: We only store raw experience in the character object, level is always derived
  // Make sure we remove any 'level' property if it exists
  if ("level" in character.skills[skill]) {
    delete character.skills[skill].level;
  }

  const levelUpEmbeds = [];
  // Track already generated level-up embeds to prevent duplicates
  const processedLevels = new Set();
  let totalLevelUpCoinReward = 0; // <-- ADDED: Accumulator for coin rewards

  // For each level gained, generate a level-up embed and apply rewards
  for (let level = oldLevel + 1; level <= newLevel; level++) {
    // Skip if we already processed this level (prevents duplicates)
    if (processedLevels.has(level)) {
      continue;
    }

    processedLevels.add(level);

    // Get rewards for this level using the data-driven configuration
    const { statRewards, specialRewards, coinReward } = getSkillRewardsForLevel(
      skill,
      level
    );

    // Accumulate coin reward if present
    if (coinReward > 0) {
      totalLevelUpCoinReward += coinReward; // <-- MODIFIED: Accumulate instead of applying
      // character.currencies = character.currencies || {}; // No longer needed here
      // if (typeof character.coins !== 'number') character.coins = 0; // No longer needed here
      // character.coins += coinReward; // No longer needed here
    }

    // Apply stat rewards by updating character stats
    for (const reward of statRewards) {
      // Make sure the stat object exists
      if (!character.stats) character.stats = {};
      if (!character.stats[reward.stat]) character.stats[reward.stat] = {};
      if (!character.stats[reward.stat].fromLevels)
        character.stats[reward.stat].fromLevels = 0;

      // Add the reward value to the fromLevels property
      character.stats[reward.stat].fromLevels += reward.value;
    }

    // Format rewards for display
    const formattedRewards = formatRewardsText(
      statRewards,
      specialRewards,
      coinReward,
      skill,
      level
    );

    // Check for sea creature unlocks at this level (for fishing skill only)
    const seaCreatureUnlocks = getSeaCreatureUnlocksForLevel(skill, level);

    // Check for potion unlocks at this level (for alchemy skill only)
    const potionUnlocks = getPotionUnlocksForLevel(skill, level);

    // Create and add the level up embed
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GOLD)
      .setTitle(
        `${skillEmojis[skill] || "⭐"} ${
          skill.charAt(0).toUpperCase() + skill.slice(1)
        } Level Up!`
      )
      .setDescription(
        `<@${character.discordId}> Your ${skill} skill is now level **${level}**`
        //`<@${character.discordId}> Your ${skill} skill is now level \`${level}\`!` // Old format, do not remove
      )
      .addFields({
        name: "Rewards",
        value: formattedRewards,
      });

    // Add sea creature unlocks field if any
    if (seaCreatureUnlocks.length > 0) {
      embed.addFields({
        name: "<:mob_seawalker:1369193266121277470> You can now fish up:",
        value: seaCreatureUnlocks.join("\n"),
        inline: false,
      });
    }

    // Add potion unlocks field if any
    if (potionUnlocks.length > 0) {
      embed.addFields({
        name: "🧪 New Potion Recipes Unlocked:",
        value: potionUnlocks.join("\n"),
        inline: false,
      });
    }

    levelUpEmbeds.push({ embed });
  }

  // Removed verbose final return logging

  // Return the raw exp value and embeds - don't include level in the returned object
  return {
    exp: newTotalExp,
    levelUpEmbeds: levelUpEmbeds.length > 0 ? levelUpEmbeds : null,
    totalLevelUpCoinReward: totalLevelUpCoinReward, // <-- ADDED: Return accumulated coins
    character: character, // Return the updated character object
    skillData: character.skills, // Return the updated skill data
  };
}

function calculateTotalFishingSpeed(character) {
  if (!character) {
    return 0;
  }

  let fishingSpeed = 0;

  // Get base fishing speed from character stats
  if (character.stats?.FISHING_SPEED) {
    fishingSpeed = character.stats.FISHING_SPEED.base || 0;
  }

  // Check for FISH_BAIT in inventory - legacy code that may not be used anymore
  // Modern fishing uses enhancedBaitManager which handles all bait types dynamically
  if (character.inventory) {
    const fishBait = character.inventory.find(
      (item) => item.id === "FISH_BAIT"
    );
    if (fishBait) {
      // Get fishing speed bonus dynamically from item stats
      const configManager = require("./configManager");
      const fishBaitItem = configManager.getItem("FISH_BAIT");
      const fishBaitSpeedBonus = fishBaitItem?.stats?.FISHING_SPEED || 45; // fallback to 45 if item not found
      fishingSpeed += fishBaitSpeedBonus;

      // Note: The actual consumption of the bait should be handled in the fishing command
      // to ensure it's only consumed on successful catches
    }
  }
  return fishingSpeed;
}

/**
 * Adds experience to a specific pet and handles level ups. Stores EXP as REAL.
 * Modifies the character object directly.
 * @param {object} character The character object.
 * @param {string} petId The UUID of the pet.
 * @param {number} expToAdd The amount of EXP to add (can be decimal).
 * @returns {Promise<{leveledUp: boolean, levelUpEmbeds: Array<EmbedBuilder>|null}>}
 */
async function addPetExp(character, petId, expToAdd) {
  if (!character || !character.pets)
    return { leveledUp: false, levelUpEmbeds: null };

  const pet = character.pets.find((p) => p.id === petId);
  if (!pet) return { leveledUp: false, levelUpEmbeds: null };

  // Get current level before adding XP
  const oldLevel = getPetLevel(pet);

  // Migrate legacy pet data to new format
  const currentTotalExp = getPetTotalExp(pet);

  // Add the new XP to total (always allow XP accumulation, even at max level)
  const newTotalExp = currentTotalExp + expToAdd;

  // Update pet to use new format
  pet.totalExp = newTotalExp;

  // Remove legacy fields
  if (pet.exp !== undefined) delete pet.exp;
  if (pet.xp !== undefined) delete pet.xp;
  if (pet.level !== undefined) delete pet.level;

  // Calculate new level
  const newLevel = getPetLevel(pet);
  const petLevelUpOccurred = newLevel > oldLevel;

  const levelUpEmbeds = [];
  if (petLevelUpOccurred) {
    // Only create embed if a level up happened
    const petBaseData = configManager.getItem(pet.petKey);
    const petName = petBaseData?.name || "Pet";
    const petEmoji = petBaseData?.emoji || "❓";
    const rarityColor = ITEM_RARITY[pet.rarity]?.color || "#FFFFFF";

    const petLevelUpEmbed = new EmbedBuilder()
      .setColor(rarityColor)
      .setDescription(
        `<@${character.discordId}> Your ${petEmoji} **${pet.rarity} ${petName}** is now level **${newLevel}**`
      );
    levelUpEmbeds.push(petLevelUpEmbed);
  }

  return {
    leveledUp: petLevelUpOccurred, // True if level changed
    levelUpEmbeds: levelUpEmbeds.length > 0 ? levelUpEmbeds : null,
    newLevel: newLevel,
    newExpInLevel: calculateLevelFromTotalXp(pet.rarity, pet.totalExp)
      .expInLevel,
    petLeveledUp: petLevelUpOccurred, // Flag specifically for pet level ups
    character: character, // Return the updated character object
  };
}

module.exports = {
  addSkillExpOnly,
  getTamingExpFromPetExp,
  calculateTotalFishingSpeed,
  addPetExp,
};
