/**
 * Simple cache for parsed data_json to avoid repeated JSON parsing
 */
class DataJsonCache {
  constructor(maxSize = 1000) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  get(key) {
    if (this.cache.has(key)) {
      // Move to end for LRU
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  clear() {
    this.cache.clear();
  }
}

const dataJsonCache = new DataJsonCache();

/**
 * Parse data_json with caching
 * @param {string|object} dataJson - The data_json to parse
 * @param {string} cacheKey - Unique key for caching (e.g., equipment_id)
 * @returns {object} Parsed data_json
 */
function parseDataJsonCached(dataJson, cacheKey) {
  if (!dataJson) return {};

  // If already an object, return as is
  if (typeof dataJson === "object") {
    return dataJson;
  }

  // Check cache first
  const cached = dataJsonCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  // Parse and cache
  try {
    const parsed = JSON.parse(dataJson);
    dataJsonCache.set(cacheKey, parsed);
    return parsed;
  } catch (error) {
    console.error(`Error parsing data_json for ${cacheKey}:`, error);
    return {};
  }
}

module.exports = {
  parseDataJsonCached,
  dataJsonCache,
};
