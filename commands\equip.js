const { EMBED_COLORS } = require("../gameConfig.js");
const {
  SlashCommandBuilder,
  MessageFlags,
  EmbedBuilder,
} = require("discord.js");
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager.js");
const configManager = require("../utils/configManager.js");
const { checkRankPermission } = require("../utils/permissionUtils");
const { equipItemAtomically } = require("../utils/inventory.js");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("equip")
    .setDescription("Equip an item from your storage.")
    .addStringOption((option) =>
      option
        .setName("item")
        .setDescription("The ID of the item you want to equip")
        .setRequired(true)
        .setAutocomplete(true)
    ),
  async execute(interaction) {
    const userId = interaction.user.id;
    try {
      const currentActivity = await getCurrentActivity(userId);
      if (currentActivity) {
        return warnUserBusy(interaction, currentActivity, "equip");
      }

      const character = await getPlayerData(userId);
      if (!character)
        return interaction.reply({
          content: "You don't have a character yet! Visit the setup channel.",
          flags: [MessageFlags.Ephemeral],
        });
      if (!character.inventory?.equipment)
        return interaction.reply({
          content: "You have no equipment to equip.",
          flags: [MessageFlags.Ephemeral],
        });

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.reply({
          content:
            "You don't have permission to use this command (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const itemIdToEquip = interaction.options.getString("item");

      const itemToEquip = character.inventory.equipment.find(
        (eq) => eq.id === itemIdToEquip
      );

      if (!itemToEquip) {
        return interaction.reply({
          content: "Could not find the selected item in your equipment.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      if (itemToEquip.isEquipped) {
        return interaction.reply({
          content: "This item is already equipped.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const allItems = configManager.getAllItems();
      const itemDef = allItems[itemToEquip.itemKey];
      if (!itemDef) {
        return interaction.reply({
          content: "[EQP-CFG-001] Error finding item details.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      let equipSlot = itemDef.equipSlot;

      if (!equipSlot) {
        if (itemDef.type === "ACCESSORY") {
          return interaction.reply({
            content:
              "This item is an accessory and cannot be equipped. Use the accessories system instead.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const subtypeToSlot = {
          SWORD: "WEAPON",
          BOW: "WEAPON",
          PICKAXE: "PICKAXE",
          SHOVEL: "SHOVEL",
          HOE: "HOE",
          ROD: "ROD",
          HELMET: "HELMET",
          CHESTPLATE: "CHESTPLATE",
          LEGGINGS: "LEGGINGS",
          BOOTS: "BOOTS",
          RING: "RING",
          NECKLACE: "NECKLACE",
          CLOAK: "CLOAK",
          BELT: "BELT",
        };
        const slotKey = itemDef.subtype || itemDef.type;

        if (slotKey === "AXE" || slotKey === "FARMING_AXE") {
          if (itemDef.type === "TOOL") {
            equipSlot = "AXE"; // Tool axes go in AXE slot
          } else {
            equipSlot = "WEAPON"; // Weapon axes go in WEAPON slot
          }
        } else if (slotKey && subtypeToSlot[slotKey.toUpperCase()]) {
          equipSlot = subtypeToSlot[slotKey.toUpperCase()];
        } else if (slotKey) {
          equipSlot = slotKey.toUpperCase();
        }
      }
      if (!equipSlot) {
        return interaction.reply({
          content: "This item cannot be equipped (invalid type/subtype).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const { success, message, unequippedItemKey } = await equipItemAtomically(
        userId,
        itemIdToEquip,
        equipSlot
      );

      if (success) {
        const refreshedCharacter = await getPlayerData(userId);
        await recalculateAndSaveStats(userId, refreshedCharacter);
        const equippedItemDef = configManager.getItem(itemToEquip.itemKey);

        let itemName = equippedItemDef?.name || itemToEquip.itemKey;
        if (itemToEquip.data_json) {
          let dataJson;
          try {
            if (typeof itemToEquip.data_json === "string") {
              dataJson = JSON.parse(itemToEquip.data_json);
            } else {
              dataJson = itemToEquip.data_json;
            }

            if (dataJson && dataJson.reforge) {
              const {
                getDynamicReforgeName,
              } = require("../utils/dynamicReforgeStats");
              const dynamicReforgeName = getDynamicReforgeName(
                dataJson.reforge,
                equippedItemDef
              );
              itemName = `${dynamicReforgeName} ${itemName}`;
            }
          } catch (error) {
            console.error(
              `[Equip Command] Error parsing data_json for item ${itemToEquip.id}:`,
              error
            );
          }
        }

        let description = `Equipped ${equippedItemDef?.emoji || "❓"} **${itemName}**.`;
        if (unequippedItemKey) {
          const unequippedItemDef = configManager.getItem(unequippedItemKey);
          description = `Equipped ${equippedItemDef?.emoji || "❓"} **${itemName}** in place of ${unequippedItemDef?.emoji || "❓"} **${unequippedItemDef?.name || unequippedItemKey}**.`;
        }
        const equipEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.LIGHT_GREEN)
          //.setTitle("Item Equipped")
          .setDescription(description);
        await interaction.reply({ embeds: [equipEmbed] });
      } else {
        await interaction.reply({
          content: `Failed to equip: ${message}`,
          flags: [MessageFlags.Ephemeral],
        });
      }
    } catch (error) {
      console.error(`[Equip Command Error] User: ${userId}`, error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: "[EQP-EXE-ERR] An unexpected error occurred.",
            flags: [MessageFlags.Ephemeral],
          });
        } else if (interaction.isRepliable()) {
          await interaction.followUp({
            content: "[EQP-EXE-ERR] An unexpected error occurred.",
            flags: [MessageFlags.Ephemeral],
          });
        }
      } catch (replyError) {
        console.error(
          "[Equip Command] Failed to send error message:",
          replyError
        );
      }
    }
  },
  async autocomplete(interaction) {
    try {
      const character = await getPlayerData(interaction.user.id);
      if (!character || !character.inventory?.equipment)
        return interaction.respond([]);

      const allItemsAutocomplete = configManager.getAllItems();
      const focusedValue = interaction.options.getFocused().toLowerCase();

      const storageItems = character.inventory.equipment
        .filter((eq) => !eq.isEquipped)
        .map((eq) => {
          const itemDetails = allItemsAutocomplete[eq.itemKey];
          return itemDetails
            ? { ...itemDetails, id: eq.id, itemKey: eq.itemKey }
            : null;
        })
        .filter((item) => item !== null)
        .filter((item) => item.type !== "ACCESSORY")
        .filter(
          (item) =>
            item.name.toLowerCase().includes(focusedValue) ||
            item.id.toLowerCase().startsWith(focusedValue)
        )
        .map((item) => {
          let displayName = item.name;
          try {
            const eq = character.inventory.equipment.find(
              (e) => e.id === item.id
            );
            if (eq && eq.data_json) {
              let dataJson = {};
              if (typeof eq.data_json === "string") {
                dataJson = JSON.parse(eq.data_json);
              } else {
                dataJson = eq.data_json;
              }

              if (dataJson.reforge) {
                const {
                  getDynamicReforgeName,
                } = require("../utils/dynamicReforgeStats");
                const dynamicReforgeName = getDynamicReforgeName(
                  dataJson.reforge,
                  item
                );
                displayName = `${dynamicReforgeName} ${item.name}`;
              }
            }
          } catch (error) {
            console.error(
              "[Equip Autocomplete] Error parsing reforge data:",
              error
            );
          }

          return {
            name: `${displayName} (${item.id.slice(0, 4)})`,
            value: item.id,
          };
        })
        .slice(0, 25);

      await interaction.respond(storageItems);
    } catch (autocompleteError) {
      console.error("[Equip Autocomplete Error]:", autocompleteError);
      await interaction.respond([]).catch(() => {});
    }
  },
};
