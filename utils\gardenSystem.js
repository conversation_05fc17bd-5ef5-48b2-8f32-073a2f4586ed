// Garden System - Milestone tracking and Garden XP management

const {
  getPlayerData,
  savePlayerData,
  updatePlayerJsonFields,
} = require("./playerDataManager");
const _configManager = require("./configManager");

// Crop milestone requirements - organized by crop type
const CROP_MILESTONES = {
  // Wheat, <PERSON>umpkin, Mushrooms pattern
  WHEAT: [
    30, 80, 160, 330, 660, 1330, 2660, 5160, 8660, 13660, 20160, 28160, 38160,
    58160, 93160, 143160, 218160, 318160, 493160, 743160, 1093160, 1593160,
    2343160, 3343160, 4643160, 5243160, 8243160, 10543160, 13143160, 16143160,
    19143160, 22143160, 25143160, 28143160, 31143160, 34143160, 37143160,
    40143160, 43143160, 46143160, 49143160, 52143160, 55143160, 58143160,
    61143160, 64143160,
  ],
  PUMPKIN: [
    30, 80, 160, 330, 660, 1330, 2660, 5160, 8660, 13660, 20160, 28160, 38160,
    58160, 93160, 143160, 218160, 318160, 493160, 743160, 1093160, 1593160,
    2343160, 3343160, 4643160, 5243160, 8243160, 10543160, 13143160, 16143160,
    19143160, 22143160, 25143160, 28143160, 31143160, 34143160, 37143160,
    40143160, 43143160, 46143160, 49143160, 52143160, 55143160, 58143160,
    61143160, 64143160,
  ],
  MUSHROOMS: [
    30, 80, 160, 330, 660, 1330, 2660, 5160, 8660, 13660, 20160, 28160, 38160,
    58160, 93160, 143160, 218160, 318160, 493160, 743160, 1093160, 1593160,
    2343160, 3343160, 4643160, 5243160, 8243160, 10543160, 13143160, 16143160,
    19143160, 22143160, 25143160, 28143160, 31143160, 34143160, 37143160,
    40143160, 43143160, 46143160, 49143160, 52143160, 55143160, 58143160,
    61143160, 64143160,
  ],

  // Carrot, Potato pattern
  CARROT: [
    100, 250, 500, 1000, 2500, 5000, 10000, 17500, 27500, 42500, 62500, 87500,
    127500, 197500, 297500, 497500, 747500, 997500, 1497500, 2247500, 3247500,
    4747500, 6747500, 9747500, 13747500, 18747500, 24747500, 31747500, 39747500,
    48747500, 58747500, 68747500, 78747500, 88747500, 98747500, 108747500,
    118747500, 128747500, 138747500, 148747500, 158747500, 168747500, 178747500,
    188747500, 198747500, 208747500,
  ],
  POTATO: [
    100, 250, 500, 1000, 2500, 5000, 10000, 17500, 27500, 42500, 62500, 87500,
    127500, 197500, 297500, 497500, 747500, 997500, 1497500, 2247500, 3247500,
    4747500, 6747500, 9747500, 13747500, 18747500, 24747500, 31747500, 39747500,
    48747500, 58747500, 68747500, 78747500, 88747500, 98747500, 108747500,
    118747500, 128747500, 138747500, 148747500, 158747500, 168747500, 178747500,
    188747500, 198747500, 208747500,
  ],

  // Melon pattern
  MELON: [
    150, 400, 800, 1650, 3300, 6650, 13300, 25800, 43300, 68300, 100800, 140800,
    190800, 290800, 465800, 715800, 1090800, 1590800, 2465800, 3715800, 5465800,
    7965800, 11715800, 16715800, 23215800, 31215800, 41215800, 52715800,
    65715800, 80715800, 95715800, 110715800, 125715800, 140715800, 155715800,
    170715800, 185715800, 200715800, 215715800, 230715800, 245715800, 260715800,
    275715800, 290715800, 305715800, 320715800,
  ],

  // Sugar Cane pattern
  SUGAR_CANE: [
    60, 160, 320, 660, 1320, 2660, 5320, 10320, 17320, 27320, 40320, 56320,
    76320, 116320, 186320, 286320, 436320, 636320, 986320, 1486320, 2186320,
    3186320, 4686320, 6686320, 9286320, 12486320, 16486320, 21086320, 26286320,
    32286320, 38286320, 44286320, 50286320, 56286320, 62286320, 68286320,
    74286320, 80286320, 86286320, 92286320, 98286320, 104286320, 110286320,
    116286320, 122286320, 128286320,
  ],

  // Cactus pattern (same as Sugar Cane)
  CACTUS: [
    60, 160, 320, 660, 1320, 2660, 5320, 10320, 17320, 27320, 40320, 56320,
    76320, 116320, 186320, 286320, 436320, 636320, 986320, 1486320, 2186320,
    3186320, 4686320, 6686320, 9286320, 12486320, 16486320, 21086320, 26286320,
    32286320, 38286320, 44286320, 50286320, 56286320, 62286320, 68286320,
    74286320, 80286320, 86286320, 92286320, 98286320, 104286320, 110286320,
    116286320, 122286320, 128286320,
  ],

  // Cocoa Beans, Nether Wart pattern
  COCOA_BEANS: [
    90, 240, 490, 990, 1990, 3990, 7990, 15490, 25490, 40490, 60490, 85490,
    115490, 165490, 265490, 415490, 615490, 915490, 1415490, 2165490, 3165490,
    4665490, 6665490, 9665490, 13665490, 18665490, 24665490, 31665490, 39665490,
    48665490, 57665490, 66665490, 75665490, 84665490, 93665490, 102665490,
    111665490, 120665490, 129665490, 138665490, 147665490, 156665490, 165665490,
    174665490, 183665490, 192665490,
  ],
  NETHER_WART: [
    90, 240, 490, 990, 1990, 3990, 7990, 15490, 25490, 40490, 60490, 85490,
    115490, 165490, 265490, 415490, 615490, 915490, 1415490, 2165490, 3165490,
    4665490, 6665490, 9665490, 13665490, 18665490, 24665490, 31665490, 39665490,
    48665490, 57665490, 66665490, 75665490, 84665490, 93665490, 102665490,
    111665490, 120665490, 129665490, 138665490, 147665490, 156665490, 165665490,
    174665490, 183665490, 192665490,
  ],
};

// XP reward patterns
const FARMING_XP_REWARDS = [
  100, 250, 500, 1000, 2500, 5000, 10000, 15000, 20000, 30000, 35000, 40000,
  45000,
];
const GARDEN_XP_REWARDS = [
  10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170,
  180, 190, 200, 210, 220, 230, 240, 250, 260, 270, 280, 290, 300,
];

/**
 * Calculate milestone number from harvested amount
 * @param {number} harvested - Amount harvested
 * @param {Array} milestoneArray - Array of milestone requirements
 * @returns {number} Current milestone number (0-based)
 */
function calculateMilestoneFromHarvested(harvested, milestoneArray) {
  let milestone = 0;
  for (let i = 0; i < milestoneArray.length; i++) {
    if (harvested >= milestoneArray[i]) {
      milestone = i + 1;
    } else {
      break;
    }
  }
  return milestone;
}

/**
 * Get farming XP reward for a milestone
 * @param {number} milestoneNumber - Milestone number (1-46)
 * @returns {number} Farming XP reward
 */
function getFarmingXpReward(milestoneNumber) {
  if (milestoneNumber <= 0) return 0;
  if (milestoneNumber <= FARMING_XP_REWARDS.length) {
    return FARMING_XP_REWARDS[milestoneNumber - 1];
  }
  // After milestone 13, all give 50000 XP
  return 50000;
}

/**
 * Get Garden XP reward for a milestone
 * @param {number} milestoneNumber - Milestone number (1-46)
 * @returns {number} Garden XP reward
 */
function getGardenXpReward(milestoneNumber) {
  if (milestoneNumber <= 0) return 0;
  if (milestoneNumber <= GARDEN_XP_REWARDS.length) {
    return GARDEN_XP_REWARDS[milestoneNumber - 1];
  }
  // After milestone 30, all give 300 XP
  return 300;
}

/**
 * Get player's Garden milestone data
 * @param {Object} character - Player character data
 * @returns {Object} Garden milestone data
 */
function getGardenMilestones(character) {
  if (!character.gardenMilestones) {
    return {};
  }

  try {
    // If it's already an object, return it directly
    if (
      typeof character.gardenMilestones === "object" &&
      character.gardenMilestones !== null
    ) {
      return character.gardenMilestones;
    }

    // Parse the JSON string, handling multiple levels of encoding
    let parsed = JSON.parse(character.gardenMilestones);

    // Handle multiple levels of JSON encoding (keep parsing while it's still a string)
    while (typeof parsed === "string") {
      try {
        parsed = JSON.parse(parsed);
      } catch (error) {
        // If we can't parse further, break out to avoid infinite loop
        console.error("[Garden] Could not parse further:", error);
        break;
      }
    }
    return parsed;
  } catch (error) {
    console.error("[Garden] Error parsing garden milestones:", error);
    return {};
  }
}

/**
 * Get current milestone and progress for a crop (calculated dynamically)
 * @param {Object} character - Player character data
 * @param {string} cropKey - Crop item key (e.g., 'WHEAT')
 * @returns {Object} Current milestone info
 */
function getCropMilestoneInfo(character, cropKey) {
  const milestones = getGardenMilestones(character);

  // Handle both old format {harvested: X, milestone: Y} and new format (just number)
  let harvested = 0;
  if (typeof milestones[cropKey] === "number") {
    harvested = milestones[cropKey];
  } else if (
    typeof milestones[cropKey] === "object" &&
    milestones[cropKey]?.harvested
  ) {
    harvested = milestones[cropKey].harvested; // Handle old format
  }

  const cropMilestoneArray = CROP_MILESTONES[cropKey];

  if (!cropMilestoneArray) {
    return {
      currentMilestone: 0,
      harvested: 0,
      nextRequired: 0,
      maxMilestone: 0,
    };
  }

  // Calculate current milestone dynamically based on harvested amount
  let currentMilestone = 0;
  for (let i = 0; i < cropMilestoneArray.length; i++) {
    if (harvested >= cropMilestoneArray[i]) {
      currentMilestone = i + 1;
    } else {
      break;
    }
  }

  const maxMilestone = cropMilestoneArray.length;
  const nextRequired =
    currentMilestone < maxMilestone ? cropMilestoneArray[currentMilestone] : 0;

  return {
    currentMilestone,
    harvested,
    nextRequired,
    maxMilestone,
    isMaxed: currentMilestone >= maxMilestone,
  };
}

/**
 * Update crop harvest count and check for milestone completion
 * @param {string} userId - Player user ID
 * @param {string} cropKey - Crop item key
 * @param {number} amount - Amount harvested
 * @returns {Object} Update result with milestone achievements
 */
async function updateCropHarvest(userId, cropKey, amount) {
  try {
    const character = await getPlayerData(userId);
    if (!character) {
      throw new Error("Character not found");
    }

    const milestones = getGardenMilestones(character);

    // Handle both old format {harvested: X, milestone: Y} and new format (just number)
    let oldHarvested = 0;
    if (typeof milestones[cropKey] === "number") {
      oldHarvested = milestones[cropKey];
    } else if (
      typeof milestones[cropKey] === "object" &&
      milestones[cropKey]?.harvested
    ) {
      oldHarvested = milestones[cropKey].harvested; // Migrate from old format
    }
    const cropMilestoneArray = CROP_MILESTONES[cropKey];

    if (!cropMilestoneArray) {
      return { milestonesAchieved: [], totalFarmingXp: 0, totalGardenXp: 0 };
    }

    // Update harvest count
    const newHarvestTotal = oldHarvested + amount;

    // Calculate old and new milestones dynamically
    const oldMilestone = calculateMilestoneFromHarvested(
      oldHarvested,
      cropMilestoneArray
    );
    const newMilestone = calculateMilestoneFromHarvested(
      newHarvestTotal,
      cropMilestoneArray
    );

    // Check for milestone completions
    const milestonesAchieved = [];
    let totalFarmingXp = 0;
    let totalGardenXp = 0;

    // Award XP for any new milestones achieved
    for (
      let milestone = oldMilestone + 1;
      milestone <= newMilestone;
      milestone++
    ) {
      const requiredAmount = cropMilestoneArray[milestone - 1];
      // include total harvested and next milestone for display
      const nextReq = cropMilestoneArray[milestone] || 0;
      milestonesAchieved.push({
        cropKey,
        milestone,
        required: requiredAmount,
        harvested: newHarvestTotal,
        nextRequired: nextReq,
        farmingXp: getFarmingXpReward(milestone),
        garden_xp: getGardenXpReward(milestone),
      });

      totalFarmingXp += getFarmingXpReward(milestone);
      totalGardenXp += getGardenXpReward(milestone);

      // Note: Disblock XP for crop milestones is now handled by the reconciliation system
    }

    // Save updated harvest count (only the number, not milestone)
    milestones[cropKey] = newHarvestTotal;

    // Clean up the milestones object to ensure all values are numbers
    const cleanMilestones = {};
    for (const [crop, value] of Object.entries(milestones)) {
      if (typeof value === "number") {
        cleanMilestones[crop] = value;
      } else if (typeof value === "object" && value?.harvested) {
        // Migrate old format to new format
        cleanMilestones[crop] = value.harvested;
      } else {
        // Default to 0 for any invalid data
        cleanMilestones[crop] = 0;
      }
    }

    // Check for Garden level-up before updating XP
    const oldGardenXp = character.garden_xp || 0;
    const newGardenXp = oldGardenXp + totalGardenXp;

    // Check for Garden level-up
    const levelUpInfo = checkGardenLevelUp(oldGardenXp, newGardenXp);

    // Save using the proper savePlayerData pattern like other systems
    await savePlayerData(
      userId,
      {
        gardenMilestones: cleanMilestones,
        garden_xp: newGardenXp,
      },
      ["gardenMilestones", "garden_xp"]
    );

    return {
      milestonesAchieved,
      totalFarmingXp,
      totalGardenXp,
      newHarvestTotal,
      newMilestone,
      gardenLevelUp: levelUpInfo,
    };
  } catch (error) {
    console.error("[Garden] Error updating crop harvest:", error);
    return { milestonesAchieved: [], totalFarmingXp: 0, totalGardenXp: 0 };
  }
}

/**
 * Check if player is in their Garden
 * @param {Object} character - Player character data
 * @returns {boolean} True if in Garden
 */
function isInGarden(character) {
  return character.current_region === "garden";
}

// Crop Upgrades: costs per tier (I-IX) and Garden level gates
const CROP_UPGRADE_COSTS = [5, 10, 20, 50, 100, 500, 1000, 2000, 4000]; // in Copper
const CROP_UPGRADE_GATES = [2, 4, 6, 8, 10, 12, 13, 14, 15]; // Garden Level required for each tier
const CROP_UPGRADE_MAX_TIER = 9; // I-IX
const CROP_UPGRADE_FORTUNE_PER_TIER = 5; // +5 Farming Fortune per tier

/**
 * Get player's crop upgrades map safely
 * @param {object} character
 * @returns {Record<string, number>} map of cropKey -> tier (0-9)
 */
function getCropUpgrades(character) {
  const raw =
    character.garden_crop_upgrades || character.garden_crop_upgrades_json;
  if (!raw) return {};
  if (typeof raw === "object") return raw;
  try {
    return JSON.parse(raw) || {};
  } catch {
    return {};
  }
}

/**
 * Get current tier for a crop
 * @param {object} character
 * @param {string} cropKey
 */
function getCropUpgradeTier(character, cropKey) {
  const upgrades = getCropUpgrades(character);
  const t = upgrades[cropKey] || 0;
  return Math.max(0, Math.min(CROP_UPGRADE_MAX_TIER, t));
}

/**
 * Calculate total fortune bonus for a specific crop from upgrades
 * Applies globally when harvesting that crop
 * @param {object} character
 * @param {string} cropKey
 * @returns {number}
 */
function getFortuneBonusForCrop(character, cropKey) {
  const tier = getCropUpgradeTier(character, cropKey);
  return tier * CROP_UPGRADE_FORTUNE_PER_TIER;
}

/**
 * Get next upgrade info for a crop given player's Garden level
 * @param {object} character
 * @param {string} cropKey
 * @param {number} gardenLevel
 * @returns {{nextTier:number, cost:number, requiredLevel:number, canUpgrade:boolean} | null}
 */
function getNextUpgradeInfo(character, cropKey, gardenLevel) {
  const current = getCropUpgradeTier(character, cropKey);
  if (current >= CROP_UPGRADE_MAX_TIER) return null;
  const nextIdx = current; // zero-based index
  const cost = CROP_UPGRADE_COSTS[nextIdx];
  const requiredLevel = CROP_UPGRADE_GATES[nextIdx];
  return {
    nextTier: current + 1,
    cost,
    requiredLevel,
    canUpgrade: gardenLevel >= requiredLevel,
  };
}

/**
 * Persist an upgrade purchase atomically: deduct copper and set tier if affordable and gated
 * @param {string} userId
 * @param {string} cropKey
 * @param {object} character
 * @returns {{success:boolean, message?:string, newTier?:number, copperSpent?:number}}
 */
async function purchaseCropUpgrade(userId, cropKey, character) {
  try {
    const gardenLevel = getGardenLevel(character.garden_xp || 0);
    const info = getNextUpgradeInfo(character, cropKey, gardenLevel);
    if (!info) return { success: false, message: "Already at max tier." };
    if (!info.canUpgrade)
      return {
        success: false,
        message: `Requires Garden Level ${info.requiredLevel}.`,
      };

    const { dbRunQueued } = require("./dbUtils");
    // Deduct copper guarded
    const updateRes = await dbRunQueued(
      "UPDATE players SET copper = copper - ? WHERE discord_id = ? AND copper >= ?",
      [info.cost, userId, info.cost]
    );

    if (!updateRes || updateRes.changes === 0) {
      return { success: false, message: "Not enough Copper." };
    }

    // Update JSON upgrades map
    const currentUpgrades = getCropUpgrades(character);
    const newTier = info.nextTier;
    const newMap = { ...currentUpgrades, [cropKey]: newTier };
    await updatePlayerJsonFields(userId, { garden_crop_upgrades: newMap });

    return { success: true, newTier, copperSpent: info.cost };
  } catch (e) {
    console.error("[Garden] purchaseCropUpgrade error:", e);
    return { success: false, message: "Upgrade failed due to an error." };
  }
}

// Garden level requirements
const GARDEN_LEVEL_REQUIREMENTS = [
  0, // Level 1: 0 XP
  70, // Level 2: 70 XP
  140, // Level 3: 140 XP
  280, // Level 4: 280 XP
  520, // Level 5: 520 XP
  1120, // Level 6: 1,120 XP
  2620, // Level 7: 2,620 XP (Cactus - to be added later)
  4620, // Level 8: 4,620 XP
  7120, // Level 9: 7,120 XP
  10120, // Level 10: 10,120 XP
  20120, // Level 11: 20,120 XP
  30120, // Level 12: 30,120 XP
  40120, // Level 13: 40,120 XP
  50120, // Level 14: 50,120 XP
  60120, // Level 15: 60,120 XP (Max level)
];

// Crop unlocks by Garden level
const CROP_UNLOCKS = {
  1: ["WHEAT"],
  2: ["CARROT"],
  3: ["POTATO"],
  4: ["PUMPKIN"],
  5: ["SUGAR_CANE"],
  6: ["MELON"],
  7: ["CACTUS"],
  8: ["COCOA_BEANS"],
  9: ["MUSHROOMS"],
  10: ["NETHER_WART"],
};

/**
 * Get Garden level from Garden XP
 * @param {number} gardenXp - Total Garden XP
 * @returns {number} Garden level
 */
function getGardenLevel(gardenXp) {
  const xp = gardenXp || 0;

  for (let i = GARDEN_LEVEL_REQUIREMENTS.length - 1; i >= 0; i--) {
    if (xp >= GARDEN_LEVEL_REQUIREMENTS[i]) {
      return i + 1; // Levels are 1-indexed
    }
  }

  return 1; // Minimum level
}

/**
 * Get XP required for next Garden level
 * @param {number} currentLevel - Current Garden level
 * @returns {number} XP required for next level (0 if max level)
 */
function getXpForNextGardenLevel(currentLevel) {
  if (currentLevel >= GARDEN_LEVEL_REQUIREMENTS.length) {
    return 0; // Max level reached
  }

  return GARDEN_LEVEL_REQUIREMENTS[currentLevel]; // Next level requirement
}

/**
 * Check if Garden level increased and return level-up info
 * @param {number} oldXp - Previous Garden XP
 * @param {number} newXp - New Garden XP
 * @returns {Object} Level-up information
 */
function checkGardenLevelUp(oldXp, newXp) {
  const oldLevel = getGardenLevel(oldXp);
  const newLevel = getGardenLevel(newXp);

  if (newLevel > oldLevel) {
    const unlockedCrops = [];

    // Check what crops were unlocked
    for (let level = oldLevel + 1; level <= newLevel; level++) {
      if (CROP_UNLOCKS[level]) {
        unlockedCrops.push(...CROP_UNLOCKS[level]);
      }
    }

    return {
      leveledUp: true,
      oldLevel,
      newLevel,
      unlockedCrops,
    };
  }

  return { leveledUp: false };
}

/**
 * Get all crops unlocked at current Garden level
 * @param {number} gardenLevel - Current Garden level
 * @returns {Array} Array of unlocked crop keys
 */
function getUnlockedCrops(gardenLevel) {
  const unlockedCrops = [];

  for (let level = 1; level <= gardenLevel; level++) {
    if (CROP_UNLOCKS[level]) {
      unlockedCrops.push(...CROP_UNLOCKS[level]);
    }
  }

  return unlockedCrops;
}

/**
 * Check if a crop is unlocked at current Garden level
 * @param {string} cropKey - Crop item key
 * @param {number} gardenLevel - Current Garden level
 * @returns {boolean} True if crop is unlocked
 */
function isCropUnlocked(cropKey, gardenLevel) {
  const unlockedCrops = getUnlockedCrops(gardenLevel);
  return unlockedCrops.includes(cropKey);
}

/**
 * Format milestone progress for display
 * @param {Object} character - Player character data
 * @param {string} cropKey - Crop item key
 * @returns {string} Formatted progress string
 */
function formatMilestoneProgress(character, cropKey) {
  const info = getCropMilestoneInfo(character, cropKey);

  if (info.isMaxed) {
    return `${info.harvested.toLocaleString()} (MAXED!)`;
  }

  if (info.nextRequired === 0) {
    return `${info.harvested.toLocaleString()} (No milestones)`;
  }

  // Calculate progress within the current milestone
  const progressStartAmount =
    info.currentMilestone > 0
      ? CROP_MILESTONES[cropKey]?.[info.currentMilestone - 1] || 0
      : 0;
  const progressCurrent = info.harvested - progressStartAmount;
  const progressMax = info.nextRequired - progressStartAmount;

  return `${progressCurrent.toLocaleString()}/${progressMax.toLocaleString()} on Milestone ${info.currentMilestone + 1}`;
}

module.exports = {
  CROP_MILESTONES,
  GARDEN_LEVEL_REQUIREMENTS,
  CROP_UNLOCKS,
  CROP_UPGRADE_COSTS,
  CROP_UPGRADE_GATES,
  CROP_UPGRADE_MAX_TIER,
  CROP_UPGRADE_FORTUNE_PER_TIER,
  getFarmingXpReward,
  getGardenXpReward,
  getGardenMilestones,
  getCropMilestoneInfo,
  updateCropHarvest,
  isInGarden,
  getGardenLevel,
  getXpForNextGardenLevel,
  checkGardenLevelUp,
  getUnlockedCrops,
  isCropUnlocked,
  formatMilestoneProgress,
  getCropUpgrades,
  getCropUpgradeTier,
  getFortuneBonusForCrop,
  getNextUpgradeInfo,
  purchaseCropUpgrade,
};
