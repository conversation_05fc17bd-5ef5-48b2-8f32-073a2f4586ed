/**
 * Unified Stop Button System
 * Ensures stop buttons always exist and work regardless of message cycling or bot restarts
 * No more collectors - purely global interaction handler based
 */

const { ButtonBuilder, ButtonStyle, ActionRowBuilder } = require("discord.js");
const { getActionById, updateActionMessageId } = require("./actionPersistence");

/**
 * Creates a stop button with unified customId format
 * @param {number} actionId - Database action ID
 * @param {string} userId - Discord user ID
 * @param {string} [label="Stop"] - Button label
 * @returns {ButtonBuilder} The stop button
 */
function createStopButton(actionId, userId, label = "Stop") {
  return new ButtonBuilder()
    .setCustomId(`unified_stop:${actionId}:${userId}`)
    .setLabel(label)
    .setStyle(ButtonStyle.Danger);
}

/**
 * Ensures a stop button exists on a message, removing old ones if needed
 * @param {import('discord.js').Message} message - Discord message to modify
 * @param {number} actionId - Database action ID
 * @param {string} userId - Discord user ID
 * @param {string} [label="Stop"] - Button label
 * @returns {Promise<boolean>} True if button was added/updated, false if failed
 */
async function ensureUnifiedStopButton(
  message,
  actionId,
  userId,
  label = "Stop"
) {
  try {
    if (!message || !message.edit || !actionId || !userId) {
      console.warn(
        `[UnifiedStop] Invalid parameters: message=${!!message}, actionId=${actionId}, userId=${userId}`
      );
      return false;
    }

    // Remove any existing stop buttons for this action
    const cleanedComponents = [];
    let foundExistingButton = false;

    for (const row of message.components || []) {
      const cleanedRow = new ActionRowBuilder();
      let hasRemainingButtons = false;

      for (const component of row.components) {
        const isStopButton =
          component.customId &&
          (component.customId.startsWith(`stop_action:${actionId}:`) ||
            component.customId.startsWith(`stop_resumed_action:${actionId}:`) ||
            component.customId.startsWith(`unified_stop:${actionId}:`));

        if (isStopButton) {
          foundExistingButton = true;
          // Don't add the old stop button to the cleaned row
        } else {
          // Keep non-stop buttons
          cleanedRow.addComponents(component);
          hasRemainingButtons = true;
        }
      }

      // Only add the row if it has remaining buttons
      if (hasRemainingButtons) {
        cleanedComponents.push(cleanedRow);
      }
    }

    // Add new unified stop button
    const stopButton = createStopButton(actionId, userId, label);
    const stopRow = new ActionRowBuilder().addComponents(stopButton);
    cleanedComponents.push(stopRow);

    // Update the message
    await message.edit({ components: cleanedComponents });

    if (foundExistingButton) {
      console.log(
        `[UnifiedStop] Replaced existing stop button for action ${actionId}`
      );
    } else {
      console.log(`[UnifiedStop] Added new stop button for action ${actionId}`);
    }

    return true;
  } catch (error) {
    // Handle specific message edit errors gracefully during resumption
    if (error.code === 50005) {
      console.log(
        `[UnifiedStop] Cannot edit message authored by another user (resumption scenario) - skipping stop button update for action ${actionId}`
      );
      return false; // Return false so caller can try alternative approaches
    }

    console.warn(
      `[UnifiedStop] Failed to ensure stop button for action ${actionId}:`,
      error.message
    );
    return false;
  }
}

/**
 * Creates a new message with a stop button when existing message cannot be edited
 * @param {import('discord.js').TextChannel} channel - Discord channel
 * @param {number} actionId - Database action ID
 * @param {string} userId - Discord user ID
 * @param {string} actionType - Type of action (farming, mining, etc.)
 * @param {string} [reason=""] - Reason for creating new message
 * @returns {Promise<import('discord.js').Message|null>} The new message or null if failed
 */
async function createNewMessageWithStopButton(
  channel,
  actionId,
  userId,
  actionType,
  reason = ""
) {
  try {
    const { EmbedBuilder } = require("discord.js");
    const { EMBED_COLORS } = require("../gameConfig");

    const stopButton = createStopButton(
      actionId,
      userId,
      `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
    );
    const stopRow = new ActionRowBuilder().addComponents(stopButton);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.YELLOW)
      .setTitle("🎯 Action Control")
      .setDescription(
        `Your ${actionType} action is running. Use the stop button below to end it.${reason ? `\n\n**Reason:** ${reason}` : ""}\n\n**Action ID:** ${actionId}`
      )
      .setTimestamp();

    const newMessage = await channel.send({
      content: `<@${userId}>`,
      embeds: [embed],
      components: [stopRow],
    });

    // Update database with new message ID
    await updateActionMessageId(actionId, newMessage.id);

    console.log(
      `[UnifiedStop] Created new message ${newMessage.id} with stop button for action ${actionId}`
    );
    return newMessage;
  } catch (error) {
    console.error(
      `[UnifiedStop] Failed to create new message with stop button for action ${actionId}:`,
      error
    );
    return null;
  }
}

/**
 * Handles unified stop button interactions
 * @param {import('discord.js').ButtonInteraction} interaction - Button interaction
 * @returns {Promise<boolean>} True if handled, false if not our button
 */
async function handleUnifiedStopButton(interaction) {
  try {
    const customId = interaction.customId;

    if (!customId.startsWith("unified_stop:")) {
      return false; // Not our button
    }

    const parts = customId.split(":");
    const actionId = parseInt(parts[1], 10);
    const userId = parts[2];

    if (isNaN(actionId) || !userId) {
      console.warn(`[UnifiedStop] Invalid stop button customId: ${customId}`);
      return false;
    }

    // Verify the user clicking the button is the same user who started the action
    if (interaction.user.id !== userId) {
      await interaction.reply({
        content: "❌ You can only stop your own actions.",
        ephemeral: true,
      });
      return true;
    }

    // Get action details for worker routing (actions always run on workers)
    const actionRecord = await getActionById(actionId);
    const actionType = actionRecord?.action_type || "unknown";
    const workerId = actionRecord?.worker_id;

    // Actions always run on workers, so always route stop requests to workers
    if (workerId) {
      console.log(
        `[UnifiedStop] Routing stop request for action ${actionId} to worker ${workerId}`
      );

      try {
        // Check if we're on the main bot (need to route to worker)
        const isMainBot = !process.env.IS_WORKER_BOT;
        if (isMainBot) {
          const axios = require("axios");
          const config = require("../config.json");

          // Find the worker's API URL
          const worker = Object.values(config.multiBot?.workerBots || {}).find(
            (w) => w.id === workerId
          );
          if (worker) {
            const workerUrl = `http://127.0.0.1:${worker.port}`;

            // Route the stop request to the worker
            await axios.post(
              `${workerUrl}/api/stop-action`,
              {
                actionId: actionId,
                userId: userId,
                interactionId: interaction.id,
                channelId: interaction.channelId,
              },
              {
                timeout: 5000,
              }
            );

            console.log(
              `[UnifiedStop] Successfully routed stop request for action ${actionId} to worker ${workerId}`
            );

            // Acknowledge the button press
            await interaction.deferUpdate();
            return true;
          } else {
            console.error(
              `[UnifiedStop] Worker ${workerId} not found in config - cannot stop action ${actionId}`
            );
            await interaction.reply({
              content: "❌ Unable to stop action - worker not found.",
              ephemeral: true,
            });
            return true;
          }
        } else {
          // We're on a worker bot, handle locally
          const { abortAction } = require("./instantStopUtils");
          console.log(
            `[UnifiedStop] User ${userId} clicked unified stop button for ${actionType} action ${actionId} on worker`
          );
          const aborted = abortAction(actionId);

          if (aborted) {
            console.log(
              `[UnifiedStop] Successfully aborted ${actionType} action ${actionId} on worker`
            );
          } else {
            console.warn(
              `[UnifiedStop] Action ${actionId} not found in worker's active controllers (may already be stopped)`
            );
          }

          // Acknowledge the button press
          await interaction.deferUpdate();
          return true;
        }
      } catch (routingError) {
        console.error(
          `[UnifiedStop] Failed to route stop request to worker ${workerId}:`,
          routingError.message
        );
        await interaction.reply({
          content: "❌ Error communicating with worker. Please try again.",
          ephemeral: true,
        });
        return true;
      }
    } else {
      console.error(
        `[UnifiedStop] No worker_id found for action ${actionId} - cannot stop action`
      );
      await interaction.reply({
        content: "❌ Unable to stop action - worker information not found.",
        ephemeral: true,
      });
      return true;
    }

    // Acknowledge the button press
    try {
      await interaction.deferUpdate();
      console.log(
        `[UnifiedStop] Acknowledged stop button press for ${actionType} action ${actionId}`
      );
    } catch (updateError) {
      console.warn(
        `[UnifiedStop] Failed to acknowledge button press for action ${actionId}:`,
        updateError.message
      );
    }

    return true;
  } catch (error) {
    console.error(
      "[UnifiedStop] Error handling stop button interaction:",
      error
    );
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "❌ Error processing stop request. Please try again.",
          ephemeral: true,
        });
      }
    } catch (replyError) {
      console.warn("[UnifiedStop] Failed to send error reply:", replyError);
    }
    return false;
  }
}

module.exports = {
  createStopButton,
  ensureUnifiedStopButton,
  createNewMessageWithStopButton,
  handleUnifiedStopButton,
};
