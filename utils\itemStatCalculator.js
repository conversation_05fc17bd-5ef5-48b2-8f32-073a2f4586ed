/**
 * Calculates total stats for an item including base stats, reforge, enchantments, and hot potato books
 * This unified function replaces duplicate logic in inventory.js and inspect.js
 * @param {Object} item - The equipped item object
 * @param {Object} itemData - The item configuration data
 * @param {Object} character - The character object (optional, needed for wood collection tracking)
 * @returns {Object} Object containing separated stat sources and total stats
 */
function calculateItemStats(item, itemData, character = null) {
  const result = {
    baseStats: { ...itemData.baseStats },
    reforgeStats: {},
    enchantStats: {},
    hotPotatoStats: {},
    dynamicStats: {},
    totalStats: { ...itemData.baseStats },
  };

  // Parse data_json if it exists
  let dataJson = {};
  if (item.data_json) {
    try {
      dataJson =
        typeof item.data_json === "string"
          ? JSON.parse(item.data_json)
          : item.data_json;
    } catch (error) {
      console.error("[ItemStatCalculator] Error parsing data_json:", error);
    }
  }

  // Apply dynamic stats (scaling with skill levels)
  if (itemData.dynamicStats && character) {
    const { getLevelFromExp } = require("./expFunctions");

    for (const [statKey, dynamicData] of Object.entries(
      itemData.dynamicStats
    )) {
      let statBonus = dynamicData.base || 0;

      // Apply farming level scaling
      if (
        dynamicData.perFarmingLevel &&
        character.skills?.farming?.exp !== undefined
      ) {
        const farmingLevel = getLevelFromExp(
          character.skills.farming.exp
        ).level;
        statBonus += dynamicData.perFarmingLevel * farmingLevel;
      }

      // Apply mining level scaling
      if (
        dynamicData.perMiningLevel &&
        character.skills?.mining?.exp !== undefined
      ) {
        const miningLevel = getLevelFromExp(character.skills.mining.exp).level;
        statBonus += dynamicData.perMiningLevel * miningLevel;
      }

      // Apply foraging level scaling
      if (
        dynamicData.perForagingLevel &&
        character.skills?.foraging?.exp !== undefined
      ) {
        const foragingLevel = getLevelFromExp(
          character.skills.foraging.exp
        ).level;
        statBonus += dynamicData.perForagingLevel * foragingLevel;
      }

      // Apply fishing level scaling
      if (
        dynamicData.perFishingLevel &&
        character.skills?.fishing?.exp !== undefined
      ) {
        const fishingLevel = getLevelFromExp(
          character.skills.fishing.exp
        ).level;
        statBonus += dynamicData.perFishingLevel * fishingLevel;
      }

      // Apply combat level scaling
      if (
        dynamicData.perCombatLevel &&
        character.skills?.combat?.exp !== undefined
      ) {
        const combatLevel = getLevelFromExp(character.skills.combat.exp).level;
        statBonus += dynamicData.perCombatLevel * combatLevel;
      }

      // Apply enchanting level scaling
      if (
        dynamicData.perEnchantingLevel &&
        character.skills?.enchanting?.exp !== undefined
      ) {
        const enchantingLevel = getLevelFromExp(
          character.skills.enchanting.exp
        ).level;
        statBonus += dynamicData.perEnchantingLevel * enchantingLevel;
      }

      // Apply alchemy level scaling
      if (
        dynamicData.perAlchemyLevel &&
        character.skills?.alchemy?.exp !== undefined
      ) {
        const alchemyLevel = getLevelFromExp(
          character.skills.alchemy.exp
        ).level;
        statBonus += dynamicData.perAlchemyLevel * alchemyLevel;
      }

      // Apply taming level scaling
      if (
        dynamicData.perTamingLevel &&
        character.skills?.taming?.exp !== undefined
      ) {
        const tamingLevel = getLevelFromExp(character.skills.taming.exp).level;
        statBonus += dynamicData.perTamingLevel * tamingLevel;
      }

      // For dynamic stats, the calculated value replaces the base stat, not adds to it
      if (statBonus > 0) {
        result.dynamicStats[statKey] = statBonus;
        // Replace the stat value with the dynamic calculation
        result.totalStats[statKey] = statBonus;
      }
    }
  }

  // Apply reforge bonuses (dynamic calculation)
  if (dataJson.reforge) {
    const { calculateDynamicReforgeStats } = require("./dynamicReforgeStats");
    const reforgeStats = calculateDynamicReforgeStats(
      dataJson.reforge,
      itemData
    );
    for (const [statKey, bonus] of Object.entries(reforgeStats)) {
      result.reforgeStats[statKey] = bonus;
      result.totalStats[statKey] = (result.totalStats[statKey] || 0) + bonus;
    }
  }

  // Fallback: Apply reforge bonuses from legacy reforge property
  if (item.reforge && item.reforge.stats) {
    for (const [statKey, bonus] of Object.entries(item.reforge.stats)) {
      result.reforgeStats[statKey] = bonus;
      result.totalStats[statKey] = (result.totalStats[statKey] || 0) + bonus;
    }
  }

  // Apply enchantment bonuses
  if (dataJson.enchantments) {
    // Sharpness enchantment (affects damage multiplier)
    if (dataJson.enchantments.SHARPNESS && result.baseStats.DAMAGE) {
      const level = dataJson.enchantments.SHARPNESS;
      const damageMultBonus = level === 5 ? 30 : level * 5; // 5% per level, 30% at level 5
      const baseDamage = result.baseStats.DAMAGE;
      const bonusDamage = Math.floor(baseDamage * (damageMultBonus / 100));
      result.enchantStats.DAMAGE = bonusDamage;
      result.totalStats.DAMAGE = (result.totalStats.DAMAGE || 0) + bonusDamage;
    }

    // Protection enchantment (affects defense)
    if (dataJson.enchantments.PROTECTION) {
      const level = dataJson.enchantments.PROTECTION;
      const defenseBonus = level * 4; // 4 defense per level
      result.enchantStats.DEFENSE = defenseBonus;
      result.totalStats.DEFENSE =
        (result.totalStats.DEFENSE || 0) + defenseBonus;
    }

    // Growth enchantment (affects health)
    if (dataJson.enchantments.GROWTH) {
      const level = dataJson.enchantments.GROWTH;
      const healthBonus = level * 15; // 15 health per level
      result.enchantStats.HEALTH = healthBonus;
      result.totalStats.HEALTH = (result.totalStats.HEALTH || 0) + healthBonus;
    }

    // Critical enchantment (affects crit damage)
    if (dataJson.enchantments.CRITICAL) {
      const level = dataJson.enchantments.CRITICAL;
      const critDamageBonus = level * 10; // 10 crit damage per level
      result.enchantStats.CRIT_DAMAGE = critDamageBonus;
      result.totalStats.CRIT_DAMAGE =
        (result.totalStats.CRIT_DAMAGE || 0) + critDamageBonus;
    }

    // Harvesting enchantment (affects farming fortune)
    if (dataJson.enchantments.HARVESTING) {
      const level = dataJson.enchantments.HARVESTING;
      const farmingFortuneBonus = level * 12.5; // 12.5 farming fortune per level
      result.enchantStats.FARMING_FORTUNE =
        (result.enchantStats.FARMING_FORTUNE || 0) + farmingFortuneBonus;
      result.totalStats.FARMING_FORTUNE =
        (result.totalStats.FARMING_FORTUNE || 0) + farmingFortuneBonus;
    }

    // Sunder enchantment (Farming Axe only; affects farming fortune)
    if (dataJson.enchantments.SUNDER && itemData.subtype === "FARMING_AXE") {
      const level = dataJson.enchantments.SUNDER;
      const farmingFortuneBonus = level * 12.5; // 12.5 per level
      result.enchantStats.FARMING_FORTUNE =
        (result.enchantStats.FARMING_FORTUNE || 0) + farmingFortuneBonus;
      result.totalStats.FARMING_FORTUNE =
        (result.totalStats.FARMING_FORTUNE || 0) + farmingFortuneBonus;
    }

    // Efficiency enchantment (affects fortune and sweep stats)
    if (dataJson.enchantments.EFFICIENCY) {
      const level = dataJson.enchantments.EFFICIENCY;
      const fortune = level * 10 + 5; // 15, 25, 35, 45, 55 fortune

      // Add fortune based on tool type
      if (
        (itemData.subtype === "PICKAXE" || itemData.subtype === "SHOVEL") &&
        (itemData.type === "TOOL" || !itemData.type)
      ) {
        result.enchantStats.MINING_FORTUNE = fortune;
        result.totalStats.MINING_FORTUNE =
          (result.totalStats.MINING_FORTUNE || 0) + fortune;

        // Level 5 also adds sweep stats
        if (level === 5) {
          result.enchantStats.MINING_SWEEP = 1;
          result.totalStats.MINING_SWEEP =
            (result.totalStats.MINING_SWEEP || 0) + 1;
        }
      } else if (
        itemData.subtype === "FARMING_AXE" &&
        (itemData.type === "TOOL" || !itemData.type)
      ) {
        // Farming axe - give farming fortune and sweep
        result.enchantStats.FARMING_FORTUNE =
          (result.enchantStats.FARMING_FORTUNE || 0) + fortune;
        result.totalStats.FARMING_FORTUNE =
          (result.totalStats.FARMING_FORTUNE || 0) + fortune;

        if (level === 5) {
          result.enchantStats.FARMING_SWEEP = 1;
          result.totalStats.FARMING_SWEEP =
            (result.totalStats.FARMING_SWEEP || 0) + 1;
        }
      } else if (
        itemData.subtype === "AXE" &&
        (itemData.type === "TOOL" || !itemData.type)
      ) {
        // Foraging axe - give foraging fortune and sweep
        result.enchantStats.FORAGING_FORTUNE = fortune;
        result.totalStats.FORAGING_FORTUNE =
          (result.totalStats.FORAGING_FORTUNE || 0) + fortune;

        if (level === 5) {
          result.enchantStats.FORAGING_SWEEP = 1;
          result.totalStats.FORAGING_SWEEP =
            (result.totalStats.FORAGING_SWEEP || 0) + 1;
        }
      } else if (
        itemData.subtype === "HOE" &&
        (itemData.type === "TOOL" || !itemData.type)
      ) {
        result.enchantStats.FARMING_FORTUNE =
          (result.enchantStats.FARMING_FORTUNE || 0) + fortune;
        result.totalStats.FARMING_FORTUNE =
          (result.totalStats.FARMING_FORTUNE || 0) + fortune;

        if (level === 5) {
          result.enchantStats.FARMING_SWEEP = 1;
          result.totalStats.FARMING_SWEEP =
            (result.totalStats.FARMING_SWEEP || 0) + 1;
        }
      }
    }

    // Fishing Rod enchantments
    if (itemData.subtype === "ROD" && itemData.type === "TOOL") {
      // Angler enchantment (affects sea creature chance)
      if (dataJson.enchantments.ANGLER) {
        const level = dataJson.enchantments.ANGLER;
        const seaCreatureChanceBonus = level; // 1% per level
        result.enchantStats.SEA_CREATURE_CHANCE = seaCreatureChanceBonus;
        result.totalStats.SEA_CREATURE_CHANCE =
          (result.totalStats.SEA_CREATURE_CHANCE || 0) + seaCreatureChanceBonus;
      }

      // Piscary enchantment (affects fishing speed)
      if (dataJson.enchantments.PISCARY) {
        const level = dataJson.enchantments.PISCARY;
        const fishingSpeedBonus = level * 2.5; // 2.5 per level
        result.enchantStats.FISHING_SPEED = fishingSpeedBonus;
        result.totalStats.FISHING_SPEED =
          (result.totalStats.FISHING_SPEED || 0) + fishingSpeedBonus;
      }
    }
  }

  // Apply Hot Potato Book bonuses
  if (dataJson.hotPotatoBooks && dataJson.hotPotatoBooks > 0) {
    const bookCount = dataJson.hotPotatoBooks;

    // Determine item category for stat bonuses
    let category = null;
    if (itemData.type === "ARMOR") category = "ARMOR";
    else if (itemData.type === "WEAPON") category = "WEAPON";

    if (category) {
      // Define Hot Potato Book stat bonuses
      const HOT_POTATO_STATS = {
        ARMOR: { HEALTH: 4, DEFENSE: 2 },
        WEAPON: { DAMAGE: 2, STRENGTH: 2 },
      };

      const bonusPerBook = HOT_POTATO_STATS[category];

      for (const [stat, value] of Object.entries(bonusPerBook)) {
        const totalBonus = value * bookCount;
        result.hotPotatoStats[stat] = totalBonus;
        result.totalStats[stat] = (result.totalStats[stat] || 0) + totalBonus;
      }
    }
  }

  // Apply progressive ability bonuses (e.g., Fig Hew)
  // Handle both single ability and multiple abilities
  const abilities =
    itemData.abilities ||
    (itemData.ability ? { singleAbility: itemData.ability } : {});

  for (const [, ability] of Object.entries(abilities)) {
    if (ability.type === "PROGRESSIVE") {
      const logsOnRegion = dataJson.logsOnRegion || {};
      const regionLogs = logsOnRegion[ability.region] || 0;

      // Calculate bonus based on logs broken
      const bonusLevels = Math.floor(regionLogs / ability.logsPerBonus);
      const actualBonus = Math.min(bonusLevels, ability.maxBonus);

      if (actualBonus > 0) {
        const statKey = ability.stat;
        if (!result.abilityStats) result.abilityStats = {};
        result.abilityStats[statKey] = actualBonus;
        result.totalStats[statKey] =
          (result.totalStats[statKey] || 0) + actualBonus;
      }
    } else if (
      ability.type === "BULWARK" ||
      ability.type === "ZOMBIE_BULWARK"
    ) {
      // Handle Bulwark ability - tracks mob kills and provides defense bonus
      const targetMobType = ability.targetMobType || "Undead";
      const killPropertyName =
        targetMobType === "Undead" ? "zombieKills" : "bulwarkKills";
      const mobKills = dataJson[killPropertyName] || 0;

      // Find the highest threshold met
      let currentBonus = 0;
      for (const threshold of ability.thresholds) {
        if (mobKills >= threshold.kills) {
          currentBonus = threshold.bonus;
        } else {
          break;
        }
      }

      if (currentBonus > 0) {
        const statKey = ability.stat;
        if (!result.abilityStats) result.abilityStats = {};
        result.abilityStats[statKey] = currentBonus;
        // Note: For Bulwark abilities, we don't add to totalStats here since it's conditional
        // The defense bonus is only applied against the target mob type in combat
      }
    } else if (ability.type === "KILL_TRACKER") {
      // Handle Kill Tracker ability - tracks total kills across all mobs
      const totalKills = dataJson.totalKills || 0;

      // Calculate bonus based on kills
      const bonusLevels = Math.floor(totalKills / ability.killsPerBonus);
      const actualBonus = Math.min(bonusLevels, ability.maxBonus);

      if (actualBonus > 0) {
        const statKey = ability.stat;
        if (!result.abilityStats) result.abilityStats = {};
        result.abilityStats[statKey] = actualBonus;
        result.totalStats[statKey] =
          (result.totalStats[statKey] || 0) + actualBonus;
      }
    } else if (ability.type === "WOOD_COLLECTION_TRACKER") {
      // Handle Wood Collection Tracker ability - tracks total wood across all collections
      // We need to get the character's collection data for this
      // Get total wood collected from character collections
      let totalWoodCollected = 0;

      if (character && character.collections) {
        // List of all wood collection keys from FORAGING category
        const woodCollectionKeys = [
          "OAK_LOG",
          "SPRUCE_LOG",
          "BIRCH_LOG",
          "JUNGLE_LOG",
          "ACACIA_LOG",
          "DARK_OAK_LOG",
          "CHERRY_LOG",
          "FIG_LOG",
          "MANGROVE_LOG",
        ];

        for (const woodKey of woodCollectionKeys) {
          totalWoodCollected += character.collections[woodKey] || 0;
        }
      }

      // Calculate bonus based on wood collected
      const bonusLevels = Math.floor(totalWoodCollected / ability.woodPerBonus);
      const actualBonus = Math.min(bonusLevels, ability.maxBonus);

      if (actualBonus > 0) {
        const statKey = ability.stat;
        if (!result.abilityStats) result.abilityStats = {};
        result.abilityStats[statKey] = actualBonus;
        result.totalStats[statKey] =
          (result.totalStats[statKey] || 0) + actualBonus;
      }
    }
  }

  return result;
}

module.exports = {
  calculateItemStats,
};
