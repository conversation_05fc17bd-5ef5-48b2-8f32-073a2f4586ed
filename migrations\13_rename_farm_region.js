// Utilities not needed - using custom promisified helpers instead

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) {
        console.error("Database run error:", err.message);
        reject(err);
      } else {
        resolve({ lastID: this.lastID, changes: this.changes });
      }
    });
  });
}

module.exports = {
  async up(db) {
    console.log(
      "Applying migration: Renaming currentRegion from 'The Farm' to 'The Farming Islands'...",
    );
    try {
      // Update the dedicated current_region column
      const updateResult = await dbRunAsync(
        db,
        "UPDATE players SET current_region = ? WHERE current_region = ?",
        ["The Farming Islands", "The Farm"], // Set 'The Farming Islands' where it currently is 'The Farm'
      );
      console.log(
        `  -> Updated current_region for ${updateResult.changes} players.`,
      );

      // Update other potential dedicated columns if they exist and store region names, e.g.:
      // const updateHomeResult = await dbRunAsync(db,
      //     `UPDATE players SET home_location = ? WHERE home_location = ?`,
      //     ['The Barn', 'The Farm']
      // Silent home location update

      console.log("Region rename migration applied successfully.");
    } catch (err) {
      console.error("Migration failed:", err);
      throw err; // Re-throw error to halt migration process
    }
  },
};
