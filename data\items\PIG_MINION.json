{"name": "<PERSON> Minion", "emoji": "<:minion_pig:1375851905468862474>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "resourceItemKey": "RAW_PORKCHOP", "recipes": [{"ingredients": [{"itemKey": "RAW_PORKCHOP", "amount": 64}]}], "craftingRequirements": {"collections": {"RAW_PORKCHOP": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 26, "maxStorage": 192}, {"tier": 2, "generationIntervalSeconds": 26, "maxStorage": 320, "upgradeCost": [{"itemKey": "RAW_PORKCHOP", "amount": 128}]}, {"tier": 3, "generationIntervalSeconds": 24, "maxStorage": 320, "upgradeCost": [{"itemKey": "RAW_PORKCHOP", "amount": 256}]}, {"tier": 4, "generationIntervalSeconds": 24, "maxStorage": 448, "upgradeCost": [{"itemKey": "RAW_PORKCHOP", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 22, "maxStorage": 448, "upgradeCost": [{"itemKey": "ENCHANTED_PORK", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 22, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_PORK", "amount": 24}]}, {"tier": 7, "generationIntervalSeconds": 20, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_PORK", "amount": 64}]}, {"tier": 8, "generationIntervalSeconds": 20, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_PORK", "amount": 128}]}, {"tier": 9, "generationIntervalSeconds": 18, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_PORK", "amount": 256}]}, {"tier": 10, "generationIntervalSeconds": 18, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_PORK", "amount": 512}]}, {"tier": 11, "generationIntervalSeconds": 16, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_GRILLED_PORK", "amount": 8}]}], "drops": [{"itemKey": "RAW_PORKCHOP", "chance": 1}]}