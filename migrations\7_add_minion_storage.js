// Migration to add minionStorage column

const MIGRATION_VERSION = 7;

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add minion_storage_json column`,
  );

  await new Promise((resolve, reject) => {
    // Add the column with default '[]'
    db.run(
      "ALTER TABLE players ADD COLUMN minion_storage_json TEXT DEFAULT '[]'",
      function (err) {
        if (err) {
          if (err.message.includes("duplicate column name")) {
            console.log(
              `[Migration ${MIGRATION_VERSION}] Column minion_storage_json already exists, skipping.`,
            );
            resolve();
          } else {
            console.error(
              `[Migration ${MIGRATION_VERSION}] Error adding minion_storage_json column:`,
              err,
            );
            reject(err);
          }
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully added minion_storage_json column.`,
          );
          resolve();
        }
      },
    );
  });
}

module.exports = { up /*, down */ };
