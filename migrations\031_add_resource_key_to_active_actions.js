const { dbRun } = require("../utils/dbUtils");

async function up() {
  console.log(
    "Applying migration: Add resource_key column to active_actions table",
  );
  try {
    await dbRun("ALTER TABLE active_actions ADD COLUMN resource_key TEXT");
    console.log("Successfully added resource_key column.");
  } catch (err) {
    console.error("Error applying migration:", err);
    throw err; // Re-throw the error to stop the migration process if it fails
  }
}

async function down() {
  console.log(
    "Reverting migration: Remove resource_key column from active_actions table",
  );
  // Note: SQLite doesn't easily support dropping columns without recreating the table.
  // This 'down' migration is primarily for completeness/tracking and won't fully revert
  // in a simple way. Data loss would occur if we recreated the table.
  console.warn(
    "SQLite does not support simple column drops. Manual intervention may be required to fully revert.",
  );
  // If you *really* needed to revert, you'd create a new table without the column,
  // copy data, drop the old table, and rename the new one.
  // await dbRun('ALTER TABLE active_actions DROP COLUMN resource_key'); // This syntax is not standard SQLite
}

module.exports = { up, down };
