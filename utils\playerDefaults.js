// Default player data structure
const { SKILLS, STATS } = require("../gameConfig");

// Defines the default structure for all skills a new player starts with
function getDefaultSkills() {
  const skills = {};
  // Use SKILLS constant if available, otherwise define known skills
  const skillNames = SKILLS?.LIST || [
    "combat",
    "fishing",
    "mining",
    "farming",
    "foraging",
  ];

  skillNames.forEach((skillName) => {
    // All skills just need an exp property - levels are calculated from exp
    skills[skillName] = {
      exp: 0,
    };
  });
  return skills;
}

// Defines the default structure for all stats a new player starts with
function getDefaultStats() {
  const stats = {};
  for (const statKey in STATS) {
    stats[statKey] = {
      base: STATS[statKey].base || 0, // Get base value from gameConfig or default to 0
      fromLevels: 0,
      fromSlayers: 0,
      fromEquipment: 0,
      fromPet: 0, // Added fromPet
      fromSetBonus: 0, // Added fromSetBonus
      fromAccessories: 0, // Added fromAccessories
      fromMagicalPower: 0, // Added fromMagicalPower
      fromPetScore: 0, // Added fromPetScore for Magic Find bonus
    };
  }
  // Ensure Fishing Speed gets its default components if defined
  if (STATS.FISHING_SPEED && stats.FISHING_SPEED) {
    // Check if stats.FISHING_SPEED exists before trying to modify
    stats.FISHING_SPEED = {
      base: STATS.FISHING_SPEED.base || 0,
      fromLevels: 0,
      fromSlayers: 0,
      fromEquipment: 0,
      fromPet: 0, // Added fromPet for consistency
      fromSetBonus: 0, // Added fromSetBonus for consistency
      fromAccessories: 0, // Added fromAccessories for consistency
      fromMagicalPower: 0, // Added fromMagicalPower for consistency
      fromPetScore: 0, // Added fromPetScore for consistency
    };
  }
  return stats;
}

const getDefaultPlayerData = (discordId, name) => ({
  discordId: discordId,
  name: name,
  rank: "MEMBER",
  current_region: "The Hub",
  current_health: 0, // Will be set based on max health later
  currencies: {
    coins: 0,
    bank: 0,
    gems: 0,
  },
  skills: getDefaultSkills(),
  stats: getDefaultStats(),
  inventory: {
    items: {}, // Stackable items (itemKey: quantity)
    equipment: [], // Unique items (array of {itemKey, isEquipped, ...other unique stats})
  },
  collections: {},
  pets: [],
  active_pet_id: null,
  island: {
    placedMinions: [], // NEW: Array for placed minion objects
  },
  visiting_island_owner_id: null, // Existing field
  minion_storage_json: [], // NEW: Array for unplaced minion objects
  crafted_minions_json: [], // NEW: Array of unique minion craft identifiers (e.g., "KEY_T1")
  cakeBag: [], // Stores { year: number, obtainedTimestampUTC: number }
  lastClaimedYear: 0, // Tracks the last SB year a cake was claimed for
  mob_kills_json: {}, // Tracks mob kill counts by mobKey
});

module.exports = { getDefaultSkills, getDefaultStats, getDefaultPlayerData };
