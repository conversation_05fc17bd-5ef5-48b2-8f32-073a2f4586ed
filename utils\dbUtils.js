const db = require("./database"); // Import the raw db connection

/**
 * Executes a db.all query with the given SQL and parameters, returning a Promise.
 * Includes retry logic for SQLITE_BUSY errors.
 * @param {string} sql The SQL query to execute.
 * @param {Array<any>} [params=[]] Optional parameters for the SQL query.
 * @param {number} [retries=3] Number of retry attempts for SQLITE_BUSY errors.
 * @returns {Promise<Array<any>>} A promise that resolves with an array of rows, or rejects with an error.
 */
function dbAll(sql, params = [], retries = 3) {
  return new Promise((resolve, reject) => {
    const attemptAll = (attemptNumber) => {
      db.all(sql, params, (err, rows) => {
        if (err) {
          // retry on SQLITE_BUSY errors
          if (err.message.includes("SQLITE_BUSY") && attemptNumber < retries) {
            console.warn(
              `DB Busy (dbAll) - retry ${attemptNumber + 1}/${retries}:`,
              sql.substring(0, 50) + "..."
            );
            setTimeout(
              () => attemptAll(attemptNumber + 1),
              100 * attemptNumber
            ); // exponential backoff
            return;
          }
          console.error(
            "DB Error (dbAll):",
            err.message,
            "\nSQL:",
            sql,
            "\nParams:",
            params
          );
          reject(err);
        } else {
          resolve(rows);
        }
      });
    };
    attemptAll(0);
  });
}

/**
 * Executes a db.get query with the given SQL and parameters, returning a Promise.
 * Includes retry logic for SQLITE_BUSY errors.
 * @param {string} sql The SQL query to execute.
 * @param {Array<any>} [params=[]] Optional parameters for the SQL query.
 * @param {number} [retries=3] Number of retry attempts for SQLITE_BUSY errors.
 * @returns {Promise<any | undefined>} A promise that resolves with the first row found, or undefined if no rows are found, or rejects with an error.
 */
function dbGet(sql, params = [], retries = 3) {
  return new Promise((resolve, reject) => {
    const attemptGet = (attemptNumber) => {
      db.get(sql, params, (err, row) => {
        if (err) {
          // retry on SQLITE_BUSY errors
          if (err.message.includes("SQLITE_BUSY") && attemptNumber < retries) {
            console.warn(
              `DB Busy (dbGet) - retry ${attemptNumber + 1}/${retries}:`,
              sql.substring(0, 50) + "..."
            );
            setTimeout(
              () => attemptGet(attemptNumber + 1),
              100 * attemptNumber
            ); // exponential backoff
            return;
          }
          console.error(
            "DB Error (dbGet):",
            err.message,
            "\nSQL:",
            sql,
            "\nParams:",
            params
          );
          reject(err);
        } else {
          resolve(row); // Resolves with undefined if no row found, which is standard for db.get
        }
      });
    };
    attemptGet(0);
  });
}

// --- Batched write support -------------------------------------------------
// We expose *two* write helpers:
//   1) dbRun        – direct, immediate write (the original behaviour).
//   2) dbRunQueued – goes through writeQueue.enqueue so many statements can
//                    be flushed together.
// Existing code that opens its own BEGIN/COMMIT must keep using dbRun.

const { enqueue } = require("./writeQueue");

/**
 * Executes a db.run query with the given SQL and parameters, returning a Promise.
 * Includes retry logic for SQLITE_BUSY errors.
 * @param {string} sql The SQL query to execute.
 * @param {Array<any>} [params=[]] Optional parameters for the SQL query.
 * @param {number} [retries=3] Number of retry attempts for SQLITE_BUSY errors.
 * @returns {Promise<{lastID: number, changes: number}>} A promise that resolves with lastID and changes, or rejects with an error.
 */
function dbRun(sql, params = [], retries = 3) {
  return new Promise((resolve, reject) => {
    const attemptRun = (attemptNumber) => {
      db.run(sql, params, function (err) {
        if (err) {
          // retry on SQLITE_BUSY errors
          if (err.message.includes("SQLITE_BUSY") && attemptNumber < retries) {
            console.warn(
              `DB Busy (dbRun) - retry ${attemptNumber + 1}/${retries}:`,
              sql.substring(0, 50) + "..."
            );
            setTimeout(
              () => attemptRun(attemptNumber + 1),
              100 * attemptNumber
            ); // exponential backoff
            return;
          }
          console.error(
            "DB Error (dbRun):",
            err.message,
            "\nSQL:",
            sql,
            "\nParams:",
            params
          );
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    };
    attemptRun(0);
  });
}

// Queued variant for background/timer workloads
function dbRunQueued(sql, params = []) {
  return enqueue(sql, params);
}

module.exports = {
  dbAll,
  dbGet,
  dbRun,
  dbRunQueued,
};
