/**
 * Stripe Webhook Handler
 * Handles real-time payment confirmations from Stripe
 */

const {
  validateWebhookSignature,
  handlePaymentSuccess,
} = require("./stripeUtils");

/**
 * Handles incoming Stripe webhooks
 * @param {express.Request} req - Express request object
 * @param {express.Response} res - Express response object
 * @param {Client} discordClient - Discord client for notifications
 */
async function handleStripeWebhook(req, res, discordClient) {
  const signature = req.headers["stripe-signature"];

  try {
    // Validate webhook signature
    const event = validateWebhookSignature(req.body, signature);

    if (!event) {
      console.error("[WebhookHandler] Invalid webhook signature");
      return res.status(400).send("Invalid signature");
    }

    // <PERSON>le completed payments
    if (event.type === "checkout.session.completed") {
      const success = await handlePaymentSuccess(event);

      if (success) {
        console.log(
          `[WebhookHandler] Successfully processed payment: ${event.data.object.id}`
        );

        // Send Discord notifications if needed
        const session = event.data.object;
        if (session.metadata && discordClient) {
          // This could trigger Discord notifications here
          // Already handled in stripeUtils.js via polling
        }
      } else {
        console.error(
          `[WebhookHandler] Failed to process payment: ${event.data.object.id}`
        );
        return res.status(500).send("Payment processing failed");
      }
    }

    res.status(200).send("Webhook received");
  } catch (error) {
    console.error("[WebhookHandler] Error processing webhook:", error);
    res.status(500).send("Webhook processing error");
  }
}

module.exports = {
  handleStripeWebhook,
};
