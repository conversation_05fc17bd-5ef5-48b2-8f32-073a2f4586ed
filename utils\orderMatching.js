const { dbAll } = require("./dbUtils");
const {
  executeMarketTransaction,
  withMarketLock,
  createUserItemLock: _createUserItemLock,
} = require("./marketTransactions");

/**
 * Immediately processes order matching when a new order is created
 * This prevents any edge cases where matching orders exist but aren't completed
 */

/**
 * Processes automatic matching for a newly created sell order
 * Finds matching buy orders and executes trades immediately
 */
async function processNewSellOrder(
  sellOrderId,
  sellerId,
  itemKey,
  quantity,
  pricePerUnit,
  client = null
) {
  // use item-specific lock to prevent race conditions between different order types
  const lockKey = `order_match_sell_${itemKey}`;

  return await withMarketLock(lockKey, async () => {
    console.log(
      `[Order Matching] Processing new sell order ${sellOrderId} for ${itemKey}`
    );

    // find all matching buy orders (price >= sell price, sorted by best price then oldest)
    const matchingBuyOrders = await dbAll(
      `
      SELECT * FROM market_buy_orders 
      WHERE item_key = ? AND price_per_unit >= ? AND quantity > 0
      ORDER BY price_per_unit DESC, created_at ASC
    `,
      [itemKey, pricePerUnit]
    );

    if (matchingBuyOrders.length === 0) {
      console.log(
        `[Order Matching] No matching buy orders found for sell order ${sellOrderId}`
      );
      return { matches: 0, remainingQuantity: quantity };
    }

    let remainingQuantity = quantity;
    let matchCount = 0;
    const completedTrades = [];

    for (const buyOrder of matchingBuyOrders) {
      if (remainingQuantity <= 0) break;

      // determine how much we can trade
      const tradeQuantity = Math.min(remainingQuantity, buyOrder.quantity);
      // use buyer's price (they offered higher or equal to our ask)
      const tradePrice = buyOrder.price_per_unit;

      try {
        // execute the trade using existing atomic transaction system
        const result = await executeMarketTransaction(
          {
            buyer: buyOrder.buyer_id,
            seller: sellerId,
            itemKey,
            quantity: tradeQuantity,
            pricePerUnit: tradePrice,
            sellOrderId,
            buyOrderId: buyOrder.id,
            transactionType: "automatic_match",
          },
          client
        );

        if (result.success) {
          remainingQuantity -= tradeQuantity;
          matchCount++;

          completedTrades.push({
            buyerId: buyOrder.buyer_id,
            sellerId,
            quantity: tradeQuantity,
            pricePerUnit: tradePrice,
            totalValue: tradeQuantity * tradePrice,
          });

          console.log(
            `[Order Matching] Successfully matched ${tradeQuantity}x ${itemKey} at ${tradePrice} coins each`
          );
        } else {
          console.error(
            `[Order Matching] Failed to execute trade: ${result.error}`
          );
          // continue with next order if this one fails
        }
      } catch (error) {
        console.error(`[Order Matching] Error executing trade:`, error);
        // continue with next order if this one fails
      }
    }

    console.log(
      `[Order Matching] Completed ${matchCount} automatic matches for sell order ${sellOrderId}`
    );

    return {
      matches: matchCount,
      remainingQuantity,
      completedTrades,
    };
  });
}

/**
 * Processes automatic matching for a newly created buy order
 * Finds matching sell orders and executes trades immediately
 */
async function processNewBuyOrder(
  buyOrderId,
  buyerId,
  itemKey,
  quantity,
  pricePerUnit,
  client = null
) {
  // use item-specific lock to prevent race conditions between different order types
  const lockKey = `order_match_buy_${itemKey}`;

  return await withMarketLock(lockKey, async () => {
    console.log(
      `[Order Matching] Processing new buy order ${buyOrderId} for ${itemKey}`
    );

    // find all matching sell orders (price <= buy price, sorted by best price then oldest)
    const matchingSellOrders = await dbAll(
      `
      SELECT * FROM market_sell_orders 
      WHERE item_key = ? AND price_per_unit <= ? AND quantity > 0
      ORDER BY price_per_unit ASC, created_at ASC
    `,
      [itemKey, pricePerUnit]
    );

    if (matchingSellOrders.length === 0) {
      console.log(
        `[Order Matching] No matching sell orders found for buy order ${buyOrderId}`
      );
      return { matches: 0, remainingQuantity: quantity };
    }

    let remainingQuantity = quantity;
    let matchCount = 0;
    const completedTrades = [];

    for (const sellOrder of matchingSellOrders) {
      if (remainingQuantity <= 0) break;

      // determine how much we can trade
      const tradeQuantity = Math.min(remainingQuantity, sellOrder.quantity);
      // use seller's price (they asked lower or equal to our bid)
      const tradePrice = sellOrder.price_per_unit;

      try {
        // execute the trade using existing atomic transaction system
        const result = await executeMarketTransaction(
          {
            buyer: buyerId,
            seller: sellOrder.seller_id,
            itemKey,
            quantity: tradeQuantity,
            pricePerUnit: tradePrice,
            sellOrderId: sellOrder.id,
            buyOrderId,
            transactionType: "automatic_match",
          },
          client
        );

        if (result.success) {
          remainingQuantity -= tradeQuantity;
          matchCount++;

          completedTrades.push({
            buyerId,
            sellerId: sellOrder.seller_id,
            quantity: tradeQuantity,
            pricePerUnit: tradePrice,
            totalValue: tradeQuantity * tradePrice,
          });

          console.log(
            `[Order Matching] Successfully matched ${tradeQuantity}x ${itemKey} at ${tradePrice} coins each`
          );
        } else {
          console.error(
            `[Order Matching] Failed to execute trade: ${result.error}`
          );
          // continue with next order if this one fails
        }
      } catch (error) {
        console.error(`[Order Matching] Error executing trade:`, error);
        // continue with next order if this one fails
      }
    }

    console.log(
      `[Order Matching] Completed ${matchCount} automatic matches for buy order ${buyOrderId}`
    );

    return {
      matches: matchCount,
      remainingQuantity,
      completedTrades,
    };
  });
}

/**
 * Attempts to match any existing orders for an item
 * This can be called manually or as a cleanup function
 */
async function processExistingOrders(itemKey, client = null) {
  const lockKey = `order_match_${itemKey}`;

  return await withMarketLock(lockKey, async () => {
    console.log(`[Order Matching] Processing existing orders for ${itemKey}`);

    // get all orders for this item
    const [sellOrders, buyOrders] = await Promise.all([
      dbAll(
        `
        SELECT * FROM market_sell_orders 
        WHERE item_key = ?
        ORDER BY price_per_unit ASC, created_at ASC
      `,
        [itemKey]
      ),
      dbAll(
        `
        SELECT * FROM market_buy_orders 
        WHERE item_key = ?
        ORDER BY price_per_unit DESC, created_at ASC
      `,
        [itemKey]
      ),
    ]);

    if (sellOrders.length === 0 || buyOrders.length === 0) {
      return { matches: 0, message: "No orders to match" };
    }

    let matchCount = 0;
    const completedTrades = [];

    // try to match orders
    for (const sellOrder of sellOrders) {
      for (const buyOrder of buyOrders) {
        // check if prices are compatible
        if (buyOrder.price_per_unit >= sellOrder.price_per_unit) {
          // determine trade quantity
          const tradeQuantity = Math.min(sellOrder.quantity, buyOrder.quantity);
          // use seller's price (lower of the two)
          const tradePrice = sellOrder.price_per_unit;

          try {
            const result = await executeMarketTransaction(
              {
                buyer: buyOrder.buyer_id,
                seller: sellOrder.seller_id,
                itemKey,
                quantity: tradeQuantity,
                pricePerUnit: tradePrice,
                sellOrderId: sellOrder.id,
                buyOrderId: buyOrder.id,
                transactionType: "manual_match",
              },
              client
            );

            if (result.success) {
              matchCount++;
              completedTrades.push({
                buyerId: buyOrder.buyer_id,
                sellerId: sellOrder.seller_id,
                quantity: tradeQuantity,
                pricePerUnit: tradePrice,
                totalValue: tradeQuantity * tradePrice,
              });

              // update order quantities for next iteration
              sellOrder.quantity -= tradeQuantity;
              buyOrder.quantity -= tradeQuantity;

              console.log(
                `[Order Matching] Manually matched ${tradeQuantity}x ${itemKey} at ${tradePrice} coins each`
              );
            }
          } catch (error) {
            console.error(`[Order Matching] Error in manual match:`, error);
          }
        }
      }
    }

    return {
      matches: matchCount,
      completedTrades,
    };
  });
}

module.exports = {
  processNewSellOrder,
  processNewBuyOrder,
  processExistingOrders,
};
