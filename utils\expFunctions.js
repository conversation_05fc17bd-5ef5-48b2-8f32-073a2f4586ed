// Define the CUMULATIVE experience required to reach each level according to Hypixel Skyblock Wiki
// Index 0 is unused (level 0), index 1 corresponds to Level 1 (50 XP total), etc.
// These are TOTAL XP amounts from the "Total" column, not the "Level" column
const xpTable = [
  0, // Level 0 (Unused)
  50, // Level 1 - 50 XP total
  175, // Level 2 - 175 XP total
  375, // Level 3 - 375 XP total
  675, // Level 4 - 675 XP total
  1175, // Level 5 - 1,175 XP total
  1925, // Level 6 - 1,925 XP total
  2925, // Level 7 - 2,925 XP total
  4425, // Level 8 - 4,425 XP total
  6425, // Level 9 - 6,425 XP total
  9925, // Level 10 - 9,925 XP total
  14925, // Level 11 - 14,925 XP total
  22425, // Level 12 - 22,425 XP total
  32425, // Level 13 - 32,425 XP total
  47425, // Level 14 - 47,425 XP total
  67425, // Level 15 - 67,425 XP total
  97425, // Level 16 - 97,425 XP total
  147425, // Level 17 - 147,425 XP total
  222425, // Level 18 - 222,425 XP total
  322425, // Level 19 - 322,425 XP total
  522425, // Level 20 - 522,425 XP total
  822425, // Level 21 - 822,425 XP total
  1222425, // Level 22 - 1,222,425 XP total
  1722425, // Level 23 - 1,722,425 XP total
  2322425, // Level 24 - 2,322,425 XP total
  3022425, // Level 25 - 3,022,425 XP total
  3822425, // Level 26 - 3,822,425 XP total
  4722425, // Level 27 - 4,722,425 XP total
  5722425, // Level 28 - 5,722,425 XP total
  6822425, // Level 29 - 6,822,425 XP total
  8022425, // Level 30 - 8,022,425 XP total
  9322425, // Level 31 - 9,322,425 XP total
  10722425, // Level 32 - 10,722,425 XP total
  12222425, // Level 33 - 12,222,425 XP total
  13822425, // Level 34 - 13,822,425 XP total
  15522425, // Level 35 - 15,522,425 XP total
  17322425, // Level 36 - 17,322,425 XP total
  19222425, // Level 37 - 19,222,425 XP total
  21222425, // Level 38 - 21,222,425 XP total
  23322425, // Level 39 - 23,322,425 XP total
  25522425, // Level 40 - 25,522,425 XP total
  27822425, // Level 41 - 27,822,425 XP total
  30222425, // Level 42 - 30,222,425 XP total
  32722425, // Level 43 - 32,722,425 XP total
  35322425, // Level 44 - 35,322,425 XP total
  38072425, // Level 45 - 38,072,425 XP total
  40972425, // Level 46 - 40,972,425 XP total
  44072425, // Level 47 - 44,072,425 XP total
  47472425, // Level 48 - 47,472,425 XP total
  51172425, // Level 49 - 51,172,425 XP total
  55172425, // Level 50 - 55,172,425 XP total
  59472425, // Level 51 - 59,472,425 XP total
  64072425, // Level 52 - 64,072,425 XP total
  68972425, // Level 53 - 68,972,425 XP total
  74172425, // Level 54 - 74,172,425 XP total
  79672425, // Level 55 - 79,672,425 XP total
  85472425, // Level 56 - 85,472,425 XP total
  91572425, // Level 57 - 91,572,425 XP total
  97972425, // Level 58 - 97,972,425 XP total
  104672425, // Level 59 - 104,672,425 XP total
  111672425, // Level 60 - 111,672,425 XP total
];

/**
 * Gets the experience required to reach the NEXT level.
 * Example: getRequiredExp(1) returns XP needed to complete level 1 (go from 1 to 2), which is 75.
 * Example: getRequiredExp(50) returns XP needed to complete level 50 (go from 50 to 51).
 * @param {number} currentLevel - The current completed level (e.g., 0, 1, 2...).
 * @returns {number} Experience required for the *next* level up, or Infinity if max level.
 */
function getRequiredExp(currentLevel) {
  // Level 0 requires xpTable[1] to reach level 1
  // Level 1 requires xpTable[2] to reach level 2
  const nextLevelIndex = currentLevel + 1;
  if (nextLevelIndex >= xpTable.length) {
    return Infinity; // Max level reached or table doesn't go high enough
  }
  return xpTable[nextLevelIndex] || Infinity;
}

/**
 * Calculates the player's current level and progress towards the next level based on total accumulated experience.
 * @param {number} totalExp - The total accumulated experience points.
 * @returns {object} An object containing { level: number, currentLevelExp: number, requiredExpForNextLevel: number }.
 *                  level: The current completed level.
 *                  currentLevelExp: Experience points accumulated *into* the current level (progress towards next).
 *                  requiredExpForNextLevel: Experience points needed to complete the current level and reach the next level.
 */
function getLevelFromExp(totalExp) {
  if (totalExp < 0) totalExp = 0;

  let currentLevel = 0;

  // Find the highest level where totalExp >= xpTable[level]
  for (let level = 1; level < xpTable.length; level++) {
    const expRequiredForThisLevel = xpTable[level];
    if (expRequiredForThisLevel === undefined) break; // Stop if table ends

    if (totalExp >= expRequiredForThisLevel) {
      currentLevel = level;
    } else {
      break; // Not enough XP for this level
    }
  }

  // Calculate progress into the current level
  let currentLevelExp = 0;
  let requiredExpForNextLevel = Infinity;

  if (currentLevel === 0) {
    // Level 0: progress is total XP, requirement is for level 1
    currentLevelExp = totalExp;
    requiredExpForNextLevel = xpTable[1] || Infinity;
  } else {
    // Calculate XP into current level (progress towards next level)
    const expRequiredForCurrentLevel = xpTable[currentLevel] || 0;
    currentLevelExp = totalExp - expRequiredForCurrentLevel;

    // Get total cumulative XP requirement for next level
    const totalExpForNextLevel = xpTable[currentLevel + 1];

    if (totalExpForNextLevel !== undefined) {
      // Calculate XP needed to complete the current level (for progress bar display)
      requiredExpForNextLevel =
        totalExpForNextLevel - expRequiredForCurrentLevel;
    } else {
      // Max level reached
      requiredExpForNextLevel = Infinity;
    }
  }

  return {
    level: currentLevel, // The completed level number
    currentLevelExp: currentLevelExp, // XP progress into the current level (towards next)
    requiredExpForNextLevel: requiredExpForNextLevel, // XP needed to complete the current level and reach the next level
  };
}

/**
 * Calculates the total cumulative experience based on level and progress into that level.
 * @param {number} level - The current completed level (e.g., 1, 2, ...).
 * @param {number} progressExp - The experience accumulated *into* the next level.
 * @returns {number} The total cumulative experience.
 */
function calculateTotalExp(level, progressExp) {
  let total = 0;
  // Sum XP required for all levels *up to* the current completed level
  for (let i = 1; i <= level; i++) {
    total += xpTable[i] || 0; // Add requirement for level i
  }
  // Add the progress into the current level
  total += progressExp || 0;
  return total;
}

module.exports = {
  xpTable,
  getRequiredExp,
  getLevelFromExp,
  calculateTotalExp,
};
