const { SlashCommandBuilder } = require("discord.js");
const { handleSkillActionExecution } = require("../utils/skillActionUtils.js");
const { farmingSkillConfig } = require("../data/skillConfigs");
const {
  getAbortSignal,
  cancellableDelay,
} = require("../utils/instantStopUtils");
const { findCollectionForItem } = require("../utils/collectionUtils");

// conditional imports for worker bot compatibility
const isWorkerBot = process.env.IS_WORKER_BOT === "true";
const playerDataModule = isWorkerBot
  ? require("../utils/workerDatabaseProxy")
  : require("../utils/playerDataManager");

const farmingAnimation = async (
  messageId,
  channel,
  embed,
  farmingTime,
  stopRequestMap,
  actionId,
  auxResourceConsumed,
  resourceEmoji
) => {
  const farmerEmoji = "👨‍🌾";
  const displayEmoji = resourceEmoji || "❓";
  const steps = 2;
  const trackLength = 15;

  // fixed timing: 1000ms per step as akin wants
  const stepDuration = 1000;

  // Preserve the original footer if it exists
  const originalFooter = embed.toJSON().footer;

  for (let i = 0; i <= steps; i++) {
    // Check for stop before each animation step (immediate responsiveness)
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Farm] Stop requested before animation step ${i} for action ${actionId}.`
      );
      break;
    }

    const farmerPosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(farmerPosition)}${farmerEmoji}${" ".repeat(trackLength - farmerPosition)}`;
    embed.setDescription(`\`${movingPart}\` ${displayEmoji}`);

    // Restore the footer if it was originally present
    if (originalFooter) {
      embed.setFooter({ text: originalFooter.text });
    }

    try {
      // use animation queue to respect discord rate limits
      const { queueMessageEdit } = require("../utils/animationQueue");
      await queueMessageEdit(channel, messageId, { embeds: [embed] });
    } catch (editError) {
      console.log(
        `[Animation][Farm] Minor error editing animation message (action ${actionId}): ${editError.message}`
      );
      break;
    }

    // Check for stop after each animation step (immediate responsiveness)
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Farm] Stop requested after animation step ${i} for action ${actionId}.`
      );
      break;
    }

    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Farm] Animation cancelled during delay for action ${actionId}.`
          );
          break;
        }
        throw error;
      }
    }
  }
};

async function handleFarmAction(
  interaction,
  resourceKey,
  amount,
  isAgainCommandOrResumption = false,
  originalTotalAmountFromResumption = null,
  character
) {
  const isResumption = interaction.isResumption || false;

  // Set up the correct amount and resource key based on whether this is a new action or resumption
  let actualAmount = amount;

  if (isResumption) {
    // For resumption, we need to use the original total amount for loop logic
    actualAmount = originalTotalAmountFromResumption;
    // The resourceKey and initialCompletedCycles are already set correctly from actionResumption.js
  }

  const normalizedResourceKey =
    typeof resourceKey === "string" ? resourceKey.toUpperCase() : "";

  // Garden crop validation - only check if in Garden and not resuming
  if (!isResumption && character.current_region === "garden") {
    const { getGardenLevel, isCropUnlocked } = require("../utils/gardenSystem");
    const gardenLevel = getGardenLevel(character.garden_xp || 0);

    if (!isCropUnlocked(normalizedResourceKey, gardenLevel)) {
      const configManager = require("../utils/configManager");
      const cropData = configManager.getAllItems()[normalizedResourceKey];
      const cropName = cropData?.name || normalizedResourceKey;

      // Use editReply since handleSkillCommand() defers the interaction
      return interaction.editReply({
        content: `❌ **${cropName}** is not unlocked in your Garden yet! You need a higher Garden Level to farm this crop.`,
      });
    }
  }

  const result = await handleSkillActionExecution(
    interaction,
    character,
    normalizedResourceKey,
    actualAmount,
    isResumption ? false : isAgainCommandOrResumption,
    farmingSkillConfig
  );

  return result;
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("farm")
    .setDescription("Farm crops in your current region")
    .addStringOption((option) =>
      option
        .setName("crop")
        .setDescription("Type of crop to farm")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'Number of repetitions (e.g., 5, or type "max"). Defaults to your max allowed amount.'
        )
        .setRequired(false)
    ),

  async execute(interaction) {
    // Extract and validate parameters before delegation
    const {
      extractAndValidateSkillCommandParams,
    } = require("../utils/commandUtils");
    const params = await extractAndValidateSkillCommandParams(
      interaction,
      farmingSkillConfig
    );

    if (!params) {
      // extractAndValidateSkillCommandParams already sent error response
      return;
    }

    // Delegate to worker bot
    const { workerManager } = require("../utils/workerManager");
    await workerManager.delegateAction(
      interaction,
      "farm",
      params.amount,
      params.resourceKey,
      params.wasMax
    );
  },

  farmingAnimation,
  handleFarmAction,

  async autocomplete(interaction) {
    const { getPlayerData } = playerDataModule;
    const {
      getGardenLevel,
      getUnlockedCrops,
      isInGarden,
    } = require("../utils/gardenSystem");
    const { getLevelFromExp } = require("../utils/expFunctions");
    const configManager = require("../utils/configManager");

    try {
      // Avoid responding to very old interactions which Discord will reject with 10062
      if (
        interaction.createdTimestamp &&
        Date.now() - interaction.createdTimestamp > 14 * 60 * 1000
      ) {
        return;
      }
      const userId = interaction.user.id;
      const character = await getPlayerData(userId);

      if (!character) {
        // Fallback to empty if no character
        try {
          await interaction.respond([]);
        } catch (e) {
          if (e?.code !== 10062)
            console.warn(
              "[Farm] autocomplete empty respond failed:",
              e?.message || e
            );
        }
        return;
      }

      const focusedValue = interaction.options.getFocused().toLowerCase();
      const allItems = configManager.getAllItems();
      const currentRegion = character.current_region;

      // Calculate farming skill level for level requirements
      const farmingData = character.skills.farming;
      const farmingLevel = farmingData
        ? getLevelFromExp(farmingData.exp || 0).level
        : 0;

      // Check if player is in Garden
      if (isInGarden(character)) {
        // In garden: only show unlocked crops
        const gardenLevel = getGardenLevel(character.garden_xp || 0);
        const unlockedCrops = getUnlockedCrops(gardenLevel);

        const cropChoices = [];
        for (const cropKey of unlockedCrops) {
          const cropData = allItems[cropKey];
          if (cropData && cropData.sourceSkill === "Farming") {
            cropChoices.push({
              name: cropData.name,
              value: cropKey,
            });
          }
        }

        const filtered = cropChoices.filter((choice) =>
          choice.name.toLowerCase().includes(focusedValue)
        );

        try {
          await interaction.respond(
            filtered.slice(0, 25).map((choice) => ({
              name: choice.name,
              value: choice.value,
            }))
          );
        } catch (e) {
          if (e?.code !== 10062)
            console.warn(
              "[Farm] autocomplete respond failed:",
              e?.message || e
            );
        }
      } else {
        // Outside Garden: show crops available in current region
        const availableResources = Object.entries(allItems)
          .filter(([key, item]) => {
            return (
              item.sourceSkill === "Farming" &&
              findCollectionForItem(key) !== null &&
              item.foundInRegions &&
              item.foundInRegions.includes(currentRegion) &&
              (item.requiredLevel === undefined ||
                item.requiredLevel <= farmingLevel)
            );
          })
          .map(([key, item]) => ({
            name: item.name,
            value: key,
          }))
          .filter((choice) => choice.name.toLowerCase().includes(focusedValue))
          .slice(0, 25);

        try {
          await interaction.respond(availableResources);
        } catch (e) {
          if (e?.code !== 10062)
            console.warn(
              "[Farm] autocomplete respond failed:",
              e?.message || e
            );
        }
      }
    } catch (error) {
      console.error("[Farm Autocomplete] Error:", error);
      // Fallback to empty on error
      try {
        await interaction.respond([]);
      } catch {}
    }
  },
};
