/**
 * Mob Abilities System
 * Handles special abilities that mobs can use during combat
 */

/**
 * Process mob abilities during combat
 * @param {Object} mobInstanceData - The mob instance data
 * @param {Object} character - The player character
 * @param {number} turn - Current turn number
 * @param {Array} turnEvents - Array to add events to
 * @returns {Object} - Object containing any character modifications
 */
async function processMobAbilities(
  mobInstanceData,
  character,
  turn,
  _turnEvents
) {
  const effects = {
    damageDealt: 0,
    healingDone: 0,
    statusEffects: [],
    messages: [],
  };

  // Check if mob has abilities
  if (!mobInstanceData.abilities || !Array.isArray(mobInstanceData.abilities)) {
    return effects;
  }

  // Process each ability
  for (const ability of mobInstanceData.abilities) {
    if (!ability.enabled) continue;

    const abilityResult = await processIndividualAbility(
      ability,
      mobInstanceData,
      character,
      turn
    );

    if (abilityResult.triggered) {
      effects.damageDealt += abilityResult.damage || 0;
      effects.healingDone += abilityResult.healing || 0;
      effects.statusEffects.push(...(abilityResult.statusEffects || []));
      effects.messages.push(...(abilityResult.messages || []));
    }
  }

  return effects;
}

/**
 * Process an individual mob ability
 * @param {Object} ability - The ability configuration
 * @param {Object} mobInstanceData - The mob instance data
 * @param {Object} character - The player character
 * @param {number} turn - Current turn number
 * @returns {Object} - Ability processing result
 */
async function processIndividualAbility(
  ability,
  mobInstanceData,
  character,
  turn
) {
  const result = {
    triggered: false,
    damage: 0,
    healing: 0,
    statusEffects: [],
    messages: [],
  };

  // Check if ability should trigger this turn
  if (!shouldAbilityTrigger(ability, turn)) {
    return result;
  }

  result.triggered = true;

  switch (ability.type) {
    case "LIFE_DRAIN":
      return processLifeDrain(ability, mobInstanceData, character, result);

    case "POISON":
      return processPoison(ability, mobInstanceData, character, result);

    case "REGENERATION":
      return processRegeneration(ability, mobInstanceData, character, result);

    case "BACKSTAB":
      return await processBackstab(ability, mobInstanceData, character, result);

    default:
      console.warn(`[MobAbilities] Unknown ability type: ${ability.type}`);
      result.triggered = false;
      return result;
  }
}

/**
 * Check if an ability should trigger this turn
 * @param {Object} ability - The ability configuration
 * @param {number} turn - Current turn number
 * @returns {boolean} - Whether the ability should trigger
 */
function shouldAbilityTrigger(ability, turn) {
  // Skip turn 1 for most abilities to let combat start normally
  if (turn <= 1) return false;

  // Check interval-based triggering
  if (ability.interval && typeof ability.interval === "number") {
    return turn % ability.interval === 0;
  }

  // Check turn-based triggering (every X turns)
  if (ability.turnInterval && typeof ability.turnInterval === "number") {
    return (turn - 2) % ability.turnInterval === 0; // Start from turn 2
  }

  // Default to every 5 turns if no interval specified
  return turn % 5 === 0;
}

/**
 * Process Life Drain ability
 * @param {Object} ability - The ability configuration
 * @param {Object} mobInstanceData - The mob instance data
 * @param {Object} character - The player character
 * @param {Object} result - The result object to modify
 * @returns {Object} - Modified result object
 */
function processLifeDrain(ability, mobInstanceData, character, result) {
  const basePercentage = ability.healthPercentage || 2.5; // Default 2.5%

  // Get max health from totalStats (calculated stats) or fallback methods
  let maxHealth;
  if (character.totalStats?.HEALTH) {
    maxHealth = character.totalStats.HEALTH;
  } else if (character.stats?.HEALTH) {
    // Fallback: calculate total from stat components
    const healthStat = character.stats.HEALTH;
    maxHealth =
      (healthStat.base || 0) +
      (healthStat.fromLevels || 0) +
      (healthStat.fromSlayers || 0) +
      (healthStat.fromEquipment || 0) +
      (healthStat.fromPet || 0) +
      (healthStat.fromSetBonus || 0) +
      (healthStat.fromAccessories || 0) +
      (healthStat.fromMagicalPower || 0);
  } else {
    maxHealth = character.current_health || 100; // Final fallback
  }

  // Calculate damage as percentage of player's max health
  const damageAmount = Math.floor(maxHealth * (basePercentage / 100));

  // Ensure minimum damage of 1
  const finalDamage = Math.max(1, damageAmount);

  // Get ability name dynamically (fallback to "Life Drain" if not specified)
  const abilityName = ability.name || "Life Drain";

  result.damage = finalDamage;
  result.messages.push(`Uses ${abilityName}. You take ${finalDamage} damage`);

  // Optionally heal the mob (if specified in ability config)
  if (ability.healMob) {
    const healAmount = Math.floor(finalDamage * (ability.healRatio || 0.5));
    result.healing = healAmount;
    result.messages.push(`Heals for ${healAmount} health from your life force`);
  }

  console.log(
    `[MobAbilities] Life Drain: ${finalDamage} damage to player (${basePercentage}% of ${maxHealth} max health)`
  );

  return result;
}

/**
 * Process Poison ability
 * @param {Object} ability - The ability configuration
 * @param {Object} mobInstanceData - The mob instance data
 * @param {Object} character - The player character
 * @param {Object} result - The result object to modify
 * @returns {Object} - Modified result object
 */
function processPoison(ability, mobInstanceData, character, result) {
  const damage = ability.damage || 10;
  const duration = ability.duration || 3;
  const abilityName = ability.name || "Poison";

  result.damage = damage;
  result.statusEffects.push({
    type: "poison",
    duration: duration,
    damage: damage,
  });
  result.messages.push(`Uses ${abilityName}. You take ${damage} poison damage`);

  return result;
}

/**
 * Process Regeneration ability (for the mob)
 * @param {Object} ability - The ability configuration
 * @param {Object} mobInstanceData - The mob instance data
 * @param {Object} character - The player character
 * @param {Object} result - The result object to modify
 * @returns {Object} - Modified result object
 */
function processRegeneration(ability, mobInstanceData, character, result) {
  const healAmount =
    ability.healAmount || Math.floor(mobInstanceData.stats.health * 0.05); // 5% of max health
  const abilityName = ability.name || "Regeneration";

  result.healing = healAmount;
  result.messages.push(`Uses ${abilityName}. Regenerates ${healAmount} health`);

  return result;
}

/**
 * Process Backstab ability
 * @param {Object} ability - The ability configuration
 * @param {Object} mobInstanceData - The mob instance data
 * @param {Object} character - The player character
 * @param {Object} result - The result object to modify
 * @returns {Object} - Modified result object
 */
async function processBackstab(ability, mobInstanceData, character, result) {
  const damageMultiplier = ability.damageMultiplier || 1.5; // Default +50% damage
  const abilityName = ability.name || "Backstab";

  // Calculate base damage from mob stats
  const baseDamage = mobInstanceData.stats?.damage || 0;
  const backstabDamage = Math.floor(baseDamage * damageMultiplier);

  // Apply damage reduction from spider accessories for arthropod mobs
  let damageAfterReduction = backstabDamage;
  if (character.discordId) {
    try {
      const { calculateAccessoryDamageReduction } = require("./damage");
      const damageReduction = await calculateAccessoryDamageReduction(
        character,
        mobInstanceData,
        false
      );
      damageAfterReduction = Math.floor(
        backstabDamage * (1 - damageReduction / 100)
      );

      if (damageReduction > 0) {
        console.log(
          `[MobAbilities] Backstab: Applied ${damageReduction}% damage reduction from accessories`
        );
      }
    } catch (error) {
      console.warn(
        `[MobAbilities] Backstab: Failed to calculate accessory damage reduction:`,
        error.message
      );
      // Continue with unreduced damage if accessory calculation fails
    }
  }

  // Apply player defense calculation
  const { calculateDamageTaken } = require("./damage");
  const playerMaxHealth =
    character.totalStats?.HEALTH || character.current_health || 100;
  const playerDefense = character.totalStats?.DEFENSE || 0;

  const { damage: finalDamage } = calculateDamageTaken(
    damageAfterReduction,
    playerMaxHealth,
    playerDefense
  );

  result.damage = Math.max(1, finalDamage); // ensure minimum 1 damage
  result.messages.push(
    `Uses ${abilityName}. You take ${Math.round(result.damage)} damage`
  );

  console.log(
    `[MobAbilities] Backstab: ${result.damage} damage (base: ${baseDamage}, multiplier: ${damageMultiplier}x, backstab: ${backstabDamage}, after reduction: ${damageAfterReduction}, final: ${finalDamage})`
  );

  return result;
}

/**
 * Get display information for mob abilities (for inspect command, etc.)
 * @param {Array} abilities - Array of mob abilities
 * @returns {Array} - Array of ability descriptions
 */
function getMobAbilityDescriptions(abilities) {
  if (!abilities || !Array.isArray(abilities)) {
    return [];
  }

  return abilities
    .filter((ability) => ability.enabled)
    .map((ability) => {
      const abilityName = ability.name || ability.type;

      switch (ability.type) {
        case "LIFE_DRAIN": {
          const percentage = ability.healthPercentage || 2.5;
          const interval = ability.turnInterval || 2;
          return `**${abilityName}**: Drains ${percentage}% of your max health every ${interval} turns`;
        }

        case "POISON": {
          const damage = ability.damage || 10;
          const poisonInterval = ability.turnInterval || 3;
          return `**${abilityName}**: Deals ${damage} poison damage every ${poisonInterval} turns`;
        }

        case "REGENERATION": {
          const regenInterval = ability.turnInterval || 4;
          return `**${abilityName}**: Heals itself every ${regenInterval} turns`;
        }

        case "BACKSTAB": {
          const damageMultiplier = ability.damageMultiplier || 1.5;
          const interval = ability.turnInterval || 3;
          const damageBonus = Math.round((damageMultiplier - 1) * 100);
          return `**${abilityName}**: Leaps behind you and attacks for +${damageBonus}% damage every ${interval} turns`;
        }

        default:
          return `❓ **${abilityName}**: Unknown ability`;
      }
    });
}

module.exports = {
  processMobAbilities,
  processIndividualAbility,
  shouldAbilityTrigger,
  processLifeDrain,
  processPoison,
  processRegeneration,
  processBackstab,
  getMobAbilityDescriptions,
};
