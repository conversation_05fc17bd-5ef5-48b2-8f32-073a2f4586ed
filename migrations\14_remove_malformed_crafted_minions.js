const {
  getPlayerData,
  savePlayerData,
} = require("../utils/playerDataManager.js"); // Adjust path as needed

// Helper function to run db.all as a Promise
function dbAllAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) {
        console.error("Database all error:", err.message);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

module.exports = {
  async up(db) {
    console.log(
      "Applying migration: Removing malformed craftedMinions entries (like BASE_T1_T2)...",
    );
    let playersChecked = 0;
    let playersUpdated = 0;
    let errorCount = 0;

    try {
      // Fetch only discord_ids first
      const playerIds = await dbAllAsync(db, "SELECT discord_id FROM players");

      console.log(`  -> Found ${playerIds.length} players to check.`);

      for (const row of playerIds) {
        const userId = row.discord_id;
        playersChecked++;
        try {
          // Load full player data using the manager
          const characterData = await getPlayerData(userId);
          if (
            !characterData ||
            !characterData.craftedMinions ||
            !Array.isArray(characterData.craftedMinions)
          ) {
            continue; // Skip if no data or no craftedMinions array
          }

          const originalCrafted = characterData.craftedMinions;
          const malformedPattern = /^.*_T1_T\d+$/; // Matches strings ending in _T1_T<number>

          // Filter out the bad entries
          const cleanedCraftedMinions = originalCrafted.filter(
            (key) => !malformedPattern.test(key),
          );

          // Check if the array actually changed
          if (cleanedCraftedMinions.length < originalCrafted.length) {
            console.log(
              `   - Player ${userId}: Found malformed entries. Original count: ${originalCrafted.length}, Cleaned count: ${cleanedCraftedMinions.length}`,
            );
            console.log(`     Original: ${JSON.stringify(originalCrafted)}`);
            console.log(
              `     Cleaned:  ${JSON.stringify(cleanedCraftedMinions)}`,
            );

            // Update the character data object
            characterData.crafted_minions_json = cleanedCraftedMinions;

            // Save using the player data manager - IMPORTANT: This relies on savePlayerData working correctly!
            await savePlayerData(userId, characterData);
            playersUpdated++;
          } else {
            // Silent - no malformed entries found
          }
        } catch (playerError) {
          console.error(`  -> Error processing player ${userId}:`, playerError);
          errorCount++;
        }
      }

      console.log("Migration check completed.");
      console.log(`  Players Checked: ${playersChecked}`);
      console.log(`  Players Updated: ${playersUpdated}`);
      if (errorCount > 0) {
        console.error(`  Encountered errors processing ${errorCount} players.`);
      }
      if (playersUpdated > 0) {
        console.warn(
          "  NOTE: Data update relies on savePlayerData correctly persisting the craftedMinions array.",
        );
      }
    } catch (err) {
      console.error("Migration failed:", err);
      throw err; // Re-throw error to halt migration process
    }
  },
};
