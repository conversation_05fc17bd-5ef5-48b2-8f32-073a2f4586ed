/**
 * Adds player_last_active_channel table to track where users last used a command
 * Used for minion fullness notifications
 */
module.exports = {
  /**
   * @param {import('sqlite3').Database} db
   * @returns {Promise<void>}
   */
  up: async (db) => {
    return new Promise((resolve, reject) => {
      db.run(
        `
                CREATE TABLE IF NOT EXISTS player_last_active_channel (
                    discord_id TEXT PRIMARY KEY,
                    channel_id TEXT NOT NULL,
                    updated_at INTEGER NOT NULL
                )
            `,
        (err) => {
          if (err) {
            reject(err);
          } else {
            resolve();
          }
        },
      );
    });
  },
};
