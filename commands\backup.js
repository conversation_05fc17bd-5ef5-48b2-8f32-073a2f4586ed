const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");
const { manualBackup } = require("../utils/databaseBackup");
const { getPlayerData } = require("../utils/playerDataManager");
const { checkRankPermission } = require("../utils/permissionUtils");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("backup")
    .setDescription("Manually create a database backup (Admin only)"),

  async execute(interaction) {
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      const embed = new EmbedBuilder()
        .setTitle("❌ Access Denied")
        .setDescription("Could not verify your character data for permissions.")
        .setColor(EMBED_COLORS.ERROR);

      return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    if (!checkRankPermission(character, "ADMIN")) {
      const embed = new EmbedBuilder()
        .setTitle("❌ Access Denied")
        .setDescription(
          "You do not have permission (Rank Required: ADMIN) to use this command."
        )
        .setColor(EMBED_COLORS.ERROR);

      return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    await interaction.deferReply({ ephemeral: true });

    try {
      manualBackup();

      const embed = new EmbedBuilder()
        .setTitle("✅ Backup Created")
        .setDescription("Database backup has been created successfully.")
        .setColor(EMBED_COLORS.GREEN);

      await interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error("Manual backup error:", error);

      const embed = new EmbedBuilder()
        .setTitle("❌ Backup Failed")
        .setDescription(
          "Failed to create database backup. Check console for details."
        )
        .setColor(EMBED_COLORS.ERROR);

      await interaction.editReply({ embeds: [embed] });
    }
  },
};
