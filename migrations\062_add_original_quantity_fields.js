// add original_quantity field to market orders for progress tracking
module.exports.up = function () {
  return new Promise((resolve, reject) => {
    const sqlite3 = require("sqlite3").verbose();
    const path = require("path");

    const dbPath = path.join(__dirname, "..", "data", "database.db");
    const db = new sqlite3.Database(dbPath);

    try {
      console.log(
        "[Migration 062] Adding original_quantity fields to market orders tables...",
      );

      // add original_quantity to sell orders
      db.run(
        `ALTER TABLE market_sell_orders ADD COLUMN original_quantity INTEGER DEFAULT 1`,
        (err) => {
          if (err && !err.message.includes("duplicate column")) {
            console.error(
              "[Migration 062] Error adding original_quantity to market_sell_orders:",
              err,
            );
            db.close();
            return reject(err);
          }

          // add original_quantity to buy orders
          db.run(
            `ALTER TABLE market_buy_orders ADD COLUMN original_quantity INTEGER DEFAULT 1`,
            (err) => {
              if (err && !err.message.includes("duplicate column")) {
                console.error(
                  "[Migration 062] Error adding original_quantity to market_buy_orders:",
                  err,
                );
                db.close();
                return reject(err);
              }

              // update existing orders to set original_quantity = quantity (for existing orders)
              db.run(
                `UPDATE market_sell_orders SET original_quantity = quantity WHERE original_quantity IS NULL OR original_quantity = 1`,
                (err) => {
                  if (err) {
                    console.error(
                      "[Migration 062] Error updating existing sell orders:",
                      err,
                    );
                    db.close();
                    return reject(err);
                  }

                  db.run(
                    `UPDATE market_buy_orders SET original_quantity = quantity WHERE original_quantity IS NULL OR original_quantity = 1`,
                    (err) => {
                      if (err) {
                        console.error(
                          "[Migration 062] Error updating existing buy orders:",
                          err,
                        );
                        db.close();
                        return reject(err);
                      }

                      console.log(
                        "[Migration 062] Successfully added original_quantity fields to market orders tables",
                      );
                      db.close();
                      resolve();
                    },
                  );
                },
              );
            },
          );
        },
      );
    } catch (error) {
      console.error("[Migration 062] Unexpected error:", error);
      db.close();
      reject(error);
    }
  });
};
