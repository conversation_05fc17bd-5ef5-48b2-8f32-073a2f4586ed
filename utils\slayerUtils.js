const configManager = require("./configManager");

/**
 * Dynamic Slayer Quest Utilities
 * Provides dynamic handling of slayer quest data without hardcoding
 */

/**
 * Gets slayer quest information dynamically from mob data
 * @param {string} questType - The quest type (e.g., 'revenant_horror_ii')
 * @returns {object|null} Quest information object or null if not found
 */
function getSlayerQuestInfo(questType) {
  const mobData = configManager.getMob(questType);

  if (!mobData || !mobData.slayer) {
    return null;
  }

  return {
    questType: questType,
    bossName: mobData.name,
    bossEmoji: mobData.emoji,
    slayerType: mobData.slayer.type,
    combatExpRequired: mobData.slayer.combatExpRequired,
    questCost: mobData.slayer.questCost,
    targetMobType: mobData.slayer.targetMobType,
    xpReward: mobData.slayer.xpReward,
  };
}

/**
 * Creates slayer quest data object for combat tracking
 * @param {object} activeSlayerQuest - Raw quest data from database
 * @param {number} mobCombatExp - Combat EXP per mob kill
 * @param {string} currentMobKey - Current mob being fought
 * @param {number} combatWisdomMultiplier - Combat Wisdom multiplier (default: 1)
 * @returns {object|null} Formatted slayer quest data or null
 */
function createSlayerQuestData(
  activeSlayerQuest,
  mobCombatExp,
  currentMobKey,
  combatWisdomMultiplier = 1
) {
  if (!activeSlayerQuest) {
    return null;
  }

  const questInfo = getSlayerQuestInfo(activeSlayerQuest.type);
  if (!questInfo) {
    console.error(
      `[SlayerUtils] Invalid quest type: ${activeSlayerQuest.type}`
    );
    return null;
  }

  // Calculate combatExpGained from kill counts with Combat Wisdom applied
  let calculatedCombatExp = 0;
  if (activeSlayerQuest.mobKills) {
    const mobs = configManager.getAllMobs();
    for (const [mobType, killCount] of Object.entries(
      activeSlayerQuest.mobKills
    )) {
      const mobData = mobs[mobType];
      if (mobData && mobData.baseExp && mobData.baseExp.amount) {
        // Apply Combat Wisdom multiplier to base Combat EXP
        const baseExp = mobData.baseExp.amount;
        const wisdomBoostedExp = parseFloat(
          (baseExp * combatWisdomMultiplier).toFixed(2)
        );
        calculatedCombatExp += killCount * wisdomBoostedExp;
      }
    }
  }

  // Update the quest data with calculated value
  activeSlayerQuest.combatExpGained = calculatedCombatExp;

  // Calculate dynamic progress with Combat Wisdom applied
  const wisdomBoostedMobExp = parseFloat(
    (mobCombatExp * combatWisdomMultiplier).toFixed(2)
  );
  const killsNeeded = Math.ceil(
    questInfo.combatExpRequired / wisdomBoostedMobExp
  );
  const killsCompleted = Math.floor(calculatedCombatExp / wisdomBoostedMobExp);
  const killsRemaining = Math.max(0, killsNeeded - killsCompleted);

  // Check if current mob is valid for this slayer quest
  const normalizedMobKey = currentMobKey.toLowerCase();
  let isValidMob = false;

  // Check by mob type
  if (questInfo.targetMobType) {
    const currentMobData = configManager.getMob(normalizedMobKey);

    if (currentMobData && currentMobData.mobType) {
      isValidMob =
        currentMobData.mobType.toLowerCase() ===
        questInfo.targetMobType.toLowerCase();
    }
  }

  return {
    isActive: true,
    questType: activeSlayerQuest.type,
    questInfo: questInfo,
    combatExpRequired: questInfo.combatExpRequired,
    combatExpGained: calculatedCombatExp,
    mobCombatExp: wisdomBoostedMobExp, // Use wisdom-boosted value
    originalBaseMobExp: mobCombatExp, // Store original base EXP to prevent back-calculation issues
    killsNeeded: killsNeeded,
    killsCompleted: killsCompleted,
    killsRemaining: killsRemaining,
    bossName: questInfo.bossName, // Use actual name from mob data
    currentMobKey: normalizedMobKey,
    isValidMob: isValidMob,
  };
}

/**
 * Gets the boss key that should spawn for a completed quest
 * @param {string} questType - The quest type (e.g., 'revenant_horror_ii')
 * @returns {string} The boss key to spawn
 */
function getBossKeyForQuest(questType) {
  // The boss key is the same as the quest type since they match mob keys
  return questType;
}

/**
 * Gets the tier number from a slayer quest type
 * @param {string} questType - The quest type (e.g., 'revenant_horror_iii')
 * @returns {number} The tier number (1, 2, 3, etc.)
 */
function getQuestTier(questType) {
  const romanToNumber = {
    i: 1,
    ii: 2,
    iii: 3,
    iv: 4,
    v: 5,
  };

  const match = questType.toLowerCase().match(/_([iv]+)$/);
  if (match) {
    return romanToNumber[match[1].toLowerCase()] || 1;
  }
  return 1;
}

/**
 * Gets eligible mini-bosses for a slayer quest
 * @param {string} slayerType - The slayer type (zombie, spider, etc.)
 * @param {number} questTier - The quest tier
 * @param {string} targetMobType - The target mob type for the quest
 * @returns {Array} Array of eligible mini-boss mob keys
 */
function getEligibleMiniBosses(slayerType, questTier, targetMobType) {
  const eligibleMiniBosses = [];

  for (const [mobKey, mobData] of Object.entries(configManager.getAllMobs())) {
    if (mobData.miniBoss) {
      const miniBossConfig = mobData.miniBoss;

      // Check if this mini-boss matches the quest criteria
      if (
        miniBossConfig.slayerType === slayerType &&
        miniBossConfig.requiredTier <= questTier &&
        miniBossConfig.targetMobType === targetMobType
      ) {
        eligibleMiniBosses.push(mobKey);
      }
    }
  }

  return eligibleMiniBosses;
}

/**
 * Rolls for mini-boss spawn and returns the selected mini-boss key
 * @param {string} slayerType - The slayer type (zombie, spider, etc.)
 * @param {number} questTier - The quest tier
 * @param {string} targetMobType - The target mob type for the quest
 * @returns {string|null} The selected mini-boss key or null if no spawn
 */
function rollForMiniBossSpawn(slayerType, questTier, targetMobType) {
  const eligibleMiniBosses = getEligibleMiniBosses(
    slayerType,
    questTier,
    targetMobType
  );

  if (eligibleMiniBosses.length === 0) {
    return null;
  }

  // Get the spawn chance from the first eligible mini-boss (they should all have the same chance)
  const mobs = configManager.getAllMobs();
  const firstMiniBoss = mobs[eligibleMiniBosses[0]];
  const spawnChance = firstMiniBoss.miniBoss?.spawnChance || 0.15;

  // Roll for spawn
  if (Math.random() < spawnChance) {
    // Randomly select one of the eligible mini-bosses
    const randomIndex = Math.floor(Math.random() * eligibleMiniBosses.length);
    return eligibleMiniBosses[randomIndex];
  }

  return null;
}

module.exports = {
  getSlayerQuestInfo,
  createSlayerQuestData,
  getBossKeyForQuest,
  getQuestTier,
  getEligibleMiniBosses,
  rollForMiniBossSpawn,
};
