function createCustomHealthBar(current, max) {
  max = Math.max(1, max);
  current = Math.max(0, current);

  const healthPercent = current / max;
  const totalSegments = 10;
  const filledSegments = healthPercent * totalSegments;

  const emojis = {
    left: [
      "<:leftempty:1391779731426512980>",
      "<:lefthalf:1391779734601597111>",
      "<:leftfull:1391779732768555149>",
    ],
    mid: [
      "<:midempty:1391779735708897311>",
      "<:midhalf:1391779738397179956>",
      "<:midfull:1391779736924983519>",
    ],
    right: [
      "<:rightempty:1391779739739361452>",
      "<:righthalf:1391779749743034460>",
      "<:rightfull:1391779747813396600>",
    ],
  };

  let healthBarString = "";
  for (let i = 0; i < totalSegments; i++) {
    const segmentPosition = i + 1;
    let segmentType = "mid";
    if (segmentPosition === 1) segmentType = "left";
    if (segmentPosition === totalSegments) segmentType = "right";

    let segmentStateIndex = 0;
    if (filledSegments >= segmentPosition) {
      segmentStateIndex = 2; // full
    } else if (filledSegments >= segmentPosition - 0.5) {
      segmentStateIndex = 1; // half
    }

    healthBarString += emojis[segmentType][segmentStateIndex];
  }

  return healthBarString;
}

module.exports = {
  createCustomHealthBar,
};
