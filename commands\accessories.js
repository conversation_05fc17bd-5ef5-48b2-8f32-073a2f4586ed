const { EMBED_COLORS } = require("../gameConfig.js");
const {
  SlashCommandBuilder,
  EmbedBuilder,
  MessageFlags,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  StringSelectMenuOptionBuilder,
  ComponentType,
} = require("discord.js");
const wikiCommand = require("./wiki.js");
const { getPlayerData } = require("../utils/playerDataManager.js");
const { getPlayerItemCollectionLevel } = require("../utils/collectionUtils");
const { formatDisblockLevel } = require("../utils/disblockXpSystem");
const configManager = require("../utils/configManager");
const {
  getPlayerAccessories,
  equipAccessoryAtomically,
  unequipAccessoryAtomically,
} = require("../utils/accessoryManager");

// Rarity order mapping for sorting (highest to lowest)
const RARITY_ORDER = {
  MYTHIC: 6,
  LEGENDARY: 5,
  EPIC: 4,
  RARE: 3,
  UNCOMMON: 2,
  COMMON: 1,
  SPECIAL: 0,
};

/**
 * Sorts accessories by rarity (highest first)
 * @param {Array} accessories - Array of accessories to sort
 * @param {Object} allItems - Items configuration object
 * @param {boolean} ascending - Optional ascending order flag
 * @returns {Array} - Sorted accessories array
 */
function sortAccessoriesByRarity(accessories, ascending = false) {
  const isAscending = typeof ascending === "boolean" ? ascending : false;

  return accessories.sort((a, b) => {
    const rarityA =
      (typeof a.itemData?.rarity === "string"
        ? a.itemData.rarity
        : a.itemData?.rarity?.name
      )?.toUpperCase() || "COMMON";
    const rarityB =
      (typeof b.itemData?.rarity === "string"
        ? b.itemData.rarity
        : b.itemData?.rarity?.name
      )?.toUpperCase() || "COMMON";

    const orderA = RARITY_ORDER[rarityA] || 1;
    const orderB = RARITY_ORDER[rarityB] || 1;

    return isAscending ? orderA - orderB : orderB - orderA;
  });
}

/**
 * Helper function to split accessories into chunks that fit within character limits
 * @param {Array} accessories - Array of accessories to split
 * @param {Object} allItems - Items configuration object
 * @param {number} maxChunkLength - Maximum character length per chunk
 * @returns {Array} - Array of accessory chunks
 */

/**
 * Builds a progression map for accessory lines based on recipe requirements
 * @param {Object} allItems - Items configuration object
 * @returns {Object} - Map of accessory lines to their progression chains
 */
function buildAccessoryProgressionMap(allItems) {
  const progressionMap = {};

  // Get all accessories with lines
  const accessories = Object.entries(allItems)
    .filter(([, item]) => item.type === "ACCESSORY" && item.accessoryLine)
    .map(([key, item]) => ({ itemKey: key, ...item }));

  // Group by accessory line
  const lineGroups = {};
  accessories.forEach((accessory) => {
    const line = accessory.accessoryLine;
    if (!lineGroups[line]) {
      lineGroups[line] = [];
    }
    lineGroups[line].push(accessory);
  });

  // Build progression chains for each line
  Object.entries(lineGroups).forEach(([line, accessories]) => {
    const progression = [];
    const accessoryMap = {};

    // Create a map for quick lookup
    accessories.forEach((acc) => {
      accessoryMap[acc.itemKey] = acc;
    });

    // Find the starting accessory (one that doesn't require another accessory from the same line)
    const startingAccessory = accessories.find((acc) => {
      if (!acc.recipes || acc.recipes.length === 0) return true;

      // NOTE: Do NOT rely on the `unique` flag – some recipes omit it.
      // An ingredient references a lower-tier accessory if its `itemKey` exists in this line's accessory map.
      const requiresLineAccessory = acc.recipes.some((recipe) =>
        recipe.ingredients.some(
          (ingredient) => accessoryMap[ingredient.itemKey]
        )
      );

      return !requiresLineAccessory;
    });

    if (startingAccessory) {
      // Build the chain starting from the base accessory
      let current = startingAccessory;
      const visited = new Set();

      while (current && !visited.has(current.itemKey)) {
        visited.add(current.itemKey);
        progression.push(current.itemKey);

        // Find the next accessory that requires this one
        const next = accessories.find((acc) => {
          if (visited.has(acc.itemKey)) return false;

          return (
            acc.recipes &&
            acc.recipes.some((recipe) =>
              recipe.ingredients.some(
                (ingredient) => ingredient.itemKey === current.itemKey
              )
            )
          );
        });

        current = next;
      }
    }

    progressionMap[line] = progression;
  });

  return progressionMap;
}

// Accessory bag size mapping based on redstone collection levels
const ACCESSORY_BAG_TIERS = {
  0: { slots: 0, name: "No Accessory Bag" },
  2: { slots: 3, name: "Small Accessory Bag" },
  6: { slots: 9, name: "Medium Accessory Bag" },
  9: { slots: 15, name: "Large Accessory Bag" },
  10: { slots: 21, name: "Greater Accessory Bag" },
  11: { slots: 27, name: "Giant Accessory Bag" },
  12: { slots: 33, name: "Massive Accessory Bag" },
  13: { slots: 39, name: "Humongous Accessory Bag" },
  14: { slots: 45, name: "Colossal Accessory Bag" },
  15: { slots: 51, name: "Titanic Accessory Bag" },
  16: { slots: 57, name: "Preposterous Accessory Bag" },
};

/**
 * Gets the accessory bag size based on redstone collection level
 * @param {object} character - Player character data
 * @returns {object} - Object with slots and bag name
 */
function getAccessoryBagSize(character) {
  const redstoneLevel = getPlayerItemCollectionLevel(character, "REDSTONE");

  // Find the highest tier the player qualifies for
  let currentTier = ACCESSORY_BAG_TIERS[0]; // Default to 0 slots

  for (const [tierLevel, tierData] of Object.entries(ACCESSORY_BAG_TIERS)) {
    if (redstoneLevel >= parseInt(tierLevel)) {
      currentTier = tierData;
    }
  }

  return currentTier;
}

/**
 * Gets the accessory bag tier name for a specific redstone level
 * Used for collection tier rewards
 * @param {number} redstoneLevel - The redstone collection level
 * @returns {string|null} - The bag name with slots, or null if no upgrade
 */
function getAccessoryBagTierReward(redstoneLevel) {
  const tierData = ACCESSORY_BAG_TIERS[redstoneLevel];
  if (tierData && redstoneLevel > 0) {
    return `${tierData.name} (${tierData.slots} Slots)`;
  }
  return null;
}

// Global flag to prevent concurrent showMissingAccessories calls
const activeMissingAccessoriesCalls = new Set();

/**
 * Show missing accessories for a user
 * @param {Object} interaction - Discord interaction object
 * @param {string} userId - Discord user ID
 * @param {boolean} showBackButton - Whether to show a back button (default: true for accessories command)
 */
async function showMissingAccessories(
  interaction,
  userId,
  showBackButton = true,
  page = 0
) {
  const callKey = `${userId}_${interaction.id}`;

  // Prevent concurrent calls for the same user/interaction
  if (activeMissingAccessoriesCalls.has(callKey)) {
    return;
  }

  activeMissingAccessoriesCalls.add(callKey);

  try {
    const character = await getPlayerData(userId);
    if (!character) {
      return interaction.editReply({
        content: "Character data not found!",
        embeds: [],
        components: [],
      });
    }

    const playerAccessories = await getPlayerAccessories(userId);
    let allItems = configManager.getAllItems();

    // Check if allItems is undefined and try to reload if needed
    if (!allItems || Object.keys(allItems).length === 0) {
      try {
        await configManager.reloadItems();
        allItems = configManager.getAllItems();
        if (!allItems || Object.keys(allItems).length === 0) {
          allItems = {};
        }
      } catch {
        allItems = {};
      }
    }

    // Build progression map for accessory lines
    const progressionMap = buildAccessoryProgressionMap(allItems);

    // Get all equipped accessories
    const equippedAccessories = playerAccessories.filter(
      (acc) => acc.isEquipped
    );

    // Get all available accessories from items.json
    const allAccessories = Object.entries(allItems)
      .filter(([, item]) => item.type === "ACCESSORY")
      .map(([key, item]) => ({ itemKey: key, ...item }));

    // Create sets for faster lookup
    const equippedItemKeys = new Set(
      equippedAccessories.map((acc) => acc.itemKey)
    );

    // Track highest tier equipped for each accessory line
    const equippedLineTiers = {};
    equippedAccessories.forEach((acc) => {
      const itemData = allItems[acc.itemKey];
      if (itemData && itemData.accessoryLine) {
        const line = itemData.accessoryLine;
        const progression = progressionMap[line] || [];
        const tierIndex = progression.indexOf(acc.itemKey);

        if (tierIndex !== -1) {
          equippedLineTiers[line] = Math.max(
            equippedLineTiers[line] || -1,
            tierIndex
          );
        }
      }
    });

    // Find missing accessories
    const missingAccessories = allAccessories.filter((accessory) => {
      // Skip if already equipped
      if (equippedItemKeys.has(accessory.itemKey)) {
        return false;
      }

      // If accessory has a line, check progression
      if (accessory.accessoryLine) {
        const line = accessory.accessoryLine;
        const progression = progressionMap[line] || [];
        const accessoryTierIndex = progression.indexOf(accessory.itemKey);

        // If accessory is not in progression chain, include it
        if (accessoryTierIndex === -1) {
          return true;
        }

        const equippedTierIndex = equippedLineTiers[line];

        // If no accessory from this line is equipped, only show the first tier
        if (equippedTierIndex === undefined) {
          return accessoryTierIndex === 0;
        }

        // Only show the next tier after the highest equipped
        return accessoryTierIndex === equippedTierIndex + 1;
      }

      // Include accessories without lines that aren't equipped
      return true;
    });

    // Sort missing accessories by rarity (lowest → highest)
    const sortedMissing = sortAccessoriesByRarity(missingAccessories, true);

    // Get player's disblock level and username for title
    const disblockLevel = formatDisblockLevel(character.disblock_xp || 0);
    const username = character.name || "Unknown";

    // Create missing accessories embed
    const missingEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PINK_RED)
      .setTitle(
        `[${Math.floor(disblockLevel)}] ${username}'s Missing Accessories`
      )
      .setDescription(
        sortedMissing.length === 0
          ? "🎉 You have all available accessories equipped or have better versions from their lines!"
          : `You are missing ${sortedMissing.length} accessories:`
      );

    // Add missing accessories to embed with pagination
    if (sortedMissing.length > 0) {
      const itemsPerPage = 10; // Reduced to ensure we stay under character limits
      const totalPages = Math.ceil(sortedMissing.length / itemsPerPage);
      const startIndex = page * itemsPerPage;
      const endIndex = Math.min(
        startIndex + itemsPerPage,
        sortedMissing.length
      );
      const currentPageItems = sortedMissing.slice(startIndex, endIndex);

      const accessoryList = currentPageItems
        .map((acc) => {
          const emoji = acc.emoji || "❓";
          const rarity =
            (typeof acc.rarity === "string"
              ? acc.rarity
              : acc.rarity?.name
            )?.toUpperCase() || "COMMON";
          const lineInfo = acc.accessoryLine
            ? ` (${acc.accessoryLine} Line)`
            : "";
          return `${emoji} **${rarity}** ${acc.name}${lineInfo}`;
        })
        .join("\n");

      const fieldName =
        totalPages > 1
          ? `Missing Accessories (Page ${page + 1}/${totalPages})`
          : "Missing Accessories";
      missingEmbed.addFields({
        name: fieldName,
        value: accessoryList,
        inline: false,
      });

      // Update description to show pagination info
      missingEmbed.setDescription(
        `You are missing ${sortedMissing.length} accessories (showing ${startIndex + 1}-${endIndex})`
      );
    }

    // Create components
    const components = [];

    // Add pagination and select menu if there are missing accessories
    if (sortedMissing.length > 0) {
      const itemsPerPage = 10;
      const totalPages = Math.ceil(sortedMissing.length / itemsPerPage);
      const startIndex = page * itemsPerPage;
      const endIndex = Math.min(
        startIndex + itemsPerPage,
        sortedMissing.length
      );
      const currentPageItems = sortedMissing.slice(startIndex, endIndex);

      // Add select menu for current page items
      const selectOptions = currentPageItems.map((acc) => {
        const emoji = acc.emoji || "❓";
        const rarity =
          (typeof acc.rarity === "string"
            ? acc.rarity
            : acc.rarity?.name
          )?.toUpperCase() || "COMMON";
        const lineInfo = acc.accessoryLine
          ? ` (${acc.accessoryLine} Line)`
          : "";

        return new StringSelectMenuOptionBuilder()
          .setLabel(`${acc.name}${lineInfo}`)
          .setDescription(`${rarity} Accessory`)
          .setValue(acc.itemKey)
          .setEmoji(emoji);
      });

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId("missing_accessory_wiki")
        .setPlaceholder("Select an accessory to view its wiki page")
        .addOptions(selectOptions);

      components.push(new ActionRowBuilder().addComponents(selectMenu));

      // Add pagination buttons if there are multiple pages
      if (totalPages > 1) {
        const timestamp = Date.now();
        const prevButton = new ButtonBuilder()
          .setCustomId(`missing_prev_${page}_${userId}_${timestamp}`)
          .setLabel("◀ Prev")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === 0);

        const nextButton = new ButtonBuilder()
          .setCustomId(`missing_next_${page}_${userId}_${timestamp}`)
          .setLabel("Next ▶")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === totalPages - 1);

        components.push(
          new ActionRowBuilder().addComponents(prevButton, nextButton)
        );
      }
    }

    if (showBackButton) {
      const backButton = new ButtonBuilder()
        .setCustomId("back_to_accessories")
        .setLabel("← Back to Accessories")
        .setStyle(ButtonStyle.Secondary);

      components.push(new ActionRowBuilder().addComponents(backButton));
    }

    const response = await interaction.editReply({
      embeds: [missingEmbed],
      components: components,
    });

    const collector = response.createMessageComponentCollector({
      componentType: ComponentType.StringSelect,
      time: 300000, // 5 minutes
    });

    const buttonCollector = response.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: 300000, // 5 minutes
    });

    collector.on("collect", async (selectInteraction) => {
      if (selectInteraction.user.id !== userId) {
        return selectInteraction.reply({
          content: "You can only view your own accessories!",
          ephemeral: true,
        });
      }

      if (selectInteraction.customId === "missing_accessory_wiki") {
        const selectedItemKey = selectInteraction.values[0];

        try {
          // Safely acknowledge the interaction – avoid double-defer or expired interactions
          if (!selectInteraction.deferred && !selectInteraction.replied) {
            try {
              await selectInteraction.deferUpdate();
            } catch {
              // Interaction might have expired or been acknowledged already – simply exit
              return;
            }
          }
        } catch {
          return;
        }

        const mockInteraction = {
          ...selectInteraction,
          options: {
            getString: (name) => {
              if (name === "item_key") {
                return selectedItemKey;
              }
              return null;
            },
          },
          deferReply: async () => {
            return Promise.resolve();
          },
          editReply: async (options) => {
            return await selectInteraction.editReply(options);
          },

          replied: false,
          deferred: true,

          fromAccessoriesCommand: true,
          fromMissingAccessories: true,
          originalUserId: userId,
        };

        mockInteraction.replied = false;
        mockInteraction.deferred = true;

        collector.stop("navigating_to_wiki");
        buttonCollector.stop("navigating_to_wiki");

        await new Promise((resolve) => {
          setTimeout(resolve, 100);
        });

        try {
          await wikiCommand.execute(mockInteraction);
        } catch (error) {
          console.error("Error executing wiki command:", error);
        }
      }
    });

    buttonCollector.on("collect", async (buttonInteraction) => {
      if (buttonInteraction.user.id !== userId) {
        return buttonInteraction.reply({
          content: "You can only view your own accessories!",
          ephemeral: true,
        });
      }

      if (!buttonInteraction.deferred && !buttonInteraction.replied) {
        try {
          await buttonInteraction.deferUpdate();
        } catch {
          return;
        }
      }

      if (buttonInteraction.customId === "back_to_accessories") {
        collector.stop("navigating_back_to_accessories");
        buttonCollector.stop("navigating_back_to_accessories");

        await refreshAccessoriesDisplay(buttonInteraction, userId);
      } else if (
        buttonInteraction.customId.startsWith("missing_prev_") ||
        buttonInteraction.customId.startsWith("missing_next_")
      ) {
        const parts = buttonInteraction.customId.split("_");
        const action = parts[1];
        const currentPage = parseInt(parts[2]);
        const targetUserId = parts[3];

        if (targetUserId !== userId) {
          return buttonInteraction.reply({
            content: "You can only view your own accessories!",
            ephemeral: true,
          });
        }

        let newPage = currentPage;
        if (action === "prev" && currentPage > 0) {
          newPage = currentPage - 1;
        } else if (action === "next") {
          newPage = currentPage + 1;
        }

        if (newPage !== currentPage) {
          collector.stop("pagination");
          buttonCollector.stop("pagination");

          await new Promise((resolve) => {
            setTimeout(resolve, 100);
          });
          await showMissingAccessories(
            buttonInteraction,
            userId,
            showBackButton,
            newPage
          );
        }
      }
    });

    collector.on("end", (_, reason) => {
      if (
        reason === "navigating_back_to_accessories" ||
        reason === "navigating_to_wiki"
      ) {
        return;
      }

      if (reason === "time") {
        const disabledComponents = components.map((row) => {
          const newRow = ActionRowBuilder.from(row);
          newRow.components.forEach((component) => {
            component.setDisabled(true);
          });
          return newRow;
        });

        interaction
          .editReply({ components: disabledComponents })
          .catch(() => {});
      }
    });
  } finally {
    activeMissingAccessoriesCalls.delete(callKey);
  }
}

/**
 * Handles pagination for accessory menus
 * @param {Object} interaction - Discord interaction object
 * @param {string} userId - User ID
 */
async function handleAccessoryPagination(interaction, userId) {
  const currentMessage = interaction.message;
  let equipPage = 0;
  let unequipPage = 0;
  const components = currentMessage.components;
  for (const row of components) {
    for (const component of row.components) {
      if (component.customId === "equip_accessories" && component.placeholder) {
        const match = component.placeholder.match(/Page (\d+)\/(\d+)/);
        if (match) {
          equipPage = parseInt(match[1]) - 1; // Convert to 0-based index
        }
      } else if (
        component.customId === "unequip_accessories" &&
        component.placeholder
      ) {
        const match = component.placeholder.match(/Page (\d+)\/(\d+)/);
        if (match) {
          unequipPage = parseInt(match[1]) - 1; // Convert to 0-based index
        }
      }
    }
  }

  if (interaction.customId === "equip_prev_page") {
    equipPage = Math.max(0, equipPage - 1);
  } else if (interaction.customId === "equip_next_page") {
    equipPage = equipPage + 1;
  } else if (interaction.customId === "unequip_prev_page") {
    unequipPage = Math.max(0, unequipPage - 1);
  } else if (interaction.customId === "unequip_next_page") {
    unequipPage = unequipPage + 1;
  }

  await refreshAccessoriesDisplay(interaction, userId, equipPage, unequipPage);
}

/**
 * Refresh the accessories display with updated data
 * @param {Object} interaction - Discord interaction object
 * @param {string} userId - Discord user ID
 * @param {number} equipPage - Current page for equip menu (default: 0)
 * @param {number} unequipPage - Current page for unequip menu (default: 0)
 */
async function refreshAccessoriesDisplay(
  interaction,
  userId,
  equipPage = 0,
  unequipPage = 0
) {
  const character = await getPlayerData(userId);
  if (!character) {
    return interaction.editReply({
      content: "Character data not found!",
      embeds: [],
      components: [],
    });
  }

  const bagInfo = getAccessoryBagSize(character);

  const playerAccessories = await getPlayerAccessories(userId);

  let allItems = configManager.getAllItems();

  if (!allItems || Object.keys(allItems).length === 0) {
    try {
      await configManager.reloadItems();
      allItems = configManager.getAllItems();
      if (!allItems || Object.keys(allItems).length === 0) {
        allItems = {};
      }
    } catch {
      allItems = {};
    }
  }

  const equippedAccessories = playerAccessories
    .filter((acc) => acc.isEquipped)
    .map((acc) => ({
      ...acc,
      itemData: allItems[acc.itemKey],
    }))
    .filter((acc) => acc.itemData);

  const unequippedAccessories = playerAccessories
    .filter((acc) => !acc.isEquipped)
    .map((acc) => ({
      ...acc,
      itemData: allItems[acc.itemKey],
    }))
    .filter((acc) => acc.itemData);

  const disblockLevel = formatDisblockLevel(character.disblock_xp || 0);
  const username = character.name || "Unknown";
  const equippedEmbed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GREEN)
    .setTitle(`[${Math.floor(disblockLevel)}] ${username}'s Accessories`)
    .setFooter({
      text:
        bagInfo.slots > 0
          ? `${bagInfo.name} (${bagInfo.slots} Slots)`
          : "No Accessory Bag",
    });

  const unequippedEmbed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ORANGE)
    .setTitle("Accessories Storage")
    .setDescription(
      unequippedAccessories.length === 0 ? "No accessories in storage." : null
    );

  if (bagInfo.slots === 0) {
    equippedEmbed.setDescription(
      "You need to reach **Redstone Collection 2** to unlock your first accessory bag!"
    );
  } else {
    equippedEmbed.setDescription(null);

    if (equippedAccessories.length > 0) {
      const sortedEquipped = sortAccessoriesByRarity(
        [...equippedAccessories],
        allItems
      );
      addAccessoriesInTwoColumns(sortedEquipped, equippedEmbed, "Equipped", 16);
    }
  }

  if (unequippedAccessories.length > 0) {
    const sortedUnequipped = sortAccessoriesByRarity(
      [...unequippedAccessories],
      allItems
    );
    unequippedEmbed.setDescription(null);
    addAccessoriesInTwoColumns(
      sortedUnequipped,
      unequippedEmbed,
      "Storage",
      16
    );
  }

  const components = [];
  const itemsPerPage = 25;
  if (unequippedAccessories.length > 0 && bagInfo.slots > 0) {
    const availableSlots = bagInfo.slots - equippedAccessories.length;
    if (availableSlots > 0) {
      const equippedItemKeys = new Set(
        equippedAccessories.map((acc) => acc.itemKey)
      );
      const availableAccessories = unequippedAccessories.filter(
        (acc) => !equippedItemKeys.has(acc.itemKey)
      );

      if (availableAccessories.length > 0) {
        const startIndex = equipPage * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedAccessories = availableAccessories.slice(
          startIndex,
          endIndex
        );
        const totalPages = Math.ceil(
          availableAccessories.length / itemsPerPage
        );

        const equipOptions = paginatedAccessories.map((acc) => {
          const emoji = acc.itemData?.emoji || "❓";
          const rarity =
            (typeof acc.itemData?.rarity === "string"
              ? acc.itemData.rarity
              : acc.itemData?.rarity?.name
            )?.toUpperCase() || "COMMON";
          return {
            label: `${acc.itemData?.name || acc.itemKey}`,
            description: `${rarity} - Click to equip`,
            value: acc.id,
            emoji: emoji,
          };
        });

        const equipSelectMenu = new StringSelectMenuBuilder()
          .setCustomId("equip_accessories")
          .setPlaceholder(
            totalPages > 1
              ? `Select accessories to equip... (Page ${equipPage + 1}/${totalPages})`
              : "Select accessories to equip..."
          )
          .setMinValues(1)
          .setMaxValues(
            Math.max(1, Math.min(equipOptions.length, availableSlots))
          )
          .addOptions(equipOptions);

        components.push(new ActionRowBuilder().addComponents(equipSelectMenu));

        if (totalPages > 1) {
          const equipPrevButton = new ButtonBuilder()
            .setCustomId("equip_prev_page")
            .setLabel("Prev")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(equipPage === 0);

          const equipNextButton = new ButtonBuilder()
            .setCustomId("equip_next_page")
            .setLabel("Next")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(equipPage >= totalPages - 1);

          components.push(
            new ActionRowBuilder().addComponents(
              equipPrevButton,
              equipNextButton
            )
          );
        }
      }
    }
  }

  if (equippedAccessories.length > 0) {
    const startIndex = unequipPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedEquipped = equippedAccessories.slice(startIndex, endIndex);
    const totalPages = Math.ceil(equippedAccessories.length / itemsPerPage);

    const unequipOptions = paginatedEquipped.map((acc) => {
      const emoji = acc.itemData?.emoji || "❓";
      let displayName = acc.itemData?.name || acc.itemKey;

      if (acc.data_json?.reforge?.name) {
        displayName = `${acc.data_json.reforge.name} ${displayName}`;
      }

      return {
        label: displayName,
        description: "Click to unequip",
        value: acc.id,
        emoji: emoji,
      };
    });

    const unequipSelectMenu = new StringSelectMenuBuilder()
      .setCustomId("unequip_accessories")
      .setPlaceholder(
        totalPages > 1
          ? `Select accessories to unequip... (Page ${unequipPage + 1}/${totalPages})`
          : "Select accessories to unequip..."
      )
      .setMinValues(1)
      .setMaxValues(unequipOptions.length)
      .addOptions(unequipOptions);

    components.push(new ActionRowBuilder().addComponents(unequipSelectMenu));

    if (totalPages > 1) {
      const unequipPrevButton = new ButtonBuilder()
        .setCustomId("unequip_prev_page")
        .setLabel("Prev")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(unequipPage === 0);

      const unequipNextButton = new ButtonBuilder()
        .setCustomId("unequip_next_page")
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(unequipPage >= totalPages - 1);

      components.push(
        new ActionRowBuilder().addComponents(
          unequipPrevButton,
          unequipNextButton
        )
      );
    }
  }

  const refreshButton = new ButtonBuilder()
    .setCustomId("refresh_accessories")
    .setLabel("Refresh")
    .setStyle(ButtonStyle.Secondary);

  const seeMissingButton = new ButtonBuilder()
    .setCustomId("see_missing_accessories")
    .setLabel("See Missing")
    .setStyle(ButtonStyle.Primary);

  components.push(
    new ActionRowBuilder().addComponents(refreshButton, seeMissingButton)
  );

  const embedsToSend = [equippedEmbed];
  const hasStorageContent =
    unequippedEmbed.data.description ||
    (unequippedEmbed.data.fields && unequippedEmbed.data.fields.length > 0);

  if (hasStorageContent) {
    embedsToSend.push(unequippedEmbed);
  }

  const response = await interaction.editReply({
    embeds: embedsToSend,
    components: components,
  });

  const collector = response.createMessageComponentCollector({
    componentType: ComponentType.StringSelect,
    time: 300000, // 5 minutes
  });

  collector.on("collect", async (selectInteraction) => {
    if (selectInteraction.user.id !== userId) {
      return selectInteraction.reply({
        content: "You can only manage your own accessories!",
        ephemeral: true,
      });
    }

    // Safely acknowledge the interaction – avoid double-defer or expired interactions
    if (!selectInteraction.deferred && !selectInteraction.replied) {
      try {
        await selectInteraction.deferUpdate();
      } catch {
        // Interaction might have expired or been acknowledged already – simply exit
        return;
      }
    }
    const selectedValues = selectInteraction.values;

    if (selectInteraction.customId === "equip_accessories") {
      for (const accessoryId of selectedValues) {
        await equipAccessoryAtomically(userId, accessoryId, selectInteraction);
      }
      await refreshAccessoriesDisplay(selectInteraction, userId);
    } else if (selectInteraction.customId === "unequip_accessories") {
      for (const accessoryId of selectedValues) {
        await unequipAccessoryAtomically(
          userId,
          accessoryId,
          selectInteraction
        );
      }
      await refreshAccessoriesDisplay(selectInteraction, userId);
    }
  });

  const buttonCollector = response.createMessageComponentCollector({
    componentType: ComponentType.Button,
    time: 300000, // 5 minutes
  });

  buttonCollector.on("collect", async (buttonInteraction) => {
    if (buttonInteraction.user.id !== userId) {
      return buttonInteraction.reply({
        content: "You can only manage your own accessories!",
        ephemeral: true,
      });
    }

    if (!buttonInteraction.deferred && !buttonInteraction.replied) {
      try {
        await buttonInteraction.deferUpdate();
      } catch {
        return;
      }
    }

    if (buttonInteraction.customId === "refresh_accessories") {
      await refreshAccessoriesDisplay(buttonInteraction, userId);
    } else if (buttonInteraction.customId === "see_missing_accessories") {
      collector.stop("navigating_to_missing");
      buttonCollector.stop("navigating_to_missing");
      try {
        await showMissingAccessories(buttonInteraction, userId, true);
      } catch {
        await buttonInteraction.editReply({
          content:
            "An error occurred while loading missing accessories. Please try again.",
          embeds: [],
          components: [],
        });
      }
    }
  });

  collector.on("end", (collected, reason) => {
    if (reason === "navigating_to_missing" || reason === "navigating_to_wiki") {
      return;
    }

    if (reason === "time") {
      const disabledComponents = components.map((row) => {
        const newRow = ActionRowBuilder.from(row);
        newRow.components.forEach((component) => {
          component.setDisabled(true);
        });
        return newRow;
      });

      interaction.editReply({ components: disabledComponents }).catch(() => {});
    }
  });

  buttonCollector.on("end", () => {});
}

/**
 * Formats accessories into a two-column layout using embed fields
 * @param {Array} accessories - Array of accessory objects with itemData
 * @param {EmbedBuilder} embed - The embed to add fields to
 * @param {string} sectionTitle - Title for this section (e.g., "Equipped", "Storage")
 * @param {number} maxPerSection - Maximum accessories per section before splitting
 */
function addAccessoriesInTwoColumns(
  accessories,
  embed,
  sectionTitle,
  maxPerSection = 20
) {
  if (accessories.length === 0) {
    return;
  }

  const chunks = [];
  for (let i = 0; i < accessories.length; i += maxPerSection) {
    chunks.push(accessories.slice(i, i + maxPerSection));
  }

  chunks.forEach((chunk, chunkIndex) => {
    const chunkTitle =
      chunks.length > 1
        ? `${sectionTitle} (${chunkIndex + 1}/${chunks.length})`
        : sectionTitle;

    const midpoint = Math.ceil(chunk.length / 2);
    const leftColumn = chunk.slice(0, midpoint);
    const rightColumn = chunk.slice(midpoint);

    const getRarityAbbreviation = (rarity) => {
      const rarityMap = {
        COMMON: "C",
        UNCOMMON: "U",
        RARE: "R",
        EPIC: "E",
        LEGENDARY: "L",
        MYTHIC: "M",
      };
      return rarityMap[rarity] || "C";
    };

    const leftColumnText = leftColumn
      .map((acc) => {
        const emoji = acc.itemData?.emoji || "❓";
        const rarity =
          (typeof acc.itemData?.rarity === "string"
            ? acc.itemData.rarity
            : acc.itemData?.rarity?.name
          )?.toUpperCase() || "COMMON";
        const rarityAbbrev = getRarityAbbreviation(rarity);
        let displayName = acc.itemData?.name || acc.itemKey;

        if (acc.data_json?.reforge?.name) {
          displayName = `${acc.data_json.reforge.name} ${displayName}`;
        }

        const amountText = acc.amount > 1 ? ` x${acc.amount}` : "";
        const uuidPrefix = acc.id ? acc.id.slice(0, 4) : "????";
        return `${emoji} **[${rarityAbbrev}]** ${displayName}${amountText} \`${uuidPrefix}\``;
      })
      .join("\n");

    const rightColumnText =
      rightColumn.length > 0
        ? rightColumn
            .map((acc) => {
              const emoji = acc.itemData?.emoji || "❓";
              const rarity =
                (typeof acc.itemData?.rarity === "string"
                  ? acc.itemData.rarity
                  : acc.itemData?.rarity?.name
                )?.toUpperCase() || "COMMON";
              const rarityAbbrev = getRarityAbbreviation(rarity);
              let displayName = acc.itemData?.name || acc.itemKey;

              if (acc.data_json?.reforge?.name) {
                displayName = `${acc.data_json.reforge.name} ${displayName}`;
              }

              const amountText = acc.amount > 1 ? ` x${acc.amount}` : "";
              const uuidPrefix = acc.id ? acc.id.slice(0, 4) : "????";
              return `${emoji} **[${rarityAbbrev}]** ${displayName}${amountText} \`${uuidPrefix}\``;
            })
            .join("\n")
        : "\u200b";

    embed.addFields(
      {
        name: chunkTitle,
        value: leftColumnText || "\u200b",
        inline: true,
      },
      {
        name: "\u200b",
        value: rightColumnText,
        inline: true,
      },
      {
        name: "\u200b",
        value: "\u200b",
        inline: true,
      }
    );
  });
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("accessories")
    .setDescription("View your equipped and unequipped accessories"),

  async execute(interaction) {
    await interaction.deferReply();

    const userId = interaction.user.id;
    const character = await getPlayerData(userId);

    if (!character) {
      return interaction.editReply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    const bagInfo = getAccessoryBagSize(character);
    const playerAccessories = await getPlayerAccessories(userId);
    const allItems = configManager.getAllItems();

    const equippedAccessories = playerAccessories
      .filter((acc) => acc.isEquipped)
      .map((acc) => ({
        ...acc,
        itemData: allItems[acc.itemKey],
      }))
      .filter((acc) => acc.itemData);

    const unequippedAccessories = playerAccessories
      .filter((acc) => !acc.isEquipped)
      .map((acc) => ({
        ...acc,
        itemData: allItems[acc.itemKey],
      }))
      .filter((acc) => acc.itemData);

    const disblockLevel = formatDisblockLevel(character.disblock_xp || 0);
    const username = character.name || "Unknown";
    const equippedEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(`[${Math.floor(disblockLevel)}] ${username}'s Accessories`)
      .setFooter({
        text:
          bagInfo.slots > 0
            ? `${bagInfo.name} (${bagInfo.slots} Slots)`
            : "No Accessory Bag",
      });

    if (bagInfo.slots === 0) {
      equippedEmbed.setDescription(
        "You need to reach **Redstone Collection 2** to unlock your first accessory bag!"
      );
    } else if (equippedAccessories.length > 0) {
      const sortedEquipped = sortAccessoriesByRarity(
        [...equippedAccessories],
        allItems
      );
      addAccessoriesInTwoColumns(sortedEquipped, equippedEmbed, "Equipped", 16);
    }

    const unequippedEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ORANGE)
      .setTitle("Accessories Storage")
      .setDescription(
        unequippedAccessories.length === 0 ? "No accessories in storage." : null
      );

    if (unequippedAccessories.length > 0) {
      const sortedUnequipped = sortAccessoriesByRarity(
        [...unequippedAccessories],
        allItems
      );
      unequippedEmbed.setDescription(null);
      addAccessoriesInTwoColumns(
        sortedUnequipped,
        unequippedEmbed,
        "Storage",
        16
      );
    }

    const equipPage = 0;
    const unequipPage = 0;
    const itemsPerPage = 25;
    const components = [];

    if (unequippedAccessories.length > 0 && bagInfo.slots > 0) {
      const equippedItemKeys = new Set(
        equippedAccessories.map((acc) => acc.itemKey)
      );
      const availableAccessories = unequippedAccessories.filter(
        (acc) => !equippedItemKeys.has(acc.itemKey)
      );

      if (availableAccessories.length > 0) {
        const startIndex = equipPage * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedAccessories = availableAccessories.slice(
          startIndex,
          endIndex
        );
        const totalPages = Math.ceil(
          availableAccessories.length / itemsPerPage
        );
        const availableSlots = bagInfo.slots - equippedAccessories.length;

        const equipOptions = paginatedAccessories.map((acc) => {
          const emoji = acc.itemData?.emoji || "❓";
          const rarity =
            (typeof acc.itemData?.rarity === "string"
              ? acc.itemData.rarity
              : acc.itemData?.rarity?.name
            )?.toUpperCase() || "COMMON";
          return {
            label: `${acc.itemData?.name || acc.itemKey}`,
            description: `${rarity} - Click to equip`,
            value: acc.id,
            emoji: emoji,
          };
        });

        const equipSelectMenu = new StringSelectMenuBuilder()
          .setCustomId("equip_accessories")
          .setPlaceholder(
            totalPages > 1
              ? `Select accessories to equip... (Page ${equipPage + 1}/${totalPages})`
              : "Select accessories to equip..."
          )
          .setMinValues(1)
          .setMaxValues(
            Math.max(1, Math.min(equipOptions.length, availableSlots))
          )
          .addOptions(equipOptions);

        components.push(new ActionRowBuilder().addComponents(equipSelectMenu));

        if (totalPages > 1) {
          const equipPrevButton = new ButtonBuilder()
            .setCustomId("equip_prev_page")
            .setLabel("Prev")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(equipPage === 0);

          const equipNextButton = new ButtonBuilder()
            .setCustomId("equip_next_page")
            .setLabel("Next")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(equipPage >= totalPages - 1);

          components.push(
            new ActionRowBuilder().addComponents(
              equipPrevButton,
              equipNextButton
            )
          );
        }
      }
    }

    if (equippedAccessories.length > 0) {
      const startIndex = unequipPage * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const paginatedEquipped = equippedAccessories.slice(startIndex, endIndex);
      const totalPages = Math.ceil(equippedAccessories.length / itemsPerPage);

      const unequipOptions = paginatedEquipped.map((acc) => {
        const emoji = acc.itemData?.emoji || "❓";
        let displayName = acc.itemData?.name || acc.itemKey;

        if (acc.data_json?.reforge?.name) {
          displayName = `${acc.data_json.reforge.name} ${displayName}`;
        }

        return {
          label: displayName,
          description: "Click to unequip",
          value: acc.id,
          emoji: emoji,
        };
      });

      const unequipSelectMenu = new StringSelectMenuBuilder()
        .setCustomId("unequip_accessories")
        .setPlaceholder(
          totalPages > 1
            ? `Select accessories to unequip... (Page ${unequipPage + 1}/${totalPages})`
            : "Select accessories to unequip..."
        )
        .setMinValues(1)
        .setMaxValues(unequipOptions.length)
        .addOptions(unequipOptions);

      components.push(new ActionRowBuilder().addComponents(unequipSelectMenu));

      if (totalPages > 1) {
        const unequipPrevButton = new ButtonBuilder()
          .setCustomId("unequip_prev_page")
          .setLabel("Prev")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(unequipPage === 0);

        const unequipNextButton = new ButtonBuilder()
          .setCustomId("unequip_next_page")
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(unequipPage >= totalPages - 1);

        components.push(
          new ActionRowBuilder().addComponents(
            unequipPrevButton,
            unequipNextButton
          )
        );
      }
    }

    const refreshButton = new ButtonBuilder()
      .setCustomId("refresh_accessories")
      .setLabel("Refresh")
      .setStyle(ButtonStyle.Secondary);

    const seeMissingButton = new ButtonBuilder()
      .setCustomId("see_missing_accessories")
      .setLabel("See Missing")
      .setStyle(ButtonStyle.Primary);

    components.push(
      new ActionRowBuilder().addComponents(refreshButton, seeMissingButton)
    );

    const embedsToSend = [equippedEmbed];
    const hasStorageContent =
      unequippedEmbed.data.description ||
      (unequippedEmbed.data.fields && unequippedEmbed.data.fields.length > 0);

    if (hasStorageContent) {
      embedsToSend.push(unequippedEmbed);
    }

    const response = await interaction.editReply({
      embeds: embedsToSend,
      components: components,
    });

    // Create collector for select menu interactions
    const collector = response.createMessageComponentCollector({
      componentType: ComponentType.StringSelect,
      time: 300000, // 5 minutes
    });

    collector.on("collect", async (selectInteraction) => {
      if (selectInteraction.user.id !== userId) {
        return selectInteraction.reply({
          content: "You can only manage your own accessories!",
          ephemeral: true,
        });
      }

      // Safely acknowledge the interaction – avoid double-defer or expired interactions
      if (!selectInteraction.deferred && !selectInteraction.replied) {
        try {
          await selectInteraction.deferUpdate();
        } catch {
          // Interaction might have expired or been acknowledged already – simply exit
          return;
        }
      }
      const selectedValues = selectInteraction.values;

      if (selectInteraction.customId === "equip_accessories") {
        for (const accessoryId of selectedValues) {
          await equipAccessoryAtomically(
            userId,
            accessoryId,
            selectInteraction
          );
        }
        await refreshAccessoriesDisplay(selectInteraction, userId);
      } else if (selectInteraction.customId === "unequip_accessories") {
        for (const accessoryId of selectedValues) {
          await unequipAccessoryAtomically(
            userId,
            accessoryId,
            selectInteraction
          );
        }
        await refreshAccessoriesDisplay(selectInteraction, userId);
      } else if (selectInteraction.customId === "missing_accessory_wiki") {
        const selectedItemKey = selectedValues[0];

        const mockInteraction = {
          ...selectInteraction,
          options: {
            getString: (name) => {
              if (name === "item_key") return selectedItemKey;
              return null;
            },
          },
          deferReply: async () => {
            // Already deferred above, do nothing
          },
          editReply: async (options) => {
            return await selectInteraction.editReply(options);
          },
          replied: false,
          deferred: true,
          fromMissingAccessories: true,
          fromAccessoriesCommand: true,
          originalUserId: userId,
        };

        mockInteraction.replied = false;
        mockInteraction.deferred = true;

        collector.stop("navigating_to_wiki");
        buttonCollector.stop("navigating_to_wiki");
        try {
          await wikiCommand.execute(mockInteraction);
        } catch {
          // Silent error handling
        }
      }
    });

    const buttonCollector = response.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: 300000, // 5 minutes
    });

    buttonCollector.on("collect", async (buttonInteraction) => {
      if (buttonInteraction.user.id !== userId) {
        return buttonInteraction.reply({
          content: "You can only manage your own accessories!",
          ephemeral: true,
        });
      }

      if (!buttonInteraction.deferred && !buttonInteraction.replied) {
        try {
          await buttonInteraction.deferUpdate();
        } catch {
          return;
        }
      }

      if (buttonInteraction.customId === "refresh_accessories") {
        await refreshAccessoriesDisplay(buttonInteraction, userId);
      } else if (buttonInteraction.customId === "see_missing_accessories") {
        // Stop current collectors before navigating to prevent conflicts
        collector.stop("navigating_to_missing");
        buttonCollector.stop("navigating_to_missing");
        try {
          await showMissingAccessories(buttonInteraction, userId, true);
        } catch {
          await buttonInteraction.editReply({
            content:
              "An error occurred while loading missing accessories. Please try again.",
            embeds: [],
            components: [],
          });
        }
      } else if (buttonInteraction.customId === "back_to_accessories") {
        await refreshAccessoriesDisplay(buttonInteraction, userId);
      } else if (
        buttonInteraction.customId === "equip_prev_page" ||
        buttonInteraction.customId === "equip_next_page" ||
        buttonInteraction.customId === "unequip_prev_page" ||
        buttonInteraction.customId === "unequip_next_page"
      ) {
        await handleAccessoryPagination(buttonInteraction, userId);
      } else if (
        buttonInteraction.customId.startsWith("missing_prev_") ||
        buttonInteraction.customId.startsWith("missing_next_")
      ) {
        return;
      }
    });

    collector.on("end", (_, reason) => {
      if (
        reason === "navigating_to_missing" ||
        reason === "navigating_to_wiki"
      ) {
        return;
      }

      if (reason === "time") {
        const disabledComponents = components.map((row) => {
          const newRow = ActionRowBuilder.from(row);
          newRow.components.forEach((component) => {
            component.setDisabled(true);
          });
          return newRow;
        });

        interaction
          .editReply({ components: disabledComponents })
          .catch(() => {});
      }
    });

    buttonCollector.on("end", () => {});
  },

  getAccessoryBagSize,
  getAccessoryBagTierReward,
  ACCESSORY_BAG_TIERS,
  refreshAccessoriesDisplay,
  showMissingAccessories,
};
