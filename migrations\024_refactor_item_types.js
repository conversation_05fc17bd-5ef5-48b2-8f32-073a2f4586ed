// Migration: 024_refactor_item_types.js
// Purpose: Refactor item type structure in data/items.json to use flat string type.
// Usage: Called by migration runner (exports async function up).

const fs = require("fs");
const path = require("path");

const itemsPath = path.join(__dirname, "../data/items.json");
const backupPath = path.join(__dirname, "../data/items.json.bak");

function isTypeObjectWithSubtypes(type) {
  return (
    type && typeof type === "object" && "name" in type && "subtypes" in type
  );
}

function refactorItems(items) {
  const changed = [];
  const result = {};
  for (const [key, item] of Object.entries(items)) {
    if (isTypeObjectWithSubtypes(item.type)) {
      const newItem = { ...item, type: item.type.name };
      changed.push(key);
      result[key] = newItem;
    } else {
      result[key] = item;
    }
  }
  return { result, changed };
}

module.exports.up = async function up() {
  // Backup if not already present
  if (!fs.existsSync(backupPath)) {
    fs.copyFileSync(itemsPath, backupPath);
    console.log(`Backup created: ${backupPath}`);
  } else {
    console.log(`Backup already exists: ${backupPath}`);
  }

  const raw = fs.readFileSync(itemsPath, "utf8");
  const items = JSON.parse(raw);
  const { result, changed } = refactorItems(items);

  fs.writeFileSync(itemsPath, JSON.stringify(result, null, 2), "utf8");

  if (changed.length === 0) {
    console.log("No items needed to be changed. Migration complete.");
  } else {
    console.log("Refactored the following items:");
    for (const key of changed) {
      console.log(`- ${key}`);
    }
    console.log("\nMigration complete. Updated data/items.json.");
  }
};
