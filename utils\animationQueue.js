// animation queue system to respect discord's 5 edits per 5 seconds per channel rate limit

const { Collection } = require("discord.js");
const channelQueues = new Map(); // channelId -> array of pending edit operations

/**
 * queue a message edit operation to respect rate limits
 * batches edits to stay under discord's 5 edits per 5 seconds limit
 */
async function queueMessageEdit(channel, messageId, editOptions) {
  const channelId = channel.id;

  if (!channelQueues.has(channelId)) {
    channelQueues.set(channelId, []);
  }

  const queue = channelQueues.get(channelId);

  return new Promise((resolve, reject) => {
    queue.push({
      channel, // store the channel object directly
      messageId,
      editOptions,
      resolve,
      reject,
      timestamp: Date.now(),
    });

    // process queue if this is the first item
    if (queue.length === 1) {
      processQueue(channelId);
    }
  });
}

/**
 * process the edit queue for a channel with rate limiting
 */
async function processQueue(channelId) {
  const queue = channelQueues.get(channelId);
  if (!queue || queue.length === 0) {
    return;
  }

  const now = Date.now();
  const RATE_LIMIT_WINDOW = 5000; // 5 seconds
  const MAX_EDITS_PER_WINDOW = 5;

  // get recent edits in the last 5 seconds
  const recentEdits = queue.filter(
    (edit) => now - edit.timestamp < RATE_LIMIT_WINDOW
  );

  // if we're at the limit, wait before processing
  if (recentEdits.length >= MAX_EDITS_PER_WINDOW) {
    const oldestEdit = recentEdits[0];
    const waitTime = RATE_LIMIT_WINDOW - (now - oldestEdit.timestamp) + 100; // small buffer

    setTimeout(() => processQueue(channelId), waitTime);
    return;
  }

  // process the next edit
  const editOperation = queue.shift();
  if (!editOperation) {
    return;
  }

  try {
    // use the channel object directly from the edit operation
    const message = await editOperation.channel.messages.fetch(
      editOperation.messageId
    );
    await message.edit(editOperation.editOptions);
    editOperation.resolve();
  } catch (error) {
    editOperation.reject(error);
  }

  // schedule next edit with a small delay to be safe
  if (queue.length > 0) {
    setTimeout(() => processQueue(channelId), 200); // 200ms between edits
  }
}

/**
 * Legacy note: container-based message edit operation
 * @param {Object} channel - Discord channel object
 * @param {string} messageId - Message ID to edit
 * @param {EmbedBuilder} embed - Embed to send (use with components)
 * @param {Array} buttons - Array of buttons to add as separate action row
 */
// Deprecated: container-based edits removed; use queueMessageEdit with { embeds, components }

/**
 * clear all queued edits for a channel (useful for cleanup)
 */
function clearChannelQueue(channelId) {
  const queue = channelQueues.get(channelId);
  if (queue) {
    // reject all pending operations
    queue.forEach((operation) => {
      operation.reject(new Error("Queue cleared"));
    });
    channelQueues.delete(channelId);
  }
}

module.exports = {
  queueMessageEdit,
  clearChannelQueue,
};
