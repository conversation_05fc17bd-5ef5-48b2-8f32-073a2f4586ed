const fs = require("fs").promises;
const path = require("path");

const itemsDir = path.join(__dirname, "..", "data", "items");
const mobsDir = path.join(__dirname, "..", "data", "mobs");
const QUIET_BOOT = process.env.QUIET_BOOT === "true";

// Internal variables to hold the live data
let liveItems = {};
let liveMobs = {};

// loads and parses all items from the items directory
async function loadAndProcessItems() {
  try {
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log(
        `[ConfigManager] Loading items from directory ${itemsDir}...`
      );
    const files = await fs.readdir(itemsDir);
    const parsedItems = {};
    let itemCount = 0;
    for (const file of files) {
      if (!file.endsWith(".json")) continue;
      const itemKey = path.basename(file, ".json").toUpperCase();
      const filePath = path.join(itemsDir, file);
      const rawData = await fs.readFile(filePath, "utf8");
      const itemData = JSON.parse(rawData);
      itemData.itemKey = itemKey;
      parsedItems[itemKey] = itemData;
      itemCount++;
    }
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log(
        `[ConfigManager] Loaded and parsed ${itemCount} items from directory.`
      );
    return parsedItems;
  } catch (error) {
    console.error(
      "[ConfigManager] CRITICAL ERROR loading or processing items from directory:",
      error
    );
    throw new Error(
      `Failed to load or process items from directory: ${error.message}`
    );
  }
}
async function loadAndProcessMobs() {
  try {
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log(`[ConfigManager] Loading mobs from directory ${mobsDir}...`);
    const files = await fs.readdir(mobsDir);
    const parsedMobs = {};
    let mobCount = 0;
    for (const file of files) {
      if (!file.endsWith(".json")) continue;
      const mobKey = path.basename(file, ".json").toUpperCase();
      const filePath = path.join(mobsDir, file);
      const rawData = await fs.readFile(filePath, "utf8");
      const mobData = JSON.parse(rawData);
      mobData.key = mobKey;
      parsedMobs[mobKey] = mobData;
      mobCount++;
    }
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log(
        `[ConfigManager] Loaded and parsed ${mobCount} mobs from directory.`
      );
    return parsedMobs;
  } catch (error) {
    console.error(
      "[ConfigManager] CRITICAL ERROR loading or processing items from directory:",
      error
    );
    throw new Error(
      `Failed to load or process items from directory: ${error.message}`
    );
  }
}

/**
 * Initializes the config manager by loading items and mobs.
 * Should be called once at bot startup.
 */
async function initialize() {
  if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
    console.log("[ConfigManager] Initializing...");
  try {
    liveItems = await loadAndProcessItems();
    liveMobs = await loadAndProcessMobs();
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log("[ConfigManager] Initialization complete.");
  } catch (err) {
    liveItems = {};
    liveMobs = {};
    console.error("[ConfigManager] Initialization failed:", err);
    throw err;
  }
  return { items: liveItems, mobs: liveMobs };
}

/**
 * Gets the definition for a specific item key from the live config.
 * @param {string} itemKey The key of the item (e.g., 'WHEAT').
 * @returns {object | null} The item definition object or null if not found.
 */
function getItem(itemKey) {
  if (!itemKey) return null;
  return liveItems[itemKey.toUpperCase()] || null;
}

function getMob(mobKey) {
  if (!mobKey) return null;
  return liveMobs[mobKey.toUpperCase()] || null;
}

/**
 * Gets the entire live ITEMS object.
 * @returns {object} The live ITEMS object.
 */
/**
 * Gets the entire live ITEMS object.
 * Returns the in-memory reference directly to avoid deep cloning overhead for read-only usage.
 * @returns {object} The live ITEMS object.
 */
function getAllItems() {
  return liveItems;
}

/**
 * Gets the entire live MOBS object.
 * Returns the in-memory reference directly to avoid deep cloning overhead for read-only usage.
 * @returns {object} The live MOBS object.
 */
function getAllMobs() {
  return liveMobs;
}

// --- ADDED Functions for Updating and Saving ---

/**
 * Updates specific fields of an item definition in the live config.
 * IMPORTANT: This only updates the in-memory config. Call saveItems() to persist.
 * @param {string} itemKey The key of the item to update.
 * @param {object} updateData An object containing the fields to update (e.g., { name: 'New Name', sellPrice: 10 }).
 * @returns {boolean} True if the item was found and updated, false otherwise.
 */
function updateItem(itemKey, updateData) {
  const keyUpper = itemKey.toUpperCase();
  if (!liveItems[keyUpper]) {
    console.error(
      `[ConfigManager] updateItem failed: Item key "${keyUpper}" not found.`
    );
    return false;
  }
  try {
    for (const field in updateData) {
      if (Object.hasOwnProperty.call(updateData, field)) {
        liveItems[keyUpper][field] = updateData[field];
      }
    }
    console.log(
      `[ConfigManager] Updated item "${keyUpper}" in memory with fields: ${Object.keys(updateData).join(", ")}.`
    );
    return true;
  } catch (error) {
    console.error(
      `[ConfigManager] Error applying update to item "${keyUpper}":`,
      error
    );
    return false;
  }
}

function updateMob(mobKey, updateData) {
  const keyUpper = mobKey.toUpperCase();
  if (!liveMobs[keyUpper]) {
    console.error(
      `[ConfigManager] updateMob failed: Mob key "${keyUpper}" not found.`
    );
    return false;
  }
  try {
    for (const field in updateData) {
      if (Object.hasOwnProperty.call(updateData, field)) {
        liveMobs[keyUpper][field] = updateData[field];
      }
    }
    console.log(
      `[ConfigManager] Updated mob "${keyUpper}" in memory with fields: ${Object.keys(updateData).join(", ")}.`
    );
    return true;
  } catch (error) {
    console.error(
      `[ConfigManager] Error applying update to mob "${keyUpper}":`,
      error
    );
    return false;
  }
}

/**
 * Saves the current state of the liveItems object back to items.json.
 * @returns {Promise<boolean>} True if saving was successful, false otherwise.
 */
async function saveItems() {
  console.log(
    `[ConfigManager] Attempting to save live items to directory ${itemsDir}...`
  );
  try {
    await fs.mkdir(itemsDir, { recursive: true });
    for (const [key, item] of Object.entries(liveItems)) {
      const cleanItem = { ...item };
      delete cleanItem.itemKey;
      const dataToWrite = JSON.stringify(cleanItem, null, 2);
      const filePath = path.join(itemsDir, `${key}.json`);
      await fs.writeFile(filePath, dataToWrite, "utf8");
    }
    console.log(
      "[ConfigManager] Successfully saved all items to individual files."
    );
    return true;
  } catch (error) {
    console.error(
      "[ConfigManager] CRITICAL ERROR saving items to directory:",
      error
    );
    return false;
  }
}

async function saveMobs() {
  console.log(
    `[ConfigManager] Attempting to save live mobs to directory ${mobsDir}...`
  );
  try {
    await fs.mkdir(mobsDir, { recursive: true });
    for (const [key, mob] of Object.entries(liveMobs)) {
      const cleanMob = { ...mob };
      delete cleanMob.key;
      const dataToWrite = JSON.stringify(cleanMob, null, 2);
      const filePath = path.join(mobsDir, `${key}.json`);
      await fs.writeFile(filePath, dataToWrite, "utf8");
    }
    console.log(
      "[ConfigManager] Successfully saved all mobs to individual files."
    );
    return true;
  } catch (error) {
    console.error(
      "[ConfigManager] CRITICAL ERROR saving mobs to directory:",
      error
    );
    return false;
  }
}

module.exports = {
  initialize,
  getItem,
  getAllItems,
  updateItem,
  saveItems,
  getMob,
  getAllMobs,
  updateMob,
  saveMobs,
};
