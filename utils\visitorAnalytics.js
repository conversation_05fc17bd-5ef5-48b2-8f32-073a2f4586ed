// Garden Visitor Analytics - Track visitor patterns and provide insights

const { dbAll, dbGet } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");
const { GARDEN_VISITORS } = require("./gardenVisitors");

/**
 * Get visitor statistics for a player
 * @param {string} userId - Player user ID
 * @returns {Object} Visitor statistics
 */
async function getVisitorStats(userId) {
  try {
    const character = await getPlayerData(userId);
    if (!character) return null;

    const offersAccepted = character.visitor_offers_accepted || 0;

    // Handle visitor_unique_served field - it could be an array or JSON string
    let uniqueServed = [];
    if (character.visitor_unique_served) {
      if (Array.isArray(character.visitor_unique_served)) {
        // Already parsed by playerDataManager
        uniqueServed = character.visitor_unique_served;
      } else {
        // Still a JSON string, need to parse
        try {
          uniqueServed = JSON.parse(character.visitor_unique_served);
        } catch {
          uniqueServed = [];
        }
      }
    }

    // Get active visitors count
    const activeVisitors = await dbAll(
      "SELECT COUNT(*) as count FROM active_garden_visitors WHERE discord_id = ?",
      [userId]
    );
    const activeCount = activeVisitors[0]?.count || 0;

    // Calculate completion rate by rarity
    const rarityStats = {};
    for (const [visitorKey, visitorData] of Object.entries(GARDEN_VISITORS)) {
      const rarity = visitorData.rarity;
      if (!rarityStats[rarity]) {
        rarityStats[rarity] = { served: 0, total: 0 };
      }
      rarityStats[rarity].total++;
      if (uniqueServed.includes(visitorKey)) {
        rarityStats[rarity].served++;
      }
    }

    return {
      offersAccepted,
      uniqueVisitorsServed: uniqueServed.length,
      totalUniqueVisitors: Object.keys(GARDEN_VISITORS).length,
      activeVisitors: activeCount,
      rarityBreakdown: rarityStats,
      completionRate:
        (uniqueServed.length / Object.keys(GARDEN_VISITORS).length) * 100,
    };
  } catch (error) {
    console.error("[Visitor Analytics] Error getting visitor stats:", error);
    return null;
  }
}

/**
 * Get server-wide visitor statistics
 * @returns {Object} Server statistics
 */
async function getServerVisitorStats() {
  try {
    // Total offers accepted across all players
    const totalOffers = await dbGet(
      "SELECT SUM(visitor_offers_accepted) as total FROM players WHERE visitor_offers_accepted > 0"
    );

    // Most popular visitors (most served)
    const popularVisitors = await dbAll(`
            SELECT 
                visitor_key,
                COUNT(*) as times_served
            FROM (
                SELECT 
                    discord_id,
                    json_each.value as visitor_key
                FROM players, json_each(visitor_unique_served)
                WHERE visitor_unique_served IS NOT NULL 
                AND visitor_unique_served != '[]'
            )
            GROUP BY visitor_key
            ORDER BY times_served DESC
            LIMIT 10
        `);

    // Active visitors distribution
    const activeDistribution = await dbAll(`
            SELECT 
                visitor_rarity,
                COUNT(*) as count
            FROM active_garden_visitors
            GROUP BY visitor_rarity
            ORDER BY count DESC
        `);

    return {
      totalOffersAccepted: totalOffers?.total || 0,
      popularVisitors: popularVisitors.map((v) => ({
        visitorKey: v.visitor_key,
        visitorName: GARDEN_VISITORS[v.visitor_key]?.name || v.visitor_key,
        timesServed: v.times_served,
      })),
      activeVisitorDistribution: activeDistribution,
    };
  } catch (error) {
    console.error("[Visitor Analytics] Error getting server stats:", error);
    return null;
  }
}

/**
 * Get visitor recommendations for a player
 * @param {string} userId - Player user ID
 * @returns {Array} Array of visitor recommendations
 */
async function getVisitorRecommendations(userId) {
  try {
    const character = await getPlayerData(userId);
    if (!character) return [];

    let uniqueServed = [];
    if (character.visitor_unique_served) {
      if (Array.isArray(character.visitor_unique_served)) {
        // Already parsed by playerDataManager
        uniqueServed = character.visitor_unique_served;
      } else {
        // Still a JSON string, need to parse
        try {
          uniqueServed = JSON.parse(character.visitor_unique_served);
        } catch {
          uniqueServed = [];
        }
      }
    }

    const recommendations = [];

    // Find unserved visitors
    const unservedVisitors = Object.entries(GARDEN_VISITORS)
      .filter(([key, _]) => !uniqueServed.includes(key))
      .map(([key, data]) => ({ key, ...data }));

    // Prioritize by rarity (rarer visitors first for achievement purposes)
    const rarityPriority = {
      MYTHIC: 4,
      LEGENDARY: 3,
      RARE: 2,
      UNCOMMON: 1,
      SPECIAL: 5,
    };
    unservedVisitors.sort(
      (a, b) =>
        (rarityPriority[b.rarity] || 0) - (rarityPriority[a.rarity] || 0)
    );

    // Add top 5 recommendations
    for (let i = 0; i < Math.min(5, unservedVisitors.length); i++) {
      const visitor = unservedVisitors[i];
      recommendations.push({
        visitorKey: visitor.key,
        name: visitor.name,
        rarity: visitor.rarity,
        reason: `New ${visitor.rarity.toLowerCase()} visitor - complete for collection progress!`,
        preferredCrops: visitor.preferredCrops,
      });
    }

    return recommendations;
  } catch (error) {
    console.error("[Visitor Analytics] Error getting recommendations:", error);
    return [];
  }
}

module.exports = {
  getVisitorStats,
  getServerVisitorStats,
  getVisitorRecommendations,
};
