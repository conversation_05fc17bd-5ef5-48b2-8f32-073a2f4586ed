{"name": "Enderman Minion", "emoji": "<:minion_enderman:1378736526636220628>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "combat", "resourceItemKey": "ENDER_PEARL", "recipes": [{"ingredients": [{"itemKey": "ENDER_PEARL", "amount": 64}]}], "craftingRequirements": {"collections": {"ENDER_PEARL": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 32, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 32, "maxStorage": 192, "upgradeCost": [{"itemKey": "ENDER_PEARL", "amount": 128}]}, {"tier": 3, "generationIntervalSeconds": 30, "maxStorage": 192, "upgradeCost": [{"itemKey": "ENCHANTED_ENDER_PEARL", "amount": 8}]}, {"tier": 4, "generationIntervalSeconds": 30, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_ENDER_PEARL", "amount": 24}]}, {"tier": 5, "generationIntervalSeconds": 28, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_ENDER_PEARL", "amount": 48}]}, {"tier": 6, "generationIntervalSeconds": 28, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_ENDER_PEARL", "amount": 96}]}, {"tier": 7, "generationIntervalSeconds": 25, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_EYE_OF_ENDER", "amount": 8}]}, {"tier": 8, "generationIntervalSeconds": 25, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_EYE_OF_ENDER", "amount": 24}]}, {"tier": 9, "generationIntervalSeconds": 22, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_EYE_OF_ENDER", "amount": 48}]}, {"tier": 10, "generationIntervalSeconds": 22, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_EYE_OF_ENDER", "amount": 96}]}, {"tier": 11, "generationIntervalSeconds": 18, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_EYE_OF_ENDER", "amount": 192}]}], "drops": [{"itemKey": "POTATO", "chance": 1, "min": 2, "max": 4}]}