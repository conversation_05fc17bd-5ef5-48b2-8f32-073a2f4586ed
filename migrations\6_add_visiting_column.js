// Migration to add visiting_island_owner_id column

const MIGRATION_VERSION = 6;

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add visiting_island_owner_id column`,
  );

  await new Promise((resolve, reject) => {
    // Add the column, allowing NULL values
    db.run(
      "ALTER TABLE players ADD COLUMN visiting_island_owner_id TEXT DEFAULT NULL",
      function (err) {
        if (err) {
          if (err.message.includes("duplicate column name")) {
            console.log(
              `[Migration ${MIGRATION_VERSION}] Column visiting_island_owner_id already exists, skipping.`,
            );
            resolve();
          } else {
            console.error(
              `[Migration ${MIGRATION_VERSION}] Error adding visiting_island_owner_id column:`,
              err,
            );
            reject(err);
          }
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully added visiting_island_owner_id column.`,
          );
          resolve();
        }
      },
    );
  });
}

module.exports = { up /*, down */ };
