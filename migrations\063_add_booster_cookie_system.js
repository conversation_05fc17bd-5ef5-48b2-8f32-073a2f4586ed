// Migration 063: Add booster cookie and bits system fields
// Adds columns for booster cookie expiry, bits, bits multiplier, and last bits collection time

async function up(db, _playerDataUtils) {
  console.log(
    "Running migration 063: Add booster cookie and bits system fields...",
  );

  try {
    // Check if columns already exist
    const columns = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(players)", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const columnNames = columns.map((col) => col.name);

    // Add booster_cookie_expiry column
    if (!columnNames.includes("booster_cookie_expiry")) {
      console.log("Adding booster_cookie_expiry column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN booster_cookie_expiry INTEGER",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added booster_cookie_expiry column");
    } else {
      console.log("booster_cookie_expiry column already exists, skipping...");
    }

    // Add bits column
    if (!columnNames.includes("bits")) {
      console.log("Adding bits column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN bits INTEGER DEFAULT 0",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added bits column");
    } else {
      console.log("bits column already exists, skipping...");
    }

    // Add bits_available column
    if (!columnNames.includes("bits_available")) {
      console.log("Adding bits_available column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN bits_available INTEGER DEFAULT 0",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added bits_available column");
    } else {
      console.log("bits_available column already exists, skipping...");
    }

    // Add bits_multiplier column
    if (!columnNames.includes("bits_multiplier")) {
      console.log("Adding bits_multiplier column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN bits_multiplier REAL DEFAULT 1.0",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added bits_multiplier column");
    } else {
      console.log("bits_multiplier column already exists, skipping...");
    }

    // Add base_bits_from_cookies column (for retroactive multiplier application)
    if (!columnNames.includes("base_bits_from_cookies")) {
      console.log("Adding base_bits_from_cookies column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN base_bits_from_cookies INTEGER DEFAULT 0",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added base_bits_from_cookies column");
    } else {
      console.log("base_bits_from_cookies column already exists, skipping...");
    }

    // Add last_bits_collection column
    if (!columnNames.includes("last_bits_collection")) {
      console.log("Adding last_bits_collection column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN last_bits_collection INTEGER",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added last_bits_collection column");
    } else {
      console.log("last_bits_collection column already exists, skipping...");
    }

    console.log("Migration 063 completed successfully");
  } catch (error) {
    console.error("Migration 063 failed:", error);
    throw error;
  }
}

module.exports = { up };
