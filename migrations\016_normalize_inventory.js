// UUID not needed in current implementation

// Helper function to run db.all as a Promise
function dbAllAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve({ lastID: this.lastID, changes: this.changes });
    });
  });
}

// Safely parse JSON, returning null on error
function safeJsonParse(jsonString) {
  if (!jsonString) return null;
  try {
    return JSON.parse(jsonString);
  } catch {
    // console.error('Error parsing JSON:', e); // Optional: Log parse errors
    return null;
  }
}

module.exports = {
  async up(db) {
    console.log("Starting migration 016: Normalize Inventory...");

    try {
      // --- 1. Fetch existing player data with JSON inventory ---
      console.log("Fetching player data with inventory JSON...");
      const players = await dbAllAsync(
        db,
        "SELECT discord_id, inventory_items_json, equipment_json FROM players",
      );
      console.log(`Found ${players.length} players to process.`);

      // --- 2. Prepare INSERT statements ---
      const insertItemSql =
        "INSERT OR IGNORE INTO player_inventory_items (discord_id, item_name, amount) VALUES (?, ?, ?)";
      const insertEquipmentSql =
        "INSERT OR IGNORE INTO player_equipment (equipment_id, discord_id, item_key, is_equipped) VALUES (?, ?, ?, ?)";

      // --- 3. Process each player ---
      for (const player of players) {
        const userId = player.discord_id;
        console.log(`Processing player ${userId}...`);

        // Process Stackable Items
        const inventoryData = safeJsonParse(player.inventory_items_json);
        if (
          inventoryData &&
          typeof inventoryData === "object" &&
          inventoryData !== null
        ) {
          for (const [itemName, amount] of Object.entries(inventoryData)) {
            if (typeof amount === "number" && amount > 0) {
              await dbRunAsync(db, insertItemSql, [userId, itemName, amount]);
            } else {
              console.warn(
                `  Skipping invalid item amount for ${itemName} in player ${userId}:`,
                amount,
              );
            }
          }
        } else if (player.inventory_items_json) {
          console.warn(
            `  Could not parse or invalid format for inventory_items_json for player ${userId}. Skipping.`,
          );
        }

        // Process Equipment Items
        const equipmentData = safeJsonParse(player.equipment_json);
        if (Array.isArray(equipmentData)) {
          for (const equipment of equipmentData) {
            if (equipment && equipment.itemKey && equipment.id) {
              const equipmentId = equipment.id;
              const itemKey = equipment.itemKey;
              const isEquipped = equipment.isEquipped ? 1 : 0;
              await dbRunAsync(db, insertEquipmentSql, [
                equipmentId,
                userId,
                itemKey,
                isEquipped,
              ]);
            } else {
              console.warn(
                `  Skipping invalid equipment object in player ${userId}:`,
                equipment,
              );
            }
          }
        } else if (player.equipment_json) {
          console.warn(
            `  Could not parse or invalid format for equipment_json for player ${userId}. Skipping.`,
          );
        }
      }

      console.log("Finished migrating inventory data to normalized tables.");

      // --- 4. (Optional - Initially Commented Out) Drop old JSON columns ---
      /* 
            console.log('Dropping old JSON inventory columns from players table...');
            await dbRunAsync(db, `ALTER TABLE players DROP COLUMN inventory_items_json`);
            await dbRunAsync(db, `ALTER TABLE players DROP COLUMN equipment_json`);
            console.log('Old JSON inventory columns dropped.');
            */
      console.log(
        "Migration 016 completed successfully. Remember to uncomment ALTER TABLE commands later if needed.",
      );
    } catch (err) {
      console.error("Migration 016 FAILED:", err);
      throw err; // Re-throw error to stop migration process
    }
  },
};
