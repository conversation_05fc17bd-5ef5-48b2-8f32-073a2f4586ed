{"name": "<PERSON> Minion", "emoji": "<:minion_birch:1388634917889507433>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "foraging", "resourceItemKey": "BIRCH_LOG", "recipes": [{"ingredients": [{"itemKey": "BIRCH_LOG", "amount": 80}]}], "craftingRequirements": {"collections": {"BIRCH_LOG": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 48, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 48, "maxStorage": 192, "upgradeCost": [{"itemKey": "BIRCH_LOG", "amount": 160}]}, {"tier": 3, "generationIntervalSeconds": 45, "maxStorage": 192, "upgradeCost": [{"itemKey": "BIRCH_LOG", "amount": 320}]}, {"tier": 4, "generationIntervalSeconds": 45, "maxStorage": 384, "upgradeCost": [{"itemKey": "BIRCH_LOG", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 42, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 42, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 16}]}, {"tier": 7, "generationIntervalSeconds": 38, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 32}]}, {"tier": 8, "generationIntervalSeconds": 38, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 64}]}, {"tier": 9, "generationIntervalSeconds": 33, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 128}]}, {"tier": 10, "generationIntervalSeconds": 33, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 256}]}, {"tier": 11, "generationIntervalSeconds": 27, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_BIRCH_LOG", "amount": 512}]}], "drops": [{"itemKey": "BIRCH_LOG", "chance": 1}]}