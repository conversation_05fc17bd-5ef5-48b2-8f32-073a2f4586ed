const {
  getPlayerData: _getPlayerData,
  savePlayerData: _savePlayerData,
} = require("./playerDataManager");

/**
 * Cleans up expired potion effects for a character
 * @param {Object} character - The character object
 * @returns {boolean} - Whether any effects were cleaned up
 */
function cleanupExpiredEffects(character) {
  if (!character.activeEffects) {
    return false;
  }

  const currentTime = Date.now();
  let cleaned = false;

  Object.keys(character.activeEffects).forEach((family) => {
    const effect = character.activeEffects[family];
    if (effect.expiresAt <= currentTime) {
      delete character.activeEffects[family];
      cleaned = true;
    }
  });

  return cleaned;
}

/**
 * Gets all active potion effects for a character
 * @param {Object} character - The character object
 * @returns {Array} - Array of active effects with remaining time
 */
function getActiveEffects(character) {
  if (!character.activeEffects) {
    return [];
  }

  const currentTime = Date.now();
  return Object.entries(character.activeEffects)
    .filter(([_family, effect]) => effect.expiresAt > currentTime)
    .map(([family, effect]) => ({
      family,
      ...effect,
      remainingTime: effect.expiresAt - currentTime,
    }));
}

/**
 * Checks if a character can consume a specific potion
 * @param {Object} character - The character object
 * @param {Object} potionConfig - The potion's consumable configuration
 * @param {number} duration - The duration the potion would last (in milliseconds)
 * @returns {Object} - { canConsume: boolean, reason?: string }
 */
function canConsumePotionEffect(character, potionConfig, duration) {
  if (!character.activeEffects) {
    return { canConsume: true };
  }

  // If God Potion is active, block consumption of regular potions
  if (isGodPotionActive(character)) {
    return {
      canConsume: false,
      reason: `You already have an active God Potion. All potion effects are maxed.`,
    };
  }

  const currentEffect = character.activeEffects[potionConfig.family];
  const currentTime = Date.now();

  // Calculate remaining time for current effect
  let remainingTime = 0;
  if (currentEffect && currentEffect.expiresAt > currentTime) {
    remainingTime = currentEffect.expiresAt - currentTime;
  }

  // Check if adding this potion would exceed 72 hours
  const maxDuration = 72 * 60 * 60 * 1000; // 72 hours in milliseconds
  const newTotalTime = remainingTime + duration;

  if (newTotalTime > maxDuration) {
    const maxHours = Math.floor(maxDuration / (60 * 60 * 1000));
    const currentHours = Math.floor(remainingTime / (60 * 60 * 1000));

    return {
      canConsume: false,
      reason: `Would exceed the ${maxHours}-hour limit for ${potionConfig.family} effects. Current remaining time: ${currentHours} hours`,
    };
  }

  // Check if there's a stronger potion active
  if (currentEffect && currentEffect.tier > potionConfig.tier) {
    const currentPotionName = currentEffect.name || "Unknown Potion";
    return {
      canConsume: false,
      reason: `You have a stronger ${potionConfig.family} effect active: **${currentPotionName}**`,
    };
  }

  return { canConsume: true };
}

/**
 * Applies a potion effect to a character
 * @param {Object} character - The character object
 * @param {string} potionName - The name of the potion
 * @param {Object} potionConfig - The potion's consumable configuration
 * @param {number} duration - The duration the potion should last (in milliseconds)
 */
function applyPotionEffect(character, potionName, potionConfig, duration) {
  if (!character.activeEffects) {
    character.activeEffects = {};
  }

  const currentTime = Date.now();
  const newExpiresAt = currentTime + duration;

  // Apply the potion effect (replaces any weaker effect)
  character.activeEffects[potionConfig.family] = {
    name: potionName,
    tier: potionConfig.tier,
    effects: potionConfig.effects,
    expiresAt: newExpiresAt,
    appliedAt: currentTime,
  };
}

// God Potion helpers
const GOD_POTION_KEY = "__GOD_POTION__";

function isGodPotionActive(character) {
  const effect = character?.activeEffects?.[GOD_POTION_KEY];
  return !!(effect && effect.expiresAt > Date.now());
}

function getGodPotionRemaining(character) {
  const effect = character?.activeEffects?.[GOD_POTION_KEY];
  if (!effect) return 0;
  return Math.max(0, effect.expiresAt - Date.now());
}

// Computes the aggregated highest-tier effects for all potion families, excluding awkward potion and non-POTION_EFFECT items
function computeGodPotionAggregatedEffects(configManager) {
  const all = configManager.getAllItems();
  // family -> best { name, tier, effects }
  const bestByFamily = {};
  for (const [key, item] of Object.entries(all)) {
    const consumable = item?.consumable;
    if (!consumable || consumable.type !== "POTION_EFFECT") continue;
    if ((item.name || "").toLowerCase().includes("awkward")) continue; // skip awkward potion
    const family = consumable.family;
    if (!family) continue;
    const entry = {
      name: item.name || key,
      tier: consumable.tier || 0,
      effects: consumable.effects || {},
      emoji: item.emoji,
    };
    if (!bestByFamily[family] || entry.tier > bestByFamily[family].tier) {
      bestByFamily[family] = entry;
    }
  }

  // sum effects
  const aggregated = {
    effects: {},
    families: Object.keys(bestByFamily).length,
    details: bestByFamily,
  };
  for (const fam of Object.keys(bestByFamily)) {
    const effs = bestByFamily[fam].effects;
    for (const [stat, val] of Object.entries(effs)) {
      aggregated.effects[stat] = (aggregated.effects[stat] || 0) + val;
    }
  }
  return aggregated;
}

function applyGodPotion(character, durationMs) {
  if (!character.activeEffects) character.activeEffects = {};
  const now = Date.now();
  const current = character.activeEffects[GOD_POTION_KEY];
  const maxDuration = 72 * 60 * 60 * 1000;
  let newExpiresAt;
  if (current && current.expiresAt > now) {
    // extend with cap
    const remaining = current.expiresAt - now;
    const newTotal = Math.min(maxDuration, remaining + durationMs);
    newExpiresAt = now + newTotal;
  } else {
    newExpiresAt = now + durationMs;
  }

  // When applying God Potion, wipe other potion effects (but keep booster cookie system which is separate)
  if (character.activeEffects) {
    for (const k of Object.keys(character.activeEffects)) {
      if (k !== GOD_POTION_KEY) {
        delete character.activeEffects[k];
      }
    }
  }

  character.activeEffects[GOD_POTION_KEY] = {
    name: "God Potion",
    expiresAt: newExpiresAt,
    appliedAt: now,
  };
}

/**
 * Formats duration for display
 * @param {number} durationMs - Duration in milliseconds
 * @returns {string} - Formatted duration string
 */
function formatDuration(durationMs) {
  const totalSeconds = Math.floor(durationMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Formats duration for display (shorter version for embeds)
 * @param {number} durationMs - Duration in milliseconds
 * @returns {string} - Formatted duration string
 */
function formatDurationShort(durationMs) {
  const totalSeconds = Math.floor(durationMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

module.exports = {
  cleanupExpiredEffects,
  getActiveEffects,
  canConsumePotionEffect,
  applyPotionEffect,
  formatDuration,
  formatDurationShort,
  // God Potion exports
  isGodPotionActive,
  getGodPotionRemaining,
  computeGodPotionAggregatedEffects,
  applyGodPotion,
  GOD_POTION_KEY,
};
