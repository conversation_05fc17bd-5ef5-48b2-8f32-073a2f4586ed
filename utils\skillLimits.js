/**
 * Calculates the maximum number of consecutive actions a player can perform
 * for a skill based on their level in that skill.
 *
 * Formula:
 * - Base: 25 actions
 * - Levels 1-5: +5 per level
 * - Levels 6-10: +10 per level
 * - Levels 11-15: +15 per level
 * - Levels 16-20: +20 per level
 * - Levels 21-25: +25 per level
 * - Levels 26-30: +30 per level
 * - Levels 31-35: +35 per level
 * - Levels 36-40: +40 per level
 * - Levels 41-45: +45 per level
 * - Levels 46-50: +50 per level
 * - Levels 50+: Capped at level 50 bonus
 *
 * @param {number} skillLevel The player's level in the relevant skill.
 * @returns {number} The maximum allowed action amount.
 */
function calculateMaxActionAmount(skillLevel) {
  const baseLimit = 25;

  if (skillLevel <= 0) {
    return baseLimit;
  }

  const effectiveLevel = Math.min(skillLevel, 50);
  let totalBonus = 0;
  let currentLevel = 1;

  const bonusPerLevel = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50];

  for (let tier = 0; tier < 10 && currentLevel <= effectiveLevel; tier++) {
    const levelsInTier = Math.min(5, effectiveLevel - currentLevel + 1);
    totalBonus += levelsInTier * bonusPerLevel[tier];
    currentLevel += 5;
  }

  return baseLimit + totalBonus;
}

module.exports = { calculateMaxActionAmount };
