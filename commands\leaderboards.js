const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  EmbedBuilder,
  MessageFlags,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const { SKILLS, skillEmojis, EMBED_COLORS } = require("../gameConfig"); // Get skills and potentially emojis
const { COLLECTIONS } = require("../data/collections"); // Import collections data
const { safeJsonParse, getPlayerData } = require("../utils/playerDataManager"); // Re-use safe JSON parsing and player data fetching
const { getLevelFromExp } = require("../utils/expFunctions"); // Import necessary exp functions
const {
  // calculateDisblockLevel,
  formatDisblockLevel,
  calculateXPBreakdown,
} = require("../utils/disblockXpSystem"); // Import Disblock level functions
const { dbAll, dbGet } = require("../utils/dbUtils"); // Import the database helpers
const { getItem } = require("../utils/configManager"); // Import to get item emojis

// Helper function to get all collections with their display names
function getAllCollections() {
  const allCollections = [];

  for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
    for (const [itemKey, collectionData] of Object.entries(
      categoryCollections
    )) {
      allCollections.push({
        name: `${collectionData.name} Collection`,
        value: `collection_${itemKey}`,
        itemKey: itemKey,
        displayName: collectionData.name,
      });
    }
  }

  return allCollections.sort((a, b) => a.name.localeCompare(b.name));
}

// Collections will be loaded dynamically in autocomplete

module.exports = {
  data: new SlashCommandBuilder()
    .setName("leaderboards")
    .setDescription("View game leaderboards")
    .addStringOption((option) =>
      option
        .setName("type")
        .setDescription("Choose the leaderboard type")
        .setRequired(true)
        .setAutocomplete(true)
    ),
  async execute(interaction) {
    const leaderboardType = interaction.options.getString("type");
    const page =
      interaction.options?.getInteger?.("page") ||
      interaction.pageOverride ||
      1;

    await interaction.deferReply();

    try {
      let allPlayersData;
      try {
        // Fetch skills_json, name, and disblock_xp for leaderboards
        allPlayersData = await dbAll(
          "SELECT discord_id, name, skills_json, disblock_xp FROM players"
        );
      } catch (dbError) {
        console.error("Leaderboard DB Error:", dbError);
        return interaction.editReply(
          "[LDR-DB-ERR] Failed to retrieve player data for the leaderboard. Please report this error code to an Admin."
        );
      }

      if (!allPlayersData || allPlayersData.length === 0) {
        return interaction.editReply(
          "No player data found to generate leaderboard."
        );
      }

      let leaderboardTitle = "Leaderboard";
      let leaderboardData = []; // Will hold { name: string, value: number | { level: number, totalExp: number } }

      // --- Skill Average Logic ---
      if (leaderboardType === "skill_average") {
        leaderboardTitle = "Skill Average Leaderboard";
        const skillNames = SKILLS.LIST;
        if (!skillNames || skillNames.length === 0) {
          console.error("SKILLS.LIST is not defined or empty in gameConfig.js");
          return interaction.editReply(
            "[LDR-CFG-001] Error: Skill list configuration is missing. Please report this error code to an Admin."
          );
        }
        const excludedSkills = SKILLS.AVERAGE_EXCLUDED; // Use centralized excluded skills list

        leaderboardData = allPlayersData
          .map((player) => {
            const skills = safeJsonParse(player.skills_json, {});
            let totalLevel = 0;
            let includedSkillsCount = 0;
            skillNames.forEach((skillName) => {
              // Use getLevelFromExp to be consistent and handle potential future level calculation changes
              const skillData = skills[skillName] || { exp: 0.0 };
              const rawExp = skillData.exp || 0;
              const levelInfo = getLevelFromExp(rawExp);
              const currentLevel = levelInfo.level;

              if (!excludedSkills.includes(skillName)) {
                totalLevel += currentLevel; // Add the calculated level
                includedSkillsCount++;
              }
            });
            const average =
              includedSkillsCount > 0 ? totalLevel / includedSkillsCount : 0;
            return {
              name: player.name,
              discord_id: player.discord_id,
              value: average,
            };
          })
          .sort((a, b) => b.value - a.value); // Sort by average descending

        // --- Individual Skill Logic ---
      } else if (SKILLS.LIST.includes(leaderboardType)) {
        const skillName = leaderboardType;
        const skillEmoji = skillEmojis[skillName] || "❓";
        leaderboardTitle = `${skillEmoji} ${skillName.charAt(0).toUpperCase() + skillName.slice(1)} Leaderboard`;

        leaderboardData = allPlayersData
          .map((player) => {
            const skills = safeJsonParse(player.skills_json, {});
            const skillData = skills[skillName] || { exp: 0.0 };
            const rawExp = skillData.exp || 0;
            const levelInfo = getLevelFromExp(rawExp);

            return {
              name: player.name,
              discord_id: player.discord_id, // Include discord_id for user lookup
              value: {
                level: levelInfo.level,
                totalExp: rawExp, // Use raw experience for tie-breaking
              },
            };
          })
          .sort((a, b) => {
            // Sort descending: first by level, then by total experience
            if (b.value.level !== a.value.level) {
              return b.value.level - a.value.level;
            } else {
              return b.value.totalExp - a.value.totalExp;
            }
          });

        // --- Networth Logic ---
      } else if (leaderboardType === "networth") {
        leaderboardTitle =
          "<:bank_coins:1367849114242383987> Networth Leaderboard";
        try {
          // Import detailed networth calculation
          const { getNetworthBreakdown } = require("./networth");

          // Fetch all players for networth calculation
          const allPlayersDbData = await dbAll(
            "SELECT discord_id, name FROM players"
          );

          if (!allPlayersDbData || allPlayersDbData.length === 0) {
            console.error(
              "[CRITICAL_DEBUG] Database query for player data returned no results."
            );
            await interaction.reply({
              content:
                "An error occurred while fetching leaderboard data. Please check logs.",
              ephemeral: true,
              flags: MessageFlags.Ephemeral,
            });
            return;
          }

          const networthPromises = allPlayersDbData.map(async (player) => {
            const { total: networth } = await getNetworthBreakdown(
              player.discord_id
            );
            return {
              name: player.name,
              discord_id: player.discord_id,
              value: networth,
            };
          });

          leaderboardData = await Promise.all(networthPromises);
          leaderboardData = leaderboardData.sort((a, b) => b.value - a.value);
        } catch (err) {
          console.error(
            "Error executing leaderboard command (networth specific section):",
            err
          );
          // Allow global error handler in bot.js to handle the interaction reply.
          // Rethrow or simply return to ensure the command execution stops if needed,
          // but the main error handler should catch this if it propagates.
          throw err; // Rethrow to be caught by global handler
        }

        // --- Disblock Level Logic ---
      } else if (leaderboardType === "disblock_level") {
        leaderboardTitle = "🎯 Disblock Level Leaderboard";

        // Calculate actual XP including pet score for each player
        const xpPromises = allPlayersData.map(async (player) => {
          try {
            // Get full player data using getPlayerData for consistency with level command
            const fullPlayerData = await getPlayerData(player.discord_id);

            if (!fullPlayerData) {
              return {
                name: player.name,
                value: "0.00",
                xp: 0,
              };
            }

            // Calculate total XP including pet score (fullPlayerData is already properly parsed)
            const breakdown = await calculateXPBreakdown(
              fullPlayerData,
              player.discord_id
            );
            const totalXP = breakdown.grandTotal;
            const formattedLevel = formatDisblockLevel(totalXP);

            return {
              name: player.name,
              discord_id: player.discord_id, // Include discord_id for user lookup
              value: formattedLevel,
              xp: totalXP, // Keep XP for sorting
            };
          } catch (error) {
            console.error(
              `[Leaderboard] Error calculating XP for player ${player.discord_id}:`,
              error
            );
            // Fallback to stored XP if calculation fails
            const xp = player.disblock_xp || 0;
            const formattedLevel = formatDisblockLevel(xp);
            return {
              name: player.name,
              discord_id: player.discord_id, // Include discord_id for user lookup
              value: formattedLevel,
              xp: xp,
            };
          }
        });

        leaderboardData = await Promise.all(xpPromises);
        leaderboardData = leaderboardData.sort((a, b) => b.xp - a.xp); // Sort by XP but display level

        // --- Collections Logic ---
      } else if (leaderboardType.startsWith("collection_")) {
        const collectionItemKey = leaderboardType.replace("collection_", "");

        // Find the collection data
        let collectionData = null;
        let collectionDisplayName = collectionItemKey;
        let itemEmoji = "📦";

        for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
          if (categoryCollections[collectionItemKey]) {
            collectionData = categoryCollections[collectionItemKey];
            collectionDisplayName = collectionData.name;
            break;
          }
        }

        if (!collectionData) {
          return interaction.editReply(
            "[LDR-COL-001] Collection not found. Please report this error code to an Admin."
          );
        }

        // Get the item emoji from the item data
        const itemData = getItem(collectionItemKey);
        if (itemData && itemData.emoji) {
          itemEmoji = itemData.emoji;
        }

        leaderboardTitle = `${itemEmoji} ${collectionDisplayName} Collection Leaderboard`;

        // Fetch collections data for all players
        const allPlayersCollectionsData = await dbAll(
          "SELECT discord_id, name, collections_json FROM players"
        );

        if (
          !allPlayersCollectionsData ||
          allPlayersCollectionsData.length === 0
        ) {
          return interaction.editReply(
            "No player data found to generate collection leaderboard."
          );
        }

        leaderboardData = allPlayersCollectionsData
          .map((player) => {
            const collections = safeJsonParse(player.collections_json, {});
            const collectionAmount = collections[collectionItemKey] || 0;

            return {
              name: player.name,
              discord_id: player.discord_id,
              value: collectionAmount,
            };
          })
          .filter((player) => player.value > 0) // Only show players with this collection
          .sort((a, b) => b.value - a.value); // Sort by collection amount descending

        // --- Slayer Leaderboards Logic ---
      } else if (
        leaderboardType === "zombie_slayer" ||
        leaderboardType === "spider_slayer"
      ) {
        const slayerType = leaderboardType.replace("_slayer", "");
        const slayerDisplayName =
          slayerType.charAt(0).toUpperCase() + slayerType.slice(1);

        // Define slayer emojis
        const SLAYER_EMOJIS = {
          zombie: "<:revenant_horror:1389646540460658970>",
          spider: "<:mob_spider:1370526342927618078>",
        };

        const slayerEmoji = SLAYER_EMOJIS[slayerType] || "⭐";
        leaderboardTitle = `${slayerEmoji} ${slayerDisplayName} Slayer Leaderboard`;

        // Fetch slayer XP data for all players
        const allPlayersSlayerData = await dbAll(
          "SELECT discord_id, name, slayer_xp_json FROM players"
        );

        if (!allPlayersSlayerData || allPlayersSlayerData.length === 0) {
          return interaction.editReply(
            "No player data found to generate slayer leaderboard."
          );
        }

        leaderboardData = allPlayersSlayerData
          .map((player) => {
            const slayerXpData = safeJsonParse(player.slayer_xp_json, {});
            const slayerXp = slayerXpData[slayerType] || 0;

            return {
              name: player.name,
              discord_id: player.discord_id,
              value: slayerXp,
            };
          })
          .filter((player) => player.value > 0) // Only show players with slayer XP
          .sort((a, b) => b.value - a.value); // Sort by slayer XP descending

        // --- Garden Level Logic ---
      } else if (leaderboardType === "garden_level") {
        leaderboardTitle = "<:garden:1394656922623410237> Garden Leaderboards";
        // Fetch garden XP for all players
        const allPlayersGardenData = await dbAll(
          "SELECT discord_id, name, garden_xp FROM players"
        );
        if (!allPlayersGardenData || allPlayersGardenData.length === 0) {
          return interaction.editReply(
            "No player data found to generate garden leaderboard."
          );
        }
        leaderboardData = allPlayersGardenData
          .map((player) => {
            const xp = player.garden_xp || 0;
            const levelInfo = getLevelFromExp(xp);
            return {
              name: player.name,
              discord_id: player.discord_id,
              value: {
                level: levelInfo.level,
                totalExp: xp,
              },
            };
          })
          .sort((a, b) => {
            if (b.value.level !== a.value.level) {
              return b.value.level - a.value.level;
            } else {
              return b.value.totalExp - a.value.totalExp;
            }
          });

        // --- Unknown Type ---
      } else {
        console.warn(`Unknown leaderboard type requested: ${leaderboardType}`);
        return interaction.editReply(
          "[LDR-EXE-001] Unknown leaderboard type selected. If you believe this is incorrect, please report this error code to an Admin."
        );
      }

      const playersPerPage = 10;
      const startIndex = (page - 1) * playersPerPage;
      const endIndex = startIndex + playersPerPage;
      const totalPages = Math.ceil(leaderboardData.length / playersPerPage);

      // Validate page number
      if (page > totalPages && totalPages > 0) {
        return interaction.editReply(
          `Page ${page} does not exist. There are only ${totalPages} pages available.`
        );
      }

      const pageData = leaderboardData.slice(startIndex, endIndex);

      // Use consistent max lengths like inventory does
      const MAX_NAME_LEN = 26; // Increased from 18 to make lines longer
      const MAX_RANK_LEN = 4; // Accommodates up to #999

      // Format the players for this page with inventory-style formatting
      let maxValueLength = 0;

      const entryData = pageData.map((entry, index) => {
        const globalRank = startIndex + index + 1;
        const playerName = entry.name;
        const rankStr = `#${globalRank}`;

        // Truncate name if too long, like inventory does
        const truncatedName =
          playerName.length > MAX_NAME_LEN
            ? playerName.slice(0, MAX_NAME_LEN - 3) + "..."
            : playerName;

        // Format leaderboard display value based on type
        let displayValue;
        if (leaderboardType === "skill_average") {
          displayValue = entry.value.toFixed(2);
        } else if (leaderboardType === "networth") {
          displayValue = entry.value.toLocaleString("en-US", {
            minimumFractionDigits: 1,
            maximumFractionDigits: 1,
          });
        } else if (leaderboardType === "disblock_level") {
          displayValue = entry.value;
        } else if (leaderboardType === "garden_level") {
          displayValue = entry.value.totalExp.toLocaleString();
        } else if (leaderboardType.startsWith("collection_")) {
          displayValue = entry.value.toLocaleString();
        } else if (
          leaderboardType === "zombie_slayer" ||
          leaderboardType === "spider_slayer"
        ) {
          displayValue = entry.value.toLocaleString();
        } else {
          // Individual skills
          displayValue = `Level ${entry.value.level}`;
        }

        // Track maximum value length only (rank and name are now fixed)
        maxValueLength = Math.max(maxValueLength, displayValue.length);

        return {
          rankStr,
          truncatedName,
          displayValue,
          originalName: playerName,
        };
      });

      // Second pass: format with consistent widths across all pages
      const leaderboardEntries = entryData.map((entry) => {
        const paddedRank = entry.rankStr.padEnd(MAX_RANK_LEN, " ");
        const paddedName = entry.truncatedName.padEnd(MAX_NAME_LEN, " ");
        const paddedValue = entry.displayValue.padStart(maxValueLength, " ");

        // Everything inside backticks with consistent width: `#1   PlayerName         Value`
        return `\`${paddedRank} ${paddedName} ${paddedValue}\``;
      });

      // Add a header and separator for minimalist design
      let typeEmoji =
        leaderboardType === "skill_average"
          ? "<:wisdom_stat:1369101731278880849>"
          : leaderboardType === "networth"
            ? "<:bank_coins:1367849114242383987>"
            : leaderboardType === "disblock_level"
              ? "🎯"
              : skillEmojis[leaderboardType] || "🏆";

      // For collections, get the item emoji
      if (leaderboardType.startsWith("collection_")) {
        const collectionItemKey = leaderboardType.replace("collection_", "");
        const itemData = getItem(collectionItemKey);
        if (itemData && itemData.emoji) {
          typeEmoji = itemData.emoji;
        } else {
          typeEmoji = "📦";
        }
      } else if (
        leaderboardType === "zombie_slayer" ||
        leaderboardType === "spider_slayer"
      ) {
        // For slayers, get the specific slayer emoji
        const slayerType = leaderboardType.replace("_slayer", "");
        const SLAYER_EMOJIS = {
          zombie: "<:revenant_horror:1389646540460658970>",
          spider: "<:mob_spider:1370526342927618078>",
        };
        typeEmoji = SLAYER_EMOJIS[slayerType] || "⭐";
      }

      // Format title for embed - clean and simple
      const formattedTitle =
        leaderboardType === "skill_average"
          ? "Skill Average Leaderboard"
          : leaderboardType === "networth"
            ? "Networth Leaderboard"
            : leaderboardType === "disblock_level"
              ? "Disblock Level Leaderboard"
              : leaderboardType === "garden_level"
                ? "Garden Leaderboards"
                : leaderboardType.startsWith("collection_")
                  ? leaderboardTitle.replace(/^[^\s]+\s/, "") // Remove emoji from collection title
                  : leaderboardType === "zombie_slayer" ||
                      leaderboardType === "spider_slayer"
                    ? leaderboardTitle.replace(/^[^\s]+\s/, "") // Remove emoji from slayer title
                    : SKILLS.LIST.includes(leaderboardType)
                      ? `${leaderboardType.charAt(0).toUpperCase() + leaderboardType.slice(1)} Skill Leaderboard`
                      : leaderboardTitle;

      const embedTitle = `${typeEmoji} ${formattedTitle}`;

      // Create user position info
      let userPositionInfo = "";
      if (interaction.user) {
        const userId = interaction.user.id;

        // Find the player data for this user
        let userPlayerData = null;
        if (leaderboardType === "networth") {
          // For networth, we need to fetch the player data to get their name
          userPlayerData = await dbGet(
            "SELECT discord_id, name FROM players WHERE discord_id = ?",
            [userId]
          );
        } else {
          // For skill leaderboards, we can find the user in the existing leaderboardData
          const userEntry = leaderboardData.find(
            (entry) => entry.name && entry.discord_id === userId
          );
          if (userEntry) {
            userPlayerData = { discord_id: userId, name: userEntry.name };
          }
        }

        if (userPlayerData) {
          const userRankIndex = leaderboardData.findIndex(
            (entry) => entry.name === userPlayerData.name
          );
          if (userRankIndex !== -1) {
            const userEntry = leaderboardData[userRankIndex];
            let userValue;

            // Format user's value based on leaderboard type
            if (leaderboardType === "skill_average") {
              userValue = userEntry.value.toFixed(2);
            } else if (leaderboardType === "networth") {
              userValue = userEntry.value.toLocaleString("en-US", {
                minimumFractionDigits: 1,
                maximumFractionDigits: 1,
              });
            } else if (leaderboardType === "disblock_level") {
              userValue = userEntry.value;
            } else if (leaderboardType === "garden_level") {
              userValue = userEntry.value.totalExp.toLocaleString();
            } else if (leaderboardType.startsWith("collection_")) {
              userValue = userEntry.value.toLocaleString();
            } else if (
              leaderboardType === "zombie_slayer" ||
              leaderboardType === "spider_slayer"
            ) {
              userValue = userEntry.value.toLocaleString();
            } else {
              userValue = `Level ${userEntry.value.level}`;
            }

            // Format user position to match the leaderboard entries with consistent widths
            const userRankStr = `#${userRankIndex + 1}`;

            // Truncate user name if too long, like we do for leaderboard entries
            const truncatedUserName =
              userPlayerData.name.length > MAX_NAME_LEN
                ? userPlayerData.name.slice(0, MAX_NAME_LEN - 3) + "..."
                : userPlayerData.name;

            const paddedUserRank = userRankStr.padEnd(MAX_RANK_LEN, " ");
            const paddedUserName = truncatedUserName.padEnd(MAX_NAME_LEN, " ");
            const paddedUserValue = userValue.padStart(maxValueLength, " ");

            userPositionInfo = `▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬\n**\`${paddedUserRank} ${paddedUserName} ${paddedUserValue}\`**`;
          }
        }
      }

      // Add pagination info at the bottom
      const paginationInfo =
        totalPages > 1 ? `Page ${page}/${totalPages}` : "Page 1/1";
      const footerText = paginationInfo;

      // Create the leaderboard string with minimalist formatting
      const leaderboardString = [
        ...leaderboardEntries,
        userPositionInfo, // Add user's position if available
      ].join("\n");

      // 5. Create the embed with clean styling
      const embed = new EmbedBuilder()
        .setTitle(embedTitle)
        .setDescription(leaderboardString || "No players found.")
        .setColor(EMBED_COLORS.BLUE) // Clean blue color for minimalist design
        .setFooter({ text: footerText });

      // 6. Create pagination buttons if there are multiple pages
      const components = [];
      if (totalPages > 1) {
        const row = new ActionRowBuilder();

        // Previous page button
        const prevButton = new ButtonBuilder()
          .setCustomId(`leaderboard_${leaderboardType}_${page - 1}`)
          .setLabel("Prev")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === 1);

        // Next page button
        const nextButton = new ButtonBuilder()
          .setCustomId(`leaderboard_${leaderboardType}_${page + 1}`)
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === totalPages);

        row.addComponents(prevButton, nextButton);
        components.push(row);
      }

      // Send the embed with pagination buttons
      await interaction.editReply({ embeds: [embed], components });
    } catch (error) {
      console.error(
        `Error executing leaderboard command (${leaderboardType}):`,
        error
      );
      // Use followUp if editReply might fail after an error
      await interaction
        .followUp({
          content:
            "[LDR-EXE-ERR] An error occurred while fetching the leaderboard. Please report this error code to an Admin.",
          flags: [MessageFlags.Ephemeral],
        })
        .catch(() => {});
    }
  },

  // Handle pagination button interactions
  async handlePagination(interaction) {
    try {
      await interaction.deferUpdate();

      // Parse the custom ID to extract leaderboard type and page
      // Format: leaderboard_{type}_{page} where type can contain underscores
      const customIdParts = interaction.customId.split("_");

      if (customIdParts.length < 3) {
        console.error(
          "[Leaderboard Pagination] Invalid customId format:",
          interaction.customId
        );
        return;
      }

      // The page number is always the last part
      const pageString = customIdParts[customIdParts.length - 1];
      const newPage = parseInt(pageString);

      // The leaderboard type is everything between 'leaderboard_' and the last '_'
      const leaderboardType = customIdParts.slice(1, -1).join("_");

      if (isNaN(newPage) || newPage < 1) {
        console.error(
          "[Leaderboard Pagination] Invalid page number:",
          pageString,
          "parsed as:",
          newPage
        );
        return;
      }

      // Re-execute the leaderboard command with the new page
      const fakeInteraction = {
        ...interaction,
        pageOverride: newPage,
        deferReply: async () => {}, // No-op since we're updating existing message
        editReply: interaction.editReply.bind(interaction),
        options: {
          getString: () => {
            return leaderboardType;
          },
          getInteger: () => {
            return null; // Don't return page from options, use pageOverride instead
          },
        },
      };

      // Call the main execute function with the fake interaction
      await this.execute(fakeInteraction);
    } catch (error) {
      console.error(
        "[Leaderboard Pagination] Error handling pagination:",
        error
      );
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "An error occurred while updating the leaderboard.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    }
  },

  // Handle autocomplete for leaderboard types
  async autocomplete(interaction) {
    const focusedValue = interaction.options.getFocused().toLowerCase();

    // Create all available options
    const allOptions = [
      { name: "Skill Average", value: "skill_average" },
      { name: "Networth", value: "networth" },
      { name: "Disblock Level", value: "disblock_level" },
      { name: "Garden", value: "garden_level" },
      { name: "Combat", value: "combat" },
      { name: "Mining", value: "mining" },
      { name: "Foraging", value: "foraging" },
      { name: "Farming", value: "farming" },
      { name: "Fishing", value: "fishing" },
      { name: "Enchanting", value: "enchanting" },
      { name: "Alchemy", value: "alchemy" },
      { name: "Crafting", value: "crafting" },
      { name: "Runecrafting", value: "runecrafting" },
      { name: "Social", value: "social" },
      { name: "Taming", value: "taming" },
      { name: "Zombie Slayer", value: "zombie_slayer" },
      { name: "Spider Slayer", value: "spider_slayer" },
    ];

    // Add all collections
    const collections = getAllCollections();
    allOptions.push(...collections);

    // Filter options based on user input (check both name and value)
    const filtered = allOptions.filter(
      (option) =>
        option.name.toLowerCase().includes(focusedValue) ||
        option.value.toLowerCase().includes(focusedValue)
    );

    // Discord limits autocomplete to 25 options
    const limited = filtered.slice(0, 25);

    await interaction.respond(limited);
  },
};
