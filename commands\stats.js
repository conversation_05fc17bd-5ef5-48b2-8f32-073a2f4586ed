const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  Embed<PERSON><PERSON>er,
  MessageFlags,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager");
const { STATS } = require("../gameConfig");
const { calculateAllStats } = require("../utils/statCalculations");
const { getRankPrefix } = require("../utils/rankUtils");
const { getLevelFromExp } = require("../utils/expFunctions");
const { checkRankPermission } = require("../utils/permissionUtils");
const { recordActivityForAnnouncements } = require("../utils/activityTracker");
const { formatNumber } = require("../utils/displayUtils");
const { EMBED_COLORS } = require("../gameConfig");
const configManager = require("../utils/configManager");

// constants to avoid magic numbers
const COLLECTOR_TIMEOUT_MS = 300000; // 5 minutes
const PROGRESS_BAR_SIZE = 10;
const KILLS_PAGE_SIZE = 10;
const DECIMAL_PRECISION = 10; // for rounding calculations
const MAX_FRACTION_DIGITS = 1;
const INITIAL_PAGE = 0;
const INITIAL_LENGTH = 0;
const PAGE_COUNT = 3; // total number of pages in stats display
const MIN_PAGE_COUNT = 1; // minimum number of pages

// helper function to calculate potion bonuses for a stat (supports God Potion)
const calculatePotionBonus = (character, statKey) => {
  const {
    isGodPotionActive,
    computeGodPotionAggregatedEffects,
  } = require("../utils/potionEffects");
  if (isGodPotionActive(character)) {
    const aggregated = computeGodPotionAggregatedEffects(
      require("../utils/configManager")
    );
    return aggregated.effects[statKey] || 0;
  }
  let potionBonus = 0;
  if (character.activeEffects) {
    const currentTime = Date.now();
    Object.values(character.activeEffects).forEach((effect) => {
      if (
        effect.expiresAt > currentTime &&
        effect.effects &&
        effect.effects[statKey]
      ) {
        potionBonus += effect.effects[statKey];
      }
    });
  }
  return potionBonus;
};

// helper function to calculate booster cookie bonuses for a stat
const calculateBoosterCookieBonus = (character, statKey) => {
  const {
    isBoosterCookieActive,
    getBoosterCookieStatBonuses,
  } = require("../utils/boosterCookieManager");
  if (isBoosterCookieActive(character)) {
    const boosterBonuses = getBoosterCookieStatBonuses();
    return boosterBonuses[statKey] || 0;
  }
  return 0;
};

// helper function to calculate temporary stat values (equipment, pets, etc.)
const calculateTemporaryValue = (
  playerStatObject,
  potionBonus,
  boosterCookieBonus
) => {
  return (
    (playerStatObject?.fromEquipment ?? 0) +
    (playerStatObject?.fromPet ?? 0) +
    (playerStatObject?.fromSetBonus ?? 0) +
    (playerStatObject?.fromAccessories ?? 0) +
    (playerStatObject?.fromMagicalPower ?? 0) +
    potionBonus +
    boosterCookieBonus
  );
};

// helper function to format stat display values
const formatStatValue = (statDefinition, totalValue, bonus, character) => {
  let valuePart, bonusPart;

  if (statDefinition.isPercentage) {
    const formattedValue = (
      Math.round(totalValue * DECIMAL_PRECISION) / DECIMAL_PRECISION
    ).toFixed(MAX_FRACTION_DIGITS);
    valuePart = typeof totalValue === "number" ? `+${formattedValue}%` : "??";
    bonusPart =
      bonus !== 0
        ? `(${bonus > 0 ? "+" : ""}${(Math.round(bonus * DECIMAL_PRECISION) / DECIMAL_PRECISION).toFixed(MAX_FRACTION_DIGITS)}%)`
        : "";
  } else if (statDefinition.name === "Health") {
    const currentHealthDisplay =
      typeof character.current_health === "number"
        ? Math.round(character.current_health)
        : "??";
    const totalHealthDisplay =
      typeof totalValue === "number" ? Math.round(totalValue) : "??";
    valuePart = `${currentHealthDisplay}/${totalHealthDisplay}`;
    bonusPart =
      bonus !== 0 ? `(${bonus > 0 ? "+" : ""}${Math.round(bonus)})` : "";
  } else {
    valuePart =
      typeof totalValue === "number"
        ? totalValue.toLocaleString(undefined, {
            maximumFractionDigits: MAX_FRACTION_DIGITS,
          })
        : "??";
    bonusPart =
      bonus !== 0
        ? `(${bonus > 0 ? "+" : ""}${(Math.round(bonus * DECIMAL_PRECISION) / DECIMAL_PRECISION).toFixed(MAX_FRACTION_DIGITS)})`
        : "";
  }

  return { valuePart, bonusPart };
};

// helper function to create a detailed stat breakdown with sources
const createStatBreakdown = (character, statKey) => {
  const statDefinition = STATS[statKey];
  if (!statDefinition) return "No stat definition found.";

  const statObject = character.stats[statKey] || {};
  const allStats = calculateAllStats(character);
  const totalValue = allStats[statKey] || 0;
  const breakdown = [];

  // emoji mappings for different sources
  const sourceEmojis = {
    base: "<:base_value:1403042580995768400>", // base value
    fromLevels: "<:gold_ingot:1371269747664228463>", // skill levels
    fromSlayers: "<:revenant_horror:1389646540460658970>", // slayer bonuses
    fromEquipment: "<:diamond_sword:1390124635802173520>", // equipment - using diamond sword as general equipment emoji
    fromPet: "<:pet_chicken:1376171013334896680>", // active pet - using chicken pet as general pet emoji
    fromSetBonus: "<:farm_suit_helmet:1367680215253516399>", // set bonuses - using farm armor as set example
    fromAccessories: "<:emerald_ring:1386002214874779888>", // accessories - using emerald ring as accessory emoji
    fromMagicalPower: "<:stat_magic_find:1399855928894951425>", // magical power - using magic find stat emoji
    fromPetScore: "<:enchanted_diamond:1375851332375810170>", // pet score bonuses
    potions: "<:awkward_potion:1395356657655087165>", // active potions
    boosterCookie: "<a:booster_cookie:1400058756183887932>", // booster cookies
  };

  // function to format value based on stat type
  const formatValue = (value, isTotal = false) => {
    if (value === 0 && !isTotal) return null;
    if (statDefinition.isPercentage) {
      const formattedValue = (
        Math.round(value * DECIMAL_PRECISION) / DECIMAL_PRECISION
      ).toFixed(MAX_FRACTION_DIGITS);
      return isTotal
        ? `${formattedValue}%`
        : `${value > 0 ? "+" : ""}${formattedValue}%`;
    }
    if (statDefinition.name === "Health" && isTotal) {
      const currentHealth =
        typeof character.current_health === "number"
          ? Math.round(character.current_health)
          : Math.round(value);
      return `${currentHealth}/${Math.round(value)}`;
    }
    const formattedValue =
      Math.round(value * DECIMAL_PRECISION) / DECIMAL_PRECISION;
    return isTotal
      ? `${formattedValue.toLocaleString()}`
      : `${value > 0 ? "+" : ""}${formattedValue}`;
  };

  // show total first
  const totalFormatted = formatValue(totalValue, true);
  breakdown.push(`**Your Total:** \`${totalFormatted}\`\n`);

  // check each source and add to breakdown if non-zero
  if (statObject.base && statObject.base !== 0) {
    const formatted = formatValue(statObject.base);
    if (formatted)
      breakdown.push(`${sourceEmojis.base} **Base:** \`${formatted}\``);
  }

  if (statObject.fromLevels && statObject.fromLevels !== 0) {
    const formatted = formatValue(statObject.fromLevels);
    if (formatted)
      breakdown.push(`${sourceEmojis.fromLevels} **Skills:** \`${formatted}\``);
  }

  if (statObject.fromSlayers && statObject.fromSlayers !== 0) {
    const formatted = formatValue(statObject.fromSlayers);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.fromSlayers} **Slayers:** \`${formatted}\``
      );
  }

  if (statObject.fromEquipment && statObject.fromEquipment !== 0) {
    const formatted = formatValue(statObject.fromEquipment);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.fromEquipment} **Equipment:** \`${formatted}\``
      );
  }

  if (statObject.fromPet && statObject.fromPet !== 0) {
    const formatted = formatValue(statObject.fromPet);
    if (formatted)
      breakdown.push(`${sourceEmojis.fromPet} **Pet:** \`${formatted}\``);
  }

  if (statObject.fromSetBonus && statObject.fromSetBonus !== 0) {
    const formatted = formatValue(statObject.fromSetBonus);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.fromSetBonus} **Set Bonus:** \`${formatted}\``
      );
  }

  if (statObject.fromAccessories && statObject.fromAccessories !== 0) {
    const formatted = formatValue(statObject.fromAccessories);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.fromAccessories} **Accessories:** \`${formatted}\``
      );
  }

  if (statObject.fromMagicalPower && statObject.fromMagicalPower !== 0) {
    const formatted = formatValue(statObject.fromMagicalPower);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.fromMagicalPower} **Magical Power:** \`${formatted}\``
      );
  }

  if (statObject.fromPetScore && statObject.fromPetScore !== 0) {
    const formatted = formatValue(statObject.fromPetScore);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.fromPetScore} **Pet Score:** \`${formatted}\``
      );
  }

  // calculate and show potion bonuses
  const potionBonus = calculatePotionBonus(character, statKey);
  if (potionBonus !== 0) {
    const formatted = formatValue(potionBonus);
    if (formatted)
      breakdown.push(`${sourceEmojis.potions} **Potions:** \`${formatted}\``);
  }

  // calculate and show booster cookie bonuses
  const boosterCookieBonus = calculateBoosterCookieBonus(character, statKey);
  if (boosterCookieBonus !== 0) {
    const formatted = formatValue(boosterCookieBonus);
    if (formatted)
      breakdown.push(
        `${sourceEmojis.boosterCookie} **Booster Cookie:** \`${formatted}\``
      );
  }

  // handle special cases
  if (statKey === "VITALITY") {
    const { checkForVitalityDoubler } = require("../utils/healingUtils");
    const hasVitalityDoubler = checkForVitalityDoubler(character);
    if (hasVitalityDoubler) {
      breakdown.push(`💊 **Vitality Doubler:** \`x2 Multiplier\``);
    }
  }

  if (breakdown.length <= 1) {
    return `🎯 **Your Total:** \`${totalFormatted}\`\n\n*No additional stat bonuses found*`;
  }

  return breakdown.join("\n");
};

// helper function to create an even more detailed breakdown (per item, per skill, etc.)
const createDetailedStatBreakdown = (character, statKey) => {
  const statDefinition = STATS[statKey];
  if (!statDefinition) return "no stat definition found.";

  const allItems = configManager.getAllItems();
  const statObject = character.stats[statKey] || {};
  const allStats = calculateAllStats(character);
  const totalValue = allStats[statKey] || 0;

  // format helper (reuse logic but simpler)
  const formatVal = (v) => {
    if (statDefinition.isPercentage) {
      const num = Math.round(v * DECIMAL_PRECISION) / DECIMAL_PRECISION;
      return `${v > 0 ? "+" : ""}${num.toFixed(MAX_FRACTION_DIGITS)}%`;
    }
    return `${v > 0 ? "+" : ""}${(Math.round(v * DECIMAL_PRECISION) / DECIMAL_PRECISION).toLocaleString()}`;
  };

  const lines = [];
  lines.push(
    `**Your Total:** \`${statDefinition.name === "Health" ? `${Math.round(character.current_health || totalValue)}/${Math.round(totalValue)}` : statDefinition.isPercentage ? `${(Math.round(totalValue * DECIMAL_PRECISION) / DECIMAL_PRECISION).toFixed(MAX_FRACTION_DIGITS)}%` : totalValue.toLocaleString()}\``
  );
  lines.push("");

  // base
  if (statObject.base)
    lines.push(
      `<:base_value:1403042580995768400> **Base:** \`${formatVal(statObject.base)}\``
    );

  // skill contributions broken out per skill
  const { skillRewards } = require("../data/skillRewards");
  const perSkill = {}; // skill -> amount
  for (const [skillName, rewardsDef] of Object.entries(skillRewards)) {
    if (!character.skills?.[skillName]) continue;
    const skillExp = character.skills[skillName].exp || 0;
    const { getLevelFromExp } = require("../utils/expFunctions");
    const level = getLevelFromExp(skillExp).level;
    for (const reward of rewardsDef.rewards || []) {
      if (reward.stat !== statKey) continue;
      const unlockedLevels = reward.levels.filter((l) => l <= level).length;
      if (unlockedLevels > 0) {
        perSkill[skillName] =
          (perSkill[skillName] || 0) + unlockedLevels * reward.value;
      }
    }
  }
  const totalSkill = Object.values(perSkill).reduce((a, b) => a + b, 0);
  if (totalSkill) {
    lines.push(
      `<:gold_ingot:1371269747664228463> **Skills Total:** \`${formatVal(totalSkill)}\``
    );
    Object.entries(perSkill)
      .sort((a, b) => b[1] - a[1])
      .forEach(([skill, val]) => {
        lines.push(
          `  • ${skill.charAt(0).toUpperCase() + skill.slice(1)}: \`${formatVal(val)}\``
        );
      });
  }

  // slayers (keep aggregated for now)
  if (statObject.fromSlayers) {
    lines.push(
      `<:revenant_horror:1389646540460658970> **Slayers:** \`${formatVal(statObject.fromSlayers)}\``
    );
  }

  // equipment + accessories per item
  const equippedItems =
    character.inventory?.equipment?.filter((e) => e.isEquipped) || [];
  const { calculateItemStats } = require("../utils/itemStatCalculator");
  const equipmentLines = [];
  equippedItems.forEach((eq) => {
    const def = allItems[eq.itemKey];
    if (!def) return;
    try {
      const calc = calculateItemStats(eq, def, character);
      const val = calc.totalStats[statKey];
      if (val) {
        equipmentLines.push({
          name: def.name || eq.itemKey,
          emoji: def.emoji || "🔸",
          val,
        });
      }
    } catch (err) {
      console.error("[stats detailed] error calculating item stats", err);
    }
  });
  if (equipmentLines.length) {
    lines.push(
      `<:diamond_sword:1390124635802173520> **Equipment & Accessories:** \`${formatVal(equipmentLines.reduce((a, b) => a + b.val, 0))}\``
    );
    equipmentLines
      .sort((a, b) => b.val - a.val)
      .forEach((it) => {
        lines.push(`  • ${it.emoji} ${it.name}: \`${formatVal(it.val)}\``);
      });
  }

  // active pet
  if (statObject.fromPet) {
    const activePet = character.pets?.find(
      (p) => p.id === character.active_pet_id
    );
    if (activePet) {
      const petDef = allItems[activePet.petKey] || {};
      lines.push(
        `<:pet_chicken:1376171013334896680> **Pet (${petDef.name || activePet.petKey || "pet"}):** \`${formatVal(statObject.fromPet)}\``
      );
    } else {
      lines.push(
        `<:pet_chicken:1376171013334896680> **Pet:** \`${formatVal(statObject.fromPet)}\``
      );
    }
  }

  // set bonus
  if (statObject.fromSetBonus) {
    lines.push(
      `<:farm_suit_helmet:1367680215253516399> **Set Bonus:** \`${formatVal(statObject.fromSetBonus)}\``
    );
    try {
      const { getActiveSetBonuses } = require("../utils/setBonuses");
      const activeSets = getActiveSetBonuses(character);
      const allItemsLocal = configManager.getAllItems();
      const perSet = [];
      for (const [setKey, data] of Object.entries(activeSets)) {
        if (!data) continue;
        const contrib = (data.statBonuses && data.statBonuses[statKey]) || 0;
        let extra = 0;
        const notes = [];
        if (statKey === "HEALTH" && data.healthPerLevel) {
          const fishingExp = character.skills?.fishing?.exp || 0;
          const { getLevelFromExp } = require("../utils/expFunctions");
          const fishingLevel = getLevelFromExp(fishingExp).level;
          const anglerHealth = data.healthPerLevel * fishingLevel;
          if (anglerHealth) {
            extra += anglerHealth;
            notes.push(`+${anglerHealth} from Fishing Lvl ${fishingLevel}`);
          }
        }
        if (
          (statKey === "HEALTH" || statKey === "DEFENSE") &&
          data.collectionBased
        ) {
          const cKey = data.collectionKey;
          const perAmount = data.perAmount;
          const maxBonus = data.maxBonus;
          const cAmt = character.collections?.[cKey] || 0;
          const collBonus = Math.min(Math.floor(cAmt / perAmount), maxBonus);
          if (collBonus) {
            extra += collBonus;
            notes.push(
              `+${collBonus} from ${cKey} Collection (${cAmt.toLocaleString()})`
            );
          }
        }
        const total = contrib + extra;
        if (total) {
          let displayName = setKey.charAt(0).toUpperCase() + setKey.slice(1);
          const firstItemKey = data.itemKeys ? data.itemKeys[0] : null;
          let emoji = "";
          if (firstItemKey && allItemsLocal[firstItemKey]) {
            emoji = allItemsLocal[firstItemKey].emoji || "";
            if (allItemsLocal[firstItemKey].name)
              displayName = allItemsLocal[firstItemKey].name.split(" ")[0];
          }
          perSet.push(
            `  • ${emoji} ${displayName} (${data.pieces || 0}pc): \`${formatVal(total)}\`${notes.length ? ` *(${notes.join("; ")})*` : ""}`
          );
        }
      }
      if (perSet.length) lines.push(...perSet);
    } catch (err) {
      console.error("[stats detailed] set bonus breakdown error", err);
    }
  }

  // magical power
  if (statObject.fromMagicalPower) {
    lines.push(
      `<:stat_magic_find:1399855928894951425> **Magical Power:** \`${formatVal(statObject.fromMagicalPower)}\``
    );
  }

  // pet score
  if (statObject.fromPetScore) {
    lines.push(
      `<:enchanted_diamond:1375851332375810170> **Pet score:** \`${formatVal(statObject.fromPetScore)}\``
    );
  }

  // potion effects individual (handle God Potion specially)
  {
    const {
      isGodPotionActive,
      computeGodPotionAggregatedEffects,
    } = require("../utils/potionEffects");
    if (isGodPotionActive(character)) {
      const aggregated = computeGodPotionAggregatedEffects(
        require("../utils/configManager")
      );
      const amount = aggregated.effects[statKey] || 0;
      if (amount) {
        lines.push(
          `<:God_Potion:1403164753672536205> **God Potion:** \`${formatVal(amount)}\``
        );
      }
    } else if (character.activeEffects) {
      const now = Date.now();
      const potionEffects = Object.values(character.activeEffects).filter(
        (e) => e.expiresAt > now && e.effects && e.effects[statKey]
      );
      const totalPotion = potionEffects.reduce(
        (a, e) => a + (e.effects[statKey] || 0),
        0
      );
      if (totalPotion) {
        lines.push(
          `<:awkward_potion:1395356657655087165> **Potions Total:** \`${formatVal(totalPotion)}\``
        );
        potionEffects.forEach((e) => {
          const amount = e.effects[statKey];
          lines.push(`  • ${e.name || "potion"}: \`${formatVal(amount)}\``);
        });
      }
    }
  }

  // booster cookie
  const {
    isBoosterCookieActive,
    getBoosterCookieStatBonuses,
  } = require("../utils/boosterCookieManager");
  if (isBoosterCookieActive(character)) {
    const boosterBonuses = getBoosterCookieStatBonuses();
    if (boosterBonuses[statKey]) {
      lines.push(
        `<a:booster_cookie:1400058756183887932> **Booster Cookie:** \`${formatVal(boosterBonuses[statKey])}\``
      );
    }
  }

  if (statKey === "VITALITY") {
    const { checkForVitalityDoubler } = require("../utils/healingUtils");
    if (checkForVitalityDoubler(character)) {
      lines.push(
        `**Vitality Doubling Item(placeholder lol hi):** \`x2 multiplier\``
      );
    }
  }

  return lines.join("\n");
};

// helper function to create navigation buttons
const createNavigationButtons = (page, totalPages, prefix = "") => {
  const prevButton = new ButtonBuilder()
    .setCustomId(`${prefix}prev${prefix ? "_" : ""}${page}`)
    .setLabel("Previous")
    .setStyle(ButtonStyle.Primary)
    .setDisabled(page === INITIAL_PAGE);

  const nextButton = new ButtonBuilder()
    .setCustomId(`${prefix}next${prefix ? "_" : ""}${page}`)
    .setLabel("Next")
    .setStyle(ButtonStyle.Primary)
    .setDisabled(page >= totalPages - MIN_PAGE_COUNT);

  return new ActionRowBuilder().addComponents(prevButton, nextButton);
};

module.exports = {
  data: new SlashCommandBuilder()
    .setName("stats")
    .setDescription("View your character stats")
    .addStringOption((option) =>
      option
        .setName("type")
        .setDescription("Type of stats to view")
        .addChoices(
          { name: "slayers", value: "slayers" },
          { name: "kills", value: "kills" }
        )
        .setRequired(false)
    ),
  async execute(interaction) {
    try {
      const statsType = interaction.options.getString("type");

      if (statsType === "slayers") {
        return await this.handleSlayersSubcommand(interaction);
      } else if (statsType === "kills") {
        return await this.handleKillsSubcommand(interaction);
      }

      // Default behavior: show general stats
      return await this.handleGeneralStatsSubcommand(interaction);
    } catch (error) {
      console.error("[Stats Command Error]", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "[STA-EXE-ERR] An unexpected error occurred. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      } else {
        await interaction
          .followUp({
            content:
              "[STA-EXE-ERR] An unexpected error occurred. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    }
  },

  async handleGeneralStatsSubcommand(interaction) {
    if (!interaction.user || !interaction.user.id) {
      return interaction.reply({
        content:
          "[STA-NULL-USER] User information is missing. Please report this error code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    let character;
    try {
      character = await getPlayerData(interaction.user.id);
    } catch {
      return interaction.reply({
        content: "[STA-DAT-ERR] Please report this error code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!character) {
      return interaction.reply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!checkRankPermission(character, "MEMBER")) {
      return interaction.reply({
        content: "You don't have permission to use this command (Rank Error).",
        flags: [MessageFlags.Ephemeral],
      });
    }

    await recalculateAndSaveStats(interaction.user.id, character);

    const { checkAndNotifyDisblockXP } = require("../utils/disblockXpSystem");
    await checkAndNotifyDisblockXP(interaction.user.id, interaction);

    // character = await getPlayerData(interaction.user.id);

    const debugStats = ["STRENGTH", "CRIT_CHANCE", "CRIT_DAMAGE"];
    debugStats.forEach((statKey) => {
      const statObj = character.stats[statKey];
      if (statObj) {
        // Debug stat processing if needed
      }
    });

    const allStats = calculateAllStats(character);

    // debugStats.forEach(() => {
    // });

    // Define wisdom stats to show on page 2
    const wisdomStats = [
      "FARMING_WISDOM",
      "MINING_WISDOM",
      "FORAGING_WISDOM",
      "FISHING_WISDOM",
      "COMBAT_WISDOM",
      "ENCHANTING_WISDOM",
      "ALCHEMY_WISDOM",
      "TAMING_WISDOM",
      "CRAFTING_WISDOM",
    ];

    let maxNameLen = INITIAL_LENGTH;
    let maxValueLen = INITIAL_LENGTH;
    let maxBonusLen = INITIAL_LENGTH;

    /**
     * Stats Display System:
     * - Left number: Total combined value (permanent + temporary)
     * - Right number in parentheses: Just the temporary bonuses
     *
     * Permanent sources: base stats, levels, pet score bonuses
     * Temporary sources: equipment, active pets, set bonuses, accessories, potions
     *
     * Example: "Defense: 187 (+161.0)" means 187 total defense with 161 from temporary sources
     */

    // Filter out wisdom stats for page 1 (general stats)
    const statData = Object.keys(STATS)
      .filter((statKey) => !wisdomStats.includes(statKey)) // Remove wisdom stats from page 1
      .map((statKey) => {
        const statDefinition = STATS[statKey];
        if (!statDefinition) return null;

        if (statDefinition.hideFromDisplay) return null;

        if (statKey === "DAMAGE") return null;

        const playerStatObject = character.stats[statKey];

        const totalValue = allStats[statKey];

        // Calculate potion effects for this stat
        const potionBonus = calculatePotionBonus(character, statKey);

        // Calculate booster cookie bonuses for this stat
        const boosterCookieBonus = calculateBoosterCookieBonus(
          character,
          statKey
        );

        const temporaryValue = calculateTemporaryValue(
          playerStatObject,
          potionBonus,
          boosterCookieBonus
        );

        let bonus = temporaryValue;

        // Special handling for vitality doubler effect
        if (statKey === "VITALITY") {
          const { checkForVitalityDoubler } = require("../utils/healingUtils");
          const hasVitalityDoubler = checkForVitalityDoubler(character);
          if (hasVitalityDoubler) {
            // Calculate base vitality (without doubler effect)
            const baseVitality =
              (playerStatObject?.base ?? 0) +
              (playerStatObject?.fromLevels ?? 0);
            const vitalityDoubleBonus = baseVitality + temporaryValue; // The amount added by doubling
            bonus = temporaryValue + vitalityDoubleBonus;
          }
        }

        if (statKey === "DAMAGE" && character.derivedStats) {
          if (character.derivedStats.baseWeaponDamage !== undefined) {
            const baseWeaponDamage = character.derivedStats.baseWeaponDamage;
            const enhancementBonus =
              character.derivedStats.enhancementBonus || 0;
            const totalEquipmentBonus = baseWeaponDamage + enhancementBonus;

            bonus = totalEquipmentBonus;
          }
        }

        const { valuePart, bonusPart } = formatStatValue(
          statDefinition,
          totalValue,
          bonus,
          character
        );

        maxNameLen = Math.max(maxNameLen, statDefinition.name.length);
        maxValueLen = Math.max(maxValueLen, valuePart.length);
        maxBonusLen = Math.max(maxBonusLen, bonusPart.length);

        return {
          emoji: statDefinition.emoji || "❓",
          nameText: statDefinition.name,
          valuePart,
          bonusPart,
          statKey: statKey,
        };
      })
      .filter((s) => s !== null);

    const statsDisplay = statData
      .map(({ emoji, nameText, valuePart, bonusPart }) => {
        const namePadding = " ".repeat(maxNameLen - nameText.length);
        const valuePadding = " ".repeat(maxValueLen - valuePart.length);
        const bonusPadding = " ".repeat(maxBonusLen - bonusPart.length);

        return `${emoji} \`${nameText}${namePadding}  ${valuePadding}${valuePart}  ${bonusPart}${bonusPadding}\``;
      })
      .join("\n");

    // Create wisdom stats data for page 2
    let maxWisdomNameLen = INITIAL_LENGTH;
    let maxWisdomValueLen = INITIAL_LENGTH;
    let maxWisdomBonusLen = INITIAL_LENGTH;

    const wisdomStatData = wisdomStats
      .map((statKey) => {
        const statDefinition = STATS[statKey];
        if (!statDefinition) return null;

        if (statDefinition.hideFromDisplay) return null;

        const playerStatObject = character.stats[statKey];

        // Calculate permanent value (base + levels + pet score bonuses)
        const permanentValue =
          (playerStatObject?.base ?? 0) +
          (playerStatObject?.fromLevels ?? 0) +
          (playerStatObject?.fromPetScore ?? 0);

        // Calculate potion effects for this stat
        const potionBonus = calculatePotionBonus(character, statKey);

        // Calculate booster cookie bonuses for this stat
        const boosterCookieBonus = calculateBoosterCookieBonus(
          character,
          statKey
        );

        const temporaryValue = calculateTemporaryValue(
          playerStatObject,
          potionBonus,
          boosterCookieBonus
        );

        const bonus = temporaryValue;
        const totalValue = permanentValue + temporaryValue;

        const { valuePart, bonusPart } = formatStatValue(
          statDefinition,
          totalValue,
          bonus,
          character
        );

        maxWisdomNameLen = Math.max(
          maxWisdomNameLen,
          statDefinition.name.length
        );
        maxWisdomValueLen = Math.max(maxWisdomValueLen, valuePart.length);
        maxWisdomBonusLen = Math.max(maxWisdomBonusLen, bonusPart.length);

        return {
          emoji: statDefinition.emoji || "❓",
          nameText: statDefinition.name,
          valuePart,
          bonusPart,
          statKey: statKey,
        };
      })
      .filter((s) => s !== null);

    const wisdomDisplay = wisdomStatData
      .map(({ emoji, nameText, valuePart, bonusPart }) => {
        const namePadding = " ".repeat(maxWisdomNameLen - nameText.length);
        const valuePadding = " ".repeat(maxWisdomValueLen - valuePart.length);
        const bonusPadding = " ".repeat(maxWisdomBonusLen - bonusPart.length);

        return `${emoji} \`${nameText}${namePadding}  ${valuePadding}${valuePart}  ${bonusPart}${bonusPadding}\``;
      })
      .join("\n");

    const { calculateMaxActionAmount } = require("../utils/skillLimits");
    const { SKILLS } = require("../gameConfig");

    // Calculate max actions for each skill (excluding crafting, taming, enchanting)
    const skillActions = {};
    const excludedSkills = ["crafting", "taming", "enchanting"];
    SKILLS.LIST.filter((skill) => !excludedSkills.includes(skill)).forEach(
      (skill) => {
        const skillExp = character.skills[skill]?.exp || 0;
        const level = getLevelFromExp(skillExp).level;
        skillActions[skill] = calculateMaxActionAmount(level);
      }
    );

    // Import skillEmojis from gameConfig
    const { skillEmojis } = require("../gameConfig");

    // Create action stats display
    let maxSkillLen = 0;
    let maxActionLen = 0;
    const actionData = SKILLS.LIST.filter(
      (skill) => !excludedSkills.includes(skill)
    ).map((skill) => {
      const skillName = skill.charAt(0).toUpperCase() + skill.slice(1);
      const actions = skillActions[skill];
      maxSkillLen = Math.max(maxSkillLen, skillName.length);
      maxActionLen = Math.max(maxActionLen, actions.toString().length);
      return { emoji: skillEmojis[skill] || "❓", name: skillName, actions };
    });

    const actionsDisplay = actionData
      .map(({ emoji, name, actions }) => {
        const namePadding = " ".repeat(maxSkillLen - name.length);
        const actionPadding = " ".repeat(
          maxActionLen - actions.toString().length
        );
        return `${emoji} \`${name} Actions${namePadding}                ${actionPadding}${actions}\``;
      })
      .join("\n");

    //const rankPrefix = getRankPrefix(character);
    const title = `${character.name}'s Stats`;

    // Create embeds
    const pages = [
      new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(title)
        .setDescription(statsDisplay)
        .setFooter({ text: "Page 1/3" }),
      new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(title)
        .setDescription(wisdomDisplay)
        .setFooter({ text: "Page 2/3" }),
      new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(title)
        .setDescription(actionsDisplay)
        .setFooter({ text: `Page 3/${PAGE_COUNT}` }),
    ];

    let currentPage = INITIAL_PAGE;

    // Function to create select menu based on current page
    const createSelectMenu = (page) => {
      if (page === 0) {
        // General stats page
        const validStats = statData.filter(
          (stat) => stat.statKey && stat.nameText
        );
        if (validStats.length === 0) return null;

        return new StringSelectMenuBuilder()
          .setCustomId("stat_description")
          .setPlaceholder("Select a stat to learn about...")
          .setOptions(
            validStats.map((stat) => ({
              label: stat.nameText,
              value: stat.statKey,
              emoji: stat.emoji,
            }))
          );
      } else if (page === 1) {
        // Wisdom stats page
        const validWisdomStats = wisdomStatData.filter(
          (stat) => stat.statKey && stat.nameText
        );
        if (validWisdomStats.length === 0) return null;

        return new StringSelectMenuBuilder()
          .setCustomId("stat_description")
          .setPlaceholder("Select a wisdom stat to learn about...")
          .setOptions(
            validWisdomStats.map((stat) => ({
              label: stat.nameText,
              value: stat.statKey,
              emoji: stat.emoji,
            }))
          );
      }
      return null; // No select menu for actions page
    };

    const createComponents = (page) => {
      const buttonRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId("prev")
          .setLabel("Prev")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === INITIAL_PAGE),
        new ButtonBuilder()
          .setCustomId("next")
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === pages.length - 1)
      );

      const selectMenu = createSelectMenu(page);
      if (selectMenu) {
        const selectRow = new ActionRowBuilder().addComponents(selectMenu);
        return [selectRow, buttonRow];
      }
      return [buttonRow];
    };

    try {
      const initialComponents = createComponents(currentPage);
      const reply = await interaction.reply({
        embeds: [pages[currentPage]],
        components: initialComponents,
        fetchReply: true,
      });

      const collector = reply.createMessageComponentCollector({
        time: COLLECTOR_TIMEOUT_MS,
      });

      collector.on("collect", async (i) => {
        if (i.user.id !== interaction.user.id) return;

        if (i.customId === "prev") {
          currentPage--;
        } else if (i.customId === "next") {
          currentPage++;
        } else if (i.customId === "stat_description") {
          // Handle stat description selection
          const { STAT_DESCRIPTIONS } = require("../data/statDescriptions");
          const selectedStat = i.values[0];
          const statInfo = STAT_DESCRIPTIONS[selectedStat];

          if (statInfo) {
            // Get the actual stat emoji from STATS config
            const statDefinition = STATS[selectedStat];
            const statEmoji = statDefinition?.emoji || "📊";

            // create the stat breakdown
            const breakdown = createStatBreakdown(character, selectedStat);

            // create description with both stat info and breakdown
            const description = `**${statInfo.description}**\n\n**Stat Sources:**\n${breakdown}`;

            const statEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.GREEN)
              .setTitle(`${statEmoji} ${statInfo.title}`)
              .setDescription(description);

            const buttons = new ActionRowBuilder().addComponents(
              new ButtonBuilder()
                .setCustomId("back_to_stats")
                .setLabel("Back to Stats")
                .setStyle(ButtonStyle.Primary),
              new ButtonBuilder()
                .setCustomId(`expand_${selectedStat}`)
                .setLabel("More Details")
                .setStyle(ButtonStyle.Secondary)
            );

            await i.update({ embeds: [statEmbed], components: [buttons] });
            return;
          }
        } else if (i.customId.startsWith("expand_")) {
          const statKey = i.customId.replace("expand_", "");
          const { STAT_DESCRIPTIONS } = require("../data/statDescriptions");
          const statInfo = STAT_DESCRIPTIONS[statKey];
          const statDefinition = STATS[statKey];
          if (statInfo && statDefinition) {
            const statEmoji = statDefinition?.emoji || "📊";
            const detailed = createDetailedStatBreakdown(character, statKey);
            const description = `**${statInfo.description}**\n\n**Detailed Stat Sources:**\n${detailed}`;
            const detailedEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.GREEN)
              .setTitle(`${statEmoji} ${statInfo.title}: Detailed`)
              .setDescription(description);
            const buttons = new ActionRowBuilder().addComponents(
              new ButtonBuilder()
                .setCustomId("back_to_stats")
                .setLabel("Back to Stats")
                .setStyle(ButtonStyle.Primary),
              new ButtonBuilder()
                .setCustomId(`collapse_${statKey}`)
                .setLabel("Less Details")
                .setStyle(ButtonStyle.Secondary)
            );
            await i.update({ embeds: [detailedEmbed], components: [buttons] });
            return;
          }
        } else if (i.customId.startsWith("collapse_")) {
          const statKey = i.customId.replace("collapse_", "");
          const { STAT_DESCRIPTIONS } = require("../data/statDescriptions");
          const statInfo = STAT_DESCRIPTIONS[statKey];
          if (statInfo) {
            const statDefinition = STATS[statKey];
            const statEmoji = statDefinition?.emoji || "📊";
            const breakdown = createStatBreakdown(character, statKey);
            const description = `**${statInfo.description}**\n\n**Stat Sources:**\n${breakdown}`;
            const statEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.GREEN)
              .setTitle(`${statEmoji} ${statInfo.title}`)
              .setDescription(description);
            const buttons = new ActionRowBuilder().addComponents(
              new ButtonBuilder()
                .setCustomId("back_to_stats")
                .setLabel("Back to Stats")
                .setStyle(ButtonStyle.Primary),
              new ButtonBuilder()
                .setCustomId(`expand_${statKey}`)
                .setLabel("More Details")
                .setStyle(ButtonStyle.Secondary)
            );
            await i.update({ embeds: [statEmbed], components: [buttons] });
            return;
          }
        } else if (i.customId === "back_to_stats") {
          // Return to main stats view
          const components = createComponents(currentPage);
          await i.update({ embeds: [pages[currentPage]], components });
          return;
        }

        const components = createComponents(currentPage);
        await i.update({ embeds: [pages[currentPage]], components });
      });

      collector.on("end", () => {
        // Keep components enabled even after timeout - user requested always reusable
        // No need to disable components
      });
    } catch (replyError) {
      console.error(
        `[Stats Command] Error sending embed reply for ${interaction.user.id}:`,
        replyError
      );
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "[STA-RPL-ERR] An error occurred trying to display stats. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      } else {
        await interaction
          .followUp({
            content:
              "[STA-RPL-ERR] An error occurred trying to display stats. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    }

    if (interaction.channel && interaction.channel.id) {
      recordActivityForAnnouncements(interaction.channel.id);
    } else {
      console.warn(
        "[Stats Command] Skipping recordActivityForAnnouncements: interaction.channel or interaction.channel.id is null."
      );
    }
  },

  async handleSlayersSubcommand(interaction) {
    try {
      if (!interaction.user || !interaction.user.id) {
        return interaction.reply({
          content:
            "[STA-NULL-USER] User information is missing. Please report this error code to an Admin.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      let character;
      try {
        character = await getPlayerData(interaction.user.id);
      } catch {
        return interaction.reply({
          content: "[STA-DAT-ERR] Please report this error code to an Admin.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      if (!character) {
        return interaction.reply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.reply({
          content:
            "You don't have permission to use this command (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Update Disblock XP based on current progress
      try {
        const {
          checkAndNotifyDisblockXP,
        } = require("../utils/disblockXpSystem");
        await checkAndNotifyDisblockXP(interaction.user.id, interaction);
      } catch (xpError) {
        console.error("[Stats Slayers] Error updating Disblock XP:", xpError);
      }

      // Get slayer levels for display
      const { getAllSlayerLevels } = require("../utils/slayerLevelUtils");
      const slayerXpData = character.slayerXp || {};
      const slayerLevels = getAllSlayerLevels(slayerXpData);

      // Import progress bar function
      const { createProgressBar } = require("../utils/displayUtils");
      const { getFormattedRewardsText } = require("../utils/slayerRewards");

      // Calculate slayer stats display
      const zombieLevel = slayerLevels.zombie;
      const spiderLevel = slayerLevels.spider;

      // Create progress bars with XP text
      const zombieProgressBar = createProgressBar(
        zombieLevel.currentLevelExp,
        zombieLevel.requiredExpForNextLevel,
        {
          size: PROGRESS_BAR_SIZE,
          showXpText: true,
          formatFn: formatNumber,
          useEmojis: true,
        }
      );

      const spiderProgressBar = createProgressBar(
        spiderLevel.currentLevelExp,
        spiderLevel.requiredExpForNextLevel,
        {
          size: PROGRESS_BAR_SIZE,
          showXpText: true,
          formatFn: formatNumber,
          useEmojis: true,
        }
      );

      // Get formatted rewards for each slayer
      const zombieRewards = getFormattedRewardsText(
        "zombie",
        zombieLevel.level
      );
      const spiderRewards = getFormattedRewardsText(
        "spider",
        spiderLevel.level
      );

      // Format current rewards text
      const formatCurrentRewards = (rewards) => {
        if (rewards.currentRewards.length === 0) {
          return "*No rewards yet*";
        }
        return rewards.currentRewards.join("\n");
      };

      // Format next rewards text
      const formatNextRewards = (rewards) => {
        if (rewards.nextRewards.length === 0) {
          return "*No upcoming rewards*";
        }
        return rewards.nextRewards.join("\n");
      };

      // Create fields for slayer display with proper columns
      const slayerFields = [
        // Zombie Slayer Progress
        {
          name: `<:revenant_horror:1389646540460658970> Zombie Slayer (Level ${zombieLevel.level})`,
          value: zombieProgressBar,
          inline: true,
        },
        // Spider Slayer Progress
        {
          name: `<:mob_spider:1370526342927618078> Spider Slayer (Level ${spiderLevel.level})`,
          value: spiderProgressBar,
          inline: true,
        },
        // Empty spacer
        {
          name: "\u200b",
          value: "\u200b",
          inline: true,
        },
        // Zombie Current Rewards
        {
          name: "<:revenant_horror:1389646540460658970> Current Rewards",
          value: formatCurrentRewards(zombieRewards),
          inline: true,
        },
        // Spider Current Rewards
        {
          name: "<:mob_spider:1370526342927618078> Current Rewards",
          value: formatCurrentRewards(spiderRewards),
          inline: true,
        },
        // Empty spacer
        {
          name: "\u200b",
          value: "\u200b",
          inline: true,
        },
        // Zombie Next Rewards
        {
          name: "<:revenant_horror:1389646540460658970> Next Reward(s)",
          value: formatNextRewards(zombieRewards),
          inline: true,
        },
        // Spider Next Rewards
        {
          name: "<:mob_spider:1370526342927618078> Next Reward(s)",
          value: formatNextRewards(spiderRewards),
          inline: true,
        },
        // Empty spacer
        {
          name: "\u200b",
          value: "\u200b",
          inline: true,
        },
      ];

      const rankPrefix = getRankPrefix(character);
      const title = `${rankPrefix}${character.name}'s Slayer Stats`;

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(title)
        .setDescription("Your slayer progress and rewards")
        .addFields(slayerFields);

      // Create select menu for detailed slayer views
      const selectMenuOptions = [
        {
          label: `Zombie Slayer (Level ${zombieLevel.level})`,
          value: "zombie",
          emoji: "<:revenant_horror:1389646540460658970>",
          description: `View detailed Zombie Slayer rewards and progress`,
        },
        {
          label: `Spider Slayer (Level ${spiderLevel.level})`,
          value: "spider",
          emoji: "<:mob_spider:1370526342927618078>",
          description: `View detailed Spider Slayer rewards and progress`,
        },
      ];

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId("slayers_select")
        .setPlaceholder("Select a slayer to view detailed rewards")
        .addOptions(selectMenuOptions);

      const components = [new ActionRowBuilder().addComponents(selectMenu)];

      embed.setFooter({
        text: "Use /talk to Maddox The Slayer to start quests",
      });

      await interaction.reply({ embeds: [embed], components });

      if (interaction.channel && interaction.channel.id) {
        recordActivityForAnnouncements(interaction.channel.id);
      }
    } catch (error) {
      console.error("[Stats Slayers Command Error]", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "[STA-SLY-ERR] An unexpected error occurred. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    }
  },

  // Handle slayer select menu interactions
  async handleSlayerSelectMenu(interaction) {
    try {
      const selectedSlayerType = interaction.values[0];

      await interaction.deferUpdate();

      const character = await getPlayerData(interaction.user.id);

      if (!character) {
        return interaction.followUp({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      await this.showDetailedSlayerView(
        interaction,
        selectedSlayerType,
        character
      );
    } catch (error) {
      console.error("[Stats Slayers] Error in handleSlayerSelectMenu:", error);
      await interaction.followUp({
        content: "An error occurred while processing your request.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },

  // Show detailed slayer view
  async showDetailedSlayerView(interaction, slayerType, character) {
    try {
      const { getAllSlayerLevels } = require("../utils/slayerLevelUtils");
      const { createProgressBar } = require("../utils/displayUtils");
      const { getMaxRewardLevel } = require("../utils/slayerRewards");

      const slayerXpData = character.slayerXp || {};
      const slayerLevels = getAllSlayerLevels(slayerXpData);
      const currentSlayerLevel = slayerLevels[slayerType];
      const maxRewardLevel = getMaxRewardLevel(slayerType);

      // Create progress bar with XP text
      const progressBar = createProgressBar(
        currentSlayerLevel.currentLevelExp,
        currentSlayerLevel.requiredExpForNextLevel,
        {
          size: PROGRESS_BAR_SIZE,
          showXpText: true,
          formatFn: formatNumber,
          useEmojis: true,
        }
      );

      // Get slayer emojis and names
      const slayerInfo = {
        zombie: {
          emoji: "<:revenant_horror:1389646540460658970>",
          name: "Zombie Slayer",
          displayName: "Zombie",
        },
        spider: {
          emoji: "<:mob_spider:1370526342927618078>",
          name: "Spider Slayer",
          displayName: "Spider",
        },
      };

      const slayerEmoji = slayerInfo[slayerType].emoji;
      const slayerName = slayerInfo[slayerType].name;
      const slayerDisplayName = slayerInfo[slayerType].displayName;

      const rankPrefix = getRankPrefix(character);
      const title = `${slayerEmoji} ${rankPrefix}${character.name}'s ${slayerName} Rewards`;

      // Create detailed progress display
      const progressDisplay = `${slayerEmoji} **${slayerName}** (**Level ${currentSlayerLevel.level}**)\n${progressBar}`;

      // Get all rewards for all levels
      let rewardsDescription = `${progressDisplay}\n\n**All Rewards:**\n\n`;

      if (maxRewardLevel === 0) {
        rewardsDescription += `*${slayerDisplayName} Slayer rewards coming soon!*`;
      } else {
        // Show rewards for each level
        for (
          let level = 1;
          level <= Math.max(maxRewardLevel, currentSlayerLevel.level);
          level++
        ) {
          if (level <= maxRewardLevel) {
            const isUnlocked = currentSlayerLevel.level >= level;
            const statusEmoji = isUnlocked ? "✅" : "🔒";

            rewardsDescription += `${statusEmoji} **Level ${level}**\n`;

            if (level <= maxRewardLevel) {
              // Get rewards specifically for this level
              const {
                getStatRewardsForLevel,
                getItemRewardsForLevel,
                getCraftingRewardsForLevel,
              } = require("../utils/slayerRewards");
              const statRewards = getStatRewardsForLevel(slayerType, level);
              const itemRewards = getItemRewardsForLevel(slayerType, level);
              const craftingRewards = getCraftingRewardsForLevel(
                slayerType,
                level
              );

              if (
                statRewards.length > 0 ||
                itemRewards.length > 0 ||
                craftingRewards.length > 0
              ) {
                const { getStatEmoji } = require("../utils/slayerRewards");

                // Display stat rewards
                for (const reward of statRewards) {
                  const emoji = getStatEmoji(reward.stat);
                  const statName = reward.stat
                    .replace("_", " ")
                    .toLowerCase()
                    .replace(/\b\w/g, (l) => l.toUpperCase());
                  rewardsDescription += `  ${emoji} **+${reward.value}** ${statName}\n`;
                }

                // Display item rewards
                for (const item of itemRewards) {
                  rewardsDescription += `  ${item.emoji} **+${item.amount}** ${item.name}\n`;
                }

                // Display crafting recipe rewards
                for (const recipe of craftingRewards) {
                  rewardsDescription += `  ${recipe.emoji} ${recipe.name} Recipe\n`;
                }
              }
            }
            rewardsDescription += "\n";
          }
        }
      }

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(title)
        .setDescription(rewardsDescription);

      // Create back button and select menu for other slayers
      const backButton = new ButtonBuilder()
        .setCustomId("slayers_back")
        .setLabel("← Back to Slayers")
        .setStyle(ButtonStyle.Secondary);

      const selectMenuOptions = [
        {
          label: `Zombie Slayer (Level ${slayerLevels.zombie.level})`,
          value: "zombie",
          emoji: "<:revenant_horror:1389646540460658970>",
          description: `View detailed Zombie Slayer rewards`,
        },
        {
          label: `Spider Slayer (Level ${slayerLevels.spider.level})`,
          value: "spider",
          emoji: "<:mob_spider:1370526342927618078>",
          description: `View detailed Spider Slayer rewards`,
        },
      ];

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId("slayers_select")
        .setPlaceholder("Select another slayer to view rewards")
        .addOptions(selectMenuOptions);

      const components = [
        new ActionRowBuilder().addComponents(backButton),
        new ActionRowBuilder().addComponents(selectMenu),
      ];

      await interaction.editReply({ embeds: [embed], components });
    } catch (error) {
      console.error("[Stats Slayers] Error in showDetailedSlayerView:", error);
      await interaction.followUp({
        content: "An error occurred while displaying slayer details.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },

  // Handle back button
  async handleSlayerBackButton(interaction) {
    try {
      await interaction.deferUpdate();

      const character = await getPlayerData(interaction.user.id);

      if (!character) {
        return interaction.followUp({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Re-show the main slayers view
      await this.showMainSlayersView(interaction, character);
    } catch (error) {
      console.error("[Stats Slayers] Error in handleSlayerBackButton:", error);
      await interaction.followUp({
        content: "An error occurred while going back.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },

  // Show main slayers view (used by back button)
  async showMainSlayersView(interaction, character) {
    const { getAllSlayerLevels } = require("../utils/slayerLevelUtils");
    const { createProgressBar } = require("../utils/displayUtils");
    const { getFormattedRewardsText } = require("../utils/slayerRewards");

    const slayerXpData = character.slayerXp || {};
    const slayerLevels = getAllSlayerLevels(slayerXpData);

    const zombieLevel = slayerLevels.zombie;
    const spiderLevel = slayerLevels.spider;

    // Create progress bars with XP text
    const zombieProgressBar = createProgressBar(
      zombieLevel.currentLevelExp,
      zombieLevel.requiredExpForNextLevel,
      {
        size: PROGRESS_BAR_SIZE,
        showXpText: true,
        formatFn: formatNumber,
        useEmojis: true,
      }
    );

    const spiderProgressBar = createProgressBar(
      spiderLevel.currentLevelExp,
      spiderLevel.requiredExpForNextLevel,
      {
        size: PROGRESS_BAR_SIZE,
        showXpText: true,
        formatFn: formatNumber,
        useEmojis: true,
      }
    );

    // Get formatted rewards for each slayer
    const zombieRewards = getFormattedRewardsText("zombie", zombieLevel.level);
    const spiderRewards = getFormattedRewardsText("spider", spiderLevel.level);

    // Format current rewards text
    const formatCurrentRewards = (rewards) => {
      if (rewards.currentRewards.length === 0) {
        return "*No rewards yet*";
      }
      return rewards.currentRewards.join("\n");
    };

    // Format next rewards text
    const formatNextRewards = (rewards) => {
      if (rewards.nextRewards.length === 0) {
        return "*No upcoming rewards*";
      }
      return rewards.nextRewards.join("\n");
    };

    // Create fields for slayer display with proper columns
    const slayerFields = [
      // Zombie Slayer Progress
      {
        name: `<:revenant_horror:1389646540460658970> Zombie Slayer (Level ${zombieLevel.level})`,
        value: zombieProgressBar,
        inline: true,
      },
      // Spider Slayer Progress
      {
        name: `<:mob_spider:1370526342927618078> Spider Slayer (Level ${spiderLevel.level})`,
        value: spiderProgressBar,
        inline: true,
      },
      // Empty spacer
      {
        name: "\u200b",
        value: "\u200b",
        inline: true,
      },
      // Zombie Current Rewards
      {
        name: "<:revenant_horror:1389646540460658970> Current Rewards",
        value: formatCurrentRewards(zombieRewards),
        inline: true,
      },
      // Spider Current Rewards
      {
        name: "<:mob_spider:1370526342927618078> Current Rewards",
        value: formatCurrentRewards(spiderRewards),
        inline: true,
      },
      // Empty spacer
      {
        name: "\u200b",
        value: "\u200b",
        inline: true,
      },
      // Zombie Next Rewards
      {
        name: "<:revenant_horror:1389646540460658970> Next Reward(s)",
        value: formatNextRewards(zombieRewards),
        inline: true,
      },
      // Spider Next Rewards
      {
        name: "<:mob_spider:1370526342927618078> Next Reward(s)",
        value: formatNextRewards(spiderRewards),
        inline: true,
      },
      // Empty spacer
      {
        name: "\u200b",
        value: "\u200b",
        inline: true,
      },
    ];

    const rankPrefix = getRankPrefix(character);
    const title = `${rankPrefix}${character.name}'s Slayer Stats`;

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle(title)
      .setDescription("Your slayer progress and rewards")
      .addFields(slayerFields);

    // Create select menu for detailed slayer views
    const selectMenuOptions = [
      {
        label: `Zombie Slayer (Level ${zombieLevel.level})`,
        value: "zombie",
        emoji: "<:revenant_horror:1389646540460658970>",
        description: `View detailed Zombie Slayer rewards and progress`,
      },
      {
        label: `Spider Slayer (Level ${spiderLevel.level})`,
        value: "spider",
        emoji: "<:mob_spider:1370526342927618078>",
        description: `View detailed Spider Slayer rewards and progress`,
      },
    ];

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("slayers_select")
      .setPlaceholder("Select a slayer to view detailed rewards")
      .addOptions(selectMenuOptions);

    const components = [new ActionRowBuilder().addComponents(selectMenu)];

    embed.setFooter({ text: "Use /talk to Maddox The Slayer to start quests" });

    await interaction.editReply({ embeds: [embed], components });
  },

  async handleKillsSubcommand(interaction) {
    if (!interaction.user || !interaction.user.id) {
      return interaction.reply({
        content:
          "[STA-NULL-USER] User information is missing. Please report this error code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    let character;
    try {
      character = await getPlayerData(interaction.user.id);
    } catch {
      return interaction.reply({
        content: "[STA-DAT-ERR] Please report this error code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!character) {
      return interaction.reply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!checkRankPermission(character, "MEMBER")) {
      return interaction.reply({
        content: "You don't have permission to use this command (Rank Error).",
        flags: [MessageFlags.Ephemeral],
      });
    }

    await this.showMobKillsView(interaction, character, INITIAL_PAGE);
  },

  async createMobKillsEmbed(
    character,
    page,
    totalPages,
    sortedKills,
    itemsPerPage = KILLS_PAGE_SIZE
  ) {
    const { calculateDisblockLevel } = require("../utils/disblockXpSystem");

    // Get Disblock level for title
    const disblockXP = character.disblock_xp || 0;
    const { level: disblockLevel } = calculateDisblockLevel(disblockXP);

    // Calculate page boundaries
    const currentPage = Math.max(0, Math.min(page, totalPages - 1));
    const startIndex = currentPage * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, sortedKills.length);
    const displayedKills = sortedKills.slice(startIndex, endIndex);

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle(`[${disblockLevel}] ${character.name}'s Mob Kills`)
      .setFooter({
        text: `Page ${currentPage + 1}/${totalPages} • Total Unique Mobs: ${sortedKills.length}`,
      });

    // Display mob details in a visual format
    if (displayedKills.length > 0) {
      // Add fields for each mob with its emoji and formatted kill count
      displayedKills.forEach((kill, index) => {
        const rank = startIndex + index + 1;
        embed.addFields({
          name: `#${rank} ${kill.emoji} ${kill.name}`,
          value: `**Level ${kill.level}** • \`${formatNumber(kill.count)} Kills\``,
          inline: true,
        });

        // Add empty field for proper layout (2 per row)
        if (index % 2 === 0 && index < displayedKills.length - 1) {
          embed.addFields({ name: "\u200b", value: "\u200b", inline: true });
        }
      });
    } else {
      embed.setDescription("You haven't killed any mobs yet!");
    }

    return embed;
  },

  async showMobKillsView(interaction, character, page = 0) {
    const mobs = configManager.getAllMobs();
    const mobKills = character.mobKills || {};

    // Create an array of [mobKey, killCount] pairs and sort by kill count (descending)
    const sortedKills = Object.entries(mobKills)
      .map(([mobKey, killCount]) => {
        const mobData = mobs[mobKey];
        // Skip if mob data doesn't exist
        if (!mobData) return null;
        return {
          key: mobKey,
          count: killCount,
          name: mobData.name,
          emoji: mobData.emoji,
          level: mobData.level,
        };
      })
      .filter((entry) => entry !== null)
      .sort((a, b) => b.count - a.count);

    // Pagination configuration
    const itemsPerPage = KILLS_PAGE_SIZE;
    const totalPages = Math.max(
      MIN_PAGE_COUNT,
      Math.ceil(sortedKills.length / itemsPerPage)
    );
    const currentPage = Math.max(0, Math.min(page, totalPages - 1));

    // Create embed
    const embed = await this.createMobKillsEmbed(
      character,
      currentPage,
      totalPages,
      sortedKills,
      itemsPerPage
    );

    // Create pagination buttons
    const row = createNavigationButtons(currentPage, totalPages, "kills");

    const messageOptions = {
      embeds: [embed],
      components: totalPages > 1 ? [row] : [],
    };

    if (interaction.replied || interaction.deferred) {
      await interaction.editReply(messageOptions);
    } else {
      await interaction.reply(messageOptions);
    }

    // Set up button collector if we have pagination
    if (totalPages > 1) {
      const filter = (i) =>
        i.user.id === interaction.user.id &&
        (i.customId.startsWith("kills_prev_") ||
          i.customId.startsWith("kills_next_"));

      const collector = interaction.channel.createMessageComponentCollector({
        filter,
        time: COLLECTOR_TIMEOUT_MS,
      });

      // Use a variable to track the current page that can be modified
      let trackedCurrentPage = currentPage;

      collector.on("collect", async (i) => {
        let newPage = trackedCurrentPage;

        if (i.customId.startsWith("kills_next_")) {
          newPage = Math.min(trackedCurrentPage + 1, totalPages - 1);
        } else if (i.customId.startsWith("kills_prev_")) {
          newPage = Math.max(trackedCurrentPage - 1, 0);
        }

        try {
          // Create updated message components for this page
          const updatedRow = createNavigationButtons(
            newPage,
            totalPages,
            "kills"
          );

          // Get the updated embed for the new page
          const updatedEmbed = await this.createMobKillsEmbed(
            character,
            newPage,
            totalPages,
            sortedKills,
            itemsPerPage
          );

          // Update the message with new components and embed
          await i.update({
            embeds: [updatedEmbed],
            components: [updatedRow],
          });

          // Update our tracking variables for the collector
          trackedCurrentPage = newPage;
        } catch (error) {
          console.error("[Stats Command] Error updating pagination:", error);

          // For any interaction errors, don't try to send follow-up messages
          // The collector will continue to work with valid interactions
          if (error.code === 10062 || error.code === 40060) {
            console.log(
              "[Stats Command] Interaction expired or already acknowledged - continuing collection"
            );
            // We don't need to stop the collector, as other interactions may still work
          }
        }
      });

      collector.on("end", async () => {
        // Remove buttons when collector expires
        try {
          const disabledRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId("disabled_prev")
              .setLabel("Previous")
              .setStyle(ButtonStyle.Primary)
              .setDisabled(true),
            new ButtonBuilder()
              .setCustomId("disabled_next")
              .setLabel("Next")
              .setStyle(ButtonStyle.Primary)
              .setDisabled(true)
          );

          await interaction.editReply({
            embeds: [embed],
            components: [disabledRow],
          });
        } catch (error) {
          console.error("[Stats Command] Error disabling buttons:", error);
          // No need to do anything else here, as the buttons will become inactive after the collector ends
        }
      });
    }
  },
};
