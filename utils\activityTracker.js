const fs = require("fs");
const path = require("path");
const activityFile = path.join(__dirname, "recentAnnouncementActivity.json");

let recentAnnouncementActivity = {};

// Load from disk on startup
try {
  if (fs.existsSync(activityFile)) {
    const data = fs.readFileSync(activityFile, "utf8");
    recentAnnouncementActivity = JSON.parse(data);
  }
} catch (e) {
  console.error(
    "[ActivityTracker] Failed to load persistent activity file:",
    e
  );
  recentAnnouncementActivity = {};
}

// Debounce logic with Promise-based approach to avoid libuv conflicts
let writeTimeout = null;
function saveActivityToDisk() {
  if (writeTimeout) clearTimeout(writeTimeout);
  writeTimeout = setTimeout(() => {
    writeTimeout = null;
    // Use Promise-based approach instead of sync operations
    const fs = require("fs").promises;
    fs.writeFile(
      activityFile,
      JSON.stringify(recentAnnouncementActivity)
    ).catch((e) => {
      console.error(
        "[ActivityTracker] Failed to write persistent activity file:",
        e
      );
    });
  }, 500);
}

function recordActivityForAnnouncements(channelId) {
  recentAnnouncementActivity[channelId] = Date.now();
  saveActivityToDisk();
}

function getActiveChannelsForAnnouncements(withinMs = 5 * 60 * 1000) {
  const now = Date.now();
  return Object.entries(recentAnnouncementActivity)
    .filter(([, ts]) => now - ts <= withinMs)
    .map(([channelId]) => channelId);
}

module.exports = {
  recordActivityForAnnouncements,
  getActiveChannelsForAnnouncements,
};
