// Migration to add an index on the name column for faster lookups

const MIGRATION_VERSION = 5;

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add index on players(name)`,
  );

  await new Promise((resolve, reject) => {
    // Using CREATE INDEX IF NOT EXISTS for safety
    // Consider indexing LOWER(name) if lookups are always case-insensitive
    db.run(
      "CREATE INDEX IF NOT EXISTS idx_players_name ON players(name)",
      function (err) {
        if (err) {
          console.error(
            `[Migration ${MIGRATION_VERSION}] Error creating index on name column:`,
            err,
          );
          reject(err);
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully created index idx_players_name.`,
          );
          resolve();
        }
      },
    );
  });
}

module.exports = { up /*, down */ };
