{"name": "Mushroom Minion", "emoji": "<:minion_mushroom:1386570957694435369>", "type": "MINION", "isMinion": true, "rarity": "RARE", "unique": true, "sellable": false, "category": "farming", "drops": [{"itemKey": "RED_MUSHROOM", "chance": 0.5}, {"itemKey": "BROWN_MUSHROOM", "chance": 0.5}], "recipes": [{"ingredients": [{"itemKey": "RED_MUSHROOM", "amount": 80}]}], "craftingRequirements": {"collections": {"MUSHROOM": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 30, "maxStorage": 128}, {"tier": 2, "generationIntervalSeconds": 30, "maxStorage": 256, "upgradeCost": [{"itemKey": "RED_MUSHROOM", "amount": 160}]}, {"tier": 3, "generationIntervalSeconds": 28, "maxStorage": 256, "upgradeCost": [{"itemKey": "BROWN_MUSHROOM", "amount": 320}]}, {"tier": 4, "generationIntervalSeconds": 28, "maxStorage": 384, "upgradeCost": [{"itemKey": "RED_MUSHROOM", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 26, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_BROWN_MUSHROOM", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 26, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_RED_MUSHROOM", "amount": 16}]}, {"tier": 7, "generationIntervalSeconds": 23, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_BROWN_MUSHROOM", "amount": 32}]}, {"tier": 8, "generationIntervalSeconds": 23, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_RED_MUSHROOM", "amount": 64}]}, {"tier": 9, "generationIntervalSeconds": 20, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_BROWN_MUSHROOM", "amount": 128}]}, {"tier": 10, "generationIntervalSeconds": 20, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_RED_MUSHROOM", "amount": 256}]}, {"tier": 11, "generationIntervalSeconds": 16, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_BROWN_MUSHROOM", "amount": 512}]}]}