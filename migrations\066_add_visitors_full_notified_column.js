/**
 * Migration 066: Add visitors_full_notified column to player_last_active_channel table
 * Tracks when a player was last notified that their garden visitors have reached maximum capacity (5).
 */

const MIGRATION_VERSION = 66;

/**
 * Applies the migration to add visitors_full_notified column to player_last_active_channel table
 * @param {object} db - SQLite database object
 * @returns {Promise<void>}
 */
async function up(db) {
  console.log(
    "Running migration: add visitors_full_notified column to player_last_active_channel",
  );

  // Check if table exists
  const tableExists = await new Promise((resolve, reject) => {
    db.get(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='player_last_active_channel'",
      (err, row) => {
        if (err) reject(err);
        else resolve(!!row);
      },
    );
  });

  if (!tableExists) {
    console.log(
      "player_last_active_channel table does not exist. Creating with visitors_full_notified column...",
    );
    await new Promise((resolve, reject) => {
      db.run(
        `CREATE TABLE player_last_active_channel (
                    discord_id TEXT PRIMARY KEY,
                    channel_id TEXT,
                    updated_at INTEGER,
                    last_notified INTEGER,
                    any_full_notified INTEGER,
                    pet_upgrade_notified INTEGER,
                    visitors_full_notified INTEGER
                )`,
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });
    return;
  }

  // Check if column already exists
  const columnExists = await new Promise((resolve, reject) => {
    db.all("PRAGMA table_info(player_last_active_channel)", (err, rows) => {
      if (err) reject(err);
      else resolve(rows.some((col) => col.name === "visitors_full_notified"));
    });
  });

  if (columnExists) {
    console.log("visitors_full_notified column already exists; skipping");
    return;
  }

  // Add the column
  await new Promise((resolve, reject) => {
    db.run(
      "ALTER TABLE player_last_active_channel ADD COLUMN visitors_full_notified INTEGER",
      (err) => {
        if (err) reject(err);
        else resolve();
      },
    );
  });

  console.log(
    "Added visitors_full_notified column to player_last_active_channel table",
  );
}

/**
 * Rollback for this migration
 * @param {object} db - SQLite database object
 * @returns {Promise<void>}
 */
async function down(_db) {
  console.log(
    "Rolling back migration: remove visitors_full_notified column from player_last_active_channel",
  );

  // SQLite doesn't support DROP COLUMN, so we'd need to recreate the table
  // For now, we'll just log that rollback is not supported
  console.log("Rollback not supported for this migration (SQLite limitation)");
}

module.exports = { up, down, version: MIGRATION_VERSION };
