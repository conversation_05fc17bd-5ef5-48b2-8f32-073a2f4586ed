{"name": "Melon Minion", "emoji": "<:minion_melon:1375851407307182200>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "resourceItemKey": "MELON", "recipes": [{"ingredients": [{"itemKey": "MELON", "amount": 80}]}], "craftingRequirements": {"collections": {"MELON": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 18, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 18, "maxStorage": 192, "upgradeCost": [{"itemKey": "MELON", "amount": 160}]}, {"tier": 3, "generationIntervalSeconds": 16, "maxStorage": 192, "upgradeCost": [{"itemKey": "MELON", "amount": 320}]}, {"tier": 4, "generationIntervalSeconds": 16, "maxStorage": 384, "upgradeCost": [{"itemKey": "MELON", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 14, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_MELON", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 14, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_MELON", "amount": 24}]}, {"tier": 7, "generationIntervalSeconds": 12, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_MELON", "amount": 64}]}, {"tier": 8, "generationIntervalSeconds": 12, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_MELON", "amount": 128}]}, {"tier": 9, "generationIntervalSeconds": 10, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_MELON", "amount": 256}]}, {"tier": 10, "generationIntervalSeconds": 10, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_MELON", "amount": 512}]}, {"tier": 11, "generationIntervalSeconds": 8, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_MELON_BLOCK", "amount": 8}]}], "drops": [{"itemKey": "MELON", "chance": 1}]}