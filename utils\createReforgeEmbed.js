const { EmbedBuilder } = require("discord.js");
const { formatNumber } = require("../utils/displayUtils");
const { STATS, STAT_ABBREVIATIONS } = require("../gameConfig");
const { getItemColor } = require("./rarityUtils");

/**
 * Creates an embed for the blacksmith reforge interface
 * @param {Object} item - The item being reforged
 * @param {Object} currentReforge - The current reforge on the item
 * @param {string} statsText - Formatted string of the item's stats
 * @param {number} playerCoins - Player's current coin balance
 * @param {number} cost - Cost to reforge
 * @param {string} identifier - Item identifier
 * @returns {EmbedBuilder} - The created embed
 */
function createReforgeEmbed(
  item,
  currentReforge,
  statsText,
  playerCoins,
  cost,
  identifier
) {
  // ALWAYS recalculate the cost directly from the item rarity - ignore passed in cost parameter
  const { getReforgeCost } = require("../data/reforges");
  cost = getReforgeCost(item.rarity);
  console.log(
    `[createReforgeEmbed] FORCE RECALCULATED - Item rarity: ${item.rarity}, Cost: ${cost}`
  );
  // Format reforge stats if they exist
  let reforgeStatsText = "No stats";

  if (currentReforge && currentReforge.stats) {
    const reforgeStatsArray = Object.entries(currentReforge.stats).map(
      ([stat, value]) => {
        const statConfig = STATS[stat];
        if (statConfig) {
          const displayValue = statConfig.isPercentage
            ? `${value > 0 ? "+" : ""}${value}%`
            : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
          const statName =
            STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
          return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
        } else {
          return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
        }
      }
    );

    if (reforgeStatsArray.length > 0) {
      reforgeStatsText = reforgeStatsArray.reduce((acc, stat, index) => {
        if (index % 3 === 0 && index > 0) acc += "\n";
        return acc + (index % 3 === 0 ? "" : " ") + stat;
      }, "");
    }
  }

  // Build the description with proper formatting
  let description = `**Stats:**\n${statsText}\n\n`;
  description += `**Current Reforge:** ${currentReforge ? currentReforge.name : "None"}\n`;

  // Add reforge stats if they exist
  if (currentReforge) {
    description += `**Stats:**\n${reforgeStatsText}\n\n`;
  } else {
    description += "\n";
  }

  // Add currency and cost information
  description += `**Your coins:** <:purse_coins:1367849116033482772> ${formatNumber(playerCoins)} Coins\n\n`;
  description += `**Reforge Cost:** <:purse_coins:1367849116033482772> ${formatNumber(cost)} Coins`;

  return new EmbedBuilder()
    .setTitle(
      `<:npc_blacksmith:1378078835752439879> Blacksmith: ${item.emoji} ${item.name}`
    )
    .setDescription(description)
    .setColor(getItemColor(item))
    .setFooter({ text: `Item ID: ${identifier}` });
}

module.exports = createReforgeEmbed;
