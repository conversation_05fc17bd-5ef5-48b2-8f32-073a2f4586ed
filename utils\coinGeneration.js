const { dbAll, dbRunQueued } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");
const configManager = require("./configManager");

/**
 * Calculate total coin generation rate from equipped accessories
 * @param {Object} character - Player character data
 * @returns {number} Total coins per minute from accessories
 */
function calculateCoinGenerationRate(character) {
  if (
    !character ||
    !character.inventory ||
    !character.inventory.accessories ||
    !Array.isArray(character.inventory.accessories)
  ) {
    return 0;
  }

  const allItems = configManager.getAllItems();
  let totalCoinGeneration = 0;

  for (const accessory of character.inventory.accessories) {
    if (accessory.isEquipped) {
      const itemData = allItems[accessory.itemKey];
      if (itemData && itemData.effects && itemData.effects.coinGeneration) {
        totalCoinGeneration += itemData.effects.coinGeneration;
      }
    }
  }

  return totalCoinGeneration;
}

/**
 * Apply passive coin generation to all players
 * Called periodically by the bot's interval system
 */
async function applyCoinGenerationToAllPlayers() {
  try {
    // Get all players (including those without last_coin_generation set)
    const players = await dbAll(`
      SELECT discord_id, last_coin_generation 
      FROM players
    `);

    const now = Date.now();

    for (const playerRow of players) {
      try {
        const character = await getPlayerData(playerRow.discord_id);
        if (!character) continue;

        const coinGenerationRate = calculateCoinGenerationRate(character);
        if (coinGenerationRate <= 0) {
          continue;
        }

        // If player doesn't have last_coin_generation set, initialize it now
        if (playerRow.last_coin_generation === null) {
          // Initialize timestamp using queued write
          await dbRunQueued(
            "UPDATE players SET last_coin_generation = ? WHERE discord_id = ? AND last_coin_generation IS NULL",
            [now, playerRow.discord_id]
          );
          continue; // Skip this iteration, start generating from next cycle
        }

        const lastGeneration = playerRow.last_coin_generation;
        const timeDiff = now - lastGeneration;

        // Only process if at least 1 minute has passed
        if (timeDiff >= 60000) {
          // 60 seconds
          const minutesPassed = timeDiff / 60000;
          const coinsToAdd =
            Math.round(coinGenerationRate * minutesPassed * 10) / 10; // Round to 1 decimal place

          if (coinsToAdd > 0) {
            // Single queued update that includes the timestamp check in WHERE clause to avoid race conditions
            await dbRunQueued(
              `UPDATE players 
               SET coins = coins + ?, last_coin_generation = ?
               WHERE discord_id = ? AND last_coin_generation = ?`,
              [coinsToAdd, now, playerRow.discord_id, lastGeneration]
            );
          }
        }
      } catch (error) {
        console.error(
          `Error processing player ${playerRow.discord_id}:`,
          error
        );
      }
    }
  } catch (error) {
    console.error("Error in applyCoinGenerationToAllPlayers:", error);
  }
}

/**
 * Initialize coin generation for a player (set their last_coin_generation timestamp)
 * @param {string} userId - Discord user ID
 */
async function initializeCoinGeneration(userId) {
  try {
    const now = Date.now();
    // Check if player has coin generation timestamp, if not, initialize it
    const player = await dbAll(
      `
      SELECT last_coin_generation 
      FROM players 
      WHERE discord_id = ?
    `,
      [userId]
    );

    if (player.length > 0 && player[0].last_coin_generation === null) {
      await dbRunQueued(
        "UPDATE players SET last_coin_generation = ? WHERE discord_id = ? AND last_coin_generation IS NULL",
        [now, userId]
      );
    }
  } catch (error) {
    console.error(`Error initializing coin generation for ${userId}:`, error);
  }
}

/**
 * Initialize coin generation for all players who have coin-generating accessories
 * but don't have the timestamp set. This is a one-time fix function.
 */
async function initializeAllPlayersCoinGeneration() {
  try {
    const players = await dbAll(`
      SELECT discord_id 
      FROM players 
      WHERE last_coin_generation IS NULL
    `);
    let initializedCount = 0;

    const now = Date.now();

    for (const playerRow of players) {
      try {
        const character = await getPlayerData(playerRow.discord_id);
        if (!character) {
          continue;
        }

        const coinGenerationRate = calculateCoinGenerationRate(character);

        if (coinGenerationRate > 0) {
          // playersWithAccessories++; // Unused counter
          await dbRunQueued(
            "UPDATE players SET last_coin_generation = ? WHERE discord_id = ? AND last_coin_generation IS NULL",
            [now, playerRow.discord_id]
          );
          initializedCount++;
        }
      } catch (error) {
        console.error(`Error checking player ${playerRow.discord_id}:`, error);
      }
    }

    return initializedCount;
  } catch (error) {
    console.error("Error in initializeAllPlayersCoinGeneration:", error);
    return 0;
  }
}

module.exports = {
  calculateCoinGenerationRate,
  applyCoinGenerationToAllPlayers,
  initializeCoinGeneration,
  initializeAllPlayersCoinGeneration,
};
