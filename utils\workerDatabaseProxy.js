// Worker Database Proxy - Delegates all database operations to main bot via API
// This replaces direct database access for worker bots to prevent SQLITE_BUSY errors

const axios = require("axios");
const http = require("http");
const https = require("https");

const MAIN_BOT_API_URL =
  process.env.MAIN_BOT_API_URL || "http://127.0.0.1:3000";
const WORKER_BOT_ID = process.env.WORKER_BOT_ID || "worker-1";

// Create a shared axios instance with keep-alive agents
const axiosClient = axios.create({
  baseURL: MAIN_BOT_API_URL,
  timeout: 15000,
  httpAgent: new http.Agent({
    keepAlive: true,
    maxSockets: 64,
    maxFreeSockets: 32,
    timeout: 60000, // socket idle timeout
    keepAliveMsecs: 10000,
  }),
  httpsAgent: new https.Agent({
    keepAlive: true,
    maxSockets: 64,
    maxFreeSockets: 32,
  }),
  headers: { "Content-Type": "application/json" },
});

// Add internal auth header if configured
if (process.env.INTERNAL_API_TOKEN) {
  axiosClient.defaults.headers.common["x-internal-token"] =
    process.env.INTERNAL_API_TOKEN;
}

// Simple retry helper for transient network errors (expanded)
function isTransientNetworkError(error) {
  if (!error) return false;
  const code = error.code || "";
  const msg = String(error.message || "").toLowerCase();
  return (
    code === "ECONNRESET" ||
    code === "ECONNABORTED" || // axios timeout
    code === "ECONNREFUSED" ||
    code === "ETIMEDOUT" ||
    code === "EAI_AGAIN" || // DNS lookup timeout
    code === "ENETUNREACH" ||
    msg.includes("timeout") ||
    msg.includes("socket hang up") ||
    msg.includes("network error")
  );
}

function sleep(ms) {
  return new Promise((r) => setTimeout(r, ms));
}

/**
 * Generic retry with exponential backoff + jitter.
 * Designed to tolerate brief main bot restarts so actions don't crash during reward application.
 *
 * @param {Function} fn async function performing the request
 * @param {Object} opts options
 * @param {number} opts.retries max retry attempts (not counting initial try)
 * @param {number} opts.baseDelay initial backoff in ms
 * @param {number} opts.maxDelay cap per-attempt delay
 * @param {number} opts.totalTimeoutMs overall wall-clock timeout budget
 * @param {boolean} opts.aggressive if true, use faster ramp (for high-frequency calls)
 */
async function withRetry(
  fn,
  {
    retries = 2,
    baseDelay = 150,
    maxDelay = 2000,
    totalTimeoutMs = 10_000,
    aggressive = false,
  } = {}
) {
  const start = Date.now();
  let attempt = 0;
  let lastErr;
  while (attempt <= retries) {
    // attempt 0 = initial try
    try {
      return await fn();
    } catch (err) {
      lastErr = err;
      const transient = isTransientNetworkError(err);
      if (!transient) throw err; // non-network error: bubble immediately

      if (attempt === retries) {
        // no retries left
        break;
      }

      attempt++;
      // exponential backoff (attempt starts at 1 here)
      const expFactor = aggressive ? attempt : Math.pow(2, attempt - 1);
      let delay = Math.min(baseDelay * expFactor, maxDelay);
      // add jitter (±25%)
      const jitterRange = Math.floor(delay * 0.25);
      delay = delay - jitterRange + Math.floor(Math.random() * jitterRange * 2);
      const elapsed = Date.now() - start;
      if (elapsed + delay > totalTimeoutMs) {
        // exceeding overall budget; stop retrying
        break;
      }
      const code = err.code || "UNKNOWN";
      console.warn(
        `[Worker ${WORKER_BOT_ID}] Transient API error (${code}) attempt ${attempt}/${retries} – retrying in ${delay}ms: ${err.message}`
      );
      await sleep(delay);
    }
  }
  // Out of retries
  throw lastErr;
}

/**
 * Worker-safe version of getPlayerData that uses API calls instead of direct DB access
 */
async function getPlayerData(userId) {
  try {
    // Use extended retry window because this is called very frequently during reward application
    const response = await withRetry(
      () =>
        axiosClient.post(
          "/api/database/get-player-data",
          { userId },
          { timeout: 12_000 }
        ),
      { retries: 6, baseDelay: 200, maxDelay: 2500, totalTimeoutMs: 18_000 }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to get player data from main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in getPlayerData API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe version of savePlayerData that uses API calls instead of direct DB access
 */
async function savePlayerData(userId, playerData) {
  try {
    const response = await axiosClient.post(
      "/api/database/save-player-data",
      { userId, playerData },
      { timeout: 10000 }
    );

    if (!response.data.success) {
      throw new Error("Failed to save player data via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in savePlayerData API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe version of updateInventoryAtomically that uses API calls instead of direct DB access
 */
async function updateInventoryAtomically(
  userId,
  coinsToAdd = 0,
  itemsToChange = [],
  equipmentToAdd = [],
  equipmentIdsToRemove = [],
  bankCoinsToAdd = 0,
  equipmentDataUpdates = [],
  islandJsonString = null,
  collectionsJsonString = null,
  _useExistingTransaction = false, // ignored in worker mode - prefixed with _ to avoid lint error
  accessoriesToAdd = [],
  accessoryIdsToRemove = []
) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-inventory",
      {
        userId,
        coinsToAdd,
        itemsToChange,
        equipmentToAdd,
        equipmentIdsToRemove,
        bankCoinsToAdd,
        equipmentDataUpdates,
        islandJsonString,
        collectionsJsonString,
        accessoriesToAdd,
        accessoryIdsToRemove,
      },
      { timeout: 15000 }
    );

    if (!response.data.success) {
      throw new Error("Failed to update inventory via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updateInventoryAtomically API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe version of updatePlayerSkillDataAtomically that uses API calls instead of direct DB access
 */
async function updatePlayerSkillDataAtomically(userId, updates) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-player-skill-data",
      { userId, updates },
      { timeout: 10000 }
    );

    if (!response.data.success) {
      throw new Error("Failed to update player skill data via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updatePlayerSkillDataAtomically API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Utility functions that workers still need for calculations but don't require DB access
 */
function getPlayerSkillLevel(character, skillName) {
  // This is a calculation function that doesn't touch the database
  const {
    getPlayerSkillLevel: originalFunction,
  } = require("./playerDataManager");
  return originalFunction(character, skillName);
}

/**
 * Worker-safe version of getActionById that uses API calls instead of direct DB access
 */
async function getActionById(actionId) {
  try {
    const response = await withRetry(
      () => axiosClient.post("/api/database/get-action-by-id", { actionId }),
      {
        retries: 4,
        baseDelay: 150,
        maxDelay: 2000,
        totalTimeoutMs: 12_000,
        aggressive: true,
      }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to get action by ID from main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in getActionById API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe function to get pending actions for this specific worker
 */
async function getPendingActionsForWorker() {
  try {
    const response = await withRetry(
      () =>
        axiosClient.post(
          "/api/database/get-pending-actions-for-worker",
          { workerId: WORKER_BOT_ID },
          { timeout: 12_000 }
        ),
      { retries: 5, baseDelay: 180, maxDelay: 2200, totalTimeoutMs: 16_000 }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to get pending actions from main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in getPendingActionsForWorker API call:`,
      error.message
    );
    throw error;
  }
}

async function startAction(
  userId,
  actionType,
  resourceKey,
  parameters,
  totalAmount,
  channelId,
  messageId,
  workerId
) {
  try {
    const response = await axiosClient.post(
      "/api/database/start-action",
      {
        userId,
        actionType,
        resourceKey,
        parameters,
        totalAmount,
        channelId,
        messageId,
        workerId,
      },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to start action via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in startAction API call:`,
      error.message
    );
    throw error;
  }
}

async function updateActionProgress(
  actionId,
  completedCycles,
  currentCycleResult
) {
  try {
    const response = await withRetry(
      () =>
        axiosClient.post("/api/database/update-action-progress", {
          actionId,
          completedCycles,
          currentCycleResult,
        }),
      {
        retries: 4,
        baseDelay: 120,
        maxDelay: 1500,
        totalTimeoutMs: 10_000,
        aggressive: true,
      }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to update action progress via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updateActionProgress API call:`,
      error.message
    );
    throw error;
  }
}

async function completeAction(actionId) {
  try {
    const response = await axiosClient.post(
      "/api/database/complete-action",
      { actionId },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to complete action via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in completeAction API call:`,
      error.message
    );
    throw error;
  }
}

async function getActionParameters(actionId) {
  try {
    const response = await withRetry(
      () =>
        axiosClient.post(
          "/api/database/get-action-parameters",
          { actionId },
          { timeout: 12_000 }
        ),
      { retries: 4, baseDelay: 160, maxDelay: 1800, totalTimeoutMs: 14_000 }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to get action parameters from main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in getActionParameters API call:`,
      error.message
    );
    throw error;
  }
}

async function updateActionParameters(actionId, newParams) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-action-parameters",
      { actionId, newParams },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to update action parameters via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updateActionParameters API call:`,
      error.message
    );
    throw error;
  }
}

async function updateActionMessageId(actionId, newMessageId) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-action-message-id",
      { actionId, newMessageId },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to update action message ID via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updateActionMessageId API call:`,
      error.message
    );
    throw error;
  }
}

async function updateActionMessageTimestamp(actionId, timestamp) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-action-message-timestamp",
      { actionId, timestamp },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to update action message timestamp via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updateActionMessageTimestamp API call:`,
      error.message
    );
    throw error;
  }
}

async function clearActivity(userId) {
  try {
    const response = await axiosClient.post(
      "/api/database/clear-activity",
      { userId },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to clear activity via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in clearActivity API call:`,
      error.message
    );
    throw error;
  }
}

async function getCurrentActivity(userId) {
  try {
    const response = await withRetry(
      () => axiosClient.post("/api/database/get-current-activity", { userId }),
      { retries: 4, baseDelay: 140, maxDelay: 1800, totalTimeoutMs: 12_000 }
    );
    if (response.data.success) {
      return response.data.activity;
    } else {
      throw new Error("Failed to get current activity via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in getCurrentActivity API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe function to get detailed activity information (more useful than getCurrentActivity)
 */
async function getDetailedActivity(userId) {
  try {
    const response = await withRetry(
      () =>
        axiosClient.post(
          "/api/database/get-detailed-activity",
          { userId },
          { timeout: 12_000 }
        ),
      { retries: 4, baseDelay: 160, maxDelay: 1800, totalTimeoutMs: 14_000 }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to get detailed activity via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in getDetailedActivity API call:`,
      error.message
    );
    throw error;
  }
}

async function setActivity(userId, activityType, data = null) {
  try {
    const response = await axiosClient.post(
      "/api/database/set-activity",
      { userId, activityType, data },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return response.data.result;
    } else {
      throw new Error("Failed to set activity via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in setActivity API call:`,
      error.message
    );
    throw error;
  }
}

async function isConflict(userId, activityType) {
  try {
    const response = await withRetry(
      () =>
        axiosClient.post(
          "/api/database/is-conflict",
          { userId, activityType },
          { timeout: 12_000 }
        ),
      { retries: 4, baseDelay: 140, maxDelay: 1600, totalTimeoutMs: 12_000 }
    );

    if (response.data.success) {
      return response.data.isConflict;
    } else {
      throw new Error("Failed to check activity conflict via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in isConflict API call:`,
      error.message
    );
    throw error;
  }
}

async function recalculateAndSaveStats(discordId, character) {
  try {
    const response = await axiosClient.post(
      "/api/database/recalculate-and-save-stats",
      { discordId, character },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return;
    } else {
      throw new Error("Failed to recalculate and save stats via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in recalculateAndSaveStats API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe version of updatePlayerSetting that uses API calls instead of direct DB access
 */
async function updatePlayerSetting(userId, settingKey, settingValue) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-player-setting",
      { userId, settingKey, settingValue },
      { timeout: 10000 }
    );

    if (!response.data.success) {
      throw new Error("Failed to update player setting via main bot");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updatePlayerSetting API call:`,
      error.message
    );
    throw error;
  }
}

/**
 * Worker-safe function to update the worker_id for an action
 * This fixes the worker assignment mismatch issue
 */
async function updateActionWorkerId(actionId, workerId) {
  try {
    const response = await axiosClient.post(
      "/api/database/update-action-worker-id",
      { actionId, workerId },
      { timeout: 10000 }
    );

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error("Failed to update action worker_id via main bot API");
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in updateActionWorkerId API call:`,
      error.message
    );
    throw error;
  }
}

// Export the worker-safe database functions
module.exports = {
  axiosClient,
  getPlayerData,
  savePlayerData,
  updateInventoryAtomically,
  updatePlayerSkillDataAtomically,
  getPlayerSkillLevel,
  recalculateAndSaveStats,
  getActionById,
  getPendingActionsForWorker,
  getDetailedActivity,
  startAction,
  updateActionProgress,
  completeAction,
  getActionParameters,
  updateActionParameters,
  updateActionMessageId,
  updateActionMessageTimestamp,
  updateActionWorkerId,
  clearActivity,
  getCurrentActivity,
  setActivity,
  isConflict,
  updatePlayerSetting,
  // Add any other functions that workers need but make them API-based
};
