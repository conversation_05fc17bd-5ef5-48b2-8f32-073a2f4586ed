const { dbRun, dbAll } = require("../utils/dbUtils");

async function up() {
  console.log(
    "[Migration 045] Checking if last_identity_change column exists...",
  );

  try {
    // Check if column already exists
    const columns = await dbAll("PRAGMA table_info(players)");
    const columnExists = columns.some(
      (col) => col.name === "last_identity_change",
    );

    if (columnExists) {
      console.log(
        "[Migration 045] last_identity_change column already exists, skipping...",
      );
      return;
    }

    console.log(
      "[Migration 045] Adding last_identity_change column to players table...",
    );
    await dbRun(`
      ALTER TABLE players 
      ADD COLUMN last_identity_change INTEGER DEFAULT NULL
    `);

    console.log(
      "[Migration 045] Successfully added last_identity_change column",
    );
  } catch (error) {
    console.error(
      "[Migration 045] Error adding last_identity_change column:",
      error,
    );
    throw error;
  }
}

async function down() {
  console.log(
    "[Migration 045] Removing last_identity_change column from players table...",
  );

  try {
    // SQLite doesn't support DROP COLUMN directly, so we'd need to recreate the table
    // For now, we'll just log that this migration cannot be easily reversed
    console.log(
      "[Migration 045] Note: SQLite does not support DROP COLUMN. Manual intervention required to reverse.",
    );
  } catch (error) {
    console.error("[Migration 045] Error in down migration:", error);
    throw error;
  }
}

module.exports = { up, down };
