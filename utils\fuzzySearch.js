/**
 * fuzzy search utilities for matching item names and keys
 */

/**
 * calculates damerau-levenshtein distance (supports transpositions)
 * better for handling typos like "enchtnaed" -> "enchanted"
 * @param {string} str1
 * @param {string} str2
 * @returns {number} edit distance
 */
function editDistance(str1, str2) {
  const len1 = str1.length;
  const len2 = str2.length;

  // create a matrix
  const matrix = Array(len1 + 1)
    .fill(null)
    .map(() => Array(len2 + 1).fill(0));

  // initialize first row and column
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;

  // fill the matrix
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;

      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1, // deletion
        matrix[i][j - 1] + 1, // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );

      // transposition (swap adjacent characters)
      if (
        i > 1 &&
        j > 1 &&
        str1[i - 1] === str2[j - 2] &&
        str1[i - 2] === str2[j - 1]
      ) {
        matrix[i][j] = Math.min(matrix[i][j], matrix[i - 2][j - 2] + cost);
      }
    }
  }

  return matrix[len1][len2];
}

/**
 * checks if a search query fuzzy matches a target string
 * @param {string} query - the search query
 * @param {string} target - the target string to match against
 * @returns {number} match score (higher is better, 0 means no match)
 */
function fuzzyMatchScore(query, target) {
  const queryLower = query.toLowerCase().trim();
  const targetLower = target.toLowerCase();

  // exact match gets highest score
  if (queryLower === targetLower) {
    return 1000;
  }

  // starts with match gets very high score
  if (targetLower.startsWith(queryLower)) {
    return 900 - queryLower.length; // prefer shorter queries for same start
  }

  // contains match gets high score
  if (targetLower.includes(queryLower)) {
    return 800 - queryLower.length;
  }

  // word boundary matches (each word in query appears in target)
  const queryWords = queryLower.split(/\s+/).filter((word) => word.length > 0);
  if (queryWords.length > 1) {
    const targetWords = targetLower.split(/\s+/);
    let matchedWords = 0;

    for (const queryWord of queryWords) {
      for (const targetWord of targetWords) {
        if (
          targetWord.startsWith(queryWord) ||
          targetWord.includes(queryWord)
        ) {
          matchedWords++;
          break;
        }
      }
    }

    // if all query words match, give it a good score
    if (matchedWords === queryWords.length) {
      return 700 - queryWords.length * 10;
    }
  }

  // abbreviation matching (like "ench string" -> "enchanted string")
  if (queryWords.length > 1) {
    const targetWords = targetLower.split(/\s+/);
    let abbreviationScore = 0;

    for (let i = 0; i < Math.min(queryWords.length, targetWords.length); i++) {
      const queryWord = queryWords[i];
      const targetWord = targetWords[i];

      if (targetWord.startsWith(queryWord)) {
        abbreviationScore += queryWord.length;
      }
    }

    // if we got decent abbreviation matches, score it
    if (abbreviationScore >= queryLower.replace(/\s+/g, "").length * 0.7) {
      return 600 + abbreviationScore;
    }
  }

  // fuzzy matching using edit distance for typos
  if (queryLower.length >= 3) {
    // only for meaningful queries
    const distance = editDistance(queryLower, targetLower);
    const maxLen = Math.max(queryLower.length, targetLower.length);
    const similarity = 1 - distance / maxLen;

    // be more generous with typo tolerance
    if (similarity > 0.5) {
      // lowered from 0.6
      return Math.floor(similarity * 500);
    }

    // special case: check individual words for typos
    const queryWords = queryLower
      .split(/\s+/)
      .filter((word) => word.length > 2);
    const targetWords = targetLower.split(/\s+/);

    if (queryWords.length > 0 && targetWords.length > 0) {
      let totalSimilarity = 0;
      let matchedWords = 0;

      for (const queryWord of queryWords) {
        let bestWordSimilarity = 0;

        for (const targetWord of targetWords) {
          const wordDistance = editDistance(queryWord, targetWord);
          const wordMaxLen = Math.max(queryWord.length, targetWord.length);
          const wordSimilarity = 1 - wordDistance / wordMaxLen;

          if (wordSimilarity > 0.6) {
            // individual word tolerance
            bestWordSimilarity = Math.max(bestWordSimilarity, wordSimilarity);
          }
        }

        if (bestWordSimilarity > 0) {
          totalSimilarity += bestWordSimilarity;
          matchedWords++;
        }
      }

      // if we matched most words with decent similarity
      if (matchedWords >= Math.ceil(queryWords.length * 0.7)) {
        const avgSimilarity = totalSimilarity / queryWords.length;
        if (avgSimilarity > 0.6) {
          return Math.floor(avgSimilarity * 450); // slightly lower than direct fuzzy match
        }
      }
    }
  }

  return 0; // no match
}

/**
 * searches items using fuzzy matching and returns sorted results
 * @param {Object} allItems - object with all items (from configManager)
 * @param {string} searchQuery - the search query
 * @param {Object} options - search options
 * @returns {Array} sorted array of matching items with scores
 */
function fuzzySearchItems(allItems, searchQuery, options = {}) {
  const { excludeUnique = true, excludePets = true, maxResults = 50 } = options;

  if (!searchQuery || searchQuery.trim().length === 0) {
    return [];
  }

  const results = [];

  for (const [itemKey, itemData] of Object.entries(allItems)) {
    // apply filters
    if (excludeUnique && itemData?.unique === true) continue;
    if (excludePets && itemData?.type === "Pet") continue;

    const itemName = itemData?.name || itemKey;

    // calculate scores for both key and name
    const keyScore = fuzzyMatchScore(searchQuery, itemKey);
    const nameScore = fuzzyMatchScore(searchQuery, itemName);

    // take the best score
    const bestScore = Math.max(keyScore, nameScore);

    if (bestScore > 0) {
      results.push({
        itemKey,
        itemData,
        score: bestScore,
        matchedOn: keyScore > nameScore ? "key" : "name",
      });
    }
  }

  // sort by score (highest first), then by name
  results.sort((a, b) => {
    if (b.score !== a.score) {
      return b.score - a.score;
    }
    return (a.itemData?.name || a.itemKey).localeCompare(
      b.itemData?.name || b.itemKey
    );
  });

  return results.slice(0, maxResults);
}

/**
 * finds the best single match for a search query
 * @param {Object} allItems - object with all items
 * @param {string} searchQuery - the search query
 * @param {Object} options - search options
 * @returns {Object|null} best matching item or null
 */
function findBestMatch(allItems, searchQuery, options = {}) {
  const results = fuzzySearchItems(allItems, searchQuery, {
    ...options,
    maxResults: 1,
  });
  return results.length > 0 ? results[0] : null;
}

/**
 * checks if a search query is likely an exact match attempt
 * @param {string} searchQuery
 * @returns {boolean}
 */
function isLikelyExactMatch(searchQuery) {
  const query = searchQuery.trim();

  // if it's all uppercase and has underscores, likely an item key
  if (query === query.toUpperCase() && query.includes("_")) {
    return true;
  }

  // if it has proper capitalization and spaces, likely an item name
  if (/^[A-Z][a-z]/.test(query) && query.includes(" ")) {
    return true;
  }

  return false;
}

module.exports = {
  fuzzySearchItems,
  findBestMatch,
  fuzzyMatchScore,
  isLikelyExactMatch,
};
