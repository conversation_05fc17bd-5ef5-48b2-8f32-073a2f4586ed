const { EMBED_COLORS } = require("../gameConfig.js");
const {
  SlashCommandBuilder,
  EmbedBuilder,
  MessageFlags,
} = require("discord.js");
const { getPlayerData, savePlayerData } = require("../utils/playerDataManager");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager");
// const { dbGet } = require("../utils/dbUtils"); // Currently unused
const {
  getFilteredPlayers,
  getPlayerByUsername,
} = require("../utils/playerCache");
const { getMinionDisplayInfo } = require("./minions.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("visit")
    .setDescription("Visit another player's private island.")
    .addStringOption((option) =>
      option
        .setName("player")
        .setDescription("Name of the player whose island you want to visit")
        .setRequired(true)
        .setAutocomplete(true)
    ),
  async autocomplete(interaction) {
    const focusedValue = interaction.options.getFocused();

    try {
      const choices = getFilteredPlayers(focusedValue, 25);
      await interaction.respond(choices);
    } catch (error) {
      console.error("Error in visit username autocomplete:", error);
      await interaction.respond([]);
    }
  },

  async execute(interaction) {
    const visitorId = interaction.user.id;

    // Check if visitor is busy with another action
    const currentActivity = await getCurrentActivity(visitorId);
    if (currentActivity) {
      return warnUserBusy(interaction, currentActivity, "visit");
    }

    let visitorCharacter;
    try {
      visitorCharacter = await getPlayerData(visitorId);
    } catch (error) {
      console.error(
        `[Visit Command] Error fetching visitor data for ${visitorId}: [VST-DAT-VIS-ERR]`,
        error
      );
      return interaction.reply({
        content:
          "[VST-DAT-VIS-ERR] Error fetching your character data. Please try again or report this code.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!visitorCharacter) {
      return interaction.reply({
        content:
          "You must create a character first using /setup channel before visiting others.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    const targetName = interaction.options.getString("player");

    if (visitorCharacter.name === targetName) {
      return interaction.reply({
        content: "Use /warp [destination] to travel to your island.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    try {
      await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

      // Find the target player by name using cache
      const targetPlayer = getPlayerByUsername(targetName);

      if (!targetPlayer) {
        return interaction.editReply({
          content: `Could not find a character named '${targetName}'.`,
        });
      }

      const targetId = targetPlayer.discord_id;

      if (visitorCharacter.visiting_island_owner_id === targetId) {
        return interaction.editReply({
          content: `You are already visiting ${targetName}'s island.`,
        });
      }

      let targetCharacter;
      try {
        targetCharacter = await getPlayerData(targetId);
      } catch (error) {
        console.error(
          `[Visit Command] Error fetching target data for ${targetId} (name: ${targetName}): [VST-DAT-TGT-ERR]`,
          error
        );
        return interaction.editReply({
          content: `[VST-DAT-TGT-ERR] Error fetching data for character '${targetName}'.`,
        });
      }

      if (!targetCharacter || typeof targetCharacter.island !== "object") {
        console.error(
          `[Visit Command] Found ID ${targetId} but invalid data for ${targetName}: [VST-DAT-INV]`
        );
        return interaction.editReply({
          content: `[VST-DAT-INV] Found player '${targetName}' but their data is invalid.`,
        });
      }

      visitorCharacter.current_region = "Visiting";
      visitorCharacter.visiting_island_owner_id = targetId;
      try {
        await savePlayerData(visitorId, visitorCharacter);
      } catch (saveError) {
        console.error(
          `[Visit Command] Error saving visitor state for ${visitorId} visiting ${targetId}: [VST-SAVE-ERR]`,
          saveError
        );
        return interaction.editReply({
          content:
            "[VST-SAVE-ERR] Failed to save your location state. Could not visit island.",
        });
      }

      const placedMinions = targetCharacter.island?.placedMinions || [];

      const islandEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.BLURPLE)
        .setTitle(`🏝️ Visiting ${targetCharacter.name}'s Private Island`)
        .addFields({
          name: "Owner",
          value: targetCharacter.name,
          inline: false,
        });

      if (placedMinions.length > 0) {
        const minionStrings = placedMinions.map((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          return `${displayInfo.emoji} **${displayInfo.name}**`;
        });

        const half = Math.ceil(minionStrings.length / 2);
        const col1 = minionStrings.slice(0, half).join("\n");
        const col2 = minionStrings.slice(half).join("\n");
        islandEmbed.addFields(
          { name: "Placed Minions", value: col1 || "\u200B", inline: true },
          { name: "\u200B", value: col2 || "\u200B", inline: true }
        );
      } else {
        islandEmbed.addFields({
          name: "Placed Minions",
          value: "None placed.",
          inline: false,
        });
      }

      islandEmbed.setFooter({
        text: "You are just visiting. No interactions currently possible.",
      });

      try {
        await interaction.editReply({ embeds: [islandEmbed], components: [] });
      } catch (replyError) {
        console.error(
          `[Visit Command] Failed to send visit confirmation reply for ${visitorId} visiting ${targetId}: [VST-RPL-ERR]`,
          replyError
        );
      }
    } catch (error) {
      console.error(
        `[Visit Command Error] User: ${visitorId}, Target: ${targetName || "N/A"} [VST-EXE-ERR]`,
        error
      );

      if (interaction.deferred && !interaction.replied) {
        try {
          await interaction.editReply({
            content:
              "[VST-EXE-ERR] An unexpected error occurred while trying to visit the island. Please report this code.",
          });
        } catch (followUpError) {
          console.error(
            "[Visit Command] Failed to send error follow-up: [VST-FLW-ERR]",
            followUpError
          );
        }
      } else if (!interaction.replied && !interaction.deferred) {
        try {
          await interaction.reply({
            content:
              "[VST-EXE-ERR] An unexpected error occurred. Please report this code.",
            flags: [MessageFlags.Ephemeral],
          });
        } catch (initialReplyError) {
          console.error(
            "[Visit Command] Failed to send initial error reply: [VST-FLW-ERR]",
            initialReplyError
          );
        }
      }
    }
  },
};
