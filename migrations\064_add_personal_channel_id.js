// Migration: 064_add_personal_channel_id.js
// Purpose: Add personal_channel_id column to track each player's dedicated channel

const { dbRun, dbAll } = require("../utils/dbUtils");

async function up() {
  console.log(
    "[Migration 064] Adding personal_channel_id column to players table...",
  );

  try {
    // Check if column already exists
    const columns = await dbAll("PRAGMA table_info(players)");
    const columnExists = columns.some(
      (col) => col.name === "personal_channel_id",
    );

    if (columnExists) {
      console.log(
        "[Migration 064] personal_channel_id column already exists, skipping...",
      );
      return;
    }

    console.log(
      "[Migration 064] Adding personal_channel_id column to players table...",
    );
    await dbRun(`
      ALTER TABLE players 
      ADD COLUMN personal_channel_id TEXT DEFAULT NULL
    `);

    console.log(
      "[Migration 064] Successfully added personal_channel_id column",
    );
  } catch (error) {
    console.error(
      "[Migration 064] Error adding personal_channel_id column:",
      error,
    );
    throw error;
  }
}

async function down() {
  console.log(
    "[Migration 064] Removing personal_channel_id column from players table...",
  );

  try {
    // SQLite doesn't support DROP COLUMN directly, so we'd need to recreate the table
    // For now, we'll just log that this migration cannot be easily reversed
    console.log(
      "[Migration 064] Note: SQLite does not support DROP COLUMN. Manual intervention required to reverse.",
    );
  } catch (error) {
    console.error("[Migration 064] Error in down migration:", error);
    throw error;
  }
}

module.exports = { up, down };
