const { ITEM_RARITY } = require("../gameConfig");

/**
 * Gets the color for a given rarity
 * @param {string} rarity - The rarity string (e.g., 'COMMON', 'RARE', 'EPIC')
 * @returns {string} The hex color code for the rarity
 */
function getRarityColor(rarity) {
  if (!rarity) return "#FFFFFF";

  const rarityKey = rarity.toUpperCase();
  return ITEM_RARITY[rarityKey]?.color || "#FFFFFF";
}

/**
 * Gets the color for an item based on its rarity
 * @param {object} item - The item object
 * @returns {string} The hex color code for the item's rarity
 */
function getItemColor(item) {
  if (!item || !item.rarity) return "#FFFFFF";

  // Support both string rarity and object rarity
  const rarityKey = typeof item.rarity === "string" ? item.rarity : item.rarity;
  return getRarityColor(rarityKey);
}

/**
 * Gets the color for a pet based on its rarity
 * @param {object} pet - The pet object
 * @returns {string} The hex color code for the pet's rarity
 */
function getPetColor(pet) {
  if (!pet || !pet.rarity) return "#FFFFFF";

  return getRarityColor(pet.rarity);
}

module.exports = {
  getRarityColor,
  getItemColor,
  getPetColor,
};
