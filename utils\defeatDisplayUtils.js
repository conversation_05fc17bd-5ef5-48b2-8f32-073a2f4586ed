const { EmbedBuilder } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");

/**
 * Create a unified defeat embed for all combat scenarios
 * Makes all defeat displays consistent with the combat system look
 * @param {Object} options - Defeat embed options
 * @param {string} options.skillName - The skill where death occurred (combat, fishing, mining)
 * @param {Object} options.defeatedBy - { name, emoji } of what defeated the player
 * @param {number} options.completedActions - Number of actions completed before defeat
 * @param {number} options._totalActions - Total actions that were planned (unused but kept for future use)
 * @param {string} options.actionDescription - Description of what was being done
 * @returns {EmbedBuilder} Unified defeat embed
 */
function createUnifiedDefeatEmbed({
  skillName,
  defeatedBy,
  completedActions = 0,
  _totalActions = 1,
  actionDescription = null,
}) {
  const defeaterEmoji = defeatedBy?.emoji || "💀";
  const defeaterName = defeatedBy?.name || "Unknown";

  // Create action description based on skill type
  let description;
  let footerText;

  switch (skillName) {
    case "combat":
      description = `Your combat sequence against ${defeaterEmoji} **${defeaterName}** ended after **${completedActions}** fight(s) as you were defeated.`;
      footerText = "Defeated in Combat.";
      break;

    case "fishing":
      if (actionDescription) {
        description = `Your ${actionDescription} ended as you were defeated by ${defeaterEmoji} **${defeaterName}**.`;
      } else {
        description = `Your fishing action ended as you were defeated by ${defeaterEmoji} **${defeaterName}**.`;
      }
      footerText = "Defeated by Sea Creature.";
      break;

    case "mining":
      if (actionDescription) {
        description = `Your ${actionDescription} ended as you were defeated by ${defeaterEmoji} **${defeaterName}**.`;
      } else {
        description = `Your mining action ended as you were defeated by ${defeaterEmoji} **${defeaterName}**.`;
      }
      footerText = "Defeated while Mining.";
      break;

    default:
      // Generic skill format
      if (actionDescription) {
        description = `Your ${actionDescription} ended as you were defeated by ${defeaterEmoji} **${defeaterName}**.`;
      } else {
        description = `Your ${skillName} action ended as you were defeated by ${defeaterEmoji} **${defeaterName}**.`;
      }
      footerText = `Defeated during ${skillName}.`;
      break;
  }

  return new EmbedBuilder()
    .setColor(EMBED_COLORS.ERROR)
    .setDescription(description)
    .setFooter({ text: footerText });
}

module.exports = {
  createUnifiedDefeatEmbed,
};
