const fs = require("fs");
const path = require("path");

class CommandToggleManager {
  constructor() {
    this.configPath = path.join(__dirname, "..", "config.json");
    this.config = null;
    this.loadConfig();
  }

  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, "utf8");
      this.config = JSON.parse(configData);

      // initialize disabledCommands if it doesn't exist
      if (!this.config.disabledCommands) {
        this.config.disabledCommands = {};
        this.autoDetectCommands();
        this.saveConfig();
      } else {
        // auto-detect new commands and add them
        this.autoDetectCommands();
      }
    } catch (error) {
      console.error("[CommandToggleManager] Failed to load config:", error);
      throw error;
    }
  }

  autoDetectCommands() {
    try {
      const commandsDir = path.join(__dirname, "..", "commands");
      const commandFiles = fs
        .readdirSync(commandsDir)
        .filter((file) => file.endsWith(".js"));

      let hasNewCommands = false;

      for (const file of commandFiles) {
        const commandName = file.replace(".js", "");

        // skip utility/maintenance commands that shouldn't be togglable
        if (
          [
            "migrate-stop-buttons",
            "validate-channels",
            "createcharacter",
          ].includes(commandName)
        ) {
          continue;
        }

        // add new commands as enabled by default
        if (!(commandName in this.config.disabledCommands)) {
          this.config.disabledCommands[commandName] = false; // false = enabled
          hasNewCommands = true;
        }
      }

      if (hasNewCommands) {
        this.saveConfig();
        console.log(
          "[CommandToggleManager] Auto-detected new commands and added to config"
        );
      }
    } catch (error) {
      console.error(
        "[CommandToggleManager] Failed to auto-detect commands:",
        error
      );
    }
  }

  saveConfig() {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 4));
    } catch (error) {
      console.error("[CommandToggleManager] Failed to save config:", error);
      throw error;
    }
  }

  isCommandDisabled(commandName) {
    return this.config?.disabledCommands?.[commandName] === true;
  }

  toggleCommand(commandName, disabled) {
    if (!this.config.disabledCommands) {
      this.config.disabledCommands = {};
    }

    this.config.disabledCommands[commandName] = disabled;
    this.saveConfig();
  }

  toggleMultipleCommands(commandStates) {
    if (!this.config.disabledCommands) {
      this.config.disabledCommands = {};
    }

    for (const [commandName, disabled] of Object.entries(commandStates)) {
      this.config.disabledCommands[commandName] = disabled;
    }

    this.saveConfig();
  }

  getAllCommands() {
    return { ...this.config.disabledCommands };
  }

  getEnabledCommands() {
    const commands = this.getAllCommands();
    return Object.entries(commands)
      .filter(([, disabled]) => !disabled)
      .map(([name]) => name);
  }

  getDisabledCommands() {
    const commands = this.getAllCommands();
    return Object.entries(commands)
      .filter(([, disabled]) => disabled)
      .map(([name]) => name);
  }

  // reload config from disk (useful for hot-reloading)
  reload() {
    this.loadConfig();
  }
}

// singleton instance
const commandToggleManager = new CommandToggleManager();

module.exports = commandToggleManager;
