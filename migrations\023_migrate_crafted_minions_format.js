const { dbGet } = require("../utils/dbUtils");

// Rename function to 'up' to match runner expectations
async function up(db, playerDataUtils) {
  // Runner passes db connection and utils
  console.log("[Migration 023] Starting crafted_minions format migration...");
  let playersUpdated = 0;
  let playersSkipped = 0;
  let playersFailed = 0;

  // Use passed utils
  const allPlayerIds = await playerDataUtils.getAllPlayerIds();
  if (!allPlayerIds || allPlayerIds.length === 0) {
    console.log("[Migration 023] No players found to migrate.");
    return;
  }

  console.log(`[Migration 023] Found ${allPlayerIds.length} players to check.`);

  for (const discordId of allPlayerIds) {
    try {
      // Use passed db connection via dbGet
      const playerRow = await dbGet(
        "SELECT crafted_minions_json FROM players WHERE discord_id = ?",
        [discordId],
      );

      if (!playerRow || !playerRow.crafted_minions_json) {
        // Silent skip - no crafted minions data
        playersSkipped++;
        continue;
      }

      let craftedMinionsData;
      try {
        craftedMinionsData = JSON.parse(playerRow.crafted_minions_json);
      } catch (e) {
        console.error(
          `[Migration 023] Error parsing crafted_minions_json for player ${discordId}. Skipping. JSON: ${playerRow.crafted_minions_json}`,
          e,
        );
        playersFailed++;
        continue;
      }

      // Check if migration is needed (is it an array? does the first element lack '_T'?)
      if (
        Array.isArray(craftedMinionsData) &&
        craftedMinionsData.length > 0 &&
        typeof craftedMinionsData[0] === "string" &&
        !craftedMinionsData[0].includes("_T")
      ) {
        console.log(`[Migration 023] Migrating player ${discordId}...`);
        const migratedCraftedMinions = craftedMinionsData.map(
          (baseKey) => `${baseKey}_T1`,
        );

        // Use passed utils
        const fullPlayerData = await playerDataUtils.getPlayerData(discordId);
        if (!fullPlayerData) {
          console.error(
            `[Migration 023] Failed to reload full player data for ${discordId} during save. Skipping.`,
          );
          playersFailed++;
          continue;
        }
        fullPlayerData.craftedMinions = migratedCraftedMinions; // Update the parsed array
        // Use passed utils
        await playerDataUtils.savePlayerData(discordId, fullPlayerData); // Save the whole object
        console.log(
          `[Migration 023] Successfully migrated player ${discordId}. New count: ${migratedCraftedMinions.length}`,
        );
        playersUpdated++;
      } else {
        // Silent skip - already migrated or empty
        playersSkipped++;
      }
    } catch (error) {
      console.error(
        `[Migration 023] Error processing player ${discordId}:`,
        error,
      );
      playersFailed++;
    }
  }

  console.log("[Migration 023] Migration finished.");
  console.log(
    `[Migration 023] Summary: Updated: ${playersUpdated}, Skipped: ${playersSkipped}, Failed: ${playersFailed}`,
  );
}

// Export as 'up'
module.exports = { up };
