const { dbAll } = require("./dbUtils");
const { safeJsonParse } = require("./playerDataManager");
const { getLevelFromExp } = require("./expFunctions");

/**
 * gets the #1 player name for a specific skill (reuses leaderboard logic)
 * @param {string} skillName - the skill name to check (e.g., 'fishing', 'mining', 'farming', 'foraging')
 * @returns {Promise<string>} the name of the top player, or "ERROR" if can't load
 */
async function getTopPlayerForSkill(skillName) {
  try {
    // reuse the exact same logic from leaderboards command
    const allPlayersData = await dbAll(
      "SELECT discord_id, name, skills_json FROM players"
    );

    if (!allPlayersData || allPlayersData.length === 0) {
      return "ERROR";
    }

    const leaderboardData = allPlayersData
      .map((player) => {
        const skills = safeJsonParse(player.skills_json, {});
        const skillData = skills[skillName] || { exp: 0.0 };
        const rawExp = skillData.exp || 0;
        const levelInfo = getLevelFromExp(rawExp);

        return {
          name: player.name,
          discord_id: player.discord_id,
          value: {
            level: levelInfo.level,
            totalExp: rawExp,
          },
        };
      })
      .sort((a, b) => {
        // sort descending: first by level, then by total experience
        if (b.value.level !== a.value.level) {
          return b.value.level - a.value.level;
        } else {
          return b.value.totalExp - a.value.totalExp;
        }
      });

    return leaderboardData.length > 0 ? leaderboardData[0].name : "ERROR";
  } catch (error) {
    console.error(
      `[getTopPlayerForSkill] Error fetching top player for ${skillName}:`,
      error
    );
    return "ERROR";
  }
}

module.exports = {
  getTopPlayerForSkill,
};
