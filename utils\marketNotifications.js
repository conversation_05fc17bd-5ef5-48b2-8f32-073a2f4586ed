const { EmbedBuilder } = require("discord.js");
const { dbGet } = require("./dbUtils");
const { EMBED_COLORS } = require("../gameConfig");
const { getPlayerData } = require("./playerDataManager");
const configManager = require("./configManager");

/**
 * Sends a notification to a player's last active channel about a completed market order
 * Only sends notifications when orders are completely filled, not for partial fills
 */
async function sendMarketOrderNotification(
  client,
  userId,
  orderType,
  itemKey,
  quantity,
  pricePerUnit,
  isFullyFilled,
  originalQuantity,
  remainingQuantity = null
) {
  try {
    // get player's notification preferences
    const playerData = await getPlayerData(userId);
    if (!playerData) {
      console.log(
        `[Market Notifications] Player data not found for user ${userId}`
      );
      return;
    }

    const marketNotificationMode =
      playerData.settings?.marketNotificationMode || "full";
    const marketPingsEnabled = playerData.settings?.marketPings !== false; // default to true

    // check if notifications are disabled
    if (marketNotificationMode === "none") {
      return;
    }

    // check notification mode preferences
    if (marketNotificationMode === "full" && !isFullyFilled) {
      return; // only notify on full completion
    }
    // if mode is 'all', notify for both partial and full (but we're only calling this for full anyway)

    // get the player's last active channel
    const channelData = await dbGet(
      "SELECT channel_id FROM player_last_active_channel WHERE discord_id = ?",
      [userId]
    );

    if (!channelData) {
      console.log(
        `[Market Notifications] No last active channel found for user ${userId}`
      );
      return;
    }

    // fetch the channel
    const channel = await client.channels.fetch(channelData.channel_id);
    if (!channel || !channel.isTextBased()) {
      console.log(
        `[Market Notifications] Invalid channel ${channelData.channel_id} for user ${userId}`
      );
      return;
    }

    // get item info for display
    const itemData = configManager.getItem(itemKey);
    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "📦";

    // determine notification text based on order type and fill status
    const totalValue = quantity * pricePerUnit;
    let description;

    if (orderType === "sell") {
      if (isFullyFilled) {
        description = `Your **sell order** for **${quantity}x ${itemEmoji} ${itemName}** has been completely filled! You received **${totalValue.toLocaleString()}** coins.`;
      } else {
        // For partial fills, calculate how much has been filled so far
        let progressText = "";
        if (originalQuantity && remainingQuantity !== null) {
          const filledAmount = originalQuantity - remainingQuantity;
          progressText = ` (**${filledAmount}/${originalQuantity}**)`;
        }
        description = `Your **sell order** for **${itemEmoji} ${itemName}** was partially filled! **${quantity}x** sold for **${totalValue.toLocaleString()}** coins${progressText}.`;
      }
    } else if (orderType === "buy") {
      if (isFullyFilled) {
        description = `Your **buy order** for **${quantity}x ${itemEmoji} ${itemName}** has been completely filled! You received **${quantity}x ${itemEmoji} ${itemName}**.`;
      } else {
        let progressText = "";
        if (originalQuantity && remainingQuantity !== null) {
          const filledAmount = originalQuantity - remainingQuantity;
          progressText = ` (**${filledAmount}/${originalQuantity}**)`;
        }
        description = `Your **buy order** for **${itemEmoji} ${itemName}** was partially filled! You received **${quantity}x ${itemEmoji} ${itemName}** for **${totalValue.toLocaleString()}** coins${progressText}.`;
      }
    }

    // create the embed
    const embed = new EmbedBuilder()
      .setDescription(description)
      .setColor(EMBED_COLORS.GREEN);

    // send the notification
    const messageContent = marketPingsEnabled ? `<@${userId}>` : "";

    await channel.send({
      content: messageContent,
      embeds: [embed],
    });

    console.log(
      `[Market Notifications] Notified user ${userId} about ${orderType} order completion for ${itemKey} (ping: ${marketPingsEnabled})`
    );
  } catch (error) {
    console.error(
      `[Market Notifications] Failed to notify user ${userId}:`,
      error
    );
  }
}

module.exports = {
  sendMarketOrderNotification,
};
