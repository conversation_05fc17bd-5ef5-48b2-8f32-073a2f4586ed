const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const { EMBED_COLORS, STATS } = require("../gameConfig");
//const { getRankPrefix } = require("../utils/rankUtils");
const configManager = require("../utils/configManager");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("effects")
    .setDescription("View your active potion effects"),

  async execute(interaction) {
    try {
      await interaction.deferReply();

      const character = await getPlayerData(interaction.user.id);
      if (!character) {
        return interaction.editReply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
        });
      }

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.editReply({
          content: "You don't have permission to use this command.",
        });
      }

      const {
        getActiveEffects,
        cleanupExpiredEffects,
        isGodPotionActive,
        getGodPotionRemaining,
        computeGodPotionAggregatedEffects,
      } = require("../utils/potionEffects");

      // Clean up expired effects and get active ones
      cleanupExpiredEffects(character);
      const validEffects = getActiveEffects(character);
      const godActive = isGodPotionActive(character);

      // Check for booster cookie effect
      const {
        isBoosterCookieActive,
        getBoosterCookieTimeRemaining,
        formatTimeRemaining,
      } = require("../utils/boosterCookieManager");

      const hasBoosterCookie = isBoosterCookieActive(character);
      const hasActiveEffects = validEffects.length > 0;

      if (!hasBoosterCookie && !hasActiveEffects && !godActive) {
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setTitle("Active Effects")
          .setDescription("You have no active effects.")
          .setFooter({
            text: "Use /use <potion> or consume a Booster Cookie to gain effects",
          });

        return interaction.editReply({ embeds: [embed] });
      }

      // Get all items to find potion emojis
      const allItems = configManager.getAllItems();

      let effectsDisplay = "";

      // Add booster cookie effect first if active
      if (hasBoosterCookie) {
        const timeRemaining = getBoosterCookieTimeRemaining(character);
        const timeText = formatTimeRemaining(timeRemaining);

        effectsDisplay += `<a:booster_cookie:1400058756183887932> \`${timeText}\` **Booster Cookie**\n`;
        effectsDisplay += `<:purse_coins:1367849116033482772> \`Keep Coins on Death\`\n`;
        effectsDisplay += `<:wisdom_stat:1369101731278880849> \`+25 Global Wisdom\`\n`;
        effectsDisplay += `<:stat_magic_find:1399855928894951425> \`+15 Magic Find\`\n`;

        if (hasActiveEffects || godActive) {
          effectsDisplay += "\n"; // separator between booster cookie and potion effects
        }
      }

      // Add God Potion if active (shows compact summary like booster cookie)
      if (godActive) {
        const remaining = getGodPotionRemaining(character);
        // Format time
        const totalSeconds = Math.floor(remaining / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        const timeText =
          hours > 0
            ? `${hours}h ${minutes}m ${seconds}s`
            : minutes > 0
              ? `${minutes}m ${seconds}s`
              : `${seconds}s`;

        const aggregated = computeGodPotionAggregatedEffects(configManager);
        // Header line
        effectsDisplay += `<:God_Potion:1403164753672536205> \`${timeText}\` **God Potion**\n`;
        // Per-line effects with source potion (emoji + bold name)
        const lines = [];
        for (const [family, entry] of Object.entries(aggregated.details)) {
          for (const [stat, value] of Object.entries(entry.effects)) {
            const statConfig = STATS[stat];
            const statName = statConfig?.name || stat;
            const statEmoji = statConfig?.emoji || "📈";
            const potEmoji = entry.emoji || "🧪";
            lines.push(
              `${statEmoji} \`+${value} ${statName}\`: ${potEmoji} **${entry.name}**`
            );
          }
        }
        if (lines.length > 0) {
          effectsDisplay += lines.join("\n") + "\n";
        }
        if (hasActiveEffects) effectsDisplay += "\n"; // separator before individual effects
      }

      // Add individual potion effects only when God Potion not active
      if (!godActive && hasActiveEffects) {
        const potionEffectsDisplay = validEffects
          .map((effect) => {
            // Format remaining time
            const totalSeconds = Math.floor(effect.remainingTime / 1000);
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            let timeText = "";
            if (hours > 0) {
              timeText = `${hours}h ${minutes}m ${seconds}s`;
            } else if (minutes > 0) {
              timeText = `${minutes}m ${seconds}s`;
            } else {
              timeText = `${seconds}s`;
            }

            // Find the potion item by name to get its emoji
            let potionEmoji = "🧪"; // Default fallback
            const potionItem = Object.values(allItems).find(
              (item) =>
                item.name === effect.name &&
                item.consumable?.type === "POTION_EFFECT"
            );
            if (potionItem && potionItem.emoji) {
              potionEmoji = potionItem.emoji;
            }

            // Format effects with stat emojis
            const effectsText = Object.entries(effect.effects)
              .map(([stat, value]) => {
                const statConfig = STATS[stat];
                const statName = statConfig?.name || stat;
                const statEmoji = statConfig?.emoji || "📈";
                return `${statEmoji} \`+${value} ${statName}\``;
              })
              .join(" ");

            // Format: <potionEmoji> `1h 0m 18s` **Regeneration Potion** :healthregen: `+15 Health Regen`
            return `${potionEmoji} \`${timeText}\` **${effect.name}** ${effectsText}`;
          })
          .join("\n");

        effectsDisplay += potionEffectsDisplay;
      }
      //const rankPrefix = getRankPrefix(character);
      const title = `${character.name}'s Active Effects`;

      // Count total active effects (booster cookie + potions)
      const totalEffectsCount =
        (hasBoosterCookie ? 1 : 0) +
        (godActive ? 1 : 0) +
        (godActive ? 0 : validEffects.length);

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(title)
        .setDescription(effectsDisplay)
        .setFooter({
          text: `${totalEffectsCount} active effect${totalEffectsCount !== 1 ? "s" : ""}`,
        });

      return interaction.editReply({ embeds: [embed] });
    } catch (error) {
      console.error("[Effects Command Error]", error);
      return interaction.editReply({
        content:
          "An error occurred while retrieving your effects. Please try again.",
      });
    }
  },
};
