// Migration: 057_fix_garden_visitors_table.js
// Purpose: Fix the active_garden_visitors table to use discord_id instead of user_id

module.exports.up = async function up(db) {
  return new Promise((resolve, reject) => {
    // Drop the existing table if it exists
    db.run("DROP TABLE IF EXISTS active_garden_visitors", (err) => {
      if (err) {
        console.error(
          "[Migration 057] Failed to drop active_garden_visitors table:",
          err.message,
        );
        reject(err);
        return;
      }

      // Create the table with correct column name
      db.run(
        `
                CREATE TABLE IF NOT EXISTS active_garden_visitors (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    discord_id TEXT NOT NULL,
                    visitor_key TEXT NOT NULL,
                    visitor_rarity TEXT NOT NULL,
                    request_items TEXT NOT NULL,
                    reward_garden_xp INTEGER NOT NULL,
                    reward_farming_xp INTEGER NOT NULL,
                    reward_copper INTEGER NOT NULL,
                    bonus_rewards TEXT DEFAULT "[]",
                    created_at INTEGER NOT NULL,
                    FOREIGN KEY (discord_id) REFERENCES players (discord_id)
                )
            `,
        (err2) => {
          if (err2) {
            console.error(
              "[Migration 057] Failed to create active_garden_visitors table:",
              err2.message,
            );
            reject(err2);
            return;
          }

          console.log(
            "[Migration 057] Fixed active_garden_visitors table with correct discord_id column.",
          );
          resolve();
        },
      );
    });
  });
};
