const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
const { calculateTotalStat } = require("../utils/statCalculations.js");
const configManager = require("../utils/configManager");
const { runFishingEncounter } = require("../utils/fishingEngine");

const { calculateInstanceStats } = require("../utils/mobUtils.js");
const { calculateRewardsFromMob } = require("../utils/combatEngine.js");
// Removed old stop system imports - using simple abort system now

const {
  getAbortSignal,
  cancellableDelay,
} = require("../utils/instantStopUtils");
// Removed: const items = require("../data/items.json");

// Generic helper functions for stat calculations
function getStatValue(character, statName, fallbackValue = 0) {
  return character[statName] || fallbackValue;
}

function calculateStatFromCharacter(character, statName, defaultValue = 0) {
  if (typeof character[statName] === "number") {
    return character[statName];
  }

  const stats = character.stats || {};
  return stats[statName] || defaultValue;
}

function applyWisdomMultiplier(baseExp, wisdom) {
  return Math.floor(baseExp * (1 + wisdom / 100));
}

function applyFortuneToDrops(baseDrops, fortune) {
  // Fortune system: every 100 fortune = guaranteed extra base drop
  // Remainder = percentage chance for another base drop
  const guaranteedExtraBaseDrop = Math.floor(fortune / 100);
  const chanceForOneMoreBaseDrop = (fortune % 100) / 100;

  let totalDrops = baseDrops + guaranteedExtraBaseDrop * baseDrops;
  if (Math.random() < chanceForOneMoreBaseDrop) {
    totalDrops += baseDrops;
  }

  return totalDrops;
}

// Generic fortune and sweep calculation functions
function calculateFortune(preCalculatedStatsOrCharacter, statName) {
  if (
    preCalculatedStatsOrCharacter &&
    typeof preCalculatedStatsOrCharacter[statName] === "number"
  ) {
    return preCalculatedStatsOrCharacter[statName];
  }

  if (
    preCalculatedStatsOrCharacter &&
    preCalculatedStatsOrCharacter.discordId
  ) {
    return calculateStatFromCharacter(
      preCalculatedStatsOrCharacter,
      statName,
      0
    );
  }

  return 0;
}

function calculateSweep(preCalculatedStatsOrCharacter, statName) {
  if (
    preCalculatedStatsOrCharacter &&
    typeof preCalculatedStatsOrCharacter[statName] === "number"
  ) {
    return preCalculatedStatsOrCharacter[statName];
  }

  if (
    preCalculatedStatsOrCharacter &&
    preCalculatedStatsOrCharacter.discordId
  ) {
    return calculateStatFromCharacter(
      preCalculatedStatsOrCharacter,
      statName,
      0
    );
  }

  return 0;
}

// Generic animation function
function createSkillAnimation(toolEmoji, targetEmoji) {
  return async function (
    messageId,
    channel,
    embed,
    animationTime,
    stopRequestMap,
    actionId,
    auxConsumed,
    resourceEmoji
  ) {
    const displayEmoji = resourceEmoji || targetEmoji;
    const steps = 2;
    const trackLength = 15;
    const stepDuration = animationTime / steps;

    for (let i = 0; i <= steps; i++) {
      const toolPosition = Math.floor((i / steps) * trackLength);
      const movingPart = `${" ".repeat(toolPosition)}${toolEmoji}${" ".repeat(trackLength - toolPosition)}`;
      embed.setDescription(`\`${movingPart}\` ${displayEmoji}`);

      // Check for instant abort
      const signal = getAbortSignal(actionId);
      if (signal && signal.aborted) {
        console.log(
          `[Animation] Action ${actionId} cancelled instantly during animation`
        );
        break;
      }

      try {
        await channel.messages.edit(messageId, { embeds: [embed] });
      } catch (editError) {
        console.log(
          `[Animation] Minor error editing animation message (action ${actionId}): ${editError.message}`
        );
        break;
      }

      if (i < steps) {
        // Use cancellable delay that can be interrupted immediately
        try {
          await cancellableDelay(stepDuration, signal);
        } catch (error) {
          if (error.name === "AbortError") {
            console.log(
              `[Animation] Action ${actionId} cancelled during delay`
            );
            break;
          }
          throw error;
        }
      }
    }
  };
}

// Specific animation functions
// const fishingAnimationGeneric = createSkillAnimation("🎣", "🐟"); // Currently unused
// const farmingAnimationGeneric = createSkillAnimation("👨‍🌾", "🌾"); // Currently unused
// const miningAnimationGeneric = createSkillAnimation("⛏️", "🪨"); // Currently unused
// const foragingAnimationGeneric = createSkillAnimation("🪓", "🌳"); // Currently unused

// Keep skill-specific functions LOCAL to their command files for now,
// but move the config objects here.

// --- Fishing Config ---
function calculateFishingTime(
  preCalculatedStatsOrCharacter,
  auxResourceConsumed
) {
  const MAX_FISHING_TIME = 10000; // 10 seconds at 0 speed
  const MIN_FISHING_TIME = 2500; // 2.5 seconds at 300 speed
  const MAX_FISHING_SPEED = 300; // Cap at 300 fishing speed

  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.baseFishingSpeed !== "number"
  ) {
    throw new Error(
      "[calculateFishingTime] Pre-calculated fishing speed required - no fallback support"
    );
  }

  let fishingSpeed = preCalculatedStatsOrCharacter.baseFishingSpeed;

  // Add bait bonus if bait was consumed - now dynamic based on bait type
  if (
    auxResourceConsumed &&
    typeof auxResourceConsumed === "object" &&
    auxResourceConsumed.fishingSpeedBonus
  ) {
    fishingSpeed += auxResourceConsumed.fishingSpeedBonus;
  }
  // Note: Legacy boolean format (auxResourceConsumed === true) is no longer supported
  // All bait consumption now goes through enhancedBaitManager with proper object format

  // Cap fishing speed at maximum
  fishingSpeed = Math.min(fishingSpeed, MAX_FISHING_SPEED);

  // Calculate time using linear scaling between MAX and MIN times
  const timeRange = MAX_FISHING_TIME - MIN_FISHING_TIME;
  const timeReduction = (timeRange * fishingSpeed) / MAX_FISHING_SPEED;
  const adjustedTime = Math.round(MAX_FISHING_TIME - timeReduction);

  return adjustedTime;
}

const fishingAnimation = async (
  messageId,
  channel,
  embed,
  fishingTime, // This will be the adjusted time based on fishing speed
  stopRequestMap,
  actionId,
  auxConsumed,
  resourceEmoji,
  timeRemainingMsBeforeCycle,
  character = null,
  resourceKey = null,
  isFirstCycle = false
  /* _isResumption = false */
) => {
  const fishEmoji = "🐟";
  const rodEmoji = "🎣";
  const steps = 2;
  const trackLength = 15;

  // keep original dynamic timing based on fishing speed as akin designed
  const stepDuration = fishingTime / steps;

  console.log(
    `[Animation][Fish] Action ${actionId}: fishingTime=${fishingTime}ms, stepDuration=${stepDuration}ms`
  );
  console.log(
    `[Animation][Fish] Starting animation for action ${actionId} at ${Date.now()}`
  );

  // Enhanced validation for resumption scenarios
  if (!channel || !messageId) {
    console.warn(
      `[Animation][Fish] No channel or message ID provided for action ${actionId}, skipping animation`
    );
    return;
  }

  // Bait status for display - now supports multiple bait types and Caster saves
  let baitStatus;
  if (auxConsumed && typeof auxConsumed === "object" && auxConsumed.baitKey) {
    // Dynamic bait display based on actual bait used
    const allItems = configManager.getAllItems();
    const baitData = allItems[auxConsumed.baitKey];
    const baitEmoji = baitData?.emoji || "❓";
    const baitName = baitData?.name || auxConsumed.baitKey;

    if (auxConsumed.casterSaved) {
      baitStatus = `${baitEmoji} **${baitName}** not used (Caster saved!)`;
    } else {
      baitStatus = `${baitEmoji} **${baitName}** used`;
    }
  } else if (auxConsumed === true) {
    // Legacy fallback for old boolean format
    baitStatus = "<:fish_bait:1270598967284994101> **Fish Bait** used";
  } else {
    baitStatus = "❌ **No bait** used";
  }

  for (let i = 0; i <= steps; i++) {
    // Check for instant abort before each animation step
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Fish] Action ${actionId} cancelled instantly during animation step ${i}`
      );
      break;
    }

    // Skip the first frame edit only for the very first cycle (position 0 is already shown in initial message)
    if (i === 0 && isFirstCycle) {
      console.log(
        `[Animation][Fish] Skipping first frame edit for action ${actionId} (isFirstCycle=true, i=0)`
      );
      // small delay before starting movement for first cycle
      if (i < steps) {
        try {
          await cancellableDelay(stepDuration, signal);
        } catch (error) {
          if (error.name === "AbortError") {
            console.log(
              `[Animation][Fish] Animation cancelled during initial delay for action ${actionId}`
            );
            break;
          }
          throw error;
        }
      }
      continue;
    }

    console.log(
      `[Animation][Fish] Processing frame ${i} for action ${actionId} (isFirstCycle=${isFirstCycle})`
    );

    // Calculate fish position (fish moves from left to right)
    const fishPosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(fishPosition)}${fishEmoji}${" ".repeat(trackLength - fishPosition)}`;
    const descriptionText = `\`${movingPart}\` ${rodEmoji}\n\n${baitStatus}`;

    // Create updated embed for animation
    const { updateAnimationEmbed } = require("../utils/animationContainers");
    const {
      ButtonBuilder,
      ButtonStyle,
      ActionRowBuilder,
    } = require("discord.js");

    // Get current bait info for bait swap button (include remaining amount so label persists)
    let currentBaitInfo = { emoji: "🎣", name: "No Bait", baitKey: null };
    if (auxConsumed && typeof auxConsumed === "object" && auxConsumed.baitKey) {
      const allItems = configManager.getAllItems();
      const baitData = allItems[auxConsumed.baitKey];
      currentBaitInfo = {
        emoji: baitData?.emoji || "🎣",
        name: baitData?.name || auxConsumed.baitKey,
        baitKey: auxConsumed.baitKey,
      };
    }

    // helper for compact formatting (match fishingButtons logic)
    const formatQty = (n) => {
      if (n == null) return "0";
      if (n < 1000) return n.toString();
      if (n < 1_000_000)
        return +(n / 1000).toFixed(1).replace(/\.0$/, "") + "k";
      return +(n / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
    };

    // Create bait swap button (with quantity if bait active)
    const baitSwapButton = new ButtonBuilder()
      .setCustomId(`bait_swap:${actionId}:${character?.discord_id}`)
      .setEmoji(currentBaitInfo.emoji)
      .setStyle(ButtonStyle.Secondary);

    if (currentBaitInfo.baitKey) {
      try {
        const { enhancedBaitManager } = require("../utils/enhancedBaitManager");
        const available = await enhancedBaitManager.getAvailableBaits(
          character?.discord_id
        );
        const remaining = available[currentBaitInfo.baitKey] ?? 0;
        baitSwapButton.setLabel(formatQty(remaining));
      } catch {
        /* ignore amount errors */
      }
    }

    const result = updateAnimationEmbed(
      embed.data.title || "Fishing",
      descriptionText,
      actionId,
      character?.discord_id,
      "Fishing",
      embed.data.color || 0x2f3136,
      embed.data.footer?.text,
      [baitSwapButton]
    );

    // Enhanced message editing with resumption-safe error handling and rate limiting
    try {
      const { queueMessageEdit } = require("../utils/animationQueue");
      const rows =
        result.buttons && result.buttons.length
          ? [new ActionRowBuilder().addComponents(...result.buttons)]
          : [];
      await queueMessageEdit(channel, messageId, {
        embeds: [result.embed],
        components: rows,
      });
    } catch (editError) {
      console.log(
        `[Animation][Fish] Error editing animation message (action ${actionId}): ${editError.message}`
      );

      // For resumption scenarios, certain errors are expected and shouldn't break the animation
      if (
        editError.code === 10008 ||
        editError.code === 50001 ||
        editError.code === 50013
      ) {
        console.log(
          `[Animation][Fish] Message access lost during resumption for action ${actionId}, ending animation gracefully`
        );
        break;
      } else if (editError.code === 50005) {
        // Cannot edit a message authored by another user - this happens during resumption
        // when a different worker tries to edit a message created by the original worker
        console.log(
          `[Animation][Fish] Cannot edit message authored by another user (resumption scenario) - animation will continue without visual updates for action ${actionId}`
        );
        break;
      }

      // For other errors, try to continue the animation but don't crash
      if ((i === 0 && !isFirstCycle) || (i === 1 && isFirstCycle)) {
        // first actual animation step
        console.warn(
          `[Animation][Fish] Animation message failed on first step for action ${actionId}, continuing silently`
        );
      }
      break;
    }

    // Check for instant abort after each animation step
    const signal2 = getAbortSignal(actionId);
    if (signal2 && signal2.aborted) {
      console.log(
        `[Animation][Fish] Action ${actionId} cancelled instantly after animation step ${i}`
      );
      break;
    }

    // Wait for next step (except on last iteration)
    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Fish] Animation cancelled during delay for action ${actionId}`
          );
          break;
        }
        throw error;
      }
    }
  }
};

// --- Added: Custom wrapper function for fishing to ensure petToAdd is properly passed through ---
async function goFishing(character, interaction, preCalculatedStats = null) {
  const result = await runFishingEncounter(
    character,
    interaction,
    preCalculatedStats
  );

  // Ensure the result object has all necessary properties
  // Handle both droppedItems (from sea creatures) and items (from treasure/normal fish)
  const allDroppedItems = result.droppedItems || result.items || [];

  return {
    droppedItems: allDroppedItems, // Unified drops array
    exp: result.exp || 0,
    coins: result.coins || 0,
    seaCreatureKey: result.seaCreatureKey,
    mobName: result.mobName,
    mobEmoji: result.mobEmoji,
    defeated: result.defeated || false,
    playerDefeated: result.defeated || false,
    defeatedBy: result.defeatedBy,
    finalCharacterState: result.finalCharacterState,
    petToAdd: result.petToAdd,
    fled: result.fled, // Preserve fled status to prevent tracking fled encounters as defeated
  };
}

const fishingSkillConfig = {
  skillName: "fishing",
  actionName: "Fishing",
  resourceNameSingular: "fish", // UI display name
  resourceKeyParamName: null, // No resource parameter for fishing
  emoji: "<:raw_fish:1270599030384230450>",
  colors: {
    singleAction: EMBED_COLORS.LIGHT_GREEN,
    multiActionLoop: EMBED_COLORS.LIGHT_GREEN,
    multiActionStopped: EMBED_COLORS.ORANGE,
    multiActionComplete: EMBED_COLORS.PASTEL_GREEN,
  },
  calculateTimeFn: calculateFishingTime,
  animationFn: fishingAnimation,
  goFn: goFishing,
  consumeResourceFn: async (userId, character) => {
    const { enhancedBaitManager } = require("../utils/enhancedBaitManager");

    try {
      const result = await enhancedBaitManager.reserveBait(userId);

      if (result.success) {
        return {
          character, // Character doesn't change in this system
          baitUsedKey: result.baitKey,
          auxResourceConsumed: {
            baitKey: result.baitKey,
            fishingSpeedBonus: result.fishingSpeed,
            casterSaved: result.casterSaved,
          },
        };
      } else {
        return {
          character,
          baitUsedKey: false,
          auxResourceConsumed: false,
        };
      }
    } catch (error) {
      console.error("[Fishing] Error in consumeResourceFn:", error);
      return {
        character,
        baitUsedKey: false,
        auxResourceConsumed: false,
      };
    }
  },
};

// --- Farming Config ---
function calculateFarmingTime() {
  // For now, farming time is static, but we maintain the same signature for consistency
  return 2000; // Reduced to 2 seconds per action (same as foraging)
}

function calculateFarmingFortune(preCalculatedStatsOrCharacter) {
  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.farmingFortune !== "number"
  ) {
    throw new Error(
      "[calculateFarmingFortune] Pre-calculated farming fortune required - no fallback support"
    );
  }
  return preCalculatedStatsOrCharacter.farmingFortune;
}

async function goFarming(
  character,
  cropItemKeyRaw,
  preCalculatedStats = null,
  interaction = null
) {
  // Require pre-calculated stats - no fallback
  if (!preCalculatedStats) {
    throw new Error(
      "[goFarming] Pre-calculated stats required - no fallback support"
    );
  }

  const cropItemKey = cropItemKeyRaw.toUpperCase();
  const cropData = configManager.getAllItems()[cropItemKey];
  if (!cropData) {
    console.error(`[goFarming] Crop data not found for key: ${cropItemKey}`);
    return { droppedItems: [], exp: 0 };
  }
  const result = {
    droppedItems: [],
    exp: 0,
  };

  // Use pre-calculated stats - no fallback calculations
  const farmingFortune = preCalculatedStats.farmingFortune;
  const farmingSweep = preCalculatedStats.farmingSweep;

  // Use FARMING_SWEEP stat to determine number of crops harvested
  // Cap sweep at 1 when farming outside the garden
  let effectiveFarmingSweep = farmingSweep;
  if (character.current_region !== "garden") {
    effectiveFarmingSweep = 1;
  }
  const baseNumCrops = Math.max(1, effectiveFarmingSweep);

  // If drops array exists, use it (new DRY logic)
  if (Array.isArray(cropData.drops) && cropData.drops.length > 0) {
    const possibleLoot = cropData.drops;

    for (let i = 0; i < baseNumCrops; i++) {
      // Special handling for MUSHROOMS - randomly select between red and brown
      if (cropItemKey === "MUSHROOMS") {
        // Get base amount (1 mushroom per action)
        const baseDrop = cropData.baseAmount || 1;
        const totalDrops = applyFortuneToDrops(baseDrop, farmingFortune);

        // For each mushroom drop, randomly choose red or brown (50/50 chance)
        for (let j = 0; j < totalDrops; j++) {
          const mushroomType =
            Math.random() < 0.5 ? "RED_MUSHROOM" : "BROWN_MUSHROOM";
          result.droppedItems.push({ itemKey: mushroomType, amount: 1 });
        }

        // Add XP for mushroom farming - wisdom now applied centrally in addSkillExp function
        const itemExp = cropData?.exp || 4;
        result.exp += itemExp;
      } else {
        // Normal drop logic for other crops
        for (const drop of possibleLoot) {
          // Use min/max if present, otherwise use baseAmount or 1
          let baseDrop = 1;
          if (typeof drop.min === "number" && typeof drop.max === "number") {
            baseDrop =
              Math.floor(Math.random() * (drop.max - drop.min + 1)) + drop.min;
          } else if (cropData.baseAmount) {
            baseDrop = cropData.baseAmount;
          }
          const totalDrops = applyFortuneToDrops(baseDrop, farmingFortune);
          if (totalDrops > 0) {
            result.droppedItems.push({
              itemKey: drop.itemKey,
              amount: totalDrops,
            });
            // Calculate experience - wisdom now applied centrally in addSkillExp function
            const itemData = configManager.getAllItems()[drop.itemKey];
            const itemExp = itemData?.exp || 0;
            result.exp += itemExp;
          }
        }
      }
    }
  } else {
    // All crops must have proper drops array - no fallback support
    throw new Error(
      `[Farm][goFarming] Crop ${cropItemKey} missing drops array - no fallback support. Add proper drops configuration to item data.`
    );
  }

  // TODO: Mob encounters disabled - runCombatEncounter function doesn't exist
  // Need to implement proper combat integration for farming encounters
  /*
  if (cropData.mobEncounters && Array.isArray(cropData.mobEncounters)) {
    for (const encounter of cropData.mobEncounters) {
      const encounterRoll = Math.random();
      if (encounterRoll < encounter.chance) {
        const mobData = configManager.getAllMobs()[encounter.mobKey];
        if (mobData) {
          const combatResult = runCombatEncounter(character, mobData);
          if (combatResult) {
            totalCombatExp += combatResult.exp || 0;
            totalCoins += combatResult.rewards.coins || 0;
            mobDefeated = true;
            
            // Add mob loot to farming results
            if (combatResult.rewards.items && combatResult.rewards.items.length > 0) {
              result.items.push(...combatResult.rewards.items);
            }
          }
        }
        break; // Only one encounter per farming action
      }
    }
  }
  */

  // Check for Cropie drops when farming WHEAT, CARROT, or POTATO with Melon or Fermento Armor
  if (["WHEAT", "CARROT", "POTATO"].includes(cropItemKey)) {
    const { getActiveSetBonuses } = require("../utils/setBonuses");
    const setBonuses = getActiveSetBonuses(character);

    // Use centralized set bonus system for drop chance
    let cropieDropChance = 0;
    if (setBonuses.melon && setBonuses.melon.cropieDropChance) {
      cropieDropChance = Math.max(
        cropieDropChance,
        setBonuses.melon.cropieDropChance
      );
    }
    if (setBonuses.fermento && setBonuses.fermento.cropieDropChance) {
      cropieDropChance = Math.max(
        cropieDropChance,
        setBonuses.fermento.cropieDropChance
      );
    }

    if (cropieDropChance > 0) {
      // Roll for Cropie drop for each crop harvested
      for (let i = 0; i < baseNumCrops; i++) {
        const roll = Math.random();
        if (roll < cropieDropChance) {
          result.droppedItems.push({ itemKey: "CROPIE", amount: 1 });
        }
      }
    }
  }

  // Check for hoe abilities (e.g., Rookie Hoe seed drop chance)
  const equippedTool = character.equipment?.tool;
  if (equippedTool) {
    const allItems = configManager.getAllItems();
    const toolData = allItems[equippedTool.itemKey];

    if (toolData?.abilities) {
      for (const [, ability] of Object.entries(toolData.abilities)) {
        // Handle SEED_DROP_CHANCE abilities
        if (ability.type === "SEED_DROP_CHANCE") {
          // Check if the current crop matches the target crop (or if no target specified, apply to all)
          const targetCrop = ability.targetCrop;
          if (!targetCrop || targetCrop === cropItemKey) {
            const dropChance = ability.chance || 0;

            // Roll for extra seed drops for each crop harvested
            for (let i = 0; i < baseNumCrops; i++) {
              const roll = Math.random();
              if (roll < dropChance) {
                result.droppedItems.push({ itemKey: "SEEDS", amount: 1 });
              }
            }
          }
        }
      }
    }
  }

  // Check for Squash drops when farming PUMPKIN, MELON, or COCOA_BEANS with Cropie or Fermento Armor
  if (["PUMPKIN", "MELON", "COCOA_BEANS"].includes(cropItemKey)) {
    const { getActiveSetBonuses } = require("../utils/setBonuses");
    const setBonuses = getActiveSetBonuses(character);

    // Use centralized set bonus system for drop chance
    let squashDropChance = 0;
    if (setBonuses.cropie && setBonuses.cropie.squashDropChance) {
      squashDropChance = Math.max(
        squashDropChance,
        setBonuses.cropie.squashDropChance
      );
    }
    if (setBonuses.fermento && setBonuses.fermento.squashDropChance) {
      squashDropChance = Math.max(
        squashDropChance,
        setBonuses.fermento.squashDropChance
      );
    }

    if (squashDropChance > 0) {
      // Roll for Squash drop for each crop harvested
      for (let i = 0; i < baseNumCrops; i++) {
        const roll = Math.random();
        if (roll < squashDropChance) {
          result.droppedItems.push({ itemKey: "SQUASH", amount: 1 });
        }
      }
    }
  }

  // Check for Fermento drops when farming SUGAR_CANE, CACTUS, MUSHROOMS, or NETHER_WART with Squash or Fermento Armor
  if (
    [
      "SUGAR_CANE",
      "CACTUS",
      "RED_MUSHROOM",
      "BROWN_MUSHROOM",
      "MUSHROOMS",
      "NETHER_WART",
    ].includes(cropItemKey)
  ) {
    const { getActiveSetBonuses } = require("../utils/setBonuses");
    const setBonuses = getActiveSetBonuses(character);

    // Use centralized set bonus system for drop chance
    let fermentoDropChance = 0;
    if (setBonuses.squash && setBonuses.squash.fermentoDropChance) {
      fermentoDropChance = Math.max(
        fermentoDropChance,
        setBonuses.squash.fermentoDropChance
      );
    }
    if (setBonuses.fermento && setBonuses.fermento.fermentoDropChance) {
      fermentoDropChance = Math.max(
        fermentoDropChance,
        setBonuses.fermento.fermentoDropChance
      );
    }

    if (fermentoDropChance > 0) {
      // Roll for Fermento drop for each crop harvested
      for (let i = 0; i < baseNumCrops; i++) {
        const roll = Math.random();
        if (roll < fermentoDropChance) {
          result.droppedItems.push({ itemKey: "FERMENTO", amount: 1 });
        }
      }
    }
  }

  // Garden milestone tracking - only when farming in Garden
  if (character.current_region === "garden" && interaction) {
    const { isInGarden, updateCropHarvest } = require("../utils/gardenSystem");

    if (isInGarden(character)) {
      try {
        // Calculate total crops harvested (sum all item amounts for this crop type)
        let totalCropsHarvested = 0;
        for (const item of result.droppedItems) {
          if (
            item.itemKey === cropItemKey ||
            (cropItemKey === "MUSHROOMS" &&
              (item.itemKey === "RED_MUSHROOM" ||
                item.itemKey === "BROWN_MUSHROOM"))
          ) {
            totalCropsHarvested += item.amount;
          }
        }

        // garden harvesting completed

        if (totalCropsHarvested > 0) {
          const milestoneResult = await updateCropHarvest(
            interaction.user.id,
            cropItemKey,
            totalCropsHarvested
          );

          // Store garden notification data for separate channel notifications
          result.gardenNotifications = {
            milestones: milestoneResult.milestonesAchieved,
            levelUp: milestoneResult.gardenLevelUp,
            bonusFarmingXp: milestoneResult.totalFarmingXp, // Store bonus XP separately, don't add to action results
          };
        }
      } catch (error) {
        console.error("[Farming] Error updating Garden milestones:", error);
      }
    }
  }

  // Add combat data to result (disabled for now)
  result.combatExp = 0; // totalCombatExp disabled
  result.coins = 0; // totalCoins disabled
  result.mobDefeated = false; // mobDefeated disabled

  return result;
}

const farmingAnimation = async (
  messageId,
  channel,
  embed,
  farmingTime,
  stopRequestMap,
  actionId,
  auxResourceConsumed,
  resourceEmoji,
  timeRemainingMsBeforeCycle, // This parameter was missing!
  character = null,
  resourceKey = null,
  isFirstCycle = false
  /* _isResumption = false */
) => {
  const farmerEmoji = "👨‍🌾";
  const displayEmoji = resourceEmoji || "❓";
  const steps = 2; // 3 frames: 0, 1, 2 (keeping the original 3 frames for better visual progress)
  const trackLength = 15;

  // dynamic timing based on farming time as akin designed
  const stepDuration = farmingTime / steps;

  // Enhanced validation for resumption scenarios
  if (!channel || !messageId) {
    console.warn(
      `[Animation][Farm] No channel or message ID provided for action ${actionId}, skipping animation`
    );
    return;
  }

  for (let i = 0; i <= steps; i++) {
    // Check for instant abort before each animation step
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Farm] Action ${actionId} cancelled instantly during animation step ${i}`
      );
      break;
    }

    // Skip the first frame edit only for the very first cycle (position 0 is already shown in initial message)
    if (i === 0 && isFirstCycle) {
      // small delay before starting movement for first cycle
      if (i < steps) {
        try {
          await cancellableDelay(stepDuration, signal);
        } catch (error) {
          if (error.name === "AbortError") {
            console.log(
              `[Animation][Farm] Animation cancelled during initial delay for action ${actionId}`
            );
            break;
          }
          throw error;
        }
      }
      continue;
    }

    const farmerPosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(farmerPosition)}${farmerEmoji}${" ".repeat(
      trackLength - farmerPosition
    )}`;
    const descriptionText = `\`${movingPart}\` ${displayEmoji}`;

    // Preserve any existing footer (for Garden milestone progress)
    const originalFooter = embed.toJSON().footer;
    const footerText = originalFooter?.text;

    // Create updated embed for animation
    const { updateAnimationEmbed } = require("../utils/animationContainers");
    const result = updateAnimationEmbed(
      embed.data.title || "Farming",
      descriptionText,
      actionId,
      character?.discord_id,
      "Farming",
      embed.data.color || 0x2f3136,
      footerText
    );

    // Enhanced message editing with resumption-safe error handling and rate limiting
    try {
      const { queueMessageEdit } = require("../utils/animationQueue");
      const rows =
        result.buttons && result.buttons.length
          ? [
              new (require("discord.js").ActionRowBuilder)().addComponents(
                ...result.buttons
              ),
            ]
          : [];
      await queueMessageEdit(channel, messageId, {
        embeds: [result.embed],
        components: rows,
      });
    } catch (editError) {
      console.log(
        `[Animation][Farm] Error editing animation message (action ${actionId}): ${editError.message}`
      );

      // For resumption scenarios, certain errors are expected and shouldn't break the animation
      if (
        editError.code === 10008 ||
        editError.code === 50001 ||
        editError.code === 50013
      ) {
        console.log(
          `[Animation][Farm] Message access lost during resumption for action ${actionId}, ending animation gracefully`
        );
        break;
      } else if (editError.code === 50005) {
        // Cannot edit a message authored by another user - this happens during resumption
        // when a different worker tries to edit a message created by the original worker
        console.log(
          `[Animation][Farm] Cannot edit message authored by another user (resumption scenario) - animation will continue without visual updates for action ${actionId}`
        );
        break;
      }

      // For other errors, try to continue the animation but don't crash
      if ((i === 0 && !isFirstCycle) || (i === 1 && isFirstCycle)) {
        // first actual animation step
        console.warn(
          `[Animation][Farm] Animation message failed on first step for action ${actionId}, continuing silently`
        );
      }
      break;
    }

    // Check for instant abort after each animation step
    const signal2 = getAbortSignal(actionId);
    if (signal2 && signal2.aborted) {
      console.log(
        `[Animation][Farm] Action ${actionId} cancelled instantly after animation step ${i}`
      );
      break;
    }

    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Farm] Animation cancelled during delay for action ${actionId}.`
          );
          break;
        }
        throw error;
      }
    }
  }
};

const farmingSkillConfig = {
  skillName: "farming",
  actionName: "Farming",
  resourceNameSingular: "crop", // UI display name
  resourceKeyParamName: "crop", // Parameter name in SlashCommandBuilder
  emoji: skillEmojis["farming"] || "👨‍🌾",
  colors: {
    singleAction: EMBED_COLORS.GOLD,
    multiActionLoop: EMBED_COLORS.SILVER,
    multiActionStopped: EMBED_COLORS.ORANGE,
    multiActionComplete: EMBED_COLORS.GOLD,
  },
  calculateTimeFn: calculateFarmingTime,
  animationFn: farmingAnimation,
  goFn: goFarming,
};

// --- Mining Config ---
// Helper function to get mining data from items.json
function getMiningToughness(itemKey) {
  return configManager.getAllItems()[itemKey]?.miningToughness || 0;
}

function calculateMiningTime(
  itemKey = null,
  _preCalculatedStatsOrCharacter = null
) {
  const BASE_TIME = 3000; // Base time in milliseconds (3 seconds)
  const MIN_TIME = 3000; // Minimum mining time (3.00 seconds)

  // If no item key provided, return base time (backward compatibility)
  if (!itemKey) {
    return BASE_TIME;
  }

  // Get toughness for the item from items.json
  const toughness = getMiningToughness(itemKey);
  // Calculate mining time based on item toughness

  // Calculate base time using hybrid scaling for balanced progression
  // Items with 0 toughness should always take BASE_TIME (3 seconds)
  // Additional time is added on top of the base 3 seconds
  // Linear scaling for early toughness (0-10), logarithmic for higher values
  const additionalTime =
    toughness <= 10
      ? toughness * 100
      : 1000 + (toughness - 10) * 40 * Math.log(1 + (toughness - 10) / 10);
  const baseMineTime = BASE_TIME + additionalTime;

  // For now, we don't have mining sweep like foraging, so return the base time
  // Future enhancement could add mining speed stats here

  return Math.max(baseMineTime, MIN_TIME);
}

function calculateMiningFortune(preCalculatedStatsOrCharacter) {
  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.miningFortune !== "number"
  ) {
    throw new Error(
      "[calculateMiningFortune] Pre-calculated mining fortune required - no fallback support"
    );
  }
  return preCalculatedStatsOrCharacter.miningFortune;
}

async function goMining(
  character,
  resourceKey,
  preCalculatedStats = null,
  interaction = null
) {
  // Require pre-calculated stats - no fallback
  if (!preCalculatedStats) {
    throw new Error(
      "[goMining] Pre-calculated stats required - no fallback support"
    );
  }

  const resourceData = configManager.getAllItems()[resourceKey];
  if (!resourceData) {
    console.error(`[goMining] Resource data not found for key: ${resourceKey}`);
    return { droppedItems: [], exp: 0 };
  }

  // Use pre-calculated stats - no fallback calculations
  const miningFortune = preCalculatedStats.miningFortune;
  const miningSweep = preCalculatedStats.miningSweep;

  //console.log(`[goMining] Mining sweep: ${miningSweep}, fortune: ${miningFortune} for resource: ${resourceKey}`);

  const possibleLoot = resourceData.drops || [];
  const itemsMined = [];
  let totalExp = 0; // Mining EXP only
  let totalCombatExp = 0; // Combat EXP from Endermites
  let totalCoins = 0; // Coins from combat rewards
  const baseNumDrops = Math.max(1, miningSweep); // Ensure at least 1 drop, matching farming pattern

  //console.log(`[goMining] Base num drops: ${baseNumDrops}, possible loot items: ${possibleLoot.length}`);

  // Check for mob encounters based on resource configuration
  let mobDefeated = false;
  if (resourceData.mobEncounters && interaction) {
    for (const encounter of resourceData.mobEncounters) {
      const encounterRoll = Math.random();

      if (encounterRoll < encounter.chance) {
        const mobData = configManager.getMob(encounter.mobKey);
        if (mobData) {
          const mobInstanceData = {
            ...mobData,
            mobKey: encounter.mobKey,
            stats: calculateInstanceStats(mobData.baseStats),
          };

          try {
            const { runTurnBasedFight } = require("../utils/combatEngine");

            const safeInteraction = interaction
              ? {
                  ...interaction,
                  isModifiedForSkillCombat: true,
                }
              : null;

            const combatResult = await runTurnBasedFight({
              interaction: safeInteraction,
              mobInstanceData,
              character,
              currentFightNum: 1,
              totalFights: 1,
              actionId: safeInteraction?.actionId,
              isSeaCreatureSubCombat: false,
              preCalculatedStats,
              messageId: safeInteraction?.messageId,
              channel: safeInteraction?.channel,
            });

            if (!combatResult.victory && !combatResult.fled) {
              // Player was defeated by Endermite - use unified death system
              const {
                processPlayerDeath,
                createSkillDeathResult,
              } = require("../utils/deathSystem");

              // First process the actual death penalty
              await processPlayerDeath(
                safeInteraction?.user?.id || safeInteraction?.userId,
                combatResult.finalCharacterState,
                {
                  name: mobInstanceData.name,
                  emoji: mobInstanceData.emoji,
                },
                "endermite"
              );

              // Then return the formatted result for the skill system
              return createSkillDeathResult(
                mobInstanceData,
                combatResult.finalCharacterState
              );
            }

            if (combatResult.rewards) {
              if (combatResult.rewards.items) {
                for (const [itemKey, amount] of Object.entries(
                  combatResult.rewards.items
                )) {
                  if (amount > 0) {
                    itemsMined.push({ itemKey, amount });
                  }
                }
              }
              totalCombatExp += combatResult.rewards.exp || 0;
              totalCoins += combatResult.rewards.coins || 0;
            }

            if (combatResult.finalCharacterState) {
              Object.assign(character, combatResult.finalCharacterState);
            }

            mobDefeated = true;
            break;
          } catch (combatError) {
            console.error(
              `[goMining] Error in ${mobData.name} combat:`,
              combatError
            );
          }
        }
      }
    }
  }

  // Normal mining logic (continues after combat or if no combat triggered)
  for (let i = 0; i < baseNumDrops; i++) {
    const random = Math.random();
    let cumulativeChance = 0;
    let droppedItemKey = null;

    for (const drop of possibleLoot) {
      cumulativeChance += drop.chance;
      if (random <= cumulativeChance) {
        droppedItemKey = drop.itemKey;
        break;
      }
    }

    if (droppedItemKey) {
      // Determine base drop amount - check drops array first, then resourceData.baseAmount
      let baseDrop = 1;

      // Find the specific drop entry for this item
      const dropEntry = possibleLoot.find(
        (drop) => drop.itemKey === droppedItemKey
      );

      if (
        dropEntry &&
        dropEntry.minAmount !== undefined &&
        dropEntry.maxAmount !== undefined
      ) {
        // Use minAmount/maxAmount from the drops array (for items like Glowstone)
        baseDrop =
          Math.floor(
            Math.random() * (dropEntry.maxAmount - dropEntry.minAmount + 1)
          ) + dropEntry.minAmount;
      } else if (dropEntry && dropEntry.amount !== undefined) {
        // Use fixed amount from drops array
        baseDrop = dropEntry.amount;
      } else if (resourceData.baseAmount) {
        // Fallback to resourceData.baseAmount logic
        if (
          typeof resourceData.baseAmount === "object" &&
          resourceData.baseAmount.min !== undefined &&
          resourceData.baseAmount.max !== undefined
        ) {
          baseDrop =
            Math.floor(
              Math.random() *
                (resourceData.baseAmount.max - resourceData.baseAmount.min + 1)
            ) + resourceData.baseAmount.min;
        } else if (typeof resourceData.baseAmount === "number") {
          baseDrop = resourceData.baseAmount;
        }
      }

      const totalDrops = applyFortuneToDrops(baseDrop, miningFortune);
      itemsMined.push({ itemKey: droppedItemKey, amount: totalDrops });

      // get exp from the original resource being mined, not the dropped item
      // this handles cases like glowstone where you mine one thing but get another
      const expSourceData = configManager.getAllItems()[resourceKey];
      const itemExp = expSourceData?.exp || 0;
      // Wisdom multiplier now applied centrally in addSkillExp function
      totalExp += itemExp;
    }
  }

  //console.log(`[goMining] Returning ${itemsMined.length} items, ${totalExp} exp for ${resourceKey}`);

  return {
    droppedItems: itemsMined,
    exp: totalExp,
    combatExp: totalCombatExp,
    coins: totalCoins,
    mobDefeated: mobDefeated,
  };
}

const miningAnimation = async (
  messageId,
  channel,
  embed,
  miningTime,
  stopRequestMap,
  actionId,
  auxConsumed,
  resourceEmoji,
  timeRemainingMsBeforeCycle,
  character = null,
  resourceKey = null,
  isFirstCycle = false
  /* _isResumption = false */
) => {
  const minerEmoji = "⛏️";
  const displayEmoji = resourceEmoji || "❓";
  const steps = 2;
  const trackLength = 15;

  // dynamic timing based on mining time and ore toughness as akin designed
  const stepDuration = miningTime / steps;

  // Enhanced validation for resumption scenarios
  if (!channel || !messageId) {
    console.warn(
      `[Animation][Mine] No channel or message ID provided for action ${actionId}, skipping animation`
    );
    return;
  }

  for (let i = 0; i <= steps; i++) {
    // Check for instant abort before each animation step
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Mine] Action ${actionId} cancelled instantly during animation step ${i}`
      );
      break;
    }

    // Skip the first frame edit only for the very first cycle (position 0 is already shown in initial message)
    if (i === 0 && isFirstCycle) {
      // small delay before starting movement for first cycle
      if (i < steps) {
        try {
          await cancellableDelay(stepDuration, signal);
        } catch (error) {
          if (error.name === "AbortError") {
            console.log(
              `[Animation][Mine] Animation cancelled during initial delay for action ${actionId}`
            );
            break;
          }
          throw error;
        }
      }
      continue;
    }

    const minerPosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(minerPosition)}${minerEmoji}${" ".repeat(
      trackLength - minerPosition
    )}`;
    const descriptionText = `\`${movingPart}\` ${displayEmoji}`;

    // Create updated embed for animation
    const { updateAnimationEmbed } = require("../utils/animationContainers");
    const result = updateAnimationEmbed(
      embed.data.title || "Mining",
      descriptionText,
      actionId,
      character?.discord_id,
      "Mining",
      embed.data.color || 0x2f3136
    );

    // Enhanced message editing with resumption-safe error handling and rate limiting
    try {
      const { queueMessageEdit } = require("../utils/animationQueue");
      const rows =
        result.buttons && result.buttons.length
          ? [
              new (require("discord.js").ActionRowBuilder)().addComponents(
                ...result.buttons
              ),
            ]
          : [];
      await queueMessageEdit(channel, messageId, {
        embeds: [result.embed],
        components: rows,
      });
    } catch (editError) {
      console.log(
        `[Animation][Mine] Error editing animation message (action ${actionId}): ${editError.message}`
      );

      // For resumption scenarios, certain errors are expected and shouldn't break the animation
      if (
        editError.code === 10008 ||
        editError.code === 50001 ||
        editError.code === 50013
      ) {
        console.log(
          `[Animation][Mine] Message access lost during resumption for action ${actionId}, ending animation gracefully`
        );
        break;
      } else if (editError.code === 50005) {
        // Cannot edit a message authored by another user - this happens during resumption
        // when a different worker tries to edit a message created by the original worker
        console.log(
          `[Animation][Mine] Cannot edit message authored by another user (resumption scenario) - animation will continue without visual updates for action ${actionId}`
        );
        break;
      }

      // For other errors, try to continue the animation but don't crash
      if ((i === 0 && !isFirstCycle) || (i === 1 && isFirstCycle)) {
        // first actual animation step
        console.warn(
          `[Animation][Mine] Animation message failed on first step for action ${actionId}, continuing silently`
        );
      }
      break;
    }

    // Check for instant abort after each animation step
    const signal2 = getAbortSignal(actionId);
    if (signal2 && signal2.aborted) {
      console.log(
        `[Animation][Mine] Action ${actionId} cancelled instantly after animation step ${i}`
      );
      break;
    }

    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Mine] Animation cancelled during delay for action ${actionId}`
          );
          break;
        }
        throw error;
      }
    }
  }
};

const miningSkillConfig = {
  skillName: "mining",
  actionName: "Mining",
  resourceNameSingular: "ore", // UI display name for mining resources
  resourceKeyParamName: "resource", // Parameter name used in command (must match SlashCommandBuilder)
  emoji: skillEmojis["mining"] || "⛏️",
  colors: {
    singleAction: EMBED_COLORS.GREY,
    multiActionLoop: EMBED_COLORS.MID_GREY,
    multiActionStopped: EMBED_COLORS.ORANGE,
    multiActionComplete: EMBED_COLORS.PASTEL_GREEN,
  },
  calculateTimeFn: calculateMiningTime,
  animationFn: miningAnimation,
  goFn: goMining,
};

// --- Foraging Config ---
// Helper functions to get tree data from items.json
function getTreeToughness(itemKey) {
  return configManager.getAllItems()[itemKey]?.treeToughness || 0;
}

// Tree-to-log mapping functions removed - now using item keys directly

function calculateForagingTime(
  itemKey = null,
  preCalculatedStatsOrCharacter = null
) {
  const BASE_TIME = 2000; // Base time in milliseconds
  const MIN_TIME = 2000; // Minimum chopping time (2.00 seconds)

  // If no item key provided, return base time (backward compatibility)
  if (!itemKey) {
    return BASE_TIME;
  }

  // Get toughness for the item from items.json
  const toughness = getTreeToughness(itemKey);
  // Calculate foraging time based on tree type and toughness

  // Calculate base time using hybrid scaling for balanced progression
  // Trees with 0 toughness should always take BASE_TIME (2 seconds)
  // Additional time is added on top of the base 2 seconds
  // Linear scaling for early toughness (0-10), logarithmic for higher values
  const additionalTime =
    toughness <= 10
      ? toughness * 100
      : 1000 + (toughness - 10) * 40 * Math.log(1 + (toughness - 10) / 10);
  const baseChopTime = BASE_TIME + additionalTime;
  // Base chop time calculated

  // Get Foraging Sweep value
  let foragingSweep = 1; // Default value
  if (preCalculatedStatsOrCharacter) {
    foragingSweep = calculateForagingSweep(preCalculatedStatsOrCharacter);
  }

  // Add toughness-based sweep reduction (Option 3: Balanced)
  const effectiveSweep = foragingSweep / (1 + toughness * 0.03);

  // Add Foraging Sweep with diminishing returns (reduced effectiveness)
  // Ensure we don't take square root of negative numbers when effectiveSweep < 1
  const sweepMultiplier =
    1 / (1 + 0.05 * Math.sqrt(Math.max(0, effectiveSweep - 1)));
  const finalTime = baseChopTime * sweepMultiplier;
  // Apply foraging sweep with diminishing returns

  // Ensure minimum time is respected
  const result = Math.max(finalTime, MIN_TIME);
  // Apply minimum time cap
  return result;
}

function calculateForagingFortune(preCalculatedStatsOrCharacter) {
  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.foragingFortune !== "number"
  ) {
    throw new Error(
      "[calculateForagingFortune] Pre-calculated foraging fortune required - no fallback support"
    );
  }
  return preCalculatedStatsOrCharacter.foragingFortune;
}

function calculateForagingSweep(preCalculatedStatsOrCharacter) {
  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.foragingSweep !== "number"
  ) {
    throw new Error(
      "[calculateForagingSweep] Pre-calculated foraging sweep required - no fallback support"
    );
  }
  return preCalculatedStatsOrCharacter.foragingSweep;
}

function calculateFarmingSweep(preCalculatedStatsOrCharacter) {
  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.farmingSweep !== "number"
  ) {
    throw new Error(
      "[calculateFarmingSweep] Pre-calculated farming sweep required - no fallback support"
    );
  }
  return preCalculatedStatsOrCharacter.farmingSweep;
}

function calculateMiningSweep(preCalculatedStatsOrCharacter) {
  // Require pre-calculated stats - no fallback
  if (
    !preCalculatedStatsOrCharacter ||
    typeof preCalculatedStatsOrCharacter.miningSweep !== "number"
  ) {
    throw new Error(
      "[calculateMiningSweep] Pre-calculated mining sweep required - no fallback support"
    );
  }
  return preCalculatedStatsOrCharacter.miningSweep;
}

function goForaging(
  character,
  resourceKey,
  amount = 1,
  preCalculatedStats = null
) {
  // Require pre-calculated stats - no fallback
  if (!preCalculatedStats) {
    throw new Error(
      "[goForaging] Pre-calculated stats required - no fallback support"
    );
  }

  const resourceData = configManager.getAllItems()[resourceKey];
  if (!resourceData) {
    console.error(
      `[goForaging] Resource data not found for key: ${resourceKey}`
    );
    return { droppedItems: [], exp: 0 };
  }

  // Use pre-calculated stats - no fallback calculations
  const foragingFortune = preCalculatedStats.foragingFortune;
  const foragingSweep = preCalculatedStats.foragingSweep;

  const possibleLoot = resourceData.drops || [];
  const itemsForaged = [];
  let totalExp = 0;

  if (resourceKey === "LUSHLILAC") {
    itemsForaged.push({
      itemKey: "LUSHLILAC",
      amount: resourceData.baseAmount || 2,
    });
    totalExp = resourceData.exp || 0;
    return { droppedItems: itemsForaged, exp: totalExp };
  }

  // Sea Lumies use simple random drop mechanics (no tree calculations or sweep effects)
  let baseNumDrops;
  if (resourceKey === "SEA_LUMIES") {
    // Random 1-4 drops per action, completely independent of sweep
    baseNumDrops = Math.floor(Math.random() * 4) + 1;
  } else {
    // Apply toughness-based sweep reduction and diminishing returns for trees
    const toughness = getTreeToughness(resourceKey) || 0;
    const effectiveSweep = foragingSweep / (1 + toughness * 0.039);

    // Apply diminishing returns to effective sweep for drops (effectiveSweep^0.8)
    const sweepWithDiminishingReturns = Math.pow(effectiveSweep, 0.8);

    // Use modified sweep to determine number of blocks broken
    baseNumDrops = Math.max(1, Math.floor(sweepWithDiminishingReturns));
  }

  // Track logs broken for Fig Hew ability (if on Galatea) - only for tree resources
  const figHewTracking = {
    shouldTrack: false,
    logsToAdd: 0,
  };

  // Check if player is using Fig Hew or Figstone Splitter and is on Galatea (exclude Sea Lumies)
  if (character.current_region === "galatea" && resourceKey !== "SEA_LUMIES") {
    const equippedTool = character.equipment?.tool;
    if (
      equippedTool?.itemKey === "FIG_HEW" ||
      equippedTool?.itemKey === "FIGSTONE_SPLITTER"
    ) {
      figHewTracking.shouldTrack = true;
      figHewTracking.logsToAdd = baseNumDrops; // Track actual logs broken
    }
  }

  // Items that should not be affected by Fortune or Sweep bonuses
  const fortuneSweepExemptItems = ["SEA_LUMIES", "TENDER_WOOD", "VINESAP"];

  // Calculate sweep mechanics for non-exempt resources
  if (!fortuneSweepExemptItems.includes(resourceKey)) {
    // const treeType = resourceData.treeType; // Currently unused
    // const toughness = treeType ? getTreeToughness(treeType.toUpperCase()) || 0 : 0; // Currently unused
    // effectiveSweep = foragingSweep / (1 + toughness * 0.03); // Currently unused
    // sweepWithDiminishingReturns = Math.pow(effectiveSweep, 0.8); // Currently unused
  }

  // Handle special drops that should only be rolled once per action (not affected by sweep)
  const specialDrops = possibleLoot.filter((drop) =>
    fortuneSweepExemptItems.includes(drop.itemKey)
  );
  const regularDrops = possibleLoot.filter(
    (drop) => !fortuneSweepExemptItems.includes(drop.itemKey)
  );

  // Process special drops once per action
  for (const drop of specialDrops) {
    const random = Math.random();

    if (random <= drop.chance) {
      const droppedItemKey = drop.itemKey;

      // Handle variable drop amounts (minAmount/maxAmount)
      let baseDropAmount = 1;
      if (drop.minAmount !== undefined && drop.maxAmount !== undefined) {
        // Random amount between minAmount and maxAmount (inclusive)
        baseDropAmount =
          Math.floor(Math.random() * (drop.maxAmount - drop.minAmount + 1)) +
          drop.minAmount;
      }

      // Only proceed if we actually get a drop (baseDropAmount > 0)
      if (baseDropAmount > 0) {
        // Special drops are not affected by Fortune bonuses
        const totalDrops = baseDropAmount;

        // Find existing item in itemsForaged or add new entry
        const existingItem = itemsForaged.find(
          (item) => item.itemKey === droppedItemKey
        );
        if (existingItem) {
          existingItem.amount += totalDrops;
        } else {
          itemsForaged.push({ itemKey: droppedItemKey, amount: totalDrops });
        }

        // Wisdom multiplier now applied centrally in addSkillExp function
        const itemData = configManager.getAllItems()[droppedItemKey];
        const itemExp = itemData?.exp || 0;
        // XP for special drops is only counted once
        totalExp += itemExp;
      }
    }
  }

  // Process regular drops for each sweep iteration
  for (let i = 0; i < baseNumDrops; i++) {
    for (const drop of regularDrops) {
      const random = Math.random();

      if (random <= drop.chance) {
        const droppedItemKey = drop.itemKey;

        // Handle variable drop amounts (minAmount/maxAmount)
        let baseDropAmount = 1;
        if (drop.minAmount !== undefined && drop.maxAmount !== undefined) {
          // Random amount between minAmount and maxAmount (inclusive)
          baseDropAmount =
            Math.floor(Math.random() * (drop.maxAmount - drop.minAmount + 1)) +
            drop.minAmount;
        }

        // Only proceed if we actually get a drop (baseDropAmount > 0)
        if (baseDropAmount > 0) {
          // Exempt items are excluded from Foraging Fortune effects
          const totalDrops = fortuneSweepExemptItems.includes(droppedItemKey)
            ? baseDropAmount
            : applyFortuneToDrops(baseDropAmount, foragingFortune);

          // Find existing item in itemsForaged or add new entry
          const existingItem = itemsForaged.find(
            (item) => item.itemKey === droppedItemKey
          );
          if (existingItem) {
            existingItem.amount += totalDrops;
          } else {
            itemsForaged.push({ itemKey: droppedItemKey, amount: totalDrops });
          }

          // XP should account for special axes that effectively "break more blocks"
          // Each iteration of the baseNumDrops loop represents one "block broken"
          const itemData = configManager.getAllItems()[droppedItemKey];
          const itemExp = itemData?.exp || 0;
          // Wisdom multiplier now applied centrally in addSkillExp function
          totalExp += itemExp;
        }
      }
    }
  }

  // Return tracking data for Fig Hew ability
  const result = { droppedItems: itemsForaged, exp: totalExp };
  if (figHewTracking.shouldTrack) {
    result.figHewTracking = figHewTracking;
  }

  return result;
}

const foragingAnimation = async (
  messageId,
  channel,
  embed,
  foragingTime,
  stopRequestMap,
  actionId,
  auxResourceConsumed,
  resourceEmoji,
  timeRemainingMsBeforeCycle,
  character = null,
  resourceKey = null,
  isFirstCycle = false
  /* _isResumption = false */
) => {
  const axeEmoji = "🪓";
  const displayEmoji = resourceEmoji || "❓";
  const steps = 2;
  const trackLength = 15;

  // dynamic timing based on foraging time and tree toughness as akin designed
  const stepDuration = foragingTime / steps;

  // Enhanced validation for resumption scenarios
  if (!channel || !messageId) {
    console.warn(
      `[Animation][Forage] No channel or message ID provided for action ${actionId}, skipping animation`
    );
    return;
  }

  for (let i = 0; i <= steps; i++) {
    // Check for instant abort before each animation step
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Forage] Action ${actionId} cancelled instantly during animation step ${i}`
      );
      break;
    }

    // Skip the first frame edit only for the very first cycle (position 0 is already shown in initial message)
    if (i === 0 && isFirstCycle) {
      // small delay before starting movement for first cycle
      if (i < steps) {
        try {
          await cancellableDelay(stepDuration, signal);
        } catch (error) {
          if (error.name === "AbortError") {
            console.log(
              `[Animation][Forage] Animation cancelled during initial delay for action ${actionId}`
            );
            break;
          }
          throw error;
        }
      }
      continue;
    }

    const axePosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(axePosition)}${axeEmoji}${" ".repeat(
      trackLength - axePosition
    )}`;
    const descriptionText = `\`${movingPart}\` ${displayEmoji}`;

    // Create updated embed for animation
    const { updateAnimationEmbed } = require("../utils/animationContainers");
    const result = updateAnimationEmbed(
      embed.data.title || "Foraging",
      descriptionText,
      actionId,
      character?.discord_id,
      "Foraging",
      embed.data.color || 0x2f3136
    );

    // Enhanced message editing with resumption-safe error handling and rate limiting
    try {
      const { queueMessageEdit } = require("../utils/animationQueue");
      const rows =
        result.buttons && result.buttons.length
          ? [
              new (require("discord.js").ActionRowBuilder)().addComponents(
                ...result.buttons
              ),
            ]
          : [];
      await queueMessageEdit(channel, messageId, {
        embeds: [result.embed],
        components: rows,
      });
    } catch (editError) {
      console.log(
        `[Animation][Forage] Error editing animation message (action ${actionId}): ${editError.message}`
      );

      // For resumption scenarios, certain errors are expected and shouldn't break the animation
      if (
        editError.code === 10008 ||
        editError.code === 50001 ||
        editError.code === 50013
      ) {
        console.log(
          `[Animation][Forage] Message access lost during resumption for action ${actionId}, ending animation gracefully`
        );
        break;
      } else if (editError.code === 50005) {
        // Cannot edit a message authored by another user - this happens during resumption
        // when a different worker tries to edit a message created by the original worker
        console.log(
          `[Animation][Forage] Cannot edit message authored by another user (resumption scenario) - animation will continue without visual updates for action ${actionId}`
        );
        break;
      }

      // For other errors, try to continue the animation but don't crash
      if ((i === 0 && !isFirstCycle) || (i === 1 && isFirstCycle)) {
        // first actual animation step
        console.warn(
          `[Animation][Forage] Animation message failed on first step for action ${actionId}, continuing silently`
        );
      }
      break;
    }

    // Check for instant abort after each animation step
    const signal2 = getAbortSignal(actionId);
    if (signal2 && signal2.aborted) {
      console.log(
        `[Animation][Forage] Action ${actionId} cancelled instantly after animation step ${i}`
      );
      break;
    }

    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Forage] Animation cancelled during delay for action ${actionId}`
          );
          break;
        }
        throw error;
      }
    }
  }
};

const foragingSkillConfig = {
  skillName: "foraging",
  actionName: "Foraging",
  resourceNameSingular: "resource", // UI display name changed to "resource"
  resourceKeyParamName: "resource", // Parameter name in SlashCommandBuilder changed to "resource"
  emoji: skillEmojis["foraging"] || "🪓",
  colors: {
    singleAction: EMBED_COLORS.DARK_BROWN,
    multiActionLoop: EMBED_COLORS.SIENNA,
    multiActionStopped: EMBED_COLORS.ORANGE,
    multiActionComplete: EMBED_COLORS.PASTEL_GREEN,
  },
  calculateTimeFn: calculateForagingTime,
  animationFn: foragingAnimation,
  goFn: goForaging,
};

function calculateCombatTime(character, amount) {
  // Placeholder: 5 seconds per mob. Can be made more complex later.
  return (amount || 1) * 5000;
}

const COMBAT_EMOJI = skillEmojis["combat"] || "⚔️";
const combatSkillConfig = {
  skillName: "combat",
  actionName: "Combat",
  resourceNameSingular: "mob", // UI display name
  resourceKeyParamName: "mob", // Parameter name in SlashCommandBuilder
  emoji: COMBAT_EMOJI,
  colors: {
    singleAction: EMBED_COLORS.RED,
    multiActionLoop: EMBED_COLORS.SIENNA,
    multiActionStopped: EMBED_COLORS.ORANGE,
    multiActionComplete: EMBED_COLORS.PASTEL_GREEN,
  },
  calculateTimeFn: calculateCombatTime,
  animationFn: async () => {},
  // Custom handler for Combat unification - uses existing handleCombatAction
  customHandler: async (
    interaction,
    character,
    resourceKey,
    amount,
    isAgain,
    _skillConfig,
    wasMax = false
  ) => {
    const { handleCombatAction } = require("../commands/combat");
    await handleCombatAction(
      interaction,
      resourceKey,
      amount,
      isAgain,
      null, // originalTotalAmount
      character,
      wasMax
    );
  },
  goFn: async (character, mobKey) => {
    const baseMobData = configManager.getMob(mobKey);
    if (!baseMobData)
      throw new Error(`[Combat goFn] Invalid mobKey: ${mobKey}`);
    const mobInstance = {
      ...baseMobData,
      mobKey: mobKey,
      stats: calculateInstanceStats(baseMobData.baseStats),
    };
    const rewards = await calculateRewardsFromMob(mobInstance, character);
    return {
      victory: true,
      finalCharacterState: character,
      expGain: rewards.exp,
      items: rewards.items,
      coins: rewards.coins,
      mobName: mobInstance.name,
      mobEmoji: mobInstance.emoji,
      killedCount: 1,
    };
  },
};

function calculateBrewingTime(character, potionKey, amount = 1) {
  const potionData = configManager.getItem(potionKey);
  if (!potionData || !potionData.brewing) {
    throw new Error(`Invalid potion: ${potionKey}`);
  }
  const baseTime = potionData.brewing.time || 20;
  const alchemySpeed = getStatValue(character, "alchemy_speed") || 0;
  const effectiveTime = baseTime * (1 - alchemySpeed / 100);
  return Math.max(5, effectiveTime) * amount * 1000; // convert to milliseconds because in all potions datas we use seconds instead of milliseconds
}

const brewingAnimation = async (
  messageId,
  channel,
  embed,
  brewingTime,
  stopRequestMap,
  actionId,
  auxResourceConsumed,
  resourceEmoji,
  timeRemainingMsBeforeCycle,
  character = null,
  resourceKey = null,
  isFirstCycle = false
) => {
  const bubbleEmoji = "🫧";
  const cauldronEmoji = "⚗️";
  const displayEmoji = resourceEmoji || "❓";
  const steps = 2;
  const trackLength = 15;

  // dynamic timing based on brewing time as originally designed
  const stepDuration = brewingTime / steps;

  //console.log(`[Animation][Brewing] Action ${actionId}: brewingTime=${brewingTime}ms, stepDuration=${stepDuration}ms`);
  //console.log(`[Animation][Brewing] Starting animation for action ${actionId} at ${Date.now()}`);

  if (!channel || !messageId) {
    console.warn(
      `[Animation][Brewing] No channel or message ID provided for action ${actionId}, skipping animation`
    );
    return;
  }

  for (let i = 0; i <= steps; i++) {
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      //console.log(`[Animation][Brewing] Action ${actionId} cancelled instantly during animation step ${i}`);
      break;
    }

    // Skip the first frame edit only for the very first cycle (position 0 is already shown in initial message)
    if (i === 0 && isFirstCycle) {
      // small delay before starting movement for first cycle
      if (i < steps) {
        try {
          await cancellableDelay(stepDuration, signal);
        } catch (error) {
          if (error.name === "AbortError") {
            //console.log(`[Animation][Brewing] Animation cancelled during initial delay for action ${actionId}`);
            break;
          }
          throw error;
        }
      }
      continue;
    }

    const bubblePosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(bubblePosition)}${bubbleEmoji}${" ".repeat(
      trackLength - bubblePosition
    )}`;
    const descriptionText = `\`${movingPart}\` ${cauldronEmoji} ${displayEmoji}`;

    // Create updated embed for animation
    const { updateAnimationEmbed } = require("../utils/animationContainers");
    const result = updateAnimationEmbed(
      embed.data.title || "Brewing",
      descriptionText,
      actionId,
      character?.discord_id,
      "Brewing",
      embed.data.color || 0x2f3136
    );

    // Enhanced message editing with resumption-safe error handling and rate limiting
    try {
      const { queueMessageEdit } = require("../utils/animationQueue");
      const rows =
        result.buttons && result.buttons.length
          ? [
              new (require("discord.js").ActionRowBuilder)().addComponents(
                ...result.buttons
              ),
            ]
          : [];
      await queueMessageEdit(channel, messageId, {
        embeds: [result.embed],
        components: rows,
      });
    } catch (editError) {
      //console.log(`[Animation][Brewing] Error editing animation message (action ${actionId}): ${editError.message}`);

      if (
        editError.code === 10008 ||
        editError.code === 50001 ||
        editError.code === 50013
      ) {
        //console.log(`[Animation][Brewing] Message access lost during resumption for action ${actionId}, ending animation gracefully`);
        break;
      } else if (editError.code === 50005) {
        // Cannot edit a message authored by another user - this happens during resumption
        // when a different worker tries to edit a message created by the original worker
        //console.log(`[Animation][Brewing] Cannot edit message authored by another user (resumption scenario) - animation will continue without visual updates for action ${actionId}`);
        break;
      }

      // For other errors, try to continue the animation but don't crash
      if ((i === 0 && !isFirstCycle) || (i === 1 && isFirstCycle)) {
        // first actual animation step
        console.warn(
          `[Animation][Brewing] Animation message failed on first step for action ${actionId}, continuing silently`
        );
      }
      break;
    }

    const signal2 = getAbortSignal(actionId);
    if (signal2 && signal2.aborted) {
      //console.log(`[Animation][Brewing] Action ${actionId} cancelled instantly after animation step ${i}`);
      break;
    }

    if (i < steps) {
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          //console.log(`[Animation][Brewing] Animation cancelled during delay for action ${actionId}`);
          break;
        }
        throw error;
      }
    }
  }
};

function goBrewing(character, potionKey, amount = 1) {
  const potionData = configManager.getItem(potionKey);
  if (!potionData || !potionData.brewing) {
    throw new Error(`Invalid potion: ${potionKey}`);
  }

  // get alchemy level using proper level calculation
  const { getLevelFromExp } = require("../utils/expFunctions");
  const alchemyLevel = getLevelFromExp(
    character.skills.alchemy?.exp || 0
  ).level;

  if (alchemyLevel < (potionData.requiredLevel || 0)) {
    throw new Error("Insufficient alchemy level");
  }

  // Track consumed ingredients for display
  const consumedItemsArray = [];
  const consumedItemsObj = {};

  // Check and consume ingredients
  for (const [ing, qty] of Object.entries(potionData.brewing.ingredients)) {
    if ((character.inventory.items[ing] || 0) < qty * amount) {
      throw new Error(`Insufficient ${ing.replace("_", " ")}`);
    }
    character.inventory.items[ing] -= qty * amount;
    consumedItemsArray.push({ itemKey: ing, amount: qty * amount });
    consumedItemsObj[ing] = qty * amount;
  }

  const yieldPerAction = potionData.brewing.yield || 1;
  const totalYield = yieldPerAction * amount;
  const expGain = (potionData.exp || 5) * amount;

  // Add potions to inventory
  character.inventory.items[potionKey] =
    (character.inventory.items[potionKey] || 0) + totalYield;

  // Create items gained object for display
  const itemsGainedObj = {};
  itemsGainedObj[potionKey] = totalYield;

  return {
    droppedItems: [{ itemKey: potionKey, amount: totalYield }],
    exp: expGain,
    coins: 0,
    consumedItems: consumedItemsArray,
    // Add these properties for proper display in results
    itemsGained: itemsGainedObj,
    itemsConsumed: consumedItemsObj,
  };
}

const alchemySkillConfig = {
  skillName: "alchemy",
  actionName: "Brewing",
  resourceNameSingular: "potion",
  resourceKeyParamName: "potion",
  emoji: "⚗️",
  colors: {
    singleAction: EMBED_COLORS.PURPLE,
    multiActionLoop: EMBED_COLORS.PURPLE,
    multiActionStopped: EMBED_COLORS.ORANGE,
    multiActionComplete: EMBED_COLORS.PASTEL_GREEN,
  },
  calculateTimeFn: calculateBrewingTime,
  animationFn: brewingAnimation,
  goFn: goBrewing,
  consumeResourceFn: async (userId, character, resourceKey, amount = 1) => {
    const potionData = configManager.getItem(resourceKey);
    if (!potionData || !potionData.brewing) {
      throw new Error(`Invalid potion: ${resourceKey}`);
    }

    // Check if player has required ingredients
    const ingredients = potionData.brewing.ingredients || {};
    for (const [ingredientKey, ingredientAmount] of Object.entries(
      ingredients
    )) {
      const required = ingredientAmount * amount;
      const available = character.inventory?.items?.[ingredientKey] || 0;
      if (available < required) {
        throw new Error(`Insufficient ${ingredientKey.replace("_", " ")}`);
      }
    }

    // Return the character unchanged - actual consumption will be handled by the skill system
    return {
      character,
      ingredientsRequired: ingredients,
      amount,
    };
  },
};

module.exports = {
  // Skill configurations
  fishingSkillConfig,
  farmingSkillConfig,
  miningSkillConfig,
  foragingSkillConfig,
  combatSkillConfig,
  alchemySkillConfig,

  // Time calculation functions
  calculateFishingTime,
  calculateFarmingTime,
  calculateMiningTime,
  calculateForagingTime,
  calculateCombatTime,
  calculateBrewingTime,

  // Generic helper functions
  calculateFortune,
  calculateSweep,
  applyFortuneToDrops,
  applyWisdomMultiplier,
  getStatValue,
  calculateStatFromCharacter,
  createSkillAnimation,

  // Legacy skill-specific functions (for backward compatibility)
  calculateFarmingFortune,
  calculateFarmingSweep,
  calculateMiningFortune,
  calculateMiningSweep,
  calculateForagingFortune,
  calculateForagingSweep,

  // Go functions
  goFishing,
  goFarming,
  goMining,
  goForaging,
  goBrewing,

  // Animation functions
  fishingAnimation,
  farmingAnimation,
  miningAnimation,
  foragingAnimation,

  // Legacy tree mapping constants removed - now using item keys directly
};
