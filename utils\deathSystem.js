const { respawn<PERSON><PERSON><PERSON> } = require("./respawn");
const { savePlayerData } = require("./playerDataManager");

/**
 * Unified death system for all combat scenarios in the game
 * Handles death penalties, auto-revive, and consistent death processing
 */

/**
 * Death configuration options
 * @typedef {Object} DeathConfig
 * @property {Object} defeatedBy - Information about what defeated the player
 * @property {string} defeatedBy.name - Name of the mob/entity that defeated the player
 * @property {string} defeatedBy.emoji - Emoji of the mob/entity that defeated the player
 * @property {string} scenario - The scenario where death occurred (for logging)
 */

/**
 * Process player death with universal 50% coin penalty and auto-revive
 * @param {string} userId - Discord user ID of the player who died
 * @param {Object} character - Player character data
 * @param {Object} defeatedBy - Information about what defeated the player
 * @param {string} defeatedBy.name - Name of the mob/entity that defeated the player
 * @param {string} defeatedBy.emoji - Emoji of the mob/entity that defeated the player
 * @param {string} scenario - The scenario where death occurred (for logging)
 * @returns {Promise<Object>} Death result with penalty info and updated character
 */
async function processPlayerDeath(
  userId,
  character,
  defeatedBy = null,
  scenario = "unknown"
) {
  console.log(
    `[DeathSystem] Processing death for user ${userId} in scenario: ${scenario}`
  );

  let deathPenalty = 0;
  let netCoinChange = 0;

  // Check if player has active Booster Cookie protection
  const { isBoosterCookieActive } = require("./boosterCookieManager");
  const hasBoosterCookieProtection = isBoosterCookieActive(character);

  // Apply universal 50% coin penalty (unless protected by Booster Cookie)
  if (character.coins && !hasBoosterCookieProtection) {
    const purseBeforePenalty = character.coins;
    deathPenalty = Math.floor(purseBeforePenalty / 2);
    character.coins -= deathPenalty;
    netCoinChange = -deathPenalty;

    console.log(
      `[DeathSystem] Applied ${deathPenalty} coin death penalty to user ${userId} (${purseBeforePenalty} -> ${character.coins})`
    );
  } else if (hasBoosterCookieProtection) {
    console.log(
      `[DeathSystem] Booster Cookie protected user ${userId} from coin loss on death`
    );
  }

  // Auto-revive player
  if (character.current_health <= 0) {
    console.log(
      `[DeathSystem] Auto-reviving user ${userId} (health: ${character.current_health})`
    );
    respawnCharacter(character);
  }

  // Save only the fields that changed (coins and health)
  const fieldsToUpdate = [];
  const payload = {};
  if (typeof character.coins === "number") {
    payload.coins = character.coins;
    fieldsToUpdate.push("coins");
  }
  if (typeof character.current_health === "number") {
    payload.current_health = character.current_health;
    fieldsToUpdate.push("current_health");
  }
  if (fieldsToUpdate.length > 0) {
    await savePlayerData(userId, payload, fieldsToUpdate);
  }

  // Log defeat details
  if (defeatedBy) {
    console.log(
      `[DeathSystem] User ${userId} was defeated by ${defeatedBy.name} in ${scenario}`
    );
  }

  return {
    deathPenalty,
    netCoinChange,
    defeatedBy,
    finalCharacterState: character,
    autoRevived: character.current_health > 0,
  };
}

/**
 * Check if a combat result indicates player death
 * @param {Object} combatResult - Result from combat engine
 * @returns {boolean} True if player died (not fled)
 */
function isPlayerDefeated(combatResult) {
  return !combatResult.victory && !combatResult.fled;
}

/**
 * Create death result object for skill actions
 * @param {Object} mobData - Data about the mob that defeated the player
 * @param {Object} finalCharacterState - Updated character state after combat
 * @returns {Object} Formatted death result for skill systems
 */
function createSkillDeathResult(mobData, finalCharacterState) {
  return {
    droppedItems: [],
    items: [],
    exp: 0,
    combatExp: 0,
    defeated: true,
    defeatedBy: {
      name: mobData.name,
      emoji: mobData.emoji,
    },
    finalCharacterState,
    mobName: mobData.name,
    mobEmoji: mobData.emoji,
  };
}

module.exports = {
  processPlayerDeath,
  isPlayerDefeated,
  createSkillDeathResult,
};
