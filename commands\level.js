const { EMBED_COLORS } = require("../gameConfig.js");
const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const {
  formatDisblockLevel,
  calculateXPBreakdown,
  calculateDisblockLevel,
  checkAndNotifyDisblockXP,
} = require("../utils/disblockXpSystem");
const { getPlayerData } = require("../utils/playerDataManager");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("level")
    .setDescription("View your Disblock Level breakdown"),
  async execute(interaction) {
    const userId = interaction.user.id;

    try {
      const player = await getPlayerData(userId);
      if (!player) {
        return interaction.reply({
          content:
            "You need to create a character first! Visit the setup channel to get started.",
          ephemeral: true,
        });
      }

      await interaction.deferReply();

      // Get the character data first
      const updated<PERSON>haracter = await getPlayerData(userId);

      try {
        await checkAndNotifyDisblockXP(userId, interaction, updatedCharacter);
      } catch (xpError) {
        console.error(
          `[Level Command] Error updating Disblock XP for user ${userId}:`,
          xpError
        );
        // Continue with level display even if XP update fails
      }

      const breakdown = await calculateXPBreakdown(updatedCharacter, userId);

      const totalXP = breakdown.grandTotal;

      const formattedLevel = formatDisblockLevel(totalXP);
      const { level } = calculateDisblockLevel(totalXP);

      const embed = new EmbedBuilder()
        .setTitle(`[${level}] ${updatedCharacter.name}`)
        .setColor(EMBED_COLORS.GREEN)
        .setDescription(
          `<:disblock_level:1381196165696983080> Disblock Level: ${formattedLevel}\n\n**Disblock XP Breakdown**:\nSkills: ${breakdown.skills.total} XP\nSlayers: ${breakdown.slayers.total} XP\nCollections: ${breakdown.collections.total} XP\nUnique Minions: ${breakdown.minions.total} XP\nAccessories: ${breakdown.magicalPower.total} XP (${breakdown.magicalPower.mp} MP)\nPets: ${breakdown.petScore.total} XP (${breakdown.petScore.score} Pet Score)\nGarden Level: ${breakdown.gardenLevel.total} XP (Level ${breakdown.gardenLevel.level})\nGarden Crop Milestones: ${breakdown.gardenCropMilestones.total} XP\n\nTotal Disblock XP: ${totalXP.toLocaleString()}`
        );

      await interaction.followUp({ embeds: [embed] });
    } catch (error) {
      console.error("Error in level command:", error);
      if (interaction.deferred) {
        await interaction.followUp({
          content: "An error occurred while fetching your level data.",
          ephemeral: true,
        });
      } else {
        await interaction.reply({
          content: "An error occurred while fetching your level data.",
          ephemeral: true,
        });
      }
    }
  },
};
