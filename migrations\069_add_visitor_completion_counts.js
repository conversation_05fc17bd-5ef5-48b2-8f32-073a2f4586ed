// migration 069_add_visitor_completion_counts.js

module.exports = {
  async up(db) {
    console.log(
      "Applying migration 069: Add visitor_completion_counts column to players table...",
    );
    await db.run(
      `ALTER TABLE players ADD COLUMN visitor_completion_counts TEXT DEFAULT '{}'`,
    );
    console.log(
      "Migration 069 applied: visitor_completion_counts column added.",
    );
  },

  async down(db) {
    // SQLite does not support DROP COLUMN directly; manual migration required if rollback needed
    console.log(
      "Reverting migration 069: Remove visitor_completion_counts from players table...",
    );
    // To rollback, you would need to recreate the table without visitor_completion_counts and copy data
    // This is left as a manual step for safety
    console.log(
      "Migration 069 reverted (manual step required for full rollback).",
    );
  },
};
