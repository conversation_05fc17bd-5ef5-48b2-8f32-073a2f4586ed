/**
 * Utility functions for calculating skill action rewards
 * Handles the "+X Skill Actions" rewards when leveling up skills
 *
 * This system calculates how many additional consecutive actions a player
 * can perform when they level up a skill. The bonus follows the same
 * tier system as the base skill limits:
 *
 * Tier 1 (Levels 1-5): +5 actions per level
 * Tier 2 (Levels 6-10): +10 actions per level
 * Tier 3 (Levels 11-15): +15 actions per level
 * Tier 4 (Levels 16-20): +20 actions per level
 * Tier 5 (Levels 21-25): +25 actions per level
 * Tier 6 (Levels 26-30): +30 actions per level
 * Tier 7 (Levels 31-35): +35 actions per level
 * Tier 8 (Levels 36-40): +40 actions per level
 * Tier 9 (Levels 41-45): +45 actions per level
 * Tier 10 (Levels 46-50): +50 actions per level
 *
 * Level 1 gives +5 bonus actions as the first tier bonus.
 */

const { calculateMaxActionAmount } = require("./skillLimits");

/**
 * Calculates how many additional skill actions a player gains when leveling up
 * @param {string} skillName - The skill that leveled up
 * @param {number} newLevel - The new level reached
 * @returns {number} The number of additional actions gained (0 if no increase)
 */
function calculateSkillActionReward(skillName, newLevel) {
  if (newLevel <= 0) {
    return 0; // No bonus for level 0 or invalid levels
  }

  const actionsAtNewLevel = calculateMaxActionAmount(newLevel);
  const actionsAtPreviousLevel = calculateMaxActionAmount(newLevel - 1);

  return actionsAtNewLevel - actionsAtPreviousLevel;
}

/**
 * Gets the skill action reward text for display in level up embeds
 * @param {string} skillName - The skill that leveled up
 * @param {number} newLevel - The new level reached
 * @returns {string|null} Formatted reward text or null if no bonus
 */
function getSkillActionRewardText(skillName, newLevel) {
  const actionBonus = calculateSkillActionReward(skillName, newLevel);

  if (actionBonus <= 0) {
    return null;
  }

  // Import skill emojis from gameConfig
  const { skillEmojis } = require("../gameConfig");

  // Get the skill emoji, fallback to ⚡ if not found
  const skillEmoji = skillEmojis[skillName] || "⚡";

  // Capitalize the skill name for display
  const displaySkillName =
    skillName.charAt(0).toUpperCase() + skillName.slice(1);

  return `${skillEmoji}  \`+${actionBonus} ${displaySkillName} Actions\``;
}

/**
 * Gets the current maximum actions for a skill level
 * @param {number} skillLevel - The skill level
 * @returns {number} Maximum actions allowed
 */
function getMaxActionsForLevel(skillLevel) {
  return calculateMaxActionAmount(skillLevel);
}

module.exports = {
  calculateSkillActionReward,
  getSkillActionRewardText,
  getMaxActionsForLevel,
};
