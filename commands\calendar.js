const { EMBED_COLORS } = require("../gameConfig.js");
const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const {
  getDisBlockDateTime,
  getSeason,
  isBakerSpawnWindow,
  calculateNextDisBlockYearRolloverUTC,
  BAKER_WINDOW_REAL_SECONDS,
} = require("../utils/timeUtils.js");
const { getTimeState } = require("../bot.js");

function createTimestamp(timestamp, style = "R") {
  return `<t:${Math.floor(timestamp / 1000)}:${style}>`;
}

function getDaySuffix(day) {
  if (day > 3 && day < 21) return "th";
  switch (day % 10) {
    case 1:
      return "st";
    case 2:
      return "nd";
    case 3:
      return "rd";
    default:
      return "th";
  }
}

function formatTime(hour, minute) {
  const period = hour >= 12 ? "PM" : "AM";
  const hour12 = hour % 12 || 12;
  const minutePadded = minute.toString().padStart(2, "0");
  return `${hour12}:${minutePadded} ${period}`;
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("calendar")
    .setDescription("Shows the current date and time in DisBlock."),
  async execute(interaction) {
    try {
      const currentTimeState = getTimeState();
      const { year, month, day, hour, minute } =
        getDisBlockDateTime(currentTimeState);
      const season = getSeason(month);

      const formattedDate = `${season}, Day ${day}${getDaySuffix(day)}`;
      const formattedTime = formatTime(hour, minute);

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ORANGE)
        .setTitle("<:clock:1371709891969224705> Calendar")
        .addFields({
          name: "Current DisBlock Time",
          value: `**Year ${year}**\n${formattedDate}\n${formattedTime}\n\n`,
          inline: false,
        });

      let eventDescription = "";
      const bakerEmoji = "<:npc_baker:1371675630839533638>";
      const currentTime = Date.now();

      if (isBakerSpawnWindow(currentTimeState)) {
        const nextRolloverUTCms =
          calculateNextDisBlockYearRolloverUTC(currentTimeState);
        const timeUntilBakerLeavesMs = nextRolloverUTCms - currentTime;
        const bakerLeavesAt = new Date(currentTime + timeUntilBakerLeavesMs);

        eventDescription =
          `${bakerEmoji} **Baker** is currently in **The Hub**!\n` +
          `\u200b • Leaves ${createTimestamp(bakerLeavesAt, "R")} (${createTimestamp(bakerLeavesAt, "f")})`;
      } else {
        const nextRolloverUTCms =
          calculateNextDisBlockYearRolloverUTC(currentTimeState);
        const bakerWindowStartUTCms =
          nextRolloverUTCms - BAKER_WINDOW_REAL_SECONDS * 1000;
        const timeUntilBakerAppearsMs = bakerWindowStartUTCms - currentTime;
        const bakerAppearsAt = new Date(currentTime + timeUntilBakerAppearsMs);

        if (timeUntilBakerAppearsMs > 0) {
          eventDescription = `${bakerEmoji} **Baker** will appear in **The Hub** ${createTimestamp(bakerAppearsAt, "R")}`;
        } else {
          eventDescription = `${bakerEmoji} **Baker** will appear in **The Hub** at the next year's rollover.`;
        }
      }

      if (eventDescription) {
        embed.addFields({
          name: "Events:",
          value: eventDescription,
          inline: false,
        });
      }

      await interaction.reply({ embeds: [embed] });
    } catch (error) {
      console.error("[CalendarCommand] Error executing command:", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "There was an error fetching the DisBlock calendar.",
          ephemeral: true,
        });
      } else {
        await interaction.followUp({
          content: "There was an error fetching the DisBlock calendar.",
          ephemeral: true,
        });
      }
    }
  },
};
