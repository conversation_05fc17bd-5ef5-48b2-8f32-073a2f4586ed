/**
 * Migration 061: Update market prices to support decimal values
 * Changes price_per_unit from INTEGER to REAL to allow decimal coins like 1.5, 2.3, etc.
 */
async function up(db) {
  console.log(
    "[Migration 061] Updating market prices to support decimal values...",
  );

  // helper function to execute SQL statements synchronously
  function dbRun(sql, params = []) {
    return new Promise((resolve, reject) => {
      db.run(sql, params, function (err) {
        if (err) {
          console.error(
            "Migration DB Error:",
            err.message,
            "\nSQL:",
            sql,
            "\nParams:",
            params,
          );
          reject(err);
        } else {
          resolve({ lastID: this.lastID, changes: this.changes });
        }
      });
    });
  }

  try {
    // since sqlite doesn't support ALTER COLUMN directly, we need to recreate the tables

    // backup existing data
    await dbRun(`
      CREATE TEMPORARY TABLE temp_sell_orders AS 
      SELECT * FROM market_sell_orders
    `);

    await dbRun(`
      CREATE TEMPORARY TABLE temp_buy_orders AS 
      SELECT * FROM market_buy_orders
    `);

    // drop existing tables
    await dbRun("DROP TABLE market_sell_orders");
    await dbRun("DROP TABLE market_buy_orders");

    // recreate sell orders table with REAL price_per_unit
    await dbRun(`
      CREATE TABLE market_sell_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        seller_id TEXT NOT NULL,
        item_key TEXT NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        price_per_unit REAL NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (seller_id) REFERENCES players(discord_id),
        CHECK (quantity > 0),
        CHECK (price_per_unit > 0)
      )
    `);

    // recreate buy orders table with REAL price_per_unit
    await dbRun(`
      CREATE TABLE market_buy_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        buyer_id TEXT NOT NULL,
        item_key TEXT NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        price_per_unit REAL NOT NULL DEFAULT 0,
        total_coins_locked REAL NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (buyer_id) REFERENCES players(discord_id),
        CHECK (quantity > 0),
        CHECK (price_per_unit > 0),
        CHECK (total_coins_locked > 0)
      )
    `);

    // restore data (integers will be automatically converted to reals)
    await dbRun(`
      INSERT INTO market_sell_orders (id, seller_id, item_key, quantity, price_per_unit, created_at)
      SELECT id, seller_id, item_key, quantity, price_per_unit, created_at 
      FROM temp_sell_orders
    `);

    await dbRun(`
      INSERT INTO market_buy_orders (id, buyer_id, item_key, quantity, price_per_unit, total_coins_locked, created_at)
      SELECT id, buyer_id, item_key, quantity, price_per_unit, total_coins_locked, created_at 
      FROM temp_buy_orders
    `);

    // recreate indexes
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_item_key ON market_sell_orders(item_key)",
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_seller ON market_sell_orders(seller_id)",
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_price ON market_sell_orders(price_per_unit)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_item_key ON market_buy_orders(item_key)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_buyer ON market_buy_orders(buyer_id)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_price ON market_buy_orders(price_per_unit)",
      // composite indexes for order matching
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_item_price ON market_sell_orders(item_key, price_per_unit)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_item_price ON market_buy_orders(item_key, price_per_unit DESC)",
    ];

    for (const indexSql of indexes) {
      await dbRun(indexSql);
    }

    // cleanup temporary tables
    await dbRun("DROP TABLE temp_sell_orders");
    await dbRun("DROP TABLE temp_buy_orders");

    console.log(
      "[Migration 061] Successfully updated market tables to support decimal prices",
    );
  } catch (error) {
    console.error("[Migration 061] Error updating market tables:", error);
    throw error;
  }
}

module.exports = { up };
