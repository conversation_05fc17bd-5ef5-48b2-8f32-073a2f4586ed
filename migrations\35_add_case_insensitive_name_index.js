// Migration to add a case-insensitive index on the name column for faster lookups

const MIGRATION_VERSION = 35;

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add case-insensitive index on players(name)`,
  );

  await new Promise((resolve, reject) => {
    // Create a case-insensitive index using LOWER(name)
    db.run(
      "CREATE INDEX IF NOT EXISTS idx_players_name_lower ON players(LOWER(name))",
      function (err) {
        if (err) {
          console.error(
            `[Migration ${MIGRATION_VERSION}] Error creating case-insensitive index on name column:`,
            err,
          );
          reject(err);
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully created case-insensitive index idx_players_name_lower.`,
          );
          resolve();
        }
      },
    );
  });
}

// Optional down migration (for rolling back)
async function down(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Reverting migration: Drop index idx_players_name_lower`,
  );
  await new Promise((resolve, reject) => {
    db.run("DROP INDEX IF EXISTS idx_players_name_lower", function (err) {
      if (err) {
        console.error(
          `[Migration ${MIGRATION_VERSION}] Error dropping index:`,
          err,
        );
        reject(err);
      } else {
        console.log(
          `[Migration ${MIGRATION_VERSION}] Successfully dropped index idx_players_name_lower.`,
        );
        resolve();
      }
    });
  });
}

module.exports = { up, down };
