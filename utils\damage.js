const configManager = require("./configManager");
const { getPlayerAccessories } = require("./accessoryManager");

// Helper function to calculate damage reduction from accessories for specific mob types
async function calculateAccessoryDamageReduction(
  character,
  mobInstanceData,
  isSeaCreature
) {
  let totalReduction = 0;
  const playerAccessories = await getPlayerAccessories(character.discordId);
  const equippedAccessories = playerAccessories.filter((acc) => acc.isEquipped);
  const allItems = configManager.getAllItems();

  for (const accessory of equippedAccessories) {
    const itemData = allItems[accessory.itemKey];
    if (itemData?.type === "ACCESSORY" && itemData.effects) {
      // Check for undead damage reduction (zombies and crypt ghouls)
      const isUndead =
        mobInstanceData.mobType === "Undead" ||
        (mobInstanceData.key &&
          (mobInstanceData.key.includes("zombie") ||
            mobInstanceData.key === "crypt_ghoul"));

      if (isUndead) {
        if (itemData.effects.undeadDamageReduction) {
          totalReduction += itemData.effects.undeadDamageReduction;
        }
        // Legacy support for zombieDamageReduction
        if (itemData.effects.zombieDamageReduction) {
          totalReduction += itemData.effects.zombieDamageReduction;
        }
      }

      // Check for arthropod damage reduction (spiders and spider-like mobs)
      const isArthropod =
        mobInstanceData.mobType === "Arthropod" ||
        (mobInstanceData.key &&
          (mobInstanceData.key.includes("spider") ||
            mobInstanceData.key.includes("tarantula")));

      if (isArthropod) {
        if (itemData.effects.arthropodDamageReduction) {
          totalReduction += itemData.effects.arthropodDamageReduction;
        }
      }

      // Check for skeleton damage reduction
      if (mobInstanceData.key && mobInstanceData.key.includes("skeleton")) {
        if (itemData.effects.skeletonDamageReduction) {
          totalReduction += itemData.effects.skeletonDamageReduction;
        }
      }

      // Check for sea creature damage reduction
      if (isSeaCreature && itemData.effects.seaCreatureDamageReduction) {
        totalReduction += itemData.effects.seaCreatureDamageReduction;
      }
    }
  }

  return totalReduction;
}

/**
 * Calculate damage using the new damage calculation context system
 * @param {number} weaponBaseDamage - Base damage from weapon
 * @param {number} strength - Character strength stat
 * @param {number} critChance - Critical hit chance
 * @param {number} critDamage - Critical hit damage
 * @param {Object} damageContext - Damage calculation context (new system)
 * @param {Object} mobInstanceData - Current mob data (for dynamic bonuses like Execute)
 * @returns {Object} Damage calculation result
 */
function calculateDamageWithContext(
  weaponBaseDamage,
  strength,
  critChance,
  critDamage,
  damageContext,
  mobInstanceData = null
) {
  // Hypixel Skyblock damage formula (with static multiplier):
  // DamageDealt = ((5 + BaseDamage) * (1 + Strength/100) * AdditiveMultiplier * MultiplicativeMultiplier * StaticMultiplier + BonusModifiers) * (1 + CritDamage/100)

  const BASE_PLAYER_DAMAGE = 5;
  const { STATIC_DAMAGE_MULTIPLIER } = require("../gameConfig");

  // Calculate conditional bonuses that depend on base damage
  const { calculateConditionalBonuses } = require("./damageCalculationContext");
  const bonusModifiers = calculateConditionalBonuses(
    damageContext,
    weaponBaseDamage,
    strength,
    mobInstanceData
  );

  // Apply the damage formula with static multiplier
  const preCritDamage =
    (BASE_PLAYER_DAMAGE + weaponBaseDamage) *
      (1 + strength / 100) *
      damageContext.totalAdditiveMultiplier *
      damageContext.totalMultiplicativeMultiplier *
      STATIC_DAMAGE_MULTIPLIER + // apply static multiplier here
    bonusModifiers;

  // Critical hit calculation
  const isCritical = Math.random() * 100 < critChance;
  const critMultiplier = isCritical ? 1 + critDamage / 100 : 1;

  const finalDamage = preCritDamage * critMultiplier;

  return {
    damage: finalDamage,
    isCritical,
    dps: finalDamage,
    breakdown: {
      baseDamage: BASE_PLAYER_DAMAGE + weaponBaseDamage,
      strengthMultiplier: 1 + strength / 100,
      additiveMultiplier: damageContext.totalAdditiveMultiplier,
      multiplicativeMultiplier: damageContext.totalMultiplicativeMultiplier,
      staticMultiplier: STATIC_DAMAGE_MULTIPLIER,
      bonusModifiers: bonusModifiers,
      preCritDamage: preCritDamage,
      critMultiplier: critMultiplier,
    },
  };
}

/**
 * Legacy damage calculation function for backward compatibility
 * @deprecated Use calculateDamageWithContext instead
 */
function calculateDamage(
  weaponBaseDamage,
  strength,
  critChance,
  critDamage,
  sumOfAdditivePercentages = 0,
  productOfMultiplicativeBonuses = 1,
  sumOfFlatBonusDamage = 0
) {
  // Hypixel Skyblock damage formula (with static multiplier):
  // DamageDealt = ((5 + BaseDamage) * (1 + Strength/100) * AdditiveMultiplier * MultiplicativeMultiplier * StaticMultiplier + BonusModifiers) * (1 + CritDamage/100)

  // Constants
  const BASE_PLAYER_DAMAGE = 5; // Everyone gets this
  const { STATIC_DAMAGE_MULTIPLIER } = require("../gameConfig");

  // 1. Calculate (5 + WeaponBaseDamage)
  const totalBaseDamage = BASE_PLAYER_DAMAGE + weaponBaseDamage;

  // 2. Strength Multiplier: (1 + Strength/100)
  const strengthMultiplier = 1 + strength / 100;

  // 3. Additive Multiplier: (1 + SumOfAdditivePercentages/100)
  const additiveMultiplier = 1 + sumOfAdditivePercentages / 100;

  // 4. Multiplicative Multiplier (already a product)
  const multiplicativeMultiplier = productOfMultiplicativeBonuses;

  // 5. Flat Bonus Damage (SumOfFlatBonusDamage)
  const flatBonusDamage = sumOfFlatBonusDamage;

  // Calculate damage before crits with static multiplier:
  const preCritDamage =
    totalBaseDamage *
      strengthMultiplier *
      additiveMultiplier *
      multiplicativeMultiplier *
      STATIC_DAMAGE_MULTIPLIER + // apply static multiplier here
    flatBonusDamage;

  // 6. Critical Hit Multiplier
  const isCritical = Math.random() * 100 < critChance;
  const critMultiplier = isCritical ? 1 + critDamage / 100 : 1;

  // Final Damage
  const finalDamage = preCritDamage * critMultiplier;

  return { damage: finalDamage, isCritical, dps: finalDamage };
}

function calculateDamageTaken(incomingDamage, health, defense) {
  const damageTaken = incomingDamage * (100 / (defense + 100));
  return { damage: damageTaken, dps: damageTaken };
}

module.exports = {
  calculateAccessoryDamageReduction,
  calculateDamage,
  calculateDamageWithContext,
  calculateDamageTaken,
};
