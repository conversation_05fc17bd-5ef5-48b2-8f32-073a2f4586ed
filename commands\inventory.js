const {
  <PERSON>lash<PERSON><PERSON>mand<PERSON><PERSON>er,
  EmbedBuilder,
  MessageFlags,
} = require("discord.js");
const {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager.js");
const {
  CURRENCY,
  ITEM_RARITY,
  EMBED_COLORS,
  BITS_EMOJI,
} = require("../gameConfig.js");
const configManager = require("../utils/configManager");
const { getRankPrefix } = require("../utils/rankUtils");
const { checkRankPermission } = require("../utils/permissionUtils");
const { calculatePetStats } = require("../utils/statCalculations.js");
const { getPetLevel } = require("../utils/petUtils");
const { recordActivityForAnnouncements } = require("../utils/activityTracker");
const {
  formatDisblockLevel,
  calculateXPBreakdown,
} = require("../utils/disblockXpSystem");

const { STAT_ABBREVIATIONS } = require("../utils/statAbbreviations");
const { STATS } = require("../gameConfig");

function formatStats(baseStats) {
  if (!baseStats || Object.keys(baseStats).length === 0) return null;

  const statParts = [];
  for (const [statKey, value] of Object.entries(baseStats)) {
    // Handle special effect displays
    if (statKey === "VITALITY_EFFECT") {
      const { STATS } = require("../gameConfig");
      const vitalityConfig = STATS.VITALITY;
      statParts.push(
        `${vitalityConfig?.emoji || ""} \`Vitality: +${value.toLocaleString()}\``
      );
      continue;
    }

    const statConfig = STATS[statKey];
    if (statConfig) {
      const displayValue = statConfig.isPercentage
        ? `${value > 0 ? "+" : ""}${value}%`
        : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;

      const statName =
        STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || statKey;
      statParts.push(
        `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``
      );
    } else {
      statParts.push(
        `❓ \`${statKey}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``
      );
    }
  }
  return statParts.join(" ");
}

function renderEquippedItems(
  equippedBySlotFlat,
  allSlots,
  showStats,
  character = null
) {
  const equippedItems = [];

  // Pre-load dependencies if showing stats
  let calculateItemStats, getEnderArmorMultiplier, allItems;
  if (showStats) {
    ({ calculateItemStats } = require("../utils/itemStatCalculator.js"));
    ({ getEnderArmorMultiplier } = require("../utils/enderArmorSetBonus"));
    allItems = configManager.getAllItems();
  }

  for (const slotConfig of allSlots) {
    const equipped = equippedBySlotFlat[slotConfig.key];
    if (!equipped) continue;

    const emoji = equipped.emoji || "❓";
    const itemName = `${emoji} **${equipped.name}**`;
    let statsText = "";

    if (showStats) {
      const itemData = allItems[equipped.itemKey];
      const statResult = calculateItemStats(equipped, itemData, character);
      const { baseStats, totalStats } = statResult;

      // Handle Ender Armor set bonus
      if (character && equipped.itemKey?.startsWith("ENDER_ARMOR_")) {
        const multiplier = getEnderArmorMultiplier(character);
        if (multiplier > 1) {
          const bonusMultiplier = multiplier - 1;
          for (const [statKey, value] of Object.entries(baseStats)) {
            const setBonus = Math.floor(value * bonusMultiplier);
            if (setBonus > 0) {
              totalStats[statKey] = (totalStats[statKey] || 0) + setBonus;
            }
          }
        }
      }

      // Handle special effects like vitality doubler
      if (itemData?.effects?.vitalityDoubler && character) {
        // Calculate the actual current vitality to show the bonus amount
        const { calculateAllStats } = require("../utils/statCalculations");
        const allStats = calculateAllStats(character);
        const currentVitality = allStats.VITALITY || 100;

        // Since the doubler effect doubles the vitality, the bonus is half the current total
        // (because current = base * 2, so bonus = base = current / 2)
        const vitalityBonus = Math.floor(currentVitality / 2);
        totalStats.VITALITY_EFFECT = vitalityBonus;
      }

      // Format stats using the optimized formatStats function
      if (Object.keys(totalStats).length > 0) {
        statsText = formatStats(totalStats);
      }
    }

    equippedItems.push({
      displayText: statsText ? `${itemName}\n${statsText}` : itemName,
      slotKey: slotConfig.key,
      slotLabel: slotConfig.label,
      itemType: equipped.type,
      itemSubtype: equipped.subtype,
      originalItem: equipped,
    });
  }

  return equippedItems;
}

function paginateItems(formattedLines, page, itemsPerCol = 12) {
  const itemsPerPage = itemsPerCol * 2;
  const totalPages = Math.ceil(formattedLines.length / itemsPerPage) || 1;
  const currentPage = Math.max(0, Math.min(page, totalPages - 1));
  const startIdx = currentPage * itemsPerPage;
  const pageLines = formattedLines.slice(startIdx, startIdx + itemsPerPage);
  const col1 = pageLines.slice(0, itemsPerCol).join("\n");
  const col2 = pageLines.slice(itemsPerCol).join("\n");
  return { col1, col2, totalPages, currentPage };
}

// const { dbAll } = require("../utils/dbUtils"); // Currently unused
const {
  getAllPlayersWithUsernames,
  getPlayerByUsername,
} = require("../utils/playerCache");

// Note: getPlayerByUsername is now imported from playerCache

module.exports = {
  data: new SlashCommandBuilder()
    .setName("inventory")
    .setDescription("View your or another player's inventory")
    .addStringOption((option) =>
      option
        .setName("player")
        .setDescription("Name of the player whose inventory you want to view.")
        .setRequired(false)
        .setAutocomplete(true)
    ),
  async autocomplete(interaction) {
    const focusedValue = interaction.options.getFocused();
    const players = await getAllPlayersWithUsernames();

    const filtered = players
      .filter((player) =>
        player.name.toLowerCase().includes(focusedValue.toLowerCase())
      )
      .slice(0, 25);

    await interaction.respond(
      filtered.map((player) => ({
        name: player.name,
        value: player.name,
      }))
    );
  },
  async execute(interaction) {
    await interaction.deferReply();

    const executorCharacter = await getPlayerData(interaction.user.id);
    if (!executorCharacter) {
      return interaction.editReply(
        "You don't have a character yet! Visit the setup channel to create one."
      );
    }

    const targetUsername = interaction.options.getString("player");
    let targetUserId = interaction.user.id;

    if (targetUsername) {
      const targetPlayer = getPlayerByUsername(targetUsername);
      if (!targetPlayer) {
        return interaction.editReply({
          content: `Player with username "${targetUsername}" not found.`,
        });
      }
      targetUserId = targetPlayer.discord_id;
    }

    if (targetUserId === interaction.user.id) {
      if (!checkRankPermission(executorCharacter, "MEMBER")) {
        return interaction.editReply({
          content:
            "You don't have permission (Rank Required: MEMBER) to view your inventory.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    } else {
      if (!checkRankPermission(executorCharacter, "ADMIN")) {
        return interaction.editReply({
          content:
            "You don't have permission (Rank Required: ADMIN) to view other players' inventories.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    }

    if (targetUserId === interaction.user.id) {
      try {
        const {
          checkAndNotifyDisblockXP,
        } = require("../utils/disblockXpSystem");
        await checkAndNotifyDisblockXP(targetUserId, interaction);
      } catch (error) {
        console.error("[Inventory] Error updating Disblock XP:", error);
      }
    }

    const character = await getPlayerData(targetUserId);

    if (!character) {
      if (targetUserId === interaction.user.id) {
        return interaction.editReply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });
      } else {
        const targetUser = await interaction.client.users
          .fetch(targetUserId)
          .catch(() => null);
        const displayName = targetUser
          ? targetUser.username
          : targetUsername || "Unknown User";
        return interaction.editReply({
          content: `${displayName} does not have a character.`,
          flags: [MessageFlags.Ephemeral],
        });
      }
    }

    const liveItems = configManager.getAllItems();
    if (!liveItems) {
      console.error(
        "[Inventory] Failed to get live item data from configManager."
      );

      return interaction.editReply({
        content:
          "[INV-CFG-001] Error loading item data. Please try again later or report this code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Collect pending bits for the viewing player (only for self)
    if (targetUserId === interaction.user.id) {
      try {
        const { collectPendingBits } = require("../utils/bitsManager");
        const bitsResult = await collectPendingBits(targetUserId, character);
        // Update character with any collected bits
        if (bitsResult.bitsCollected > 0) {
          // Character is already updated by collectPendingBits
          console.log(
            `[Inventory] Collected ${bitsResult.bitsCollected} bits for user ${targetUserId}`
          );
        }
      } catch (error) {
        console.error("[Inventory] Error collecting bits:", error);
        // Don't fail the command if bits collection fails
      }
    }

    try {
      const searchQuery = ""; // Holds current search term for stackable item filtering

      const allSlots = [
        { key: "WEAPON", label: "Weapon" },
        { key: "HELMET", label: "Helmet" },
        { key: "CHESTPLATE", label: "Chestplate" },
        { key: "LEGGINGS", label: "Leggings" },
        { key: "BOOTS", label: "Boots" },
        { key: "ROD", label: "Rod" },
        { key: "PICKAXE", label: "Pickaxe" },
        { key: "AXE", label: "Axe" },
        { key: "SHOVEL", label: "Shovel" },
        { key: "HOE", label: "Hoe" },
        { key: "NECKLACE", label: "Necklace" },
        { key: "CLOAK", label: "Cloak" },
        { key: "BELT", label: "Belt" },
        { key: "GLOVES", label: "Gloves/Bracelet" },
      ];

      const equippedBySlotFlat = {};

      (character.inventory?.equipment || []).forEach((eq) => {
        if (!eq.isEquipped) return;

        const itemDetails = liveItems[eq.itemKey];
        if (!itemDetails) {
          console.warn(
            `[inventory.js] Could not find item details for key: ${eq.itemKey}`
          );
          return;
        }

        const typeName = (
          itemDetails.type?.name ||
          itemDetails.type ||
          ""
        ).toUpperCase();
        const subtype = (itemDetails.subtype || "").toUpperCase();

        // Optimized slot mapping using lookup tables
        const slotMapping = {
          // Weapons
          WEAPON: ["WEAPON", "SWORD", "BOW"],
          // Armor
          HELMET: ["HELMET"],
          CHESTPLATE: ["CHESTPLATE"],
          LEGGINGS: ["LEGGINGS"],
          BOOTS: ["BOOTS"],
          // Accessories
          NECKLACE: ["NECKLACE"],
          CLOAK: ["CLOAK"],
          BELT: ["BELT"],
          GLOVES: ["GLOVES", "BRACELET"],
          // Tools
          PICKAXE: ["PICKAXE"],
          ROD: ["ROD"],
          HOE: ["HOE"],
          SHOVEL: ["SHOVEL"],
          // Special
          TALISMAN: ["TALISMAN"],
        };

        let slot = "UNKNOWN";

        // Special case for AXE and FARMING_AXE
        if (subtype === "AXE" || subtype === "FARMING_AXE") {
          slot = typeName === "TOOL" ? "AXE" : "WEAPON";
        } else {
          // Check slot mapping
          for (const [slotType, types] of Object.entries(slotMapping)) {
            if (types.includes(typeName) || types.includes(subtype)) {
              slot = slotType;
              break;
            }
          }

          if (slot === "UNKNOWN") {
            console.warn(
              `[inventory.js] Unknown slot type for item: ${itemDetails.name}`
            );
            slot = "MISC";
          }
        }

        // Optimized data_json parsing
        let enchantments = {};
        let hotPotatoBooks = 0;
        let dataJson = {};

        if (eq.data_json) {
          try {
            dataJson =
              typeof eq.data_json === "string"
                ? JSON.parse(eq.data_json)
                : eq.data_json;

            enchantments = dataJson.enchantments || {};
            hotPotatoBooks = dataJson.hotPotatoBooks || 0;
          } catch (error) {
            console.error(
              `[Inventory] Error parsing data_json for ${eq.id}:`,
              error
            );
          }
        }

        let displayName = itemDetails.name;
        let reforgeData = null;
        if (dataJson && dataJson.reforge) {
          reforgeData = dataJson.reforge;
          // Use dynamic reforge name calculation
          const {
            getDynamicReforgeName,
          } = require("../utils/dynamicReforgeStats");
          const reforgeName = getDynamicReforgeName(reforgeData, itemDetails);
          displayName = `${reforgeName} ${itemDetails.name}`;
        }

        equippedBySlotFlat[slot] = {
          ...itemDetails,
          name: displayName,
          equipmentId: eq.id,
          enchantments: enchantments,
          hotPotatoBooks: hotPotatoBooks,
          reforge: reforgeData,
          data_json: dataJson,
        };
      });

      const showStats = false;
      const showItems = true;
      const equippedItems = renderEquippedItems(
        equippedBySlotFlat,
        allSlots,
        showStats,
        character
      );

      const emptySlotLines = [];
      for (const slot of allSlots) {
        if (!equippedBySlotFlat[slot.key]) emptySlotLines.push(slot.label);
      }
      let emptySlotsDisplay = "";
      if (emptySlotLines.length > 0) {
        for (let idx = 0; idx < emptySlotLines.length; idx += 3) {
          emptySlotsDisplay +=
            emptySlotLines
              .slice(idx, idx + 3)
              .map((label) => `\`${label}\``)
              .join(" ") + "\n";
        }
        emptySlotsDisplay = emptySlotsDisplay.trim();
      }

      const stackableItemData = [];

      // Process stackable inventory items
      Object.entries(character.inventory?.items || {}).forEach(
        ([itemKey, quantity]) => {
          const itemDetails = liveItems[itemKey];
          const numericQuantity = quantity || 0;

          if (!itemDetails) {
            console.warn(`[Inventory] Stackable itemKey "${itemKey}" invalid.`);
            stackableItemData.push({
              emoji: "❓",
              name: `UNKNOWN (${itemKey})`,
              quantity: numericQuantity,
              sortName: `UNKNOWN (${itemKey})`,
            });
            return;
          }

          stackableItemData.push({
            emoji: itemDetails.emoji || "❓",
            name: itemDetails.name,
            quantity: numericQuantity,
            sortName: itemDetails.name,
          });
        }
      );

      // Process unequipped equipment (non-unique items only)
      const { getDynamicReforgeName } = require("../utils/dynamicReforgeStats");

      character.inventory?.equipment?.forEach((eq) => {
        if (eq.isEquipped) return;

        const itemDetails = liveItems[eq.itemKey];
        if (!itemDetails) {
          console.warn(
            `[Inventory] Equipment itemKey "${eq.itemKey}" invalid.`
          );
          return;
        }

        if (itemDetails.unique) return;

        let displayName = itemDetails.name;
        if (eq.data_json) {
          try {
            const dataJson =
              typeof eq.data_json === "string"
                ? JSON.parse(eq.data_json)
                : eq.data_json;

            if (dataJson.reforge) {
              const reforgeName = getDynamicReforgeName(
                dataJson.reforge,
                itemDetails
              );
              displayName = `${reforgeName} ${itemDetails.name}`;
            }
          } catch (error) {
            console.error(
              `[Inventory] Error parsing data_json for equipment ${eq.id}:`,
              error
            );
          }
        }

        stackableItemData.push({
          emoji: itemDetails.emoji || "❓",
          name: displayName,
          quantity: 1,
          sortName: displayName,
        });
      });

      // Sort by quantity (highest first) and format for display
      stackableItemData.sort((a, b) => b.quantity - a.quantity);

      const MAX_NAME_LEN = 18;

      // Calculate max lengths for padding in one pass
      let maxNameLen = 0;
      let maxAmountLen = 0;

      const processedItems = stackableItemData.map((item) => {
        const truncatedName =
          item.name.length > MAX_NAME_LEN
            ? item.name.slice(0, MAX_NAME_LEN - 3) + "..."
            : item.name;
        const amountStr = `x${item.quantity}`;

        maxNameLen = Math.max(maxNameLen, truncatedName.length);
        maxAmountLen = Math.max(maxAmountLen, amountStr.length);

        return { ...item, truncatedName, amountStr };
      });

      const formattedLines = processedItems.map((item) => {
        const paddedName = item.truncatedName.padEnd(maxNameLen, " ");
        const paddedAmount = item.amountStr.padStart(maxAmountLen, " ");
        return `${item.emoji} \`${paddedName} ${paddedAmount}\``;
      });

      let page = 0;
      if (
        interaction.options &&
        interaction.options.getInteger &&
        interaction.options.getInteger("page") != null
      ) {
        page = interaction.options.getInteger("page");
      }
      const { col1, col2, totalPages, currentPage } = paginateItems(
        formattedLines,
        page,
        12
      );

      const rankPrefix = getRankPrefix(character);
      const title = `${rankPrefix}${character.name}'s Inventory`;

      let activePetDisplay = "`None`";
      let activePetStats = "";

      // debug: let's see what we have for pets
      //console.log(`[Inventory Debug] User ${targetUserId}: active_pet_id=${character.active_pet_id}, pets count=${character.pets?.length || 0}`);

      if (
        character.active_pet_id &&
        character.pets &&
        character.pets.length > 0
      ) {
        const activePet = character.pets.find(
          (p) => p.id === character.active_pet_id
        );
        //console.log(`[Inventory Debug] Found active pet:`, activePet ? 'yes' : 'no');
        if (activePet) {
          const petInfo = liveItems[activePet.petKey];
          const rarityInfo = ITEM_RARITY[activePet.rarity];
          activePetDisplay = `${petInfo?.emoji || "❓"} **[Lvl ${getPetLevel(
            activePet
          )}] ${rarityInfo?.name || ""} ${petInfo?.name || "Pet"}**`;
          if (showStats) {
            const petBonuses = calculatePetStats(character);
            const statsString = formatStats(petBonuses);
            if (statsString) activePetStats = `\n${statsString}`;
          }
        }
      }

      const hasEquippedItems =
        Object.keys(equippedBySlotFlat).length > 0 && equippedItems.length > 0;
      const MAX_FIELD_LENGTH = 1000;
      const equipmentFieldsToAdd = [];

      if (hasEquippedItems) {
        const categories = {
          weapons: equippedItems.filter((item) => item.slotKey === "WEAPON"),
          armor: equippedItems.filter((item) =>
            ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"].includes(item.slotKey)
          ),
          tools: equippedItems.filter((item) =>
            ["PICKAXE", "AXE", "SHOVEL", "HOE", "ROD"].includes(item.slotKey)
          ),
          equipment: equippedItems.filter(
            (item) =>
              ![
                "WEAPON",
                "HELMET",
                "CHESTPLATE",
                "LEGGINGS",
                "BOOTS",
                "PICKAXE",
                "AXE",
                "SHOVEL",
                "HOE",
                "ROD",
              ].includes(item.slotKey)
          ),
        };

        const categoryNames = {
          weapons: "Weapons",
          armor: "Armor",
          tools: "Tools",
          equipment: "Equipment",
        };

        for (const [categoryKey, categoryItems] of Object.entries(categories)) {
          if (categoryItems.length === 0) continue;

          const categoryText = categoryItems
            .map((item) => item.displayText)
            .join("\n");

          if (categoryText.length > MAX_FIELD_LENGTH) {
            let currentField = "";
            let fieldIndex = 1;

            for (const item of categoryItems) {
              if (
                currentField.length > 0 &&
                currentField.length + item.displayText.length + 1 >
                  MAX_FIELD_LENGTH
              ) {
                equipmentFieldsToAdd.push({
                  name:
                    fieldIndex === 1 ? categoryNames[categoryKey] : "\u200B",
                  value: currentField,
                  inline: false,
                });
                currentField = item.displayText;
                fieldIndex++;
              } else {
                currentField += (currentField ? "\n" : "") + item.displayText;
              }
            }

            if (currentField) {
              equipmentFieldsToAdd.push({
                name: fieldIndex === 1 ? categoryNames[categoryKey] : "\u200B",
                value: currentField,
                inline: false,
              });
            }
          } else {
            equipmentFieldsToAdd.push({
              name: categoryNames[categoryKey],
              value: categoryText,
              inline: false,
            });
          }
        }

        if (equipmentFieldsToAdd.length === 0) {
          const allItemsText = equippedItems
            .map((item) => item.displayText)
            .join("\n");
          equipmentFieldsToAdd.push({
            name: "Equipped Items",
            value: allItemsText,
            inline: false,
          });
        }
      }

      const breakdown = await calculateXPBreakdown(character, targetUserId);
      const totalXP = breakdown.grandTotal;
      const disblockLevel = formatDisblockLevel(totalXP);
      const mainEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ORANGE)
        .setTitle(title)
        .setDescription(
          `<:disblock_level:1381196165696983080> Disblock Level: **${disblockLevel}**\n${CURRENCY.purseEmoji} Purse: **${(
            character.coins || 0
          ).toLocaleString()}**   ${CURRENCY.bankEmoji} Bank: **${(
            character.bank || 0
          ).toLocaleString()}**\n${require("../gameConfig").COPPER.emoji} Copper: **${(
            character.copper || 0
          ).toLocaleString()}**\n<:emerald:1375550740654981230> Gems: **${(
            character.gems || 0
          ).toLocaleString()}**\n${BITS_EMOJI} Bits: **${(
            character.bits || 0
          ).toLocaleString()}**`
        );

      mainEmbed.addFields(
        ...equipmentFieldsToAdd,
        ...(emptySlotsDisplay
          ? [{ name: "Empty Slots", value: emptySlotsDisplay, inline: true }]
          : []),
        {
          name: "Active Pet",
          value: activePetDisplay + activePetStats,
          inline: false,
        }
      );

      const { getNetworthBreakdown } = require("./networth");
      const { total: networth } = await getNetworthBreakdown(targetUserId);
      const networthFormatted = networth.toLocaleString();

      const itemsEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setFooter({ text: `Networth: ${networthFormatted}` });

      if (showItems) {
        itemsEmbed.addFields(
          { name: "Items", value: col1 || "\u200B", inline: true },
          { name: "\u200B", value: col2 || "\u200B", inline: true }
        );
      } else {
        itemsEmbed.setDescription("_Items hidden_");
      }

      // Helper function to build button custom IDs
      const buildCustomId = (overrides = {}) => {
        const params = {
          stats: showStats ? "show" : "hide",
          items: showItems ? "show" : "hide",
          page: currentPage,
          search: encodeURIComponent(searchQuery),
          ...overrides,
        };
        return `inventory_toggle:${Object.entries(params)
          .map(([k, v]) => `${k}=${v}`)
          .join(";")}`;
      };

      const statsButton = new ButtonBuilder()
        .setCustomId(buildCustomId({ stats: !showStats ? "show" : "hide" }))
        .setLabel(showStats ? "Hide Stats" : "Show Stats")
        .setStyle(ButtonStyle.Primary);

      const itemsButton = new ButtonBuilder()
        .setCustomId(buildCustomId({ items: !showItems ? "show" : "hide" }))
        .setLabel(showItems ? "Hide Items" : "Show Items")
        .setStyle(ButtonStyle.Secondary);

      const prevButton = new ButtonBuilder()
        .setCustomId(buildCustomId({ action: "prev" }))
        .setLabel("Prev")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === 0 || !showItems);

      const nextButton = new ButtonBuilder()
        .setCustomId(buildCustomId({ action: "next" }))
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage >= totalPages - 1 || !showItems);

      const searchButton = new ButtonBuilder()
        .setCustomId(buildCustomId({ action: "search" }))
        .setLabel("Search")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(!showItems);

      const row = new ActionRowBuilder().addComponents(
        statsButton,
        itemsButton,
        prevButton,
        nextButton,
        searchButton
      );

      try {
        const replyMessage = await interaction.editReply({
          embeds: [mainEmbed, itemsEmbed],
          components: [row],
        });
        recordActivityForAnnouncements(interaction.channel.id);

        const filter = (i) =>
          i.customId.startsWith("inventory_toggle:") &&
          i.user.id === interaction.user.id;

        const collector = replyMessage.createMessageComponentCollector({
          filter,
          time: 300000,
        });

        collector.on("collect", async (i) => {
          let responseInteraction;
          try {
            const parts = i.customId.split(":")[1].split(";");
            const currentState = {
              showStats: false,
              showItems: true,
              page: 0,
              action: null,
              search: "",
            };

            for (const part of parts) {
              const [key, value] = part.split("=");
              if (key === "stats") currentState.showStats = value === "show";
              if (key === "items") currentState.showItems = value === "show";
              if (key === "page") currentState.page = parseInt(value, 10) || 0;
              if (key === "action") currentState.action = value;
              if (key === "search")
                currentState.search = decodeURIComponent(value || "");
            }

            const subtypeToSlot = {
              SWORD: "WEAPON",
              BOW: "WEAPON",
              PICKAXE: "PICKAXE",
              SHOVEL: "SHOVEL",
              HOE: "HOE",
              ROD: "ROD",
              HELMET: "HELMET",
              CHESTPLATE: "CHESTPLATE",
              LEGGINGS: "LEGGINGS",
              BOOTS: "BOOTS",
              BRACELET: "GLOVES",
              NECKLACE: "NECKLACE",
              CLOAK: "CLOAK",
              BELT: "BELT",
            };

            const newShowStats = currentState.showStats;
            const newShowItems = currentState.showItems;
            let newPage = currentState.page;
            let searchQuery = currentState.search;
            responseInteraction = i;

            if (currentState.action === "prev") {
              newPage = Math.max(0, currentState.page - 1);
            } else if (currentState.action === "next") {
              newPage = currentState.page + 1;
            } else if (currentState.action === "reset_search") {
              searchQuery = "";
              newPage = 0;
            } else if (currentState.action === "search") {
              const modal = new ModalBuilder()
                .setCustomId("inventory_search_modal")
                .setTitle("Search Items");

              const searchInput = new TextInputBuilder()
                .setCustomId("search_query_input")
                .setLabel("Enter item name")
                .setStyle(TextInputStyle.Short)
                .setRequired(true)
                .setMaxLength(50);

              modal.addComponents(
                new ActionRowBuilder().addComponents(searchInput)
              );

              await i.showModal(modal);

              try {
                const modalInteraction = await i.awaitModalSubmit({
                  filter: (mi) =>
                    mi.customId === "inventory_search_modal" &&
                    mi.user.id === i.user.id,
                  time: 120000,
                });

                searchQuery = modalInteraction.fields
                  .getTextInputValue("search_query_input")
                  .trim();
                newPage = 0;
                responseInteraction = modalInteraction;
              } catch (modalErr) {
                console.error("[Inventory Search Modal] Error:", modalErr);
                return;
              }
            }

            const buttonInteractionUserId = i.user.id;
            const originalInteractionUserId = interaction.user.id;

            if (buttonInteractionUserId !== originalInteractionUserId) {
              await i.reply({
                content:
                  "You cannot interact with someone else's inventory buttons.",
                flags: [MessageFlags.Ephemeral],
              });
              return;
            }

            const localCharacter = await getPlayerData(targetUserId);
            const localLiveItems = configManager.getAllItems();

            if (!localCharacter || !localLiveItems) {
              await i.update({
                content: "Error fetching data to update inventory view.",
                embeds: [],
                components: [],
              });
              return;
            }

            const allSlots = [
              { key: "WEAPON", label: "Weapon" },
              { key: "HELMET", label: "Helmet" },
              { key: "CHESTPLATE", label: "Chestplate" },
              { key: "LEGGINGS", label: "Leggings" },
              { key: "BOOTS", label: "Boots" },
              { key: "ROD", label: "Rod" },
              { key: "PICKAXE", label: "Pickaxe" },
              { key: "AXE", label: "Axe" },
              { key: "SHOVEL", label: "Shovel" },
              { key: "HOE", label: "Hoe" },
              { key: "NECKLACE", label: "Necklace" },
              { key: "CLOAK", label: "Cloak" },
              { key: "BELT", label: "Belt" },
              { key: "GLOVES", label: "Gloves/Bracelet" },
            ];

            let dataJson = {};

            const localEquippedBySlotFlat = {};
            (localCharacter.inventory?.equipment || []).forEach((eq) => {
              if (eq.isEquipped) {
                const itemDetails = localLiveItems[eq.itemKey];
                const typeName = (
                  itemDetails?.type?.name ||
                  itemDetails?.type ||
                  ""
                ).toUpperCase();
                const subtype = (itemDetails?.subtype || "").toUpperCase();

                let slotKey;
                if (subtype === "AXE" || subtype === "FARMING_AXE") {
                  if (typeName === "TOOL") {
                    slotKey = "AXE";
                  } else {
                    slotKey = "WEAPON";
                  }
                } else {
                  slotKey =
                    eq.subtype ||
                    itemDetails?.subtype ||
                    eq.slot ||
                    (typeof itemDetails?.type === "string"
                      ? itemDetails.type.toUpperCase()
                      : undefined);
                  if (slotKey && subtypeToSlot[slotKey.toUpperCase()]) {
                    slotKey = subtypeToSlot[slotKey.toUpperCase()];
                  } else if (slotKey) {
                    slotKey = slotKey.toUpperCase();
                  }
                }
                if (allSlots.some((s) => s.key === slotKey)) {
                  let enchantments = {};
                  let hotPotatoBooks = 0;
                  try {
                    if (eq.data_json) {
                      if (typeof eq.data_json === "string") {
                        dataJson = JSON.parse(eq.data_json);
                      } else {
                        dataJson = eq.data_json;
                      }
                      if (dataJson.enchantments) {
                        enchantments = dataJson.enchantments;
                      }
                      if (dataJson.hotPotatoBooks) {
                        hotPotatoBooks = dataJson.hotPotatoBooks;
                      }
                    }
                  } catch (error) {
                    console.error(
                      `[Inventory Toggle] Error parsing data_json for ${eq.id}:`,
                      error
                    );
                  }

                  let displayName = itemDetails.name;
                  let reforgeData = null;
                  if (dataJson && dataJson.reforge) {
                    reforgeData = dataJson.reforge;
                    const {
                      getDynamicReforgeName,
                    } = require("../utils/dynamicReforgeStats");
                    const dynamicReforgeName = getDynamicReforgeName(
                      dataJson.reforge,
                      itemDetails
                    );
                    displayName = `${dynamicReforgeName} ${itemDetails.name}`;
                  }

                  localEquippedBySlotFlat[slotKey] = {
                    ...itemDetails,
                    name: displayName,
                    equipmentId: eq.id,
                    enchantments,
                    hotPotatoBooks,
                    reforge: reforgeData,
                    data_json: dataJson,
                  };
                }
              }
            });
            const localEquippedItems = renderEquippedItems(
              localEquippedBySlotFlat,
              allSlots,
              newShowStats,
              localCharacter
            );

            const localEmptySlotLines = [];
            for (const slot of allSlots) {
              if (!localEquippedBySlotFlat[slot.key])
                localEmptySlotLines.push(slot.label);
            }
            let localEmptySlotsDisplay = "";
            if (localEmptySlotLines.length > 0) {
              for (let j = 0; j < localEmptySlotLines.length; j += 3) {
                localEmptySlotsDisplay +=
                  localEmptySlotLines
                    .slice(j, j + 3)
                    .map((label) => `\`${label}\``)
                    .join(" ") + "\n";
              }
              localEmptySlotsDisplay = localEmptySlotsDisplay.trim();
            }

            const localRankPrefix = getRankPrefix(localCharacter);
            const localTitle = `${localRankPrefix}${localCharacter.name}'s Inventory`;

            let localActivePetDisplay = "`None`";
            let localActivePetStats = "";

            // debug: let's see what we have for pets in the interaction handler
            console.log(
              `[Inventory Debug] Interaction ${targetUserId}: active_pet_id=${localCharacter.active_pet_id}, pets count=${localCharacter.pets?.length || 0}`
            );

            if (
              localCharacter.active_pet_id &&
              localCharacter.pets &&
              localCharacter.pets.length > 0
            ) {
              const activePet = localCharacter.pets.find(
                (p) => p.id === localCharacter.active_pet_id
              );
              console.log(
                `[Inventory Debug] Interaction found active pet:`,
                activePet ? "yes" : "no"
              );
              if (activePet) {
                const petInfo = localLiveItems[activePet.petKey];
                const rarityInfo = ITEM_RARITY[activePet.rarity];
                localActivePetDisplay = `${
                  petInfo?.emoji || "❓"
                } **[Lvl ${getPetLevel(activePet)}] ${rarityInfo?.name || ""} ${
                  petInfo?.name || "Pet"
                }**`;
                if (newShowStats) {
                  const petBonuses = calculatePetStats(localCharacter); // Pass localCharacter
                  const statsString = formatStats(petBonuses);
                  if (statsString) localActivePetStats = `\n${statsString}`;
                }
              }
            }

            const localStackableItemData = [];
            Object.entries(localCharacter.inventory?.items || {}).forEach(
              ([itemKey, quantity]) => {
                const itemDetails = localLiveItems[itemKey];
                if (!itemDetails) {
                  const numericQuantity = quantity || 0;
                  const quantityStr = `x ${numericQuantity}`;
                  localStackableItemData.push({
                    emoji: "❓",
                    name: `UNKNOWN (${itemKey})`,
                    quantity: numericQuantity,
                    sortName: `UNKNOWN (${itemKey})`,
                    quantityStr,
                  });
                  return;
                }
                const emoji = itemDetails.emoji || "❓";
                const name = itemDetails.name;
                const numericQuantity = quantity != null ? quantity : 0;
                const quantityStr = `x ${numericQuantity}`;
                localStackableItemData.push({
                  emoji,
                  name,
                  quantity: numericQuantity,
                  sortName: name,
                  quantityStr,
                });
              }
            );

            localCharacter.inventory?.equipment?.forEach((eq) => {
              if (!eq.isEquipped) {
                const itemDetails = localLiveItems[eq.itemKey];
                if (!itemDetails) {
                  console.warn(
                    `[Inventory] Equipment itemKey "${eq.itemKey}" invalid.`
                  );
                  return;
                }

                if (itemDetails.unique) {
                  return;
                }

                let displayName = itemDetails.name;
                if (eq.data_json) {
                  try {
                    let dataJson = eq.data_json;
                    if (typeof dataJson === "string") {
                      dataJson = JSON.parse(dataJson);
                    }

                    if (dataJson.reforge) {
                      // Use dynamic reforge name calculation
                      const {
                        getDynamicReforgeName,
                      } = require("../utils/dynamicReforgeStats");
                      const reforgeName = getDynamicReforgeName(
                        dataJson.reforge,
                        itemDetails
                      );
                      displayName = `${reforgeName} ${itemDetails.name}`;
                    }
                  } catch (error) {
                    console.error(
                      `[Inventory] Error parsing data_json for equipment ${eq.id}:`,
                      error
                    );
                  }
                }

                const emoji = itemDetails.emoji || "❓";

                localStackableItemData.push({
                  emoji,
                  name: displayName,
                  quantity: 1,
                  sortName: displayName,
                  quantityStr: "x 1",
                });
              }
            });

            localStackableItemData.sort((a, b) => b.quantity - a.quantity);
            let filteredLocalStackable = localStackableItemData;
            if (searchQuery) {
              const qLower = searchQuery.toLowerCase();
              filteredLocalStackable = localStackableItemData.filter((item) =>
                item.name.toLowerCase().includes(qLower)
              );
            }

            const MAX_NAME_LEN_COLLECTOR = 18;
            const localFormattedLines = filteredLocalStackable.map((item) => {
              const name =
                item.name.length > MAX_NAME_LEN_COLLECTOR
                  ? item.name.slice(0, MAX_NAME_LEN_COLLECTOR - 3) + "..."
                  : item.name;
              const displayNamesTemp = filteredLocalStackable.map((it) => {
                const n =
                  it.name.length > MAX_NAME_LEN_COLLECTOR
                    ? it.name.slice(0, MAX_NAME_LEN_COLLECTOR - 3) + "..."
                    : it.name;
                return n;
              });
              const maxNameLenTemp = Math.max(
                ...displayNamesTemp.map((s) => s.length),
                0
              );
              const amountStringsTemp = filteredLocalStackable.map(
                (it) => `x${it.quantity}`
              );
              const maxAmountLenTemp = Math.max(
                ...amountStringsTemp.map((s) => s.length),
                0
              );
              const paddedName = name.padEnd(maxNameLenTemp, " ");
              const amount = `x${item.quantity}`.padStart(
                maxAmountLenTemp,
                " "
              );
              return `${item.emoji} \`${paddedName} ${amount}\``;
            });

            const {
              col1: localCol1,
              col2: localCol2,
              totalPages: localTotalPages,
              currentPage: localCurrentPage,
            } = paginateItems(localFormattedLines, newPage, 12);

            const localHasEquippedItems =
              Object.keys(localEquippedBySlotFlat).length > 0 &&
              localEquippedItems.length > 0;

            const MAX_FIELD_LENGTH = 1000;
            const localEquipmentFieldsToAdd = [];

            if (localHasEquippedItems) {
              const categories = {
                weapons: localEquippedItems.filter(
                  (item) => item.slotKey === "WEAPON"
                ),
                armor: localEquippedItems.filter((item) =>
                  ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"].includes(
                    item.slotKey
                  )
                ),
                tools: localEquippedItems.filter((item) =>
                  ["PICKAXE", "AXE", "SHOVEL", "HOE", "ROD"].includes(
                    item.slotKey
                  )
                ),
                accessories: localEquippedItems.filter((item) =>
                  ["RING", "NECKLACE", "CLOAK", "BELT", "GLOVES"].includes(
                    item.slotKey
                  )
                ),
                other: localEquippedItems.filter(
                  (item) =>
                    ![
                      "WEAPON",
                      "HELMET",
                      "CHESTPLATE",
                      "LEGGINGS",
                      "BOOTS",
                      "PICKAXE",
                      "AXE",
                      "SHOVEL",
                      "HOE",
                      "ROD",
                      "RING",
                      "NECKLACE",
                      "CLOAK",
                      "BELT",
                      "GLOVES",
                    ].includes(item.slotKey)
                ),
              };

              const categoryNames = {
                weapons: "Weapons",
                armor: "Armor",
                tools: "Tools",
                accessories: "Equipment",
                other: "Other Equipment",
              };

              for (const [categoryKey, categoryItems] of Object.entries(
                categories
              )) {
                if (categoryItems.length === 0) continue;

                const categoryText = categoryItems
                  .map((item) => item.displayText)
                  .join("\n");

                if (categoryText.length > MAX_FIELD_LENGTH) {
                  let currentField = "";
                  let fieldIndex = 1;

                  for (const item of categoryItems) {
                    if (
                      currentField.length > 0 &&
                      currentField.length + item.displayText.length + 1 >
                        MAX_FIELD_LENGTH
                    ) {
                      localEquipmentFieldsToAdd.push({
                        name:
                          fieldIndex === 1
                            ? categoryNames[categoryKey]
                            : "\u200B",
                        value: currentField,
                        inline: false,
                      });
                      currentField = item.displayText;
                      fieldIndex++;
                    } else {
                      // Add to current field
                      currentField +=
                        (currentField ? "\n" : "") + item.displayText;
                    }
                  }

                  if (currentField) {
                    localEquipmentFieldsToAdd.push({
                      name:
                        fieldIndex === 1
                          ? categoryNames[categoryKey]
                          : "\u200B",
                      value: currentField,
                      inline: false,
                    });
                  }
                } else {
                  localEquipmentFieldsToAdd.push({
                    name: categoryNames[categoryKey],
                    value: categoryText,
                    inline: false,
                  });
                }
              }

              if (localEquipmentFieldsToAdd.length === 0) {
                const allItemsText = localEquippedItems
                  .map((item) => item.displayText)
                  .join("\n");
                localEquipmentFieldsToAdd.push({
                  name: "Equipped Items",
                  value: allItemsText,
                  inline: false,
                });
              }
            }

            // calculate the accurate disblock level for button interactions
            const localBreakdown = await calculateXPBreakdown(
              localCharacter,
              targetUserId
            );
            const localTotalXP = localBreakdown.grandTotal;
            const localDisblockLevel = formatDisblockLevel(localTotalXP);
            const localMainEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.ORANGE)
              .setTitle(localTitle)
              .setDescription(
                `<:disblock_level:1381196165696983080> Disblock Level: **${localDisblockLevel}**\n${CURRENCY.purseEmoji} Purse: **${(
                  localCharacter.coins || 0
                ).toLocaleString()}**   ${CURRENCY.bankEmoji} Bank: **${(
                  localCharacter.bank || 0
                ).toLocaleString()}**\n${require("../gameConfig").COPPER.emoji} Copper: **${(
                  localCharacter.copper || 0
                ).toLocaleString()}**\n<:emerald:1375550740654981230> Gems: **${(
                  localCharacter.gems || 0
                ).toLocaleString()}**\n${BITS_EMOJI} Bits: **${(
                  localCharacter.bits || 0
                ).toLocaleString()}**`
              );

            localMainEmbed.addFields(
              ...localEquipmentFieldsToAdd,
              ...(localEmptySlotsDisplay
                ? [
                    {
                      name: "Empty Slots",
                      value: localEmptySlotsDisplay,
                      inline: true,
                    },
                  ]
                : []),
              {
                name: "Active Pet",
                value: localActivePetDisplay + localActivePetStats,
                inline: false,
              }
            );

            const { total: localNetworth } =
              await getNetworthBreakdown(targetUserId);
            const localNetworthFormatted = localNetworth.toLocaleString();

            const localItemsEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.BLUE)
              .setFooter({ text: `Networth: ${localNetworthFormatted}` });

            if (newShowItems) {
              localItemsEmbed.addFields(
                { name: "Items", value: localCol1 || "\u200B", inline: true },
                { name: "\u200B", value: localCol2 || "\u200B", inline: true }
              );
            } else {
              localItemsEmbed.setDescription("_Items hidden_");
            }

            const localBuildCustomId = (overrides = {}) => {
              const params = {
                stats: newShowStats ? "show" : "hide",
                items: newShowItems ? "show" : "hide",
                page: localCurrentPage,
                search: encodeURIComponent(searchQuery),
                ...overrides,
              };
              return `inventory_toggle:${Object.entries(params)
                .map(([k, v]) => `${k}=${v}`)
                .join(";")}`;
            };

            const newStatsButton = new ButtonBuilder()
              .setCustomId(
                localBuildCustomId({ stats: !newShowStats ? "show" : "hide" })
              )
              .setLabel(newShowStats ? "Hide Stats" : "Show Stats")
              .setStyle(ButtonStyle.Primary);
            const newItemsButton = new ButtonBuilder()
              .setCustomId(
                localBuildCustomId({ items: !newShowItems ? "show" : "hide" })
              )
              .setLabel(newShowItems ? "Hide Items" : "Show Items")
              .setStyle(ButtonStyle.Secondary);

            const newPrevButton = new ButtonBuilder()
              .setCustomId(localBuildCustomId({ action: "prev" }))
              .setLabel("Prev")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(localCurrentPage === 0 || !newShowItems);
            const newNextButton = new ButtonBuilder()
              .setCustomId(localBuildCustomId({ action: "next" }))
              .setLabel("Next")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(
                localCurrentPage >= localTotalPages - 1 || !newShowItems
              );

            const newSearchButton = new ButtonBuilder()
              .setCustomId(
                localBuildCustomId({
                  action: searchQuery ? "reset_search" : "search",
                })
              )
              .setLabel(searchQuery ? "Reset Search" : "Search")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(!newShowItems);

            const newRow = new ActionRowBuilder().addComponents(
              newStatsButton,
              newItemsButton,
              newPrevButton,
              newNextButton,
              newSearchButton
            );

            await responseInteraction.update({
              embeds: [localMainEmbed, localItemsEmbed],
              components: [newRow],
            });
            recordActivityForAnnouncements(responseInteraction.channel.id);
          } catch (error) {
            console.error("[Inventory Collector] Error:", error);
            try {
              if (
                !responseInteraction.replied &&
                !responseInteraction.deferred
              ) {
                await responseInteraction.reply({
                  content:
                    "An error occurred updating your inventory view. Please try again.",
                  flags: [MessageFlags.Ephemeral],
                });
              } else {
                await responseInteraction.followUp({
                  content:
                    "An error occurred updating your inventory view. Please try again.",
                  flags: [MessageFlags.Ephemeral],
                });
              }
            } catch (err) {
              console.error("[Inventory Collector Error][Reply Fail]", err);
            }
          }
        });

        collector.on("end", (collected) => {
          if (collected.size === 0) {
            // No interactions collected during timeout
          }
        });
      } catch (replyError) {
        console.error(
          "[Inventory Command] Error sending embed reply [INV-RPL-ERR]:",
          replyError
        );
        await interaction
          .editReply({
            content:
              "[INV-RPL-ERR] An error occurred trying to display the inventory. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    } catch (error) {
      console.error("[Inventory Command Error] [INV-EXE-ERR]", error);
      await interaction
        .editReply({
          content:
            "[INV-EXE-ERR] An unexpected error occurred while fetching the inventory. Please report this error code to an Admin.",
          flags: [MessageFlags.Ephemeral],
        })
        .catch(() => {});
    }
  },
};
