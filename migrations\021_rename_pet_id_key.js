// Migration Script: 021_rename_pet_id_key.js
// Purpose: Renames the unique identifier key in pet objects from `petId` to `id`
//          for backward compatibility after a change in `utils/petUtils.js`.

// Function to perform the migration
async function up(db, playerDataUtils) {
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("--- Migration 021 START --- Running pet ID key rename...");
  let processedCount = 0;
  let migratedPlayerCount = 0;
  let migratedPetCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log(
      "--- Migration 021 INFO --- No player IDs found. Migration step 021 complete.",
    );
    return;
  }
  console.log(
    `--- Migration 021 INFO --- Found ${playerIds.length} players to check.`,
  );

  for (const userId of playerIds) {
    processedCount++;
    let playerDataChanged = false; // Flag specific to this player
    let foundPetsToMigrate = false;
    try {
      const characterData = await getPlayerData(userId);
      if (
        !characterData ||
        !Array.isArray(characterData.pets) ||
        characterData.pets.length === 0
      ) {
        continue;
      }

      console.log(
        `  - Player ${userId}: Checking ${characterData.pets.length} pet(s).`,
      );
      characterData.pets.forEach((pet, index) => {
        if (
          pet &&
          typeof pet === "object" &&
          Object.hasOwn(pet, "petId") &&
          !Object.hasOwn(pet, "id")
        ) {
          foundPetsToMigrate = true;
          console.log(
            `    - Pet Index ${index}: Found pet with petId needing migration (Key: ${pet.petKey || "N/A"}, Old ID: ${pet.petId})`,
          );
          pet.id = pet.petId;
          delete pet.petId;
          playerDataChanged = true;
          migratedPetCount++;
        } else if (pet && typeof pet === "object") {
          // Pet already has correct structure - no action needed
        }
      });

      if (playerDataChanged) {
        console.log(
          `  - Player ${userId}: Changes detected, attempting to save...`,
        );
        await savePlayerData(userId, characterData);
        console.log(`  - Player ${userId}: Save successful.`);
        migratedPlayerCount++;
      } else if (foundPetsToMigrate) {
        console.log(
          `  - Player ${userId}: Found pets needing migration, but no save triggered? (Should not happen)`,
        );
      } else {
        // No pets to migrate for this player - continue
        console.log(`  - Player ${userId}: No pets requiring migration.`);
      }
    } catch (error) {
      console.error(
        `  - ERROR processing player ${userId} for migration 021:`,
        error,
      );
      errorCount++;
    }
  }

  console.log(
    `--- Migration 021 END --- Players Processed: ${processedCount}, Players Migrated: ${migratedPlayerCount}, Pets Migrated: ${migratedPetCount}, Errors: ${errorCount}`,
  );
}

// Optional: Add a down function to revert the changes if necessary
async function down(db, playerDataUtils) {
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("Reverting pet ID key rename migration (021)...");
  let processedCount = 0;
  let revertedPlayerCount = 0;
  let revertedPetCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log("No player IDs found. Migration rollback 021 complete.");
    return;
  }
  console.log(`Found ${playerIds.length} players to check for rollback 021.`);

  for (const userId of playerIds) {
    processedCount++;
    let playerDataChanged = false;
    try {
      const characterData = await getPlayerData(userId);
      if (
        !characterData ||
        !Array.isArray(characterData.pets) ||
        characterData.pets.length === 0
      ) {
        continue;
      }

      characterData.pets.forEach((pet) => {
        // Check if id exists and petId does NOT exist
        if (
          pet &&
          typeof pet === "object" &&
          Object.hasOwn(pet, "id") &&
          !Object.hasOwn(pet, "petId")
        ) {
          console.log(
            `  - Player ${userId}: Reverting pet (Key: ${pet.petKey}, New ID: ${pet.id})`,
          );
          pet.petId = pet.id; // Assign value back to the old key
          delete pet.id; // Remove the new key
          playerDataChanged = true;
          revertedPetCount++;
        }
      });

      if (playerDataChanged) {
        await savePlayerData(userId, characterData);
        revertedPlayerCount++;
      }
    } catch (error) {
      console.error(
        `  - Error processing player ${userId} for migration rollback 021:`,
        error,
      );
      errorCount++;
    }
  }

  console.log(
    `Migration Rollback 021 complete. Players Processed: ${processedCount}, Players Reverted: ${revertedPlayerCount}, Pets Reverted: ${revertedPetCount}, Errors: ${errorCount}`,
  );
}

// Export the migration functions
module.exports = { up, down };
