// utils/activityManager.js
const { dbGet, dbRunQueued } = require("./dbUtils");
const { getActiveActionForUser } = require("./actionPersistence");
const { EmbedBuilder } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");

// --- Activity conflict groups ---
const ACTIVITY_CONFLICT_GROUPS = [
  ["farming", "mining", "foraging", "fishing", "combat"],
];

function isConflict(activityA, activityB) {
  return ACTIVITY_CONFLICT_GROUPS.some(
    (group) => group.includes(activityA) && group.includes(activityB)
  );
}

/** Checks if a user is busy with any activity in the database. */
const isUserBusy = async (userId) => {
  const result = await dbGet(
    "SELECT action_type FROM active_actions WHERE user_id = ?",
    [userId]
  );
  return !!result;
};

/** Gets the activity type the user is busy with from the database, or null. */
const getCurrentActivity = async (userId) => {
  const result = await dbGet(
    "SELECT action_type FROM active_actions WHERE user_id = ?",
    [userId]
  );
  return result ? result.action_type : null;
};

/**
 * Gets detailed activity information for a user, including progress and timing
 * This is much more useful than just getting the action_type
 */
const getDetailedActivity = async (userId) => {
  const result = await dbGet(
    "SELECT action_type, completed_cycles, total_amount, start_timestamp, worker_id FROM active_actions WHERE user_id = ?",
    [userId]
  );

  if (!result) return null;

  return {
    actionType: result.action_type,
    completedCycles: result.completed_cycles || 0,
    totalAmount: result.total_amount || 1,
    progress:
      result.total_amount > 0
        ? (
            ((result.completed_cycles || 0) / result.total_amount) *
            100
          ).toFixed(1)
        : 0,
    startTime: result.start_timestamp,
    workerId: result.worker_id,
    isActive: true,
  };
};

/** Sets the user's current activity in the database. Throws error if already busy. */
const setActivity = async (userId, activityType) => {
  if (!activityType || typeof activityType !== "string") {
    throw new Error("Invalid activityType provided to setActivity.");
  }
  await dbRunQueued(
    "INSERT OR IGNORE INTO active_actions (user_id, action_type) VALUES (?, ?)",
    [userId, activityType]
  );
  // After attempting to insert, check if the activity was set
  const inserted = await getCurrentActivity(userId);
  if (inserted !== activityType) {
    throw new Error(
      `Failed to set activity. User is already busy with: ${inserted}`
    );
  }
};

/**
 * Atomically checks if the user is busy and, if not, sets the activity.
 * Returns true if activity was set, false if user was already busy.
 */
const trySetActivity = async (userId, activityType) => {
  if (!activityType || typeof activityType !== "string") {
    throw new Error("Invalid activityType provided to trySetActivity.");
  }

  // Try to insert, fail if user already has an activity
  try {
    await dbRunQueued(
      `INSERT OR IGNORE INTO active_actions (user_id, action_type)
       VALUES (?, ?)`,
      [userId, activityType]
    );
    // Check if insert succeeded by verifying if user now has this activity
    const currentActivity = await getCurrentActivity(userId);
    return currentActivity === activityType;
  } catch (error) {
    console.error(`[trySetActivity] Error for user ${userId}:`, error);
    return false;
  }
};

/**
 * Returns the action_type of the user's active action from the DB, or null if none.
 */
async function getActiveActionTypeForUser(userId) {
  const result = await dbGet(
    "SELECT action_type FROM active_actions WHERE user_id = ?",
    [userId]
  );
  return result ? result.action_type : null;
}

/**
 * Unified function to check if a user is performing an action.
 * Throws an error if the user is busy with another action.
 * @param {string} userId - The Discord user ID.
 * @param {string} commandName - The name of the command being executed.
 * @throws {Error} If the user is busy with another action.
 */
async function checkUserActionStatus(userId, commandName) {
  const activeAction = await getActiveActionForUser(userId);
  if (activeAction) {
    throw new Error(
      `You cannot use /${commandName} while performing ${activeAction.action_type}!`
    );
  }
}

/**
 * Clears the user's active activity from the database.
 * @param {string} userId - The Discord user ID.
 */
const clearActivity = async (userId) => {
  await dbRunQueued("DELETE FROM active_actions WHERE user_id = ?", [userId]);
};

/**
 * Checks if the user is stuck with an active activity.
 * @param {string} userId - The Discord user ID.
 * @returns {Promise<boolean>} True if the user has an active action.
 */
const ensureUserNotStuck = async (userId) => {
  return await isUserBusy(userId);
};

/**
 * Replies with a standardized busy warning embed (blue info style).
 * @param {import('discord.js').Interaction} interaction - The Discord interaction.
 * @param {string} currentActivity - The current activity name.
 * @param {string} commandName - The attempted command name (optional, will use interaction.commandName if not provided).
 */
const warnUserBusy = (interaction, currentActivity, commandName) => {
  // get the actual command name from the interaction if not provided
  const actualCommandName = commandName || interaction.commandName;

  // create a mapping of activity names to command names for better ux
  const activityToCommand = {
    mining: "mine",
    farming: "farm",
    combat: "combat",
    fishing: "fish",
    foraging: "forage",
    alchemy: "brew",
  };

  // create proper display names for activities
  const activityDisplayNames = {
    mining: "mining",
    farming: "farming",
    combat: "fighting",
    fishing: "fishing",
    foraging: "foraging",
    alchemy: "brewing",
  };

  // check if user is trying the same activity they're already doing
  const commandForActivity =
    activityToCommand[currentActivity] || currentActivity;
  const isSameActivity = actualCommandName === commandForActivity;
  const displayName = activityDisplayNames[currentActivity] || currentActivity;

  let description;
  if (isSameActivity) {
    // user is trying the same command they're already doing
    description = `⏱ You are already **${displayName}**!`;
  } else {
    // user is trying a different command while busy
    description = `⏱ Hold up! You are currently **${displayName}**. Finish that and try **/${actualCommandName}** again.`;
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setDescription(description);

  // Use editReply if interaction was deferred, otherwise use reply
  if (interaction.deferred || interaction.replied) {
    return interaction.editReply({ embeds: [embed] });
  } else {
    return interaction.reply({ embeds: [embed] }); // removed ephemeral: true per user request
  }
};

module.exports = {
  isUserBusy,
  getCurrentActivity,
  getDetailedActivity,
  setActivity,
  trySetActivity,
  isConflict,
  getActiveActionTypeForUser,
  ensureUserNotStuck,
  clearActivity,
  checkUserActionStatus,
  warnUserBusy,
};
