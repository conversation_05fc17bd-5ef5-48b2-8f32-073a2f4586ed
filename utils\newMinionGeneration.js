/**
 * New Minion Generation System - Clean rewrite that actually works
 *
 * This system simulates minion generation exactly like Hypixel Skyblock:
 * - Minions generate items over time based on their generation rate
 * - Compactors automatically convert items as they accumulate (9:1 for blocks, 160:1 for enchanted)
 * - Storage shows all item types that exist
 * - Backward compatible with existing minions
 */

const { formatNumber } = require("./displayUtils");

/**
 * Simulates minion generation and compacting from last collection until now
 * @param {Object} minion - The minion object
 * @param {Object} allItems - All items from configManager
 * @returns {Object} - { generated: Object, totalGenerated: Object }
 */
function simulateMinionGeneration(minion, allItems) {
  // Get minion definition and tier data
  const minionDef = allItems[minion.itemKey];
  if (!minionDef || !minionDef.tiers || !minionDef.tiers[minion.tier]) {
    return { generated: {}, totalGenerated: {} };
  }

  const tierData = minionDef.tiers[minion.tier];
  let baseInterval = tierData.generationIntervalSeconds || 30; // Default 30 seconds

  // Apply fuel speed boost if active
  if (
    minion.fuel &&
    minion.fuel.expiresAt &&
    Date.now() < minion.fuel.expiresAt
  ) {
    const speedBoost = minion.fuel.speedBoost || 0;
    baseInterval = baseInterval / (1 + speedBoost); // Reduce interval = faster generation
  }

  // Calculate time elapsed since last collection with robust timestamp validation
  const now = Date.now();
  let lastCollection = minion.lastCollectionTimestamp || now;

  // Enhanced timestamp validation and correction
  const timestampValidation = validateAndFixTimestamp(
    minion,
    lastCollection,
    now
  );
  if (timestampValidation.wasFixed) {
    lastCollection = timestampValidation.correctedTimestamp;
    minion.lastCollectionTimestamp = lastCollection;

    // Log for audit trail
    console.warn(
      `[MinionGeneration] Timestamp corrected for minion ${minion.id} (${minion.itemKey}): ` +
        `Original: ${new Date(timestampValidation.originalTimestamp).toISOString()}, ` +
        `Corrected: ${new Date(lastCollection).toISOString()}, ` +
        `Reason: ${timestampValidation.reason}`
    );
  }

  const timeElapsed = Math.max(0, now - lastCollection);

  // Calculate how many items should have been generated
  const itemsToGenerate = Math.floor(timeElapsed / (baseInterval * 1000));

  if (itemsToGenerate === 0) {
    return { generated: {}, totalGenerated: {} };
  }

  // Initialize storage (ensure backward compatibility)
  if (!minion.resourcesStored) {
    minion.resourcesStored = {};
  }

  // Track what we generate for display purposes
  const generated = {};
  const totalGenerated = {};

  // Determine what items this minion generates
  let itemsToGenerateList = [];

  if (minionDef.drops && Array.isArray(minionDef.drops)) {
    // Minion with multiple possible drops (supports min/max amounts)
    itemsToGenerateList = minionDef.drops.map((drop) => ({
      itemKey: drop.itemKey,
      chance: drop.chance || 1.0,
      min: drop.min || 1,
      max: drop.max || 1,
    }));
  } else if (minionDef.resourceItemKey) {
    // Simple minion with single resource
    itemsToGenerateList = [
      {
        itemKey: minionDef.resourceItemKey,
        chance: 1.0,
        min: 1,
        max: 1,
      },
    ];
  } else {
    return { generated: {}, totalGenerated: {} };
  }

  // Calculate fuel consumption for the entire simulation upfront (immutable approach)
  const actualFuelQuantity = minion.fuel?.quantity || 0;
  const fuelExpiresAt = minion.fuel?.expiresAt || 0;
  const fuelType = minion.fuel?.fuelType;
  let cyclesWithFuel = 0;
  let fuelData = null;
  let cyclesPerFuel = 0;

  if (
    minion.fuel &&
    actualFuelQuantity > 0 &&
    fuelExpiresAt &&
    Date.now() < fuelExpiresAt &&
    fuelType
  ) {
    fuelData = allItems[fuelType];
    if (fuelData && fuelData.minionFuel) {
      cyclesPerFuel = Math.floor(
        (fuelData.minionFuel.durationMinutes * 60) /
          (tierData.generationIntervalSeconds || 30)
      );

      // Calculate how many cycles can be fueled with current fuel quantity
      const totalFueledCycles = actualFuelQuantity * cyclesPerFuel;
      cyclesWithFuel = Math.min(totalFueledCycles, itemsToGenerate);
    }
  }

  // Calculate fuel consumption based on actual time passed (time-based, not cycle-based)
  let finalFuelQuantity = actualFuelQuantity;
  let fuelConsumed = 0;

  if (cyclesWithFuel > 0 && fuelData && fuelData.minionFuel) {
    // Calculate actual time consumed (in seconds)
    const timeConsumedSeconds =
      cyclesWithFuel * (tierData.generationIntervalSeconds || 30);

    // Calculate fuel consumption based on time ratio
    const fuelDurationSeconds = fuelData.minionFuel.durationMinutes * 60;
    const fuelConsumedExact = timeConsumedSeconds / fuelDurationSeconds;

    // Only consume the exact amount of fuel used (no rounding up)
    fuelConsumed = Math.min(fuelConsumedExact, actualFuelQuantity);
    finalFuelQuantity = Math.max(0, actualFuelQuantity - fuelConsumed);
  }

  // Simulate generating items one by one
  for (let i = 0; i < itemsToGenerate; i++) {
    // Determine if this cycle should use fuel boost (immutable check)

    // Determine what item to generate this cycle
    for (const itemDef of itemsToGenerateList) {
      if (Math.random() <= itemDef.chance) {
        const itemKey = itemDef.itemKey;

        // Calculate amount to generate (random between min and max)
        const amountToGenerate =
          Math.floor(Math.random() * (itemDef.max - itemDef.min + 1)) +
          itemDef.min;

        // Check if storage will be full after adding these items
        const currentStorageCount = getCurrentStorageCount(minion);
        const maxStorageCapacity = getMaxStorageCapacity(minion, allItems);

        let finalAmountToGenerate = amountToGenerate;

        if (currentStorageCount + amountToGenerate > maxStorageCapacity) {
          // Storage will be full, try to sell with hopper first
          const soldItems = tryAutoSell(
            minion,
            allItems,
            itemKey,
            amountToGenerate,
            i,
            baseInterval,
            lastCollection
          );
          if (soldItems === 0) {
            // No hopper or couldn't sell, generate only what fits
            const spaceAvailable = maxStorageCapacity - currentStorageCount;
            if (spaceAvailable <= 0) {
              // No space at all, stop generating
              break;
            }
            finalAmountToGenerate = spaceAvailable;
          }
          // If we sold items, we have space, continue with full amount
        }

        // Add items to storage
        minion.resourcesStored[itemKey] =
          (minion.resourcesStored[itemKey] || 0) + finalAmountToGenerate;

        // Track for display
        generated[itemKey] = (generated[itemKey] || 0) + finalAmountToGenerate;
        totalGenerated[itemKey] =
          (totalGenerated[itemKey] || 0) + finalAmountToGenerate;

        // Note: Compacting will be applied after all generation is complete

        // If we generated less than intended due to storage limits, stop generating
        if (finalAmountToGenerate < amountToGenerate) {
          break;
        }
      }
    }

    // Check if storage is full after this generation cycle
    if (
      getCurrentStorageCount(minion) >= getMaxStorageCapacity(minion, allItems)
    ) {
      break;
    }
  }

  // Apply comprehensive compacting to all items after generation is complete
  const { processCompacting } = require("./compactorUtils");
  const compactResult = processCompacting(minion, allItems);

  if (compactResult.compacted && compactResult.changes.length > 0) {
    // Silent completion
  }

  // Apply fuel consumption changes after simulation is complete (atomic update)
  if (fuelConsumed > 0) {
    if (finalFuelQuantity <= 0) {
      // All fuel consumed, remove fuel entirely
      minion.fuel = null;
    } else {
      // Update fuel quantity
      minion.fuel.quantity = finalFuelQuantity;
    }
  }

  // Update last collection timestamp
  minion.lastCollectionTimestamp = now;

  return {
    generated,
    totalGenerated,
    compacted: compactResult.compacted,
    compactChanges: compactResult.changes,
    fuelConsumed: fuelConsumed, // For debugging/logging
  };
}

// Note: Compacting logic moved to compactorUtils.js for consistency
// All compacting is now handled by processCompacting() after generation is complete

// Removed: applyRegularCompacting() - now handled by compactorUtils.js

// Removed: applySuperCompacting() - now handled by compactorUtils.js

/**
 * Checks if minion storage is full
 * @param {Object} minion - The minion object
 * @param {Object} allItems - All items from configManager
 * @returns {boolean} - True if storage is full
 */
function isStorageFull(minion, allItems) {
  const minionDef = allItems[minion.itemKey];
  if (!minionDef || !minionDef.tiers || !minionDef.tiers[minion.tier]) {
    return false;
  }

  const tierData = minionDef.tiers[minion.tier];
  let maxStorage = tierData.maxStorage || 64;

  // Apply storage upgrades
  if (minion.upgrades && Array.isArray(minion.upgrades)) {
    for (const upgradeKey of minion.upgrades) {
      if (!upgradeKey) continue;
      const upgrade = allItems[upgradeKey];
      if (upgrade?.upgradeEffect?.extraStorage) {
        maxStorage += upgrade.upgradeEffect.extraStorage;
      }
    }
  }

  // Count current storage (excluding coins)
  let currentStorage = 0;
  for (const [itemKey, amount] of Object.entries(
    minion.resourcesStored || {}
  )) {
    if (itemKey !== "COINS") {
      currentStorage += amount;
    }
  }

  return currentStorage >= maxStorage;
}

/**
 * Formats the display for minion storage - shows ALL items that exist
 * @param {Object} minion - The minion object
 * @param {Object} allItems - All items from configManager
 * @param {boolean} compact - Whether to use compact display (fewer emojis)
 * @returns {string} - Formatted display string
 */
function formatMinionStorageDisplay(minion, allItems, compact = false) {
  const storage = minion.resourcesStored || {};
  const displayItems = [];
  let coinsDisplay = "";
  let totalItems = 0;

  // Show all items that exist in storage
  for (const [itemKey, amount] of Object.entries(storage)) {
    if (amount > 0) {
      if (itemKey === "COINS") {
        const purseEmoji = compact
          ? "💰"
          : "<:purse_coins:1367849116033482772>";
        coinsDisplay = `${purseEmoji} ${formatNumber(amount)}`;
      } else {
        if (compact) {
          // Compact mode: show emoji and amount
          const emoji = allItems[itemKey]?.emoji || "?";
          displayItems.push(`${emoji} ${amount}`);
        } else {
          // Full mode: show emoji and amount
          const emoji = allItems[itemKey]?.emoji || "?";
          displayItems.push(`${emoji} ${amount}`);
        }
        totalItems += amount;
      }
    }
  }

  // Get max storage
  const minionDef = allItems[minion.itemKey];
  const tierData = minionDef?.tiers?.[minion.tier];
  let maxStorage = tierData?.maxStorage || 64;

  // Apply storage upgrades
  if (minion.upgrades && Array.isArray(minion.upgrades)) {
    for (const upgradeKey of minion.upgrades) {
      if (!upgradeKey) continue;
      const upgrade = allItems[upgradeKey];
      if (upgrade?.upgradeEffect?.extraStorage) {
        maxStorage += upgrade.upgradeEffect.extraStorage;
      }
    }
  }

  // Build display
  const itemsDisplay = displayItems.join(compact ? ", " : " ");
  if (coinsDisplay && itemsDisplay) {
    return `${itemsDisplay}${compact ? ", " : " "}${coinsDisplay}\n**Total: ${totalItems}/${maxStorage}**`;
  } else if (coinsDisplay) {
    return `${coinsDisplay}\n**Total: ${totalItems}/${maxStorage}**`;
  } else if (itemsDisplay) {
    return `${itemsDisplay}\n**Total: ${totalItems}/${maxStorage}**`;
  } else {
    return `**Total: 0/${maxStorage}**`;
  }
}

/**
 * Gets current storage count (excluding coins)
 * @param {Object} minion - The minion object
 * @returns {number} - Current storage count
 */
function getCurrentStorageCount(minion) {
  let count = 0;
  for (const [itemKey, amount] of Object.entries(
    minion.resourcesStored || {}
  )) {
    if (itemKey !== "COINS") {
      count += amount;
    }
  }
  return count;
}

/**
 * Gets maximum storage capacity including upgrades
 * @param {Object} minion - The minion object
 * @param {Object} allItems - All items from configManager
 * @returns {number} - Maximum storage capacity
 */
function getMaxStorageCapacity(minion, allItems) {
  const minionDef = allItems[minion.itemKey];
  if (!minionDef || !minionDef.tiers || !minionDef.tiers[minion.tier]) {
    return 64;
  }

  const tierData = minionDef.tiers[minion.tier];
  let maxStorage = tierData.maxStorage || 64;

  // Apply storage upgrades
  if (minion.upgrades && Array.isArray(minion.upgrades)) {
    for (const upgradeKey of minion.upgrades) {
      if (!upgradeKey) continue;
      const upgrade = allItems[upgradeKey];
      if (upgrade?.upgradeEffect?.extraStorage) {
        maxStorage += upgrade.upgradeEffect.extraStorage;
      }
    }
  }

  return maxStorage;
}

/**
 * Tries to auto-sell items when storage is full (hopper functionality)
 * @param {Object} minion - The minion object
 * @param {Object} allItems - All items from configManager
 * @param {string} itemKey - The item that was just generated
 * @param {number} amountGenerated - How many items were just generated
 * @param {number} cycleIndex - Current generation cycle index
 * @param {number} baseInterval - Generation interval in seconds
 * @param {number} lastCollection - Last collection timestamp
 * @returns {number} - Number of items sold (0 if no selling occurred)
 */
function tryAutoSell(
  minion,
  allItems,
  itemKey,
  amountGenerated,
  cycleIndex,
  baseInterval,
  lastCollection
) {
  if (!minion.upgrades || !Array.isArray(minion.upgrades)) {
    return 0;
  }

  // Check for hopper upgrades
  let hopperInfo = null;
  for (const upgradeKey of minion.upgrades) {
    if (!upgradeKey) continue;
    const upgrade = allItems[upgradeKey];
    if (upgrade?.upgradeEffect?.automatedSelling?.enabled) {
      hopperInfo = {
        upgradeKey: upgradeKey,
        sellPercentage:
          upgrade.upgradeEffect.automatedSelling.sellPercentage || 0.5,
      };
      break;
    }
  }

  if (!hopperInfo) {
    return 0; // No hopper equipped
  }

  // Check if hopper was active for this generation cycle
  const cycleTimestamp = lastCollection + cycleIndex * baseInterval * 1000;
  const hopperPlacedTime = getHopperPlacedTime(minion, hopperInfo);

  if (!hopperPlacedTime || cycleTimestamp < hopperPlacedTime) {
    return 0; // Hopper wasn't active yet for this cycle
  }

  // Try to sell the newly generated items
  const item = allItems[itemKey];
  if (!item || !item.sellable || !item.sellPrice) {
    return 0; // Item can't be sold
  }

  const sellValue = Math.floor(item.sellPrice * hopperInfo.sellPercentage);
  const totalCoinsEarned = sellValue * amountGenerated;

  // Add coins to minion storage (coins don't count toward storage limit)
  minion.resourcesStored["COINS"] =
    (minion.resourcesStored["COINS"] || 0) + totalCoinsEarned;

  // Remove the items that were sold from storage
  minion.resourcesStored[itemKey] = Math.max(
    0,
    (minion.resourcesStored[itemKey] || 0) - amountGenerated
  );
  if (minion.resourcesStored[itemKey] === 0) {
    delete minion.resourcesStored[itemKey];
  }

  return amountGenerated; // Return number of items sold
}

/**
 * Gets when a hopper was placed on the minion
 * @param {Object} minion - The minion object
 * @param {Object} hopperInfo - Hopper information
 * @returns {number|null} - Timestamp when hopper was placed
 */
function getHopperPlacedTime(minion, hopperInfo) {
  if (!hopperInfo) return null;

  // If no upgradeTimestamps field exists, this is a legacy hopper
  // Use the minion's lastCollectionTimestamp to prevent retroactive coin generation
  if (!minion.upgradeTimestamps) {
    return minion.lastCollectionTimestamp || Date.now();
  }

  return (
    minion.upgradeTimestamps[hopperInfo.upgradeKey] ||
    minion.lastCollectionTimestamp ||
    Date.now()
  );
}

/**
 * Validates and fixes problematic timestamps
 * @param {Object} minion - The minion object
 * @param {number} lastCollection - The last collection timestamp
 * @param {number} now - Current timestamp
 * @returns {Object} - Validation result with corrected timestamp if needed
 */
function validateAndFixTimestamp(minion, lastCollection, now) {
  const originalTimestamp = lastCollection;
  let correctedTimestamp = lastCollection;
  let wasFixed = false;
  let reason = "";

  // Check for future timestamps (most common issue)
  if (lastCollection > now) {
    const timeDiff = lastCollection - now;
    const maxReasonableFutureOffset = 30000; // 30 seconds

    if (timeDiff > maxReasonableFutureOffset) {
      correctedTimestamp = now;
      wasFixed = true;
      reason = `Future timestamp by ${Math.round(timeDiff / 1000)}s`;
    } else {
      // Small future offset might be due to server time drift, use current time
      correctedTimestamp = now;
      wasFixed = true;
      reason = `Minor future timestamp by ${timeDiff}ms`;
    }
  }

  // Check for extremely old timestamps (possible corruption)
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
  if (now - lastCollection > maxAge) {
    // Don't generate for more than 30 days worth of items
    correctedTimestamp = now - maxAge;
    wasFixed = true;
    reason = `Timestamp too old (${Math.round((now - lastCollection) / (24 * 60 * 60 * 1000))} days)`;
  }

  // Check for invalid timestamps (NaN, undefined, etc.)
  if (!Number.isFinite(lastCollection) || lastCollection <= 0) {
    correctedTimestamp = now;
    wasFixed = true;
    reason = `Invalid timestamp value: ${lastCollection}`;
  }

  return {
    originalTimestamp,
    correctedTimestamp,
    wasFixed,
    reason,
  };
}

module.exports = {
  simulateMinionGeneration,
  formatMinionStorageDisplay,
  isStorageFull,
  validateAndFixTimestamp,
};
