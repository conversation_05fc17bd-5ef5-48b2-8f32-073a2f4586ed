// Migration: 070_add_crop_upgrades.js
// Purpose: Add per-crop upgrade storage for +Farming Fortune tiers

module.exports.up = async function up(db) {
  return new Promise((resolve, reject) => {
    db.run(
      "ALTER TABLE players ADD COLUMN garden_crop_upgrades_json TEXT",
      (err) => {
        if (err && !err.message.includes("duplicate column")) {
          console.error(
            "[Migration 070] Failed to add garden_crop_upgrades_json column:",
            err.message,
          );
          reject(err);
          return;
        }

        console.log(
          "[Migration 070] garden_crop_upgrades_json column added (or already exists).",
        );
        resolve();
      },
    );
  });
};
