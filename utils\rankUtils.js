/**
 * Generates the display prefix for a character's rank.
 * Returns an empty string if the rank is 'MEMBER' or undefined.
 * @param {object} character The character object from getPlayerData.
 * @returns {string} The rank prefix string (e.g., "[Admin] ") or an empty string.
 */
function getRankPrefix(character) {
  if (character && character.rank && character.rank !== "MEMBER") {
    // Use the rank exactly as stored in the database for the prefix
    return `[${character.rank}] `;
  }
  return ""; // Return empty string for 'MEMBER' or if rank is missing/null
}

module.exports = { getRankPrefix };
