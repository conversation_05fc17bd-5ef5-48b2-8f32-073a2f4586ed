const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  EmbedBuilder,
  MessageFlags,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const { EMBED_COLORS, COPPER } = require("../gameConfig");
const {
  getGardenLevel,
  getXpForNextGardenLevel,
  getCropMilestoneInfo,
  getUnlockedCrops,
  getCropUpgradeTier,
  getNextUpgradeInfo,
  purchaseCropUpgrade,
  CROP_UPGRADE_MAX_TIER,
  CROP_UPGRADE_FORTUNE_PER_TIER,
} = require("../utils/gardenSystem");
const configManager = require("../utils/configManager");
const { createProgressBar } = require("../utils/displayUtils");
const { formatXP } = require("../utils/formatUtils");
const NPCS = require("../data/npcs.js");
const { v4: uuidv4 } = require("uuid");

/**
 * Generates a shop display embed for an NPC merchant
 * @param {object} npc - The NPC object
 * @param {object} allItems - All items data
 * @param {number} playerCoins - Player's current coins
 * @param {boolean} showDetails - Whether to show item details
 * @returns {object} - Object containing embed and hasDetails flag
 */
async function generateShopDisplay(
  npc,
  allItems,
  playerCoins,
  showDetails = false
) {
  // Determine currency type and emoji
  const currencyType = npc.currency || "coins";
  const currencyConfig =
    currencyType === "copper"
      ? require("../gameConfig").COPPER
      : require("../gameConfig").CURRENCY;
  const currencyEmoji = currencyConfig.purseEmoji || currencyConfig.emoji;

  const inventoryItems =
    npc.shopInventory
      ?.sort((a, b) => (b.price || 0) - (a.price || 0))
      ?.map((item) => {
        const itemData = allItems[item.itemKey];
        if (!itemData) {
          console.warn(
            `[generateShopDisplay] Item data not found for key: ${item.itemKey} in NPC ${npc.key}'s inventory.`
          );
          return null;
        }
        const name = itemData.name || `Unknown (${item.itemKey})`;
        const price = item.price?.toLocaleString() || "?";
        const itemEmoji = itemData.emoji || "❓";
        return { name, price, itemEmoji, itemData };
      })
      .filter((item) => item !== null) || [];

  const maxNameLen = Math.max(
    ...inventoryItems.map((item) => item.name.length),
    0
  );

  // Check if any items have details to show
  const hasDetails = inventoryItems.some((item) => {
    return (
      (item.itemData.baseStats &&
        Object.keys(item.itemData.baseStats).length > 0) ||
      (item.itemData.effects &&
        Object.keys(item.itemData.effects).length > 0) ||
      (item.itemData.description && item.itemData.description.trim() !== "")
    );
  });

  // Generate item display strings
  const itemDisplays = inventoryItems.map((item) => {
    let statsDisplay = "";

    if (showDetails) {
      // Check if item has baseStats to show
      if (
        item.itemData.baseStats &&
        Object.keys(item.itemData.baseStats).length > 0
      ) {
        const { STATS } = require("../gameConfig");
        statsDisplay = Object.entries(item.itemData.baseStats)
          .map(([statKey, value]) => {
            const statConfig = STATS[statKey];
            const statEmoji = statConfig?.emoji || "❓";
            const statName = statConfig?.name || statKey;
            return `\n${statEmoji} **\`+${value} ${statName}\`**`;
          })
          .join("");
      } else {
        const description = item.itemData.description
          ? `\n${item.itemData.description}`
          : "";
        statsDisplay = description;
      }
    }

    // Pad name and price for alignment
    const paddedName = item.name.padEnd(maxNameLen, " ");
    const paddedPrice = item.price.padStart(7, " ");
    return `${item.itemEmoji} \`${paddedName} ${paddedPrice}\` ${currencyEmoji}${statsDisplay}`;
  });

  // get dynamic greeting with top player substitution
  const { getNPCGreeting } = require("../data/npcs.js");
  const dynamicGreeting = await getNPCGreeting(npc.key);

  // Create the base embed
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${npc.emoji || "❓"} ${npc.name}'s Shop`)
    .setDescription(dynamicGreeting || "How can I help you?");

  // Add items field
  if (itemDisplays.length > 0) {
    embed.addFields({
      name: "Items for Sale",
      value: itemDisplays.join("\n"),
      inline: false,
    });
  }

  // Add currency field
  const currencyName =
    currencyType === "copper" ? currencyConfig.name : "Coins";
  embed.addFields({
    name: `Your ${currencyName}`,
    value: `${currencyEmoji} ${playerCoins.toLocaleString()}`,
    inline: true,
  });

  return { embed, hasDetails };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("desk")
    .setDescription("Access your Garden Desk (only available in Garden)"),

  async execute(interaction) {
    const userId = interaction.user.id;
    let character;

    try {
      character = await getPlayerData(userId);
    } catch (error) {
      console.error(
        `[Desk Command] Error fetching player data for ${userId}:`,
        error
      );
      return interaction.reply({
        content: "Error fetching your character data. Please try again.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!character) {
      return interaction.reply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!checkRankPermission(character, "MEMBER")) {
      return interaction.reply({
        content: "You don't have permission to use this command.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Check if player is in Garden
    if (character.current_region !== "garden") {
      return interaction.reply({
        content: "You can only access this while in the Garden!",
        flags: [MessageFlags.Ephemeral],
      });
    }

    await this.showGardenDesk(interaction, character);
  },

  async showGardenDesk(interaction, character) {
    const gardenXp = character.garden_xp || 0;
    const gardenLevel = getGardenLevel(gardenXp);
    const nextLevelXp = getXpForNextGardenLevel(gardenLevel);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PASTEL_GREEN)
      .setTitle("<:garden:1394656922623410237> Garden Desk")
      .setFooter({ text: "Manage your Garden from your desk!" });

    // Build Garden level display (same as /garden command)
    let description = `**Garden Level** \`${gardenLevel}\`\n`;

    if (nextLevelXp > 0) {
      const progressBar = createProgressBar(gardenXp, nextLevelXp, {
        showPercentage: true,
        size: 10,
        useEmojis: true,
      });
      description += `${progressBar}\n`;
      description += `**${formatXP(gardenXp)} / ${formatXP(nextLevelXp)}**\n`;
    } else {
      const progressBar = createProgressBar(1, 1, {
        showPercentage: true,
        size: 10,
        useEmojis: true,
      });
      description += `${progressBar}\n`;
      description += `**${formatXP(gardenXp)} XP (MAX LEVEL)**\n`;
    }

    embed.setDescription(description);

    // Create select menu with the three options
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("desk_menu")
      .setPlaceholder("Select an option from your Garden Desk")
      .addOptions([
        {
          label: "Crop Upgrades",
          value: "crop_upgrades",
          description: "Upgrade crops for global Farming Fortune",
          emoji: "<:diamond_hoe:1373220303458795642>",
        },
        {
          label: "SkyMart",
          value: "skymart",
          description: "SkyMart",
          emoji: "<:copper:1396059859539198045>",
        },
        {
          label: "Garden Milestones",
          value: "garden_milestones",
          description: "Crop Milestones",
          emoji: "<:enchanted_wheat:1367978489277583450>",
        },
        {
          label: "Visitor Analytics",
          value: "visitor_analytics",
          description: "Visitor Statistics",
          emoji: "<:npc_adventurer:1384162058194387034>",
        },
      ]);

    const components = [new ActionRowBuilder().addComponents(selectMenu)];

    if (interaction.deferred || interaction.replied) {
      await interaction.editReply({ embeds: [embed], components });
    } else {
      await interaction.reply({ embeds: [embed], components });
    }
  },

  async showVisitorAnalytics(interaction) {
    const { getVisitorStats } = require("../utils/visitorAnalytics");

    try {
      const stats = await getVisitorStats(interaction.user.id);

      if (!stats) {
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Error")
          .setDescription(
            "Could not load visitor analytics. Please try again."
          );

        const backButton = new ButtonBuilder()
          .setCustomId("desk_back")
          .setLabel("← Back to Garden Desk")
          .setStyle(ButtonStyle.Secondary);

        await interaction.editReply({
          embeds: [embed],
          components: [new ActionRowBuilder().addComponents(backButton)],
        });
        return;
      }

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.PASTEL_GREEN)
        .setTitle(
          "<:npc_adventurer:1384162058194387034> Garden Visitor Analytics"
        );

      let description = `**Overall Progress**\n`;
      description += `Offers Completed: **${stats.offersAccepted.toLocaleString()}**\n`;
      description += `Unique Visitors Served: **${stats.uniqueVisitorsServed}/${stats.totalUniqueVisitors}** (${stats.completionRate.toFixed(1)}%)\n\n`;

      // Rarity breakdown
      description += `**Progress by Rarity**\n`;
      const rarityOrder = [
        "UNCOMMON",
        "RARE",
        "LEGENDARY",
        "MYTHIC",
        "SPECIAL",
      ];
      for (const rarity of rarityOrder) {
        if (stats.rarityBreakdown[rarity]) {
          const { served, total } = stats.rarityBreakdown[rarity];
          const percentage =
            total > 0 ? ((served / total) * 100).toFixed(1) : "0.0";
          const rarityEmoji =
            rarity === "UNCOMMON"
              ? "⚪"
              : rarity === "RARE"
                ? "🔵"
                : rarity === "LEGENDARY"
                  ? "🟡"
                  : rarity === "MYTHIC"
                    ? "🟣"
                    : "🔴";
          description += `${rarityEmoji} ${rarity}: **${served}/${total}** (${percentage}%)\n`;
        }
      }

      embed.setDescription(description);

      const npcListButton = new ButtonBuilder()
        .setCustomId("visitor_npc_list")
        .setEmoji("<:npc_adventurer:1384162058194387034>")
        .setStyle(ButtonStyle.Secondary);

      const backButton = new ButtonBuilder()
        .setCustomId("desk_back")
        .setLabel("← Back to Garden Desk")
        .setStyle(ButtonStyle.Secondary);

      await interaction.editReply({
        embeds: [embed],
        components: [
          new ActionRowBuilder().addComponents(npcListButton, backButton),
        ],
      });
    } catch (error) {
      console.error("[Desk] Error showing visitor analytics:", error);

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("❌ Error")
        .setDescription("An error occurred while loading visitor analytics.");

      const backButton = new ButtonBuilder()
        .setCustomId("desk_back")
        .setLabel("← Back to Garden Desk")
        .setStyle(ButtonStyle.Secondary);

      await interaction.editReply({
        embeds: [embed],
        components: [new ActionRowBuilder().addComponents(backButton)],
      });
    }
  },

  async showNPCList(interaction) {
    const { GARDEN_VISITORS } = require("../utils/gardenVisitors");

    try {
      const character = await getPlayerData(interaction.user.id);
      if (!character) {
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("❌ Error")
          .setDescription(
            "Could not load your character data. Please try again."
          );

        const backButton = new ButtonBuilder()
          .setCustomId("visitor_analytics_back")
          .setLabel("← Back to Analytics")
          .setStyle(ButtonStyle.Secondary);

        await interaction.editReply({
          embeds: [embed],
          components: [new ActionRowBuilder().addComponents(backButton)],
        });
        return;
      }

      // Get unique visitors served data
      let uniqueServed = [];
      if (character.visitor_unique_served) {
        if (Array.isArray(character.visitor_unique_served)) {
          // Already parsed by playerDataManager
          uniqueServed = character.visitor_unique_served;
        } else {
          // Still a JSON string, need to parse
          try {
            uniqueServed = JSON.parse(character.visitor_unique_served);
          } catch {
            uniqueServed = [];
          }
        }
      }

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.PASTEL_GREEN)
        .setTitle("<:npc_adventurer:1384162058194387034> All Garden Visitors");

      let description = "";
      const sortedVisitors = Object.entries(GARDEN_VISITORS).sort((a, b) => {
        // Sort by rarity priority, then by name
        const rarityOrder = {
          UNCOMMON: 1,
          RARE: 2,
          LEGENDARY: 3,
          MYTHIC: 4,
          SPECIAL: 5,
        };
        const rarityDiff = rarityOrder[a[1].rarity] - rarityOrder[b[1].rarity];
        if (rarityDiff !== 0) return rarityDiff;
        return a[1].name.localeCompare(b[1].name);
      });

      for (const [visitorKey, visitorData] of sortedVisitors) {
        const hasBeenServed = uniqueServed.includes(visitorKey);
        const statusIcon = hasBeenServed ? "✅" : "❌";
        description += `${statusIcon} ${visitorData.emoji} ${visitorData.name} **${visitorData.rarity}**\n\n`;
      }

      embed.setDescription(description);

      const backButton = new ButtonBuilder()
        .setCustomId("visitor_analytics_back")
        .setLabel("← Back to Analytics")
        .setStyle(ButtonStyle.Secondary);

      await interaction.editReply({
        embeds: [embed],
        components: [new ActionRowBuilder().addComponents(backButton)],
      });
    } catch (error) {
      console.error("[Desk] Error showing NPC list:", error);

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("❌ Error")
        .setDescription("An error occurred while loading the NPC list.");

      const backButton = new ButtonBuilder()
        .setCustomId("visitor_analytics_back")
        .setLabel("← Back to Analytics")
        .setStyle(ButtonStyle.Secondary);

      await interaction.editReply({
        embeds: [embed],
        components: [new ActionRowBuilder().addComponents(backButton)],
      });
    }
  },

  async showGardenMilestones(interaction, character) {
    const { v4: uuidv4 } = require("uuid");
    const gardenXp = character.garden_xp || 0;
    const gardenLevel = getGardenLevel(gardenXp);
    const unlockedCrops = getUnlockedCrops(gardenLevel);
    const allItems = configManager.getAllItems();
    const { formatNumber } = require("../utils/displayUtils");
    let totalMilestones = 0;
    const cropProgress = [];
    for (const cropKey of unlockedCrops) {
      const itemData = allItems[cropKey];
      const cropInfo = getCropMilestoneInfo(character, cropKey);
      totalMilestones += cropInfo.currentMilestone;
      if (cropInfo.harvested > 0 || itemData) {
        const emoji = itemData?.emoji || "❓";
        const cropName = itemData?.name || cropKey;
        if (cropInfo.isMaxed) {
          const progressBar = createProgressBar(1, 1, {
            showPercentage: false,
            size: 10,
            useEmojis: true,
          });
          const maxMilestoneRequired =
            require("../utils/gardenSystem").CROP_MILESTONES[cropKey]?.[
              cropInfo.maxMilestone - 1
            ] || 0;
          cropProgress.push(
            `${emoji} **${cropName}: MAXED! (Milestone ${cropInfo.currentMilestone}/${cropInfo.maxMilestone})**\n${progressBar}\n**Total: ${formatNumber(cropInfo.harvested)}/${formatNumber(maxMilestoneRequired)}**`
          );
        } else if (cropInfo.nextRequired > 0) {
          const progressStartAmount =
            cropInfo.currentMilestone > 0
              ? require("../utils/gardenSystem").CROP_MILESTONES[cropKey]?.[
                  cropInfo.currentMilestone - 1
                ] || 0
              : 0;
          const progressCurrent = cropInfo.harvested - progressStartAmount;
          const progressMax = cropInfo.nextRequired - progressStartAmount;
          const progressBar = createProgressBar(progressCurrent, progressMax, {
            showPercentage: false,
            size: 10,
            useEmojis: true,
          });
          const maxMilestoneRequired =
            require("../utils/gardenSystem").CROP_MILESTONES[cropKey]?.[
              cropInfo.maxMilestone - 1
            ] || 0;
          cropProgress.push(
            `${emoji} **${cropName}: ${formatNumber(progressCurrent)}/${formatNumber(progressMax)} (Milestone ${cropInfo.currentMilestone + 1}/${cropInfo.maxMilestone})**\n${progressBar}\n**Total: ${formatNumber(cropInfo.harvested)}/${formatNumber(maxMilestoneRequired)}**`
          );
        } else {
          const maxMilestoneRequired =
            require("../utils/gardenSystem").CROP_MILESTONES[cropKey]?.[0] || 0;
          cropProgress.push(
            `${emoji} **${cropName}: ${formatNumber(cropInfo.harvested)} (No milestones)**\n**Total: ${formatNumber(cropInfo.harvested)}/${formatNumber(maxMilestoneRequired)}**`
          );
        }
      }
    }
    const { CROP_UNLOCKS } = require("../utils/gardenSystem");
    const totalCrops = Object.values(CROP_UNLOCKS).flat().length;
    const header = `**Total Milestones** \`${totalMilestones}\`\n**Unlocked Crops:** \`${unlockedCrops.length}/${totalCrops}\`\n\n`;
    const cropHeader = cropProgress.length > 0 ? "**Crop Progress:**\n" : "";
    const lines =
      cropProgress.length > 0
        ? cropProgress
        : ["No crops harvested yet! Start farming to track your progress!"];
    const pages = [];
    let current = header + cropHeader;
    for (const line of lines) {
      if (current.length + line.length + 2 > 4000) {
        pages.push(current.trim());
        current = header + cropHeader + line + "\n\n";
      } else {
        current += line + "\n\n";
      }
    }
    pages.push(current.trim());
    let currentPage = 0;
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PASTEL_GREEN)
      .setTitle("<:garden:1394656922623410237> Garden Milestones")
      .setFooter({ text: "Track your crop milestone progress!" })
      .setDescription(pages[currentPage]);
    const prevId = `gm_prev_${uuidv4()}`;
    const nextId = `gm_next_${uuidv4()}`;
    const prevButton = new ButtonBuilder()
      .setCustomId(prevId)
      .setLabel("← Previous")
      .setStyle(ButtonStyle.Primary)
      .setDisabled(true);
    const nextButton = new ButtonBuilder()
      .setCustomId(nextId)
      .setLabel("Next →")
      .setStyle(ButtonStyle.Primary)
      .setDisabled(pages.length === 1);
    const backButton = new ButtonBuilder()
      .setCustomId("desk_back")
      .setLabel("← Back to Garden Desk")
      .setStyle(ButtonStyle.Secondary);
    const rowNav = new ActionRowBuilder().addComponents(prevButton, nextButton);
    const rowBack = new ActionRowBuilder().addComponents(backButton);
    await interaction.editReply({
      embeds: [embed],
      components: pages.length === 1 ? [rowBack] : [rowNav, rowBack],
    });
    if (pages.length === 1) return;
    const message = await interaction.fetchReply();
    const filter = (i) =>
      i.user.id === interaction.user.id &&
      [prevId, nextId, "desk_back"].includes(i.customId);
    const collector = message.createMessageComponentCollector({
      filter,
      time: 300000,
    });
    collector.on("collect", async (btn) => {
      if (btn.customId === "desk_back") {
        await btn.deferUpdate();
        collector.stop();
        await this.showGardenDesk(btn, character);
        return;
      }
      await btn.deferUpdate();
      if (btn.customId === prevId && currentPage > 0) currentPage--;
      if (btn.customId === nextId && currentPage < pages.length - 1)
        currentPage++;
      prevButton.setDisabled(currentPage === 0);
      nextButton.setDisabled(currentPage === pages.length - 1);
      embed.setDescription(pages[currentPage]);
      await message.edit({ embeds: [embed], components: [rowNav, rowBack] });
    });
  },

  // Handle select menu interactions
  async handleSelectMenu(interaction) {
    try {
      // Ignore very old interactions to avoid Discord 10062 Unknown Interaction
      if (
        interaction.createdTimestamp &&
        Date.now() - interaction.createdTimestamp > 14 * 60 * 1000
      ) {
        try {
          await interaction.reply({
            content: "This menu is no longer active. Please run /desk again.",
            ephemeral: true,
          });
        } catch {}
        return;
      }
      const customId = interaction.customId;
      const userId = interaction.user.id;

      // Handle SkyMart item selection
      if (customId.startsWith("skymart_menu:")) {
        const menuUserId = customId.split(":")[1];
        if (menuUserId !== userId) {
          return interaction.reply({
            content: "You cannot use someone else's menus.",
            ephemeral: true,
          });
        }

        const selectedItemKey = interaction.values[0];

        // Get fresh data
        const allItems = await configManager.getAllItems();
        const skymartNPC = NPCS.SKYMART_MERCHANT;

        if (!skymartNPC || !skymartNPC.shopInventory) {
          return interaction.reply({
            content: "SkyMart inventory is currently unavailable.",
            ephemeral: true,
          });
        }

        const selectedItemInfo = skymartNPC.shopInventory.find(
          (i) => i.itemKey === selectedItemKey
        );
        const selectedItemDetails = allItems[selectedItemKey];

        if (!selectedItemInfo || !selectedItemDetails) {
          return interaction.reply({
            content: "Item data missing!",
            ephemeral: true,
          });
        }

        const {
          ModalBuilder,
          TextInputBuilder,
          TextInputStyle,
          ActionRowBuilder,
        } = require("discord.js");

        const modalCustomId = `skymart_multi_modal:${userId}:${selectedItemKey}`;
        const modal = new ModalBuilder()
          .setCustomId(modalCustomId)
          .setTitle(`Buy ${selectedItemDetails.name}`)
          .addComponents(
            new ActionRowBuilder().addComponents(
              new TextInputBuilder()
                .setCustomId("quantity")
                .setLabel(
                  `How many? (${selectedItemInfo.price.toLocaleString()} copper each)`
                )
                .setStyle(TextInputStyle.Short)
                .setRequired(true)
                .setPlaceholder('Enter quantity or "max"')
            )
          );

        await interaction.showModal(modal);
        return;
      }

      // Handle desk menu
      if (customId !== "desk_menu") return;

      const selectedValue = interaction.values[0];

      // Defer with safety net for expired interactions
      try {
        if (!interaction.deferred && !interaction.replied) {
          await interaction.deferUpdate();
        }
      } catch (e) {
        if (e?.code !== 10062) {
          console.warn("[Desk] deferUpdate failed:", e?.message || e);
        }
        return; // Can't proceed if interaction isn't valid
      }

      const character = await getPlayerData(interaction.user.id);
      if (!character) {
        return interaction.followUp({
          content: "Error fetching your character data.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Check if still in Garden
      if (character.current_region !== "garden") {
        return interaction.followUp({
          content: "You must be in the Garden to use the Garden Desk!",
          flags: [MessageFlags.Ephemeral],
        });
      }

      switch (selectedValue) {
        case "crop_upgrades":
          await this.showCropUpgradesOverview(interaction, character);
          break;
        case "skymart":
          await this.showSkymart(interaction, character);
          break;
        case "garden_milestones":
          await this.showGardenMilestones(interaction, character);
          break;
        case "visitor_analytics":
          await this.showVisitorAnalytics(interaction, character);
          break;
      }
    } catch (error) {
      console.error("[Desk] Error in handleSelectMenu:", error);
      try {
        await interaction.followUp({
          content: "An error occurred while processing your request.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch {}
    }
  },

  // Handle modal submissions
  async handleModal(interaction) {
    try {
      const customId = interaction.customId;
      const userId = interaction.user.id;

      // Handle SkyMart purchase modals
      if (
        customId.startsWith("skymart_single_modal:") ||
        customId.startsWith("skymart_multi_modal:")
      ) {
        const parts = customId.split(":");
        const modalUserId = parts[1];
        const itemKey = parts[2];

        if (modalUserId !== userId) {
          return interaction.reply({
            content: "You cannot use someone else's modals.",
            ephemeral: true,
          });
        }

        // Get fresh data
        const allItems = await configManager.getAllItems();
        const skymartNPC = NPCS.SKYMART_MERCHANT;

        if (!skymartNPC || !skymartNPC.shopInventory) {
          return interaction.reply({
            content: "SkyMart inventory is currently unavailable.",
            ephemeral: true,
          });
        }

        const item = skymartNPC.shopInventory.find(
          (i) => i.itemKey === itemKey
        );
        const itemDetails = allItems[itemKey];

        if (!item || !itemDetails) {
          return interaction.reply({
            content: "Item is no longer available for purchase.",
            ephemeral: true,
          });
        }

        await interaction.deferReply();
        await this.processSkymartPurchase(
          interaction,
          item,
          itemDetails,
          userId
        );
        return;
      }
    } catch (error) {
      console.error("[Desk] Error in handleModal:", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "An error occurred while processing your request.",
          ephemeral: true,
        });
      }
    }
  },

  // Handle button interactions
  async handleButton(interaction) {
    try {
      // Ignore very old interactions to avoid Discord 10062 Unknown Interaction
      if (
        interaction.createdTimestamp &&
        Date.now() - interaction.createdTimestamp > 14 * 60 * 1000
      ) {
        try {
          await interaction.reply({
            content:
              "This button is no longer active. Please open /desk again.",
            ephemeral: true,
          });
        } catch {}
        return;
      }
      const customId = interaction.customId;
      const userId = interaction.user.id;

      // --- Crop Upgrades buttons ---
      if (
        customId.startsWith("crop_upgrades_select:") ||
        customId.startsWith("crop_upgrades_back:") ||
        customId.startsWith("crop_upgrades_confirm1:") ||
        customId.startsWith("crop_upgrades_confirmmax:")
      ) {
        const parts = customId.split(":");
        const action = parts[0];
        const btnUserId = parts[1];
        const cropKey = parts[2];

        if (btnUserId !== userId) {
          return interaction.reply({
            content: "You cannot use someone else's buttons.",
            ephemeral: true,
          });
        }

        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062) {
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          }
          return;
        }

        const character = await getPlayerData(userId);
        if (!character) {
          return interaction.followUp({
            content: "Error fetching your character data.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        if (character.current_region !== "garden") {
          return interaction.followUp({
            content: "You must be in the Garden to use the Garden Desk!",
            flags: [MessageFlags.Ephemeral],
          });
        }

        if (action === "crop_upgrades_back") {
          await this.showCropUpgradesOverview(interaction, character);
          return;
        }

        if (action === "crop_upgrades_select") {
          await this.showCropUpgradeConfirm(interaction, character, cropKey);
          return;
        }

        if (action === "crop_upgrades_confirm1") {
          const res = await purchaseCropUpgrade(userId, cropKey, character);
          if (res.success) {
            // Refresh and show overview
            const fresh = await getPlayerData(userId);
            await this.showCropUpgradesOverview(
              interaction,
              fresh,
              `${configManager.getAllItems()[cropKey]?.emoji || ""} Upgraded ${configManager.getAllItems()[cropKey]?.name || cropKey} to Tier ${res.newTier}!`
            );
          } else {
            await this.showCropUpgradeInsufficient(
              interaction,
              character,
              res.message || "Purchase failed."
            );
          }
          return;
        }

        if (action === "crop_upgrades_confirmmax") {
          // Attempt multiple sequential purchases while affordable and gated
          const allItems = configManager.getAllItems();
          let localChar = { ...character };
          let localUpgrades = {};
          try {
            localUpgrades =
              localChar.garden_crop_upgrades_json &&
              typeof localChar.garden_crop_upgrades_json === "object"
                ? localChar.garden_crop_upgrades_json
                : JSON.parse(localChar.garden_crop_upgrades_json || "{}");
          } catch {
            localUpgrades = {};
          }

          let copperLeft = localChar.copper || 0;
          const gardenLevel = getGardenLevel(localChar.garden_xp || 0);
          let purchases = 0;
          while (purchases < 9) {
            const info = getNextUpgradeInfo(localChar, cropKey, gardenLevel);
            if (!info) break; // maxed
            if (!info.canUpgrade) break; // gate
            if (info.cost > copperLeft) break; // not enough
            // Try purchase
            const res = await purchaseCropUpgrade(userId, cropKey, localChar);
            if (!res.success) break;
            purchases++;
            copperLeft -= res.copperSpent || info.cost;
            // Update local state
            const currentTier = (localUpgrades[cropKey] || 0) + 1;
            localUpgrades[cropKey] = currentTier;
            localChar.garden_crop_upgrades_json = localUpgrades;
            localChar.copper = copperLeft;
          }

          if (purchases > 0) {
            const fresh = await getPlayerData(userId);
            await this.showCropUpgradesOverview(
              interaction,
              fresh,
              `${allItems[cropKey]?.emoji || ""} Upgraded ${allItems[cropKey]?.name || cropKey} by ${purchases} tier${purchases > 1 ? "s" : ""}.`
            );
          } else {
            // Figure out reason
            const info = getNextUpgradeInfo(
              character,
              cropKey,
              getGardenLevel(character.garden_xp || 0)
            );
            if (!info) {
              await this.showCropUpgradeInsufficient(
                interaction,
                character,
                "Already at max tier."
              );
            } else if (!info.canUpgrade) {
              await this.showCropUpgradeInsufficient(
                interaction,
                character,
                `Requires Garden Level ${info.requiredLevel}.`
              );
            } else {
              await this.showCropUpgradeInsufficient(
                interaction,
                character,
                "Not enough Copper."
              );
            }
          }
          return;
        }
      }

      // Handle SkyMart buttons
      if (customId.startsWith("skymart_buy:")) {
        const buttonUserId = customId.split(":")[1];
        if (buttonUserId !== userId) {
          return interaction.reply({
            content: "You cannot use someone else's buttons.",
            ephemeral: true,
          });
        }

        // Get fresh data for purchase
        const allItems = await configManager.getAllItems();
        const skymartNPC = NPCS.SKYMART_MERCHANT;

        if (!skymartNPC) {
          return interaction.reply({
            content: "SkyMart is currently unavailable.",
            ephemeral: true,
          });
        }

        await this.handleSkymartPurchase(interaction, skymartNPC, allItems);
        return;
      }

      if (customId.startsWith("skymart_back:")) {
        const buttonUserId = customId.split(":")[1];
        if (buttonUserId !== userId) {
          return interaction.reply({
            content: "You cannot use someone else's buttons.",
            ephemeral: true,
          });
        }

        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062)
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          return;
        }
        const character = await getPlayerData(userId);
        if (!character) {
          return interaction.followUp({
            content: "Error fetching your character data.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        await this.showGardenDesk(interaction, character);
        return;
      }

      if (customId === "desk_back") {
        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062)
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          return;
        }

        const character = await getPlayerData(interaction.user.id);
        if (!character) {
          return interaction.followUp({
            content: "Error fetching your character data.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        // Check if still in Garden
        if (character.current_region !== "garden") {
          return interaction.followUp({
            content: "You must be in the Garden to use the Garden Desk!",
            flags: [MessageFlags.Ephemeral],
          });
        }

        await this.showGardenDesk(interaction, character);
        return;
      }

      if (customId === "coming_soon_back") {
        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062)
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          return;
        }

        const character = await getPlayerData(interaction.user.id);
        if (!character) {
          return interaction.followUp({
            content: "Error fetching your character data.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        await this.showGardenDesk(interaction, character);
        return;
      }

      if (customId === "skymart_error_back") {
        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062)
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          return;
        }

        const character = await getPlayerData(interaction.user.id);
        if (!character) {
          return interaction.followUp({
            content: "Error fetching your character data.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        // Check if still in Garden
        if (character.current_region !== "garden") {
          return interaction.followUp({
            content: "You must be in the Garden to use the Garden Desk!",
            flags: [MessageFlags.Ephemeral],
          });
        }

        await this.showGardenDesk(interaction, character);
        return;
      }

      if (customId === "visitor_npc_list") {
        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062)
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          return;
        }
        await this.showNPCList(interaction);
        return;
      }

      if (customId === "visitor_analytics_back") {
        try {
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
          }
        } catch (e) {
          if (e?.code !== 10062)
            console.warn("[Desk] deferUpdate failed:", e?.message || e);
          return;
        }

        const character = await getPlayerData(interaction.user.id);
        if (!character) {
          return interaction.followUp({
            content: "Error fetching your character data.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        // Check if still in Garden
        if (character.current_region !== "garden") {
          return interaction.followUp({
            content: "You must be in the Garden to use the Garden Desk!",
            flags: [MessageFlags.Ephemeral],
          });
        }

        await this.showVisitorAnalytics(interaction, character);
        return;
      }
    } catch (error) {
      console.error("[Desk] Error in handleButton:", error);
      try {
        await interaction.followUp({
          content: "An error occurred while processing your request.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch {}
    }
  },

  async showComingSoon(interaction, featureName, description) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ORANGE)
      .setTitle(`${featureName} - Coming Soon!`)
      .setDescription(
        `${description}\n\nThis feature is currently under development and will be available in a future update.`
      )
      .setFooter({ text: "updates go brrr" });

    const backButton = new ButtonBuilder()
      .setCustomId("coming_soon_back")
      .setLabel("← Back to Garden Desk")
      .setStyle(ButtonStyle.Secondary);

    const components = [new ActionRowBuilder().addComponents(backButton)];

    await interaction.editReply({ embeds: [embed], components });
  },

  async showSkymart(interaction) {
    try {
      const userId = interaction.user.id;

      // Get player data including copper
      const playerData = await getPlayerData(userId);
      const playerCopper = playerData?.copper || 0;

      // Get all items data
      const allItems = await configManager.getAllItems();

      // Get Skymart NPC
      const skymartNPC = NPCS.SKYMART_MERCHANT;
      if (!skymartNPC) {
        throw new Error("Skymart NPC not found");
      }

      // Generate shop display (pass copper instead of coins)
      const shopData = await generateShopDisplay(
        skymartNPC,
        allItems,
        playerCopper,
        false
      );
      const embed = shopData.embed;

      // Create buttons with persistent custom IDs
      const buyButton = new ButtonBuilder()
        .setCustomId(`skymart_buy:${userId}`)
        .setLabel("Buy Items")
        .setStyle(ButtonStyle.Success)
        .setEmoji("💰");

      const backButton = new ButtonBuilder()
        .setCustomId(`skymart_back:${userId}`)
        .setLabel("← Back to Garden Desk")
        .setStyle(ButtonStyle.Secondary);

      const components = [
        new ActionRowBuilder().addComponents(buyButton, backButton),
      ];

      await interaction.editReply({ embeds: [embed], components });
    } catch (error) {
      console.error("[Skymart] Error showing skymart:", error);
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("❌ Error")
        .setDescription(
          "There was an error loading SkyMart. Please try again later."
        );

      const backButton = new ButtonBuilder()
        .setCustomId("skymart_error_back")
        .setLabel("← Back to Garden Desk")
        .setStyle(ButtonStyle.Secondary);

      const components = [new ActionRowBuilder().addComponents(backButton)];

      await interaction.editReply({ embeds: [embed], components });
    }
  },

  async handleSkymartPurchase(interaction, npc, allItems) {
    try {
      const userId = interaction.user.id;

      const npcShopItems = npc.shopInventory || [];
      if (npcShopItems.length === 0) {
        return interaction.reply({
          content: "This merchant isn't selling anything right now.",
          ephemeral: true,
        });
      }

      // If only one item, skip dropdown and go straight to quantity
      if (npcShopItems.length === 1) {
        const item = npcShopItems[0];
        const itemDetails = allItems[item.itemKey];

        if (!item || !itemDetails) {
          return interaction.reply({
            content: "Item not available for purchase.",
            ephemeral: true,
          });
        }

        const {
          ModalBuilder,
          TextInputBuilder,
          TextInputStyle,
          ActionRowBuilder,
        } = require("discord.js");

        const modalCustomId = `skymart_single_modal:${userId}:${item.itemKey}`;
        const modal = new ModalBuilder()
          .setCustomId(modalCustomId)
          .setTitle(`Buy ${itemDetails.name}`)
          .addComponents(
            new ActionRowBuilder().addComponents(
              new TextInputBuilder()
                .setCustomId("quantity")
                .setLabel(
                  `How many? (${item.price.toLocaleString()} copper each)`
                )
                .setStyle(TextInputStyle.Short)
                .setRequired(true)
                .setPlaceholder('Enter quantity or "max"')
            )
          );

        await interaction.showModal(modal);
        return;
      }

      // Multiple items - show dropdown menu with persistent custom ID
      const { StringSelectMenuBuilder } = require("discord.js");

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`skymart_menu:${userId}`)
        .setPlaceholder("Select an item to buy")
        .addOptions(
          npcShopItems.map((itemInfo) => {
            const itemDetails = allItems[itemInfo.itemKey];
            let description = `Price: ${itemInfo.price?.toLocaleString() || "?"} copper`;

            if (
              itemDetails?.baseStats &&
              Object.keys(itemDetails.baseStats).length > 0
            ) {
              const statsText = Object.entries(itemDetails.baseStats)
                .map(([stat, value]) => {
                  const formattedStat = stat
                    .replace(/_/g, " ")
                    .toLowerCase()
                    .replace(/\b\w/g, (l) => l.toUpperCase());
                  return `+${value} ${formattedStat}`;
                })
                .join(", ");
              description += ` | ${statsText}`;
            }

            return {
              label: itemDetails?.name || itemInfo.itemKey,
              description: description,
              value: itemInfo.itemKey,
              emoji: itemDetails?.emoji || "❓",
            };
          })
        );

      await interaction.update({
        content: "Select the item you want to buy:",
        components: [new ActionRowBuilder().addComponents(selectMenu)],
        embeds: [],
      });
    } catch (error) {
      console.error("[Skymart Purchase] Error:", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "An error occurred while processing your purchase.",
          ephemeral: true,
        });
      }
    }
  },

  async processSkymartPurchase(interaction, item, itemDetails, userId) {
    try {
      const quantityInputStr = interaction.fields.getTextInputValue("quantity");

      // Get player data
      const character = await getPlayerData(userId);
      if (!character) {
        return interaction.editReply({
          content:
            "Could not load your character data to complete the purchase.",
        });
      }

      const playerCopper = character.copper || 0;
      const itemPrice = item.price;

      let quantityToBuy;

      if (quantityInputStr.toLowerCase() === "max") {
        if (itemPrice <= 0) {
          quantityToBuy = 1000;
        } else {
          quantityToBuy = Math.floor(playerCopper / itemPrice);
        }
      } else {
        quantityToBuy = parseInt(quantityInputStr, 10);
      }

      if (isNaN(quantityToBuy) || quantityToBuy <= 0) {
        return interaction.editReply({
          content: 'Invalid quantity. Please enter a positive number or "max".',
        });
      }

      const totalCost = quantityToBuy * itemPrice;

      if (totalCost > playerCopper) {
        const { COPPER } = require("../gameConfig");
        return interaction.editReply({
          content: `You don't have enough copper. You need ${totalCost.toLocaleString()} ${COPPER.name}, but you only have ${playerCopper.toLocaleString()}.`,
        });
      }

      // Process the purchase using copper
      const { dbRunQueued } = require("../utils/dbUtils");
      const { updateInventoryAtomically } = require("../utils/inventory");

      // Deduct copper first
      await dbRunQueued(
        "UPDATE players SET copper = copper - ? WHERE discord_id = ? AND copper >= ?",
        [totalCost, userId, totalCost]
      );

      // Determine how to add the purchased item(s)
      let itemsToChange = [];
      const equipmentToAdd = [];
      const accessoriesToAdd = [];

      if (
        itemDetails.unique === true &&
        ["ARMOR", "WEAPON", "TOOL", "EQUIPMENT"].includes(itemDetails.type) &&
        itemDetails.subtype
      ) {
        for (let i = 0; i < quantityToBuy; i++) {
          equipmentToAdd.push({ itemKey: item.itemKey });
        }
      } else if (itemDetails.type === "ACCESSORY") {
        for (let i = 0; i < quantityToBuy; i++) {
          accessoriesToAdd.push({ itemKey: item.itemKey });
        }
      } else {
        itemsToChange = [{ itemKey: item.itemKey, amount: quantityToBuy }];
      }

      // Add the item(s) to inventory appropriately
      await updateInventoryAtomically(
        userId,
        0, // No coin change
        itemsToChange, // Stackable items
        equipmentToAdd, // Equipment instances
        [], // No equipment to remove
        0, // No bank change
        [], // No equipment updates
        null, // No island changes
        null, // No collection changes
        false, // Not using existing transaction
        accessoriesToAdd, // Accessories to add
        [] // No accessories to remove
      );

      // Success message
      const { COPPER } = require("../gameConfig");
      const { EmbedBuilder } = require("discord.js");

      const successEmbed = new EmbedBuilder()
        .setColor(require("../gameConfig").EMBED_COLORS.GREEN)
        .setTitle("Item Purchased")
        .setDescription(
          `Successfully purchased ${quantityToBuy.toLocaleString()} ${itemDetails.emoji || ""} **${itemDetails.name}** for ${totalCost.toLocaleString()} ${COPPER.name}.`
        )
        .setFooter({
          text: "Copper has been deducted from your balance.",
        });

      await interaction.editReply({
        embeds: [successEmbed],
        components: [],
      });
    } catch (error) {
      console.error("[Skymart Process Purchase] Error:", error);
      await interaction.editReply({
        content: "An error occurred while processing your purchase.",
      });
    }
  },
};

// ================= Crop Upgrades UI =================
module.exports.showCropUpgradesOverview = async function (
  interaction,
  character,
  successMessage = null
) {
  try {
    const gardenLevel = getGardenLevel(character.garden_xp || 0);
    const unlockedCrops = getUnlockedCrops(gardenLevel);
    const allItems = configManager.getAllItems();

    const lines = [];
    for (const cropKey of unlockedCrops) {
      const item = allItems[cropKey];
      const emoji = item?.emoji || "❓";
      const name = item?.name || cropKey;
      const tier = getCropUpgradeTier(character, cropKey);
      const bonus = tier * CROP_UPGRADE_FORTUNE_PER_TIER;
      const next = getNextUpgradeInfo(character, cropKey, gardenLevel);
      if (!next) {
        lines.push(
          `${emoji} ${name}: Tier IX (+${CROP_UPGRADE_MAX_TIER * CROP_UPGRADE_FORTUNE_PER_TIER} FF) **MAXED**`
        );
      } else {
        const reqText = next.canUpgrade
          ? ""
          : ` (**Req. Garden Lv ${next.requiredLevel}**)`;
        lines.push(
          `${emoji} **${name}** Tier **${tier || "0"}** (**+${bonus} FF**) - Next: ${COPPER.emoji} ${next.cost}${reqText}`
        );
      }
    }

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PASTEL_GREEN)
      .setTitle("<:garden:1394656922623410237> Garden Desk: Crop Upgrades")
      .setDescription(
        `${successMessage ? `✅ ${successMessage}\n\n` : ""}` +
          `Upgrade crops to increase your Farming Fortune for that crop.\n` +
          `${COPPER.emoji} Your Copper: **${(character.copper || 0).toLocaleString()}**\n\n` +
          (lines.length > 0 ? lines.join("\n") : "No crops unlocked yet.")
      );

    // Build emoji buttons (max 5 per row)
    const rows = [];
    let currentRow = new ActionRowBuilder();
    let countInRow = 0;
    for (const cropKey of unlockedCrops) {
      const item = allItems[cropKey];
      const btn = new ButtonBuilder()
        .setCustomId(`crop_upgrades_select:${interaction.user.id}:${cropKey}`)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji(item?.emoji || "❓");
      currentRow.addComponents(btn);
      countInRow++;
      if (countInRow === 5) {
        rows.push(currentRow);
        currentRow = new ActionRowBuilder();
        countInRow = 0;
      }
    }
    if (countInRow > 0) rows.push(currentRow);

    const backRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("desk_back")
        .setLabel("← Back to Garden Desk")
        .setStyle(ButtonStyle.Secondary)
    );

    const components = rows.concat([backRow]);

    if (interaction.deferred || interaction.replied) {
      await interaction.editReply({ embeds: [embed], components });
    } else {
      await interaction.reply({ embeds: [embed], components });
    }
  } catch (error) {
    console.error("[Desk][CropUpgrades] Error showing overview:", error);
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ Error")
      .setDescription("Failed to load Crop Upgrades. Please try again.");
    const backButton = new ButtonBuilder()
      .setCustomId("desk_back")
      .setLabel("← Back to Garden Desk")
      .setStyle(ButtonStyle.Secondary);
    await interaction.editReply({
      embeds: [embed],
      components: [new ActionRowBuilder().addComponents(backButton)],
    });
  }
};

module.exports.showCropUpgradeConfirm = async function (
  interaction,
  character,
  cropKey
) {
  const gardenLevel = getGardenLevel(character.garden_xp || 0);
  const allItems = configManager.getAllItems();
  const item = allItems[cropKey];
  const name = item?.name || cropKey;
  const emoji = item?.emoji || "❓";

  const tier = getCropUpgradeTier(character, cropKey);
  const next = getNextUpgradeInfo(character, cropKey, gardenLevel);

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PASTEL_GREEN)
    .setTitle(`Crop Upgrade: ${emoji} ${name}`);

  if (!next) {
    embed.setDescription(
      `Tier IX (MAXED)\nTotal bonus: **+${CROP_UPGRADE_MAX_TIER * CROP_UPGRADE_FORTUNE_PER_TIER} Farming Fortune** (applies globally to ${name}).`
    );
    const back = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`crop_upgrades_back:${interaction.user.id}`)
        .setLabel("← Back")
        .setStyle(ButtonStyle.Secondary)
    );
    return interaction.editReply({ embeds: [embed], components: [back] });
  }

  const totalBonusNow = tier * CROP_UPGRADE_FORTUNE_PER_TIER;
  const totalBonusNext = (tier + 1) * CROP_UPGRADE_FORTUNE_PER_TIER;
  let desc =
    `Current Tier: **${tier || 0}** (+${totalBonusNow} FF)\n` +
    `Next Tier: **${tier + 1}** (+${totalBonusNext} FF)\n` +
    `Cost: ${COPPER.emoji} **${next.cost.toLocaleString()}** ${COPPER.name}\n`;

  if (!next.canUpgrade) {
    desc += `Required Garden Level: **${next.requiredLevel}**\n`;
  }

  desc += `\nThis bonus applies globally when harvesting ${name}.`;

  embed
    .setDescription(desc)
    .setFooter({ text: "Confirm to spend Copper. This cannot be undone." });

  const confirm1 = new ButtonBuilder()
    .setCustomId(`crop_upgrades_confirm1:${interaction.user.id}:${cropKey}`)
    .setLabel("Confirm (Buy 1)")
    .setStyle(ButtonStyle.Success)
    .setDisabled(!next.canUpgrade || (character.copper || 0) < next.cost);

  const confirmMax = new ButtonBuilder()
    .setCustomId(`crop_upgrades_confirmmax:${interaction.user.id}:${cropKey}`)
    .setLabel("Confirm Max")
    .setStyle(ButtonStyle.Primary)
    .setDisabled(!next.canUpgrade || (character.copper || 0) < next.cost);

  const backBtn = new ButtonBuilder()
    .setCustomId(`crop_upgrades_back:${interaction.user.id}`)
    .setLabel("← Back")
    .setStyle(ButtonStyle.Secondary);

  const row1 = new ActionRowBuilder().addComponents(
    confirm1,
    confirmMax,
    backBtn
  );
  await interaction.editReply({ embeds: [embed], components: [row1] });
};

module.exports.showCropUpgradeInsufficient = async function (
  interaction,
  character,
  messageText
) {
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ERROR)
    .setTitle("Purchase Unavailable")
    .setDescription(
      `${messageText}\n\n${COPPER.emoji} Your Copper: **${(character.copper || 0).toLocaleString()}**`
    )
    .setFooter({ text: "Complete Visitor offers to earn more Copper." });
  const back = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`crop_upgrades_back:${interaction.user.id}`)
      .setLabel("← Back")
      .setStyle(ButtonStyle.Secondary)
  );
  await interaction.editReply({ embeds: [embed], components: [back] });
};
