#Things to Ignore for Git

#Database
*.db
*.db-wal
*.db-shm

#Bot Start file
start_bot.bat
run-bot-loop.bat

#Bot Connection Configuration to Discord
.env

#Bot Connection Configuration on Dev Server
config.json
launch-all-bots.js

#Bot Connection Configuration on Live Server
live_config.json
live-launch-all-bots.js


#Node Stuff
node_modules/
        
#Others
.cursorrules
.cursor/
.windsurf/
.kiro/
.vercel/
.trae/
.vscode/
.idea/
.github/

# ESLint configuration (User preference)
eslint.config.mjs

# Custom Emojis (User preference / Generated?)
mc_emojis/

utils/recentAnnouncementActivity.json
data/time_state.json
sentry.js

# Test files
tests/
data/recentAnnouncementActivity.json
worker-resume-debug.log
disblock-pages
