// Utilities not needed - using custom promisified helpers instead

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) {
        console.error(
          `Database run error for query [${query}] with params [${params}]:`,
          err.message,
        );
        reject(err);
      } else {
        resolve({ lastID: this.lastID, changes: this.changes });
      }
    });
  });
}

// Helper function to check if a column exists
function columnExists(db, tableName, columnName) {
  return new Promise((resolve, reject) => {
    db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
      if (err) {
        console.error(
          `Error fetching table info for ${tableName}:`,
          err.message,
        );
        return reject(err);
      }
      resolve(columns.some((col) => col.name === columnName));
    });
  });
}

module.exports = {
  async up(db) {
    console.log(
      "Applying migration: Adding missing JSON and related columns to players table...",
    );
    const columnsToAdd = [
      { name: "collections_json", type: "TEXT" },
      { name: "pets_json", type: "TEXT" },
      { name: "active_pet_id", type: "TEXT" }, // Assuming TEXT for UUID
      { name: "island_json", type: "TEXT" },
      { name: "visiting_island_owner_id", type: "TEXT" }, // Assuming TEXT for Discord ID
      { name: "minion_storage_json", type: "TEXT" },
      { name: "crafted_minions_json", type: "TEXT" },
    ];

    try {
      for (const column of columnsToAdd) {
        const exists = await columnExists(db, "players", column.name);
        if (!exists) {
          console.log(`  -> Adding column '${column.name}'...`);
          await dbRunAsync(
            db,
            `ALTER TABLE players ADD COLUMN ${column.name} ${column.type}`,
          );
          console.log(`     Column '${column.name}' added.`);
        } else {
          console.log(`  -> Column '${column.name}' already exists. Skipping.`);
        }
      }
      console.log("Missing player columns migration applied successfully.");
    } catch (err) {
      console.error("Migration failed:", err);
      throw err; // Re-throw error to halt migration process
    }
  },
};
