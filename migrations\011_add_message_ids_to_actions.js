// Utilities not needed - using custom promisified helpers instead

// Helper to promisify db.run
const run = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        console.error("Error running sql " + sql);
        console.error(err);
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

// Helper to promisify db.all
const all = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        console.error("Error running sql " + sql);
        console.error(err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// Migration UP
async function up(db) {
  console.log("Applying migration: 011_add_message_ids_to_actions...");
  const tableName = "active_actions";

  // Get table info
  const columns = await all(db, `PRAGMA table_info(${tableName});`);
  const columnNames = columns.map((col) => col.name);

  // Add channel_id column if it doesn't exist
  if (!columnNames.includes("channel_id")) {
    await run(db, `ALTER TABLE ${tableName} ADD COLUMN channel_id TEXT;`);
    console.log(`  -> Added channel_id column to ${tableName}.`);
  } else {
    console.log(
      `  -> Column channel_id already exists in ${tableName}. Skipping add.`,
    );
  }

  // Add message_id column if it doesn't exist
  if (!columnNames.includes("message_id")) {
    await run(db, `ALTER TABLE ${tableName} ADD COLUMN message_id TEXT;`);
    console.log(`  -> Added message_id column to ${tableName}.`);
  } else {
    console.log(
      `  -> Column message_id already exists in ${tableName}. Skipping add.`,
    );
  }

  console.log("Migration 011 checks/updates complete.");
}

module.exports = { up /*, down */ };
