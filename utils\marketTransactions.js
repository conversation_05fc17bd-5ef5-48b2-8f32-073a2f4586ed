const { dbRunQueued, dbGet } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");
const { updateInventoryAtomically } = require("./inventory");
const { sendMarketOrderNotification } = require("./marketNotifications");

/**
 * Executes a market transaction atomically to prevent duplication glitches
 * This function ensures all operations succeed or fail together (yahoo!)
 */
async function executeMarketTransaction(transactionData, client = null) {
  const {
    buyer,
    seller,
    itemKey,
    quantity,
    pricePerUnit,
    sellOrderId,
    buyOrderId,
    transactionType, // 'instant_buy', 'instant_sell', 'order_fill'
  } = transactionData;

  try {
    // Step 1: Validate current state and lock data
    const [buyerData, sellerData] = await Promise.all([
      getPlayerData(buyer.id || buyer),
      getPlayerData(seller.id || seller),
    ]);

    if (!buyerData || !sellerData) {
      throw new Error("Could not load player data for transaction");
    }

    const totalCost = quantity * pricePerUnit;

    // Step 2: Pre-validation checks
    await validateMarketTransaction({
      buyerData,
      sellerData,
      itemKey,
      quantity,
      totalCost,
      sellOrderId,
      buyOrderId,
    });

    // Step 3: Execute atomic transaction
    const result = await executeAtomicMarketTransfer({
      buyerData,
      sellerData,
      itemKey,
      quantity,
      totalCost,
      sellOrderId,
      buyOrderId,
      transactionType,
      client,
    });

    return {
      success: true,
      result,
    };
  } catch (error) {
    console.error("[Market Transaction] Error:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Validates that a market transaction can proceed
 */
async function validateMarketTransaction({
  buyerData,
  sellerData,
  _itemKey,
  quantity,
  totalCost,
  sellOrderId,
  buyOrderId,
}) {
  // prevent self-trading (additional safety check)
  if (buyerData.discordId === sellerData.discordId) {
    throw new Error("Cannot trade with yourself");
  }

  // Check buyer has enough coins (for instant buy transactions)
  // Note: For instant sell, buyer's coins were already locked when they created their buy order
  if (!buyOrderId && (buyerData.coins || 0) < totalCost) {
    throw new Error(
      `Buyer doesn't have enough coins (needs ${totalCost}, has ${buyerData.coins || 0})`
    );
  }

  // Note: We don't check seller inventory because items are in escrow (removed when order was created)

  // Validate orders still exist and have enough quantity (double-check)
  if (sellOrderId) {
    const sellOrder = await dbGet(
      "SELECT * FROM market_sell_orders WHERE id = ? AND quantity >= ?",
      [sellOrderId, quantity]
    );
    if (!sellOrder) {
      throw new Error(
        "Sell order no longer available or insufficient quantity"
      );
    }

    // additional validation: ensure seller owns the order
    if (sellOrder.seller_id !== sellerData.discordId) {
      throw new Error("Seller does not own this sell order");
    }
  }

  if (buyOrderId) {
    const buyOrder = await dbGet(
      "SELECT * FROM market_buy_orders WHERE id = ? AND quantity >= ?",
      [buyOrderId, quantity]
    );
    if (!buyOrder) {
      throw new Error("Buy order no longer available or insufficient quantity");
    }

    // additional validation: ensure buyer owns the order
    if (buyOrder.buyer_id !== buyerData.discordId) {
      throw new Error("Buyer does not own this buy order");
    }
  }
}

/**
 * Executes the atomic transfer using the write queue transaction system
 */
async function executeAtomicMarketTransfer({
  buyerData,
  sellerData,
  itemKey,
  quantity,
  totalCost,
  sellOrderId,
  buyOrderId,
  _transactionType,
  client,
}) {
  const operations = [];
  let newBuyerCoins, newSellerCoins;

  // Handle coin transfers based on transaction type
  if (!buyOrderId) {
    // Instant buy: buyer pays directly, seller receives payment
    newBuyerCoins = (buyerData.coins || 0) - totalCost;
    newSellerCoins = (sellerData.coins || 0) + totalCost;

    operations.push({
      sql: "UPDATE players SET coins = ? WHERE discord_id = ?",
      params: [newBuyerCoins, buyerData.discordId],
    });

    operations.push({
      sql: "UPDATE players SET coins = ? WHERE discord_id = ?",
      params: [newSellerCoins, sellerData.discordId],
    });
  } else {
    // Instant sell: coins come from buy order's locked funds, only seller receives coins
    newBuyerCoins = buyerData.coins || 0; // no change
    newSellerCoins = (sellerData.coins || 0) + totalCost;

    operations.push({
      sql: "UPDATE players SET coins = ? WHERE discord_id = ?",
      params: [newSellerCoins, sellerData.discordId],
    });

    // Note: buyer's coins don't change (they were already locked in the buy order)
  }

  // Prepare inventory transfers using proper inventory system

  // always add items to buyer (items come from escrow, not seller inventory)
  await updateInventoryAtomically(
    buyerData.discordId,
    0, // coins handled separately above
    [{ itemKey, amount: quantity }], // add items to buyer
    [], // no equipment to add
    [], // no equipment to remove
    0 // no bank coins
  );

  // Note: We never remove items from seller inventory here because:
  // - For sell orders: items were already removed when the order was created (escrow)
  // - For buy orders: buyer gets items, seller gets coins (no inventory change for seller)

  // Update or remove sell order
  let sellOrderCompleted = false;
  let sellOrderPricePerUnit = 0;
  let sellOrderOriginalQuantity = 0;
  let newSellQuantity = 0;
  if (sellOrderId) {
    const sellOrder = await dbGet(
      "SELECT quantity, price_per_unit, original_quantity FROM market_sell_orders WHERE id = ?",
      [sellOrderId]
    );
    sellOrderPricePerUnit = sellOrder.price_per_unit;
    sellOrderOriginalQuantity =
      sellOrder.original_quantity || sellOrder.quantity; // fallback for old orders
    newSellQuantity = sellOrder.quantity - quantity;

    if (newSellQuantity > 0) {
      operations.push({
        sql: "UPDATE market_sell_orders SET quantity = ? WHERE id = ?",
        params: [newSellQuantity, sellOrderId],
      });
    } else {
      sellOrderCompleted = true;
      operations.push({
        sql: "DELETE FROM market_sell_orders WHERE id = ?",
        params: [sellOrderId],
      });
    }
  }

  // Update or remove buy order
  let buyOrderCompleted = false;
  let buyOrderPricePerUnit = 0;
  let buyOrderOriginalQuantity = 0;
  let newBuyQuantity = 0;
  if (buyOrderId) {
    const buyOrder = await dbGet(
      "SELECT quantity, price_per_unit, total_coins_locked, original_quantity FROM market_buy_orders WHERE id = ?",
      [buyOrderId]
    );
    buyOrderPricePerUnit = buyOrder.price_per_unit;
    buyOrderOriginalQuantity = buyOrder.original_quantity || buyOrder.quantity; // fallback for old orders
    newBuyQuantity = buyOrder.quantity - quantity;

    if (newBuyQuantity > 0) {
      // calculate new locked coins proportionally
      const newLockedCoins = newBuyQuantity * buyOrder.price_per_unit;
      operations.push({
        sql: "UPDATE market_buy_orders SET quantity = ?, total_coins_locked = ? WHERE id = ?",
        params: [newBuyQuantity, newLockedCoins, buyOrderId],
      });
    } else {
      buyOrderCompleted = true;
      operations.push({
        sql: "DELETE FROM market_buy_orders WHERE id = ?",
        params: [buyOrderId],
      });
    }
  }

  // Execute all operations atomically using the existing write queue system
  await Promise.all(operations.map((op) => dbRunQueued(op.sql, op.params)));

  // Note: We don't call savePlayerData here because we've already updated coins directly in the database
  // and calling savePlayerData would overwrite those changes with potentially stale in-memory data.
  // The inventory updates for the buyer are handled separately via updateInventoryAtomically above.

  // Send notifications for completed orders
  if (client) {
    try {
      // Notify seller if their sell order was completed
      if (sellOrderCompleted && sellOrderId) {
        await sendMarketOrderNotification(
          client,
          sellerData.discordId,
          "sell",
          itemKey,
          quantity,
          sellOrderPricePerUnit,
          true, // fully filled
          sellOrderOriginalQuantity,
          0 // remaining quantity for completed order
        );
      }

      // Notify buyer if their buy order was completed
      if (buyOrderCompleted && buyOrderId) {
        await sendMarketOrderNotification(
          client,
          buyerData.discordId,
          "buy",
          itemKey,
          quantity,
          buyOrderPricePerUnit,
          true, // fully filled
          buyOrderOriginalQuantity,
          0 // remaining quantity for completed order
        );
      }

      // Also notify for partial fills if either user has "all" notification mode
      if (sellOrderId && !sellOrderCompleted) {
        // check if seller wants all notifications
        const sellerSettings = await getPlayerData(sellerData.discordId);
        if (sellerSettings?.settings?.marketNotificationMode === "all") {
          await sendMarketOrderNotification(
            client,
            sellerData.discordId,
            "sell",
            itemKey,
            quantity,
            sellOrderPricePerUnit,
            false, // partially filled
            sellOrderOriginalQuantity,
            newSellQuantity // remaining quantity
          );
        }
      }

      if (buyOrderId && !buyOrderCompleted) {
        // check if buyer wants all notifications
        const buyerSettings = await getPlayerData(buyerData.discordId);
        if (buyerSettings?.settings?.marketNotificationMode === "all") {
          await sendMarketOrderNotification(
            client,
            buyerData.discordId,
            "buy",
            itemKey,
            quantity,
            buyOrderPricePerUnit,
            false, // partially filled
            buyOrderOriginalQuantity,
            newBuyQuantity // remaining quantity
          );
        }
      }
    } catch (notificationError) {
      console.error(
        "[Market Notifications] Error sending order completion notifications:",
        notificationError
      );
    }
  }

  return {
    buyerCoins: newBuyerCoins,
    sellerCoins: newSellerCoins,
    itemsTransferred: quantity,
    totalCost,
  };
}

/**
 * Helper function for instant buy transactions
 */
async function executeInstantBuyTransaction(
  buyerId,
  sellerId,
  itemKey,
  quantity,
  totalCost,
  sellOrderId,
  client = null
) {
  return executeMarketTransaction(
    {
      buyer: { id: buyerId },
      seller: { id: sellerId },
      itemKey,
      quantity,
      pricePerUnit: Math.round(totalCost / quantity),
      sellOrderId,
      transactionType: "instant_buy",
    },
    client
  );
}

/**
 * Helper function for instant sell transactions
 */
async function executeInstantSellTransaction(
  sellerId,
  buyerId,
  itemKey,
  quantity,
  totalCost,
  buyOrderId,
  client = null
) {
  return executeMarketTransaction(
    {
      buyer: { id: buyerId },
      seller: { id: sellerId },
      itemKey,
      quantity,
      pricePerUnit: Math.round(totalCost / quantity),
      buyOrderId,
      transactionType: "instant_sell",
    },
    client
  );
}

/**
 * Creates a market lock to prevent concurrent access to the same trade
 * This is a simple in-memory lock that prevents race conditions
 */
const activeTrades = new Map();
const confirmationTokens = new Map();
const userActionTimestamps = new Map();

async function withMarketLock(lockKey, operation) {
  if (activeTrades.has(lockKey)) {
    throw new Error("Trade already in progress for this combination");
  }

  activeTrades.set(lockKey, Date.now());

  try {
    const result = await operation();
    return result;
  } finally {
    activeTrades.delete(lockKey);
  }
}

/**
 * Creates a unique confirmation token to prevent double-confirmations
 */
function createConfirmationToken(userId, action, params) {
  const token = `${userId}_${action}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  confirmationTokens.set(token, {
    userId,
    action,
    params,
    created: Date.now(),
  });

  // clean up old tokens (older than 10 minutes)
  const cutoff = Date.now() - 600000;
  for (const [key, value] of confirmationTokens.entries()) {
    if (value.created < cutoff) {
      confirmationTokens.delete(key);
    }
  }

  return token;
}

/**
 * Validates and consumes a confirmation token (one-time use)
 */
function validateAndConsumeToken(token) {
  if (!confirmationTokens.has(token)) {
    throw new Error("Invalid or expired confirmation token");
  }

  const data = confirmationTokens.get(token);
  confirmationTokens.delete(token); // one-time use

  // check if token is too old (10 minutes)
  if (Date.now() - data.created > 600000) {
    throw new Error("Confirmation token has expired");
  }

  return data;
}

/**
 * Checks rate limiting for user actions
 */
function checkRateLimit(userId, action, cooldownMs = 1000) {
  const key = `${userId}_${action}`;
  const now = Date.now();
  const lastAction = userActionTimestamps.get(key) || 0;

  if (now - lastAction < cooldownMs) {
    throw new Error("Please wait before performing this action again");
  }

  userActionTimestamps.set(key, now);
}

/**
 * Creates a user-item specific lock key to prevent concurrent transactions
 */
function createUserItemLock(userId, action, itemKey) {
  return `${userId}_${action}_${itemKey}`;
}

/**
 * Generates a unique lock key for a trade combination
 */
function generateTradeLockKey(buyerId, sellerId, itemKey) {
  const sorted = [buyerId, sellerId].sort();
  return `${sorted[0]}_${sorted[1]}_${itemKey}`;
}

module.exports = {
  executeMarketTransaction,
  executeInstantBuyTransaction,
  executeInstantSellTransaction,
  withMarketLock,
  generateTradeLockKey,
  createConfirmationToken,
  validateAndConsumeToken,
  checkRateLimit,
  createUserItemLock,
};
