const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  Embed<PERSON>uilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  MessageFlags,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  ComponentType,
  StringSelectMenuBuilder,
  StringSelectMenuOptionBuilder,
} = require("discord.js");
const configManager = require("../utils/configManager.js");
const { findCollectionForItem } = require("../utils/collectionUtils");
const { getMobAbilityDescriptions } = require("../utils/mobAbilities.js");

const NPCS = require("../data/npcs.js");
const {
  STATS,
  PET_RARITIES,
  ITEM_RARITY,
  skillEmojis,
  EMBED_COLORS,
} = require("../gameConfig.js");
const { getRarityColor, getItemColor } = require("../utils/rarityUtils");
const { formatRequirements } = require("./recipe.js");
const { REFORGE_DATA, REFORGE_COSTS } = require("../data/reforges.js");
const { STAT_ABBREVIATIONS } = require("../utils/statAbbreviations");
const { SET_BONUSES } = require("../utils/setBonuses.js");
const { getPlayerSkillLevel } = require("../utils/playerDataManager.js");

function formatPercentage(decimal) {
  const percent = decimal * 100;

  // For very small percentages (< 0.1%), show up to 4 decimal places
  if (percent < 0.1) {
    return percent.toFixed(4).replace(/\.?0+$/, "");
  }
  // For small percentages (< 1%), show up to 3 decimal places
  else if (percent < 1) {
    return percent.toFixed(3).replace(/\.?0+$/, "");
  }
  // For percentages < 10%, show up to 2 decimal places
  else if (percent < 10) {
    return percent.toFixed(2).replace(/\.?0+$/, "");
  }
  // For larger percentages, show up to 1 decimal place
  else {
    return percent.toFixed(1).replace(/\.?0+$/, "");
  }
}

function formatStats(stats) {
  if (!stats || typeof stats !== "object") return "`None`";
  let output = "";
  for (const [key, value] of Object.entries(stats)) {
    if (typeof value === "number" && value !== 0) {
      const cleanKey = key.trim().toUpperCase();
      const statDef = STATS[cleanKey];
      const emoji = statDef?.emoji || "";
      const displayName =
        statDef?.name ||
        cleanKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
      output += `${emoji ? emoji + " " : ""}**${displayName}:** ${value}\n`;
    }
  }
  return output.trim() || "`None`";
}

function formatIngredients(ingredients, allItems) {
  if (
    !ingredients ||
    (typeof ingredients !== "object" && !Array.isArray(ingredients))
  ) {
    console.error(
      "[Wiki formatIngredients] Expected ingredients to be an object or array, but got:",
      ingredients
    );
    return "`None`";
  }

  let output = "";
  let processedIngredients = 0;

  if (Array.isArray(ingredients)) {
    if (ingredients.length === 0) {
      return "`None`";
    }
    for (const ingredientObj of ingredients) {
      if (
        typeof ingredientObj !== "object" ||
        ingredientObj === null ||
        !Object.prototype.hasOwnProperty.call(ingredientObj, "itemKey") ||
        !Object.prototype.hasOwnProperty.call(ingredientObj, "amount")
      ) {
        console.warn(
          "[Wiki formatIngredients] Skipping invalid ingredient in array:",
          ingredientObj
        );
        continue;
      }
      const itemKey = ingredientObj.itemKey;
      const amount = ingredientObj.amount;

      if (typeof amount !== "number") {
        console.warn(
          `[Wiki formatIngredients] Skipping ingredient with non-number amount in array format: ${itemKey}: ${amount}`
        );
        continue;
      }
      const itemData = allItems[itemKey];
      output += `${itemData?.emoji || "❓"} \`${amount.toLocaleString()}x ${
        itemData?.name || itemKey
      }\`\n`;
      processedIngredients++;
    }
  } else {
    const ingredientEntries = Object.entries(ingredients);
    if (ingredientEntries.length === 0) {
      return "`None`";
    }
    for (const [itemKey, amount] of ingredientEntries) {
      if (typeof amount !== "number") {
        console.warn(
          `[Wiki formatIngredients] Skipping ingredient with non-number amount in object format: ${itemKey}: ${amount}`
        );
        continue;
      }
      const itemData = allItems[itemKey];
      output += `${itemData?.emoji || "❓"} \`${amount.toLocaleString()}x ${
        itemData?.name || itemKey
      }\`\n`;
      processedIngredients++;
    }
  }

  return processedIngredients > 0 ? output.trim() : "`None`";
}

function formatMobDrops(itemKey) {
  const mobList = Object.values(configManager.getAllMobs()).filter((mob) => {
    const hasRegularDrop =
      Array.isArray(mob.loot?.drops) &&
      mob.loot.drops.some((drop) => drop.itemKey === itemKey);

    const hasDropGroupDrop =
      Array.isArray(mob.loot?.dropGroups) &&
      mob.loot.dropGroups.some(
        (group) =>
          Array.isArray(group.items) &&
          group.items.some((item) => item.itemKey === itemKey)
      );

    // Check for lootTables system (used by slayer bosses)
    const hasLootTableDrop =
      mob.loot?.lootTables &&
      Object.values(mob.loot.lootTables).some(
        (table) =>
          Array.isArray(table.items) &&
          table.items.some((item) => item.itemKey === itemKey)
      );

    return hasRegularDrop || hasDropGroupDrop || hasLootTableDrop;
  });
  if (mobList.length === 0) return null;
  let output = "";
  for (const mob of mobList) {
    const drops =
      mob.loot.drops?.filter((drop) => drop.itemKey === itemKey) || [];
    if (drops.length > 0) {
      // If there are multiple drops of the same item, combine them
      if (drops.length === 1) {
        const drop = drops[0];
        let amountStr = "";
        const isGuaranteed = (drop.chance || 1) >= 1;

        if (
          typeof drop.amount === "object" &&
          drop.amount.min !== undefined &&
          drop.amount.max !== undefined
        ) {
          if (isGuaranteed) {
            amountStr = `${
              drop.amount.min === drop.amount.max
                ? drop.amount.min
                : `${drop.amount.min}-${drop.amount.max}`
            }x`;
          } else {
            const chancePercent = formatPercentage(drop.chance || 0);
            amountStr = `0-${drop.amount.max}x **(${chancePercent}%)**`;
          }
        } else {
          if (isGuaranteed) {
            amountStr = `${drop.amount}x`;
          } else {
            const chancePercent = formatPercentage(drop.chance || 0);
            amountStr = `0-${drop.amount}x **(${chancePercent}%)**`;
          }
        }

        output += `${mob.emoji || "❓"} **${mob.name}** : ${amountStr}\n`;
      } else {
        // Multiple drops of the same item - calculate total range
        let guaranteedMin = 0;
        let totalMax = 0;

        for (const drop of drops) {
          let dropMin, dropMax;
          if (
            typeof drop.amount === "object" &&
            drop.amount.min !== undefined &&
            drop.amount.max !== undefined
          ) {
            dropMin = drop.amount.min;
            dropMax = drop.amount.max;
          } else {
            dropMin = dropMax = drop.amount;
          }

          // Only add to guaranteed minimum if chance is 100%
          if ((drop.chance || 1) >= 1) {
            guaranteedMin += dropMin;
          }

          // Add to maximum possible (assuming all drops occur)
          totalMax += dropMax;
        }

        const amountStr =
          guaranteedMin === totalMax
            ? `${guaranteedMin}x`
            : `${guaranteedMin}-${totalMax}x`;

        output += `${mob.emoji || "❓"} **${mob.name}** : ${amountStr}\n`;
      }
    }

    if (Array.isArray(mob.loot?.dropGroups)) {
      for (const group of mob.loot.dropGroups) {
        if (Array.isArray(group.items)) {
          const groupItem = group.items.find(
            (item) => item.itemKey === itemKey
          );
          if (groupItem) {
            // The group chance is the chance that the group drops, not the individual item chance
            // When the group drops, one item is selected based on weight distribution
            // So the individual item chance should be the group chance, not group chance * weight ratio
            const individualChance = group.chance || 0;
            const isGuaranteed = individualChance >= 1;

            let amountStr;
            if (isGuaranteed) {
              amountStr = `${groupItem.amount || "1"}x`;
            } else {
              const chancePercent = formatPercentage(individualChance);
              amountStr = `0-${groupItem.amount || "1"}x **(${chancePercent}%)**`;
            }

            output += `${mob.emoji || "❓"} **${mob.name}** : ${amountStr}\n`;
          }
        }
      }
    }

    // Handle lootTables system (used by slayer bosses)
    if (mob.loot?.lootTables) {
      for (const [, table] of Object.entries(mob.loot.lootTables)) {
        if (Array.isArray(table.items)) {
          const tableItem = table.items.find(
            (item) => item.itemKey === itemKey
          );
          if (tableItem) {
            let amountStr = "";
            const isGuaranteed = (tableItem.chance || 1) >= 1;

            if (
              typeof tableItem.amount === "object" &&
              tableItem.amount.min !== undefined &&
              tableItem.amount.max !== undefined
            ) {
              if (isGuaranteed) {
                amountStr = `${
                  tableItem.amount.min === tableItem.amount.max
                    ? tableItem.amount.min
                    : `${tableItem.amount.min}-${tableItem.amount.max}`
                }x`;
              } else {
                const chancePercent = formatPercentage(tableItem.chance || 0);
                amountStr = `0-${tableItem.amount.max}x **(${chancePercent}%)**`;
              }
            } else {
              if (isGuaranteed) {
                amountStr = `${tableItem.amount || 1}x`;
              } else {
                const chancePercent = formatPercentage(tableItem.chance || 0);
                amountStr = `0-${tableItem.amount || 1}x **(${chancePercent}%)**`;
              }
            }

            output += `${mob.emoji || "❓"} **${mob.name}** : ${amountStr}\n`;
          }
        }
      }
    }
  }
  return output.trim();
}

function formatBuyableFromNPCs(itemKey) {
  const npcs = Object.values(NPCS);
  const sellers = [];
  const itemKeyUpper = itemKey.toUpperCase();
  for (const npc of npcs) {
    if (Array.isArray(npc.shopInventory)) {
      const shopItem = npc.shopInventory.find(
        (entry) => (entry.itemKey || "").toUpperCase() === itemKeyUpper
      );
      if (shopItem) {
        sellers.push({
          npcName: npc.name,
          npcEmoji: npc.emoji || "❓",
          price: shopItem.price,
          currency: npc.currency || "coins",
        });
      }
    }
  }
  if (sellers.length === 0) return null;
  return sellers
    .map((s) => {
      if (s.currency === "copper") {
        return `${s.npcEmoji} **${s.npcName}**: ${s.price} copper`;
      }
      return `${s.npcEmoji} **${s.npcName}**: ${s.price} coins`;
    })
    .join("\n");
}

function formatNPCSellPrice(itemData) {
  if (
    !itemData ||
    !itemData.sellable ||
    !itemData.sellPrice ||
    itemData.sellPrice <= 0
  ) {
    return null;
  }
  return `<:purse_coins:1367849116033482772> **${itemData.sellPrice} coins** each`;
}

function formatSetBonusInfo(itemKey) {
  // Use the actual SET_BONUSES data from setBonuses.js
  let belongsToSet = null;
  for (const setData of SET_BONUSES) {
    if (setData.itemKeys.includes(itemKey)) {
      belongsToSet = setData;
      break;
    }
  }

  if (!belongsToSet) return null;

  // Fermento: show tiered bonuses like Squash but keep drops compact to fit under 1024 chars
  if (belongsToSet.key === "fermento") {
    let out = "";
    belongsToSet.thresholds.forEach((threshold) => {
      const b = threshold.bonus || {};
      const piecesText =
        typeof threshold.pieces === "string"
          ? threshold.pieces
          : `${threshold.pieces} pieces`;
      out += `**${piecesText}:**\n`;
      if (b.farmingFortune) {
        out += `<:foraging_fortune:1270616477824712714> **+${b.farmingFortune} Farming Fortune**\n`;
      }
      if (b.farmingSweep) {
        out += `<:farming_sweep:1385551740438450237> **+${b.farmingSweep} Farming Sweep**\n`;
      }
      // Compact drops line (icons + percents only)
      const parts = [];
      if (b.cropieDropChance)
        parts.push(
          `<:cropie:1386214845497479310> ${(b.cropieDropChance * 100).toFixed(2)}%`
        );
      if (b.squashDropChance)
        parts.push(
          `<:squash:1394767429187407952> ${(b.squashDropChance * 100).toFixed(2)}%`
        );
      if (b.fermentoDropChance)
        parts.push(
          `<:Fermento:1398921787617574992> ${(b.fermentoDropChance * 100).toFixed(3)}%`
        );
      if (parts.length) out += parts.join(" • ") + "\n";
    });
    return out.trim();
  }

  let output = "";

  belongsToSet.thresholds.forEach((threshold) => {
    const pieces = threshold.pieces;
    const bonus = threshold.bonus;

    const piecesText = typeof pieces === "string" ? pieces : `${pieces} pieces`;
    output += `**${piecesText}:**\n`;
    const bonusEffects = [];

    if (bonus.damageReduction) {
      bonusEffects.push(
        `${(bonus.damageReduction * 100).toFixed(0)}% Damage reduction from Sea Creatures`
      );
    }
    if (bonus.healthPerLevel) {
      bonusEffects.push(
        `<:health:1269719825429565532> +${bonus.healthPerLevel} Health per Fishing Level`
      );
    }
    if (bonus.health) {
      bonusEffects.push(
        `<:health:1269719825429565532>  **+${bonus.health} Health**`
      );
    }
    if (bonus.defense) {
      bonusEffects.push(
        `<:defense:1269719821399097456> **+${bonus.defense} Defense**`
      );
    }
    if (bonus.farmingFortune) {
      bonusEffects.push(
        `<:foraging_fortune:1270616477824712714> **+${bonus.farmingFortune} Farming Fortune**`
      );
    }
    if (bonus.farmingSweep) {
      bonusEffects.push(
        `<:farming_sweep:1385551740438450237> **+${bonus.farmingSweep} Farming Sweep**`
      );
    }
    if (bonus.miningSweep) {
      bonusEffects.push(
        `<:mining_sweep:1385551740438450237> **+${bonus.miningSweep} Mining Sweep**`
      );
    }
    if (bonus.cropieDropChance) {
      bonusEffects.push(
        `<:cropie:1386214845497479310> **${(bonus.cropieDropChance * 100).toFixed(2)}% chance to drop Cropie** when farming Wheat, Carrot, or Potato`
      );
    }
    if (bonus.squashDropChance) {
      bonusEffects.push(
        `<:squash:1394767429187407952> **${(bonus.squashDropChance * 100).toFixed(2)}% chance to drop Squash** when farming Pumpkin, Melon, or Cocoa Beans`
      );
    }
    if (bonus.fermentoDropChance) {
      bonusEffects.push(
        `<:Fermento:1398921787617574992> **${(bonus.fermentoDropChance * 100).toFixed(3)}% chance to drop Fermento** when farming Sugar Cane, Cactus, Mushrooms, or Nether Wart`
      );
    }
    if (bonus.collectionBased) {
      bonusEffects.push(
        `+1 Defense per ${bonus.perAmount.toLocaleString()} ${bonus.collectionKey} Collection (max +${bonus.maxBonus})`
      );
    }
    if (bonus.enderBonus) {
      bonusEffects.push(
        "**All stats from Ender Armor pieces are doubled when in The End**"
      );
    }
    if (bonus.octodexterity) {
      bonusEffects.push("**Double damage every 4th turn in combat**");
    }

    output += bonusEffects.join("\n") + "\n";
  });

  return output.trim();
}

async function buildPetEmbedAndComponents(
  itemData,
  itemKey,
  targetRarityKey,
  targetLevel,
  allItems
) {
  const embed = new EmbedBuilder();
  let components = [];
  const rarityKeys = PET_RARITIES;
  const currentRarityIndex = rarityKeys.indexOf(targetRarityKey);
  const rarityData = itemData.rarities[targetRarityKey];

  if (!rarityData) {
    embed.setTitle("Error").setDescription("Pet rarity data not found.");
    return { embed, components };
  }

  targetLevel = Math.min(Math.max(1, parseInt(targetLevel) || 1), 100);

  embed
    .setColor(getRarityColor(targetRarityKey))
    .setTitle(
      `${itemData.emoji || "❓"} ${itemData.name} : ${
        targetRarityKey.charAt(0) + targetRarityKey.slice(1).toLowerCase()
      } (Lvl ${targetLevel})`
    );

  if (
    typeof itemData.description === "string" &&
    itemData.description.trim().length > 0
  ) {
    embed.setDescription(itemData.description);
  }

  let statsAtLevelString = "";
  if (rarityData.stats) {
    for (const [statKey, statObj] of Object.entries(rarityData.stats)) {
      const statDef = STATS[statKey.toUpperCase()];
      const emoji = statDef?.emoji || "";
      const displayName =
        statDef?.name ||
        statKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
      const valueAtLevel =
        statObj.base + (statObj.perLevel || 0) * (targetLevel - 1);
      statsAtLevelString += `${
        emoji ? emoji + " " : ""
      }**${displayName}:** ${valueAtLevel.toFixed(statObj.perLevel ? 2 : 0)}\n`;
    }
  }
  if (statsAtLevelString)
    embed.addFields({
      name: `Stats at Lvl ${targetLevel}`,
      value: statsAtLevelString.trim(),
      inline: false,
    });

  let abilitiesString = "";

  if (rarityData.abilities && typeof rarityData.abilities === "object") {
    Object.values(rarityData.abilities).forEach((ability) => {
      if (ability && ability.name && ability.description) {
        let abilityText = `**${ability.name}:** ${ability.description}`;

        // Add specific ability details for better information
        if (
          ability.baseChance !== undefined ||
          ability.perLevel !== undefined
        ) {
          const currentChance =
            (ability.baseChance || 0) + (ability.perLevel || 0) * targetLevel;
          const maxChance = ability.maxChance || 1;
          const displayChance = Math.min(currentChance, maxChance);
          abilityText += `\n*Current: ${(displayChance * 100).toFixed(2)}% chance*`;

          if (ability.maxChance && currentChance < ability.maxChance) {
            abilityText += ` *(Max: ${(ability.maxChance * 100).toFixed(2)}%)*`;
          }
        }

        if (
          ability.baseSpeedBonus !== undefined ||
          ability.perLevel !== undefined
        ) {
          const currentBonus =
            (ability.baseSpeedBonus || 0) +
            (ability.perLevel || 0) * targetLevel;
          const maxBonus = ability.maxSpeedBonus || currentBonus;
          const displayBonus = Math.min(currentBonus, maxBonus);
          abilityText += `\n*Current: +${(displayBonus * 100).toFixed(1)}% speed boost*`;

          if (ability.maxSpeedBonus && currentBonus < ability.maxSpeedBonus) {
            abilityText += ` *(Max: +${(ability.maxSpeedBonus * 100).toFixed(1)}%)*`;
          }
        }

        abilitiesString += abilityText + "\n\n";
      }
    });
  }

  if (abilitiesString)
    embed.addFields({
      name: "Abilities",
      value: abilitiesString.trim(),
      inline: false,
    });

  const allEggs = Object.values(allItems).filter(
    (e) =>
      e.resultPetKey === itemKey &&
      e.recipes &&
      Array.isArray(e.recipes) &&
      e.recipes.length > 0
  );

  const eggs = allEggs.filter((egg) => {
    const isLowerRarity = ["COMMON", "UNCOMMON", "RARE"].includes(
      targetRarityKey
    );
    const isHigherRarity = ["EPIC", "LEGENDARY"].includes(targetRarityKey);

    if (isLowerRarity && egg.eggType === "COMMON_TO_RARE") {
      return true;
    }
    if (isHigherRarity && egg.eggType === "EPIC_TO_LEGENDARY") {
      return true;
    }

    if (!egg.eggType && egg.recipes && egg.recipes[0]?.ingredients) {
      const hasEnchantedEgg = egg.recipes[0].ingredients.some(
        (ing) => ing.itemKey === "ENCHANTED_EGG"
      );
      const hasSuperEnchantedEgg = egg.recipes[0].ingredients.some(
        (ing) => ing.itemKey === "SUPER_ENCHANTED_EGG"
      );

      if (isLowerRarity && hasEnchantedEgg && !hasSuperEnchantedEgg) {
        return true;
      }
      if (isHigherRarity && hasSuperEnchantedEgg) {
        return true;
      }
    }

    return false;
  });
  if (eggs.length > 0) {
    let obtainStr = "";
    for (const egg of eggs) {
      obtainStr += `**${egg.emoji || "🥚"} ${egg.name}**\n`;
      // Display rarity chances if the egg has them
      if (
        egg.resultRarityChances &&
        typeof egg.resultRarityChances === "object"
      ) {
        obtainStr += `* ${Object.entries(egg.resultRarityChances)
          .map(
            ([rarity, chance]) =>
              `${rarity.charAt(0) + rarity.slice(1).toLowerCase()}: ${(
                chance * 100
              ).toFixed(1)}%`
          )
          .join(" - ")}*\n`;
      }
      if (egg.recipes && egg.recipes[0]?.ingredients) {
        obtainStr += "**Recipe:**\n";
        obtainStr += formatIngredients(egg.recipes[0].ingredients, allItems)
          .split("\n")
          .map((line) => line)
          .join("\n");
        obtainStr += "\n\n";
      }
    }

    if (obtainStr)
      embed.addFields({
        name: "How to Obtain",
        value: obtainStr.trim(),
        inline: false,
      });
  } else if (itemKey === "SQUID_PET") {
    embed.addFields({
      name: "How to Obtain",
      value:
        "It can be obtained from water fishing, we recommend having some <:treasure_chance:1369190619385167882> **Treasure Chance** to drop this pet!",
      inline: false,
    });
  }

  const RarityLeftButton = new ButtonBuilder()
    .setCustomId(
      `pet_nav_rarity_left::itemKey=${itemKey}::rarity=${targetRarityKey}::level=${targetLevel}`
    )
    .setEmoji("⬅️")
    .setLabel("Rarity")
    .setStyle(ButtonStyle.Secondary)
    .setDisabled(currentRarityIndex <= 0);
  const RarityRightButton = new ButtonBuilder()
    .setCustomId(
      `pet_nav_rarity_right::itemKey=${itemKey}::rarity=${targetRarityKey}::level=${targetLevel}`
    )
    .setEmoji("➡️")
    .setLabel("Rarity")
    .setStyle(ButtonStyle.Secondary)
    .setDisabled(currentRarityIndex >= rarityKeys.length - 1);
  const LevelModalButton = new ButtonBuilder()
    .setCustomId(
      `pet_set_level_modal::itemKey=${itemKey}::rarity=${targetRarityKey}::level=${targetLevel}`
    )
    .setLabel("Set Level")
    .setStyle(ButtonStyle.Primary);

  const navRow = new ActionRowBuilder().addComponents(
    RarityLeftButton,
    LevelModalButton,
    RarityRightButton
  );
  components = [navRow];
  embed.setFooter({
    text: `Rarity: ${targetRarityKey} | Level: ${targetLevel}`,
  });

  return { embed, components };
}

// --- Helper to build Minion Embed and Components (adapted from recipe.js) ---
async function buildMinionWikiEmbedAndComponents(
  minionData,
  minionKey,
  tier,
  allItems
) {
  const embed = new EmbedBuilder();
  const components = [];
  // Ensure tiers is an array and filter out null/undefined tiers for accurate count
  const validTiers = Array.isArray(minionData.tiers)
    ? minionData.tiers.filter((t) => t != null)
    : [];
  const currentTierData = validTiers[tier]; // tier is 0-indexed for array access

  if (!currentTierData) {
    embed.setTitle("Error").setDescription("Minion tier data not found.");
    return { embed, components };
  }

  const displayTier = tier + 1; // For user-facing display

  let ingredientsString = "*Recipe not found for this tier*";
  if (
    displayTier === 1 &&
    minionData.recipes &&
    minionData.recipes[0]?.ingredients
  ) {
    ingredientsString = formatIngredients(
      minionData.recipes[0].ingredients,
      allItems
    );
  } else if (displayTier > 1 && Array.isArray(currentTierData.upgradeCost)) {
    ingredientsString = formatIngredients(
      currentTierData.upgradeCost,
      allItems
    );
  }

  const resultString = `${minionData.emoji || "❓"} 1x ${
    minionData.name || minionKey
  }`;

  embed
    .setColor(minionData.rarity?.color || "#FFFFFF")
    .setTitle(
      `${minionData.emoji || "❓"} ${
        minionData.name || minionKey
      } - Tier ${displayTier}`
    )
    .addFields(
      {
        name: `Ingredients (Tier ${displayTier})`,
        value: ingredientsString,
        inline: false,
      },
      {
        name: `Result (Tier ${displayTier})`,
        value: resultString,
        inline: false,
      },
      {
        name: "Production Rate",
        value: `\`${currentTierData.generationIntervalSeconds}s\` per item`,
        inline: true,
      },
      {
        name: "Max Storage",
        value: `\`${currentTierData.maxStorage.toLocaleString()}\` items`,
        inline: true,
      }
    );

  if (displayTier === 1) {
    const reqString = formatRequirements(null, minionData, allItems);
    if (reqString !== "None")
      embed.addFields({
        name: "Requirements (Tier 1)",
        value: reqString,
        inline: false,
      });
  }

  const statsString = formatStats(minionData.baseStats);
  if (statsString !== "`None`") {
    embed.addFields({ name: "Base Stats", value: statsString, inline: false });
  }

  if (validTiers.length > 1) {
    const left = new ButtonBuilder()
      .setCustomId(`minion_wiki_nav_left::itemKey=${minionKey}::tier=${tier}`)
      .setEmoji("⬅️")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(tier <= 0);
    const right = new ButtonBuilder()
      .setCustomId(`minion_wiki_nav_right::itemKey=${minionKey}::tier=${tier}`)
      .setEmoji("➡️")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(tier >= validTiers.length - 1);
    const modalButton = new ButtonBuilder()
      .setCustomId(`minion_wiki_tier_modal::itemKey=${minionKey}::tier=${tier}`)
      .setLabel("Go to Tier...")
      .setStyle(ButtonStyle.Primary);
    const navRow = new ActionRowBuilder().addComponents(
      left,
      modalButton,
      right
    );
    components.push(navRow);
    embed.setFooter({
      text: `Tier ${displayTier} of ${validTiers.length}. Use arrows or modal to view other tiers.`,
    });
  }
  return { embed, components };
}

async function handleMinionWiki(
  interaction,
  itemData,
  itemKey,
  allItems,
  userId
) {
  const initialTier = 0;
  const { embed, components } = await buildMinionWikiEmbedAndComponents(
    itemData,
    itemKey,
    initialTier,
    allItems
  );
  const replyMessage = await interaction.editReply({
    embeds: [embed],
    components,
  });

  const collector = replyMessage.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === userId &&
      (i.customId.startsWith("minion_wiki_nav_left::itemKey=") ||
        i.customId.startsWith("minion_wiki_nav_right::itemKey=") ||
        i.customId.startsWith("minion_wiki_tier_modal::itemKey=")),
    componentType: ComponentType.Button,
    time: 300000,
  });

  collector.on("collect", async (i) => {
    const customId = i.customId;
    let actionType = null;
    let currentItemKey = "";
    let currentTier = 0;

    const parts = customId.split("::");
    actionType = parts[0];

    for (let k = 1; k < parts.length; k++) {
      const [key, value] = parts[k].split("=");
      if (key === "itemKey") currentItemKey = value;
      if (key === "tier") currentTier = parseInt(value) || 0;
    }

    let newTier = currentTier;

    if (actionType === "minion_wiki_nav_left") {
      newTier = Math.max(0, currentTier - 1);
    } else if (actionType === "minion_wiki_nav_right") {
      newTier = Math.min(
        itemData.tiers.filter((t) => t).length - 1,
        currentTier + 1
      );
    } else if (actionType === "minion_wiki_tier_modal") {
      const modal = new ModalBuilder()
        .setCustomId(
          `minion_wiki_modal_submit::itemKey=${currentItemKey}::tier=${currentTier}`
        )
        .setTitle("Go to Minion Tier");
      const tierInput = new TextInputBuilder()
        .setCustomId("tier_input")
        .setLabel(`Enter Tier (1 - ${itemData.tiers.filter((t) => t).length})`)
        .setStyle(TextInputStyle.Short)
        .setRequired(true);
      modal.addComponents(new ActionRowBuilder().addComponents(tierInput));
      await i.showModal(modal);

      try {
        const modalInteraction = await i.awaitModalSubmit({
          filter: (mi) =>
            mi.user.id === userId &&
            mi.customId ===
              `minion_wiki_modal_submit::itemKey=${currentItemKey}::tier=${currentTier}`,
          time: 60000,
        });
        const inputTier = parseInt(
          modalInteraction.fields.getTextInputValue("tier_input")
        );
        if (
          !isNaN(inputTier) &&
          inputTier >= 1 &&
          inputTier <= itemData.tiers.filter((t) => t).length
        ) {
          newTier = inputTier - 1;
          const { embed: newEmbed, components: newComponents } =
            await buildMinionWikiEmbedAndComponents(
              itemData,
              currentItemKey,
              newTier,
              allItems
            );
          await modalInteraction.update({
            embeds: [newEmbed],
            components: newComponents,
          });
        } else {
          await modalInteraction.reply({
            content: "Invalid tier entered.",
            ephemeral: true,
          });
        }
      } catch (error) {
        console.error("Error handling minion wiki interaction:", error);
      }
      return;
    }

    if (
      newTier !== currentTier ||
      i.customId.startsWith("minion_wiki_tier_modal")
    ) {
      const { embed: newEmbed, components: newComponents } =
        await buildMinionWikiEmbedAndComponents(
          itemData,
          currentItemKey,
          newTier,
          allItems
        );
      await i.update({ embeds: [newEmbed], components: newComponents });
    } else {
      await i.deferUpdate();
    }
  });
  collector.on("end", () => {});
}

async function handlePetWiki(interaction, itemData, itemKey, allItems, userId) {
  const initialRarityKey = PET_RARITIES[0];
  const initialLevel = 1;
  const { embed, components } = await buildPetEmbedAndComponents(
    itemData,
    itemKey,
    initialRarityKey,
    initialLevel,
    allItems
  );
  const replyMessage = await interaction.editReply({
    embeds: [embed],
    components,
  });

  const state = {
    itemKey,
    rarity: initialRarityKey,
    level: initialLevel,
  };

  const collector = replyMessage.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === userId &&
      (i.customId.startsWith("pet_nav_rarity_left::") ||
        i.customId.startsWith("pet_nav_rarity_right::") ||
        i.customId.startsWith("pet_set_level_modal::")),
    componentType: ComponentType.Button,
    time: 300000,
  });

  collector.on("collect", async (i) => {
    try {
      const customId = i.customId;
      const actionType = customId.split("::")[0];

      if (
        actionType === "pet_nav_rarity_left" ||
        actionType === "pet_nav_rarity_right"
      ) {
        const rarityKeys = PET_RARITIES;
        const currentRarityIndex = rarityKeys.indexOf(state.rarity);
        let newRarityIndex = currentRarityIndex;

        if (actionType === "pet_nav_rarity_left") {
          newRarityIndex = Math.max(0, currentRarityIndex - 1);
        } else {
          newRarityIndex = Math.min(
            rarityKeys.length - 1,
            currentRarityIndex + 1
          );
        }

        if (newRarityIndex !== currentRarityIndex) {
          state.rarity = rarityKeys[newRarityIndex];
          const { embed: newEmbed, components: newComponents } =
            await buildPetEmbedAndComponents(
              itemData,
              state.itemKey,
              state.rarity,
              state.level,
              allItems
            );
          await i.update({ embeds: [newEmbed], components: newComponents });
        } else {
          await i.deferUpdate().catch(() => {});
        }
      } else if (actionType === "pet_set_level_modal") {
        const modalCustomId = `pet_level_modal_submit::${state.itemKey}::${state.rarity}::${state.level}`;
        const modal = new ModalBuilder()
          .setCustomId(modalCustomId)
          .setTitle("Set Pet Level");

        const levelInput = new TextInputBuilder()
          .setCustomId("level_input")
          .setLabel("Level (1-100)")
          .setStyle(TextInputStyle.Short)
          .setRequired(true)
          .setValue(state.level.toString());

        modal.addComponents(new ActionRowBuilder().addComponents(levelInput));

        await i.showModal(modal);
      }
    } catch (error) {
      console.error("Error handling pet wiki interaction:", error);
    }
  });

  collector.on("end", () => {});
}

async function handlePetLevelModalSubmit(interaction, allItems) {
  try {
    await interaction.deferUpdate();

    const customIdParts = interaction.customId.split("::");

    if (customIdParts.length < 4) {
      console.error(
        "[Wiki Modal Handler] Invalid customId format:",
        interaction.customId
      );
      await interaction.followUp({
        content:
          "An error occurred processing your request (invalid modal ID).",
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const itemKey = customIdParts[1];
    const rarity = customIdParts[2];

    const itemData = allItems[itemKey];
    if (!itemData || itemData.type !== "PET" || !itemData.rarities) {
      console.error(
        "[Wiki Modal Handler] Invalid item data for customId:",
        interaction.customId
      );
      await interaction.followUp({
        content:
          "An error occurred processing your request (item data not found).",
        flags: MessageFlags.Ephemeral,
      });
      return;
    }

    const newLevelInput = interaction.fields.getTextInputValue("level_input");
    const newLevel = parseInt(newLevelInput);

    if (!isNaN(newLevel) && newLevel >= 1 && newLevel <= 100) {
      const { embed: newEmbed, components: newComponents } =
        await buildPetEmbedAndComponents(
          itemData,
          itemKey,
          rarity,
          newLevel,
          allItems
        );

      await interaction.editReply({
        embeds: [newEmbed],
        components: newComponents,
      });
    } else {
      await interaction.followUp({
        content: "❌ Please enter a valid level between 1 and 100.",
        flags: MessageFlags.Ephemeral,
      });
    }
  } catch (error) {
    console.error("Error handling pet level modal submit:", error);
  }
}

async function handleGeneralItemWiki(
  interaction,
  itemData,
  itemKey,
  allItems,
  playerData = null
) {
  const generalEmbed = new EmbedBuilder()
    .setTitle(`${itemData.emoji || "❓"} ${itemData.name || itemKey}`)
    .setColor(getItemColor(itemData));
  if (itemData.description) generalEmbed.setDescription(itemData.description);

  // Show enchantment effect for Enchanted Book items
  if (itemData.type === "Enchanted Book") {
    try {
      // Import ENCHANTMENTS from enchant.js
      const {
        ENCHANTMENTS,
        formatStatBonusWithEmoji,
      } = require("./enchant.js");
      // Try to parse enchantment key and level from itemKey (e.g., SUNDER_3_BOOK)
      const match = itemKey.match(/([A-Z_]+)_([0-9]+)_BOOK/);
      if (match) {
        const enchKey = match[1];
        const level = parseInt(match[2]);
        const enchant = ENCHANTMENTS[enchKey];
        if (enchant) {
          // Show effect using formatStatBonusWithEmoji if available
          const effectText = formatStatBonusWithEmoji(
            enchKey,
            level,
            null,
            null
          );
          generalEmbed.addFields({
            name: "Enchantment Effect",
            value: effectText || enchant.description || "No effect",
            inline: false,
          });

          // Show what item types this book can be applied to
          if (
            Array.isArray(enchant.applicableTypes) &&
            enchant.applicableTypes.length > 0
          ) {
            // Friendly names for types
            const typeMap = {
              FARMING_AXE: "Farming Axes",
              WEAPON: "Weapons",
              HELMET: "Helmets",
              CHESTPLATE: "Chestplates",
              LEGGINGS: "Leggings",
              BOOTS: "Boots",
              HOE: "Hoes",
              PICKAXE: "Pickaxes",
              SHOVEL: "Shovels",
              AXE: "Axes",
              ROD: "Fishing Rods",
              ARMOR: "Armor",
            };
            const friendlyTypes = enchant.applicableTypes
              .map((t) => typeMap[t] || t)
              .join(", ");
            generalEmbed.addFields({
              name: "Can be applied to",
              value: friendlyTypes,
              inline: false,
            });
          }
        } else {
          generalEmbed.addFields({
            name: "Enchantment Effect",
            value: "No effect info found for this enchantment.",
            inline: false,
          });
        }
      } else {
        generalEmbed.addFields({
          name: "Enchantment Effect",
          value: "Unknown enchantment book format.",
          inline: false,
        });
      }
    } catch (err) {
      generalEmbed.addFields({
        name: "Enchantment Effect",
        value: "Error loading enchantment info.",
        inline: false,
      });
    }
  }

  // Add Type and Rarity fields first, before any description modifications
  if (itemData.type) {
    let typeValue = `\`${itemData.type_name || itemData.type}\``;

    if (itemData.minionFuel) {
      typeValue += "\n`Minion Fuel`";
    }
    generalEmbed.addFields({
      name: "Type",
      value: typeValue,
      inline: true,
    });
  }
  if (itemData.rarity)
    generalEmbed.addFields({
      name: "Rarity",
      value: `\`${ITEM_RARITY[itemData.rarity]?.name || itemData.rarity}\``,
      inline: true,
    });

  // Add Booster Cookie perks information as a field if this is a booster cookie
  if (itemKey === "BOOSTER_COOKIE") {
    const {
      getBoosterCookiePerksDescription,
    } = require("../utils/boosterCookiePerks");
    const perksDescription = getBoosterCookiePerksDescription();

    generalEmbed.addFields({
      name: "\u200B", // invisible character to create spacing!
      value: perksDescription,
      inline: false,
    });
  }

  const statsString = formatStats(itemData.baseStats);
  if (statsString !== "`None`")
    generalEmbed.addFields({
      name: "Stats",
      value: statsString,
      inline: false,
    });

  // Display dynamic stats (scaling with skill levels)
  if (itemData.dynamicStats) {
    let dynamicStatsString = "";
    for (const [statKey, dynamicData] of Object.entries(
      itemData.dynamicStats
    )) {
      const statDef = STATS[statKey.toUpperCase()];
      const emoji = statDef?.emoji || "";
      const displayName =
        statDef?.name ||
        statKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

      let description = "";
      if (dynamicData.base !== undefined) {
        description += `Base: ${dynamicData.base}`;
      }
      if (dynamicData.perFarmingLevel) {
        description += description
          ? `, +${dynamicData.perFarmingLevel} per Farming Level`
          : `+${dynamicData.perFarmingLevel} per Farming Level`;
      }
      if (dynamicData.perMiningLevel) {
        description += description
          ? `, +${dynamicData.perMiningLevel} per Mining Level`
          : `+${dynamicData.perMiningLevel} per Mining Level`;
      }
      if (dynamicData.perForagingLevel) {
        description += description
          ? `, +${dynamicData.perForagingLevel} per Foraging Level`
          : `+${dynamicData.perForagingLevel} per Foraging Level`;
      }
      if (dynamicData.perFishingLevel) {
        description += description
          ? `, +${dynamicData.perFishingLevel} per Fishing Level`
          : `+${dynamicData.perFishingLevel} per Fishing Level`;
      }
      if (dynamicData.perCombatLevel) {
        description += description
          ? `, +${dynamicData.perCombatLevel} per Combat Level`
          : `+${dynamicData.perCombatLevel} per Combat Level`;
      }
      if (dynamicData.perEnchantingLevel) {
        description += description
          ? `, +${dynamicData.perEnchantingLevel} per Enchanting Level`
          : `+${dynamicData.perEnchantingLevel} per Enchanting Level`;
      }
      if (dynamicData.perAlchemyLevel) {
        description += description
          ? `, +${dynamicData.perAlchemyLevel} per Alchemy Level`
          : `+${dynamicData.perAlchemyLevel} per Alchemy Level`;
      }
      if (dynamicData.perTamingLevel) {
        description += description
          ? `, +${dynamicData.perTamingLevel} per Taming Level`
          : `+${dynamicData.perTamingLevel} per Taming Level`;
      }

      if (description) {
        dynamicStatsString += `${emoji ? emoji + " " : ""}**${displayName}:** ${description}\n`;
      }
    }

    if (dynamicStatsString) {
      generalEmbed.addFields({
        name: "Scaling Stats",
        value: dynamicStatsString.trim(),
        inline: false,
      });
    }
  }

  // Handle both single ability and multiple abilities
  const abilities =
    itemData.abilities ||
    (itemData.ability ? { singleAbility: itemData.ability } : {});

  if (Object.keys(abilities).length > 0) {
    let abilityString = "";

    for (const [, ability] of Object.entries(abilities)) {
      if (abilityString) abilityString += "\n\n"; // Add spacing between abilities

      if (ability.type === "PROGRESSIVE" && ability.name === "Frenzy") {
        const forsweepEmoji = "<:foraging_sweep:1385549884186431578>";
        abilityString += `**Ability: ${ability.name}**\nGains ${forsweepEmoji} +1 Foraging Sweep every 2,000 Logs cut on Galatea.\n(Max 20)`;
      } else if (
        ability.type === "BULWARK" ||
        ability.type === "ZOMBIE_BULWARK"
      ) {
        const defenseEmoji = STATS.DEFENSE.emoji;
        const currentBonus = ability.thresholds[0].bonus;

        let nextUpgradeText = "";
        if (ability.thresholds.length > 1) {
          const nextThreshold = ability.thresholds[1];
          nextUpgradeText = `\n**Next Upgrade:** +${nextThreshold.bonus} ${defenseEmoji} (${nextThreshold.kills.toLocaleString()}/${nextThreshold.kills.toLocaleString()} Kills)`;
        } else {
          nextUpgradeText = "\n*Max Upgrade Reached*";
        }

        abilityString += `**Ability: ${ability.name}**\n${ability.description}\n**Piece Bonus:** +${currentBonus} ${defenseEmoji}${nextUpgradeText}`;
      } else if (ability.type === "KILL_TRACKER") {
        const damageEmoji = STATS.DAMAGE.emoji;
        const killsPerBonus = ability.killsPerBonus || 500;
        const maxBonus = ability.maxBonus || 35;

        abilityString += `**Ability: ${ability.name}**\n${ability.description}\n**Max Bonus:** +${maxBonus} ${damageEmoji} (${(maxBonus * killsPerBonus).toLocaleString()} kills)`;
      } else if (ability.type === "WOOD_COLLECTION_TRACKER") {
        const strengthEmoji = STATS.STRENGTH.emoji;
        const woodPerBonus = ability.woodPerBonus || 500;
        const maxBonus = ability.maxBonus || 100;

        abilityString += `**Ability: ${ability.name}**\n${ability.description}\n**Max Bonus:** +${maxBonus} ${strengthEmoji} (${(maxBonus * woodPerBonus).toLocaleString()} wood collected)`;
      } else if (ability.name && ability.description) {
        let formattedAbility = `**${ability.name}**\n${ability.description}`;

        // Add specific ability details if available
        if (ability.chance) {
          formattedAbility += `\n*Chance: ${(ability.chance * 100).toFixed(2)}%*`;
        }
        if (ability.cooldown) {
          formattedAbility += `\n*Cooldown: ${ability.cooldown}s*`;
        }
        if (ability.duration) {
          formattedAbility += `\n*Duration: ${ability.duration}s*`;
        }
        if (ability.bonus) {
          formattedAbility += `\n*Bonus: +${ability.bonus}*`;
        }

        abilityString += formattedAbility;
      }
    }

    if (abilityString) {
      generalEmbed.addFields({
        name: "Item Abilities",
        value: abilityString,
        inline: false,
      });
    }
  }

  // Display pet abilities if this item grants them
  if (itemData.petAbilities) {
    let petAbilityString = "";

    for (const [abilityName, abilityData] of Object.entries(
      itemData.petAbilities
    )) {
      if (petAbilityString) petAbilityString += "\n\n";

      petAbilityString += `**Pet Ability: ${abilityName}**\n${abilityData.description || "No description available"}`;

      if (abilityData.bonus) {
        petAbilityString += `\n*Bonus: +${abilityData.bonus}*`;
      }
      if (abilityData.chance) {
        petAbilityString += `\n*Chance: ${(abilityData.chance * 100).toFixed(2)}%*`;
      }
    }

    if (petAbilityString) {
      generalEmbed.addFields({
        name: "Pet Abilities",
        value: petAbilityString,
        inline: false,
      });
    }
  }

  // Display item effects
  if (itemData.effects) {
    let effectsString = "";
    const effects = itemData.effects;

    if (effects.healthRegenDoubler) {
      effectsString +=
        "**Special Effect:** Doubles your health regeneration rate when worn.\n";
    }
    if (effects.vitalityDoubler) {
      effectsString +=
        "**Special Effect:** Doubles your Vitality when worn, boosting all healing received.\n";
    }
    if (effects.zombieDamageReduction) {
      effectsString += `**Special Effect:** Reduces damage taken from undead enemies by ${(effects.zombieDamageReduction * 100).toFixed(0)}%.\n`;
    }
    if (effects.skeletonDamageReduction) {
      effectsString += `**Special Effect:** Reduces damage taken from skeleton enemies by ${(effects.skeletonDamageReduction * 100).toFixed(0)}%.\n`;
    }
    if (effects.seaCreatureDamageReduction) {
      effectsString += `**Special Effect:** Reduces damage taken from sea creatures by ${(effects.seaCreatureDamageReduction * 100).toFixed(0)}%.\n`;
    }

    if (effectsString) {
      generalEmbed.addFields({
        name: "\u200B",
        value: effectsString.trim(),
        inline: false,
      });
    }
  }

  const setBonusString = formatSetBonusInfo(itemKey);
  if (setBonusString) {
    generalEmbed.addFields({
      name: "Set Bonuses",
      value: setBonusString,
      inline: false,
    });
  }

  if (Array.isArray(itemData.recipes) && itemData.recipes.length > 0) {
    if (itemData.recipes.length === 1 && itemData.recipes[0]?.ingredients) {
      const ingredients = formatIngredients(
        itemData.recipes[0].ingredients,
        allItems
      );
      const resultAmount = itemData.recipes[0].resultAmount || 1;
      const resultString = `${itemData.emoji || "❓"} \`${resultAmount.toLocaleString()}x ${itemData.name || itemKey}\``;
      generalEmbed.addFields(
        { name: "Ingredients", value: ingredients, inline: false },
        { name: "Result", value: resultString, inline: false }
      );
    } else {
      itemData.recipes.forEach((rec, idx) => {
        if (!rec?.ingredients) return;
        const ingStr = formatIngredients(rec.ingredients, allItems);
        const resAmt = rec.resultAmount || 1;
        const resStr = `${itemData.emoji || "❓"} \`${resAmt.toLocaleString()}x ${itemData.name || itemKey}\``;
        generalEmbed.addFields(
          {
            name: `Recipe ${idx + 1} Ingredients`,
            value: ingStr,
            inline: false,
          },
          { name: `Recipe ${idx + 1} Result`, value: resStr, inline: false }
        );
      });
    }
  }

  // Display brewing information for potions
  if (itemData.brewing) {
    const brewing = itemData.brewing;
    let brewingInfo = "";

    // Show ingredients
    if (brewing.ingredients) {
      brewingInfo += "**Ingredients:**\n";
      brewingInfo += formatIngredients(brewing.ingredients, allItems) + "\n\n";
    }

    // Show output
    brewingInfo += `**Output:** ${itemData.emoji || "🧪"} \`1x ${itemData.name || itemKey}\`\n\n`;

    // Show alchemy XP gained
    if (brewing.expGained) {
      const alchemyEmoji = skillEmojis.alchemy || "⚗️";
      brewingInfo += `**XP Gained:** ${alchemyEmoji} ${brewing.expGained} Alchemy XP\n\n`;
    }

    // Show required alchemy level with checkmark/X
    if (itemData.requiredLevel) {
      const alchemyEmoji = skillEmojis.alchemy || "⚗️";
      const playerExists = playerData != null;
      const playerAlchemyLevel = playerExists
        ? getPlayerSkillLevel(playerData, "alchemy")
        : 0;
      const meetsReq =
        playerExists && playerAlchemyLevel >= itemData.requiredLevel;
      const checkMark = meetsReq ? "✅" : "❌";
      brewingInfo += `${checkMark} Requires ${alchemyEmoji} \`Alchemy Lvl ${itemData.requiredLevel}\` **to Brew**`;
    }

    if (brewingInfo) {
      generalEmbed.addFields({
        name: "\u200B",
        value: brewingInfo.trim(),
        inline: false,
      });
    }
  }

  // Display potion effects if this is a consumable potion
  if (itemData.consumable && itemData.consumable.type === "POTION_EFFECT") {
    const { formatDuration } = require("../utils/potionEffects.js");
    const consumable = itemData.consumable;
    let effectsInfo = "";

    // Show duration
    if (consumable.duration) {
      const durationMs = consumable.duration * 1000; // convert seconds to milliseconds
      effectsInfo += `**Duration:** ${formatDuration(durationMs)}\n\n`;
    }

    // Show effects
    if (consumable.effects && Object.keys(consumable.effects).length > 0) {
      effectsInfo += "**Effects:**\n";
      for (const [statKey, value] of Object.entries(consumable.effects)) {
        const statDef = STATS[statKey];
        const emoji = statDef?.emoji || "";
        const displayName =
          statDef?.name ||
          statKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        effectsInfo += `${emoji ? emoji + " " : ""}**${displayName}:** +${value}\n`;
      }
    }

    if (effectsInfo) {
      generalEmbed.addFields({
        name: "Potion Effects",
        value: effectsInfo.trim(),
        inline: false,
      });
    }
  }

  const requirementsString = formatRequirements(playerData, itemData, allItems);
  if (requirementsString !== "None") {
    generalEmbed.addFields({
      name: "Requirements",
      value: requirementsString,
      inline: false,
    });
  }
  const buyable = formatBuyableFromNPCs(itemKey);
  if (buyable)
    generalEmbed.addFields({
      name: "Buyable From",
      value: buyable,
      inline: false,
    });
  const sellPrice = formatNPCSellPrice(itemData);
  if (sellPrice)
    generalEmbed.addFields({
      name: "NPC Sell Price",
      value: sellPrice,
      inline: false,
    });
  const drops = formatMobDrops(itemKey);
  if (drops)
    generalEmbed.addFields({ name: "Dropped By", value: drops, inline: false });

  if (itemData.minionFuel) {
    const fuelInfo = itemData.minionFuel;
    const speedBoostPercent = Math.round(fuelInfo.speedBoost * 100);
    const durationHours = Math.floor(fuelInfo.durationMinutes / 60);
    const durationMins = fuelInfo.durationMinutes % 60;

    let durationText = "";
    if (durationHours > 0) {
      durationText =
        durationMins > 0
          ? `${durationHours}h ${durationMins}m`
          : `${durationHours}h`;
    } else {
      durationText = `${durationMins}m`;
    }

    generalEmbed.addFields({
      name: "Fuel info:",
      value: `**Speed Boost:** +${speedBoostPercent}%\n**Duration:** ${durationText}`,
      inline: false,
    });
  }

  const components = [];
  if (interaction.fromMissingAccessories) {
    const backButton = new ButtonBuilder()
      .setCustomId("back_to_missing_accessories")
      .setLabel("← Back to Missing Accessories")
      .setStyle(ButtonStyle.Secondary);

    components.push(new ActionRowBuilder().addComponents(backButton));
  }

  const response = await interaction.editReply({
    embeds: [generalEmbed],
    components,
  });

  if (interaction.fromMissingAccessories && components.length > 0) {
    const collector = response.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: 300000,
    });

    collector.on("collect", async (buttonInteraction) => {
      if (buttonInteraction.user.id !== interaction.originalUserId) {
        return buttonInteraction.reply({
          content: "You can only use your own buttons!",
          ephemeral: true,
        });
      }

      if (buttonInteraction.customId === "back_to_missing_accessories") {
        if (!buttonInteraction.deferred && !buttonInteraction.replied) {
          try {
            await buttonInteraction.deferUpdate();
          } catch (error) {
            console.log(
              "[Wiki] Failed to defer back button interaction:",
              error.message
            );
            return;
          }
        }

        collector.stop("navigating_back_to_missing");

        try {
          await buttonInteraction.message.edit({ components: [] });
        } catch (error) {
          console.log("[Wiki] Failed to clear components:", error.message);
        }

        const { showMissingAccessories } = require("./accessories.js");

        const showBackButton = interaction.fromAccessoriesCommand || false;

        await showMissingAccessories(
          interaction,
          interaction.originalUserId,
          showBackButton
        );
      }
    });

    collector.on("end", (collected, reason) => {
      if (
        reason === "navigating_back_to_missing" ||
        reason === "navigating_back_to_accessories"
      ) {
        return;
      }

      if (components.length > 0) {
        const disabledComponents = components.map((row) => {
          const newRow = ActionRowBuilder.from(row);
          newRow.components.forEach((component) => {
            component.setDisabled(true);
          });
          return newRow;
        });

        interaction
          .editReply({ components: disabledComponents })
          .catch(() => {});
      }
    });
  }
}

async function handleReforgesWiki(
  interaction,
  selectedCategory = null,
  selectedRarity = "Legendary"
) {
  if (!selectedCategory) {
    const embed = new EmbedBuilder()
      .setTitle("🔨 Reforge Information")
      .setDescription(
        "Select a category below to view available reforges for that item type."
      )
      .setColor(EMBED_COLORS.GOLD);

    const costFields = [];
    // Only show the proper case versions to avoid duplicates
    const properRarities = [
      "Common",
      "Uncommon",
      "Rare",
      "Epic",
      "Legendary",
      "Mythic",
    ];
    for (const rarity of properRarities) {
      const cost = REFORGE_COSTS[rarity];
      if (cost !== undefined) {
        costFields.push({
          name: `${rarity} Rarity`,
          value: `<:purse_coins:1367849116033482772> ${cost.toLocaleString()} coins`,
          inline: true,
        });
      }
    }
    embed.addFields(costFields);

    const categorySelect = new StringSelectMenuBuilder()
      .setCustomId("reforge_category_select")
      .setPlaceholder("Choose a reforge category...")
      .addOptions(
        new StringSelectMenuOptionBuilder()
          .setLabel("Weapon Reforges")
          .setDescription("Available for Swords and Bows")
          .setValue("WEAPON"),
        new StringSelectMenuOptionBuilder()
          .setLabel("Armor Reforges")
          .setDescription("Available for all Armor pieces")
          .setValue("ARMOR"),
        new StringSelectMenuOptionBuilder()
          .setLabel("Pickaxe Reforges")
          .setDescription("Available for Pickaxes")
          .setValue("PICKAXE"),
        new StringSelectMenuOptionBuilder()
          .setLabel("Axe Reforges")
          .setDescription("Available for Axes")
          .setValue("AXE"),
        new StringSelectMenuOptionBuilder()
          .setLabel("Hoe Reforges")
          .setDescription("Available for Hoes")
          .setValue("HOE")
      );

    const selectRow = new ActionRowBuilder().addComponents(categorySelect);

    await interaction.editReply({ embeds: [embed], components: [selectRow] });
  } else {
    const categoryNames = {
      WEAPON: "<:stone_sword:1369433285582786722> Weapon Reforges",
      ARMOR: "<:diamond_chestplate:1375560534359019520> Armor Reforges",
      PICKAXE: "<:diamond_pickaxe:1373220304586801252> Pickaxe Reforges",
      AXE: "<:diamond_axe:1373220302154240041> Axe Reforges",
      HOE: "<:diamond_hoe:1373220303458795642> Hoe Reforges",
    };

    const embed = new EmbedBuilder()
      .setTitle(categoryNames[selectedCategory] || "Reforges")
      .setDescription(`Showing ${selectedRarity} rarity stats`)
      .setColor(EMBED_COLORS.GOLD);

    const reforgeData = REFORGE_DATA[selectedCategory];
    if (!reforgeData) {
      embed.setDescription("No reforge data found for this category.");
    } else {
      const reforgeEntries = Object.entries(reforgeData);
      const fields = [];

      for (const [, reforge] of reforgeEntries) {
        const rarityStats =
          reforge.stats[selectedRarity] ||
          reforge.stats.Legendary ||
          reforge.stats.Epic ||
          reforge.stats.Rare;

        let statsDisplay = "No stats available";
        if (rarityStats) {
          const statsArray = Object.entries(rarityStats).map(
            ([statKey, value]) => {
              const statDef = STATS[statKey.toUpperCase()];
              const emoji = statDef?.emoji || "";
              const fullName =
                statDef?.name ||
                statKey
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (l) => l.toUpperCase());
              const displayName = STAT_ABBREVIATIONS[fullName] || fullName;
              const sign = value > 0 ? "+" : "";
              return `${emoji} \`${displayName}: ${sign}${value}\``;
            }
          );

          statsDisplay = statsArray.join("\n");
        }

        fields.push({
          name: `**${reforge.name}**`,
          value: statsDisplay,
          inline: true,
        });
      }

      embed.addFields(fields);
    }

    const rarityButtons = [
      new ButtonBuilder()
        .setCustomId(`reforge_rarity_${selectedCategory}_Common`)
        .setLabel("Common")
        .setStyle(
          selectedRarity === "Common"
            ? ButtonStyle.Primary
            : ButtonStyle.Secondary
        )
        .setEmoji("⚪"),
      new ButtonBuilder()
        .setCustomId(`reforge_rarity_${selectedCategory}_Uncommon`)
        .setLabel("Uncommon")
        .setStyle(
          selectedRarity === "Uncommon"
            ? ButtonStyle.Primary
            : ButtonStyle.Secondary
        )
        .setEmoji("🟢"),
      new ButtonBuilder()
        .setCustomId(`reforge_rarity_${selectedCategory}_Rare`)
        .setLabel("Rare")
        .setStyle(
          selectedRarity === "Rare"
            ? ButtonStyle.Primary
            : ButtonStyle.Secondary
        )
        .setEmoji("🔵"),
      new ButtonBuilder()
        .setCustomId(`reforge_rarity_${selectedCategory}_Epic`)
        .setLabel("Epic")
        .setStyle(
          selectedRarity === "Epic"
            ? ButtonStyle.Primary
            : ButtonStyle.Secondary
        )
        .setEmoji("🟣"),
      new ButtonBuilder()
        .setCustomId(`reforge_rarity_${selectedCategory}_Legendary`)
        .setLabel("Legendary")
        .setStyle(
          selectedRarity === "Legendary"
            ? ButtonStyle.Primary
            : ButtonStyle.Secondary
        )
        .setEmoji("🟠"),
    ];

    const mythicButton = new ButtonBuilder()
      .setCustomId(`reforge_rarity_${selectedCategory}_Mythic`)
      .setLabel("Mythic")
      .setStyle(
        selectedRarity === "Mythic"
          ? ButtonStyle.Primary
          : ButtonStyle.Secondary
      )
      .setEmoji("🔴");

    const backButton = new ButtonBuilder()
      .setCustomId("reforge_back")
      .setLabel("← Back to Categories")
      .setStyle(ButtonStyle.Secondary);

    const buttonRow1 = new ActionRowBuilder().addComponents(
      rarityButtons.slice(0, 5)
    );
    const buttonRow2 = new ActionRowBuilder().addComponents(
      mythicButton,
      backButton
    );

    await interaction.editReply({
      embeds: [embed],
      components: [buttonRow1, buttonRow2],
    });
  }
}

async function handleMobWiki(interaction, mobData) {
  const allItems = configManager.getAllItems();

  // Get rarity color, fallback to default if no rarity
  const rarityColor = mobData.rarity
    ? ITEM_RARITY[mobData.rarity]?.color || "#FFFFFF"
    : "#00AE86";

  const embed = new EmbedBuilder()
    .setTitle(`${mobData.emoji || "❓"} ${mobData.name}`)
    .setColor(rarityColor);

  // Basic mob info
  let description = "";
  if (mobData.level) {
    description += `**Level:** ${mobData.level}\n`;
  }

  // Unlocked at fishing level (only for sea creatures)
  if (mobData.seaCreature && mobData.fishingLevelReq) {
    description += `**Unlocked at:** ${skillEmojis.fishing} Fishing **Level ${mobData.fishingLevelReq}**\n`;
  }

  embed.setDescription(description || "No additional information available.");

  // Stats (remove emoji)
  if (mobData.baseStats) {
    const statsText = formatStats(mobData.baseStats);
    if (statsText !== "`None`") {
      embed.addFields({ name: "Base Stats", value: statsText, inline: true });
    }
  }

  // Abilities
  if (
    mobData.abilities &&
    Array.isArray(mobData.abilities) &&
    mobData.abilities.length > 0
  ) {
    const abilityDescriptions = getMobAbilityDescriptions(mobData.abilities);
    if (abilityDescriptions.length > 0) {
      const abilitiesText = abilityDescriptions.join("\n");
      embed.addFields({
        name: "Abilities",
        value: abilitiesText,
        inline: false,
      });
    }
  }

  // Drops (merge rewards with loot drops, remove emoji)
  let dropsText = "";

  // Add experience and coins to drops
  if (mobData.baseExp) {
    const expAmount = mobData.baseExp.amount || mobData.baseExp;
    const sourceSkill = mobData.baseExp.sourceSkill || "combat"; // Default to combat if not specified
    const skillEmoji = skillEmojis[sourceSkill] || skillEmojis.default;
    const skillName =
      sourceSkill.charAt(0).toUpperCase() + sourceSkill.slice(1); // Capitalize first letter
    dropsText += `${skillEmoji} **${skillName} EXP** : ${expAmount}x\n`;
  }
  // Handle both coins (regular mobs) and baseCoins (Sea Creatures)
  const coinData = mobData.coins || mobData.baseCoins;
  if (coinData) {
    if (
      typeof coinData === "object" &&
      coinData.min !== undefined &&
      coinData.max !== undefined
    ) {
      dropsText += `<:purse_coins:1367849116033482772> **Coins** : ${coinData.min.toLocaleString()}-${coinData.max.toLocaleString()}x\n`;
    } else if (typeof coinData === "number") {
      dropsText += `<:purse_coins:1367849116033482772> **Coins** : ${coinData.toLocaleString()}x\n`;
    }
  }

  // Add loot drops
  if (
    mobData.loot &&
    (mobData.loot.drops || mobData.loot.dropGroups || mobData.loot.lootTables)
  ) {
    // Regular drops
    if (mobData.loot.drops && mobData.loot.drops.length > 0) {
      for (const drop of mobData.loot.drops) {
        const itemData = allItems[drop.itemKey];
        const itemName = itemData?.name || drop.itemKey;
        const itemEmoji = itemData?.emoji || "❓";

        let amountStr = "";
        const isGuaranteed = (drop.chance || 1) >= 1;

        if (
          typeof drop.amount === "object" &&
          drop.amount.min !== undefined &&
          drop.amount.max !== undefined
        ) {
          if (isGuaranteed) {
            amountStr = `${drop.amount.min === drop.amount.max ? drop.amount.min : `${drop.amount.min}-${drop.amount.max}`}x`;
          } else {
            const chancePercent = ((drop.chance || 0) * 100)
              .toFixed(1)
              .replace(/\.0$/, "");
            amountStr = `0-${drop.amount.max}x **(${chancePercent}%)**`;
          }
        } else {
          if (isGuaranteed) {
            amountStr = `${drop.amount}x`;
          } else {
            const chancePercent = ((drop.chance || 0) * 100)
              .toFixed(1)
              .replace(/\.0$/, "");
            amountStr = `0-${drop.amount}x **(${chancePercent}%)**`;
          }
        }

        dropsText += `${itemEmoji} **${itemName}** : ${amountStr}\n`;
      }
    }

    // Drop groups
    if (mobData.loot.dropGroups && mobData.loot.dropGroups.length > 0) {
      for (const group of mobData.loot.dropGroups) {
        if (group.items && group.items.length > 0) {
          for (const item of group.items) {
            const itemData = allItems[item.itemKey];
            const itemName = itemData?.name || item.itemKey;
            const itemEmoji = itemData?.emoji || "❓";

            const groupChance = group.chance || 1;
            const chancePercent = (groupChance * 100)
              .toFixed(1)
              .replace(/\.0$/, "");

            let amountStr = "";
            const amount = item.amount || 1; // Default to 1 if amount is undefined
            if (
              typeof amount === "object" &&
              amount.min !== undefined &&
              amount.max !== undefined
            ) {
              amountStr = `0-${amount.max}x **(${chancePercent}%)**`;
            } else {
              amountStr = `0-${amount}x **(${chancePercent}%)**`;
            }

            dropsText += `${itemEmoji} **${itemName}** : ${amountStr}\n`;
          }
        }
      }
    }

    // LootTables system (used by slayer bosses)
    if (mobData.loot.lootTables) {
      for (const [, table] of Object.entries(mobData.loot.lootTables)) {
        if (Array.isArray(table.items)) {
          for (const item of table.items) {
            const itemData = allItems[item.itemKey];
            const itemName = itemData?.name || item.itemKey;
            const itemEmoji = itemData?.emoji || "❓";

            let amountStr = "";
            const isGuaranteed = (item.chance || 1) >= 1;

            if (
              typeof item.amount === "object" &&
              item.amount.min !== undefined &&
              item.amount.max !== undefined
            ) {
              if (isGuaranteed) {
                amountStr = `${item.amount.min === item.amount.max ? item.amount.min : `${item.amount.min}-${item.amount.max}`}x`;
              } else {
                const chancePercent = ((item.chance || 0) * 100)
                  .toFixed(1)
                  .replace(/\.0$/, "");
                amountStr = `0-${item.amount.max}x **(${chancePercent}%)**`;
              }
            } else {
              if (isGuaranteed) {
                amountStr = `${item.amount || 1}x`;
              } else {
                const chancePercent = ((item.chance || 0) * 100)
                  .toFixed(1)
                  .replace(/\.0$/, "");
                amountStr = `0-${item.amount || 1}x **(${chancePercent}%)**`;
              }
            }

            dropsText += `${itemEmoji} **${itemName}** : ${amountStr}\n`;
          }
        }
      }
    }
  }

  if (dropsText) {
    embed.addFields({ name: "Drops", value: dropsText.trim(), inline: false });
  }

  await interaction.editReply({ embeds: [embed] });
}

async function handleStatWiki(interaction, statKey, statDef, allItems) {
  const pageSize = 16;

  const matchingItems = Object.entries(allItems).filter(([itemKey, item]) => {
    const hasBase = item.baseStats && item.baseStats[statKey] !== undefined;
    const hasDynamic =
      item.dynamicStats && item.dynamicStats[statKey] !== undefined;
    const hasOther = item.stats && item.stats[statKey] !== undefined;

    // Check if this item is part of a set that provides this stat
    let hasSetBonus = false;
    for (const setData of SET_BONUSES) {
      if (setData.itemKeys.includes(itemKey)) {
        // Check if any threshold in this set provides the stat we're looking for
        for (const threshold of setData.thresholds) {
          if (threshold.bonus) {
            // Check for the stat in various formats
            const hasDirectStat = threshold.bonus[statKey] !== undefined;

            // Check for camelCase versions of the stat (e.g., farmingSweep for FARMING_SWEEP)
            const statLowerCamel = statKey
              .toLowerCase()
              .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
            const hasCamelCase = threshold.bonus[statLowerCamel] !== undefined;

            if (hasDirectStat || hasCamelCase) {
              hasSetBonus = true;
              break;
            }
          }
        }
        if (hasSetBonus) break;
      }
    }

    return hasBase || hasDynamic || hasOther || hasSetBonus;
  });

  if (matchingItems.length === 0) {
    await interaction.editReply({
      content: `❌ No items grant **${statDef.name}**.`,
      ephemeral: true,
    });
    return;
  }

  // Enhanced sorting that considers set bonuses
  matchingItems.sort(([aKey, aItem], [bKey, bItem]) => {
    const getVal = (itemKey, item) => {
      let value = 0;

      // Get value from item stats
      if (item.baseStats && typeof item.baseStats[statKey] === "number")
        value += item.baseStats[statKey];
      if (item.stats && typeof item.stats[statKey] === "number")
        value += item.stats[statKey];
      if (
        item.dynamicStats &&
        item.dynamicStats[statKey] &&
        typeof item.dynamicStats[statKey].base === "number"
      )
        value += item.dynamicStats[statKey].base;

      // Get maximum value from set bonuses
      for (const setData of SET_BONUSES) {
        if (setData.itemKeys.includes(itemKey)) {
          let maxSetBonus = 0;
          for (const threshold of setData.thresholds) {
            if (threshold.bonus) {
              const directStat = threshold.bonus[statKey];
              const statLowerCamel = statKey
                .toLowerCase()
                .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
              const camelCaseStat = threshold.bonus[statLowerCamel];

              const setBonusValue = directStat || camelCaseStat || 0;
              if (setBonusValue > maxSetBonus) {
                maxSetBonus = setBonusValue;
              }
            }
          }
          value += maxSetBonus;
          break;
        }
      }

      return value;
    };
    return getVal(bKey, bItem) - getVal(aKey, aItem);
  });

  const lines = matchingItems.map(([itemKey, item]) => {
    let valueStr = "";
    const sources = [];

    // Get value from item stats
    const base = item.baseStats?.[statKey] ?? item.stats?.[statKey];
    const dyn = item.dynamicStats?.[statKey];

    if (typeof base === "number" && base > 0) {
      sources.push(`+${base}`);
    } else if (dyn && typeof dyn.base === "number" && dyn.base > 0) {
      sources.push(`+${dyn.base}`);
    }

    // Get value from set bonuses
    for (const setData of SET_BONUSES) {
      if (setData.itemKeys.includes(itemKey)) {
        let maxSetBonus = 0;
        for (const threshold of setData.thresholds) {
          if (threshold.bonus) {
            const directStat = threshold.bonus[statKey];
            const statLowerCamel = statKey
              .toLowerCase()
              .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
            const camelCaseStat = threshold.bonus[statLowerCamel];

            const setBonusValue = directStat || camelCaseStat || 0;
            if (setBonusValue > maxSetBonus) {
              maxSetBonus = setBonusValue;
            }
          }
        }
        if (maxSetBonus > 0) {
          sources.push(`+${maxSetBonus} (Set)`);
        }
        break;
      }
    }

    if (sources.length > 0) {
      valueStr = ` ${sources.join(", ")}`;
    }

    return `${item.emoji || "❓"} **${item.name}**${valueStr}`.trim();
  });

  const totalPages = Math.ceil(lines.length / pageSize);

  const buildEmbed = (page) => {
    const start = page * pageSize;
    const pageLines = lines.slice(start, start + pageSize);
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle(`${statDef.emoji || ""} Items with ${statDef.name}`)
      .setDescription(pageLines.join("\n"))
      .setFooter({ text: `Page ${page + 1}/${totalPages}` });
    return embed;
  };

  const buildComponents = (page) => {
    if (totalPages <= 1) return [];
    const prevBtn = new ButtonBuilder()
      .setCustomId(`stat_nav_prev::stat=${statKey}::page=${page}`)
      .setEmoji("⬅️")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(page === 0);
    const nextBtn = new ButtonBuilder()
      .setCustomId(`stat_nav_next::stat=${statKey}::page=${page}`)
      .setEmoji("➡️")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(page >= totalPages - 1);
    return [new ActionRowBuilder().addComponents(prevBtn, nextBtn)];
  };

  let currentPage = 0;
  const replyMessage = await interaction.editReply({
    embeds: [buildEmbed(currentPage)],
    components: buildComponents(currentPage),
  });

  if (totalPages <= 1) return;

  const collector = replyMessage.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId.startsWith("stat_nav_prev::") ||
        i.customId.startsWith("stat_nav_next::")),
    componentType: ComponentType.Button,
    time: 300000,
  });

  collector.on("collect", async (i) => {
    try {
      const actionType = i.customId.split("::")[0];
      if (actionType === "stat_nav_prev" && currentPage > 0) {
        currentPage -= 1;
      } else if (
        actionType === "stat_nav_next" &&
        currentPage < totalPages - 1
      ) {
        currentPage += 1;
      } else {
        await i.deferUpdate().catch(() => {});
        return;
      }

      await i.update({
        embeds: [buildEmbed(currentPage)],
        components: buildComponents(currentPage),
      });
    } catch (error) {
      console.error("Error handling stat navigation:", error);
    }
  });
}

async function handleCategoryWiki(
  interaction,
  itemType,
  itemSubtype,
  categoryName
) {
  const allItems = configManager.getAllItems();

  // Filter items by type and subtype
  const categoryItems = Object.entries(allItems)
    .filter(([_key, item]) => {
      return item.type === itemType && item.subtype === itemSubtype;
    })
    .sort(([, a], [, b]) => {
      // Sort by rarity order (Common -> Legendary), then by name
      const rarityOrder = {
        COMMON: 1,
        UNCOMMON: 2,
        RARE: 3,
        EPIC: 4,
        LEGENDARY: 5,
        MYTHIC: 6,
      };
      const rarityDiff =
        (rarityOrder[a.rarity] || 0) - (rarityOrder[b.rarity] || 0);
      if (rarityDiff !== 0) return rarityDiff;
      return (a.name || "").localeCompare(b.name || "");
    });

  if (categoryItems.length === 0) {
    await interaction.editReply({
      content: `❌ No items found in category: ${categoryName}`,
      ephemeral: true,
    });
    return;
  }

  // Create embed with paginated results
  const itemsPerPage = 16;
  const totalPages = Math.ceil(categoryItems.length / itemsPerPage);
  let currentPage = 0;

  const buildEmbed = (page) => {
    const embed = new EmbedBuilder()
      .setTitle(`${categoryName} (${categoryItems.length} items)`)
      .setColor(EMBED_COLORS.BLUE);

    const startIndex = page * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, categoryItems.length);
    const pageItems = categoryItems.slice(startIndex, endIndex);

    let description = "";
    for (const [_key, item] of pageItems) {
      const rarityEmoji =
        {
          COMMON: "⚪",
          UNCOMMON: "🟢",
          RARE: "🔵",
          EPIC: "🟣",
          LEGENDARY: "🟠",
          MYTHIC: "🔴",
        }[item.rarity] || "⚪";

      description += `${rarityEmoji} ${item.emoji || "❓"} **${item.name}**\n`;
    }

    embed.setDescription(description);

    if (totalPages > 1) {
      embed.setFooter({ text: `Page ${page + 1} of ${totalPages}` });
    }

    return embed;
  };

  const buildComponents = (page) => {
    if (totalPages <= 1) return [];

    const row = new ActionRowBuilder();

    const prevButton = new ButtonBuilder()
      .setCustomId(`category_prev_${itemType}_${itemSubtype}`)
      .setLabel("Previous")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(page === 0);

    const nextButton = new ButtonBuilder()
      .setCustomId(`category_next_${itemType}_${itemSubtype}`)
      .setLabel("Next")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(page === totalPages - 1);

    row.addComponents(prevButton, nextButton);
    return [row];
  };

  const embed = buildEmbed(currentPage);
  const components = buildComponents(currentPage);

  const message = await interaction.editReply({
    embeds: [embed],
    components: components,
  });

  if (totalPages > 1) {
    const collector = message.createMessageComponentCollector({
      filter: (i) =>
        i.user.id === interaction.user.id &&
        (i.customId === `category_prev_${itemType}_${itemSubtype}` ||
          i.customId === `category_next_${itemType}_${itemSubtype}`),
      time: 300000,
    });

    collector.on("collect", async (i) => {
      if (i.customId === `category_prev_${itemType}_${itemSubtype}`) {
        currentPage = Math.max(0, currentPage - 1);
      } else if (i.customId === `category_next_${itemType}_${itemSubtype}`) {
        currentPage = Math.min(totalPages - 1, currentPage + 1);
      }

      const newEmbed = buildEmbed(currentPage);
      const newComponents = buildComponents(currentPage);

      await i.update({
        embeds: [newEmbed],
        components: newComponents,
      });
    });

    collector.on("end", () => {
      // Disable buttons when collector ends
      const disabledComponents = components.map((row) => {
        const newRow = new ActionRowBuilder();
        row.components.forEach((component) => {
          newRow.addComponents(component.setDisabled(true));
        });
        return newRow;
      });

      message.edit({ components: disabledComponents }).catch(() => {});
    });
  }
}

/**
 * Handles wiki display for Bits system
 * @param {Object} interaction - Discord interaction
 */
async function handleBitsWiki(interaction) {
  const { BITS_EMOJI } = require("../gameConfig");
  const {
    BITS_BASE_AMOUNT,
    BITS_COLLECTION_INTERVAL_MS,
  } = require("../utils/boosterCookieManager");

  // convert collection interval to readable format
  const collectionIntervalMinutes = BITS_COLLECTION_INTERVAL_MS / (1000 * 60);

  const embed = new EmbedBuilder()
    .setTitle(`${BITS_EMOJI} Bits`)
    .setColor(EMBED_COLORS.BLUE)
    .addFields(
      {
        name: "**Earning Bits**",
        value: `Consume **Booster Cookies** to earn bits\nEach cookie provides ${BITS_BASE_AMOUNT.toLocaleString()} Available Bits\nYour Bits Multiplier increases this amount (Default 1.0)`,
        inline: false,
      },
      {
        name: "**Gaining Bits**",
        value: `Bits generate automatically while cookies are active\nyou automatically gain 250 bits every ${collectionIntervalMinutes} minutes.\nBits generate from your Available Bits.\nIf your **Bits Multiplier** changes your\n **Available Bits** will be auto recalculated\nmeaning that you retroactively will gain **Available Bits**\neven on cookies you already consumed`,
        inline: false,
      },
      {
        name: "",
        value: `**Check Available Bits**: \`/settings\``,
        inline: false,
      }
    );

  await interaction.editReply({ embeds: [embed] });
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("wiki")
    .setDescription(
      "Look up any item, minion, pet, mob, or reforges in the game and see all info!"
    )
    .addStringOption((option) =>
      option
        .setName("item_key")
        .setDescription(
          "The item/mob key to look up or 'reforges' to see all reforges"
        )
        .setRequired(true)
        .setAutocomplete(true)
    ),
  async execute(interaction) {
    try {
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferReply();
      }
      const rawItemKey = interaction.options.getString("item_key");

      if (!rawItemKey) {
        console.error("[Wiki] No item_key provided in options");
        return interaction.editReply({
          content: "❌ No item key provided.",
          ephemeral: true,
        });
      }

      const selectedItemKey = rawItemKey.toUpperCase();
      const statKeyCandidate = selectedItemKey.replace(/\s+/g, "_");
      if (STATS[statKeyCandidate]) {
        const statDef = STATS[statKeyCandidate];
        await handleStatWiki(
          interaction,
          statKeyCandidate,
          statDef,
          configManager.getAllItems()
        );
        return;
      }

      if (selectedItemKey === "REFORGES") {
        await handleReforgesWiki(interaction, null, "Legendary");
        return;
      }

      // Handle category searches
      const categoryHandlers = {
        SWORDS: () =>
          handleCategoryWiki(interaction, "WEAPON", "SWORD", "Swords"),
        PICKAXES: () =>
          handleCategoryWiki(interaction, "TOOL", "PICKAXE", "Pickaxes"),
        SHOVELS: () =>
          handleCategoryWiki(interaction, "TOOL", "SHOVEL", "Shovels"),
        AXES_TOOLS: () =>
          handleCategoryWiki(interaction, "TOOL", "AXE", "Axes (Tools)"),
        AXES_WEAPONS: () =>
          handleCategoryWiki(interaction, "WEAPON", "AXE", "Axes (Weapons)"),
      };

      if (categoryHandlers[selectedItemKey]) {
        await categoryHandlers[selectedItemKey]();
        return;
      }

      // Handle special wiki topics
      if (selectedItemKey === "BITS") {
        await handleBitsWiki(interaction);
        return;
      }

      // Check if it's a mob first (try both uppercase and lowercase)
      const allMobs = configManager.getAllMobs();
      const mobData =
        allMobs[selectedItemKey] || allMobs[selectedItemKey.toLowerCase()];
      if (mobData) {
        await handleMobWiki(interaction, mobData, selectedItemKey);
        return;
      }

      const allItems = configManager.getAllItems();
      const itemData = allItems[selectedItemKey];

      if (!itemData) {
        return interaction.editReply({
          content: `❌ Unknown item/mob key: \`${selectedItemKey}\``,
          ephemeral: true,
        });
      }

      const userId = interaction.user.id;

      // Get player data for requirement checking
      let playerData = null;
      try {
        const { getPlayerData } = require("../utils/playerDataManager");
        playerData = await getPlayerData(userId);
      } catch (error) {
        // If player data can't be loaded, continue without it
        console.log(
          `[Wiki Command] Could not load player data for ${userId}, continuing without requirement checking:`,
          error.message
        );
      }

      if (
        itemData.type === "MINION" &&
        Array.isArray(itemData.tiers) &&
        itemData.tiers.filter((t) => t).length > 1
      ) {
        await handleMinionWiki(
          interaction,
          itemData,
          selectedItemKey,
          allItems,
          userId
        );
        return;
      }

      if (itemData.type === "PET" && itemData.rarities) {
        await handlePetWiki(
          interaction,
          itemData,
          selectedItemKey,
          allItems,
          userId
        );
        return;
      }

      const collectionData = findCollectionForItem(selectedItemKey);
      if (collectionData) {
        try {
          await handleGeneralItemWiki(
            interaction,
            itemData,
            selectedItemKey,
            allItems,
            playerData
          );
        } catch (error) {
          console.error(
            "[Wiki Command] Error fetching collection data:",
            error
          );
          await handleGeneralItemWiki(
            interaction,
            itemData,
            selectedItemKey,
            allItems,
            playerData
          );
        }
      } else {
        await handleGeneralItemWiki(
          interaction,
          itemData,
          selectedItemKey,
          allItems,
          playerData
        );
      }
    } catch (_error) {
      console.error("[Wiki Command] Error executing command:", _error);
      console.error("[Wiki Command] Error stack:", _error.stack);
      console.error(
        "[Wiki Command] Interaction state - deferred:",
        interaction.deferred,
        "replied:",
        interaction.replied
      );
      try {
        const itemKey = interaction.options.getString("item_key");
        console.error("[Wiki Command] Retrieved item key:", itemKey);
        const allItems = configManager.getAllItems();
        const itemData = allItems[itemKey?.toUpperCase?.()];
        console.error("[Wiki Command] itemData:", itemData);
      } catch (_logError) {
        console.error("[Wiki Command] Error logging itemData:", _logError);
      }
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "An unexpected error occurred while trying to view the item. Please report this.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      } else {
        await interaction
          .editReply({
            content:
              "An unexpected error occurred while trying to view the item. Please report this.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    }
  },
  async autocomplete(interaction) {
    try {
      const focusedValue = interaction.options.getFocused().toLowerCase();
      const MAX_CHOICES = 25;

      // Category options
      const categoryOptions = [
        { name: "Bits", value: "BITS" },
        { name: "Reforges", value: "REFORGES" },
        { name: "Swords", value: "SWORDS" },
        { name: "Pickaxes", value: "PICKAXES" },
        { name: "Shovels", value: "SHOVELS" },
        { name: "Axes (Tools)", value: "AXES_TOOLS" },
        { name: "Axes (Weapons)", value: "AXES_WEAPONS" },
      ];

      const matchingCategories = categoryOptions.filter(
        (option) =>
          option.name.toLowerCase().includes(focusedValue) ||
          option.value.toLowerCase().includes(focusedValue)
      );

      if (matchingCategories.length > 0 && focusedValue.length > 0) {
        // If exact match for categories, prioritize them
        if (
          matchingCategories.some(
            (cat) =>
              cat.name.toLowerCase() === focusedValue ||
              cat.value.toLowerCase() === focusedValue
          )
        ) {
          await interaction.respond(matchingCategories);
          return;
        }

        const allItems = configManager.getAllItems();
        const itemFiltered = Object.entries(allItems)
          .filter(([key, item]) => {
            if (/\(Tier \d+\)/i.test(item.name) || /_TIER_\d+$/i.test(key))
              return false;

            if (item.type === "PET") {
              const isBasePet = !item.resultPetKey && !!item.rarities;
              return (
                isBasePet &&
                item.name &&
                (item.name.toLowerCase().includes(focusedValue) ||
                  key.toLowerCase().includes(focusedValue))
              );
            }

            return (
              item.name &&
              (item.name.toLowerCase().includes(focusedValue) ||
                key.toLowerCase().includes(focusedValue))
            );
          })
          .map(([key, item]) => ({ name: item.name, value: key }));

        // Add mobs to autocomplete
        const mobFiltered = Object.entries(configManager.getAllMobs())
          .filter(([key, mob]) => {
            return (
              mob.name &&
              (mob.name.toLowerCase().includes(focusedValue) ||
                key.toLowerCase().includes(focusedValue))
            );
          })
          .map(([key, mob]) => ({ name: mob.name, value: key }));

        const statFiltered = Object.entries(STATS)
          .filter(([_, stat]) => stat.name.toLowerCase().includes(focusedValue))
          .map(([_, stat]) => ({ name: stat.name, value: stat.name }));
        const availableCategories = matchingCategories.slice(0, 5);
        const remainingSlots = Math.max(
          0,
          MAX_CHOICES - availableCategories.length
        );
        const allFiltered = [
          ...itemFiltered,
          ...mobFiltered,
          ...statFiltered,
        ].slice(0, remainingSlots);
        await interaction.respond([...availableCategories, ...allFiltered]);
      } else {
        const allItems = configManager.getAllItems();
        const itemFiltered = Object.entries(allItems)
          .filter(([key, item]) => {
            if (/\(Tier \d+\)/i.test(item.name) || /_TIER_\d+$/i.test(key))
              return false;

            if (item.type === "PET") {
              const isBasePet = !item.resultPetKey && !!item.rarities;
              return (
                isBasePet &&
                item.name &&
                (item.name.toLowerCase().includes(focusedValue) ||
                  key.toLowerCase().includes(focusedValue))
              );
            }

            return (
              item.name &&
              (item.name.toLowerCase().includes(focusedValue) ||
                key.toLowerCase().includes(focusedValue))
            );
          })
          .map(([key, item]) => ({ name: item.name, value: key }));

        // Add mobs to autocomplete
        const mobFiltered = Object.entries(configManager.getAllMobs())
          .filter(([key, mob]) => {
            return (
              mob.name &&
              (mob.name.toLowerCase().includes(focusedValue) ||
                key.toLowerCase().includes(focusedValue))
            );
          })
          .map(([key, mob]) => ({ name: mob.name, value: key }));

        const statFiltered = Object.entries(STATS)
          .filter(([_, stat]) => stat.name.toLowerCase().includes(focusedValue))
          .map(([_, stat]) => ({ name: stat.name, value: stat.name }));
        const availableCategories = matchingCategories.slice(0, 5);
        const remainingSlots = Math.max(
          0,
          MAX_CHOICES - availableCategories.length
        );
        const allFiltered = [
          ...itemFiltered,
          ...mobFiltered,
          ...statFiltered,
        ].slice(0, remainingSlots);
        await interaction.respond([...availableCategories, ...allFiltered]);
      }
    } catch (_error) {
      console.error("[Wiki Autocomplete] Error:", _error);
      await interaction.respond([]);
    }
  },
  handlePetNavigation: async function (interaction) {
    return module.exports.execute(interaction);
  },
  handlePetLevelModalSubmit: handlePetLevelModalSubmit,

  handleReforgeSelection: async function (interaction) {
    await interaction.deferUpdate();

    if (interaction.customId === "reforge_back") {
      return handleReforgesWiki(interaction);
    }

    if (interaction.customId === "reforge_category_select") {
      const selectedCategory = interaction.values[0];
      return handleReforgesWiki(interaction, selectedCategory, "Legendary");
    }

    if (interaction.customId.startsWith("reforge_rarity_")) {
      const parts = interaction.customId.split("_");
      const selectedCategory = parts[2];
      const selectedRarity = parts[3];
      return handleReforgesWiki(interaction, selectedCategory, selectedRarity);
    }
  },

  handleRaritySelection: async function (interaction) {
    return module.exports.handleReforgeSelection(interaction);
  },
};
