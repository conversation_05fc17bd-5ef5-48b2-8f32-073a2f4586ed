const { dbRun, dbAll } = require("../utils/dbUtils");

async function up() {
  console.log(
    "[Migration 044] Checking if last_coin_generation column exists...",
  );

  try {
    // Check if column already exists
    const columns = await dbAll("PRAGMA table_info(players)");
    const columnExists = columns.some(
      (col) => col.name === "last_coin_generation",
    );

    if (columnExists) {
      console.log(
        "[Migration 044] last_coin_generation column already exists, skipping...",
      );
      return;
    }

    console.log(
      "[Migration 044] Adding last_coin_generation column to players table...",
    );
    await dbRun(`
      ALTER TABLE players 
      ADD COLUMN last_coin_generation INTEGER DEFAULT NULL
    `);

    console.log(
      "[Migration 044] Successfully added last_coin_generation column",
    );
  } catch (error) {
    console.error(
      "[Migration 044] Error adding last_coin_generation column:",
      error,
    );
    throw error;
  }
}

async function down() {
  console.log(
    "[Migration 044] Removing last_coin_generation column from players table...",
  );

  try {
    // SQLite doesn't support DROP COLUMN directly, so we'd need to recreate the table
    // For now, we'll just log that this migration cannot be easily reversed
    console.log(
      "[Migration 044] Note: SQLite does not support DROP COLUMN. Manual intervention required to reverse.",
    );
  } catch (error) {
    console.error("[Migration 044] Error in down migration:", error);
    throw error;
  }
}

module.exports = { up, down };
