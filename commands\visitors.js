const {
  <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er,
  EmbedBuilder,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const { getPlayerData, savePlayerData } = require("../utils/playerDataManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const { updateInventoryAtomically } = require("../utils/inventory");
const { EMBED_COLORS, COPPER } = require("../gameConfig");
const {
  GARDEN_VISITORS,
  getActiveVisitors,
  spawnVisitor,
  VISITOR_SPAWN_INTERVAL,
  FARMING_TIME_REDUCTION,
  areVisitorsAtMaxCapacity,
} = require("../utils/gardenVisitors");
const configManager = require("../utils/configManager");
const { dbRunQueued } = require("../utils/dbUtils");

// removed unused activeCollectors map (no longer needed)

module.exports = {
  data: new SlashCommandBuilder()
    .setName("visitors")
    .setDescription("View and interact with Garden Visitors"),

  async execute(interaction) {
    const userId = interaction.user.id;
    let character;

    try {
      character = await getPlayerData(userId);
    } catch (error) {
      console.error(
        `[Visitors Command] Error fetching player data for ${userId}:`,
        error
      );
      return interaction.reply({
        content: "Error fetching your character data. Please try again.",
        ephemeral: true,
      });
    }

    if (!character) {
      return interaction.reply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
        ephemeral: true,
      });
    }

    if (!checkRankPermission(character, "MEMBER")) {
      return interaction.reply({
        content: "You don't have permission to use this command.",
        ephemeral: true,
      });
    }

    // Restrict usage to the Garden region (mirrors /desk behavior)
    if (character.current_region !== "garden") {
      return interaction.reply({
        content: "You can only use this while in the Garden!",
        ephemeral: true,
      });
    }
    // Initialize garden_visitor_timer if undefined
    if (character.garden_visitor_timer === undefined) {
      try {
        await savePlayerData(
          userId,
          {
            garden_visitor_timer: 0,
          },
          ["garden_visitor_timer"]
        );
        character.garden_visitor_timer = 0;
      } catch (error) {
        console.error(
          `[Visitors] Failed to initialize timer for ${userId}:`,
          error
        );
        return interaction.reply({
          content:
            "❌ Error initializing Garden Visitors system. Please try again.",
          ephemeral: true,
        });
      }
    }

    await showVisitorsMenu(interaction, character);
  },
};

/**
 * Show the main visitors menu
 */
async function showVisitorsMenu(interaction, character, isEdit = false) {
  const userId = character.discordId || character.discord_id;
  const activeVisitors = await getActiveVisitors(userId);

  // Calculate next spawn time
  let nextSpawnTime = character.garden_visitor_timer || 0;

  // If no timer is set or timer is in the past, set a new timer
  if (nextSpawnTime <= Date.now()) {
    nextSpawnTime = Date.now() + VISITOR_SPAWN_INTERVAL;

    // Update the timer in the database
    try {
      await savePlayerData(
        userId,
        {
          garden_visitor_timer: nextSpawnTime,
        },
        ["garden_visitor_timer"]
      );
    } catch (error) {
      console.log(
        `[Visitors] Could not update timer for ${userId}:`,
        error.message
      );
      // Continue without updating timer - use calculated value for display
    }
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PASTEL_GREEN)
    .setTitle("<:garden:1394656922623410237> Garden Visitors")
    .setFooter({
      text: `Farming reduces visitor spawn time by ${FARMING_TIME_REDUCTION / 1000}s per action!`,
    });

  const components = [];

  if (activeVisitors.length === 0) {
    embed.setDescription(
      "No visitors are currently in your garden.\n\n" +
        `Next visitor arrives: <t:${Math.floor(nextSpawnTime / 1000)}:R>\n\n`
      //"Farm crops in your Garden to reduce the wait time!"
    );
  } else {
    const atMaxCapacity = await areVisitorsAtMaxCapacity(userId);

    // Build visitor list with their offers
    const allItems = configManager.getAllItems();
    const visitorList = [];

    for (const visitor of activeVisitors) {
      const visitorData = GARDEN_VISITORS[visitor.key];
      const requestItem = visitor.requestItems[0]; // Get first (and usually only) requested item
      const itemData = allItems[requestItem.itemKey];
      const itemEmoji = itemData?.emoji || "❓";

      visitorList.push(
        `${visitorData.emoji} **${visitorData.name}** wants ${itemEmoji} x${requestItem.amount.toLocaleString()}`
      );
    }

    let description =
      `You have **${activeVisitors.length}** visitor${activeVisitors.length === 1 ? "" : "s"} waiting!\n\n` +
      `${visitorList.join("\n")}`;
    if (!atMaxCapacity) {
      description += `\n\nNext visitor arrives: <t:${Math.floor(nextSpawnTime / 1000)}:R>\n\n`;
    } else {
      description += `\n\n`;
    }
    embed.setDescription(description);

    // Create select menu for visitors
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("select_visitor")
      .setPlaceholder("Choose a visitor to speak with...")
      .addOptions(
        activeVisitors.map((visitor) => {
          const visitorData = GARDEN_VISITORS[visitor.key];
          return {
            label: `${visitorData.name} (${visitor.rarity})`,
            description: `Click to view ${visitorData.name}'s offer`,
            value: `visitor_${visitor.id}`,
            emoji: visitorData.emoji,
          };
        })
      );

    components.push(new ActionRowBuilder().addComponents(selectMenu));
  }

  // Add refresh button
  const buttons = [];

  const refreshButton = new ButtonBuilder()
    .setCustomId("refresh_visitors")
    .setLabel("Refresh")
    .setStyle(ButtonStyle.Secondary);
  buttons.push(refreshButton);

  const closeButton = new ButtonBuilder()
    .setCustomId("close_visitors")
    .setLabel("Close")
    .setStyle(ButtonStyle.Secondary);

  buttons.push(closeButton);
  components.push(new ActionRowBuilder().addComponents(...buttons));

  const messageOptions = { embeds: [embed], components };

  if (isEdit) {
    await interaction.editReply(messageOptions);
  } else {
    await interaction.reply(messageOptions);
  }

  // No per-command collector:use centralized persistent handler
}

/**
 * Handle refresh visitors button
 */
async function handleRefreshVisitors(interaction, character) {
  try {
    const userId = character.discordId || character.discord_id;
    console.log(`[Visitors] Refresh button clicked by ${userId}`);
    await interaction.deferUpdate();

    // Try to spawn a new visitor
    console.log(`[Visitors] Attempting to spawn visitor for ${userId}`);
    const spawnResult = await spawnVisitor(userId);
    console.log(`[Visitors] Spawn result:`, spawnResult);

    // Refresh the menu
    const updatedCharacter = await getPlayerData(userId);
    await showVisitorsMenu(interaction, updatedCharacter, true);
  } catch (error) {
    console.error("[Visitors] Error in handleRefreshVisitors:", error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "An error occurred while refreshing visitors.",
        ephemeral: true,
      });
    } else {
      await interaction.followUp({
        content: "An error occurred while refreshing visitors.",
        ephemeral: true,
      });
    }
  }
}

/**
 * Show individual visitor offer
 */
async function showVisitorOffer(interaction, character, visitorId) {
  const userId = character.discordId || character.discord_id;
  const activeVisitors = await getActiveVisitors(userId);
  const visitor = activeVisitors.find((v) => v.id === visitorId);

  if (!visitor) {
    await interaction.update({
      content: "This visitor is no longer available.",
      embeds: [],
      components: [],
    });
    return;
  }

  const visitorData = GARDEN_VISITORS[visitor.key];
  const allItems = configManager.getAllItems();

  // Check player inventory for required items
  let playerInventory = {};
  try {
    if (typeof character.inventory === "string") {
      playerInventory = JSON.parse(character.inventory || "{}");
    } else if (
      typeof character.inventory === "object" &&
      character.inventory !== null
    ) {
      // Handle new inventory structure from getPlayerData
      if (character.inventory.items) {
        playerInventory = character.inventory.items;
      } else {
        playerInventory = character.inventory;
      }
    } else {
      playerInventory = {};
    }
  } catch (error) {
    console.error(`[Visitors] Error parsing inventory for ${userId}:`, error);
    console.error(`[Visitors] Inventory value:`, character.inventory);
    console.error(`[Visitors] Inventory type:`, typeof character.inventory);
    playerInventory = {};
  }
  const requestText = [];
  let canComplete = true;

  for (const request of visitor.requestItems) {
    const item = allItems[request.itemKey];
    const playerAmount = playerInventory[request.itemKey] || 0;
    const hasEnough = playerAmount >= request.amount;

    if (!hasEnough) canComplete = false;

    const statusEmoji = hasEnough ? "✅" : "❌";
    const itemName = item?.name || request.itemKey;
    const itemEmoji = item?.emoji || "❓";

    requestText.push(
      `${statusEmoji} ${itemEmoji} ${itemName} x ${request.amount.toLocaleString()}${!hasEnough ? ` (have ${playerAmount.toLocaleString()})` : ""}`
    );
  }

  const rewardsText = [
    `<:garden:1394656922623410237> Garden XP: +${visitor.rewards.garden_xp}`,
    `<:skill_farming:1367753361511682128> Farming XP: +${visitor.rewards.farmingXp}`,
    `${COPPER.emoji} Copper: +${visitor.rewards.copper}`,
  ];

  // Add bonus rewards if any
  if (visitor.rewards.bonusRewards && visitor.rewards.bonusRewards.length > 0) {
    for (const bonus of visitor.rewards.bonusRewards) {
      const bonusItem = allItems[bonus.itemKey];
      rewardsText.push(
        `${bonusItem?.emoji || "❓"} ${bonusItem?.name || bonus.itemKey}: +${bonus.amount}`
      );
    }
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${visitorData.emoji} ${visitorData.name}'s Offer`)
    .setDescription(
      `**${visitor.rarity}** Visitor\n\n` +
        `"${visitorData.dialogue}"\n\n` +
        `**┌─── REQUESTING ───┐**\n${requestText.join("\n")}\n\n` +
        `**┌─── OFFERING ───┐**\n${rewardsText.join("\n")}`
    );

  const acceptButton = new ButtonBuilder()
    .setCustomId(`accept_offer_${visitorId}`)
    .setLabel("✅ Accept Offer")
    .setStyle(ButtonStyle.Success)
    .setDisabled(!canComplete);

  const declineButton = new ButtonBuilder()
    .setCustomId(`decline_offer_${visitorId}`)
    .setLabel("❌ Decline")
    .setStyle(ButtonStyle.Danger);

  const backButton = new ButtonBuilder()
    .setCustomId("back_to_visitors")
    .setLabel("⬅️ Back to Visitors")
    .setStyle(ButtonStyle.Secondary);

  const components = [
    new ActionRowBuilder().addComponents(
      acceptButton,
      declineButton,
      backButton
    ),
  ];

  // Check if interaction has been replied to and use appropriate method
  if (interaction.replied || interaction.deferred) {
    await interaction.editReply({ embeds: [embed], components });
  } else {
    await interaction.update({ embeds: [embed], components });
  }
}

/**
 * Handle accepting a visitor offer
 */
async function handleAcceptOffer(interaction, character, visitorId) {
  await interaction.deferUpdate();

  const userId = character.discordId || character.discord_id;
  const activeVisitors = await getActiveVisitors(userId);
  const visitor = activeVisitors.find((v) => v.id === visitorId);

  if (!visitor) {
    await interaction.followUp({
      content: "This visitor is no longer available.",
      ephemeral: true,
    });
    return;
  }

  // Check if player has required items with improved error handling
  let playerInventory = {};
  try {
    if (typeof character.inventory === "string") {
      playerInventory = JSON.parse(character.inventory || "{}");
    } else if (
      typeof character.inventory === "object" &&
      character.inventory !== null
    ) {
      // Handle new inventory structure from getPlayerData
      if (character.inventory.items) {
        playerInventory = character.inventory.items;
      } else {
        playerInventory = character.inventory;
      }
    } else {
      playerInventory = {};
    }
  } catch (error) {
    console.error(
      `[Visitors] Error parsing inventory in handleAcceptOffer for ${userId}:`,
      error
    );
    console.error(`[Visitors] Inventory value:`, character.inventory);
    console.error(`[Visitors] Inventory type:`, typeof character.inventory);

    // Try to recover by refreshing player data
    try {
      const refreshedCharacter = await getPlayerData(userId);
      if (refreshedCharacter && refreshedCharacter.inventory) {
        if (typeof refreshedCharacter.inventory === "object") {
          playerInventory =
            refreshedCharacter.inventory.items || refreshedCharacter.inventory;
        }
      }
    } catch (recoveryError) {
      console.error(
        `[Visitors] Failed to recover inventory data for ${userId}:`,
        recoveryError
      );
      playerInventory = {};
    }
  }
  const allItems = configManager.getAllItems();

  for (const request of visitor.requestItems) {
    const playerAmount = playerInventory[request.itemKey] || 0;
    if (playerAmount < request.amount) {
      const item = allItems[request.itemKey];
      await interaction.followUp({
        content: `❌ **Insufficient Items**\n\nYou don't have enough ${item?.name || request.itemKey}.\nNeed: ${request.amount.toLocaleString()}, Have: ${playerAmount.toLocaleString()}`,
        ephemeral: true,
      });
      return;
    }
  }

  // Remove items from inventory using atomic inventory update
  const itemsToRemove = [];

  for (const request of visitor.requestItems) {
    itemsToRemove.push({
      itemKey: request.itemKey,
      amount: -request.amount, // Negative amount to remove
    });
  }

  // Update inventory atomically
  await updateInventoryAtomically(userId, 0, itemsToRemove);

  // Add rewards
  const newGardenXp = (character.garden_xp || 0) + visitor.rewards.garden_xp;
  const newCopper = (character.copper || 0) + visitor.rewards.copper;

  // Add farming XP to skills system (with automatic notifications)
  const { addSkillExp } = require("../utils/skillExpManager");

  if (visitor.rewards.farmingXp > 0) {
    await addSkillExp(
      userId,
      "farming",
      visitor.rewards.farmingXp,
      interaction
    );
  }

  // Handle bonus rewards (items)
  if (visitor.rewards.bonusRewards && visitor.rewards.bonusRewards.length > 0) {
    const bonusItems = visitor.rewards.bonusRewards.map((bonus) => ({
      itemKey: bonus.itemKey,
      amount: bonus.amount,
    }));
    await updateInventoryAtomically(userId, 0, bonusItems);
  }

  // Update player data
  const newOffersAccepted = (character.visitor_offers_accepted || 0) + 1;

  await savePlayerData(
    userId,
    {
      garden_xp: newGardenXp,
      copper: newCopper,
      visitor_offers_accepted: newOffersAccepted,
    },
    ["garden_xp", "copper", "visitor_offers_accepted"]
  );

  // Track unique visitor served
  let uniqueServed = [];
  try {
    if (typeof character.visitor_unique_served === "string") {
      uniqueServed = JSON.parse(character.visitor_unique_served || "[]");
    } else if (Array.isArray(character.visitor_unique_served)) {
      uniqueServed = character.visitor_unique_served;
    } else {
      uniqueServed = [];
    }

    // Ensure uniqueServed is actually an array
    if (!Array.isArray(uniqueServed)) {
      uniqueServed = [];
    }
  } catch (error) {
    console.error(
      `[Visitors] Error parsing visitor_unique_served for ${userId}:`,
      error
    );
    console.error(
      `[Visitors] visitor_unique_served value:`,
      character.visitor_unique_served
    );
    console.error(
      `[Visitors] visitor_unique_served type:`,
      typeof character.visitor_unique_served
    );
    uniqueServed = [];

    // Reset corrupted data to valid state
    await savePlayerData(
      userId,
      {
        visitor_unique_served: [],
      },
      ["visitor_unique_served"]
    );
  }
  let wasNewUniqueVisitor = false;
  if (!uniqueServed.includes(visitor.key)) {
    uniqueServed.push(visitor.key);
    wasNewUniqueVisitor = true;
    await savePlayerData(
      userId,
      {
        visitor_unique_served: uniqueServed,
      },
      ["visitor_unique_served"]
    );
  }

  // Track per-NPC completion counts (for future enhanced tracking)
  let completionCounts = {};
  try {
    if (typeof character.visitor_completion_counts === "string") {
      completionCounts = JSON.parse(
        character.visitor_completion_counts || "{}"
      );
    } else if (
      character.visitor_completion_counts &&
      typeof character.visitor_completion_counts === "object"
    ) {
      completionCounts = character.visitor_completion_counts;
    } else {
      completionCounts = {};
    }
  } catch {
    completionCounts = {};
  }

  // Increment the count for this specific visitor
  completionCounts[visitor.key] = (completionCounts[visitor.key] || 0) + 1;

  // Save the updated completion counts (let saver serialize JSON)
  await savePlayerData(
    userId,
    {
      visitor_completion_counts: completionCounts,
    },
    ["visitor_completion_counts"]
  );

  // Check for milestone rewards (notifications handled automatically by centralized system)
  const {
    checkOffersAcceptedMilestones,
    checkUniqueVisitorsMilestones,
  } = require("../utils/visitorMilestones");
  const previousOffersAccepted = character.visitor_offers_accepted || 0;
  const previousUniqueCount = wasNewUniqueVisitor
    ? uniqueServed.length - 1
    : uniqueServed.length;
  const offersMilestones = await checkOffersAcceptedMilestones(
    userId,
    newOffersAccepted,
    previousOffersAccepted,
    interaction
  );
  const uniqueMilestones = wasNewUniqueVisitor
    ? await checkUniqueVisitorsMilestones(
        userId,
        uniqueServed,
        previousUniqueCount,
        interaction
      )
    : [];

  // Remove visitor from active list
  await dbRunQueued("DELETE FROM active_garden_visitors WHERE id = ?", [
    visitorId,
  ]);

  // Show success message
  const visitorData = GARDEN_VISITORS[visitor.key];
  const rewardsReceived = [
    `<:garden:1394656922623410237> Garden XP: +${visitor.rewards.garden_xp}`,
    `<:skill_farming:1367753361511682128> Farming XP: +${visitor.rewards.farmingXp}`,
    `${COPPER.emoji} Copper: +${visitor.rewards.copper}`,
  ];

  // Add bonus rewards to display
  if (visitor.rewards.bonusRewards && visitor.rewards.bonusRewards.length > 0) {
    for (const bonus of visitor.rewards.bonusRewards) {
      const bonusItem = allItems[bonus.itemKey];
      rewardsReceived.push(
        `${bonusItem?.emoji || "❓"} ${bonusItem?.name || bonus.itemKey}: +${bonus.amount}`
      );
    }
  }

  // Add milestone rewards to display
  const allMilestones = [...offersMilestones, ...uniqueMilestones];
  if (allMilestones.length > 0) {
    rewardsReceived.push(""); // Empty line
    rewardsReceived.push("**🎉 MILESTONE REWARDS:**");
    for (const milestone of allMilestones) {
      const milestoneType =
        milestone.type === "offers_accepted"
          ? "Offers Accepted"
          : "Unique Visitors";
      rewardsReceived.push(
        `<:milestone:1396243017341472798> ${milestoneType} Milestone ${milestone.milestone}!`
      );
      rewardsReceived.push(
        `  <:garden:1394656922623410237> Garden XP: +${milestone.garden_xp}`
      );
      rewardsReceived.push(
        `  <:skill_farming:1367753361511682128> Farming XP: +${milestone.farmingXp}`
      );
    }
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GREEN)
    .setTitle("✅ Offer Completed!")
    .setDescription(
      `${visitorData.emoji} **${visitorData.name}**: "Thank you for your help!"\n\n` +
        `**┌─── REWARDS RECEIVED ───┐**\n${rewardsReceived.join("\n")}`
    );

  const backButton = new ButtonBuilder()
    .setCustomId("back_to_visitors")
    .setLabel("⬅️ Back to Visitors")
    .setStyle(ButtonStyle.Secondary);

  await interaction.editReply({
    embeds: [embed],
    components: [new ActionRowBuilder().addComponents(backButton)],
  });

  // Level up notifications are now handled automatically by the centralized skill notification system
}

/**
 * Handle declining a visitor offer
 */
async function handleDeclineOffer(interaction, character, visitorId) {
  await interaction.deferUpdate();

  const userId = character.discordId || character.discord_id;

  // Remove visitor from active list
  await dbRunQueued("DELETE FROM active_garden_visitors WHERE id = ?", [
    visitorId,
  ]);

  // Go back to visitors menu
  const updatedCharacter = await getPlayerData(userId);
  await showVisitorsMenu(interaction, updatedCharacter, true);

  //await interaction.followUp({
  //    content: "Visitor offer declined and removed.",
  //    ephemeral: true
  //});
}

// Persistent interaction handler for visitors menu
module.exports.handleInteraction = async function (interaction) {
  const customId = interaction.customId;
  const userId = interaction.user.id;
  let character;
  try {
    character = await getPlayerData(userId);
  } catch (error) {
    console.error(
      "[Visitors] Error fetching character in handleInteraction:",
      error
    );
    return interaction.reply({
      content: "Error loading your data. Please try again.",
      ephemeral: true,
    });
  }
  if (!character) {
    return interaction.reply({
      content: "Character not found.",
      ephemeral: true,
    });
  }
  // Enforce Garden-only access for any persistent component interactions
  if (character.current_region !== "garden") {
    return interaction.reply({
      content: "You must be in the Garden to use Garden Visitors.",
      ephemeral: true,
    });
  }
  if (customId === "refresh_visitors") {
    return handleRefreshVisitors(interaction, character);
  } else if (customId === "close_visitors") {
    // Send a styled embed for closing the visitors menu, similar to enchant close embed
    const closeEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PASTEL_GREEN)
      .setTitle("<:garden:1394656922623410237> Garden Visitors")
      .setDescription("Visitors menu closed.");
    return interaction.update({ embeds: [closeEmbed], components: [] });
  } else if (customId === "select_visitor") {
    await interaction.deferUpdate();
    const visitorId = parseInt(
      interaction.values[0].replace("visitor_", ""),
      10
    );
    return showVisitorOffer(interaction, character, visitorId);
  } else if (customId.startsWith("accept_offer_")) {
    const visitorId = parseInt(customId.replace("accept_offer_", ""), 10);
    return handleAcceptOffer(interaction, character, visitorId);
  } else if (customId.startsWith("decline_offer_")) {
    const visitorId = parseInt(customId.replace("decline_offer_", ""), 10);
    return handleDeclineOffer(interaction, character, visitorId);
  } else if (customId === "back_to_visitors") {
    await interaction.deferUpdate();
    const updatedCharacter = await getPlayerData(userId);
    return showVisitorsMenu(interaction, updatedCharacter, true);
  }
  // Unknown interaction for this command
};
