const Stripe = require("stripe");
const { updateGemsAtomically } = require("./playerDataManager");
const { EmbedBuilder } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");

// initialize stripe with secret key from environment (only if key is provided)
const stripe = process.env.STRIPE_SECRET_KEY
  ? new Stripe(process.env.STRIPE_SECRET_KEY)
  : null;

// gem package configurations
const GEM_PACKAGES = {
  starter: {
    id: "starter",
    name: "675 Gems",
    gems: 675,
    price: 499, // price in cents ($4.99)
    emoji: "💎",
  },
  value: {
    id: "value",
    name: "1,400 Gems",
    gems: 1400,
    price: 999, // price in cents ($9.99)
    emoji: "💎",
  },
  premium: {
    id: "premium",
    name: "3,600 Gems",
    gems: 3600,
    price: 2499, // price in cents ($24.99)
    emoji: "💎",
  },
  ultimate: {
    id: "ultimate",
    name: "7,300 Gems",
    gems: 7300,
    price: 4999, // price in cents ($49.99)
    emoji: "💎",
  },
  mega: {
    id: "mega",
    name: "16,400 Gems",
    gems: 16400,
    price: 9999, // price in cents ($99.99)
    emoji: "💎",
  },
};

/**
 * Creates a Stripe checkout session for gem purchase
 * @param {string} discordId - The Discord user ID
 * @param {string} packageId - The gem package ID (starter, value, premium, ultimate)
 * @param {string} username - The Discord username for reference
 * @param {string} channelId - The Discord channel ID where the transaction started
 * @param {string} guildId - The Discord guild ID (optional, for server channels)
 * @returns {Promise<string>} The checkout session URL
 */
async function createGemCheckoutSession(
  discordId,
  packageId,
  username,
  channelId,
  guildId = null
) {
  try {
    if (!process.env.STRIPE_SECRET_KEY || !stripe) {
      throw new Error("Stripe secret key not configured");
    }

    const gemPackage = GEM_PACKAGES[packageId];
    if (!gemPackage) {
      throw new Error(`Invalid gem package: ${packageId}`);
    }

    // construct the Discord channel URL based on whether it's a DM or server channel
    const baseUrl = guildId
      ? `https://discord.com/channels/${guildId}/${channelId}`
      : `https://discord.com/channels/@me/${channelId}`;

    // create checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `${gemPackage.emoji} ${gemPackage.name}`,
              description: `${gemPackage.gems} Gems for Disblock`,
            },
            unit_amount: gemPackage.price,
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      // redirect users back to the channel where they initiated the purchase
      success_url: baseUrl,
      cancel_url: baseUrl,
      metadata: {
        discordId: discordId,
        packageId: packageId,
        username: username,
        channelId: channelId, // store channel for success message
        gems: gemPackage.gems.toString(),
        timestamp: Date.now().toString(), // for tracking when payment was initiated
      },
      // expire the session after 30 minutes
      expires_at: Math.floor(Date.now() / 1000) + 30 * 60,
    });

    console.log(
      `[StripeUtils] Created checkout session for ${username} (${discordId}): ${packageId} package`
    );
    return session.url;
  } catch (error) {
    console.error("[StripeUtils] Error creating checkout session:", error);
    throw error;
  }
}

/**
 * Creates a Stripe checkout session for gem gift purchase
 * @param {string} buyerDiscordId - The Discord user ID of the buyer
 * @param {string} recipientDiscordId - The Discord user ID of the recipient
 * @param {string} packageId - The gem package ID
 * @param {string} buyerUsername - The buyer's Discord username
 * @param {string} recipientName - The recipient's in-game name
 * @param {string} channelId - The Discord channel ID where the transaction started
 * @param {string} guildId - The Discord guild ID (optional, for server channels)
 * @returns {Promise<string>} The checkout session URL
 */
async function createGemGiftCheckoutSession(
  buyerDiscordId,
  recipientDiscordId,
  packageId,
  buyerUsername,
  recipientName,
  channelId,
  guildId = null
) {
  try {
    if (!process.env.STRIPE_SECRET_KEY || !stripe) {
      throw new Error("Stripe secret key not configured");
    }

    const gemPackage = GEM_PACKAGES[packageId];
    if (!gemPackage) {
      throw new Error(`Invalid gem package: ${packageId}`);
    }

    // construct the Discord channel URL based on whether it's a DM or server channel
    const baseUrl = guildId
      ? `https://discord.com/channels/${guildId}/${channelId}`
      : `https://discord.com/channels/@me/${channelId}`;

    // create checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `🎁 ${gemPackage.emoji} ${gemPackage.name} (Gift)`,
              description: `${gemPackage.gems} Gems for ${recipientName} in Disblock`,
            },
            unit_amount: gemPackage.price,
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      // redirect users back to the channel where they initiated the purchase
      success_url: baseUrl,
      cancel_url: baseUrl,
      metadata: {
        discordId: buyerDiscordId,
        recipientDiscordId: recipientDiscordId,
        packageId: packageId,
        username: buyerUsername, // This is now the buyer's in-game name
        recipientName: recipientName,
        channelId: channelId,
        gems: gemPackage.gems.toString(),
        timestamp: Date.now().toString(),
        isGift: "true", // flag to indicate this is a gift
      },
      // expire the session after 30 minutes
      expires_at: Math.floor(Date.now() / 1000) + 30 * 60,
    });

    console.log(
      `[StripeUtils] Created gift checkout session for ${buyerUsername} (${buyerDiscordId}) -> ${recipientName} (${recipientDiscordId}): ${packageId} package`
    );
    return session.url;
  } catch (error) {
    console.error("[StripeUtils] Error creating gift checkout session:", error);
    throw error;
  }
}

/**
 * Handles successful payment webhook from Stripe
 * @param {object} event - The Stripe webhook event
 * @returns {Promise<boolean>} Success status
 */
async function handlePaymentSuccess(event) {
  try {
    const session = event.data.object;
    const {
      discordId,
      packageId,
      username,
      gems,
      isGift,
      recipientDiscordId,
      recipientName,
    } = session.metadata;

    if (isGift === "true") {
      // handle gift purchase
      console.log(
        `[StripeUtils] Processing successful gift payment from ${username} (${discordId}) to ${recipientName} (${recipientDiscordId}): ${packageId} package`
      );

      // add gems to recipient's account
      const result = await updateGemsAtomically(
        recipientDiscordId,
        parseInt(gems)
      );

      if (!result) {
        console.error(
          `[StripeUtils] Failed to add gift gems to recipient ${recipientDiscordId}`
        );
        return false;
      }

      console.log(
        `[StripeUtils] Successfully added ${gems} gift gems to ${recipientName} (${recipientDiscordId}). New balance: ${result.gems}`
      );
    } else {
      // handle regular purchase
      console.log(
        `[StripeUtils] Processing successful payment for ${username} (${discordId}): ${packageId} package`
      );

      // add gems to player account
      const result = await updateGemsAtomically(discordId, parseInt(gems));

      if (!result) {
        console.error(
          `[StripeUtils] Failed to add gems to player ${discordId}`
        );
        return false;
      }

      console.log(
        `[StripeUtils] Successfully added ${gems} gems to ${username} (${discordId}). New balance: ${result.gems}`
      );
    }

    return true;
  } catch (error) {
    console.error("[StripeUtils] Error handling payment success:", error);
    return false;
  }
}

/**
 * Validates Stripe webhook signature
 * @param {string} payload - The raw request body
 * @param {string} signature - The Stripe signature header
 * @returns {object|null} The validated event object or null if invalid
 */
function validateWebhookSignature(payload, signature) {
  try {
    if (!process.env.STRIPE_WEBHOOK_SECRET || !stripe) {
      throw new Error("Stripe webhook secret not configured");
    }

    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    );

    return event;
  } catch (error) {
    console.error("[StripeUtils] Webhook signature validation failed:", error);
    return null;
  }
}

/**
 * Sends a success message to Discord when gems are purchased
 */
async function sendGemPurchaseSuccessMessage(
  discordClient,
  channelId,
  discordId,
  packageId,
  gemsAdded,
  newTotal
) {
  try {
    const channel = await discordClient.channels.fetch(channelId);
    if (!channel) {
      console.warn(
        `[StripeUtils] Could not find channel ${channelId} for success message`
      );
      return;
    }

    const gemPackage = GEM_PACKAGES[packageId];
    if (!gemPackage) {
      console.warn(
        `[StripeUtils] Could not find package info for ${packageId}`
      );
      return;
    }

    const successEmbed = new EmbedBuilder()
      .setDescription(
        `<@${discordId}> your ${gemPackage.name} purchase was successful!\n\n**Gems Purchased**\n<:emerald:1375550740654981230> +${gemsAdded} Gems\n\n**New Total**\n<:emerald:1375550740654981230> ${newTotal.toLocaleString()} Gems`
      )
      .setColor(EMBED_COLORS.GREEN)
      .setFooter({ text: "Thank you for supporting DisBlock!" });

    await channel.send({ embeds: [successEmbed] });
    console.log(
      `[StripeUtils] Sent success message to channel ${channelId} for ${discordId}`
    );
  } catch (error) {
    console.error("[StripeUtils] Error sending success message:", error);
  }
}

/**
 * Sends a success message to Discord when gems are gifted
 */
async function sendGemGiftSuccessMessage(
  discordClient,
  channelId,
  _buyerDiscordId,
  recipientDiscordId,
  _buyerUsername,
  _recipientName,
  packageId,
  _gemsAdded,
  _newTotal
) {
  try {
    const channel = await discordClient.channels.fetch(channelId);
    if (!channel) {
      console.warn(
        `[StripeUtils] Could not find channel ${channelId} for gift success message`
      );
      return;
    }

    const gemPackage = GEM_PACKAGES[packageId];
    if (!gemPackage) {
      console.warn(
        `[StripeUtils] Could not find package info for ${packageId}`
      );
      return;
    }

    const successEmbed = new EmbedBuilder()
      .setDescription(
        `You gifted **${_recipientName}** some gems!\n\n**Gift Package**\n<:emerald:1375550740654981230> ${gemPackage.name}`
      )
      .setColor(EMBED_COLORS.GREEN)
      .setFooter({ text: "Thank you for supporting DisBlock!" });

    await channel.send({ embeds: [successEmbed] });
    console.log(
      `[StripeUtils] Sent gift success message to channel ${channelId} for gift to ${recipientDiscordId}`
    );
  } catch (error) {
    console.error("[StripeUtils] Error sending gift success message:", error);
  }
}

/**
 * Sends a direct notification to the gift recipient
 */
async function sendGiftRecipientNotification(
  discordClient,
  recipientDiscordId,
  buyerUsername,
  packageId,
  gemsAdded,
  newTotal
) {
  try {
    const { dbGet } = require("./dbUtils");

    const gemPackage = GEM_PACKAGES[packageId];
    if (!gemPackage) {
      console.warn(
        `[StripeUtils] Could not find package info for ${packageId}`
      );
      return;
    }

    // get the recipient's last active channel
    const channelData = await dbGet(
      "SELECT channel_id FROM player_last_active_channel WHERE discord_id = ?",
      [recipientDiscordId]
    );

    if (!channelData) {
      console.warn(
        `[StripeUtils] No last active channel found for recipient ${recipientDiscordId}`
      );
      return;
    }

    // fetch the channel
    const channel = await discordClient.channels.fetch(channelData.channel_id);
    if (!channel || !channel.isTextBased()) {
      console.warn(
        `[StripeUtils] Invalid channel ${channelData.channel_id} for recipient ${recipientDiscordId}`
      );
      return;
    }

    const giftEmbed = new EmbedBuilder()
      .setTitle(`🎁 You received a gift from ${buyerUsername}!`)
      .setDescription(
        `**Gift Package**\n<:emerald:1375550740654981230> +${gemsAdded} Gems\n\n**Your New Total**\n<:emerald:1375550740654981230> ${newTotal.toLocaleString()} Gems`
      )
      .setColor(EMBED_COLORS.GREEN)
      .setFooter({ text: "Go thank them! It's a very valuable gift!" });

    await channel.send({
      content: `<@${recipientDiscordId}>`,
      embeds: [giftEmbed],
    });
    console.log(
      `[StripeUtils] Sent gift notification to recipient ${recipientDiscordId} in channel ${channelData.channel_id}`
    );
  } catch (error) {
    console.error(
      "[StripeUtils] Error sending gift recipient notification:",
      error
    );
    // don't throw error here - the main gift processing should still succeed even if notification fails
  }
}

/**
 * Checks for completed payments in the last few minutes and processes them
 * Call this function periodically (every 5 seconds) from your bot
 * @param {Client} discordClient - The Discord client for sending messages
 * @returns {Promise<number>} Number of payments processed
 */
async function checkAndProcessCompletedPayments(discordClient) {
  try {
    if (!process.env.STRIPE_SECRET_KEY || !stripe) {
      // silently skip if stripe not configured - don't spam logs
      return 0;
    }

    // check for sessions completed in the last 2 minutes (faster checking now)
    const twoMinutesAgo = Math.floor((Date.now() - 2 * 60 * 1000) / 1000);

    const sessions = await stripe.checkout.sessions.list({
      created: { gte: twoMinutesAgo },
      status: "complete",
      limit: 20,
    });

    let processedCount = 0;

    for (const session of sessions.data) {
      if (
        session.metadata &&
        session.metadata.discordId &&
        session.metadata.gems
      ) {
        // check if we already processed this payment
        const alreadyProcessed = await checkIfPaymentProcessed(session.id);
        if (alreadyProcessed) {
          continue;
        }

        const {
          discordId,
          packageId,
          username,
          gems,
          channelId,
          isGift,
          recipientDiscordId,
          recipientName,
        } = session.metadata;

        if (isGift === "true") {
          // process gift
          console.log(
            `[StripeUtils] Found completed gift payment from ${username} (${discordId}) to ${recipientName} (${recipientDiscordId}): ${packageId} package`
          );

          // add gems to recipient's account
          const result = await updateGemsAtomically(
            recipientDiscordId,
            parseInt(gems)
          );

          if (result) {
            console.log(
              `[StripeUtils] Successfully added ${gems} gift gems to ${recipientName} (${recipientDiscordId}). New balance: ${result.gems}`
            );

            // send gift success message to the channel where transaction started
            if (discordClient && channelId) {
              await sendGemGiftSuccessMessage(
                discordClient,
                channelId,
                discordId,
                recipientDiscordId,
                username,
                recipientName,
                packageId,
                parseInt(gems),
                result.gems
              );
            }

            // send a direct notification to the gift recipient
            if (discordClient) {
              await sendGiftRecipientNotification(
                discordClient,
                recipientDiscordId,
                username,
                packageId,
                parseInt(gems),
                result.gems
              );
            }

            // mark this payment as processed
            await markPaymentAsProcessed(
              session.id,
              recipientDiscordId,
              parseInt(gems),
              packageId,
              true,
              recipientDiscordId
            );
            processedCount++;
          } else {
            console.error(
              `[StripeUtils] Failed to add gift gems to recipient ${recipientDiscordId}`
            );
          }
        } else {
          // process regular purchase
          console.log(
            `[StripeUtils] Found completed payment for ${username} (${discordId}): ${packageId} package`
          );

          // add gems to player account
          const result = await updateGemsAtomically(discordId, parseInt(gems));

          if (result) {
            console.log(
              `[StripeUtils] Successfully added ${gems} gems to ${username} (${discordId}). New balance: ${result.gems}`
            );

            // send success message to the channel where transaction started
            if (discordClient && channelId) {
              await sendGemPurchaseSuccessMessage(
                discordClient,
                channelId,
                discordId,
                packageId,
                parseInt(gems),
                result.gems
              );
            }

            // mark this payment as processed
            await markPaymentAsProcessed(
              session.id,
              discordId,
              parseInt(gems),
              packageId,
              false,
              null
            );
            processedCount++;
          } else {
            console.error(
              `[StripeUtils] Failed to add gems to player ${discordId}`
            );
          }
        }
      }
    }

    if (processedCount > 0) {
      console.log(
        `[StripeUtils] Processed ${processedCount} completed payments`
      );
    }

    return processedCount;
  } catch (error) {
    console.error("[StripeUtils] Error checking completed payments:", error);
    return 0;
  }
}

/**
 * Checks if a payment has already been processed to avoid duplicates
 */
async function checkIfPaymentProcessed(sessionId) {
  try {
    const { dbGet } = require("./dbUtils");
    const result = await dbGet(
      "SELECT 1 FROM processed_payments WHERE session_id = ?",
      [sessionId]
    );
    return !!result;
  } catch (error) {
    console.error(
      `[StripeUtils] Error checking payment status for ${sessionId}:`,
      error
    );
    // Fall back to in-memory check
    if (!global.processedPayments) {
      global.processedPayments = new Set();
    }
    return global.processedPayments.has(sessionId);
  }
}

/**
 * Marks a payment as processed
 */
async function markPaymentAsProcessed(
  sessionId,
  discordId,
  gems,
  packageId = "unknown",
  isGift = false,
  recipientDiscordId = null
) {
  try {
    const { dbRunQueued } = require("./dbUtils");
    await dbRunQueued(
      `INSERT OR IGNORE INTO processed_payments 
       (session_id, discord_id, gems_amount, processed_at, package_id, is_gift, recipient_discord_id) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        sessionId,
        discordId,
        gems,
        Date.now(),
        packageId,
        isGift ? 1 : 0,
        recipientDiscordId,
      ]
    );

    // Also add to in-memory set as backup
    if (!global.processedPayments) {
      global.processedPayments = new Set();
    }
    global.processedPayments.add(sessionId);

    console.log(
      `[StripeUtils] Marked payment ${sessionId} as processed for ${discordId} (${gems} gems)`
    );
  } catch (error) {
    console.error(`[StripeUtils] Error marking payment as processed:`, error);
    // Fall back to in-memory tracking
    if (!global.processedPayments) {
      global.processedPayments = new Set();
    }
    global.processedPayments.add(sessionId);
  }
}

module.exports = {
  GEM_PACKAGES,
  createGemCheckoutSession,
  createGemGiftCheckoutSession,
  handlePaymentSuccess,
  validateWebhookSignature,
  checkAndProcessCompletedPayments,
};
