const { dbRun } = require("../utils/dbUtils");

async function up() {
  console.log(
    "Applying migration 067: Add unique index on user_id in active_actions table...",
  );
  try {
    await dbRun(
      "CREATE UNIQUE INDEX IF NOT EXISTS uniq_active_actions_user_id ON active_actions(user_id)",
    );
    console.log("Unique index on active_actions.user_id created successfully.");
  } catch (err) {
    console.error("Error creating unique index on active_actions:", err);
    throw err;
  }
}

async function down() {
  console.log(
    "Reverting migration 067: Drop unique index on user_id in active_actions table...",
  );
  try {
    await dbRun("DROP INDEX IF EXISTS uniq_active_actions_user_id");
    console.log("Unique index on active_actions.user_id dropped successfully.");
  } catch (err) {
    console.error("Error dropping unique index on active_actions:", err);
    throw err;
  }
}

module.exports = { up, down };
