const { EMBED_COLORS } = require("../gameConfig.js");
const {
  <PERSON>lash<PERSON>ommandBuilder,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("cakebag")
    .setDescription("Displays your collection of New Year Cakes.")
    .addStringOption((option) =>
      option
        .setName("sort")
        .setDescription("Sort order for cakes")
        .setRequired(false)
        .addChoices(
          { name: "Year (Oldest First)", value: "year_asc" },
          { name: "Year (Newest First)", value: "year_desc" },
          { name: "Recently Obtained", value: "recent" }
        )
    ),
  async execute(interaction) {
    const userId = interaction.user.id;
    const sortOption = interaction.options.getString("sort") || "year_asc";
    await interaction.deferReply();

    try {
      const playerData = await getPlayerData(userId);
      if (!playerData) {
        return interaction.editReply({
          content: "Could not load your cake bag data.",
        });
      }

      const cakeBag = playerData.cakeBag || [];
      const cakeEmoji = "<:new_year_cake:1372914056641511484>";

      if (cakeBag.length === 0) {
        const embed = new EmbedBuilder()
          .setTitle(`${playerData.name}'s Cake Bag`)
          .setDescription("You haven't collected any New Year Cakes yet!")
          .setColor(EMBED_COLORS.GOLD);
        return interaction.editReply({ embeds: [embed] });
      }

      const validCakes = cakeBag.filter(
        (cake) =>
          cake &&
          typeof cake.year === "number" &&
          typeof cake.obtainedTimestampUTC === "number"
      );

      let sortedCakes;
      switch (sortOption) {
        case "year_desc":
          sortedCakes = [...validCakes].sort((a, b) => b.year - a.year);
          break;
        case "recent":
          sortedCakes = [...validCakes].sort(
            (a, b) => b.obtainedTimestampUTC - a.obtainedTimestampUTC
          );
          break;
        default:
          sortedCakes = [...validCakes].sort((a, b) => a.year - b.year);
      }

      const dateFormatter = new Intl.DateTimeFormat(undefined, {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        timeZone: "UTC",
      });

      const itemsPerPage = 10;
      const pages = [];

      for (let i = 0; i < sortedCakes.length; i += itemsPerPage) {
        const pageCakes = sortedCakes.slice(i, i + itemsPerPage);
        const description = pageCakes
          .map((cake) => {
            const dateStr = dateFormatter.format(
              new Date(cake.obtainedTimestampUTC * 1000)
            );
            return `${cakeEmoji} **New Year Cake (Year ${cake.year})** - Obtained: ${dateStr} UTC`;
          })
          .join("\n");
        pages.push(description);
      }

      if (pages.length === 0) {
        const embed = new EmbedBuilder()
          .setTitle(`${playerData.name}'s Cake Bag`)
          .setDescription("No valid cake data found in your collection.")
          .setColor(EMBED_COLORS.GOLD);
        return interaction.editReply({ embeds: [embed] });
      }

      const totalPages = pages.length;
      let currentPageIndex = 0;

      const createEmbed = (pageIndex) => {
        return new EmbedBuilder()
          .setTitle(`${playerData.name}'s Cake Bag`)
          .setDescription(
            `**${sortedCakes.length} Total Cakes** • Sorted by: ${getSortDescription(sortOption)}\n\n${pages[pageIndex]}`
          )
          .setColor(EMBED_COLORS.GOLD)
          .setFooter({ text: `Page ${pageIndex + 1} of ${totalPages}` });
      };

      let actionRow;
      if (totalPages > 1) {
        actionRow = new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId("prev")
            .setLabel("◀️")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(currentPageIndex === 0),
          new ButtonBuilder()
            .setCustomId("next")
            .setLabel("▶️")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(currentPageIndex === totalPages - 1)
        );
      }

      const response = await interaction.editReply({
        embeds: [createEmbed(currentPageIndex)],
        components: actionRow ? [actionRow] : [],
      });

      if (totalPages > 1) {
        const collector = response.createMessageComponentCollector({
          filter: (i) =>
            i.user.id === interaction.user.id &&
            (i.customId === "prev" || i.customId === "next"),
          time: 120000,
        });

        collector.on("collect", async (i) => {
          if (i.customId === "prev" && currentPageIndex > 0) {
            currentPageIndex--;
          } else if (
            i.customId === "next" &&
            currentPageIndex < totalPages - 1
          ) {
            currentPageIndex++;
          }

          actionRow.components[0].setDisabled(currentPageIndex === 0);
          actionRow.components[1].setDisabled(
            currentPageIndex === totalPages - 1
          );

          await i.update({
            embeds: [createEmbed(currentPageIndex)],
            components: [actionRow],
          });
        });

        collector.on("end", async () => {
          await response.edit({ components: [] });
        });
      }
    } catch (error) {
      console.error("[CakeBag Command] Error executing command:", error);
      await interaction.editReply({
        content: "An error occurred while fetching your cake bag.",
      });
    }
  },
};

function getSortDescription(sortOption) {
  switch (sortOption) {
    case "year_asc":
      return "Year (Oldest First)";
    case "year_desc":
      return "Year (Newest First)";
    case "recent":
      return "Recently Obtained";
    default:
      return "Year (Oldest First)";
  }
}
