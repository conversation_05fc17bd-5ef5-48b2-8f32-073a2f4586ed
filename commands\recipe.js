const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  ComponentType,
  StringSelectMenuBuilder,
} = require("discord.js");
const { STATS, skillEmojis } = require("../gameConfig.js");
const configManager = require("../utils/configManager.js");
const { getItemColor } = require("../utils/rarityUtils.js");
const {
  getPlayerData,
  getPlayerSkillLevel,
} = require("../utils/playerDataManager.js");
const { getPlayerItemCollectionLevel } = require("../utils/collectionUtils.js");
const craftingUtils = require("../utils/craftingUtils.js");
const { getInventory } = require("../utils/inventory.js");
const { getPlayerAccessories } = require("../utils/accessoryManager.js");
// Using embeds + standard components only

// helper function to format crafting materials breakdown for error display
function formatCraftingMaterialsBreakdown(
  recipe,
  playerInventory,
  allItems,
  craftAmount = 1,
  selectedUniqueItems = {}
) {
  if (!recipe?.ingredients?.length) {
    return "No ingredients found for this recipe.";
  }

  let output = "";

  for (const ingredient of recipe.ingredients) {
    if (!ingredient?.itemKey) continue;

    const itemData = allItems[ingredient.itemKey];
    const neededAmount = ingredient.amount * craftAmount;
    const isUnique = itemData?.unique === true;

    let hasEnough = false;
    let quantityDisplay = "";

    if (isUnique) {
      // for unique items, check if they're selected rather than in inventory
      const selectedCount = selectedUniqueItems[ingredient.itemKey] || 0;
      hasEnough = selectedCount >= neededAmount;
      quantityDisplay = hasEnough ? " (Selected)" : " (Not selected)";
    } else {
      // for regular items, check inventory as normal
      const playerAmount = playerInventory?.items?.[ingredient.itemKey] || 0;
      hasEnough = playerAmount >= neededAmount;

      if (playerAmount > 0) {
        quantityDisplay = ` (You have ${playerAmount.toLocaleString()})`;
      }
    }

    // determine if player has enough
    const checkmark = hasEnough ? "✅" : "❌";

    // format the display
    const emoji = itemData?.emoji || "❓";
    const itemName = itemData?.name || ingredient.itemKey;

    output += `${checkmark} ${emoji} \`${neededAmount.toLocaleString()}x ${itemName}\`${quantityDisplay}\n`;
  }

  return output.trim();
}

// helper function to calculate how many of an item can be crafted
function calculateCraftableAmount(ingredients, playerInventory) {
  if (!playerInventory || !playerInventory.items || !ingredients) {
    return 0;
  }

  let maxCraftable = Infinity;

  if (Array.isArray(ingredients)) {
    for (const ingredientObj of ingredients) {
      if (
        typeof ingredientObj !== "object" ||
        ingredientObj === null ||
        !Object.prototype.hasOwnProperty.call(ingredientObj, "itemKey") ||
        !Object.prototype.hasOwnProperty.call(ingredientObj, "amount")
      ) {
        continue;
      }
      const itemKey = ingredientObj.itemKey;
      const amount = ingredientObj.amount;
      if (typeof amount !== "number") continue;

      const playerAmount = playerInventory.items[itemKey] || 0;
      const possibleCrafts = Math.floor(playerAmount / amount);
      maxCraftable = Math.min(maxCraftable, possibleCrafts);
    }
  } else {
    for (const [itemKey, amount] of Object.entries(ingredients)) {
      if (typeof amount !== "number") continue;

      const playerAmount = playerInventory.items[itemKey] || 0;
      const possibleCrafts = Math.floor(playerAmount / amount);
      maxCraftable = Math.min(maxCraftable, possibleCrafts);
    }
  }

  return maxCraftable === Infinity ? 0 : Math.max(0, maxCraftable);
}

function formatIngredients(
  ingredients,
  allItems,
  playerInventory = null,
  uniqueItemsData = null
) {
  if (
    !ingredients ||
    (typeof ingredients !== "object" && !Array.isArray(ingredients))
  ) {
    console.error(
      "[formatIngredients] Expected ingredients to be an object or array, but got:",
      ingredients
    );
    return "Invalid ingredient data";
  }

  let output = "";
  let processedIngredients = 0;

  if (Array.isArray(ingredients)) {
    if (ingredients.length === 0) {
      return "None";
    }
    for (const ingredientObj of ingredients) {
      if (
        typeof ingredientObj !== "object" ||
        ingredientObj === null ||
        !Object.prototype.hasOwnProperty.call(ingredientObj, "itemKey") ||
        !Object.prototype.hasOwnProperty.call(ingredientObj, "amount")
      ) {
        console.warn(
          "[formatIngredients] Skipping invalid ingredient in array:",
          ingredientObj
        );
        continue;
      }
      const itemKey = ingredientObj.itemKey;
      const amount = ingredientObj.amount;

      if (typeof amount !== "number") {
        console.warn(
          `[formatIngredients] Skipping ingredient with non-number amount in array format: ${itemKey}: ${amount}`
        );
        continue;
      }
      const itemData = allItems[itemKey];

      // check if player has enough of this ingredient
      let checkmark = "";
      let quantityDisplay = "";
      if (playerInventory && playerInventory.items) {
        const playerAmount = playerInventory.items[itemKey] || 0;

        // check for unique items if this is a unique item type
        let hasUniqueItem = false;
        if (itemData?.unique === true && uniqueItemsData) {
          // check if player has any eligible unique items of this type
          const eligibleItems = uniqueItemsData[itemKey] || [];
          hasUniqueItem = eligibleItems.length > 0;
        }

        // for unique items, show checkmark if they have any eligible items
        // for regular items, show checkmark if they have enough
        const hasEnough =
          itemData?.unique === true ? hasUniqueItem : playerAmount >= amount;
        checkmark = hasEnough ? "✅ " : "❌ ";

        // only show quantity if player has at least 1 and it's not a unique item
        if (playerAmount >= 1 && !itemData?.unique) {
          quantityDisplay = ` (You have ${playerAmount.toLocaleString()})`;
        }
      }

      output += `${checkmark}${itemData?.emoji || "❓"} \`${amount.toLocaleString()}x ${
        itemData?.name || itemKey
      }\`${quantityDisplay}\n`;
      processedIngredients++;
    }
  } else {
    const ingredientEntries = Object.entries(ingredients);
    if (ingredientEntries.length === 0) {
      return "None";
    }
    for (const [itemKey, amount] of ingredientEntries) {
      if (typeof amount !== "number") {
        console.warn(
          `[formatIngredients] Skipping ingredient with non-number amount in object format: ${itemKey}: ${amount}`
        );
        continue;
      }
      const itemData = allItems[itemKey];

      // check if player has enough of this ingredient
      let checkmark = "";
      let quantityDisplay = "";
      if (playerInventory && playerInventory.items) {
        const playerAmount = playerInventory.items[itemKey] || 0;

        // check for unique items if this is a unique item type
        let hasUniqueItem = false;
        if (itemData?.unique === true && uniqueItemsData) {
          // check if player has any eligible unique items of this type
          const eligibleItems = uniqueItemsData[itemKey] || [];
          hasUniqueItem = eligibleItems.length > 0;
        }

        // for unique items, show checkmark if they have any eligible items
        // for regular items, show checkmark if they have enough
        const hasEnough =
          itemData?.unique === true ? hasUniqueItem : playerAmount >= amount;
        checkmark = hasEnough ? "✅ " : "❌ ";

        // only show quantity if player has at least 1 and it's not a unique item
        if (playerAmount >= 1 && !itemData?.unique) {
          quantityDisplay = ` (You have ${playerAmount.toLocaleString()})`;
        }
      }

      output += `${checkmark}${itemData?.emoji || "❓"} \`${amount.toLocaleString()}x ${
        itemData?.name || itemKey
      }\`${quantityDisplay}\n`;
      processedIngredients++;
    }
  }

  return processedIngredients > 0 ? output.trim() : "None";
}

function formatRequirements(playerData, itemData, allItems, opts = {}) {
  const outputLines = [];
  const meetEmoji = "✅";
  const failEmoji = "❌";
  const playerExists = playerData != null;

  const requirements = itemData?.craftingRequirements || itemData?.requirements;

  if (requirements) {
    if (requirements.skills) {
      for (const [skillKey, requiredLevel] of Object.entries(
        requirements.skills
      )) {
        const skillKeyLower = skillKey.toLowerCase();
        const playerSkillLevel = playerExists
          ? getPlayerSkillLevel(playerData, skillKeyLower)
          : 0;
        const meetsReq = playerExists && playerSkillLevel >= requiredLevel;
        const checkMark = meetsReq ? meetEmoji : failEmoji;
        const skillNameDisplay =
          skillKeyLower.charAt(0).toUpperCase() + skillKeyLower.slice(1);
        const emoji = skillEmojis[skillKeyLower] || "❓";
        outputLines.push(
          `${checkMark} Requires ${emoji} \`${skillNameDisplay} Lvl ${requiredLevel}\` **to Craft${
            opts.isPet ? "" : "/Upgrade"
          }**`
        );
      }
    }

    if (requirements.collections) {
      for (const [collectionItemKey, requiredLevel] of Object.entries(
        requirements.collections
      )) {
        const collectionItemKeyUpper = collectionItemKey.toUpperCase();
        const playerLevel = playerExists
          ? getPlayerItemCollectionLevel(playerData, collectionItemKeyUpper)
          : 0;
        const meetsReq = playerExists && playerLevel >= requiredLevel;
        const checkMark = meetsReq ? meetEmoji : failEmoji;

        // Try to get item data first
        const requiredItemData = allItems[collectionItemKeyUpper];
        let collectionName = requiredItemData?.name || collectionItemKey;
        let collectionEmoji = requiredItemData?.emoji || "❓";

        // If no direct item match, try to find the collection data
        if (!requiredItemData) {
          try {
            const {
              getCollections,
            } = require("../utils/collectionsManager.js");
            const collections = getCollections();

            // Search for the collection in all categories
            for (const category of Object.values(collections)) {
              if (category[collectionItemKeyUpper]) {
                const collectionData = category[collectionItemKeyUpper];
                collectionName = collectionData.name || collectionItemKey;
                collectionEmoji = collectionData.emoji || "❓";
                break;
              }
            }
          } catch (error) {
            console.error("[Recipe] Error loading collection data:", error);
          }
        }

        outputLines.push(
          `${checkMark} Requires ${collectionEmoji} \`${collectionName} Collection Lvl ${requiredLevel}\` **to Craft${
            opts.isPet ? "" : "/Upgrade"
          }**`
        );
      }
    }

    if (requirements.slayers) {
      const { getSlayerLevelFromExp } = require("../utils/slayerLevelUtils");
      const slayerXpData = playerData?.slayerXp || {};

      for (const [slayerType, requiredLevel] of Object.entries(
        requirements.slayers
      )) {
        const slayerXp = slayerXpData[slayerType] || 0;
        const playerLevel = playerExists
          ? getSlayerLevelFromExp(slayerXp).level
          : 0;
        const meetsReq = playerExists && playerLevel >= requiredLevel;
        const checkMark = meetsReq ? meetEmoji : failEmoji;
        const slayerEmoji =
          slayerType === "zombie"
            ? "<:revenant_horror:1389646540460658970>"
            : "<:mob_spider:1370526342927618078>";
        const slayerName =
          slayerType.charAt(0).toUpperCase() + slayerType.slice(1);
        outputLines.push(
          `${checkMark} Requires ${slayerEmoji} \`${slayerName} Slayer Lvl ${requiredLevel}\` **to Craft${
            opts.isPet ? "" : "/Upgrade"
          }**`
        );
      }
    }
  }

  const isEquippable =
    itemData?.equipSlot ||
    itemData?.type === "TOOL" ||
    itemData?.type?.name === "TOOL" ||
    itemData?.type === "EQUIPMENT" ||
    itemData?.type?.name === "EQUIPMENT";

  if (isEquippable && itemData.equipRequirements) {
    const equipReqs = itemData.equipRequirements;

    if (equipReqs.skills) {
      for (const [skillKey, requiredLevel] of Object.entries(
        equipReqs.skills
      )) {
        const skillKeyLower = skillKey.toLowerCase();
        const playerSkillLevel = playerExists
          ? getPlayerSkillLevel(playerData, skillKeyLower)
          : 0;
        const meetsReq = playerExists && playerSkillLevel >= requiredLevel;
        const checkMark = meetsReq ? meetEmoji : failEmoji;
        const skillNameDisplay =
          skillKeyLower.charAt(0).toUpperCase() + skillKeyLower.slice(1);
        const emoji = skillEmojis[skillKeyLower] || "❓";
        outputLines.push(
          `${checkMark} Requires ${emoji} \`${skillNameDisplay} Lvl ${requiredLevel}\` **to Equip**`
        );
      }
    }
  }

  if (outputLines.length === 0) {
    return "None";
  }

  return outputLines.join("\n");
}

function formatStats(baseStats) {
  if (!baseStats || Object.keys(baseStats).length === 0) {
    return null;
  }
  let output = "";
  for (const [statKey, value] of Object.entries(baseStats)) {
    const statConfig = STATS[statKey];
    if (statConfig) {
      // Format percentages correctly if specified in STATS config
      const displayValue = statConfig.isPercentage
        ? `${value > 0 ? "+" : ""}${value}%`
        : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
      output += `${statConfig.emoji || ""} **${
        statConfig.name || statKey
      }**: ${displayValue}\n`;
    } else {
      // Fallback for stats not defined in STATS config
      output += `❓ **${statKey}**: ${
        value > 0 ? "+" : ""
      }${value.toLocaleString()}\n`;
    }
  }
  return output.trim();
}

// --- Minion Tier Navigation Handler ---

/**
 * Handles minion tier navigation for /recipe command.
 * @param {Interaction} interaction - The button or modal interaction.
 */

// Helper to update the minion embed for a given tier (expects 0-indexed tier)

// Helper to build Minion Embed and Components (expects 0-indexed tier)
async function handleMinionTierNavigation(interaction) {
  // Only allow the original user to use the navigation
  const originalMessage = interaction.message;
  if (
    !originalMessage ||
    !originalMessage.interaction ||
    !originalMessage.interaction.user
  ) {
    console.warn(
      "[handleMinionTierNavigation] Missing original message.interaction context or user. Interaction ID:",
      interaction.id
    );
    if (!interaction.replied && !interaction.deferred) {
      try {
        return interaction.reply({
          content:
            "Cannot navigate, message context lost. Please try the command again.",
        });
      } catch (e) {
        console.error("Error replying to interaction with missing context:", e);
      }
    }
    return;
  }
  const originalUserId = originalMessage.interaction.user.id;
  if (interaction.user.id !== originalUserId) {
    return interaction.reply({
      content: "You cannot use this navigation for someone else.",
    });
  }

  // Parse context from customId instead of embed state
  let minionKeyFromId = null;
  let currentTierDisplay = 1;
  if (interaction.isButton() && typeof interaction.customId === "string") {
    const match = interaction.customId.match(
      /minion_(left|right|tier_modal)(?:.*)::minionKey=([^:]+)::tier=(\d+)/
    );
    if (match) {
      minionKeyFromId = match[2];
      currentTierDisplay = parseInt(match[3], 10) || 1;
    }
  }
  // Fallback: try modal customId payload
  if (
    !minionKeyFromId &&
    interaction.isModalSubmit() &&
    typeof interaction.customId === "string"
  ) {
    const m2 = interaction.customId.match(
      /minion_tier_modal_submit::minionKey=([^:]+)(?::|$)/
    );
    if (m2) {
      minionKeyFromId = m2[1];
    }
  }
  if (!minionKeyFromId) {
    return interaction.reply({
      content: "Could not determine minion context.",
      ephemeral: true,
    });
  }

  const allItems = configManager.getAllItems();
  const minionEntry = Object.entries(allItems).find(
    ([key, item]) => item.type === "MINION" && key === minionKeyFromId
  );
  if (!minionEntry) {
    return interaction.reply({
      content: "Could not find minion definition.",
    });
  }
  const [minionKey, minionData] = minionEntry;

  if (!Array.isArray(minionData.tiers)) {
    console.error(
      `[handleMinionTierNavigation] minionData.tiers is not an array for ${minionKey}`
    );
    return interaction.reply({
      content: "Minion tier data is malformed.",
    });
  }
  const validTiers = minionData.tiers.filter((t) => t != null);
  const maxTierDisplay = validTiers.length;

  if (maxTierDisplay === 0) {
    return interaction.reply({
      content: "This minion has no defined tiers.",
    });
  }

  if (interaction.isButton()) {
    let newTierDisplay = currentTierDisplay;
    if (interaction.customId.startsWith("minion_left")) {
      newTierDisplay = Math.max(1, currentTierDisplay - 1);
    } else if (interaction.customId.startsWith("minion_right")) {
      newTierDisplay = Math.min(maxTierDisplay, currentTierDisplay + 1);
    } else if (interaction.customId.startsWith("minion_tier_modal")) {
      if (maxTierDisplay <= 1) {
        // No point showing modal if only 1 tier or less
        return interaction.reply({
          content:
            "This minion only has one tier or tier navigation is not available.",
        });
      }
      const modal = new ModalBuilder()
        .setCustomId(`minion_tier_modal_submit::minionKey=${minionKey}`)
        .setTitle("Go to Minion Tier...")
        .addComponents(
          new ActionRowBuilder().addComponents(
            new TextInputBuilder()
              .setCustomId("minion_tier_input")
              .setLabel("Enter Tier (Number)")
              .setStyle(TextInputStyle.Short)
              .setRequired(true)
              .setPlaceholder(`1 - ${maxTierDisplay}`)
          )
        );
      try {
        return await interaction.showModal(modal);
      } catch (modalError) {
        console.error(
          "[handleMinionTierNavigation] Error showing modal:",
          modalError
        );
        if (!interaction.replied && !interaction.deferred) {
          try {
            await interaction.reply({
              content:
                "Could not display the tier selection modal. Please try again.",
            });
          } catch (replyError) {
            console.error(
              "[handleMinionTierNavigation] Error sending followup after modal error:",
              replyError
            );
          }
        }
        return;
      }
    } else {
      return interaction.reply({
        content: "Unknown navigation button.",
      });
    }

    if (newTierDisplay === currentTierDisplay) {
      return interaction.deferUpdate();
    }
    return updateMinionTierEmbed(
      interaction,
      minionData,
      minionKey,
      newTierDisplay - 1,
      allItems
    );
  } else if (
    interaction.isModalSubmit() &&
    typeof interaction.customId === "string" &&
    interaction.customId.startsWith("minion_tier_modal_submit")
  ) {
    const tierInput = interaction.fields.getTextInputValue("minion_tier_input");
    const newTierDisplayFromModal = parseInt(tierInput, 10);

    if (
      isNaN(newTierDisplayFromModal) ||
      newTierDisplayFromModal < 1 ||
      newTierDisplayFromModal > maxTierDisplay
    ) {
      return interaction.reply({
        content: `Invalid tier. Enter a number between 1 and ${maxTierDisplay}.`,
      });
    }

    const { embed, components: newComponents } =
      await buildMinionTierEmbedAndComponents(
        minionData,
        minionKey,
        newTierDisplayFromModal - 1,
        allItems
      );

    try {
      await interaction.update({ embeds: [embed], components: newComponents });
    } catch (updateError) {
      console.error(
        "[handleMinionTierNavigation] Modal submit: Error updating message:",
        updateError
      );
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .followUp({
            content: "Failed to update the display to the selected tier.",
          })
          .catch((e) => console.error("Error in modal submit followup: ", e));
      } else if (interaction.replied || interaction.deferred) {
        await interaction
          .followUp({
            content:
              "Error updating the display. Please try the command again.",
          })
          .catch((e) =>
            console.error(
              "Error in modal submit followup (already replied/deferred): ",
              e
            )
          );
      }
    }
    return;
  }
}

// Helper to update the minion embed for a given tier (expects 0-indexed tier)
async function updateMinionTierEmbed(
  interaction,
  minionData,
  minionKey,
  tierIndex,
  allItems
) {
  const { embed, components: newComponents } =
    await buildMinionTierEmbedAndComponents(
      minionData,
      minionKey,
      tierIndex,
      allItems
    );
  try {
    await interaction.update({ embeds: [embed], components: newComponents });
  } catch (error) {
    console.error(
      `[updateMinionTierEmbed] Failed to update embed for minion ${minionKey} tier index ${tierIndex}:`,
      error
    );
    if (!interaction.replied && !interaction.deferred) {
      await interaction
        .followUp({
          content: "Error updating display. Please try again.",
        })
        .catch(() => {});
    }
  }
}

// Helper to build Minion Embed and Components (expects 0-indexed tier)
async function buildMinionTierEmbedAndComponents(
  minionData,
  minionKey,
  tierIndex,
  allItems
) {
  const embed = new EmbedBuilder();
  const components = [];

  if (!Array.isArray(minionData.tiers)) {
    console.error(
      `[buildMinionTierEmbedAndComponents] minionData.tiers is not an array for ${minionKey}`
    );
    embed.setTitle("Error").setDescription("Minion tier data is malformed.");
    return { embed, components };
  }
  const validTiers = minionData.tiers.filter((t) => t != null);
  const totalTierCount = validTiers.length;
  const currentTierData = validTiers[tierIndex];

  if (!currentTierData) {
    embed
      .setTitle("Error")
      .setDescription(`Minion tier data not found for index ${tierIndex}.`);
    return { embed, components };
  }

  const displayTierNumber = tierIndex + 1;

  let ingredientsString = "*Recipe not found for this tier*";
  let ingredients = null;

  if (displayTierNumber === 1) {
    if (minionData.recipes && minionData.recipes[0]?.ingredients) {
      ingredients = minionData.recipes[0].ingredients;
    }
  } else if (Array.isArray(currentTierData.upgradeCost)) {
    ingredients = currentTierData.upgradeCost;
  }

  if (ingredients) {
    ingredientsString = formatIngredients(ingredients, allItems, null, null);
  }

  const resultString = `${minionData.emoji || "❓"} \`1x ${
    minionData.name || minionKey
  }\``;
  const playerData = null;

  embed
    .setColor(getItemColor(minionData))
    .setTitle(
      `${minionData.emoji || "❓"} Minion: ${
        minionData.name || minionKey
      } - Tier ${displayTierNumber}`
    )
    .setFields(
      {
        name: `Ingredients (Tier ${displayTierNumber})`,
        value: ingredientsString,
        inline: false,
      },
      {
        name: `Result (Tier ${displayTierNumber})`,
        value: resultString,
        inline: false,
      },
      {
        name: "Production Rate",
        value: `\`${currentTierData.generationIntervalSeconds}s per item\``,
        inline: true,
      },
      {
        name: "Max Storage",
        value: `\`${currentTierData.maxStorage.toLocaleString()} items\``,
        inline: true,
      }
    );

  if (displayTierNumber === 1) {
    const requirementsString = formatRequirements(
      playerData,
      minionData,
      allItems
    );
    if (requirementsString && requirementsString.toLowerCase() !== "none") {
      embed.addFields({
        name: "Requirements (Tier 1)",
        value: requirementsString,
        inline: false,
      });
    }
  }

  if (minionData.baseStats) {
    const statsString = formatStats(minionData.baseStats);
    if (statsString && statsString.toLowerCase() !== "none") {
      embed.addFields({
        name: "Base Minion Stats",
        value: statsString,
        inline: false,
      });
    }
  }

  if (totalTierCount > 1) {
    const left = new ButtonBuilder()
      .setCustomId(
        `minion_left::minionKey=${minionKey}::tier=${displayTierNumber}`
      )
      .setEmoji("⬅️")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(tierIndex <= 0);
    const right = new ButtonBuilder()
      .setCustomId(
        `minion_right::minionKey=${minionKey}::tier=${displayTierNumber}`
      )
      .setEmoji("➡️")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(tierIndex >= totalTierCount - 1);
    const modalButton = new ButtonBuilder()
      .setCustomId(
        `minion_tier_modal::minionKey=${minionKey}::tier=${displayTierNumber}::max=${totalTierCount}`
      )
      .setLabel("Go to Tier...")
      .setStyle(ButtonStyle.Primary);

    const navRow = new ActionRowBuilder().addComponents(
      left,
      modalButton,
      right
    );
    components.push(navRow);
    embed.setFooter({
      text: `Tier ${displayTierNumber} of ${totalTierCount}. Use arrows or modal to view other tiers.`,
    });
  } else {
    embed.setFooter({
      text: `Tier ${displayTierNumber} of ${totalTierCount}.`,
    });
  }
  // Return standard embed + components
  return { embed, components };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("recipe")
    .setDescription("View the recipe for a specific item.")
    .addStringOption((option) =>
      option
        .setName("item_key")
        .setDescription("The item key whose recipe you want to view")
        .setRequired(true)
        .setAutocomplete(true)
    ),
  async execute(interaction) {
    try {
      if (interaction.isCommand && interaction.isCommand()) {
        await interaction.deferReply();
      }

      let itemKey;
      if (interaction.options && interaction.options.getString) {
        const { findItemByKeyOrName } = require("../utils/itemSearchUtils");
        const allItems = configManager.getAllItems();

        // Try to find the item by key or name (handles both direct lookup and flexible matching)
        const foundItemKey = findItemByKeyOrName(
          interaction.options.getString("item_key"),
          allItems
        );

        if (!foundItemKey) {
          return interaction.editReply({
            content: `Item "${interaction.options.getString("item_key")}" not found. Please check the spelling or use the autocomplete suggestions.`,
          });
        }

        itemKey = foundItemKey;
      } else if (
        interaction.message &&
        interaction.message.embeds &&
        interaction.message.embeds[0]?.title
      ) {
        // Parse itemKey from embed title (e.g., '<emoji> Recipe: Rabbit Pet')
        const title = interaction.message.embeds[0].title;
        const match = title.match(/Recipe: (.+)$/);
        if (match) {
          // Try to find the itemKey by name
          const allItems = configManager.getAllItems();
          const found = Object.entries(allItems).find(
            ([, item]) => item.name === match[1].trim()
          );
          if (found) {
            itemKey = found[0];
          }
        }
      }
      if (!itemKey) {
        return interaction.editReply({
          content: "Could not determine item key for recipe navigation.",
        });
      }

      const allItems = configManager.getAllItems();

      if (!allItems) {
        console.error(
          "[Recipe Command] Failed to get item data from configManager. [REC-CFG-001]"
        );
        return interaction.editReply({
          content:
            "[REC-CFG-001] Internal error loading item data. Please report this.",
        });
      }

      const itemData = allItems[itemKey];

      let playerData = null;
      let playerInventory = null;
      try {
        playerData = await getPlayerData(interaction.user.id);
        if (playerData) {
          playerInventory = await getInventory(interaction.user.id);
        }
      } catch (playerDataError) {
        if (playerDataError.message.includes("Player not found")) {
          console.log(
            `[Recipe Command] Player ${interaction.user.id} not found, proceeding without player data.`
          );
        } else {
          console.error(
            `[Recipe Command] Error fetching player data for ${interaction.user.id}: [REC-DAT-001]`,
            playerDataError
          );
          return interaction.editReply({
            content:
              "[REC-DAT-001] Failed to retrieve your character data. Please report this.",
          });
        }
      }

      if (!itemData) {
        return interaction.editReply({
          content: `❌ Unknown item key: \`${itemKey}\``,
        });
      }

      // If the requested item is a MINION, ensure we use the base definition
      const baseItemData = itemData.type === "MINION" ? itemData : null; // Use directly if it's a minion

      const baseEmbed = new EmbedBuilder()
        .setColor(getItemColor(itemData))
        .setTitle(
          `${itemData.emoji || "❓"} Recipe: ${itemData.name || itemKey}`
        );

      // --- Component Row ---

      // --- Handle Minions Specifically ---
      if (baseItemData) {
        // Use the baseItemData if it's a minion
        const minionConfigData = baseItemData; // Alias for clarity
        const baseMinionKey = baseItemData.itemKey; // Get base key directly

        if (!minionConfigData.tiers || minionConfigData.tiers.length <= 1) {
          // Tiers array includes null at index 0
          return interaction.editReply({
            content: `❌ Error: Minion tier data structure invalid or missing for base key \`${baseMinionKey}\`. [REC-MIN-DAT-NF]`,
          });
        }

        const currentTierDisplay = 1; // Always show Tier 1 initially
        const currentTierData = minionConfigData.tiers[currentTierDisplay]; // T1 data is at index 1

        if (!currentTierData) {
          return interaction.editReply({
            content: `❌ Error: Tier ${currentTierDisplay} data object not found for minion \`${baseMinionKey}\`. [REC-MIN-T1-NF]`,
          });
        }

        // Get T1 recipe info directly from the base definition
        let ingredientsString = "*Recipe not found for Tier 1*";
        let ingredientsFieldName = "Ingredients (Tier 1)";
        if (
          minionConfigData.recipes &&
          minionConfigData.recipes[0]?.ingredients
        ) {
          ingredientsString = formatIngredients(
            minionConfigData.recipes[0].ingredients,
            allItems,
            playerInventory,
            null
          );

          // calculate how many can be crafted
          if (playerInventory) {
            const craftableAmount = calculateCraftableAmount(
              minionConfigData.recipes[0].ingredients,
              playerInventory
            );
            if (craftableAmount > 0) {
              ingredientsFieldName = `Ingredients (Tier 1) (You can craft ${craftableAmount.toLocaleString()})`;
            }
          }
        } else {
          console.warn(
            `[Recipe Command] Tier 1 recipe ingredients missing for ${baseMinionKey} in items.json`
          );
        }

        // Result is always 1x of the base minion item
        const resultString = `${minionConfigData.emoji || "❓"} \`1x ${
          minionConfigData.name || baseMinionKey
        }\``;

        baseEmbed
          .setTitle(
            `${minionConfigData.emoji || "❓"} Minion: ${
              minionConfigData.name || baseMinionKey
            } - Tier ${currentTierDisplay}`
          )
          .setFields(
            // Use setFields to replace any previous fields if switching views
            {
              name: ingredientsFieldName,
              value: ingredientsString,
              inline: false,
            },
            { name: "Result (Tier 1)", value: resultString, inline: false },
            {
              name: "Production Rate",
              value: `\`${currentTierData.generationIntervalSeconds}s\` per item`,
              inline: true,
            },
            {
              name: "Max Storage",
              value: `\`${currentTierData.maxStorage.toLocaleString()}\` items`,
              inline: true,
            },
            {
              name: "Requirements (Tier 1)",
              value: formatRequirements(playerData, minionConfigData, allItems),
              inline: false,
            } // Requirements are on the base item
          );

        // Stats are on the base item definition, not specific tiers
        const statsString = formatStats(minionConfigData.baseStats);
        if (statsString && statsString.toLowerCase() !== "none") {
          baseEmbed.addFields({
            name: "Base Stats",
            value: statsString,
            inline: false,
          });
        }

        // --- Add navigation UI for minions ---
        const left = new ButtonBuilder()
          .setCustomId(
            `minion_left::minionKey=${baseMinionKey}::tier=${currentTierDisplay}`
          )
          .setEmoji("⬅️")
          .setStyle(ButtonStyle.Secondary);
        const right = new ButtonBuilder()
          .setCustomId(
            `minion_right::minionKey=${baseMinionKey}::tier=${currentTierDisplay}`
          )
          .setEmoji("➡️")
          .setStyle(ButtonStyle.Secondary);
        const modal = new ButtonBuilder()
          .setCustomId(
            `minion_tier_modal::minionKey=${baseMinionKey}::tier=${currentTierDisplay}::max=${minionConfigData.tiers.filter((t) => t != null).length}`
          )
          .setLabel("Go to Tier...")
          .setStyle(ButtonStyle.Primary);
        const navRow = new ActionRowBuilder().addComponents(left, modal, right);
        baseEmbed.setFooter({
          text: "Use arrows or modal to view other tiers (feature coming soon)",
        });
        // embed is already built
        const components = [navRow];
        if (interaction.isButton()) {
          await interaction.update({ embeds: [baseEmbed], components });
        } else {
          await interaction.editReply({ embeds: [baseEmbed], components });
        }
        return;
      } else if (itemData.type === "PET") {
        // --- Handle Pets: Show one recipe at a time with navigation ---
        const petKey = itemKey;
        const petRecipes = Object.entries(allItems)
          .filter(
            ([, v]) =>
              v.resultPetKey === petKey &&
              v.recipes &&
              Array.isArray(v.recipes) &&
              v.recipes.length > 0
          )
          .map(([k, v]) => ({ key: k, data: v }));
        let currentRecipeIdx = 0;
        // Check for customId in interaction for navigation
        if (
          interaction.isButton() &&
          interaction.customId.startsWith("pet_recipe_nav::")
        ) {
          const idxMatch = interaction.customId.match(/::idx=(\d+)/);
          const idx = idxMatch ? parseInt(idxMatch[1], 10) : NaN;
          if (!isNaN(idx) && idx >= 0 && idx < petRecipes.length) {
            currentRecipeIdx = idx;
          }
        }
        if (petRecipes.length === 0) {
          baseEmbed.addFields({
            name: "Crafting",
            value: "*This item cannot be crafted.*",
            inline: false,
          });
        } else {
          const { key: variantKey, data: variantData } =
            petRecipes[currentRecipeIdx];
          const recipe = variantData.recipes[0];
          const ingredientsString = formatIngredients(
            recipe.ingredients,
            allItems,
            playerInventory,
            null
          );
          const label = variantData.name || variantKey;
          // --- Format Produces line ---
          let producesBlock = "";
          if (variantData.probabilities) {
            const rarityEmojis = {
              COMMON: "⚪",
              UNCOMMON: "🟢",
              RARE: "🔵",
              EPIC: "🟣",
              LEGENDARY: "🟠",
            };
            const producesLines = Object.keys(variantData.probabilities)
              .map((rarity) => {
                const rarityName =
                  rarity.charAt(0) + rarity.slice(1).toLowerCase();
                const resultPet = variantData.resultPetKey
                  ? allItems[variantData.resultPetKey]
                  : null;
                const petEmoji = resultPet?.emoji || "❓";
                const petName = resultPet?.name || "Pet";
                return `${rarityEmojis[rarity] || ""} ${petEmoji} ${rarityName} ${petName}`;
              })
              .join("\n");
            producesBlock = `**Produces: (random)**\n${producesLines}`;
          }
          let recipeValue = ingredientsString;
          if (producesBlock) recipeValue += `\n\n${producesBlock}`;
          baseEmbed.addFields({
            name: `Recipe (${label})`,
            value: recipeValue,
            inline: false,
          });
          // Show requirements for this recipe
          if (variantData.craftingRequirements) {
            baseEmbed.addFields({
              name: `Requirements (${label})`,
              value: `\n${formatRequirements(
                playerData,
                variantData,
                allItems,
                { isPet: true }
              )}`,
              inline: false,
            });
          }
        }
        // Add navigation and Quick Craft buttons
        let components = [];
        if (petRecipes.length > 0) {
          const navRow = new ActionRowBuilder();
          if (petRecipes.length === 1) {
            navRow.addComponents(
              new ButtonBuilder()
                .setCustomId(
                  `quickcraft_x1_${petRecipes[currentRecipeIdx].key}`
                )
                .setLabel("Quick Craft x1")
                .setStyle(ButtonStyle.Primary)
            );
            components = [navRow];
          } else if (petRecipes.length === 2) {
            const otherIdx = (currentRecipeIdx + 1) % 2;
            if (currentRecipeIdx === 0) {
              navRow.addComponents(
                new ButtonBuilder()
                  .setCustomId(
                    `quickcraft_x1_${petRecipes[currentRecipeIdx].key}`
                  )
                  .setLabel("Quick Craft x1")
                  .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                  .setCustomId(
                    `pet_recipe_nav::petKey=${petKey}::idx=${otherIdx}`
                  )
                  .setEmoji("➡️")
                  .setStyle(ButtonStyle.Secondary)
              );
            } else {
              navRow.addComponents(
                new ButtonBuilder()
                  .setCustomId(
                    `pet_recipe_nav::petKey=${petKey}::idx=${otherIdx}`
                  )
                  .setEmoji("⬅️")
                  .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                  .setCustomId(
                    `quickcraft_x1_${petRecipes[currentRecipeIdx].key}`
                  )
                  .setLabel("Quick Craft x1")
                  .setStyle(ButtonStyle.Primary)
              );
            }
            baseEmbed.setFooter({
              text: `Recipe ${currentRecipeIdx + 1} of 2`,
            });
            components = [navRow];
          } else if (petRecipes.length > 2) {
            navRow.addComponents(
              new ButtonBuilder()
                .setCustomId(
                  `pet_recipe_nav::petKey=${petKey}::idx=${
                    (currentRecipeIdx - 1 + petRecipes.length) %
                    petRecipes.length
                  }`
                )
                .setEmoji("⬅️")
                .setStyle(ButtonStyle.Secondary),
              new ButtonBuilder()
                .setCustomId(
                  `quickcraft_x1_${petRecipes[currentRecipeIdx].key}`
                )
                .setLabel("Quick Craft x1")
                .setStyle(ButtonStyle.Primary),
              new ButtonBuilder()
                .setCustomId(
                  `pet_recipe_nav::petKey=${petKey}::idx=${(currentRecipeIdx + 1) % petRecipes.length}`
                )
                .setEmoji("➡️")
                .setStyle(ButtonStyle.Secondary)
            );
            baseEmbed.setFooter({
              text: `Recipe ${currentRecipeIdx + 1} of ${petRecipes.length}`,
            });
            components = [navRow];
          }
        }

        const combined = components;
        if (interaction.isButton()) {
          let replyMessage;
          if (interaction.message && interaction.message.editable) {
            await interaction.update({
              embeds: [baseEmbed],
              components: combined,
            });
            replyMessage = interaction.message;
          } else {
            await interaction.editReply({
              embeds: [baseEmbed],
              components: combined,
            });
            replyMessage = await interaction.fetchReply();
          }

          if (petRecipes.length > 0 && replyMessage) {
            const collectorFilter = (i) =>
              i.user.id === interaction.user.id &&
              (i.customId.startsWith("quickcraft_x1_") ||
                i.customId.startsWith("pet_recipe_nav::"));
            const buttonCollector =
              replyMessage.createMessageComponentCollector({
                filter: collectorFilter,
                componentType: ComponentType.Button,
                time: 180000,
              });

            buttonCollector.on("collect", async (collectedInteraction) => {
              if (collectedInteraction.customId.startsWith("quickcraft_x1_")) {
                await collectedInteraction.deferUpdate();
                const itemKeyToCraft = collectedInteraction.customId.substring(
                  "quickcraft_x1_".length
                );
                const userId = collectedInteraction.user.id;

                if (!itemKeyToCraft) {
                  console.error(
                    `[Recipe QuickCraft Collector] Failed to parse itemKey from ${collectedInteraction.customId}`
                  );
                  return collectedInteraction.editReply({
                    content: "Error: Could not determine item to craft.",
                  });
                }

                try {
                  const craftResult = await craftingUtils.attemptCraft(
                    userId,
                    itemKeyToCraft,
                    1,
                    null,
                    collectedInteraction
                  );

                  if (craftResult.success) {
                    // get updated character data to show skill progress
                    const {
                      getPlayerData,
                    } = require("../utils/playerDataManager");
                    const updatedCharacter = await getPlayerData(userId);

                    // create proper action result embed with skill progress bar
                    const {
                      buildFinalSkillResultEmbed,
                    } = require("../utils/displayUtils");

                    const resultObject = {
                      title: "Crafted",
                      color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
                      skillName: "crafting",
                      exp: craftResult.craftingXpGained || 0,
                      petExp: craftResult.petXpGained || 0,
                      tamingExp: craftResult.tamingXpGained || 0,
                      finalCharacterData: updatedCharacter,
                      finalSkillData: {
                        exp: updatedCharacter.skills?.crafting?.exp || 0,
                      },
                      items: {
                        [craftResult.itemKey]: craftResult.amount,
                      },
                      consumedItems: craftResult.consumedItems || {},
                    };

                    const successEmbed =
                      buildFinalSkillResultEmbed(resultObject);
                    await collectedInteraction.message.edit({
                      embeds: [successEmbed],
                      components: [],
                    });

                    if (craftResult.uniqueCraftEmbed) {
                      await collectedInteraction.followUp({
                        embeds: [craftResult.uniqueCraftEmbed],
                      });
                    }
                  } else {
                    let errorDescription = craftResult.message;

                    // if it's an insufficient materials error, show detailed breakdown
                    if (
                      craftResult.needsDetailedBreakdown ||
                      (craftResult.message &&
                        (craftResult.message.includes(
                          "You don't have enough ingredients"
                        ) ||
                          (craftResult.message.includes("You need") &&
                            craftResult.message.includes("but you only have"))))
                    ) {
                      try {
                        const playerInventory = await getInventory(userId);
                        const recipe = allItems[itemKeyToCraft]?.recipes?.[0];

                        if (recipe && playerInventory) {
                          const breakdown = formatCraftingMaterialsBreakdown(
                            recipe,
                            playerInventory,
                            allItems,
                            1
                          );
                          errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
                        }
                      } catch (breakdownError) {
                        console.error(
                          "[Recipe QuickCraft] Error generating material breakdown:",
                          breakdownError
                        );
                        // fall back to original error message
                        errorDescription = craftResult.message;
                      }
                    }

                    const errorEmbed = new EmbedBuilder()
                      .setColor(require("../gameConfig").EMBED_COLORS.LIGHT_RED)
                      .setTitle("❌ Craft Failed")
                      .setDescription(errorDescription);

                    await collectedInteraction.followUp({
                      embeds: [errorEmbed],
                      ephemeral: true,
                    });
                  }
                } catch (error) {
                  console.error(
                    `[Recipe QuickCraft Collector] Error processing quickcraft for ${itemKeyToCraft}, user ${userId}:`,
                    error
                  );
                  await collectedInteraction
                    .editReply({
                      content:
                        "An unexpected error occurred while trying to quick craft.",
                    })
                    .catch(() => {});
                }
              } else if (
                collectedInteraction.customId.startsWith("pet_recipe_nav::")
              ) {
                await module.exports.handlePetRecipeNavigation(
                  collectedInteraction
                );
              }
            });

            buttonCollector.on("end", () => {
              if (replyMessage.editable) {
                const currentComponents = replyMessage.components;
                if (currentComponents && currentComponents.length > 0) {
                  const disabledComponents = currentComponents.map((row) => {
                    const newRow = new ActionRowBuilder();
                    row.components.forEach((component) => {
                      newRow.addComponents(
                        ButtonBuilder.from(component).setDisabled(true)
                      );
                    });
                    return newRow;
                  });
                  replyMessage
                    .edit({ components: disabledComponents })
                    .catch((e) => {
                      if (e.code !== 10008)
                        console.error(
                          "[Recipe QuickCraft Collector End] Failed to disable buttons:",
                          e
                        );
                    });
                }
              }
            });
          }
        } else {
          // This is the initial interaction (likely slash command)
          const replyMessage = await interaction.editReply({
            embeds: [baseEmbed],
            components,
          });

          if (petRecipes.length > 0 && replyMessage) {
            const collectorFilter = (i) =>
              i.user.id === interaction.user.id &&
              (i.customId.startsWith("quickcraft_x1_") ||
                i.customId.startsWith("pet_recipe_nav_"));
            const buttonCollector =
              replyMessage.createMessageComponentCollector({
                filter: collectorFilter,
                componentType: ComponentType.Button,
                time: 180000,
              });

            buttonCollector.on("collect", async (collectedInteraction) => {
              if (collectedInteraction.customId.startsWith("quickcraft_x1_")) {
                await collectedInteraction.deferReply();
                const itemKeyToCraft = collectedInteraction.customId.substring(
                  "quickcraft_x1_".length
                );
                const userId = collectedInteraction.user.id;

                if (!itemKeyToCraft) {
                  console.error(
                    `[Recipe QuickCraft Collector] Failed to parse itemKey from ${collectedInteraction.customId}`
                  );
                  return collectedInteraction.editReply({
                    content: "Error: Could not determine item to craft.",
                  });
                }

                try {
                  const craftResult = await craftingUtils.attemptCraft(
                    userId,
                    itemKeyToCraft,
                    1,
                    null,
                    collectedInteraction
                  );

                  if (craftResult.success) {
                    // get updated character data to show skill progress
                    const {
                      getPlayerData,
                    } = require("../utils/playerDataManager");
                    const updatedCharacter = await getPlayerData(userId);

                    // create proper action result embed with skill progress bar
                    const {
                      buildFinalSkillResultEmbed,
                    } = require("../utils/displayUtils");

                    const resultObject = {
                      title: "Crafted",
                      color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
                      skillName: "crafting",
                      exp: craftResult.craftingXpGained || 0,
                      petExp: craftResult.petXpGained || 0,
                      tamingExp: craftResult.tamingXpGained || 0,
                      finalCharacterData: updatedCharacter,
                      finalSkillData: {
                        exp: updatedCharacter.skills?.crafting?.exp || 0,
                      },
                      items: {
                        [craftResult.itemKey]: craftResult.amount,
                      },
                      consumedItems: craftResult.consumedItems || {},
                    };

                    const successEmbed =
                      buildFinalSkillResultEmbed(resultObject);
                    await collectedInteraction.message.edit({
                      embeds: [successEmbed],
                      components: [],
                    });

                    if (craftResult.uniqueCraftEmbed) {
                      await collectedInteraction.followUp({
                        embeds: [craftResult.uniqueCraftEmbed],
                      });
                    }
                  } else {
                    let errorDescription = craftResult.message;

                    // if it's an insufficient materials error, show detailed breakdown
                    if (
                      craftResult.needsDetailedBreakdown ||
                      (craftResult.message &&
                        (craftResult.message.includes(
                          "You don't have enough ingredients"
                        ) ||
                          (craftResult.message.includes("You need") &&
                            craftResult.message.includes("but you only have"))))
                    ) {
                      try {
                        const playerInventory = await getInventory(userId);
                        const recipe = allItems[itemKeyToCraft]?.recipes?.[0];

                        if (recipe && playerInventory) {
                          const breakdown = formatCraftingMaterialsBreakdown(
                            recipe,
                            playerInventory,
                            allItems,
                            1
                          );
                          errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
                        }
                      } catch (breakdownError) {
                        console.error(
                          "[Recipe QuickCraft] Error generating material breakdown:",
                          breakdownError
                        );
                        // fall back to original error message
                        errorDescription = craftResult.message;
                      }
                    }

                    const errorEmbed = new EmbedBuilder()
                      .setColor(require("../gameConfig").EMBED_COLORS.LIGHT_RED)
                      .setTitle("❌ Craft Failed")
                      .setDescription(errorDescription);

                    await collectedInteraction.editReply({
                      embeds: [errorEmbed],
                      components: [],
                    });
                  }
                } catch (error) {
                  console.error(
                    `[Recipe QuickCraft Collector] Error processing quickcraft for ${itemKeyToCraft}, user ${userId}:`,
                    error
                  );
                  await collectedInteraction
                    .editReply({
                      content:
                        "An unexpected error occurred while trying to quick craft.",
                    })
                    .catch(() => {});
                }
              } else if (
                collectedInteraction.customId.startsWith("pet_recipe_nav_")
              ) {
                await module.exports.handlePetRecipeNavigation(
                  collectedInteraction
                );
              }
            });

            buttonCollector.on("end", () => {
              if (replyMessage.editable) {
                const currentComponents = replyMessage.components;
                if (currentComponents && currentComponents.length > 0) {
                  const disabledComponents = currentComponents.map((row) => {
                    const newRow = new ActionRowBuilder();
                    row.components.forEach((component) => {
                      newRow.addComponents(
                        ButtonBuilder.from(component).setDisabled(true)
                      );
                    });
                    return newRow;
                  });
                  replyMessage
                    .edit({ components: disabledComponents })
                    .catch((e) => {
                      if (e.code !== 10008)
                        console.error(
                          "[Recipe QuickCraft Collector End] Failed to disable buttons:",
                          e
                        );
                    });
                }
              }
            });
          }
        }
        return;
      } else {
        // --- Handle Regular Items (Use original itemData) ---
        const recipes = Array.isArray(itemData.recipes) ? itemData.recipes : [];
        const hasRecipe = recipes.length > 0;

        if (hasRecipe) {
          // Iterate all recipes and display each separately
          for (let rIndex = 0; rIndex < recipes.length; rIndex++) {
            const recipe = recipes[rIndex];
            if (!recipe?.ingredients) continue;
            // unique items availability only for that recipe
            let uniqueItemsString = "";
            const uniqueItemsData = {};
            if (playerData) {
              try {
                const hasUniqueIngredient = recipe.ingredients.some(
                  (ing) => allItems[ing.itemKey]?.unique === true
                );
                if (hasUniqueIngredient) {
                  const playerInventoryForUnique = await getInventory(
                    playerData.discordId
                  );
                  const playerAccessories = await getPlayerAccessories(
                    playerData.discordId
                  );
                  const uniqueItemsInfo = [];
                  for (const ingredient of recipe.ingredients) {
                    const ingData = allItems[ingredient.itemKey];
                    if (ingData?.unique === true) {
                      const eligibleEquipment = (
                        playerInventoryForUnique.equipment || []
                      ).filter(
                        (eq) =>
                          eq.itemKey === ingredient.itemKey && !eq.isEquipped
                      );
                      const eligibleAccessories = (
                        playerAccessories || []
                      ).filter((acc) => acc.itemKey === ingredient.itemKey);
                      const eligibleItems = [
                        ...eligibleEquipment,
                        ...eligibleAccessories,
                      ];
                      uniqueItemsData[ingredient.itemKey] = eligibleItems;
                      const itemName = ingData.name || ingredient.itemKey;
                      const emoji = ingData.emoji || "❓";
                      if (eligibleItems.length > 0) {
                        const itemList = eligibleItems
                          .slice(0, 5)
                          .map((it) => `#${it.id.slice(0, 6)}`)
                          .join(", ");
                        const moreText =
                          eligibleItems.length > 5
                            ? ` (+${eligibleItems.length - 5} more)`
                            : "";
                        uniqueItemsInfo.push(
                          `${emoji} **${itemName}**: ${itemList}${moreText}`
                        );
                      } else {
                        uniqueItemsInfo.push(
                          `${emoji} **${itemName}**: ❌ None available`
                        );
                      }
                    }
                  }
                  if (uniqueItemsInfo.length > 0)
                    uniqueItemsString = uniqueItemsInfo.join("\n");
                }
              } catch (scanErr) {
                console.error(
                  "[Recipe Command] Unique item scan failed:",
                  scanErr
                );
              }
            }

            const ingredientsString = formatIngredients(
              recipe.ingredients,
              allItems,
              playerInventory,
              uniqueItemsData
            );
            const resultString = `${itemData.emoji || "❓"} \`${(recipe.resultAmount || 1).toLocaleString()}x ${itemData.name || itemKey}\``;
            // calculate craftable
            let ingredientsFieldName = `Recipe ${rIndex + 1} Ingredients`;
            if (playerInventory) {
              try {
                const craftableAmount = calculateCraftableAmount(
                  recipe.ingredients,
                  playerInventory
                );
                if (craftableAmount > 0)
                  ingredientsFieldName += ` (You can craft ${craftableAmount.toLocaleString()})`;
              } catch {
                /* ignore calc failure */
              }
            }
            baseEmbed.addFields(
              {
                name: ingredientsFieldName,
                value: ingredientsString,
                inline: false,
              },
              {
                name: `Recipe ${rIndex + 1} Result`,
                value: resultString,
                inline: false,
              }
            );
            if (uniqueItemsString) {
              baseEmbed.addFields({
                name: `Recipe ${rIndex + 1} Unique Items`,
                value: uniqueItemsString,
                inline: false,
              });
            }
          }
        } else {
          baseEmbed.addFields({
            name: "Crafting",
            value: "*This item cannot be crafted.*",
            inline: false,
          });
        }

        baseEmbed.addFields({
          name: "Requirements",
          value: formatRequirements(playerData, itemData, allItems),
          inline: false,
        });

        const statsString = formatStats(itemData.baseStats);
        if (statsString) {
          baseEmbed.addFields({
            name: "Stats",
            value: statsString,
            inline: false,
          });
        }

        // --- Add navigation UI for MINIONS and PETS ---
        let replyMessage; // Declare replyMessage here to be accessible for collectors

        if (
          itemData.type === "MINION" &&
          itemData.tiers &&
          itemData.tiers.length > 1
        ) {
          const initialTier = 1; // Always show tier 1 initially
          const { embed: minionEmbed, components: minionComponents } =
            await buildMinionTierEmbedAndComponents(
              itemData,
              itemKey,
              initialTier,
              allItems
            );

          if (interaction.isButton() && interaction.message?.editable) {
            // If from a pet_recipe_nav button, update that message
            await interaction.update({
              embeds: [minionEmbed],
              components: minionComponents,
            });
            replyMessage = interaction.message;
          } else {
            // Initial slash command reply
            await interaction.editReply({
              embeds: [minionEmbed],
              components: minionComponents,
            });
            replyMessage = await interaction.fetchReply();
          }

          // Collector for MINION navigation buttons
          if (replyMessage && minionComponents.length > 0) {
            const minionNavFilter = (i) =>
              i.user.id === interaction.user.id &&
              (i.customId.startsWith("minion_left") ||
                i.customId.startsWith("minion_right") ||
                i.customId.startsWith("minion_tier_modal"));
            const minionNavCollector =
              replyMessage.createMessageComponentCollector({
                filter: minionNavFilter,
                componentType: ComponentType.Button,
                time: 180000,
              }); // 3 minutes

            minionNavCollector.on("collect", async (collectedInteraction) => {
              // handleMinionTierNavigation will call deferUpdate or showModal itself, and then handle the modal submit internally.
              await handleMinionTierNavigation(collectedInteraction);
            });

            minionNavCollector.on("end", () => {
              if (replyMessage.editable) {
                const currentComponents = replyMessage.components;
                if (currentComponents && currentComponents.length > 0) {
                  const disabledComponents = currentComponents.map((row) => {
                    const newRow = new ActionRowBuilder();
                    row.components.forEach((component) => {
                      newRow.addComponents(
                        ButtonBuilder.from(component).setDisabled(true)
                      );
                    });
                    return newRow;
                  });
                  replyMessage
                    .edit({ components: disabledComponents })
                    .catch((e) => {
                      if (e.code !== 10008 && e.code !== 10062)
                        console.error(
                          "[Recipe MinionNav Collector End] Failed to disable buttons:",
                          e
                        );
                    });
                }
              }
            });
          }
          return; // Handled minion display
        } else if (itemData.type === "PET" && itemData.rarities) {
          // For PETS (existing rarity navigation, distinct from pet *recipe* nav)
          const left = new ButtonBuilder()
            .setCustomId("pet_left")
            .setEmoji("⬅️")
            .setStyle(ButtonStyle.Secondary);
          const right = new ButtonBuilder()
            .setCustomId("pet_right")
            .setEmoji("➡️")
            .setStyle(ButtonStyle.Secondary);
          const modal = new ButtonBuilder()
            .setCustomId("pet_rarity_modal")
            .setLabel("Go to Rarity...")
            .setStyle(ButtonStyle.Primary);
          const navRow = new ActionRowBuilder().addComponents(
            left,
            modal,
            right
          );
          baseEmbed.setFooter({
            text: "Use arrows or modal to view other rarities (feature coming soon)",
          });

          const components = [navRow];
          if (interaction.isButton()) {
            await interaction.update({ embeds: [baseEmbed], components });
          } else {
            await interaction.editReply({ embeds: [baseEmbed], components });
          }
          return;
        }

        let components = [];
        if (
          hasRecipe &&
          itemData.type !== "MINION" &&
          itemData.type !== "PET"
        ) {
          if (itemData.recipes.length === 1) {
            const craftButton = new ButtonBuilder()
              .setCustomId("recipe_craft_button::r=0")
              .setLabel("Craft...")
              .setStyle(ButtonStyle.Primary);
            components = [new ActionRowBuilder().addComponents(craftButton)];
          } else if (itemData.recipes.length > 1) {
            // Build select menu for choosing recipe (with emojis)
            const options = itemData.recipes.map((r, i) => {
              const ingPreview = r.ingredients
                .slice(0, 3)
                .map((ing) => {
                  const ingData = allItems[ing.itemKey];
                  return `${ing.amount || 1}x ${ingData?.name || ing.itemKey}`;
                })
                .join(", ");
              // try to use target item emoji for option emoji for quick visual diff
              let optEmoji = null;
              try {
                const raw = itemData.emoji;
                if (raw) {
                  if (/^<a?:/.test(raw)) {
                    const match = raw.match(/^<a?:([^:]+):(\d+)>$/);
                    if (match)
                      optEmoji = {
                        name: match[1],
                        id: match[2],
                        animated: raw.startsWith("<a:"),
                      };
                  } else if (/^\p{Emoji}$/u.test(raw) || raw.length <= 3) {
                    // unicode emoji
                    optEmoji = { name: raw }; // discord.js handles unicode by name char
                  }
                }
              } catch {
                /* ignore collector cleanup error */
              }
              const base = {
                label: `Recipe ${i + 1}`,
                description: ingPreview.substring(0, 95) || "Select",
                value: `r:${i}`,
              };
              if (optEmoji) base.emoji = optEmoji;
              return base;
            });
            const select = new StringSelectMenuBuilder()
              .setCustomId(`recipe_pick::item=${itemKey}`)
              .setPlaceholder("Select a recipe")
              .addOptions(options);
            components = [new ActionRowBuilder().addComponents(select)];
          }
        }

        if (interaction.isButton()) {
          await interaction.update({ embeds: [baseEmbed], components });
        } else {
          await interaction.editReply({ embeds: [baseEmbed], components });
        }
        // Add collector for Craft button
        if (
          components.length > 0 &&
          components[0].components.some(
            (c) => c.data.custom_id === "recipe_craft_button"
          )
        ) {
          const replyMessage = interaction.isButton()
            ? interaction.message
            : await interaction.fetchReply();
          const filter = (i) =>
            i.user.id === interaction.user.id &&
            i.customId.startsWith("recipe_craft_button");
          const collector = replyMessage.createMessageComponentCollector({
            filter,
            componentType: ComponentType.Button,
            time: 180000,
          });
          collector.on("collect", async (collectedInteraction) => {
            const m = collectedInteraction.customId.match(/::r=(\d+)/);
            const rIndex = m ? parseInt(m[1], 10) : 0;
            await module.exports.handleRecipeCraftButton(
              collectedInteraction,
              itemKey,
              rIndex
            );
          });
        }
        // collector for recipe select
        if (
          components.length &&
          components[0].components[0]?.data?.custom_id?.startsWith(
            "recipe_pick::"
          )
        ) {
          const replyMessage = interaction.isButton()
            ? interaction.message
            : await interaction.fetchReply();
          const selectFilter = (i) =>
            i.user.id === interaction.user.id &&
            i.customId === `recipe_pick::item=${itemKey}`;
          const selectCollector = replyMessage.createMessageComponentCollector({
            filter: selectFilter,
            componentType: ComponentType.StringSelect,
            time: 180000,
          });
          selectCollector.on("collect", async (collected) => {
            const val = collected.values[0];
            const idxMatch = val.match(/r:(\d+)/);
            const rIndex = idxMatch ? parseInt(idxMatch[1], 10) : 0;
            const chosen = itemData.recipes[rIndex];
            const hasUnique = chosen.ingredients.some(
              (ing) => allItems[ing.itemKey]?.unique === true
            );
            if (hasUnique) {
              // reuse craft button handler path
              await module.exports.handleRecipeCraftButton(
                collected,
                itemKey,
                rIndex
              );
              // clear the original select after crafting attempt (success path inside handler will also clear)
              try {
                await collected.message.edit({ components: [] });
              } catch {
                /* ignore edit cleanup error */
              }
              return;
            }
            // show modal for amount
            try {
              const {
                ModalBuilder,
                TextInputBuilder,
                TextInputStyle,
                ActionRowBuilder,
              } = require("discord.js");
              const modal = new ModalBuilder()
                .setCustomId(
                  `recipe_craft_modal::itemKey=${itemKey}::r=${rIndex}`
                )
                .setTitle("Craft Item");
              const amountInput = new TextInputBuilder()
                .setCustomId("craft_amount_input")
                .setLabel('Amount (number or "max")')
                .setPlaceholder("1 or max")
                .setStyle(TextInputStyle.Short)
                .setRequired(true)
                .setValue("1");
              modal.addComponents(
                new ActionRowBuilder().addComponents(amountInput)
              );
              await collected.showModal(modal);
            } catch (e) {
              console.error(
                "[Recipe] Failed to show modal after recipe select:",
                e
              );
              await collected
                .reply({
                  ephemeral: true,
                  content: "Could not open craft modal.",
                })
                .catch(() => {});
            }
          });
        }
        return;
      }
    } catch (error) {
      console.error("[Recipe Command] Error executing command:", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "An unexpected error occurred while trying to view the recipe. Please report this.",
          })
          .catch(() => {});
      } else {
        await interaction
          .editReply({
            content:
              "An unexpected error occurred while trying to view the recipe. Please report this.",
          })
          .catch(() => {});
      }
    }
  },
  async autocomplete(interaction) {
    try {
      const focusedValue = interaction.options.getFocused().toLowerCase();
      const allItemsObject = configManager.getAllItems(); // Get the raw object { KEY: itemData, ... }
      const { itemMatchesSearch } = require("../utils/itemSearchUtils");

      const filteredChoices = Object.entries(allItemsObject) // Get [ [KEY, itemData], ... ]
        .filter(([itemKey, itemData]) => {
          if (!itemData || !itemData.name) return false;
          // Exclude tier/variant entries
          if (
            /\(Tier \d+\)/i.test(itemData.name) ||
            /_TIER_\d+$/i.test(itemKey)
          )
            return false;
          // For pets, include if it has direct crafting recipes or is obtainable
          if (itemData.type === "PET") {
            // Only include the base pet (not eggs/variants)
            const isBasePet = !itemData.resultPetKey && !!itemData.rarities;
            if (!isBasePet) return false;

            // Include pets that have direct crafting recipes
            const hasDirectRecipe =
              itemData.recipes &&
              Array.isArray(itemData.recipes) &&
              itemData.recipes.length > 0;

            // Include special pets (Squid from fishing, Bee from NPC)
            const isSpecialPet =
              itemKey === "SQUID_PET" || itemKey === "BEE_PET";

            if (hasDirectRecipe || isSpecialPet) {
              return itemMatchesSearch(itemKey, itemData, focusedValue);
            }
            return false;
          }
          // If item has a recipe, include it
          const hasRecipe =
            itemData?.recipes &&
            Array.isArray(itemData.recipes) &&
            itemData.recipes.length > 0;
          if (hasRecipe) {
            return itemMatchesSearch(itemKey, itemData, focusedValue);
          }
          // For minions, include the base item if any variant has a recipe (as before)
          if (itemData.type === "MINION") {
            const baseKey = itemKey;
            const hasVariantWithRecipe = Object.entries(allItemsObject).some(
              ([otherKey, otherData]) => {
                if (otherKey === baseKey) return false;
                if (!otherKey.startsWith(baseKey)) return false;
                return (
                  otherData?.recipes &&
                  Array.isArray(otherData.recipes) &&
                  otherData.recipes.length > 0
                );
              }
            );
            if (hasVariantWithRecipe) {
              return itemMatchesSearch(itemKey, itemData, focusedValue);
            }
          }
          return false;
        })
        .slice(0, 25) // Limit choices
        .map(([itemKey, itemData]) => ({
          // Destructure again for mapping
          name: String(itemData.name), // REMOVED EMOJI: Use only the item name
          value: itemKey, // Use the KEY from Object.entries as the value
        }));

      await interaction.respond(filteredChoices);
    } catch (error) {
      console.error("[Recipe Autocomplete] Error:", error);
      try {
        await interaction.respond([]);
      } catch (respondError) {
        console.error(
          "[Recipe Autocomplete] Failed to send empty response after error:",
          respondError
        );
      }
    }
  },
  handleMinionTierNavigation,
  async handlePetRecipeNavigation(interaction) {
    // Only allow the original user to use the navigation
    const originalMessage = interaction.message;
    if (
      !originalMessage ||
      !originalMessage.interaction ||
      !originalMessage.interaction.user
    ) {
      console.warn(
        "[handlePetRecipeNavigation] Missing original message.interaction context or user. Interaction ID:",
        interaction.id
      );
      if (!interaction.replied && !interaction.deferred) {
        try {
          return interaction.reply({
            content:
              "Cannot navigate pet recipes, message context lost. Please try the command again.",
          });
        } catch (e) {
          console.error(
            "Error replying to interaction with missing context:",
            e
          );
        }
      }
      return;
    }
    const originalUserId = originalMessage.interaction.user.id;
    if (interaction.user.id !== originalUserId) {
      return interaction.reply({
        content: "You cannot use this navigation for someone else.",
      });
    }

    const embed = interaction.message.embeds[0];
    if (!embed || !embed.title) {
      return interaction.reply({
        content: "Could not parse recipe info from embed.",
      });
    }
    const allItems = configManager.getAllItems();

    // Get player data for inventory display
    let playerInventory = null;
    try {
      const playerData = await getPlayerData(interaction.user.id);
      if (playerData) {
        playerInventory = await getInventory(interaction.user.id);
      }
    } catch {
      console.log(
        `[Pet Recipe Navigation] Player ${interaction.user.id} not found or error retrieving data, proceeding without inventory display.`
      );
    }
    // --- PET RECIPE HANDLING ---
    const petRecipeMatch = embed.title.match(/Recipe: (.+)$/);
    if (petRecipeMatch) {
      const petName = petRecipeMatch[1].trim();
      const petEntry = Object.entries(allItems).find(
        ([, item]) => item.type === "PET" && item.name === petName
      );
      if (!petEntry) {
        return interaction.reply({
          content: "Could not find pet definition.",
        });
      }
      const [petKey, petData] = petEntry;

      // Check if pet has direct crafting recipe
      if (!petData.recipes || petData.recipes.length === 0) {
        if (petKey === "SQUID_PET") {
          return interaction.reply({
            content:
              "🎣 **Squid Pet** is obtained through fishing, not crafting.\n\nTry fishing in water with some <:treasure_chance:1369190619385167882> **Treasure Chance** to increase your odds!",
          });
        } else if (petKey === "BEE_PET") {
          return interaction.reply({
            content:
              "🛒 **Bee Pet** can be purchased from **Bea** NPC at different rarities.\n\n<:npc_bea:1368683794197909595> **Bea** (The Hub): 5,000 coins (Common)",
          });
        } else {
          return interaction.reply({
            content: "No crafting recipe found for this pet.",
          });
        }
      }

      // Handle direct pet crafting recipe
      const recipe = petData.recipes[0];
      const ingredientsString = formatIngredients(
        recipe.ingredients,
        allItems,
        playerInventory,
        null
      );

      // Create recipe embed for direct pet crafting
      const recipeEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.LIGHT_BLUE)
        .setTitle(`Recipe: ${petData.name}`)
        .setDescription("Crafting")
        .addFields(
          {
            name: "Ingredients",
            value: ingredientsString,
            inline: false,
          },
          {
            name: "Produces",
            value: `${petData.emoji || "🐾"} **Common ${petData.name}**`,
            inline: false,
          }
        );

      // Add collection requirements if they exist
      if (petData.craftingRequirements?.collections) {
        let requirementsStr = "";
        for (const [collectionKey, level] of Object.entries(
          petData.craftingRequirements.collections
        )) {
          const collectionItem = allItems[collectionKey];
          const collectionName = collectionItem
            ? collectionItem.name
            : collectionKey;
          const collectionEmoji = collectionItem ? collectionItem.emoji : "❓";
          requirementsStr += `${collectionEmoji} **${collectionName} Collection Level ${level}**\n`;
        }

        recipeEmbed.addFields({
          name: "Requirements",
          value: requirementsStr.trim(),
          inline: false,
        });
      }

      // Add upgrade information
      const {
        getUpgradeRequirements,
        getNextRarity,
      } = require("../utils/petUpgradeUtils");
      const upgradeReqs = getUpgradeRequirements(petKey, "COMMON", 1);

      if (upgradeReqs) {
        const nextRarity = getNextRarity("COMMON");
        const { ITEM_RARITY } = require("../data/itemRarities");
        const nextRarityInfo = ITEM_RARITY[nextRarity];

        let upgradeStr = `**Upgrade to ${nextRarityInfo.name}:**\n`;
        upgradeStr += `<:purse_coins:1367849116033482772> **${upgradeReqs.coins.toLocaleString()} coins**\n`;

        // Show materials
        if (upgradeReqs.materials && upgradeReqs.materials.length > 0) {
          upgradeStr += "**Materials:**\n";
          for (const material of upgradeReqs.materials) {
            const materialItem = allItems[material.itemKey];
            const materialName = materialItem
              ? materialItem.name
              : material.itemKey;
            const materialEmoji = materialItem ? materialItem.emoji : "❓";
            upgradeStr += `${materialEmoji} ${material.amount.toLocaleString()}x ${materialName}\n`;
          }
        }

        upgradeStr +=
          "\n*Upgrade via <:npc_kat:1375244764575826011> **Kat** in The Hub*";

        recipeEmbed.addFields({
          name: "Pet Upgrade",
          value: upgradeStr.trim(),
          inline: false,
        });
      }

      return interaction.reply({ embeds: [recipeEmbed] });
    }
    // --- MINION RECIPE HANDLING (fallback to old logic) ---
    // Title format: '<emoji> Minion: <Name> - Tier <N>'
    const titleMatch = embed.title.match(/Minion: (.+) - Tier (\d+)/);
    if (!titleMatch) {
      return interaction.reply({
        content: "Could not parse minion info from embed title.",
      });
    }
    const minionName = titleMatch[1].trim();
    const currentTier = parseInt(titleMatch[2], 10);
    const minionEntry = Object.entries(allItems).find(
      ([, item]) => item.type === "MINION" && item.name === minionName
    );
    if (!minionEntry) {
      return interaction.reply({
        content: "Could not find minion definition.",
      });
    }
    const [minionKey, minionData] = minionEntry;
    const validTiers = minionData.tiers.filter((t) => t != null);
    const totalTiers = validTiers.length - 1;
    // Handle left/right/modal
    if (interaction.isButton()) {
      let newTier = currentTier;
      if (interaction.customId === "minion_left") {
        newTier = Math.max(1, currentTier - 1);
      } else if (interaction.customId === "minion_right") {
        newTier = Math.min(totalTiers, currentTier + 1);
      } else if (interaction.customId === "minion_tier_modal") {
        const modal = new ModalBuilder()
          .setCustomId("minion_tier_modal_submit")
          .setTitle("Go to Minion Tier...")
          .addComponents(
            new ActionRowBuilder().addComponents(
              new TextInputBuilder()
                .setCustomId("minion_tier_input")
                .setLabel("Enter Tier (number)")
                .setStyle(TextInputStyle.Short)
                .setRequired(true)
                .setPlaceholder(`1 - ${totalTiers}`)
            )
          );
        return interaction.showModal(modal);
      } else {
        return interaction.reply({
          content: "Unknown navigation button.",
        });
      }
      if (newTier === currentTier) {
        return interaction.deferUpdate(); // No change
      }
      return updateMinionTierEmbed(
        interaction,
        minionData,
        minionKey,
        newTier,
        allItems
      );
    } else if (interaction.isModalSubmit()) {
      // Modal submit: get tier from input
      const tierInput =
        interaction.fields.getTextInputValue("minion_tier_input");
      const newTier = parseInt(tierInput, 10);
      if (isNaN(newTier) || newTier < 1 || newTier > totalTiers) {
        return interaction.reply({
          content: `Invalid tier. Enter a number between 1 and ${totalTiers}.`,
        });
      }
      // Build the new embed and components
      const embedAndComponents = await buildMinionTierEmbedAndComponents(
        minionData,
        minionKey,
        newTier,
        allItems
      );
      if (interaction.message && interaction.message.edit) {
        try {
          await interaction.message.edit({
            embeds: [embedAndComponents.embed],
            components: embedAndComponents.components,
          });
          await interaction.deferUpdate();
        } catch (err) {
          if (
            err.code === 10008 ||
            (err.rawError && err.rawError.code === 10008)
          ) {
            // Unknown Message
            await interaction.reply({
              embeds: [embedAndComponents.embed],
              components: embedAndComponents.components,
              ephemeral: true,
            });
          } else {
            throw err;
          }
        }
      } else {
        await interaction.reply({
          embeds: [embedAndComponents.embed],
          components: embedAndComponents.components,
          ephemeral: true,
        });
      }
      return;
    }
  },
  formatRequirements,
  formatStats,
  async handleRecipeCraftModal(interaction) {
    // Immediately acknowledge the modal to close it
    try {
      if (!interaction.deferred && !interaction.replied) {
        // Send a zero-width ephemeral ack to close the modal without the 'thinking' bubble
        await interaction.reply({ ephemeral: true, content: "\u200b" });
      }
    } catch (e) {
      // If defer fails (rare), continue; we'll try to reply/edit below
      console.error("[Recipe Craft Modal] deferReply failed:", e);
    }

    const amountInput =
      interaction.fields.getTextInputValue("craft_amount_input");
    let recipeIndexInput = null;
    try {
      recipeIndexInput = interaction.fields.getTextInputValue(
        "craft_recipe_index_input"
      );
    } catch {
      /* optional */
    }
    const userId = interaction.user.id;

    // Prefer parsing itemKey from modal customId (state-agnostic)
    let itemKey = null;
    let recipeIndexFromId = 0;
    if (typeof interaction.customId === "string") {
      const idMatch = interaction.customId.match(
        /recipe_craft_modal::itemKey=([^:]+)(?::|$)/
      );
      if (idMatch) itemKey = idMatch[1];
      const rMatch = interaction.customId.match(/::r=(\d+)/);
      if (rMatch) recipeIndexFromId = parseInt(rMatch[1], 10) || 0;
    }

    const allItems = configManager.getAllItems();

    // Fallback: try to parse from the original embed title if present
    if (!itemKey) {
      const embed = interaction.message?.embeds?.[0];
      if (embed && embed.title) {
        const match = embed.title.match(/Recipe: (.+)$/);
        if (match) {
          const found = Object.entries(allItems).find(
            ([, item]) => item.name === match[1].trim()
          );
          if (found) {
            itemKey = found[0];
          }
        }
      }
    }

    if (!itemKey) {
      return interaction.followUp({
        content: "Could not determine item to craft.",
        ephemeral: true,
      });
    }

    const itemData = allItems[itemKey];

    let craftAmount;
    const MAX_CRAFT_LIMIT = 1000;

    // First, determine intended recipe index (user may have typed it) BEFORE computing max
    let recipeIndex = recipeIndexFromId;
    try {
      if (recipeIndexInput && itemData?.recipes?.length > 1) {
        const parsed = parseInt(recipeIndexInput, 10);
        if (
          Number.isInteger(parsed) &&
          parsed >= 1 &&
          parsed <= itemData.recipes.length
        ) {
          recipeIndex = parsed - 1; // convert to 0-based
        }
      }
    } catch {
      /* ignore bad index */
    }

    const recipeForAmount = itemData?.recipes?.[recipeIndex || 0];

    if (amountInput.toLowerCase() === "max") {
      const playerInventory = await getInventory(userId);
      if (!recipeForAmount) {
        return interaction.followUp({
          content:
            "❌ Error: Could not find recipe information to calculate max.",
          ephemeral: true,
        });
      }
      craftAmount = craftingUtils.calculateMaxCraftable(
        {
          items: playerInventory.items,
          equipment: playerInventory.equipment || [],
        },
        recipeForAmount,
        configManager.getAllItems()
      );
      if (!Number.isFinite(craftAmount) || craftAmount <= 0) {
        return interaction.followUp({
          content: "❌ You don't have the required ingredients to craft any.",
          ephemeral: true,
        });
      }
      if (craftAmount > MAX_CRAFT_LIMIT) {
        craftAmount = MAX_CRAFT_LIMIT;
        await interaction.followUp({
          content: `⚠️ You can only craft up to ${MAX_CRAFT_LIMIT} at once. Setting amount to ${MAX_CRAFT_LIMIT}.`,
          ephemeral: true,
        });
      }
    } else {
      craftAmount = parseInt(amountInput, 10);
      if (!Number.isFinite(craftAmount) || craftAmount <= 0) {
        return interaction.followUp({
          content: 'Invalid amount. Please enter a positive number or "max".',
          ephemeral: true,
        });
      }
      if (craftAmount > MAX_CRAFT_LIMIT) {
        craftAmount = MAX_CRAFT_LIMIT;
        await interaction.followUp({
          content: `⚠️ You can only craft up to ${MAX_CRAFT_LIMIT} at once. Setting amount to ${MAX_CRAFT_LIMIT}.`,
          ephemeral: true,
        });
      }
    }

    // recipeIndex already determined above

    try {
      const craftResult = await craftingUtils.attemptCraft(
        userId,
        itemKey,
        craftAmount,
        null,
        interaction,
        { recipeIndex }
      );

      if (craftResult.success) {
        // get updated character data to show skill progress
        const { getPlayerData } = require("../utils/playerDataManager");
        const updatedCharacter = await getPlayerData(userId);

        // create proper action result embed with skill progress bar
        const { buildFinalSkillResultEmbed } = require("../utils/displayUtils");

        const resultObject = {
          title: "Crafted",
          color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
          skillName: "crafting",
          exp: craftResult.craftingXpGained || 0,
          petExp: craftResult.petXpGained || 0,
          tamingExp: craftResult.tamingXpGained || 0,
          finalCharacterData: updatedCharacter,
          finalSkillData: {
            exp: updatedCharacter.skills?.crafting?.exp || 0,
          },
          items: {
            [craftResult.itemKey]: craftResult.amount,
          },
          consumedItems: craftResult.consumedItems || {},
        };

        const successEmbed = buildFinalSkillResultEmbed(resultObject);
        if (itemData.recipes && itemData.recipes.length > 1) {
          successEmbed.setFooter({ text: `Used Recipe #${recipeIndex + 1}` });
        }

        // Edit the original /recipe message instead of sending a new one
        try {
          const msg =
            interaction.message ||
            (interaction.channel && (await interaction.fetchReply()));
          if (msg && msg.edit) {
            await msg.edit({ embeds: [successEmbed], components: [] });
          } else {
            await interaction.followUp({ embeds: [successEmbed] });
          }
        } catch {
          // Fallback to a follow-up
          await interaction.followUp({ embeds: [successEmbed] });
        }

        // Clean up the ephemeral ack message
        try {
          await interaction.deleteReply();
        } catch {
          /* ignore delete fail */
        }

        if (craftResult.uniqueCraftEmbed) {
          await interaction.followUp({
            embeds: [craftResult.uniqueCraftEmbed],
          });
        }
      } else {
        let errorDescription = craftResult.message;

        // if it's an insufficient materials error, show detailed breakdown
        if (
          craftResult.needsDetailedBreakdown ||
          (craftResult.message &&
            (craftResult.message.includes(
              "You don't have enough ingredients"
            ) ||
              (craftResult.message.includes("You need") &&
                craftResult.message.includes("but you only have"))))
        ) {
          try {
            const playerInventory = await getInventory(userId);
            const recipe = itemData?.recipes?.[0];

            if (recipe && playerInventory) {
              const breakdown = formatCraftingMaterialsBreakdown(
                recipe,
                playerInventory,
                allItems,
                craftAmount
              );
              errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
            }
          } catch (breakdownError) {
            console.error(
              "[Recipe Modal] Error generating material breakdown:",
              breakdownError
            );
            // fall back to original error message
            errorDescription = craftResult.message;
          }
        }

        const errorEmbed = new EmbedBuilder()
          .setColor(require("../gameConfig").EMBED_COLORS.LIGHT_RED)
          .setTitle("❌ Craft Failed")
          .setDescription(errorDescription);

        await interaction.followUp({
          embeds: [errorEmbed],
          ephemeral: true,
        });
        try {
          await interaction.deleteReply();
        } catch {
          /* ignore delete fail */
        }
      }
    } catch (error) {
      console.error(
        `[Recipe Craft Modal] Error processing craft for ${itemKey}, user ${userId}:`,
        error
      );

      await interaction.followUp({
        content: "An unexpected error occurred while trying to craft.",
        ephemeral: true,
      });
      try {
        await interaction.deleteReply();
      } catch {
        /* ignore delete fail */
      }
    }
    return;
  },
  async handleRecipeCraftButton(interaction, itemKey, forcedRecipeIndex = 0) {
    // await interaction.deferReply(); // removed unconditional defer; will be called when needed

    const userId = interaction.user.id;
    const allItems = configManager.getAllItems();
    const itemData = allItems[itemKey];

    if (!itemData) {
      if (!interaction.deferred && !interaction.replied) {
        return interaction.reply({
          content: `❌ Unknown item: ${itemKey}`,
          ephemeral: true,
        });
      } else {
        return interaction.followUp({
          content: `❌ Unknown item: ${itemKey}`,
          ephemeral: true,
        });
      }
    }

    const recipes = Array.isArray(itemData.recipes) ? itemData.recipes : [];
    const recipeIndex =
      Number.isInteger(forcedRecipeIndex) &&
      forcedRecipeIndex >= 0 &&
      forcedRecipeIndex < recipes.length
        ? forcedRecipeIndex
        : 0;
    const recipe = recipes[recipeIndex];
    if (!recipe) {
      if (!interaction.deferred && !interaction.replied) {
        return interaction.reply({
          content: "❌ No crafting recipe found for this item.",
          ephemeral: true,
        });
      } else {
        return interaction.followUp({
          content: "❌ No crafting recipe found for this item.",
          ephemeral: true,
        });
      }
    }

    // Check if any ingredient is a unique item
    const hasUniqueIngredient = recipe.ingredients.some((ing) => {
      const ingData = allItems[ing.itemKey];
      return ingData?.unique === true;
    });

    if (hasUniqueIngredient) {
      // We will send follow-up messages, so defer the initial reply
      await interaction.deferReply();
      // Find all unique ingredients
      const uniqueIngredients = recipe.ingredients
        .map((ing) => ({
          ...ing,
          itemData: allItems[ing.itemKey],
        }))
        .filter((ing) => ing.itemData?.unique === true);

      if (uniqueIngredients.length === 0) {
        return interaction.followUp({
          content: "❌ Error: No unique ingredients found in recipe.",
        });
      }

      const playerInventory = await getInventory(userId);
      const playerAccessories = await getPlayerAccessories(userId);

      // Check if we have multiple unique ingredients
      if (uniqueIngredients.length > 1) {
        // Multi-select approach for multiple unique ingredients
        const allEligibleItems = [];
        const uniqueIngredientInfo = [];

        for (const uniqueIngredient of uniqueIngredients) {
          const eligibleEquipment = (playerInventory.equipment || []).filter(
            (eq) => eq.itemKey === uniqueIngredient.itemKey && !eq.isEquipped
          );

          const eligibleAccessories = (playerAccessories || []).filter(
            (acc) => acc.itemKey === uniqueIngredient.itemKey
          );

          const eligibleItems = [...eligibleEquipment, ...eligibleAccessories];

          if (eligibleItems.length === 0) {
            const itemName =
              allItems[uniqueIngredient.itemKey]?.name ||
              uniqueIngredient.itemKey;
            const emoji = allItems[uniqueIngredient.itemKey]?.emoji || "";
            return interaction.followUp({
              content: `❌ You don't have any available ${emoji} ${itemName} to use for this craft.`,
            });
          }

          uniqueIngredientInfo.push({
            itemKey: uniqueIngredient.itemKey,
            itemName:
              allItems[uniqueIngredient.itemKey]?.name ||
              uniqueIngredient.itemKey,
            emoji: allItems[uniqueIngredient.itemKey]?.emoji || "",
            count: eligibleItems.length,
          });

          allEligibleItems.push(
            ...eligibleItems.map((item) => ({
              ...item,
              requiredForIngredient: uniqueIngredient.itemKey,
            }))
          );
        }

        // Check total item count (Discord limit is 25 per select menu)
        if (allEligibleItems.length > 25) {
          return interaction.followUp({
            content:
              "You have too many eligible items. Please reduce your inventory to 25 or fewer to craft.",
          });
        }

        const options = allEligibleItems
          .sort((a, b) => {
            // Sort by ingredient type first, then by item name
            if (a.requiredForIngredient !== b.requiredForIngredient) {
              return a.requiredForIngredient.localeCompare(
                b.requiredForIngredient
              );
            }
            const nameA = allItems[a.itemKey]?.name || a.itemKey;
            const nameB = allItems[b.itemKey]?.name || b.itemKey;
            return nameA.localeCompare(nameB) || a.id.localeCompare(b.id);
          })
          .map((item) => {
            const itemName = allItems[item.itemKey]?.name || item.itemKey;
            const requiredForName =
              allItems[item.requiredForIngredient]?.name ||
              item.requiredForIngredient;
            return {
              label: `${itemName} #${item.id.slice(0, 6)}`,
              description: requiredForName,
              value: `${item.requiredForIngredient}:${item.id}`,
            };
          });

        const selectionId = `recipe_craft_multi_select_${Date.now()}`;
        const selectMenu = new StringSelectMenuBuilder()
          .setCustomId(selectionId)
          .setPlaceholder(
            `Select ${uniqueIngredients.length} items (one of each type)`
          )
          .addOptions(options)
          .setMinValues(uniqueIngredients.length)
          .setMaxValues(uniqueIngredients.length);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const selectionEmbed =
          craftingUtils.createMultiUniqueItemSelectionEmbed(
            itemData.name,
            itemData.emoji,
            uniqueIngredientInfo
          );

        const replyMessage = await interaction.followUp({
          embeds: [selectionEmbed],
          components: [row],
        });

        const collectorFilter = (i) =>
          i.customId === selectionId && i.user.id === userId;

        try {
          const collectedInteraction = await replyMessage.awaitMessageComponent(
            {
              filter: collectorFilter,
              componentType: ComponentType.StringSelect,
              time: 60000, // 60 seconds
            }
          );

          // Parse selected items and validate
          const selectedItems = collectedInteraction.values.map((value) => {
            const [ingredientKey, itemId] = value.split(":");
            return { ingredientKey, itemId };
          });

          // Validate that we have exactly one item for each unique ingredient
          const selectedIngredientKeys = selectedItems.map(
            (item) => item.ingredientKey
          );
          const requiredIngredientKeys = uniqueIngredients.map(
            (ing) => ing.itemKey
          );

          const missingIngredients = requiredIngredientKeys.filter(
            (key) => !selectedIngredientKeys.includes(key)
          );

          if (missingIngredients.length > 0) {
            await collectedInteraction.reply({
              content: `❌ You must select exactly one item for each unique ingredient. Missing: ${missingIngredients.join(", ")}`,
              ephemeral: true,
            });
            return;
          }

          // Check for duplicates
          const uniqueSelected = new Set(selectedIngredientKeys);
          if (uniqueSelected.size !== selectedIngredientKeys.length) {
            await collectedInteraction.reply({
              content: `❌ You cannot select multiple items of the same type. Please select exactly one item for each unique ingredient.`,
              ephemeral: true,
            });
            return;
          }

          await collectedInteraction.deferUpdate();

          // Craft using multiple selected items
          const craftResult = await craftingUtils.attemptCraft(
            userId,
            itemKey,
            1,
            selectedItems,
            collectedInteraction,
            { recipeIndex }
          );

          if (craftResult.success) {
            // get updated character data to show skill progress
            const { getPlayerData } = require("../utils/playerDataManager");
            const updatedCharacter = await getPlayerData(userId);

            // create proper action result embed with skill progress bar
            const {
              buildFinalSkillResultEmbed,
            } = require("../utils/displayUtils");

            const resultObject = {
              title: "Crafted",
              color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
              skillName: "crafting",
              exp: craftResult.craftingXpGained || 0,
              petExp: craftResult.petXpGained || 0,
              tamingExp: craftResult.tamingXpGained || 0,
              finalCharacterData: updatedCharacter,
              finalSkillData: {
                exp: updatedCharacter.skills?.crafting?.exp || 0,
              },
              items: {
                [craftResult.itemKey]: craftResult.amount,
              },
              consumedItems: craftResult.consumedItems || {},
            };

            const successEmbed = buildFinalSkillResultEmbed(resultObject);
            if (itemData.recipes && itemData.recipes.length > 1) {
              successEmbed.setFooter({
                text: `Used Recipe #${recipeIndex + 1}`,
              });
            }
            await collectedInteraction.message.edit({
              embeds: [successEmbed],
              components: [],
            });

            if (craftResult.uniqueCraftEmbed) {
              await collectedInteraction.followUp({
                embeds: [craftResult.uniqueCraftEmbed],
              });
            }
          } else {
            let errorDescription = craftResult.message;

            // if it's an insufficient materials error, show detailed breakdown
            if (
              craftResult.needsDetailedBreakdown ||
              (craftResult.message &&
                (craftResult.message.includes(
                  "You don't have enough ingredients"
                ) ||
                  (craftResult.message.includes("You need") &&
                    craftResult.message.includes("but you only have"))))
            ) {
              try {
                const playerInventory = await getInventory(userId);
                const recipe = itemData?.recipes?.[0];

                if (recipe && playerInventory) {
                  // create map of selected unique items for the breakdown
                  const selectedUniqueItems = {};
                  if (selectedItems && selectedItems.length > 0) {
                    // count selected items by ingredient key
                    for (const item of selectedItems) {
                      const count =
                        selectedUniqueItems[item.ingredientKey] || 0;
                      selectedUniqueItems[item.ingredientKey] = count + 1;
                    }

                    // If no unique ingredients, open a modal for amount (and recipe choice if multiple)
                    try {
                      const {
                        ModalBuilder,
                        TextInputBuilder,
                        TextInputStyle,
                        ActionRowBuilder,
                      } = require("discord.js");
                      const modal = new ModalBuilder()
                        .setCustomId(`recipe_craft_modal::itemKey=${itemKey}`)
                        .setTitle("Craft Item");

                      const amountInput = new TextInputBuilder()
                        .setCustomId("craft_amount_input")
                        .setLabel('Amount (number or "max")')
                        .setPlaceholder("1 or max")
                        .setStyle(TextInputStyle.Short)
                        .setRequired(true)
                        .setValue("1");

                      const rows = [
                        new ActionRowBuilder().addComponents(amountInput),
                      ];
                      if (recipes.length > 1) {
                        const recipeInput = new TextInputBuilder()
                          .setCustomId("craft_recipe_index_input")
                          .setLabel(`Recipe # (1-${recipes.length})`) // user-friendly index
                          .setPlaceholder("1")
                          .setStyle(TextInputStyle.Short)
                          .setRequired(false);
                        rows.push(
                          new ActionRowBuilder().addComponents(recipeInput)
                        );
                      }
                      modal.addComponents(...rows);
                      await interaction.showModal(modal);
                    } catch (err) {
                      console.error(
                        "[Recipe] Failed to show craft modal:",
                        err
                      );
                      if (!interaction.replied && !interaction.deferred) {
                        await interaction
                          .reply({
                            content: "Could not open craft modal.",
                            ephemeral: true,
                          })
                          .catch(() => {});
                      } else {
                        await interaction
                          .followUp({
                            content: "Could not open craft modal.",
                            ephemeral: true,
                          })
                          .catch(() => {});
                      }
                    }
                    return;
                  }

                  const breakdown = formatCraftingMaterialsBreakdown(
                    recipe,
                    playerInventory,
                    allItems,
                    1,
                    selectedUniqueItems
                  );
                  errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
                }
              } catch (breakdownError) {
                console.error(
                  "[Recipe Multi-Select] Error generating material breakdown:",
                  breakdownError
                );
                // fall back to original error message
                errorDescription = craftResult.message;
              }
            }

            const errorEmbed = new EmbedBuilder()
              .setColor(require("../gameConfig").EMBED_COLORS.LIGHT_RED)
              .setTitle("❌ Craft Failed")
              .setDescription(errorDescription);

            await collectedInteraction.editReply({
              embeds: [errorEmbed],
              content: null,
              components: [],
            });
          }
        } catch (error) {
          console.error(
            "[Recipe Craft] Error in multi-select collector:",
            error
          );
          await interaction
            .followUp({
              content: "❌ Selection timed out or an error occurred.",
            })
            .catch(() => {});
        }
        return;
      }

      // Single unique ingredient handling (existing logic)
      const uniqueIngredient = uniqueIngredients[0];
      const eligibleEquipment = (playerInventory.equipment || []).filter(
        (eq) => eq.itemKey === uniqueIngredient.itemKey && !eq.isEquipped
      );

      const eligibleAccessories = (playerAccessories || []).filter(
        (acc) => acc.itemKey === uniqueIngredient.itemKey
      );

      const eligibleItems = [...eligibleEquipment, ...eligibleAccessories];

      if (eligibleItems.length === 0) {
        const itemName =
          allItems[uniqueIngredient.itemKey]?.name || uniqueIngredient.itemKey;
        const emoji = allItems[uniqueIngredient.itemKey]?.emoji || "";
        return interaction.followUp({
          content: `❌ You don't have any available ${emoji} ${itemName} to use for this craft.`,
        });
      }

      if (eligibleItems.length > 25) {
        return interaction.followUp({
          content:
            "You have too many eligible items. Please reduce your inventory to 25 or fewer to craft.",
        });
      }

      // Build select menu options
      const options = eligibleItems
        .sort((a, b) => {
          const nameA = allItems[a.itemKey]?.name || a.itemKey;
          const nameB = allItems[b.itemKey]?.name || b.itemKey;
          return nameA.localeCompare(nameB) || a.id.localeCompare(b.id);
        })
        .map((item) => ({
          label: `${allItems[item.itemKey]?.name || item.itemKey} #${item.id.slice(0, 6)}`,
          description: item.isEquipped
            ? "Equipped"
            : eligibleAccessories.some(
                  (acc) =>
                    acc.accessory_id === item.id ||
                    acc.accessory_id === item.accessory_id
                )
              ? "Accessory"
              : undefined,
          value: item.id,
        }));

      // Generate a unique ID for this specific selection
      const selectionId = `recipe_craft_select_${Date.now()}`;

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(selectionId)
        .setPlaceholder("Select which item to use")
        .addOptions(options);

      const row = new ActionRowBuilder().addComponents(selectMenu);

      const itemName =
        allItems[uniqueIngredient.itemKey]?.name || uniqueIngredient.itemKey;
      const emoji = allItems[uniqueIngredient.itemKey]?.emoji || "";
      const selectionEmbed = craftingUtils.createUniqueItemSelectionEmbed(
        itemName,
        emoji
      );

      const replyMessage = await interaction.followUp({
        embeds: [selectionEmbed],
        components: [row],
      });

      // Create a filter that only matches this specific selection
      const collectorFilter = (i) =>
        i.customId === selectionId && i.user.id === userId;

      try {
        const collectedInteraction = await replyMessage.awaitMessageComponent({
          filter: collectorFilter,
          componentType: ComponentType.StringSelect,
          time: 60000, // 60 seconds
        });

        const selectedEquipmentId = collectedInteraction.values[0];

        await collectedInteraction.deferUpdate();

        // For unique items, we'll craft 1 directly
        const craftResult = await craftingUtils.attemptCraft(
          userId,
          itemKey,
          1,
          selectedEquipmentId,
          collectedInteraction,
          { recipeIndex }
        );

        if (craftResult.success) {
          // get updated character data to show skill progress
          const { getPlayerData } = require("../utils/playerDataManager");
          const updatedCharacter = await getPlayerData(userId);

          // create proper action result embed with skill progress bar
          const {
            buildFinalSkillResultEmbed,
          } = require("../utils/displayUtils");

          const resultObject = {
            title: "Crafted",
            color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
            skillName: "crafting",
            exp: craftResult.craftingXpGained || 0,
            petExp: craftResult.petXpGained || 0,
            tamingExp: craftResult.tamingXpGained || 0,
            finalCharacterData: updatedCharacter,
            finalSkillData: {
              exp: updatedCharacter.skills?.crafting?.exp || 0,
            },
            items: {
              [craftResult.itemKey]: craftResult.amount,
            },
            consumedItems: craftResult.consumedItems || {},
          };

          const successEmbed = buildFinalSkillResultEmbed(resultObject);

          await collectedInteraction.editReply({
            embeds: [successEmbed],
            components: [],
          });

          if (craftResult.uniqueCraftEmbed) {
            await collectedInteraction.followUp({
              embeds: [craftResult.uniqueCraftEmbed],
            });
          }
        } else {
          let errorDescription = craftResult.message;

          // if it's an insufficient materials error, show detailed breakdown
          if (
            craftResult.needsDetailedBreakdown ||
            (craftResult.message &&
              (craftResult.message.includes(
                "You don't have enough ingredients"
              ) ||
                (craftResult.message.includes("You need") &&
                  craftResult.message.includes("but you only have"))))
          ) {
            try {
              const playerInventory = await getInventory(userId);
              const recipe = itemData?.recipes?.[0];

              if (recipe && playerInventory) {
                // create map of selected unique items for the breakdown
                const selectedUniqueItems = {};
                if (selectedEquipmentId) {
                  // we know one unique item was selected
                  const uniqueIngredients = recipe.ingredients.filter((ing) => {
                    const ingData = allItems[ing.itemKey];
                    return ingData?.unique === true;
                  });
                  if (uniqueIngredients.length > 0) {
                    selectedUniqueItems[uniqueIngredients[0].itemKey] = 1; // 1 item selected
                  }
                }

                const breakdown = formatCraftingMaterialsBreakdown(
                  recipe,
                  playerInventory,
                  allItems,
                  1,
                  selectedUniqueItems
                );
                errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
              }
            } catch (breakdownError) {
              console.error(
                "[Recipe Button] Error generating material breakdown:",
                breakdownError
              );
              // fall back to original error message
              errorDescription = craftResult.message;
            }
          }

          const errorEmbed = new EmbedBuilder()
            .setColor(require("../gameConfig").EMBED_COLORS.LIGHT_RED)
            .setTitle("❌ Craft Failed")
            .setDescription(errorDescription);

          await collectedInteraction.editReply({
            embeds: [errorEmbed],
            content: null,
            components: [],
          });
        }
      } catch (error) {
        console.error("[Recipe Craft] Error in unique item selection:", error);
        await interaction
          .followUp({
            content: "❌ Selection timed out or an error occurred.",
          })
          .catch(() => {});
      }
    } else {
      // No unique ingredients: show modal directly for amount (supports recipe index)
      try {
        const {
          ModalBuilder,
          TextInputBuilder,
          TextInputStyle,
          ActionRowBuilder,
        } = require("discord.js");
        const modal = new ModalBuilder()
          .setCustomId(
            `recipe_craft_modal::itemKey=${itemKey}::r=${recipeIndex}`
          )
          .setTitle("Craft Item");
        const amountInput = new TextInputBuilder()
          .setCustomId("craft_amount_input")
          .setLabel('Amount (number or "max")')
          .setPlaceholder("1 or max")
          .setStyle(TextInputStyle.Short)
          .setRequired(true)
          .setValue("1");
        modal.addComponents(new ActionRowBuilder().addComponents(amountInput));
        await interaction.showModal(modal);
      } catch (err) {
        console.error("[Recipe] Failed to open craft modal:", err);
        if (!interaction.replied && !interaction.deferred) {
          await interaction
            .reply({ ephemeral: true, content: "Could not open craft modal." })
            .catch(() => {});
        } else {
          await interaction
            .followUp({
              ephemeral: true,
              content: "Could not open craft modal.",
            })
            .catch(() => {});
        }
      }
    }
  },
};
