// Migration Script: 018_migrate_minion_data_format.js
// Purpose: Converts player minion data from tiered item keys (e.g., "COBBLESTONE_MINION_T5")
//          to the new format using a base item key and a tier property
//          (e.g., { itemKey: "COBBLESTONE_MINION", tier: 5, id: "...", ... }).

// Path not needed in this migration
// REMOVE const { getPlayerData, savePlayerData, getAllPlayerIds } = require('../../utils/playerDataManager');
const { v4: uuidv4 } = require("uuid");

// Regex to extract base key and tier from old tiered keys
const TIERED_KEY_REGEX = /^(.*)_T(\d+)$/;

// Function to perform the migration - NOW ACCEPTS playerDataUtils
async function up(db, playerDataUtils) {
  // Destructure the passed functions
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("Running minion data format migration (018)...");
  let processedCount = 0;
  let migratedCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log("No player IDs found. Migration step complete.");
    return;
  }

  console.log(`Found ${playerIds.length} players to check for migration 018.`);

  for (const discordId of playerIds) {
    processedCount++;
    if (processedCount % 50 === 0) {
      console.log(
        `Migration 018: Processed ${processedCount}/${playerIds.length}...`,
      );
    }

    try {
      const player = await getPlayerData(discordId);
      if (!player) {
        // console.warn(` - Skipping ${discordId} in migration 018: Could not load player data.`);
        continue;
      }

      let needsSave = false;

      // --- Migrate minionStorage ---
      if (player.minionStorage && Array.isArray(player.minionStorage)) {
        const newMinionStorage = [];
        for (const oldMinionData of player.minionStorage) {
          if (typeof oldMinionData === "string") {
            // Handle very old format (just tiered key string)
            const match = oldMinionData.match(TIERED_KEY_REGEX);
            if (match) {
              const baseKey = match[1];
              const tier = parseInt(match[2], 10);
              newMinionStorage.push({
                itemKey: baseKey,
                tier: tier,
                id: uuidv4(), // Generate new ID as old one wasn't stored
                resourcesStored: {},
                lastCollectionTimestamp: null, // Or Date.now()? Set to null for safety
              });
              needsSave = true;
            } else {
              console.warn(
                `  - Player ${discordId}: Found invalid string in minionStorage (Mig 018): ${oldMinionData}. Keeping as is.`,
              );
              newMinionStorage.push(oldMinionData); // Keep invalid data? Or discard? Keeping for now.
            }
          } else if (
            typeof oldMinionData === "object" &&
            oldMinionData !== null &&
            oldMinionData.key &&
            !oldMinionData.itemKey
          ) {
            // Handle object format with old 'key' property
            const match = oldMinionData.key.match(TIERED_KEY_REGEX);
            if (match) {
              const baseKey = match[1];
              const tier = parseInt(match[2], 10);
              newMinionStorage.push({
                itemKey: baseKey,
                tier:
                  oldMinionData.tier !== undefined ? oldMinionData.tier : tier, // Prefer existing tier if present
                id: oldMinionData.id || uuidv4(), // Use existing ID if available
                resourcesStored: oldMinionData.resourcesStored || {},
                lastCollectionTimestamp: oldMinionData.lastCollectionTimestamp, // Keep existing timestamp
                // Copy other existing properties if necessary
              });
              needsSave = true;
            } else {
              console.warn(
                `  - Player ${discordId}: Found object in minionStorage with invalid key format (Mig 018): ${oldMinionData.key}. Keeping as is.`,
              );
              newMinionStorage.push(oldMinionData); // Keep invalid data
            }
          } else if (
            typeof oldMinionData === "object" &&
            oldMinionData !== null &&
            oldMinionData.itemKey &&
            oldMinionData.tier !== undefined
          ) {
            // Already in new format (or close enough), keep it
            newMinionStorage.push(oldMinionData);
          } else {
            // Unexpected format
            console.warn(
              `  - Player ${discordId}: Found unexpected data in minionStorage (Mig 018):`,
              oldMinionData,
              ". Keeping as is.",
            );
            newMinionStorage.push(oldMinionData);
          }
        }
        if (needsSave) {
          player.minion_storage_json = newMinionStorage;
        }
      }

      // --- Migrate island.placedMinions ---
      if (
        player.island?.placedMinions &&
        Array.isArray(player.island.placedMinions)
      ) {
        const newPlacedMinions = [];
        let placedChanged = false; // Track changes specifically for this array
        for (const oldPlacedMinion of player.island.placedMinions) {
          if (
            typeof oldPlacedMinion === "object" &&
            oldPlacedMinion !== null &&
            oldPlacedMinion.key &&
            !oldPlacedMinion.itemKey
          ) {
            // Object format with old 'key' property
            const match = oldPlacedMinion.key.match(TIERED_KEY_REGEX);
            if (match) {
              const baseKey = match[1];
              const tier = parseInt(match[2], 10);
              newPlacedMinions.push({
                itemKey: baseKey,
                tier:
                  oldPlacedMinion.tier !== undefined
                    ? oldPlacedMinion.tier
                    : tier,
                id: oldPlacedMinion.id || uuidv4(),
                resourcesStored: oldPlacedMinion.resourcesStored || {},
                lastCollectionTimestamp:
                  oldPlacedMinion.lastCollectionTimestamp,
                // Copy other existing properties
              });
              needsSave = true;
              placedChanged = true;
            } else {
              console.warn(
                `  - Player ${discordId}: Found object in placedMinions with invalid key format (Mig 018): ${oldPlacedMinion.key}. Keeping as is.`,
              );
              newPlacedMinions.push(oldPlacedMinion);
            }
          } else if (
            typeof oldPlacedMinion === "object" &&
            oldPlacedMinion !== null &&
            oldPlacedMinion.itemKey &&
            oldPlacedMinion.tier !== undefined
          ) {
            // Already in new format
            newPlacedMinions.push(oldPlacedMinion);
          } else {
            // Unexpected format
            console.warn(
              `  - Player ${discordId}: Found unexpected data in placedMinions (Mig 018):`,
              oldPlacedMinion,
              ". Keeping as is.",
            );
            newPlacedMinions.push(oldPlacedMinion);
          }
        }
        if (placedChanged) {
          // Ensure island object exists if we made changes
          if (!player.island_json) player.island_json = {};
          player.island.placedMinions = newPlacedMinions;
        }
      }

      // Save if any changes were made
      if (needsSave) {
        await savePlayerData(discordId, player);
        migratedCount++;
        // console.log(`  - Migrated data for ${discordId} in migration 018.`); // Optional: less verbose log
      }
    } catch (error) {
      console.error(
        `  - Error processing player ${discordId} in migration 018:`,
        error,
      );
      errorCount++;
    }
  }

  console.log("-----------------------------------------");
  console.log("Minion data format migration (018) finished.");
  console.log(`Total Players Checked: ${processedCount}`);
  console.log(`Players Migrated:      ${migratedCount}`);
  console.log(`Errors Encountered:    ${errorCount}`);
  console.log("-----------------------------------------");

  // If errors occurred, throw an error to potentially stop the overall migration process in bot.js
  if (errorCount > 0) {
    throw new Error(`Migration 018 completed with ${errorCount} errors.`);
  }
}

// Export the migration function
module.exports = { up };

// REMOVE // Run the migration
// REMOVE runMigration().catch(err => {
// REMOVE     console.error("Migration script failed with error:", err);
// REMOVE     process.exit(1);
// REMOVE });
