// Migration: 058_add_gems_currency.js
// Purpose: Add gems currency column for premium purchases via Stripe

const { dbRun, dbAll } = require("../utils/dbUtils");

async function up() {
  console.log(
    "[Migration 058] Adding gems currency column to players table...",
  );

  try {
    // Check if column already exists
    const columns = await dbAll("PRAGMA table_info(players)");
    const columnExists = columns.some((col) => col.name === "gems");

    if (columnExists) {
      console.log("[Migration 058] gems column already exists, skipping...");
      return;
    }

    console.log("[Migration 058] Adding gems column to players table...");
    await dbRun(`
      ALTER TABLE players 
      ADD COLUMN gems INTEGER DEFAULT 0
    `);

    console.log("[Migration 058] Successfully added gems column");
  } catch (error) {
    console.error("[Migration 058] Error adding gems column:", error);
    throw error;
  }
}

async function down() {
  console.log("[Migration 058] Removing gems column from players table...");

  try {
    // SQLite doesn't support DROP COLUMN directly, so we'd need to recreate the table
    // For now, we'll just log that this migration cannot be easily reversed
    console.log(
      "[Migration 058] Note: SQLite does not support DROP COLUMN. Manual intervention required to reverse.",
    );
  } catch (error) {
    console.error("[Migration 058] Error in down migration:", error);
    throw error;
  }
}

module.exports = { up, down };
