/**
 * Automatic Stop Button Migration on Bot Startup
 * Runs automatically when bot starts to ensure all active actions have working stop buttons
 */

const { getActiveActions } = require("./actionPersistence");
const {
  ensureUnifiedStopButton,
  migrateToUnifiedStopButton,
} = require("./unifiedStopButtons");

/**
 * Automatically migrates all active actions to unified stop buttons on bot startup
 * @param {import('discord.js').Client} client - Discord client
 * @returns {Promise<void>}
 */
async function autoMigrateOnStartup(client) {
  console.log(
    "[AutoMigration] Starting automatic stop button migration on bot startup..."
  );

  try {
    // Wait a moment for bot to be fully ready
    await new Promise((resolve) => setTimeout(resolve, 2000));

    const activeActions = await getActiveActions();
    console.log(
      `[AutoMigration] Found ${activeActions.length} active actions to migrate`
    );

    if (activeActions.length === 0) {
      console.log("[AutoMigration] No active actions found, migration skipped");
      return;
    }

    let migrated = 0;
    let failed = 0;
    let skipped = 0;

    // Process in batches to avoid rate limiting
    const batchSize = 5;
    for (let i = 0; i < activeActions.length; i += batchSize) {
      const batch = activeActions.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async (action) => {
          try {
            const {
              id: actionId,
              user_id: userId,
              action_type: actionType,
              channel_id: channelId,
              message_id: messageId,
            } = action;

            if (!channelId || !messageId) {
              skipped++;
              return;
            }

            const channel = await client.channels
              .fetch(channelId)
              .catch(() => null);
            if (!channel) {
              skipped++;
              return;
            }

            const message = await channel.messages
              .fetch(messageId)
              .catch(() => null);
            if (!message) {
              skipped++;
              return;
            }

            // Check if already has unified button
            const hasUnified = message.components?.some((row) =>
              row.components.some(
                (component) =>
                  component.customId &&
                  component.customId.startsWith(`unified_stop:${actionId}:`)
              )
            );

            if (hasUnified) {
              skipped++;
              return;
            }

            // Migrate to unified format
            const success = await migrateToUnifiedStopButton(
              message,
              actionId,
              userId,
              `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
            );

            if (success) {
              migrated++;
              console.log(
                `[AutoMigration] Migrated ${actionType} action ${actionId}`
              );
            } else {
              // Try adding unified button if migration didn't work
              const added = await ensureUnifiedStopButton(
                message,
                actionId,
                userId,
                `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
              );
              if (added) {
                migrated++;
                console.log(
                  `[AutoMigration] Added unified button to ${actionType} action ${actionId}`
                );
              } else {
                failed++;
              }
            }
          } catch (error) {
            console.warn(
              `[AutoMigration] Error processing action ${action.id}:`,
              error.message
            );
            failed++;
          }
        })
      );

      // Small delay between batches
      if (i + batchSize < activeActions.length) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    console.log(
      `[AutoMigration] Startup migration complete: ${migrated} migrated, ${failed} failed, ${skipped} skipped`
    );
  } catch (error) {
    console.error("[AutoMigration] Error during startup migration:", error);
  }
}

module.exports = {
  autoMigrateOnStartup,
};
