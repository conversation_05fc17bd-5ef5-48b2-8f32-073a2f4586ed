// Migration: 055_add_garden_visitors_system.js
// Purpose: Add Garden Visitors system with copper currency and visitor tracking

module.exports.up = async function up(db) {
  return new Promise((resolve, reject) => {
    // Add copper currency column
    db.run("ALTER TABLE players ADD COLUMN copper INTEGER DEFAULT 0", (err) => {
      if (err && !err.message.includes("duplicate column")) {
        console.error(
          "[Migration 055] Failed to add copper column:",
          err.message,
        );
        reject(err);
        return;
      }

      // Add garden visitor timer column
      db.run(
        "ALTER TABLE players ADD COLUMN garden_visitor_timer INTEGER DEFAULT 0",
        (err2) => {
          if (err2 && !err2.message.includes("duplicate column")) {
            console.error(
              "[Migration 055] Failed to add garden_visitor_timer column:",
              err2.message,
            );
            reject(err2);
            return;
          }

          // Add visitor milestone tracking columns
          db.run(
            "ALTER TABLE players ADD COLUMN visitor_offers_accepted INTEGER DEFAULT 0",
            (err3) => {
              if (err3 && !err3.message.includes("duplicate column")) {
                console.error(
                  "[Migration 055] Failed to add visitor_offers_accepted column:",
                  err3.message,
                );
                reject(err3);
                return;
              }

              db.run(
                'ALTER TABLE players ADD COLUMN visitor_unique_served TEXT DEFAULT "[]"',
                (err4) => {
                  if (err4 && !err4.message.includes("duplicate column")) {
                    console.error(
                      "[Migration 055] Failed to add visitor_unique_served column:",
                      err4.message,
                    );
                    reject(err4);
                    return;
                  }

                  // Create active garden visitors table
                  db.run(
                    `
                                            CREATE TABLE IF NOT EXISTS active_garden_visitors (
                                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                                discord_id TEXT NOT NULL,
                                                visitor_key TEXT NOT NULL,
                                                visitor_rarity TEXT NOT NULL,
                                                request_items TEXT NOT NULL,
                                                reward_garden_xp INTEGER NOT NULL,
                                                reward_farming_xp INTEGER NOT NULL,
                                                reward_copper INTEGER NOT NULL,
                                                bonus_rewards TEXT DEFAULT "[]",
                                                created_at INTEGER NOT NULL,
                                                FOREIGN KEY (discord_id) REFERENCES players (discord_id)
                                            )
                                        `,
                    (err5) => {
                      if (err5) {
                        console.error(
                          "[Migration 055] Failed to create active_garden_visitors table:",
                          err5.message,
                        );
                        reject(err5);
                        return;
                      }

                      console.log(
                        "[Migration 055] Garden Visitors system added successfully.",
                      );
                      resolve();
                    },
                  );
                },
              );
            },
          );
        },
      );
    });
  });
};
