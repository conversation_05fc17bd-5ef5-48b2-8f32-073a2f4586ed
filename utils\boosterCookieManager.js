/**
 * Booster Cookie Manager
 * Handles booster cookie activation, stat bonuses, and effects
 */

const { savePlayerData } = require("./playerDataManager");

// booster cookie constants
const BOOSTER_COOKIE_DURATION_MS = 4 * 24 * 60 * 60 * 1000; // 4 days in milliseconds
const BITS_BASE_AMOUNT = 4800; // base bits per cookie
const BITS_COLLECTION_INTERVAL_MS = 30 * 60 * 1000; // 30 minutes in milliseconds
const BITS_COLLECTION_PERCENTAGE = 0.052083; // 5.2083% per collection

/**
 * Activates booster cookie effect for a player
 * @param {string} userId - The player's Discord ID
 * @param {Object} character - The player's character data
 * @param {number} cookieCount - Number of cookies to consume
 * @returns {Object} Result with success status and new expiry time
 */
async function activateBoosterCookie(userId, character, cookieCount = 1) {
  const currentTime = Date.now();
  const durationToAdd = BOOSTER_COOKIE_DURATION_MS * cookieCount;

  // recalculate bits available based on current multiplier (in case it was manually changed)
  const originalBitsAvailable = character.bits_available || 0;
  character = recalculateBitsFromMultiplier(character);
  const newBitsAvailable = character.bits_available || 0;
  const bitsAvailableChanged = originalBitsAvailable !== newBitsAvailable;

  // save the recalculated bits available if it changed, before adding new bits
  if (bitsAvailableChanged) {
    await savePlayerData(userId, character, ["bits_available"]);
  }

  // calculate new expiry time
  let newExpiryTime;
  if (
    character.booster_cookie_expiry &&
    character.booster_cookie_expiry > currentTime
  ) {
    // extend existing effect
    newExpiryTime = character.booster_cookie_expiry + durationToAdd;
  } else {
    // start new effect
    newExpiryTime = currentTime + durationToAdd;
  }

  // track base bits earned from cookies (without multiplier)
  const baseBitsToAdd = BITS_BASE_AMOUNT * cookieCount;
  const totalBaseBits = (character.base_bits_from_cookies || 0) + baseBitsToAdd;

  // calculate total bits available based on current multiplier
  const bitsMultiplier = character.bits_multiplier || 1.0;
  const totalBitsEarned = Math.floor(totalBaseBits * bitsMultiplier);
  const bitsAlreadyCollected = character.bits || 0;
  const bitsAvailable = Math.max(0, totalBitsEarned - bitsAlreadyCollected);

  // update character data
  character.booster_cookie_expiry = newExpiryTime;
  character.base_bits_from_cookies = totalBaseBits; // track base bits without multiplier
  character.bits_available = bitsAvailable; // recalculated available bits
  character.bits_multiplier = bitsMultiplier; // ensure it exists
  character.last_bits_collection = currentTime; // reset collection timer

  // save to database
  await savePlayerData(userId, character, [
    "booster_cookie_expiry",
    "base_bits_from_cookies",
    "bits_available",
    "bits_multiplier",
    "last_bits_collection",
  ]);

  return {
    success: true,
    newExpiryTime,
    baseBitsAdded: baseBitsToAdd,
    totalBitsAvailable: bitsAvailable,
    totalBaseBits: totalBaseBits,
  };
}

/**
 * Checks if a player has an active booster cookie effect
 * @param {Object} character - The player's character data
 * @returns {boolean} Whether booster cookie is active
 */
function isBoosterCookieActive(character) {
  if (!character.booster_cookie_expiry) return false;
  return character.booster_cookie_expiry > Date.now();
}

/**
 * Gets remaining time for booster cookie effect
 * @param {Object} character - The player's character data
 * @returns {number} Remaining time in milliseconds, or 0 if not active
 */
function getBoosterCookieTimeRemaining(character) {
  if (!isBoosterCookieActive(character)) return 0;
  return character.booster_cookie_expiry - Date.now();
}

/**
 * Gets the stat bonuses provided by booster cookie
 * @returns {Object} Object with stat bonuses
 */
function getBoosterCookieStatBonuses() {
  const { STATS } = require("../gameConfig");

  const bonuses = {};

  // dynamically add +25 to all wisdom stats
  Object.keys(STATS).forEach((statKey) => {
    if (statKey.endsWith("_WISDOM")) {
      bonuses[statKey] = 25;
    }
  });

  // +15 magic find
  bonuses.MAGIC_FIND = 15;

  return bonuses;
}

/**
 * Formats remaining time for display
 * @param {number} timeMs - Time in milliseconds
 * @returns {string} Formatted time string
 */
function formatTimeRemaining(timeMs) {
  if (timeMs <= 0) return "0s";

  const totalSeconds = Math.floor(timeMs / 1000);
  const days = Math.floor(totalSeconds / 86400);
  const hours = Math.floor((totalSeconds % 86400) / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);

  return parts.join(" ");
}

/**
 * Recalculates available bits when multiplier changes
 * This ensures all previous cookies benefit from new multipliers
 * @param {Object} character - The player's character data
 * @returns {Object} Updated character data
 */
function recalculateBitsFromMultiplier(character) {
  const baseBits = character.base_bits_from_cookies || 0;
  const bitsMultiplier = character.bits_multiplier || 1.0;
  const bitsAlreadyCollected = character.bits || 0;

  // calculate total bits that should be earned from all cookies
  const totalBitsEarned = Math.floor(baseBits * bitsMultiplier);

  // available bits = total possible - already collected
  const bitsAvailable = Math.max(0, totalBitsEarned - bitsAlreadyCollected);

  character.bits_available = bitsAvailable;

  return character;
}

module.exports = {
  activateBoosterCookie,
  isBoosterCookieActive,
  getBoosterCookieTimeRemaining,
  getBoosterCookieStatBonuses,
  formatTimeRemaining,
  recalculateBitsFromMultiplier,
  // constants for external use
  BOOSTER_COOKIE_DURATION_MS,
  BITS_BASE_AMOUNT,
  BITS_COLLECTION_INTERVAL_MS,
  BITS_COLLECTION_PERCENTAGE,
};
