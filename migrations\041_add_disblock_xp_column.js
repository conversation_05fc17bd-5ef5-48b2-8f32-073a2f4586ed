// Migration to add disblock_xp column for Disblock XP system

const MIGRATION_VERSION = 41;

async function up(db) {
  return new Promise((resolve, reject) => {
    console.log(
      `[Migration ${MIGRATION_VERSION}] Applying migration: Add disblock_xp column`,
    );

    // Add disblock_xp column with default value of 0
    db.run(
      "ALTER TABLE players ADD COLUMN disblock_xp INTEGER DEFAULT 0",
      function (err) {
        if (err) {
          if (err.message.includes("duplicate column name")) {
            console.log(
              `[Migration ${MIGRATION_VERSION}] Column disblock_xp already exists, skipping.`,
            );
            resolve();
          } else {
            console.error(
              `[Migration ${MIGRATION_VERSION}] Error adding disblock_xp column:`,
              err,
            );
            reject(err);
          }
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully added disblock_xp column.`,
          );
          resolve();
        }
      },
    );
  });
}

module.exports = {
  version: MIGRATION_VERSION,
  up,
  // rollbackMigration
};
