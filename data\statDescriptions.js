// descriptions for what each stat does in the game
const { wrapText } = require("../utils/textUtils");

const STAT_DESCRIPTIONS = {
  HEALTH: {
    title: "Health",
    description: wrapText("Your Health stat increases your maximum health"),
  },
  INTELLIGENCE: {
    title: "Intelligence",
    description: wrapText(
      "Intelligence increases the damage of your magical items and your mana pool"
    ),
  },
  DEFENSE: {
    title: "Defense",
    description: wrapText(
      "Your Defense stat reduces the damage that you take from enemies"
    ),
  },
  STRENGTH: {
    title: "Strength",
    description: wrapText("Strength increases the damage you deal"),
  },
  CRIT_CHANCE: {
    title: "Crit Chance",
    description: wrapText(
      "Critical Chance is the percent chance that you land a Critical Hit when damaging an enemy"
    ),
  },
  CRIT_DAMAGE: {
    title: "Crit Damage",
    description: wrapText(
      "Critical Damage multiplies the damage that you deal when you land a Critical Hit"
    ),
  },
  HEALTH_REGEN: {
    title: "Health Regen",
    description: wrapText(
      "Health Regen increases the amount of health that you naturally regenerate over time"
    ),
  },
  VITALITY: {
    title: "Vitality",
    description: wrapText(
      "Vitality increases your incoming healing, including health regen"
    ),
  },
  DODGE_CHANCE: {
    title: "Dodge Chance",
    description: wrapText(
      "Dodge Chance is the percentage chance to completely avoid a hit from enemies"
    ),
  },
  FISHING_SPEED: {
    title: "Fishing Speed",
    description: wrapText(
      "Fishing Speed reduces the time it takes to catch stuff"
    ),
  },
  SEA_CREATURE_CHANCE: {
    title: "Sea Creature Chance",
    description: wrapText(
      "Sea Creature Chance increases the chance of catching sea creatures while fishing"
    ),
  },
  TREASURE_CHANCE: {
    title: "Treasure Chance",
    description: wrapText(
      "Treasure Chance is the percentage chance to find treasures while fishing"
    ),
  },
  FORAGING_FORTUNE: {
    title: "Foraging Fortune",
    description: wrapText(
      "Foraging Fortune increases the chance of getting extra drops while foraging"
    ),
  },
  FARMING_FORTUNE: {
    title: "Farming Fortune",
    description: wrapText(
      "Farming Fortune increases the chance of getting extra drops while farming"
    ),
  },
  MINING_FORTUNE: {
    title: "Mining Fortune",
    description: wrapText(
      "Mining Fortune increases the chance of getting extra drops while mining"
    ),
  },
  FORAGING_SWEEP: {
    title: "Foraging Sweep",
    description: wrapText(
      "Foraging Sweep is the number of logs you can forage at once in a single action"
    ),
  },
  FARMING_SWEEP: {
    title: "Farming Sweep",
    description: wrapText(
      "Farming Sweep is the number of crops you can harvest at once in a single action"
    ),
  },
  MINING_SWEEP: {
    title: "Mining Sweep",
    description: wrapText(
      "Mining Sweep is the number of ore you can mine at once in a single action"
    ),
  },
  FARMING_WISDOM: {
    title: "Farming Wisdom",
    description: wrapText(
      "Farming Wisdom increases the Farming experience you gain"
    ),
  },
  MINING_WISDOM: {
    title: "Mining Wisdom",
    description: wrapText(
      "Mining Wisdom increases the Mining experience you gain"
    ),
  },
  FORAGING_WISDOM: {
    title: "Foraging Wisdom",
    description: wrapText(
      "Foraging Wisdom increases the Foraging experience you gain"
    ),
  },
  FISHING_WISDOM: {
    title: "Fishing Wisdom",
    description: wrapText(
      "Fishing Wisdom increases the Fishing experience you gain"
    ),
  },
  COMBAT_WISDOM: {
    title: "Combat Wisdom",
    description: wrapText(
      "Combat Wisdom increases the Combat experience you gain"
    ),
  },
  ENCHANTING_WISDOM: {
    title: "Enchanting Wisdom",
    description: wrapText(
      "Enchanting Wisdom increases the Enchanting experience you gain"
    ),
  },
  ALCHEMY_WISDOM: {
    title: "Alchemy Wisdom",
    description: wrapText(
      "Alchemy Wisdom increases the Alchemy experience you gain"
    ),
  },
  TAMING_WISDOM: {
    title: "Taming Wisdom",
    description: wrapText(
      "Taming Wisdom increases the Taming experience you gain"
    ),
  },
  CRAFTING_WISDOM: {
    title: "Crafting Wisdom",
    description: wrapText(
      "Crafting Wisdom increases the Crafting experience you gain"
    ),
  },
  MAGIC_FIND: {
    title: "Magic Find",
    description: wrapText(
      "Magic Find increases the chance of finding rare items (anything ≤ 1% chance)"
    ),
  },
  POTION_DURATION: {
    title: "Potion Duration",
    description: wrapText(
      "Potion Duration increases how long potion effects last when consuming Potions"
    ),
  },
};

module.exports = { STAT_DESCRIPTIONS };
