const { EMBED_COLORS } = require("../gameConfig.js");
const { EmbedBuilder } = require("discord.js");
const { getPlayerSkillLevel } = require("./playerDataManager");
const collectionsManager = require("./collectionsManager.js");
const configManager = require("./configManager"); // Add configManager

/**
 * Finds the collection definition for a given item key.
 * @param {string} itemKey The key of the item (e.g., 'COBBLESTONE').
 * @returns {object|null} The collection definition object or null if not found.
 */
function findCollectionForItem(itemKey) {
  const COLLECTIONS = collectionsManager.getCollections();
  for (const category in COLLECTIONS) {
    // Check if itemKey matches a collection directly
    if (COLLECTIONS[category][itemKey]) {
      return COLLECTIONS[category][itemKey];
    }
    // Check if itemKey is in any collection's itemKeys array
    for (const collectionKey in COLLECTIONS[category]) {
      const collection = COLLECTIONS[category][collectionKey];
      if (
        collection.itemKeys &&
        Array.isArray(collection.itemKeys) &&
        collection.itemKeys.includes(itemKey)
      ) {
        return collection;
      }
    }
  }
  return null;
}

/**
 * Finds the collection key for a given item key.
 * @param {string} itemKey The key of the item (e.g., 'RED_MUSHROOM').
 * @returns {string|null} The collection key (e.g., 'MUSHROOM') or null if not found.
 */
function findCollectionKeyForItem(itemKey) {
  const COLLECTIONS = collectionsManager.getCollections();
  for (const category in COLLECTIONS) {
    // Check if itemKey matches a collection directly
    if (COLLECTIONS[category][itemKey]) {
      return itemKey;
    }
    // Check if itemKey is in any collection's itemKeys array
    for (const collectionKey in COLLECTIONS[category]) {
      const collection = COLLECTIONS[category][collectionKey];
      if (
        collection.itemKeys &&
        Array.isArray(collection.itemKeys) &&
        collection.itemKeys.includes(itemKey)
      ) {
        return collectionKey;
      }
    }
  }
  return null;
}

/**
 * Helper to get the base item and amount per enchanted/block item from its recipe.
 * Returns { baseItemKey, baseAmountPerEnchanted } or null if not applicable.
 * Applies to enchanted items (ENCHANTED_X) and block items (BLOCK_OF_X) for minion collection conversion.
 */
function getBaseItemForEnchanted(itemKey) {
  const allItems = configManager.getAllItems();
  const itemData = allItems[itemKey];
  if (
    !itemData ||
    !itemData.recipes ||
    !Array.isArray(itemData.recipes) ||
    itemData.recipes.length === 0
  )
    return null;

  // Only apply to enchanted items or block items that should be converted to base items
  // Enchanted items: ENCHANTED_REDSTONE → REDSTONE (160:1 ratio)
  // Block items: BLOCK_OF_REDSTONE → REDSTONE (1:9 ratio)
  if (!itemKey.startsWith("ENCHANTED_") && !itemKey.startsWith("BLOCK_OF_"))
    return null;

  // Assume first recipe is the standard one
  const recipe = itemData.recipes[0];
  if (
    !recipe.ingredients ||
    !Array.isArray(recipe.ingredients) ||
    recipe.ingredients.length !== 1
  )
    return null;
  const ingredient = recipe.ingredients[0];
  if (!ingredient.itemKey || typeof ingredient.amount !== "number") return null;
  return {
    baseItemKey: ingredient.itemKey,
    baseAmountPerEnchanted: ingredient.amount,
  };
}

/**
 * Updates a player's collection progress, checks for tier unlocks & requirement-based recipe unlocks,
 * applies rewards, and returns any notification embeds.
 *
 * @param {string} userId Discord User ID
 * @param {string} itemKey The key of the item collected (e.g., 'COBBLESTONE')
 * @param {number} amount The amount of the item collected
 * @param {object} character The character object to modify directly.
 * @param {object} [options] Optional. { allowConversion: boolean } - if true, allow conversion from enchanted/block items to base items
 * @returns {Promise<{character: object, notifications: EmbedBuilder[]|null}>} The modified character object and an array of notification embeds or null
 */
async function addCollectionProgress(
  userId,
  itemKey,
  amount,
  character,
  options = {}
) {
  if (!character) {
    console.error(
      `[addCollectionProgress] Received null character object for user ${userId}`
    );
    return { character: null, notifications: null };
  }

  try {
    const collectionDef = findCollectionForItem(itemKey);
    if (!collectionDef) {
      // Check if this is an enchanted/block item that should be converted to base item for collection
      // Only applies to minion collection, not direct skill actions
      if (options.allowConversion) {
        const baseInfo = getBaseItemForEnchanted(itemKey);
        if (baseInfo) {
          // Recursively award base item collection progress
          return await addCollectionProgress(
            userId,
            baseInfo.baseItemKey,
            amount * baseInfo.baseAmountPerEnchanted,
            character,
            { allowConversion: true }
          );
        }
      }
      return { character, notifications: null }; // Item doesn't belong to a collection
    }

    // --- Find the correct collection key for storage ---
    const collectionKey = findCollectionKeyForItem(itemKey);
    if (!collectionKey) {
      return { character, notifications: null };
    }
    const collectionKeyUpper = collectionKey.toUpperCase();
    // ---------------------------------------------

    const allItems = configManager.getAllItems();
    // Use itemKey to get itemData for display purposes
    const _itemData = allItems[itemKey.toUpperCase()];

    // Get collection tiers from collections.js instead of items.json
    const collectionTiers = collectionDef.tiers.map((tier) => tier.amount);
    if (!collectionTiers || collectionTiers.length === 0) {
      return { character, notifications: null };
    }

    // State BEFORE Update
    const characterStateBeforeUpdate = JSON.parse(JSON.stringify(character));
    const previouslyCraftable = getCurrentlyCraftableItems(
      characterStateBeforeUpdate,
      allItems
    );

    // Apply Collection Update using collectionKeyUpper
    character.collections = character.collections || {};
    const oldAmount = character.collections[collectionKeyUpper] || 0; // Use collectionKeyUpper
    const newAmount = oldAmount + amount;
    character.collections[collectionKeyUpper] = newAmount; // Use collectionKeyUpper

    // Only log collection milestones, not routine progress

    const notificationEmbeds = [];

    // Check for tier unlocks (milestones)
    const currentTierLevel = collectionTiers.reduce(
      (maxLevel, milestone, idx) => {
        return oldAmount >= milestone ? Math.max(maxLevel, idx + 1) : maxLevel;
      },
      0
    );

    for (let idx = 0; idx < collectionTiers.length; idx++) {
      const milestone = collectionTiers[idx];
      const tierLevel = idx + 1;
      if (newAmount >= milestone && tierLevel > currentTierLevel) {
        const tierNotificationEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GOLD)
          .setTitle("Collection Tier Reached!")
          .setDescription(
            //`<@${userId}> **${getCollectionEmoji(itemKey)} ${
            `**${getCollectionEmoji(itemKey)} ${getCollectionDisplayName(
              itemKey
            )} Collection Level ${tierLevel}**\nCollected ${newAmount.toLocaleString()} / ${milestone.toLocaleString()}`
          );

        // Check for accessory bag rewards for redstone collection
        if (collectionKeyUpper === "REDSTONE") {
          try {
            const {
              getAccessoryBagTierReward,
            } = require("../commands/accessories");
            const bagReward = getAccessoryBagTierReward(tierLevel);
            if (bagReward) {
              const currentDescription = tierNotificationEmbed.data.description;
              tierNotificationEmbed.setDescription(
                `${currentDescription}\n\n<:accessory_bag:1384159284123664516> **${bagReward}** unlocked!`
              );
            }
          } catch (error) {
            console.error(
              "[Collection] Error adding accessory bag reward:",
              error
            );
          }
        }

        notificationEmbeds.push(tierNotificationEmbed);
      }
    }

    // --- Smart Recipe Unlock Check ---
    const currentlyCraftable = getCurrentlyCraftableItems(character, allItems);
    const recipeUnlockDescriptions = [];
    for (const craftableItemKey of currentlyCraftable) {
      if (!previouslyCraftable.has(craftableItemKey)) {
        const unlockedItemData = allItems[craftableItemKey];
        const recipeEmoji = unlockedItemData?.emoji || "❓";
        const recipeName = unlockedItemData?.name || craftableItemKey;
        recipeUnlockDescriptions.push(
          `Recipe Unlocked: ${recipeEmoji} **${recipeName}**`
        );
      }
    }
    if (recipeUnlockDescriptions.length > 0) {
      const recipeEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GOLD)
        .setTitle("New Recipes Unlocked!")
        .setDescription(recipeUnlockDescriptions.join("\n"));
      notificationEmbeds.push(recipeEmbed);
    }

    // Note: Base item collection progress is already handled above in the initial check (lines 57-65)
    // Removing duplicate recursive call to prevent exponential explosion

    return {
      character,
      notifications: notificationEmbeds.length > 0 ? notificationEmbeds : null,
    };
  } catch (err) {
    console.error(
      `[addCollectionProgress] Error updating collection for user ${userId}, item ${itemKey}:`,
      err
    );
    return { character, notifications: null };
  }
}

// Helper to get collection definition for an itemKey from collections.js
function getCollectionDefForItem(itemKey) {
  const COLLECTIONS = collectionsManager.getCollections();
  for (const categoryName in COLLECTIONS) {
    if (COLLECTIONS[categoryName][itemKey]) {
      return COLLECTIONS[categoryName][itemKey];
    }
  }
  return null;
}

// Function to get the actual collection level for a given CATEGORY
function getPlayerCollectionLevel(character, categoryName) {
  if (!character || !character.collections || !categoryName) return 0;
  let maxLevelInCategory = 0;
  const COLLECTIONS = collectionsManager.getCollections();
  const categoryCollections = COLLECTIONS[categoryName];
  if (!categoryCollections) return 0;
  for (const itemKey in categoryCollections) {
    const collectionDef = categoryCollections[itemKey];
    const collectedAmount = character.collections[itemKey] || 0;
    const currentLevel = collectionDef.tiers.reduce((maxLevel, tier) => {
      return collectedAmount >= tier.amount
        ? Math.max(maxLevel, tier.level)
        : maxLevel;
    }, 0);
    maxLevelInCategory = Math.max(maxLevelInCategory, currentLevel);
  }
  return maxLevelInCategory;
}

// Function to get collection level for a SPECIFIC ITEM (Case-Insensitive Check)
function getPlayerItemCollectionLevel(character, itemKey) {
  if (!character || !character.collections || !itemKey) return 0;

  // --- Case-Insensitive Key Finding ---
  const itemKeyUpper = itemKey.toUpperCase();
  let collectedAmount = 0;

  // Find the correct key case-insensitively in player data
  for (const storedKey in character.collections) {
    if (storedKey.toUpperCase() === itemKeyUpper) {
      collectedAmount = character.collections[storedKey] || 0;
      break;
    }
  }
  // --- End Case-Insensitive Key Finding ---

  // Use the original itemKey (or itemKeyUpper) to find the definition
  const collectionDef = getCollectionDefForItem(itemKeyUpper);
  if (!collectionDef || !Array.isArray(collectionDef.tiers)) return 0;

  // Calculate level based on the found amount and the definition
  const currentLevel = collectionDef.tiers.reduce((maxLevel, tier) => {
    return collectedAmount >= tier.amount
      ? Math.max(maxLevel, tier.level)
      : maxLevel;
  }, 0);

  return currentLevel;
}

// Function to get collection category info based on item key/name
function getCollectionInfo(itemKeyOrName) {
  const allItems = configManager.getAllItems();
  let itemKey = itemKeyOrName;

  // If it's a name, try to find the key
  if (!allItems[itemKey]) {
    const foundKey = Object.keys(allItems).find(
      (key) => allItems[key].name === itemKeyOrName
    );
    if (foundKey) {
      itemKey = foundKey;
    } else {
      return null; // Couldn't find item by name or key
    }
  }

  // Find the category
  const COLLECTIONS = collectionsManager.getCollections();
  for (const categoryName in COLLECTIONS) {
    if (COLLECTIONS[categoryName][itemKey]) {
      return {
        categoryName: categoryName,
        collectionDef: COLLECTIONS[categoryName][itemKey],
      };
    }
  }
  return null; // Item doesn't belong to any known category
}

/**
 * Checks if a player meets all crafting requirements for a specific item.
 * @param {object} characterData The player's character data.
 * @param {object} itemData The item definition containing craftingRequirements.
 * @returns {boolean} True if all requirements are met, false otherwise.
 */
function checkCraftingRequirements(characterData, itemData) {
  if (!itemData || !itemData.craftingRequirements) {
    return true; // No requirements means it can be crafted (assuming ingredients are met elsewhere)
  }

  const requirements = itemData.craftingRequirements;

  // Check Skills
  if (requirements.skills) {
    for (const [skillKey, requiredLevel] of Object.entries(
      requirements.skills
    )) {
      // Use helper from playerDataManager or re-implement logic if needed
      const playerLevel = getPlayerSkillLevel(
        characterData,
        skillKey.toLowerCase()
      );
      if (playerLevel < requiredLevel) {
        return false; // Skill requirement not met
      }
    }
  }

  // Check Collections
  if (requirements.collections) {
    for (const [collectionKey, requiredLevel] of Object.entries(
      requirements.collections
    )) {
      // Use helper defined in this file
      const playerLevel = getPlayerItemCollectionLevel(
        characterData,
        collectionKey
      );
      if (playerLevel < requiredLevel) {
        return false; // Collection requirement not met
      }
    }
  }

  // Check Slayers
  if (requirements.slayers) {
    const { getSlayerLevelFromExp } = require("./slayerLevelUtils");
    const slayerXpData = characterData.slayerXp || {};

    for (const [slayerType, requiredLevel] of Object.entries(
      requirements.slayers
    )) {
      const slayerXp = slayerXpData[slayerType] || 0;
      const playerLevel = getSlayerLevelFromExp(slayerXp).level;
      if (playerLevel < requiredLevel) {
        return false; // Slayer requirement not met
      }
    }
  }

  return true; // All requirements met
}

/**
 * Determines the set of item keys that a player can currently craft based on requirements.
 * Does NOT check for ingredients.
 * @param {object} characterData The player's character data object.
 * @param {object} allItems Object containing all item definitions.
 * @returns {Set<string>} A Set containing the item keys of all craftable items.
 */
function getCurrentlyCraftableItems(characterData, allItems) {
  const craftableItems = new Set();
  if (!characterData || !allItems) {
    return craftableItems; // Return empty set if data is missing
  }

  for (const itemKey in allItems) {
    const itemData = allItems[itemKey];
    // Check if the item *has* a recipe and if requirements are met
    if (
      itemData.recipes &&
      itemData.recipes.length > 0 &&
      checkCraftingRequirements(characterData, itemData)
    ) {
      craftableItems.add(itemKey);
    }
  }
  return craftableItems;
}

/**
 * Gets the display name for a collection.
 * @param {string} itemKey The item key to find the collection for.
 * @returns {string} The collection display name.
 */
function getCollectionDisplayName(itemKey) {
  const collectionKey = findCollectionKeyForItem(itemKey);
  if (!collectionKey) return itemKey;

  const collectionDef = findCollectionForItem(itemKey);
  return collectionDef?.name || collectionKey;
}

/**
 * Gets the emoji for a collection.
 * @param {string} itemKey The item key to find the collection for.
 * @returns {string} The collection emoji.
 */
function getCollectionEmoji(itemKey) {
  const collectionDef = findCollectionForItem(itemKey);
  if (collectionDef?.emoji) {
    return collectionDef.emoji;
  }

  // Fallback to item emoji
  const allItems = configManager.getAllItems();
  return allItems[itemKey]?.emoji || "❓";
}

module.exports = {
  addCollectionProgress,
  findCollectionForItem,
  findCollectionKeyForItem,
  getPlayerCollectionLevel,
  getPlayerItemCollectionLevel,
  getCollectionInfo,
  checkCraftingRequirements,
  getCurrentlyCraftableItems,
  getCollectionDisplayName,
  getCollectionEmoji,
};
