const { dbRunQueued, dbGet } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");
const configManager = require("./configManager");
const { EMBED_COLORS } = require("../gameConfig");
const { getLevelFromExp } = require("./expFunctions");
const {
  EmbedBuilder,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");

const WARDROBE_SLOTS = {
  HELMET: "helmet_item_id",
  CHESTPLATE: "chestplate_item_id",
  LEGGINGS: "leggings_item_id",
  BOOTS: "boots_item_id",
  WEAPON: "weapon_item_id",
  AXE: "axe_item_id",
  PICKAXE: "pickaxe_item_id",
  SHOVEL: "shovel_item_id",
  HOE: "hoe_item_id",
  FISHING_ROD: "fishing_rod_item_id",
  NECKLACE: "necklace_item_id",
  CLOAK: "cloak_item_id",
  BELT: "belt_item_id",
  GLOVES: "gloves_item_id",
  PET: "pet_id",
};

const SLOT_LABELS = {
  HELMET: "Helmet",
  CHESTPLATE: "Chestplate",
  LEGGINGS: "Leggings",
  BOOTS: "Boots",
  WEAPON: "Weapon",
  AXE: "Axe",
  PICKAXE: "Pickaxe",
  SHOVEL: "Shovel",
  HOE: "Hoe",
  FISHING_ROD: "Fishing Rod",
  NECKLACE: "Necklace",
  CLOAK: "Cloak",
  BELT: "Belt",
  GLOVES: "Gloves/Bracelet",
  PET: "Pet",
};

/**
 * Calculates the number of wardrobe slots a player has based on their crafting level
 * @param {object} character - Player character data
 * @returns {number} - Number of wardrobe slots available
 */
function calculateWardrobeSlots(character) {
  const baseSlotsDefault = 3;

  if (!character || !character.skills || !character.skills.crafting) {
    return baseSlotsDefault;
  }

  const craftingLevel = getLevelFromExp(
    character.skills.crafting.exp || 0
  ).level;
  let bonusSlots = 0;

  if (craftingLevel >= 5) bonusSlots += 1;
  if (craftingLevel >= 10) bonusSlots += 1;
  if (craftingLevel >= 15) bonusSlots += 1;

  return baseSlotsDefault + bonusSlots;
}

async function handleSlotConfiguration(interaction, slotNumber, slotKey) {
  try {
    const userId = interaction.user.id;
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    // Validate slot number
    if (slotNumber < 1 || slotNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      return interaction.update({ embeds: [errorEmbed] });
    }

    await interaction.deferUpdate();

    const allItems = configManager.getAllItems();

    // Get available items for this slot
    const availableItems = getAvailableItemsForSlot(
      playerData,
      allItems,
      slotKey
    );

    if (availableItems.length === 0) {
      return interaction.followUp({
        content: `❌ You don't have any items that can be equipped in the ${SLOT_LABELS[slotKey]} slot.`,
      });
    }

    const embed = new EmbedBuilder()
      .setTitle(`⚙️ Configure ${SLOT_LABELS[slotKey]} - Preset ${slotNumber}`)
      .setDescription(
        `Select an item for the ${SLOT_LABELS[slotKey]} slot, or choose "None" to clear it.`
      )
      .setColor(EMBED_COLORS.ORANGE);

    // Create select menu with available items
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`set_item_${slotNumber}_${slotKey}`)
      .setPlaceholder(`Choose ${SLOT_LABELS[slotKey]}...`)
      .setMaxValues(1);

    // Add "None" option to clear the slot
    selectMenu.addOptions({
      label: "None",
      description: "Clear this slot",
      value: "none",
      emoji: "❌",
    });

    // Add available items (limit to 24 to stay within Discord limits)
    const itemsToShow = availableItems.slice(0, 24);
    for (const item of itemsToShow) {
      const statusText = item.isEquipped ? " (Currently Equipped)" : "";
      selectMenu.addOptions({
        label: `${item.name}${statusText}`,
        description: item.isEquipped ? "Currently equipped" : "In storage",
        value: item.itemKey,
        emoji: item.emoji || "📦",
      });
    }

    if (availableItems.length > 24) {
      embed.setFooter({
        text: `Showing first 24 of ${availableItems.length} items`,
      });
    }

    const backButton = new ButtonBuilder()
      .setCustomId(`configure_back_${slotNumber}`)
      .setLabel("Back to Configure")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("↩️");

    const rows = [
      new ActionRowBuilder().addComponents(selectMenu),
      new ActionRowBuilder().addComponents(backButton),
    ];

    await interaction.editReply({
      embeds: [embed],
      components: rows,
    });
  } catch (error) {
    console.error("Error handling slot configuration:", error);
    await interaction.followUp({
      content: "❌ An error occurred while configuring the slot.",
    });
  }
}

async function setPresetItem(interaction, slotNumber, slotKey, itemKey) {
  try {
    const userId = interaction.user.id;
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    // Validate slot number
    if (slotNumber < 1 || slotNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      return interaction.update({ embeds: [errorEmbed] });
    }

    await interaction.deferUpdate();

    const dbColumn = WARDROBE_SLOTS[slotKey];

    if (!dbColumn) {
      return interaction.followUp({
        content: "❌ Invalid slot specified.",
      });
    }

    // Check if preset exists, create if not
    const preset = await dbGet(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
      [userId, slotNumber]
    );

    let itemValue, message;

    if (itemKey === "none") {
      itemValue = null;
      message = `✅ Cleared ${SLOT_LABELS[slotKey]} slot for Preset ${slotNumber}.`;
    } else if (slotKey === "PET") {
      // For pets, itemKey is actually the pet's itemKey, we need to find the pet ID
      if (playerData.pets && Array.isArray(playerData.pets)) {
        const pet = playerData.pets.find((p) => (p.key || p.name) === itemKey);
        if (pet) {
          itemValue = pet.id;
          message = `✅ Set ${SLOT_LABELS[slotKey]} to ${pet.name} for Preset ${slotNumber}.`;
        } else {
          return interaction.followUp({
            content: `❌ Pet "${itemKey}" not found in your collection.`,
            ephemeral: true,
          });
        }
      } else {
        return interaction.followUp({
          content: "❌ No pets available.",
        });
      }
    } else {
      // For equipment items, itemKey is the item key, we need to find the item ID
      const inventoryItem = playerData.inventory.equipment.find(
        (item) => item.itemKey === itemKey
      );

      if (inventoryItem) {
        itemValue = inventoryItem.id;
        const allItems = configManager.getAllItems();
        const itemName = allItems[itemKey]?.name || itemKey;
        message = `✅ Set ${SLOT_LABELS[slotKey]} to ${itemName} for Preset ${slotNumber}.`;
      } else {
        return interaction.followUp({
          content: "❌ Item not found in inventory.",
        });
      }
    }

    if (preset) {
      // Update existing preset
      await dbRunQueued(
        `UPDATE wardrobe_presets SET ${dbColumn} = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ? AND slot_number = ?`,
        [itemValue, userId, slotNumber]
      );
    } else {
      // Create new preset
      await dbRunQueued(
        `INSERT INTO wardrobe_presets (user_id, slot_number, ${dbColumn}) VALUES (?, ?, ?)`,
        [userId, slotNumber, itemValue]
      );
    }

    await interaction.followUp({
      content: message,
    });

    // Go back to configure menu
    const { showConfigureMenu } = require("../commands/wardrobe");
    await showConfigureMenu(interaction, userId, slotNumber);
  } catch (error) {
    console.error("Error setting preset item:", error);
    await interaction.followUp({
      content: "❌ An error occurred while saving the item.",
    });
  }
}

async function clearPreset(interaction, slotNumber) {
  try {
    const userId = interaction.user.id;
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    // Validate slot number
    if (slotNumber < 1 || slotNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      return interaction.update({ embeds: [errorEmbed] });
    }

    await interaction.deferUpdate();

    await dbRunQueued(
      "DELETE FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
      [userId, slotNumber]
    );

    await interaction.followUp({
      content: `✅ Cleared Preset ${slotNumber}.`,
    });

    // Go back to main wardrobe menu
    const { showWardrobeMenu } = require("../commands/wardrobe");
    await showWardrobeMenu(interaction, userId, true);
  } catch (error) {
    console.error("Error clearing preset:", error);
    await interaction.followUp({
      content: "❌ An error occurred while clearing the preset.",
    });
  }
}

function getAvailableItemsForSlot(playerData, allItems, slotKey) {
  const availableItems = [];

  if (slotKey === "PET") {
    // Handle pets
    if (playerData.pets && Array.isArray(playerData.pets)) {
      for (const pet of playerData.pets) {
        availableItems.push({
          id: pet.id,
          itemKey: pet.key || pet.name,
          name: pet.name,
          emoji: pet.emoji || "🐾",
          isEquipped: playerData.active_pet_id === pet.id,
        });
      }
    }
  } else {
    // Handle equipment items
    const allEquipment = playerData.inventory.equipment || [];

    for (const item of allEquipment) {
      const itemDef = allItems[item.itemKey];
      if (!itemDef) continue;

      // farming axes only show up in AXE slot, never in other slots
      if (slotKey === "AXE") {
        if (
          getItemSlot(itemDef) === "AXE" ||
          (itemDef.subtype === "FARMING_AXE" && itemDef.type === "TOOL")
        ) {
          availableItems.push({
            id: item.id,
            itemKey: item.itemKey,
            name: itemDef.name,
            emoji: itemDef.emoji,
            isEquipped: item.isEquipped,
          });
        }
      } else {
        // all other slots use getItemSlot strictly
        if (getItemSlot(itemDef) === slotKey) {
          availableItems.push({
            id: item.id,
            itemKey: item.itemKey,
            name: itemDef.name,
            emoji: itemDef.emoji,
            isEquipped: item.isEquipped,
          });
        }
      }
    }
  }

  // Sort: equipped items first, then by name
  availableItems.sort((a, b) => {
    if (a.isEquipped && !b.isEquipped) return -1;
    if (!a.isEquipped && b.isEquipped) return 1;
    return a.name.localeCompare(b.name);
  });

  return availableItems;
}

function getItemSlot(itemDef) {
  // Handle special cases first
  if (itemDef.subtype === "AXE" || itemDef.subtype === "FARMING_AXE") {
    // farming axes and normal axes go in AXE slot if tool, WEAPON slot if weapon
    return itemDef.type === "TOOL" ? "AXE" : "WEAPON";
  }

  // Handle equipment types
  if (itemDef.type === "EQUIPMENT") {
    if (itemDef.subtype === "GLOVES" || itemDef.subtype === "BRACELET") {
      return "GLOVES";
    }
    return itemDef.subtype; // NECKLACE, CLOAK, BELT
  }

  // Handle armor
  if (["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"].includes(itemDef.subtype)) {
    return itemDef.subtype;
  }

  // Handle weapons
  if (itemDef.type === "WEAPON") {
    return "WEAPON";
  }

  // Handle tools
  if (itemDef.type === "TOOL") {
    if (itemDef.subtype === "ROD") {
      return "FISHING_ROD";
    }
    return itemDef.subtype; // PICKAXE, SHOVEL, HOE
  }

  return null;
}

module.exports = {
  handleSlotConfiguration,
  setPresetItem,
  clearPreset,
  getAvailableItemsForSlot,
  getItemSlot,
  calculateWardrobeSlots,
  WARDROBE_SLOTS,
  SLOT_LABELS,
};
