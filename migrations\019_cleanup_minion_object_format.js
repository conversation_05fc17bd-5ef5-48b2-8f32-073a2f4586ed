// Migration Script: 019_cleanup_minion_object_format.js
// Purpose: Converts minion objects that were skipped by migration 018 because their `key`
//          property already contained a base key. Ensures all minion objects use the
//          { itemKey, tier, id, ... } format.

const { v4: uuidv4 } = require("uuid");

// Function to perform the migration
async function up(db, playerDataUtils) {
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("Running minion object format cleanup migration (019)...");
  let processedCount = 0;
  let migratedCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log("No player IDs found. Migration step 019 complete.");
    return;
  }

  console.log(`Found ${playerIds.length} players to check for migration 019.`);

  for (const discordId of playerIds) {
    processedCount++;
    if (processedCount % 50 === 0) {
      console.log(
        `Migration 019: Processed ${processedCount}/${playerIds.length}...`,
      );
    }

    try {
      const player = await getPlayerData(discordId);
      if (!player) {
        continue;
      }

      let needsSave = false;

      // --- Cleanup minionStorage ---
      if (player.minionStorage && Array.isArray(player.minionStorage)) {
        const newMinionStorage = [];
        let storageChanged = false;
        for (const minionData of player.minionStorage) {
          // Target condition: is object, has 'key', does NOT have 'itemKey'
          if (
            typeof minionData === "object" &&
            minionData !== null &&
            minionData.key &&
            !minionData.itemKey
          ) {
            console.log(
              `  - Player ${discordId}: Found minion in storage needing cleanup (key: ${minionData.key})`,
            );
            newMinionStorage.push({
              itemKey: minionData.key, // Assume key holds the base key
              tier: minionData.tier !== undefined ? minionData.tier : 1, // Default to tier 1 if missing
              id: minionData.id || uuidv4(),
              resourcesStored: minionData.resourcesStored || {},
              lastCollectionTimestamp: minionData.lastCollectionTimestamp,
            });
            needsSave = true;
            storageChanged = true;
          } else {
            // Keep already correct or unprocessable data
            newMinionStorage.push(minionData);
          }
        }
        if (storageChanged) {
          player.minion_storage_json = newMinionStorage;
        }
      }

      // --- Cleanup island.placedMinions ---
      if (
        player.island?.placedMinions &&
        Array.isArray(player.island.placedMinions)
      ) {
        const newPlacedMinions = [];
        let placedChanged = false;
        for (const placedMinionData of player.island.placedMinions) {
          // Target condition: is object, has 'key', does NOT have 'itemKey'
          if (
            typeof placedMinionData === "object" &&
            placedMinionData !== null &&
            placedMinionData.key &&
            !placedMinionData.itemKey
          ) {
            console.log(
              `  - Player ${discordId}: Found placed minion needing cleanup (key: ${placedMinionData.key})`,
            );
            newPlacedMinions.push({
              itemKey: placedMinionData.key,
              tier:
                placedMinionData.tier !== undefined ? placedMinionData.tier : 1,
              id: placedMinionData.id || uuidv4(),
              resourcesStored: placedMinionData.resourcesStored || {},
              lastCollectionTimestamp: placedMinionData.lastCollectionTimestamp,
            });
            needsSave = true;
            placedChanged = true;
          } else {
            // Keep already correct or unprocessable data
            newPlacedMinions.push(placedMinionData);
          }
        }
        if (placedChanged) {
          if (!player.island_json) player.island_json = {}; // Should exist if placedMinions did, but safe check
          player.island.placedMinions = newPlacedMinions;
        }
      }

      // Save if any changes were made
      if (needsSave) {
        await savePlayerData(discordId, player);
        migratedCount++;
        console.log(`  - Cleaned up minion data for ${discordId}.`);
      }
    } catch (error) {
      console.error(
        `  - Error processing player ${discordId} in migration 019:`,
        error,
      );
      errorCount++;
    }
  }

  console.log("-----------------------------------------");
  console.log("Minion object format cleanup migration (019) finished.");
  console.log(`Total Players Checked: ${processedCount}`);
  console.log(`Players Cleaned Up:    ${migratedCount}`);
  console.log(`Errors Encountered:    ${errorCount}`);
  console.log("-----------------------------------------");

  if (errorCount > 0) {
    throw new Error(`Migration 019 completed with ${errorCount} errors.`);
  }
}

module.exports = { up };
