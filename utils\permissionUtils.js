/**
 * Defines the hierarchy of ranks. Higher value means higher privilege.
 * Keys should be uppercase for case-insensitive lookup.
 */
const RANK_HIERARCHY = {
  MEMBER: 1,
  ADMIN: 2,
  OWNER: 3,
  // Add future ranks here in uppercase
};

/**
 * Checks if a character's rank meets or exceeds a required rank (case-insensitive).
 * @param {object} character The character object from getPlayerData (must have a .rank property).
 * @param {string} requiredRank The minimum rank required (e.g., 'MEMBER', 'Admin', 'Owner'). Case-insensitive.
 * @returns {boolean} True if the character has sufficient rank, false otherwise.
 */
function checkRankPermission(character, requiredRank) {
  if (!character || !character.rank) {
    // If character or rank is missing, deny permission implicitly
    // (Shouldn't happen for commands requiring a character, but good safety check)
    return false;
  }

  const characterRankUpper = character.rank.toUpperCase();
  const requiredRankUpper = requiredRank.toUpperCase();

  const characterRankValue = RANK_HIERARCHY[characterRankUpper];
  const requiredRankValue = RANK_HIERARCHY[requiredRankUpper];

  // If either rank isn't defined in the hierarchy, deny permission for safety
  if (characterRankValue === undefined || requiredRankValue === undefined) {
    console.warn(
      `[permissionUtils] Unknown rank encountered (case-insensitive check): character='${character.rank}' (-> '${characterRankUpper}'), required='${requiredRank}' (-> '${requiredRankUpper}')`
    );
    return false;
  }

  // Check if the character's rank value is greater than or equal to the required rank value
  return characterRankValue >= requiredRankValue;
}

module.exports = { checkRankPermission, RANK_HIERARCHY };
