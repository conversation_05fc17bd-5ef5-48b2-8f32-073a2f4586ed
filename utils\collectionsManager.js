const fs = require("fs").promises;
const path = require("path");

const collectionsFilePath = path.join(
  __dirname,
  "..",
  "data",
  "collections.js"
);
let liveCollections = {}; // In-memory store for collection data

/**
 * Loads collection data from collections.js into memory.
 * Uses require() for standard Node.js module loading.
 */
async function loadCollections() {
  // Keep async for consistency if initialize is async
  const QUIET_BOOT = process.env.QUIET_BOOT === "true";
  if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
    console.log("[CollectionsManager] Loading collections...");
  try {
    // Clear cache to ensure fresh load on re-initialize (if needed)
    delete require.cache[require.resolve(collectionsFilePath)];
    // Use require directly
    const loadedModule = require(collectionsFilePath);

    if (
      loadedModule.COLLECTIONS &&
      typeof loadedModule.COLLECTIONS === "object"
    ) {
      liveCollections = loadedModule.COLLECTIONS;
      if (process.env.IS_MAIN_BOT === "true") {
        if (!QUIET_BOOT) {
          console.log(
            `[CollectionsManager] Successfully loaded ${
              Object.keys(liveCollections).length
            } collection categories via require().`
          );
        }
      }
    } else {
      throw new Error(
        "Could not find a valid { COLLECTIONS } export in collections.js"
      );
    }

    if (Object.keys(liveCollections).length === 0) {
      console.warn("[CollectionsManager] Loaded collections data is empty.");
    }
  } catch (error) {
    console.error(
      "[CollectionsManager] CRITICAL ERROR loading collections.js:",
      error
    );
    liveCollections = {}; // Reset to empty on error
    // Throw the error if loading is critical
    throw error;
  }
}

/**
 * Saves the current in-memory collection data back to collections.js.
 * WARNING: This overwrites the file and loses comments/formatting.
 * @returns {Promise<{success: boolean, error?: string}>}
 */
async function saveCollections() {
  console.log("[CollectionsManager] Saving collections to file...");
  try {
    // Format the output similar to how recipeadmin did
    const outputString = `// Defines the structure and tiers for various collections\n// WARNING: Automatically generated/updated. Comments and formatting may be lost.\n\nconst COLLECTIONS = ${JSON.stringify(
      liveCollections,
      null,
      4
    )}; // Use 4 spaces for indentation\n\nmodule.exports = { COLLECTIONS };\n`;

    await fs.writeFile(collectionsFilePath, outputString, "utf8");
    console.log(
      `[CollectionsManager] Successfully wrote updated data to ${collectionsFilePath}`
    );
    return { success: true };
  } catch (error) {
    console.error("[CollectionsManager] Error writing collections.js:", error);
    return { success: false, error: "Error writing collections data file." };
  }
}

/**
 * Gets a deep copy of the current collection data.
 * @returns {object} A deep copy of the live collections data. Returns empty object if load failed.
 */
function getCollections() {
  // Auto-initialize if collections are empty
  if (Object.keys(liveCollections).length === 0) {
    console.warn(
      "[CollectionsManager] Collections not loaded, attempting synchronous load..."
    );
    try {
      // Synchronous loading using require directly
      delete require.cache[require.resolve(collectionsFilePath)];
      const loadedModule = require(collectionsFilePath);

      if (
        loadedModule.COLLECTIONS &&
        typeof loadedModule.COLLECTIONS === "object"
      ) {
        liveCollections = loadedModule.COLLECTIONS;
        console.log(
          `[CollectionsManager] Successfully auto-loaded ${Object.keys(liveCollections).length} collection categories.`
        );
      } else {
        console.error(
          "[CollectionsManager] Could not find valid COLLECTIONS export"
        );
        return {};
      }
    } catch (error) {
      console.error(
        "[CollectionsManager] Error during auto-initialization:",
        error
      );
      return {};
    }
  }

  // Return a deep copy to prevent modification of the live object outside the manager
  try {
    return JSON.parse(JSON.stringify(liveCollections));
  } catch (e) {
    console.error(
      "[CollectionsManager] Error deep copying liveCollections:",
      e
    );
    return {}; // Return empty object on error
  }
}

/**
 * Adds a reward to a specific collection tier in memory.
 * Does NOT save automatically. Call saveCollections() afterwards.
 * @param {string} category - Uppercase category key (e.g., 'MINING')
 * @param {string} itemKey - Uppercase item key (e.g., 'COBBLESTONE')
 * @param {number} tierLevel - The tier level (1-indexed)
 * @param {string} rewardType - Type of reward ('recipe', 'item', 'stat', 'skill_xp')
 * @param {string} rewardValue - Value associated with the reward (e.g., recipe key, item key)
 * @param {number} [rewardAmount] - Optional amount (for item/skill_xp) or value (for stat)
 * @returns {{success: boolean, error?: string}}
 */
function addCollectionReward(
  category,
  itemKey,
  tierLevel,
  rewardType,
  rewardValue,
  rewardAmount = null
) {
  try {
    if (
      !liveCollections[category] ||
      !liveCollections[category][itemKey] ||
      !liveCollections[category][itemKey].tiers
    ) {
      return {
        success: false,
        error: `Collection category \`${category}\` or item \`${itemKey}\` not found.`,
      };
    }

    const targetTier = liveCollections[category][itemKey].tiers.find(
      (t) => t.level === tierLevel
    );
    if (!targetTier) {
      return {
        success: false,
        error: `Tier level \`${tierLevel}\` not found for ${category}:${itemKey}.`,
      };
    }

    const newReward = { type: rewardType, value: rewardValue };
    if (
      rewardAmount !== null &&
      ["item", "skill_xp", "stat"].includes(rewardType)
    ) {
      // Assuming 'stat' rewards might also use amount/value? Adjust as needed.
      newReward.amount = rewardAmount;
    } else if (rewardAmount !== null) {
      console.warn(
        `[CollectionsManager] rewardAmount provided for reward type '${rewardType}' but not used.`
      );
    }

    if (!targetTier.rewards) {
      targetTier.rewards = [];
    }

    // Optional: Check if the exact same reward already exists
    const exists = targetTier.rewards.some(
      (r) =>
        r.type === newReward.type &&
        r.value === newReward.value &&
        // Only check amount if it's relevant for the type
        (["item", "skill_xp", "stat"].includes(r.type)
          ? r.amount === newReward.amount
          : true)
    );

    if (exists) {
      return {
        success: false,
        error: `Reward ${JSON.stringify(
          newReward
        )} already exists for this tier.`,
      };
    }

    targetTier.rewards.push(newReward);
    console.log(
      `[CollectionsManager] Added reward to memory: ${category}:${itemKey} Tier ${tierLevel} -> ${JSON.stringify(
        newReward
      )}`
    );
    return { success: true };
  } catch (error) {
    console.error(
      "[CollectionsManager] Error adding collection reward to memory:",
      error
    );
    return {
      success: false,
      error: `Unexpected error adding reward: ${error.message}`,
    };
  }
}

/**
 * Updates recipe keys used in collection rewards based on a provided map.
 * Modifies the liveCollections object directly.
 * Does NOT save automatically. Call saveCollections() afterwards.
 * @param {Object.<string, string>} renameMap - An object mapping old recipe keys to new recipe keys.
 * @returns {boolean} - True if any rewards were updated, false otherwise.
 */
function updateRecipeKeysInRewards(renameMap) {
  let updated = false;
  if (!liveCollections || Object.keys(renameMap).length === 0) {
    return false;
  }

  try {
    for (const categoryKey in liveCollections) {
      for (const itemKey in liveCollections[categoryKey]) {
        const collectionDef = liveCollections[categoryKey][itemKey];
        if (
          collectionDef &&
          collectionDef.tiers &&
          Array.isArray(collectionDef.tiers)
        ) {
          for (const tier of collectionDef.tiers) {
            if (tier.rewards && Array.isArray(tier.rewards)) {
              for (const reward of tier.rewards) {
                if (reward.type === "recipe" && renameMap[reward.value]) {
                  const oldKey = reward.value;
                  const newKey = renameMap[oldKey];
                  console.log(
                    `[CollectionsManager] Updating reward recipe key in ${categoryKey}:${itemKey} Tier ${tier.level}: ${oldKey} -> ${newKey}`
                  );
                  reward.value = newKey;
                  updated = true;
                }
              }
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(
      "[CollectionsManager] Error during updateRecipeKeysInRewards:",
      error
    );
    // Depending on severity, you might want to stop or just log
  }
  return updated;
}

/**
 * Updates the rewards array for a specific collection tier in memory.
 * Does NOT save automatically. Call saveCollections() afterwards.
 * @param {string} category - Uppercase category key (e.g., 'MINING')
 * @param {string} itemKey - Uppercase item key (e.g., 'COBBLESTONE')
 * @param {number} tierLevel - The tier level (1-indexed)
 * @param {Array<object>} newRewardsArray - The complete new array of reward objects for the tier.
 * @returns {{success: boolean, error?: string}}
 */
function updateTierRewards(category, itemKey, tierLevel, newRewardsArray) {
  try {
    if (
      !liveCollections[category] ||
      !liveCollections[category][itemKey] ||
      !liveCollections[category][itemKey].tiers
    ) {
      return {
        success: false,
        error: `Collection category \`${category}\` or item \`${itemKey}\` not found.`,
      };
    }

    const targetTier = liveCollections[category][itemKey].tiers.find(
      (t) => t.level === tierLevel
    );
    if (!targetTier) {
      return {
        success: false,
        error: `Tier level \`${tierLevel}\` not found for ${category}:${itemKey}.`,
      };
    }

    if (!Array.isArray(newRewardsArray)) {
      return {
        success: false,
        error: "Invalid newRewardsArray provided (must be an array).",
      };
    }

    // Directly update the rewards array on the live object
    targetTier.rewards = newRewardsArray;
    console.log(
      `[CollectionsManager] Updated rewards in memory for: ${category}:${itemKey} Tier ${tierLevel}`
    );
    return { success: true };
  } catch (error) {
    console.error(
      "[CollectionsManager] Error updating collection tier rewards in memory:",
      error
    );
    return {
      success: false,
      error: `Unexpected error updating rewards: ${error.message}`,
    };
  }
}

/**
 * Initializes the Collections Manager by loading data.
 * Should be called at bot startup.
 */
async function initialize() {
  await loadCollections();
}

/**
 * Retrieves all unique item keys defined within any collection category.
 * @returns {string[]} An array of unique item keys found in collections.
 */
function getAllCollectionItemKeys() {
  const itemKeys = new Set();
  for (const category of Object.values(liveCollections)) {
    for (const itemKey of Object.keys(category)) {
      itemKeys.add(itemKey);
    }
  }
  return Array.from(itemKeys).sort(); // Return sorted array
}

module.exports = {
  initialize,
  getCollections,
  addCollectionReward,
  updateRecipeKeysInRewards,
  updateTierRewards,
  saveCollections,
  getAllCollectionItemKeys,
};
