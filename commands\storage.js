const {
  <PERSON><PERSON><PERSON><PERSON>mandBuilder,
  EmbedBuilder,
  MessageFlags,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
} = require("discord.js");
const { v4: uuidv4 } = require("uuid");
const { updateInventoryAtomically } = require("../utils/inventory");

const storageMessageIds = new Map();
const menuMessageIds = new Map();
const activeCollectors = new Map();
const { getPlayerData } = require("../utils/playerDataManager");
const { STATS, CURRENCY, EMBED_COLORS } = require("../gameConfig.js");
const configManager = require("../utils/configManager.js");
const { checkRankPermission } = require("../utils/permissionUtils");

const { STAT_ABBREVIATIONS } = require("../utils/statAbbreviations");

function formatStats(baseStats) {
  if (!baseStats || Object.keys(baseStats).length === 0) {
    return null;
  }
  const statParts = [];
  for (const [statKey, value] of Object.entries(baseStats)) {
    const statConfig = STATS[statKey];
    if (statConfig) {
      const displayValue = statConfig.isPercentage
        ? `${value > 0 ? "+" : ""}${value}%`
        : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;

      const statName =
        STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || statKey;
      statParts.push(
        `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``
      );
    } else {
      statParts.push(
        `❓ \`${statKey}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``
      );
    }
  }
  return statParts.join(" ");
}

/**
 * Handles the "Manage" button click on the storage embed
 * Shows a multi-select interface for choosing multiple items to sell/destroy at once
 *
 * @param {import('discord.js').ButtonInteraction} interaction - The button interaction
 */
async function handleManageButton(interaction) {
  await interaction.deferUpdate();

  const userId = interaction.user.id;
  const storageMessageId = interaction.message.id;
  storageMessageIds.set(userId, storageMessageId);

  // Disable both buttons to prevent multiple clicks
  try {
    const message = interaction.message;
    const components = message.components.map((row) => {
      const newRow = ActionRowBuilder.from(row);
      const newComponents = row.components.map((component) => {
        const newComponent = ButtonBuilder.from(component);
        return newComponent.setDisabled(true);
      });
      return newRow.setComponents(newComponents);
    });

    await message.edit({ components });
  } catch (error) {
    console.error("[Storage Command] Error disabling buttons:", error);
  }

  // Get fresh character data
  const freshCharacter = await getPlayerData(userId);
  if (!freshCharacter) {
    return interaction.followUp({
      content: "Failed to retrieve your character data.",
    });
  }

  // Get all items from config
  const allItems = configManager.getAllItems();
  if (!allItems) {
    return interaction.followUp({
      content: "Failed to load item definitions.",
    });
  }

  // Get all unequipped equipment from storage (excluding accessories)
  const storageItems =
    freshCharacter.inventory?.equipment?.filter((eq) => {
      if (eq.isEquipped) return false;
      const itemDetails = allItems[eq.itemKey];
      return itemDetails && itemDetails.type !== "ACCESSORY";
    }) || [];

  if (storageItems.length === 0) {
    return interaction.followUp({
      content: "You have no items in storage to manage.",
    });
  }

  // Create multi-select menu for item selection
  const menuId = `storage_quick_sell_menu_${uuidv4()}`;

  // Group items by type for better organization
  const itemsByType = {};

  storageItems.forEach((item) => {
    const itemDetails = allItems[item.itemKey];
    if (!itemDetails) return;

    const type = itemDetails.type || "Other";
    if (!itemsByType[type]) {
      itemsByType[type] = [];
    }

    itemsByType[type].push({
      item,
      details: itemDetails,
    });
  });

  // Create options for the select menu, grouped by type
  const options = [];

  Object.entries(itemsByType).forEach(([type, items]) => {
    // Add the actual items (no headers for multi-select to save space)
    items.forEach(({ item, details }) => {
      const sellPrice = details.sellPrice || 0;
      const canSell = details.sellable !== false && sellPrice > 0;

      options.push({
        label: `${details.name || item.itemKey}`,
        description: `${type} | ${canSell ? `Sell: ${sellPrice} coins` : "Cannot sell"} | ID: ${item.id.slice(0, 4)}`,
        value: item.id,
        emoji: details.emoji || "📦",
      });
    });
  });

  // Pagination for multi-select (Discord limit is 25 options)
  const maxOptionsPerPage = 23; // Leave room for navigation
  const totalPages = Math.ceil(options.length / maxOptionsPerPage);
  let currentPage = 0;

  // Function to generate components for the current page
  const generateComponents = (pageIndex) => {
    const startIdx = pageIndex * maxOptionsPerPage;
    const endIdx = Math.min(startIdx + maxOptionsPerPage, options.length);
    const pageOptions = options.slice(startIdx, endIdx);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(menuId)
      .setPlaceholder(
        `Select items to manage (Page ${pageIndex + 1}/${totalPages})`
      )
      .setMinValues(1)
      .setMaxValues(Math.max(1, Math.min(pageOptions.length, 25)))
      .addOptions(pageOptions);

    const components = [new ActionRowBuilder().addComponents(selectMenu)];

    // Add navigation buttons if we have multiple pages
    if (totalPages > 1) {
      const navigationRow = new ActionRowBuilder();

      const prevButton = new ButtonBuilder()
        .setCustomId(`${menuId}_prev`)
        .setLabel("Prev")
        .setStyle(ButtonStyle.Secondary)
        .setEmoji("⬅️")
        .setDisabled(pageIndex === 0);

      const nextButton = new ButtonBuilder()
        .setCustomId(`${menuId}_next`)
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setEmoji("➡️")
        .setDisabled(pageIndex === totalPages - 1);

      navigationRow.addComponents(prevButton, nextButton);
      components.push(navigationRow);
    }

    return components;
  };

  // Send the initial selection menu with an embed
  const components = generateComponents(currentPage);

  const selectionEmbed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle("Quick Storage Management")
    .setDescription(
      "Select multiple items to manage from your storage\nto manage them all at once."
    )
    .setFooter({ text: `📄 Page ${currentPage + 1}/${totalPages}` });

  const menuMessage = await interaction.followUp({
    embeds: [selectionEmbed],
    components,
  });

  // Store the menu message ID for this user
  menuMessageIds.set(userId, menuMessage.id);

  // Stop any existing collector for this user
  const existingCollector = activeCollectors.get(userId);
  if (existingCollector) {
    existingCollector.stop();
  }

  // Set up collector for the selection menu
  const collector = menuMessage.createMessageComponentCollector({
    filter: (i) => {
      return (
        (i.customId === menuId ||
          i.customId === `${menuId}_prev` ||
          i.customId === `${menuId}_next`) &&
        i.user.id === userId
      );
    },
    time: 30000, // 30 second timeout
  });

  // Store the new collector
  activeCollectors.set(userId, collector);

  // Clean up when the collector ends
  collector.on("end", async (collected, reason) => {
    if (activeCollectors.get(userId) === collector) {
      activeCollectors.delete(userId);

      // If the collector ended due to timeout, show the "unused menu" embed
      if (reason === "time") {
        try {
          const closedEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle("Storage Management")
            .setDescription("This menu was unused so it was closed.");

          await menuMessage.edit({
            embeds: [closedEmbed],
            components: [],
          });
        } catch (error) {
          console.error(
            `[Storage Command] Error closing unused menu for user ${userId}:`,
            error
          );
        }
      }
    }
  });

  collector.on("collect", async (i) => {
    try {
      // Handle pagination buttons
      if (i.customId === `${menuId}_prev`) {
        currentPage = Math.max(0, currentPage - 1);

        // Update both embed and components
        const updatedEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setTitle("Quick Storage Management")
          .setDescription(
            "Select multiple items to manage from your storage\nto manage them all at once."
          )
          .setFooter({ text: `📄 Page ${currentPage + 1}/${totalPages}` });

        await i.update({
          embeds: [updatedEmbed],
          components: generateComponents(currentPage),
        });
        return;
      }

      if (i.customId === `${menuId}_next`) {
        currentPage = Math.min(totalPages - 1, currentPage + 1);

        // Update both embed and components
        const updatedEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setTitle("Quick Storage Management")
          .setDescription(
            "Select multiple items to manage from your storage\nto manage them all at once."
          )
          .setFooter({ text: `📄 Page ${currentPage + 1}/${totalPages}` });

        await i.update({
          embeds: [updatedEmbed],
          components: generateComponents(currentPage),
        });
        return;
      }

      // Handle select menu interactions
      if (i.customId === menuId) {
        const selectedValues = i.values;
        const itemIds = selectedValues;

        if (itemIds.length === 0) {
          await i.deferUpdate();
          return;
        }

        // Get selected items
        const selectedItems = storageItems.filter((item) =>
          itemIds.includes(item.id)
        );

        if (selectedItems.length === 0) {
          await i.reply({
            content:
              "Selected items not found. They may have been removed from your storage.",
          });
          return;
        }

        // Handle bulk item actions
        await handleBulkItemActions(
          i,
          selectedItems,
          allItems,
          userId,
          interaction
        );
        return;
      }
    } catch (error) {
      console.error(
        "[Storage Command] Error in quick manage selection menu handler:",
        error
      );
      await i
        .reply({
          content: "An error occurred while processing your selection.",
        })
        .catch(() => {});
    }
  });
}

/**
 * Handles bulk actions for multiple selected items
 *
 * @param {import('discord.js').SelectMenuInteraction} interaction - The select menu interaction
 * @param {Array} selectedItems - Array of selected item objects
 * @param {Object} allItems - All item definitions from config
 * @param {string} userId - The user's Discord ID
 */
async function handleBulkItemActions(
  interaction,
  selectedItems,
  allItems,
  userId,
  originalInteraction
) {
  await interaction.deferUpdate();

  // Categorize items by whether they can be sold or not
  const sellableItems = [];
  const unsellableItems = [];

  selectedItems.forEach((item) => {
    const itemDetails = allItems[item.itemKey];
    if (!itemDetails) return;

    const sellPrice = itemDetails.sellPrice || 0;
    const canSell = itemDetails.sellable !== false && sellPrice > 0;

    if (canSell) {
      sellableItems.push({ item, details: itemDetails, sellPrice });
    } else {
      unsellableItems.push({ item, details: itemDetails });
    }
  });

  // Create action buttons - only Sell ALL and Cancel
  const actionButtons = [];
  const buttonIds = {};

  // Always show Sell ALL button (will sell what can be sold, destroy the rest)
  buttonIds.sellAll = `storage_bulk_sell_${uuidv4()}`;
  actionButtons.push(
    new ButtonBuilder()
      .setCustomId(buttonIds.sellAll)
      .setLabel("Sell ALL")
      .setStyle(ButtonStyle.Success)
      .setEmoji("<:purse_coins:1367849116033482772>")
  );

  buttonIds.cancel = `storage_bulk_cancel_${uuidv4()}`;
  actionButtons.push(
    new ButtonBuilder()
      .setCustomId(buttonIds.cancel)
      .setLabel("Cancel")
      .setStyle(ButtonStyle.Secondary)
  );

  // Split buttons into rows (max 5 per row)
  const rows = [];
  for (let i = 0; i < actionButtons.length; i += 5) {
    const rowButtons = actionButtons.slice(i, i + 5);
    rows.push(new ActionRowBuilder().addComponents(rowButtons));
  }

  // Create summary embed
  const summaryEmbed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle("Quick Storage Management")
    .setDescription(`You have selected **${selectedItems.length}** items.`);

  // Create item list with sell prices
  let itemList = "";
  selectedItems.forEach((item) => {
    const itemDetails = allItems[item.itemKey];
    if (!itemDetails) return;

    const sellPrice = itemDetails.sellPrice || 0;
    const emoji = itemDetails.emoji || "📦";
    const itemName = itemDetails.name || item.itemKey;
    const formattedPrice =
      sellPrice > 0 ? `${sellPrice.toLocaleString()}` : "0";

    itemList += `${emoji} ${itemName} (<:purse_coins:1367849116033482772> **${formattedPrice}**)\n`;
  });

  if (itemList) {
    summaryEmbed.setDescription(
      `You have selected **${selectedItems.length}** items.\n\n${itemList}`
    );
  }

  summaryEmbed.setFooter({
    text: "⚠️ Choose an action below. This will affect all selected items.",
  });

  // Send the confirmation message
  const confirmMessage = await interaction.followUp({
    embeds: [summaryEmbed],
    components: rows,
  });

  // Stop any existing collector for this user before creating a new one
  const existingCollector = activeCollectors.get(userId);
  if (existingCollector) {
    existingCollector.stop();
  }

  // Set up collector for the action buttons
  const collector = confirmMessage.createMessageComponentCollector({
    filter: (i) => {
      return (
        Object.values(buttonIds).includes(i.customId) && i.user.id === userId
      );
    },
    time: 60000, // 1 minute timeout
    max: 1, // Only allow one action
  });

  // Store the new collector
  activeCollectors.set(userId, collector);

  collector.on("collect", async (i) => {
    try {
      if (i.customId === buttonIds.cancel) {
        const cancelEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREY)
          .setTitle("❌ Action Cancelled")
          .setDescription("No changes were made to your storage.")
          .setFooter({ text: "⏰ This message will disappear in 10 seconds." });

        await i.update({
          embeds: [cancelEmbed],
          components: [],
        });

        setTimeout(async () => {
          try {
            await confirmMessage.delete();
          } catch (error) {
            console.error(
              "[Storage Command] Error deleting cancel message:",
              error
            );
          }
        }, 10000);
        return;
      }

      if (i.customId === buttonIds.sellAll) {
        // Process both selling and destroying in one action
        await processBulkSellAll(i, sellableItems, unsellableItems, userId);
        return;
      }
    } catch (error) {
      console.error("[Storage Command] Error in bulk action handler:", error);
      await i
        .reply({
          content: "An error occurred while processing your action.",
        })
        .catch(() => {});
    }
  });

  collector.on("end", async (collected, reason) => {
    // Clean up the collector from activeCollectors
    if (activeCollectors.get(userId) === collector) {
      activeCollectors.delete(userId);
    }

    // Refresh storage menu if action was completed successfully
    if (reason === "limit") {
      try {
        // Use the original interaction that was passed to handleBulkItemActions
        if (originalInteraction) {
          await refreshStorageMenu(originalInteraction, userId);
        }
      } catch (error) {
        console.error(
          "[Storage Command] Error refreshing storage menu after bulk action:",
          error
        );
      }
    }

    if (reason === "time") {
      try {
        const timeoutEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("⏰ Session Expired")
          .setDescription(
            "The bulk action selection has timed out. Please try again."
          )
          .setFooter({ text: "⏰ This message will disappear in 10 seconds." });

        await confirmMessage.edit({
          embeds: [timeoutEmbed],
          components: [],
        });

        setTimeout(async () => {
          try {
            await confirmMessage.delete();
          } catch (error) {
            console.error(
              "[Storage Command] Error deleting timeout message:",
              error
            );
          }
        }, 10000);
      } catch (error) {
        console.error("[Storage Command] Error handling timeout:", error);
      }
    }
  });
}

/**
 * Processes bulk sell all - sells sellable items and destroys unsellable items
 *
 * @param {import('discord.js').ButtonInteraction} interaction - The button interaction
 * @param {Array} sellableItems - Array of sellable items
 * @param {Array} unsellableItems - Array of unsellable items
 * @param {string} userId - The user's Discord ID
 */
async function processBulkSellAll(
  interaction,
  sellableItems,
  unsellableItems,
  userId
) {
  await interaction.deferUpdate();

  try {
    const totalValue = sellableItems.reduce(
      (sum, { sellPrice }) => sum + sellPrice,
      0
    );
    const sellableItemIds = sellableItems.map(({ item }) => item.id);
    const unsellableItemIds = unsellableItems.map(({ item }) => item.id);
    const allItemIds = [...sellableItemIds, ...unsellableItemIds];

    // Remove all items from inventory and add coins for sellable items
    await updateInventoryAtomically(
      userId,
      totalValue, // Add total coins from sellable items
      [], // No stackable items to change
      [], // No equipment to add
      allItemIds // All equipment IDs to remove (both sellable and unsellable)
    );

    // Create success embed
    let description = "";
    if (sellableItems.length > 0) {
      description += `Sold **${sellableItems.length}** items for **${totalValue}** ${CURRENCY.name}.`;
    }
    if (unsellableItems.length > 0) {
      if (description) description += "\n";
      description += `Destroyed **${unsellableItems.length}** unsellable items.`;
    }

    // Create detailed summary of processed items
    let detailedSummary = "";

    if (sellableItems.length > 0) {
      detailedSummary += "**Items Sold:**\n";
      sellableItems.forEach((sellableItem) => {
        const itemDetails = sellableItem.details;
        if (itemDetails) {
          const emoji = itemDetails.emoji || "📦";
          const itemName = itemDetails.name || sellableItem.item.itemKey;
          const sellPrice = sellableItem.sellPrice;
          detailedSummary += `${emoji} ${itemName} (<:purse_coins:1367849116033482772> **${sellPrice.toLocaleString()}**)\n`;
        }
      });
      detailedSummary += `\n<:purse_coins:1367849116033482772> **Total Earned:** ${totalValue.toLocaleString()} Coins\n`;
    }

    if (unsellableItems.length > 0) {
      if (detailedSummary) detailedSummary += "\n";
      detailedSummary += "**Items Destroyed:**\n";
      unsellableItems.forEach((unsellableItem) => {
        const itemDetails = unsellableItem.details;
        if (itemDetails) {
          const emoji = itemDetails.emoji || "📦";
          const itemName = itemDetails.name || unsellableItem.item.itemKey;
          detailedSummary += `${emoji} ${itemName}\n`;
        }
      });
    }

    const successEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Quick Storage Management")
      .setDescription(detailedSummary)
      .setFooter({
        text: "Your coins have been added to your purse.",
      });

    await interaction.editReply({
      embeds: [successEmbed],
      components: [],
    });

    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        console.error(
          "[Storage Command] Error deleting success message:",
          error
        );
      }
    }, 10000);

    // Don't refresh the storage menu here - it will be handled by the bulk action collector
    // This prevents creating conflicting collectors with different menuIds
  } catch (error) {
    console.error("[Storage Command] Error in bulk sell all:", error);

    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ Error")
      .setDescription(
        `Failed to process items: ${error.message || "Unknown error"}`
      )
      .setFooter({ text: "⏰ This message will disappear in 10 seconds." });

    await interaction.editReply({
      embeds: [errorEmbed],
      components: [],
    });

    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        console.error("[Storage Command] Error deleting error message:", error);
      }
    }, 10000);
  }
}

/**
 * Refreshes the storage management menu with updated inventory
 *
 * @param {import('discord.js').ButtonInteraction|import('discord.js').SelectMenuInteraction} interaction - The interaction
 * @param {string} userId - The user's Discord ID
 */
async function refreshStorageMenu(interaction, userId) {
  try {
    const channel = interaction.channel;

    // Get fresh character data to ensure we have the latest inventory
    const freshCharacter = await getPlayerData(userId);
    if (!freshCharacter) {
      console.error(
        "[Storage Command] Failed to get fresh character data for refresh"
      );
      return;
    }

    // Get all items from config
    const allItems = configManager.getAllItems();
    if (!allItems) {
      console.error(
        "[Storage Command] Failed to load item definitions for refresh"
      );
      return;
    }

    // Get all unequipped equipment from storage (excluding accessories)
    const storageItems =
      freshCharacter.inventory?.equipment?.filter((eq) => {
        if (eq.isEquipped) return false;
        const itemDetails = allItems[eq.itemKey];
        return itemDetails && itemDetails.type !== "ACCESSORY";
      }) || [];

    // Check if there are any items left in storage
    if (storageItems.length === 0) {
      // Update the original /storage message
      const storageMessageId = storageMessageIds.get(userId);
      if (storageMessageId) {
        try {
          const storageMessage = await channel.messages.fetch(storageMessageId);

          // Create an empty storage embed
          const emptyStorageEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle(`${freshCharacter.name}'s Storage`)
            .setDescription("You have no items in storage.");

          // Keep the original Manage button
          const manageButton = storageMessage.components[0].components[0];
          const storageRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId(manageButton.customId)
              .setLabel(manageButton.label)
              .setStyle(manageButton.style)
              .setEmoji(manageButton.emoji?.name || "🔧")
              .setDisabled(true) // Disable the button since there are no items
          );

          // Update the storage message
          await storageMessage.edit({
            embeds: [emptyStorageEmbed],
            components: [storageRow],
          });
        } catch (error) {
          console.error(
            `[Storage Command] Error updating empty storage message:`,
            error
          );
        }
      }

      // Update the menu message to show empty state
      const menuMessageId = menuMessageIds.get(userId);
      if (menuMessageId) {
        try {
          const menuMessage = await channel.messages.fetch(menuMessageId);

          // Create an empty menu embed
          const emptyMenuEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle("Storage Management")
            .setDescription("You have no items left in storage to manage.");

          // Update the menu message with no components
          await menuMessage.edit({
            embeds: [emptyMenuEmbed],
            components: [],
          });

          // Stop any existing collector
          const existingCollector = activeCollectors.get(userId);
          if (existingCollector) {
            existingCollector.stop();
          }
        } catch (error) {
          console.error(
            `[Storage Command] Error updating empty menu message:`,
            error
          );
        }
      }

      return; // Exit early since there's nothing else to do
    }

    // STEP 1: Update the original /storage message
    // Get the ID from our map
    const storageMessageId = storageMessageIds.get(userId);

    if (storageMessageId) {
      try {
        const storageMessage = await channel.messages.fetch(storageMessageId);

        // Recreate the original storage embed with updated data
        const storageEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setTitle(`${freshCharacter.name}'s Storage`);
        const categories = {
          Weapons: [],
          "Armor Pieces": [],
          Equipments: [],
          Tools: [],
        };

        // Process all equipment items
        freshCharacter.inventory?.equipment?.forEach((eq) => {
          if (!eq.isEquipped) {
            const itemDetails = allItems[eq.itemKey];
            if (!itemDetails) return;

            // Determine display category
            let displayCategory = "Equipments";
            if (
              itemDetails.type === "TOOL" &&
              ["AXE", "HOE", "PICKAXE", "ROD", "SHOVEL"].includes(
                itemDetails.subtype
              )
            ) {
              displayCategory = "Tools";
            } else if (
              ["SWORD", "BOW", "AXE"].includes(itemDetails.subtype) ||
              itemDetails.type === "WEAPON"
            ) {
              displayCategory = "Weapons";
            } else if (
              ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"].includes(
                itemDetails.subtype
              ) ||
              itemDetails.type === "ARMOR"
            ) {
              displayCategory = "Armor Pieces";
              // Skip accessories (talismans, rings, etc.)
            } else if (itemDetails.type === "ACCESSORY") {
              return; // Skip accessories completely
            } else if (
              ["HOE", "PICKAXE", "ROD", "SHOVEL"].includes(
                itemDetails.subtype
              ) ||
              itemDetails.type === "TOOL"
            ) {
              displayCategory = "Tools";
            }

            const emoji = itemDetails.emoji || "❓";
            let itemDisplay = `\`${eq.id.slice(0, 4)}\` ${emoji} **${
              itemDetails.name
            }**`;

            // Process stats and enchantments (simplified for brevity)
            let statsString = "";
            if (
              itemDetails.baseStats &&
              Object.keys(itemDetails.baseStats).length > 0
            ) {
              const statLines = [];
              for (const [statKey, value] of Object.entries(
                itemDetails.baseStats
              )) {
                const statConfig = STATS[statKey];
                if (statConfig) {
                  const displayValue = statConfig.isPercentage
                    ? `${value > 0 ? "+" : ""}${value}%`
                    : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;

                  const statName =
                    STAT_ABBREVIATIONS?.[statConfig.name] ||
                    statConfig.name ||
                    statKey;
                  statLines.push(
                    `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``
                  );
                } else {
                  statLines.push(
                    `❓ \`${statKey}: ${
                      value > 0 ? "+" : ""
                    }${value.toLocaleString()}\``
                  );
                }
              }
              statsString = statLines.join(" ");
            }

            // Add stats if available
            if (statsString) {
              itemDisplay += `\n${statsString}`;
            }

            categories[displayCategory].push(itemDisplay);
          }
        });

        // Build the embed description with pagination support
        const displayItems = [];
        Object.entries(categories).forEach(([category, items]) => {
          if (items.length > 0) {
            displayItems.push(`**${category}:**`);
            items.forEach((item) => displayItems.push(item));
            displayItems.push(""); // Add spacing between categories
          } else {
            displayItems.push(`**${category}:**`);
            displayItems.push("_Nothing in here_");
            displayItems.push(""); // Add spacing between categories
          }
        });

        // Remove the last empty line
        if (displayItems[displayItems.length - 1] === "") {
          displayItems.pop();
        }

        // Calculate pagination - aim for ~3000 characters per page to stay well under 4096 limit
        const maxCharsPerPage = 3000;
        const pages = [];
        let currentPage = [];
        let currentLength = 0;

        for (const item of displayItems) {
          const itemLength = item.length + 1; // +1 for newline

          // If adding this item would exceed the limit, start a new page
          if (
            currentLength + itemLength > maxCharsPerPage &&
            currentPage.length > 0
          ) {
            pages.push(currentPage.join("\n"));
            currentPage = [item];
            currentLength = itemLength;
          } else {
            currentPage.push(item);
            currentLength += itemLength;
          }
        }

        // Add the last page if it has content
        if (currentPage.length > 0) {
          pages.push(currentPage.join("\n"));
        }

        // If no items at all, create a single page
        if (pages.length === 0) {
          pages.push(
            "**Weapons:**\n_Nothing in here_\n\n**Armor Pieces:**\n_Nothing in here_\n\n**Equipments:**\n_Nothing in here_\n\n**Tools:**\n_Nothing in here_"
          );
        }

        // Use the first page for the updated storage display
        storageEmbed.setDescription(pages[0]);
        storageEmbed.setFooter({ text: `Page 1/${pages.length} • Updated` });

        // Keep the original components structure but disable manage button during active management
        const originalComponents = storageMessage.components;
        const updatedComponents = [];

        // If there were navigation buttons, recreate them (disabled during management)
        if (originalComponents.length > 1) {
          const navRow = new ActionRowBuilder();
          const originalNavButtons = originalComponents[0].components;

          originalNavButtons.forEach((button) => {
            navRow.addComponents(
              new ButtonBuilder()
                .setCustomId(button.customId)
                .setLabel(button.label)
                .setStyle(button.style)
                .setEmoji(button.emoji?.name || button.emoji?.id || "")
                .setDisabled(true) // Disable during management
            );
          });

          updatedComponents.push(navRow);
        }

        // Add the manage button (disabled) - use consistent style
        const storageRow = new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId(`storage_manage_${uuidv4()}`)
            .setLabel("Manage")
            .setStyle(ButtonStyle.Primary)
            .setDisabled(true) // Always keep it disabled during active management
        );

        updatedComponents.push(storageRow);

        // Update the storage message
        await storageMessage.edit({
          embeds: [storageEmbed],
          components: updatedComponents,
        });
      } catch (error) {
        console.error(
          `[Storage Command] Error updating storage message with ID ${storageMessageId}:`,
          error
        );
        // Continue even if updating the storage message fails
      }
    }

    // STEP 2: Find the menu message in the channel
    // Try to find it by ID first, then fall back to searching
    let menuMessage = null;
    const menuMessageId = menuMessageIds.get(userId);

    if (menuMessageId) {
      try {
        menuMessage = await channel.messages.fetch(menuMessageId);
      } catch (error) {
        console.error(
          `[Storage Command] Error fetching menu message with ID ${menuMessageId}:`,
          error
        );
        menuMessage = null;
      }
    }

    // If we couldn't get the message by ID, try to find it by components
    if (!menuMessage) {
      const messages = await channel.messages.fetch({ limit: 10 });
      menuMessage = messages.find(
        (msg) =>
          msg.components.length > 0 &&
          msg.components[0].components.length > 0 &&
          msg.components[0].components[0].customId &&
          msg.components[0].components[0].customId.startsWith(
            "storage_quick_sell_menu_"
          )
      );
    }

    if (!menuMessage) {
      console.error(
        "[Storage Command] Could not find the menu message to update"
      );
      return;
    }

    // Create a new select menu for item selection (use same pattern as quick sell menu)
    const menuId = `storage_quick_sell_menu_${uuidv4()}`;

    // Group items by type for better organization
    const itemsByType = {};

    storageItems.forEach((item) => {
      const itemDetails = allItems[item.itemKey];
      if (!itemDetails) return;

      const type = itemDetails.type || "Other";
      if (!itemsByType[type]) {
        itemsByType[type] = [];
      }

      itemsByType[type].push({
        item,
        details: itemDetails,
      });
    });

    // Create options for the select menu, grouped by type
    const options = [];

    Object.entries(itemsByType).forEach(([type, items]) => {
      // Add a non-selectable group header
      if (items.length > 0) {
        options.push({
          label: `--- ${type} ---`,
          value: `header_${type}`,
          description: `${items.length} items`,
          default: false,
          disabled: true,
        });

        // Add the actual items
        items.forEach(({ item, details }) => {
          const sellPrice = details.sellPrice || 0;
          options.push({
            label: details.name || item.itemKey,
            description: `Sell value: ${sellPrice} coins (ID: ${item.id.slice(
              0,
              4
            )})`,
            value: item.id,
            emoji: details.emoji || "❓",
          });
        });
      }
    });

    // Ensure we have at least one selectable option (not just headers)
    const selectableOptions = options.filter((option) => !option.disabled);
    if (selectableOptions.length === 0) {
      // Add a placeholder option to prevent Discord API error
      options.push({
        label: "No items available",
        description: "All items have been sold or removed",
        value: "no_items",
        disabled: true,
      });
    }

    // If we have too many options, we need to paginate
    // Discord has a limit of 25 options per select menu
    const maxOptionsPerPage = 23; // Leave room for navigation options
    const totalPages = Math.ceil(options.length / maxOptionsPerPage);
    let currentPage = 0;

    // Function to generate components for the current page
    const generateComponents = (pageIndex) => {
      const startIdx = pageIndex * maxOptionsPerPage;
      const endIdx = Math.min(startIdx + maxOptionsPerPage, options.length);
      const pageOptions = options.slice(startIdx, endIdx);

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(menuId)
        .setPlaceholder(
          `Select items to manage (Page ${pageIndex + 1}/${totalPages})`
        )
        .setMinValues(1)
        .setMaxValues(
          Math.max(
            1,
            Math.min(pageOptions.filter((opt) => !opt.disabled).length, 25)
          )
        )
        .addOptions(pageOptions);

      const components = [new ActionRowBuilder().addComponents(selectMenu)];

      // Add navigation buttons if we have multiple pages
      if (totalPages > 1) {
        const navigationRow = new ActionRowBuilder();

        const prevButton = new ButtonBuilder()
          .setCustomId(`${menuId}_prev`)
          .setLabel("Prev")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("⬅️")
          .setDisabled(pageIndex === 0);

        const nextButton = new ButtonBuilder()
          .setCustomId(`${menuId}_next`)
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("➡️")
          .setDisabled(pageIndex === totalPages - 1);

        navigationRow.addComponents(prevButton, nextButton);
        components.push(navigationRow);
      }

      return components;
    };

    // Create an embed for the refreshed menu
    const refreshedEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle("Storage Management")
      .setDescription(
        "Select multiple items to manage from your storage\nto manage them all at once."
      )
      .setFooter({
        text: `📄 Page ${currentPage + 1}/${totalPages} • Updated inventory`,
      });

    // Update the menu message with the new menu
    const components = generateComponents(currentPage);
    await menuMessage.edit({
      embeds: [refreshedEmbed],
      components,
    });

    // Store the updated menu ID
    menuMessageIds.set(userId, menuMessage.id);

    // Stop any existing collector for this user
    const existingCollector = activeCollectors.get(userId);
    if (existingCollector) {
      existingCollector.stop();
    }

    // Set up a new collector for the selection menu and navigation buttons
    const collector = menuMessage.createMessageComponentCollector({
      filter: (i) => {
        return (
          (i.customId === menuId ||
            i.customId === `${menuId}_prev` ||
            i.customId === `${menuId}_next`) &&
          i.user.id === userId
        );
      },
      time: 30000, // 30 second timeout
    });

    // Store the new collector
    activeCollectors.set(userId, collector);

    // Clean up when the collector ends
    collector.on("end", async (collected, reason) => {
      // Only remove if this is still the active collector for this user
      if (activeCollectors.get(userId) === collector) {
        activeCollectors.delete(userId);

        // If the collector ended due to timeout, show the "unused menu" embed
        if (reason === "time") {
          try {
            const closedEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.BLUE)
              .setTitle("Storage Management")
              .setDescription("This menu was unused so it was closed.");

            await menuMessage.edit({
              embeds: [closedEmbed],
              components: [],
            });
          } catch (error) {
            console.error(
              `[Storage Command] Error closing unused menu for user ${userId}:`,
              error
            );
          }
        }

        // We no longer re-enable the Manage button automatically
        // The button will remain disabled until the user runs /storage again
      }
    });

    collector.on("collect", async (i) => {
      try {
        // Handle pagination buttons
        if (i.customId === `${menuId}_prev`) {
          currentPage = Math.max(0, currentPage - 1);
          await i.update({
            components: generateComponents(currentPage),
          });
          return;
        }

        if (i.customId === `${menuId}_next`) {
          currentPage = Math.min(totalPages - 1, currentPage + 1);
          await i.update({
            components: generateComponents(currentPage),
          });
          return;
        }

        // Handle select menu interactions
        if (i.customId === menuId) {
          const selectedValues = i.values;
          const itemIds = selectedValues;

          if (itemIds.length === 0) {
            await i.deferUpdate();
            return;
          }

          // Get selected items
          const selectedItems = storageItems.filter((item) =>
            itemIds.includes(item.id)
          );

          if (selectedItems.length === 0) {
            await i.reply({
              content:
                "Selected items not found. They may have been removed from your storage.",
            });
            return;
          }

          // Handle bulk item actions
          await handleBulkItemActions(
            i,
            selectedItems,
            allItems,
            userId,
            interaction
          );
          return;
        }
      } catch (error) {
        console.error(
          "[Storage Command] Error in selection menu handler:",
          error
        );
        await i
          .reply({
            content: "An error occurred while processing your selection.",
          })
          .catch(() => {});
      }
    });
  } catch (error) {
    console.error("[Storage Command] Error refreshing storage menu:", error);
  }
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("storage")
    .setDescription("View your equipment storage"),
  async execute(interaction) {
    let character;
    try {
      character = await getPlayerData(interaction.user.id);
    } catch (playerDataError) {
      console.error(
        `[Storage Command] Error fetching player data for ${interaction.user.id}: [STR-DAT-ERR]`,
        playerDataError
      );
      return interaction.reply({
        content:
          "[STR-DAT-ERR] Failed to retrieve your character data. Please report this error code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    try {
      if (!character)
        return interaction.reply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });

      // Basic permission check
      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.reply({
          content:
            "You don't have permission to use this command (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const categories = {
        Weapons: [],
        "Armor Pieces": [],
        Equipments: [],
        Tools: [],
      };

      let allItems;
      try {
        allItems = configManager.getAllItems();
        if (!allItems)
          throw new Error("getAllItems returned null or undefined");
      } catch (configError) {
        console.error(
          "[Storage Command] Error loading item data: [STR-CFG-001]",
          configError
        );
        return interaction.reply({
          content:
            "[STR-CFG-001] Failed to load item definitions. Please report this error code to an Admin.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      character.inventory?.equipment?.forEach((eq) => {
        if (!eq.isEquipped) {
          const itemDetails = allItems[eq.itemKey];
          if (!itemDetails) {
            console.warn(
              `[Storage Command] Equipment item has no matching definition in item config for key: ${eq.itemKey}`
            );
            return;
          }

          // Determine display category
          let displayCategory = "Equipments";
          if (
            itemDetails.type === "TOOL" &&
            ["AXE", "HOE", "PICKAXE", "ROD", "SHOVEL"].includes(
              itemDetails.subtype
            )
          ) {
            displayCategory = "Tools";
          } else if (
            ["SWORD", "BOW", "AXE"].includes(itemDetails.subtype) ||
            itemDetails.type === "WEAPON"
          ) {
            displayCategory = "Weapons";
          } else if (
            ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"].includes(
              itemDetails.subtype
            ) ||
            itemDetails.type === "ARMOR"
          ) {
            displayCategory = "Armor Pieces";
            // Skip accessories (talismans, rings, etc.)
          } else if (itemDetails.type === "ACCESSORY") {
            return; // Skip accessories completely
          } else if (
            ["HOE", "PICKAXE", "ROD", "SHOVEL"].includes(itemDetails.subtype) ||
            itemDetails.type === "TOOL"
          ) {
            displayCategory = "Tools";
          }

          const emoji = itemDetails.emoji || "❓";
          // const rarityInfo = (typeof itemDetails.rarity === 'object' && itemDetails.rarity)
          //     ? `(${itemDetails.rarity.name})`
          //     : '';

          // Parse data_json if it's a string
          let dataJson = {}; // Initialize dataJson as an empty object
          if (eq.data_json) {
            // Ensure data_json is an object
            if (typeof eq.data_json === "string") {
              try {
                dataJson = JSON.parse(eq.data_json);
              } catch (error) {
                console.error(
                  `[Storage Command] Error parsing data_json for item ${eq.id}:`,
                  error
                );
                // Keep dataJson as {} if parsing fails
              }
            } else {
              dataJson = eq.data_json; // If it's already an object
            }
          }

          // Check for reforge data and modify item name if reforged
          let displayName = itemDetails.name;
          if (dataJson.reforge) {
            const {
              getDynamicReforgeName,
            } = require("../utils/dynamicReforgeStats");
            const dynamicReforgeName = getDynamicReforgeName(
              dataJson.reforge,
              itemDetails
            );
            displayName = `${dynamicReforgeName} ${itemDetails.name}`;
          }

          let itemDisplay = `\`${eq.id.slice(0, 4)}\` ${emoji} **${
            displayName
          }**`; // Removed rarityInfo

          // Add enchantment text to display
          const enchantmentText = "";
          let statsString = "";

          // dataJson is already parsed and an object here (or {} if initial parsing failed or eq.data_json was null/undefined).
          // The `if (eq.data_json)` check might seem redundant if dataJson is already populated,
          // but it can serve as a safeguard if dataJson somehow wasn't initialized correctly earlier,
          // or if we only want to proceed with enchantment/HPB logic if there was original data_json.
          // However, the re-parsing `dataJson = JSON.parse(dataJson)` is definitely not needed here
          // as dataJson should already be an object.

          // If eq.data_json was not present initially, dataJson would be {}, so enchantments/HPB wouldn't be found.
          // If eq.data_json was present and parsed, dataJson holds that parsed object.
          // If eq.data_json was present but failed to parse, dataJson is {}.
          if (dataJson && Object.keys(dataJson).length > 0) {
            // Process enchantments, Hot Potato Books, and Reforge stats
            if (
              (dataJson.enchantments &&
                Object.keys(dataJson.enchantments).length > 0) ||
              dataJson.hotPotatoBooks ||
              (dataJson.reforge && dataJson.reforge.stats)
            ) {
              const enchantments = dataJson.enchantments || {};
              const hotPotatoBooks = dataJson.hotPotatoBooks || 0;

              // Create modified stats based on enchantments, Hot Potato Books, and Reforge
              const modifiedStats = { ...(itemDetails.baseStats || {}) };
              let statsModified = false;

              // Apply dynamic reforge stats if they exist
              if (dataJson.reforge) {
                const {
                  calculateDynamicReforgeStats,
                } = require("../utils/dynamicReforgeStats");
                const dynamicReforgeStats = calculateDynamicReforgeStats(
                  dataJson.reforge,
                  itemDetails
                );
                if (Object.keys(dynamicReforgeStats).length > 0) {
                  for (const [statKey, value] of Object.entries(
                    dynamicReforgeStats
                  )) {
                    if (!modifiedStats[statKey]) modifiedStats[statKey] = 0;
                    modifiedStats[statKey] += value;
                    statsModified = true;
                  }
                }
              }

              // Apply Sharpness enchantment (affects DAMAGE)
              if (enchantments.SHARPNESS && modifiedStats.DAMAGE) {
                const level = enchantments.SHARPNESS;
                const damageMultBonus = level === 5 ? 30 : level * 5; // 5% per level, 30% at level 5
                const baseDamage = modifiedStats.DAMAGE;
                const bonusDamage = Math.floor(
                  baseDamage * (damageMultBonus / 100)
                );

                modifiedStats.DAMAGE = baseDamage + bonusDamage;
                statsModified = true;
              }

              // Apply Protection enchantment (affects DEFENSE)
              if (enchantments.PROTECTION) {
                const level = enchantments.PROTECTION;
                const defenseBonus = level * 4; // 4 defense per level

                if (!modifiedStats.DEFENSE) modifiedStats.DEFENSE = 0;
                const baseDefense = modifiedStats.DEFENSE;

                modifiedStats.DEFENSE = baseDefense + defenseBonus;
                statsModified = true;
              }

              // Apply Growth enchantment (affects HEALTH)
              if (enchantments.GROWTH) {
                const level = enchantments.GROWTH;
                const healthBonus = level * 15; // 15 health per level

                if (!modifiedStats.HEALTH) modifiedStats.HEALTH = 0;
                const baseHealth = modifiedStats.HEALTH;

                modifiedStats.HEALTH = baseHealth + healthBonus;
                statsModified = true;
              }

              // Apply Critical enchantment (affects CRIT_DAMAGE)
              if (enchantments.CRITICAL) {
                const level = enchantments.CRITICAL;
                const critDamageBonus = level * 10; // 10 crit damage per level

                if (!modifiedStats.CRIT_DAMAGE) modifiedStats.CRIT_DAMAGE = 0;
                const baseCritDamage = modifiedStats.CRIT_DAMAGE;

                modifiedStats.CRIT_DAMAGE = baseCritDamage + critDamageBonus;
                statsModified = true;
              }

              // Apply Hot Potato Book bonuses if they exist
              if (hotPotatoBooks > 0) {
                // Determine item category for stat bonuses
                let category = null;
                if (itemDetails.type === "ARMOR") category = "ARMOR";
                else if (itemDetails.type === "WEAPON") category = "WEAPON";

                if (category) {
                  // Define Hot Potato Book stat bonuses
                  const HOT_POTATO_STATS = {
                    ARMOR: { HEALTH: 4, DEFENSE: 2 },
                    WEAPON: { DAMAGE: 2, STRENGTH: 2 },
                  };

                  const bonusPerBook = HOT_POTATO_STATS[category];

                  for (const [stat, value] of Object.entries(bonusPerBook)) {
                    const totalBonus = value * hotPotatoBooks;

                    // Initialize stat if it doesn't exist in base stats
                    if (!modifiedStats[stat]) {
                      modifiedStats[stat] = itemDetails.baseStats?.[stat] || 0;
                    }
                    modifiedStats[stat] += totalBonus;
                    statsModified = true;
                  }
                }
              }

              // Format the modified stats
              if (Object.keys(modifiedStats).length > 0) {
                const statLines = [];

                // For each stat, format it and add sparkles only for enchantments
                for (const [statKey, value] of Object.entries(modifiedStats)) {
                  // Skip stats with 0 value
                  if (value === 0) continue;

                  const statConfig = STATS[statKey];
                  if (statConfig) {
                    const displayValue = statConfig.isPercentage
                      ? `${value > 0 ? "+" : ""}${value}%`
                      : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;

                    // Check if this stat is different from base because of modifications
                    const originalValue = itemDetails.baseStats?.[statKey] || 0;
                    const isModified = statsModified && originalValue !== value;

                    // Use abbreviation if available, otherwise use the full name
                    const statName =
                      STAT_ABBREVIATIONS[statConfig.name] ||
                      statConfig.name ||
                      statKey;

                    // Determine what type of modification this is
                    let indicator = "";
                    if (isModified) {
                      // Check if this stat is affected by enchantments
                      const hasEnchantmentBonus =
                        enchantments &&
                        ((statKey === "DAMAGE" && enchantments.SHARPNESS) ||
                          (statKey === "DEFENSE" && enchantments.PROTECTION) ||
                          (statKey === "HEALTH" && enchantments.GROWTH) ||
                          (statKey === "CRIT_DAMAGE" && enchantments.CRITICAL));

                      // Add sparkles for enchantments
                      if (hasEnchantmentBonus) {
                        indicator = " ✨"; // Enchantment only
                      }
                    }

                    // Add the stat line with appropriate indicator
                    if (value !== 0) {
                      statLines.push(
                        `${
                          statConfig.emoji || ""
                        } \`${statName}: ${displayValue}${indicator}\``
                      );
                    }
                  } else {
                    // Handle unknown stats
                    if (value !== 0) {
                      statLines.push(
                        `❓ \`${statKey}: ${
                          value > 0 ? "+" : ""
                        }${value.toLocaleString()}\``
                      );
                    }
                  }
                }

                // Join all stat lines
                statsString = statLines.join(" ");
              }
            } else {
              // No enchantments, show base stats if available
              if (
                itemDetails.baseStats &&
                Object.keys(itemDetails.baseStats).length > 0
              ) {
                statsString = formatStats(itemDetails.baseStats);
              }
            }
          } else {
            // No data_json at all, show base stats if available
            if (
              itemDetails.baseStats &&
              Object.keys(itemDetails.baseStats).length > 0
            ) {
              statsString = formatStats(itemDetails.baseStats);
            }
          }

          // Add enchantment text first
          if (enchantmentText) {
            itemDisplay += enchantmentText;
          }

          // Then add stats
          if (statsString) {
            itemDisplay += `\n${statsString}`;
          }

          categories[displayCategory].push(itemDisplay);
        }
      });

      // Build the embed description with pagination support
      const storageDisplayItems = [];
      Object.entries(categories).forEach(([category, items]) => {
        if (items.length > 0) {
          storageDisplayItems.push(`**${category}:**`);
          items.forEach((item) => storageDisplayItems.push(item));
          storageDisplayItems.push(""); // Add spacing between categories
        } else {
          storageDisplayItems.push(`**${category}:**`);
          storageDisplayItems.push("_Nothing in here_");
          storageDisplayItems.push(""); // Add spacing between categories
        }
      });

      // Remove the last empty line
      if (storageDisplayItems[storageDisplayItems.length - 1] === "") {
        storageDisplayItems.pop();
      }

      // Calculate pagination - aim for ~3000 characters per page to stay well under 4096 limit
      const maxCharsPerPage = 3000;
      const pages = [];
      let currentPage = [];
      let currentLength = 0;

      for (const item of storageDisplayItems) {
        const itemLength = item.length + 1; // +1 for newline

        // If adding this item would exceed the limit, start a new page
        if (
          currentLength + itemLength > maxCharsPerPage &&
          currentPage.length > 0
        ) {
          pages.push(currentPage.join("\n"));
          currentPage = [item];
          currentLength = itemLength;
        } else {
          currentPage.push(item);
          currentLength += itemLength;
        }
      }

      // Add the last page if it has content
      if (currentPage.length > 0) {
        pages.push(currentPage.join("\n"));
      }

      // If no items at all, create a single page
      if (pages.length === 0) {
        pages.push(
          "**Weapons:**\n_Nothing in here_\n\n**Armor Pieces:**\n_Nothing in here_\n\n**Equipments:**\n_Nothing in here_\n\n**Tools:**\n_Nothing in here_"
        );
      }

      let currentPageIndex = 0;
      const totalPages = pages.length;

      // Function to create embed for current page
      const createPageEmbed = (pageIndex) => {
        const pageEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setTitle(`${character.name}'s Storage`)
          .setDescription(pages[pageIndex]);

        // Always show page footer
        pageEmbed.setFooter({ text: `Page ${pageIndex + 1}/${totalPages}` });

        return pageEmbed;
      };

      // Create navigation components
      const createComponents = (pageIndex) => {
        const components = [];

        // Navigation row (always show, but disable when not needed)
        const navRow = new ActionRowBuilder();

        navRow.addComponents(
          new ButtonBuilder()
            .setCustomId(`storage_prev_${uuidv4()}`)
            .setLabel("Previous")
            .setStyle(ButtonStyle.Secondary)
            .setEmoji("⬅️")
            .setDisabled(pageIndex === 0 || totalPages === 1)
        );

        navRow.addComponents(
          new ButtonBuilder()
            .setCustomId(`storage_next_${uuidv4()}`)
            .setLabel("Next")
            .setStyle(ButtonStyle.Secondary)
            .setEmoji("➡️")
            .setDisabled(pageIndex === totalPages - 1 || totalPages === 1)
        );

        components.push(navRow);

        // Manage button row
        const manageButtonId = `storage_manage_${uuidv4()}`;
        const manageRow = new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId(manageButtonId)
            .setLabel("Manage")
            .setStyle(ButtonStyle.Primary)
        );

        components.push(manageRow);
        return components;
      };

      const reply = await interaction.reply({
        embeds: [createPageEmbed(currentPageIndex)],
        components: createComponents(currentPageIndex),
      });

      // Store the storage message ID for this user
      storageMessageIds.set(interaction.user.id, reply.id);

      // Set up collector for all buttons (navigation and manage)
      const collector = reply.createMessageComponentCollector({
        filter: (i) =>
          i.user.id === interaction.user.id &&
          (i.customId.startsWith("storage_prev_") ||
            i.customId.startsWith("storage_next_") ||
            i.customId.startsWith("storage_manage_")),
        time: 300000, // 5 minute timeout
        // No max limit so buttons can be used multiple times
      });

      collector.on("collect", async (i) => {
        try {
          if (i.customId.startsWith("storage_prev_")) {
            // Previous page
            if (currentPageIndex > 0) {
              currentPageIndex--;
              await i.update({
                embeds: [createPageEmbed(currentPageIndex)],
                components: createComponents(currentPageIndex),
              });
            } else {
              await i.deferUpdate();
            }
          } else if (i.customId.startsWith("storage_next_")) {
            // Next page
            if (currentPageIndex < totalPages - 1) {
              currentPageIndex++;
              await i.update({
                embeds: [createPageEmbed(currentPageIndex)],
                components: createComponents(currentPageIndex),
              });
            } else {
              await i.deferUpdate();
            }
          } else if (i.customId.startsWith("storage_manage_")) {
            // Manage button (unified functionality)
            await handleManageButton(i);
          } else {
            await i.deferUpdate();
          }
        } catch (error) {
          console.error("[Storage Command] Error in button handler:", error);
          await i
            .reply({
              content: "An error occurred while processing your request.",
              flags: [MessageFlags.Ephemeral],
            })
            .catch(() => {});
        }
      });
    } catch (error) {
      console.error(
        "[Storage Command] Error executing command: [STR-CMD-ERR]",
        error
      );
      return interaction.reply({
        content:
          "[STR-CMD-ERR] An error occurred while executing the command. Please report this error code to an Admin.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },
};
