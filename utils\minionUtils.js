const configManager = require("./configManager");
const { dbRunQueued, dbGet } = require("./dbUtils");
const { withMinionLock } = require("./minionMutex");

/**
 * Checks if a minion is full based on its properties
 * @param {Object} minion - The minion object
 * @param {Object} minionData - The minion type data from items.json
 * @returns {boolean} - Whether the minion is full
 */
function isMinionFull(minion, minionData) {
  // Get minion tier data
  const tierData = minionData.tiers[minion.tier];
  if (!tierData) return false;

  // IMPORTANT: Apply compacting before checking fullness
  // This ensures we check against compacted storage, not raw storage
  const allItems = configManager.getAllItems();
  const { processCompacting } = require("./compactorUtils");

  // Apply compacting to the original minion to ensure consistency
  // This ensures the stored data matches what we're checking
  processCompacting(minion, allItems);

  // Calculate base storage capacity from tier
  let capacity = tierData.maxStorage || 0;

  // Apply storage upgrades from items.json
  if (minion.upgrades && minion.upgrades.length > 0) {
    // Count how many of each upgrade type is installed
    const upgradeCounts = {};
    minion.upgrades.forEach((upgradeKey) => {
      if (!upgradeKey) return;
      upgradeCounts[upgradeKey] = (upgradeCounts[upgradeKey] || 0) + 1;
    });

    // Add storage for each unique upgrade type
    for (const [upgradeKey, count] of Object.entries(upgradeCounts)) {
      const upgradeItem = allItems[upgradeKey];
      if (upgradeItem?.upgradeEffect?.extraStorage) {
        capacity += upgradeItem.upgradeEffect.extraStorage * count;
      }
    }
  }

  // Calculate how many items should be generated by now
  const now = Date.now();
  let lastCollection = minion.lastCollectionTimestamp || now;

  // Fix for future timestamps - if lastCollectionTimestamp is in the future, reset it to now
  if (lastCollection > now) {
    console.log(
      `[MinionUtils] WARNING: Future timestamp detected for ${minion.itemKey} (${lastCollection} > ${now}). Resetting to current time.`
    );
    lastCollection = now;
    minion.lastCollectionTimestamp = now; // Update the minion object to prevent this issue from recurring
  }

  const timeElapsedMs = Math.max(0, now - lastCollection);
  const itemsGenerated = Math.floor(
    timeElapsedMs / (tierData.generationIntervalSeconds * 1000)
  );

  // If minion has random drops or resources, we need to estimate how many items would be generated
  let estimatedGeneratedItems = itemsGenerated;
  if (minionData.drops?.length > 0) {
    // Only count items with 100% chance for fullness check
    const itemsToCheck = minionData.drops
      .filter((drop) => drop.chance >= 1.0)
      .map((drop) => ({ itemKey: drop.itemKey, chance: 1.0 }));

    const expectedItemsPerCycle = itemsToCheck.reduce((sum, drop) => {
      return sum + (drop.chance || 1.0);
    }, 0);

    estimatedGeneratedItems = Math.floor(
      itemsGenerated * expectedItemsPerCycle
    );
  }

  // Count items already in storage (using compacted storage)
  let storedItems = 0;
  if (minion.resourcesStored) {
    for (const resource in minion.resourcesStored) {
      // Skip coins as they don't count toward storage limit
      if (resource !== "COINS") {
        storedItems += minion.resourcesStored[resource] || 0;
      }
    }
  }

  // Minion is full if total items (stored + estimated generated) reaches or exceeds capacity
  return storedItems + estimatedGeneratedItems >= capacity;
}

/**
 * Determines fullness status for a player's placed minions (after triggering generation/compacting).
 * @param {string} userId - Discord user ID
 * @param {import('sqlite3').Database} db - DB connection
 * @returns {Promise<{anyFull: boolean, allFull: boolean}>}
 */
async function getPlacedMinionsFullStatus(userId) {
  return await withMinionLock(userId, "generation", async () => {
    try {
      const row = await dbGet(
        "SELECT island_json FROM players WHERE discord_id = ?",
        [userId]
      );

      if (!row || !row.island_json) {
        return { anyFull: false, allFull: false };
      }

      const islandData = JSON.parse(row.island_json);
      const placedMinions = islandData?.placedMinions || [];

      if (placedMinions.length === 0) {
        return { anyFull: false, allFull: false };
      }

      const allItems = configManager.getAllItems();
      let islandDataChanged = false;

      const { simulateMinionGeneration } = require("./newMinionGeneration");

      let anyFull = false;
      let allFull = true;

      placedMinions.forEach((minion) => {
        const minionData = allItems[minion.itemKey];
        if (!minionData) {
          allFull = false; // Unknown minion counts as not full
          return;
        }

        // Preserve original timestamp to detect changes
        const originalTimestamp = minion.lastCollectionTimestamp;

        // Trigger generation + compacting
        simulateMinionGeneration(minion, allItems);

        if (originalTimestamp !== minion.lastCollectionTimestamp) {
          islandDataChanged = true;
        }

        const isFull = isMinionFull(minion, minionData);
        if (isFull) anyFull = true;
        else allFull = false;
      });

      if (islandDataChanged) {
        const updatedIslandJson = JSON.stringify(islandData);
        try {
          await dbRunQueued(
            "UPDATE players SET island_json = ? WHERE discord_id = ?",
            [updatedIslandJson, userId]
          );
        } catch (updateErr) {
          console.error(
            "[MinionSystem] Failed to save generated minion storage:",
            updateErr
          );
        }
      }

      return { anyFull, allFull };
    } catch (error) {
      console.error("Error parsing minion data:", error);
      return { anyFull: false, allFull: false };
    }
  }); // End of withMinionLock
}

// Refactor existing helper to use the new status function (backwards compatibility)
async function allPlacedMinionsFull(userId) {
  const { allFull } = await getPlacedMinionsFullStatus(userId);
  return allFull;
}

module.exports = {
  isMinionFull,
  allPlacedMinionsFull,
  getPlacedMinionsFullStatus,
};
