const { handleGenericSkillAction } = require("../utils/genericSkillHandler");
const {
  fishingSkillConfig,
  farmingSkillConfig,
  miningSkillConfig,
  foragingSkillConfig,
  combatSkillConfig,
  alchemySkillConfig,
} = require("../data/skillConfigs");

// Config map for all actions
const actionConfigs = {
  alchemy: {
    handler: (
      interaction,
      character,
      resourceKey,
      amount,
      isAgain,
      actionId,
      pendingLoot,
      startingCycle
    ) => {
      // Pass all parameters including those for resumption
      return handleGenericSkillAction(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        alchemySkillConfig,
        actionId,
        pendingLoot,
        startingCycle
      );
    },
  },
  fishing: {
    handler: (
      interaction,
      character,
      resourceKey,
      amount,
      isAgain,
      actionId,
      pendingLoot,
      startingCycle
    ) => {
      // Pass all parameters including those for resumption
      return handleGenericSkillAction(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        fishingSkillConfig,
        actionId,
        pendingLoot,
        startingCycle
      );
    },
  },
  farming: {
    handler: (
      interaction,
      character,
      resourceKey,
      amount,
      isAgain,
      actionId,
      pendingLoot,
      startingCycle
    ) => {
      // Pass all parameters including those for resumption
      return handleGenericSkillAction(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        farmingSkillConfig,
        actionId,
        pendingLoot,
        startingCycle
      );
    },
  },
  mining: {
    handler: (
      interaction,
      character,
      resourceKey,
      amount,
      isAgain,
      actionId,
      pendingLoot,
      startingCycle
    ) => {
      // Pass all parameters including those for resumption
      return handleGenericSkillAction(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        miningSkillConfig,
        actionId,
        pendingLoot,
        startingCycle
      );
    },
  },
  foraging: {
    handler: (
      interaction,
      character,
      resourceKey,
      amount,
      isAgain,
      actionId,
      pendingLoot,
      startingCycle
    ) => {
      // Pass all parameters including those for resumption
      return handleGenericSkillAction(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        foragingSkillConfig,
        actionId,
        pendingLoot,
        startingCycle
      );
    },
  },
  combat: {
    handler: (
      interaction,
      character,
      resourceKey,
      amount,
      isAgain,
      actionId,
      pendingLoot,
      startingCycle
    ) => {
      // Pass all parameters including those for resumption
      return handleGenericSkillAction(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        combatSkillConfig,
        actionId,
        pendingLoot,
        startingCycle
      );
    },
  },
};

// Combat handler registration no longer needed - using unified system

/**
 * Unified entry point for all skill and combat actions.
 * Handles both new and resumed actions.
 * @param {object} interaction - Discord interaction or simulated interaction
 * @param {string} actionType - One of 'alchemy', 'fishing', 'farming', 'mining', 'foraging', 'combat'
 * @param {object} options - Optional extra parameters (resourceKey, amount, resume state, etc.)
 */
async function startOrResumeAction(interaction, actionType, options = {}) {
  const config = actionConfigs[actionType];
  if (!config || typeof config.handler !== "function")
    throw new Error(`Unknown or unregistered action type: ${actionType}`);

  // If this is a resumption, options should contain all necessary state
  if (options.isResumption) {
    // Call the handler with all resume params
    return await config.handler(
      interaction,
      options.character,
      options.resourceKey ?? null,
      options.amount ?? 1,
      /* isAgain= */ false,
      options.actionId ?? null,
      options.pendingLoot ?? null,
      options.startingCycle ?? 0
    );
  }

  // Combat now uses the unified system like other skills

  // Otherwise, this is a new action. Validate and extract params as needed.
  // For now, just call the handler with the basic params (expand as needed per skill)
  return await config.handler(
    interaction,
    options.character,
    options.resourceKey ?? null,
    options.amount ?? 1,
    /* isAgain= */ false
  );
}

module.exports = { actionConfigs, startOrResumeAction };
