const fs = require("fs").promises;
const path = require("path");

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, "..", "logs");
const crashLogsDir = path.join(logsDir, "crashes");

// Ensure directories exist
async function ensureDirectories() {
  try {
    await fs.mkdir(logsDir, { recursive: true });
    await fs.mkdir(crashLogsDir, { recursive: true });
  } catch (error) {
    console.error("[CrashLogger] Failed to create log directories:", error);
  }
}

// Initialize directories on module load
ensureDirectories();

/**
 * Logs a crash to a file with detailed information
 * @param {string} crashType - Type of crash (e.g., 'skill_action', 'activity_lock', 'database')
 * @param {Error} error - The error object
 * @param {Object} context - Additional context information
 */
async function logCrash(crashType, error, context = {}) {
  try {
    const timestamp = new Date().toISOString();
    const filename = `${crashType}_${timestamp.replace(/[:.]/g, "-")}.log`;
    const filepath = path.join(crashLogsDir, filename);

    const crashData = {
      timestamp,
      crashType,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context,
      nodeVersion: process.version,
      platform: process.platform,
    };

    const logContent = `=== CRASH LOG ===\n${JSON.stringify(crashData, null, 2)}\n\n=== STACK TRACE ===\n${error.stack}\n`;

    await fs.writeFile(filepath, logContent, "utf8");
    console.error(`[CrashLogger] Crash logged to: ${filename}`);

    // Also log to console for immediate visibility
    console.error(`[${crashType.toUpperCase()}] CRASH:`, error.message);

    return filepath;
  } catch (logError) {
    console.error("[CrashLogger] Failed to write crash log:", logError);
    // Fallback to console only
    console.error(`[${crashType.toUpperCase()}] ORIGINAL CRASH:`, error);
  }
}

/**
 * Logs a skill action crash with specific context
 * @param {Error} error - The error object
 * @param {Object} context - Skill action context (userId, skillName, actionId, etc.)
 */
async function logSkillActionCrash(error, context) {
  return logCrash("skill_action", error, {
    ...context,
    category: "skill_execution",
  });
}

/**
 * Logs an activity lock crash
 * @param {Error} error - The error object
 * @param {Object} context - Activity context (userId, activityType, etc.)
 */
async function logActivityLockCrash(error, context) {
  return logCrash("activity_lock", error, {
    ...context,
    category: "activity_management",
  });
}

/**
 * Logs a database operation crash
 * @param {Error} error - The error object
 * @param {Object} context - Database context (operation, table, userId, etc.)
 */
async function logDatabaseCrash(error, context) {
  return logCrash("database", error, {
    ...context,
    category: "database_operation",
  });
}

/**
 * Logs a general application crash
 * @param {Error} error - The error object
 * @param {Object} context - General context
 */
async function logGeneralCrash(error, context) {
  return logCrash("general", error, {
    ...context,
    category: "general_error",
  });
}

/**
 * Gets a list of recent crash files
 * @param {number} limit - Maximum number of files to return
 * @returns {Array} Array of crash file information
 */
async function getRecentCrashes(limit = 10) {
  try {
    const files = await fs.readdir(crashLogsDir);
    const crashFiles = files
      .filter((file) => file.endsWith(".log"))
      .map((file) => {
        const filepath = path.join(crashLogsDir, file);
        return { filename: file, filepath };
      })
      .sort((a, b) => b.filename.localeCompare(a.filename)) // Sort by filename (timestamp)
      .slice(0, limit);

    return crashFiles;
  } catch (error) {
    console.error("[CrashLogger] Failed to get recent crashes:", error);
    return [];
  }
}

/**
 * Cleans up old crash logs (older than specified days)
 * @param {number} daysToKeep - Number of days to keep logs
 */
async function cleanupOldLogs(daysToKeep = 30) {
  try {
    const files = await fs.readdir(crashLogsDir);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    let deletedCount = 0;

    for (const file of files) {
      if (!file.endsWith(".log")) continue;

      const filepath = path.join(crashLogsDir, file);
      const stats = await fs.stat(filepath);

      if (stats.mtime < cutoffDate) {
        await fs.unlink(filepath);
        deletedCount++;
      }
    }

    if (deletedCount > 0) {
      console.log(`[CrashLogger] Cleaned up ${deletedCount} old crash logs`);
    }
  } catch (error) {
    console.error("[CrashLogger] Failed to cleanup old logs:", error);
  }
}

module.exports = {
  logCrash,
  logSkillActionCrash,
  logActivityLockCrash,
  logDatabaseCrash,
  logGeneralCrash,
  getRecentCrashes,
  cleanupOldLogs,
};
