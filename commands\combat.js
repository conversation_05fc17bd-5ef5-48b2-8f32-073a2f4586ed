const { EMBED_COLORS } = require("../gameConfig.js");
const {
  Slash<PERSON>ommandBuilder,
  EmbedBuilder,
  ActionRowBuilder,
} = require("discord.js");
const configManager = require("../utils/configManager");

// Conditional imports based on whether this is running on a worker bot
const isWorkerBot = process.env.IS_WORKER_BOT === "true";
const playerDataModule = isWorkerBot
  ? require("../utils/workerDatabaseProxy")
  : require("../utils/playerDataManager");
const actionPersistenceModule = isWorkerBot
  ? require("../utils/workerDatabaseProxy")
  : require("../utils/actionPersistence");
const activityModule = isWorkerBot
  ? require("../utils/workerDatabaseProxy")
  : require("../utils/activityManager");

const {
  calculateActionDerivedExp,
  applyActionRewards,
  sendActionFollowups,
} = require("../utils/skillActionUtils.js");
const { calculateInstanceStats } = require("../utils/mobUtils.js");
// Combat handler registration no longer needed - using unified system
const { runTurnBasedFight } = require("../utils/combatEngine.js");
const { buildFinalSkillResultEmbed } = require("../utils/displayUtils.js");
const { hexToNumber } = require("../utils/animationContainers.js");
// Removed direct require - using conditional imports above
// const {
//   startAction,
//   updateActionProgress,
//   completeAction,
//   updateActionMessageId,
//   updateActionMessageTimestamp,
//   getActionParameters,
// } = require("../utils/actionPersistence.js");
// Removed direct require - using conditional imports above
// const { clearActivity } = require("../utils/activityManager.js");
// Removed old stop system imports - using simple abort system now
// Combat stop functionality removed - no abort signal needed
const { combatSkillConfig } = require("../data/skillConfigs");
// messageThrottler removed - using manual stop button creation

async function handleCombatAction(
  interaction,
  resourceKey,
  amount,
  isAgain = false,
  originalTotalAmount = null,
  character,
  wasMax = false
) {
  if (!character) {
    console.error("[handleCombatAction] Character object is missing!");
    return;
  }

  // CRITICAL SAFETY CHECK: Prevent main bot from executing worker-delegated actions
  if (interaction.isResumption && interaction.actionId) {
    // Check if this action should be handled by a worker
    try {
      const actionRecord = await actionPersistenceModule.getActionById(
        interaction.actionId
      );
      if (
        actionRecord &&
        actionRecord.worker_id &&
        !process.env.WORKER_BOT_ID
      ) {
        console.error(
          `[handleCombatAction] SAFETY VIOLATION: Main bot attempting to execute combat action ${interaction.actionId} that should be handled by worker ${actionRecord.worker_id}`
        );
        console.error(
          `[handleCombatAction] This action will be skipped to prevent message edit conflicts. The worker should handle this action.`
        );
        return {
          finalCharacterState: character,
          exp: 0,
          coins: 0,
          items: {},
          defeated: false,
          isInterrupted: true,
          killedCount: 0,
        };
      }
    } catch (actionCheckError) {
      console.error(
        `[handleCombatAction] Failed to check action ownership for ${interaction.actionId}:`,
        actionCheckError
      );
    }
  }

  const userId = character.discordId;
  let actualMobKey;
  let loopTotalAmount;
  let startingCycle = 0;
  let actionDbId = null;
  let messageToEdit;
  let currentCharacterState = { ...character };
  let playerInterrupted = false;
  let stopButtonComponents = []; // Store stop button components to prevent flickering

  // Capture starting coin balance for net change calculation
  const startingCoinBalance = currentCharacterState?.coins || 0;
  // track start time for runtime footer
  const actionStartTime = Date.now();

  const auxData = interaction.isResumption
    ? interaction.parameters?.auxData || {}
    : (interaction.options?.get
        ? interaction.options.get("auxData")?.value
        : null) || {};

  if (!auxData.skipInitialStatRecalculation) {
    await playerDataModule.recalculateAndSaveStats(
      userId,
      currentCharacterState
    );
  }

  if (interaction.isResumption) {
    actionDbId = interaction.actionId;
    startingCycle = interaction.initialCompletedCycles || 0;
    loopTotalAmount = originalTotalAmount;
    actualMobKey = interaction.parameters?.mobKey || resourceKey;
    messageToEdit = interaction.messageToEdit;

    //console.log(`[Combat][Resumption] Action ID ${actionDbId} for ${actualMobKey}. Resuming at cycle ${startingCycle + 1}/${loopTotalAmount}.`);

    // Retrieve wasMax flag from stored parameters for proper repeat button functionality
    if (interaction.parameters?.wasMax !== undefined) {
      wasMax = interaction.parameters.wasMax;
      //console.log(`[Combat][Resumption] Retrieved wasMax flag: ${wasMax} for action ${actionDbId}`);
    } else {
      //console.log(`[Combat][Resumption] No wasMax flag found in parameters for action ${actionDbId}, defaulting to false`);
    }

    /*console.log(
      `[Combat][Resumption] Action ID ${actionDbId} for ${actualMobKey}. Cycles ${startingCycle}/${loopTotalAmount}.`
    );*/

    // Ensure Stop button exists for resumed actions via unified utility, but only if not already present
    if (loopTotalAmount > 1 && messageToEdit) {
      const hasStopButton = (messageToEdit.components || []).some((row) =>
        row.components.some(
          (c) =>
            c.customId && c.customId.startsWith(`unified_stop:${actionDbId}`)
        )
      );
      if (!hasStopButton) {
        try {
          const {
            ensureUnifiedStopButton,
          } = require("../utils/unifiedStopButtons");
          await ensureUnifiedStopButton(
            messageToEdit,
            actionDbId,
            userId,
            "Stop Combat"
          );
        } catch (buttonError) {
          console.warn(
            `[Combat] Failed to add unified stop button:`,
            buttonError.message
          );
          // Fallback to old system
          const { ensureStopButton } = require("../utils/stopButtonUtils");
          await ensureStopButton(
            messageToEdit,
            actionDbId,
            userId,
            "Stop Combat"
          );
        }
      }
    }
  } else {
    actualMobKey = resourceKey;
    loopTotalAmount = parseInt(amount, 10) || 1;
    if (loopTotalAmount <= 0) loopTotalAmount = 1;
  }

  const baseMobData = configManager.getMob(actualMobKey);

  if (!baseMobData) {
    const errorMsg = "Error: Invalid mob key for combat.";
    console.error(`[Combat Handler] ${errorMsg} Key: ${actualMobKey}`);
    try {
      if (interaction.isResumption && interaction.channel) {
        await interaction.channel.send({
          content: `${errorMsg} Action ID: ${actionDbId}`,
        });
      } else if (!interaction.isResumption) {
        // For non-resumption interactions, send to channel instead of using interaction tokens
        try {
          await interaction.channel.send({
            content: `<@${userId}> ${errorMsg}`,
          });
        } catch (channelError) {
          console.error(
            "[Combat Handler] Error sending error message to channel:",
            channelError
          );
        }
      }
    } catch (err) {
      console.error("[Combat Handler] Error replying about invalid mob:", err);
    }
    if (actionDbId)
      await actionPersistenceModule
        .completeAction(actionDbId)
        .catch((e) =>
          console.error("Error completing action for invalid mob", e)
        );

    if (interaction.isResumption || !actionDbId)
      activityModule.clearActivity(userId);
    return;
  }

  if (!interaction.isResumption) {
    // If a delegated actionId is present, use it and skip creating a new action
    if (interaction.actionId) {
      actionDbId = interaction.actionId;
      // For "again" actions, just use the existing message
      if (isAgain && interaction.message) {
        messageToEdit = interaction.message;
      } else {
        if (!interaction.replied && !interaction.deferred)
          await interaction
            .deferReply()
            .catch((e) => console.warn("Defer reply failed for new combat", e));

        try {
          // Simple initialization embed that combat engine will immediately replace
          const initEmbed = new EmbedBuilder()
            .setColor(hexToNumber(EMBED_COLORS.LIGHT_GREEN))
            .setTitle("Initializing combat engine...");

          let buttonRows = [];
          // Create stop button for multi-combat
          if (loopTotalAmount > 1) {
            const { createStopButton } = require("../utils/unifiedStopButtons");
            // Use the real actionId from worker delegation
            const stopButton = createStopButton(
              actionDbId,
              userId,
              "Stop Combat"
            );
            const stopRow = new ActionRowBuilder().addComponents(stopButton);
            buttonRows = [stopRow];
          }

          // Send initialization message with stop button for multi-combat
          messageToEdit = await interaction.followUp({
            embeds: [initEmbed],
            components: buttonRows,
            fetchReply: true,
          });

          // PATCH: Always update messageId in DB for delegated actions
          if (actionDbId && messageToEdit && messageToEdit.id) {
            await actionPersistenceModule.updateActionMessageId(
              actionDbId,
              messageToEdit.id
            );
          }
        } catch (err) {
          console.error(
            "[Combat Handler] Error creating initial message:",
            err
          );
          activityModule.clearActivity(userId);
          return;
        }
      }
    } else {
      // atomic activity check before starting/resuming combat action
      try {
        const { trySetActivity, warnUserBusy } = activityModule;
        const activitySet = await trySetActivity(userId, "combat");
        if (!activitySet) {
          await warnUserBusy(interaction, "combat", "combat");
          return;
        }

        try {
          // Initialization embed
          const initEmbed = new EmbedBuilder()
            .setColor(hexToNumber(EMBED_COLORS.LIGHT_GREEN))
            .setTitle("Initializing combat engine...");

          let buttonRows = [];
          if (loopTotalAmount > 1) {
            const { createStopButton } = require("../utils/unifiedStopButtons");
            const stopButton = createStopButton("temp", userId, "Stop Combat");
            const stopRow = new ActionRowBuilder().addComponents(stopButton);
            buttonRows = [stopRow];
          }

          messageToEdit = await interaction.followUp({
            embeds: [initEmbed],
            components: buttonRows,
            fetchReply: true,
          });
        } catch (err) {
          console.error(
            "[Combat Handler] Error creating initial message:",
            err
          );
          activityModule.clearActivity(userId);
          return;
        }
      } catch (e) {
        console.error("[Combat Handler] Error preparing message:", e);
        activityModule.clearActivity(userId);
        return;
      }

      const initialParams = {
        wasMax: wasMax,
        mobKey: actualMobKey,
      };
      try {
        const channelIdForDB = messageToEdit
          ? messageToEdit.channelId
          : interaction.channelId;
        const workerId =
          interaction.workerId || interaction.assignedWorkerId || null;

        // SAFETY: Warn if workerId is null (shouldn't happen in normal operation)
        if (!workerId) {
          console.warn(
            `[handleCombatAction] Creating combat action with NULL workerId. This may indicate a delegation issue.`
          );
          console.warn(`[handleCombatAction] Interaction details:`, {
            hasWorkerId: !!interaction.workerId,
            hasAssignedWorkerId: !!interaction.assignedWorkerId,
            isWorkerBot: !!process.env.WORKER_BOT_ID,
            isActionDelegation: !!interaction.actionId,
            userId: userId,
          });
        }

        actionDbId = await actionPersistenceModule.startAction(
          userId,
          "combat",
          actualMobKey,
          initialParams,
          loopTotalAmount,
          channelIdForDB,
          messageToEdit ? messageToEdit.id : null,
          workerId
        );

        // Initialize message timestamp for message cycling
        await actionPersistenceModule.updateActionMessageTimestamp(
          actionDbId,
          Date.now()
        );

        // Actions always run on workers, so main bot should never create abort controllers
        // The executing worker will create the abort controller when it receives the action
        console.log(
          `[InstantStop] Skipping AbortController creation on main bot - will be handled by executing worker ${workerId || "TBD"}`
        );
        if (
          loopTotalAmount > 1 &&
          messageToEdit &&
          messageToEdit.components &&
          messageToEdit.components.length > 0
        ) {
          try {
            const updatedComponents = [...messageToEdit.components];
            // Search all rows for temp stop button ID regardless of container position
            let updated = false;
            for (const row of updatedComponents) {
              if (row && Array.isArray(row.components)) {
                for (const btn of row.components) {
                  if (
                    btn &&
                    typeof btn.customId === "string" &&
                    btn.customId.includes("unified_stop:temp")
                  ) {
                    btn.customId = `unified_stop:${actionDbId}:${userId}`;
                    updated = true;
                    console.log(
                      "[Combat][Debug] Updated stop button customId to:",
                      btn.customId
                    );
                  }
                }
              }
            }
            if (updated) {
              await messageToEdit.edit({
                components: updatedComponents,
              });
              console.log(
                `[Combat] Updated stop button with actionId ${actionDbId}`
              );
            }
          } catch (buttonUpdateError) {
            console.warn(
              `[Combat] Failed to update stop button with actionId:`,
              buttonUpdateError.message
            );
            const {
              ensureUnifiedStopButton,
            } = require("../utils/unifiedStopButtons");
            await ensureUnifiedStopButton(
              messageToEdit,
              actionDbId,
              userId,
              "Stop Combat"
            );
          }
        }
      } catch (dbError) {
        console.error("[Combat Handler] Error starting action in DB:", dbError);
        try {
          if (messageToEdit && messageToEdit.edit) {
            console.log(
              `[Combat][StopButton] DB error - preserving stop button components:`,
              stopButtonComponents.length,
              "components"
            );
            await messageToEdit.edit({
              content: "Error: Could not save action to database.",
              embeds: [],
            });
          }
        } catch (editError) {
          console.error(
            "[Combat Handler] Error editing message after DB error:",
            editError
          );
        }
        activityModule.clearActivity(userId);
        return;
      }
    }
  }

  let totalExpGained = 0,
    totalPetExpGained = 0,
    totalCoinsGained = 0;
  const itemsGainedAggregated = {};
  // Combat Wisdom is now calculated once at the end instead of accumulated
  let fightsActuallyCompletedThisSession = 0;
  let playerWasDefeated = false;
  let defeatedByMobDetails = null;

  // Track all defeated mobs for combat results
  const defeatedMobs = {};

  // Slayer quest tracking variables
  let activeSlayerQuest = null;
  let slayerQuestData = null;
  let slayerBossToSpawn = null;
  let slayerXpByType = {}; // Track slayer XP gains by type: { zombie: 15, spider: 10 }
  let originalSlayerType = null; // Store original slayer type for display even after quest completion

  // Store original mob data for restoration after boss fights
  const originalMobData = baseMobData;
  const originalMobKey = actualMobKey;

  // Make baseCombatWisdom function-scoped so it can be reused later when
  // recalculating wisdom multipliers (e.g. after weapon ability bonuses).
  let baseCombatWisdom = 0;
  let combatWisdomMultiplier = 1;

  if (currentCharacterState && currentCharacterState.stats) {
    const { calculateTotalStat } = require("../utils/statCalculations");

    baseCombatWisdom = calculateTotalStat(
      currentCharacterState.stats.COMBAT_WISDOM || {}
    );

    if (baseCombatWisdom > 0) {
      combatWisdomMultiplier = 1 + baseCombatWisdom / 100;
    }
  }

  // Initialize slayer quest tracking
  try {
    // Use conditional import for worker compatibility
    const { getPlayerData } = playerDataModule;
    const playerData = await getPlayerData(userId);

    if (playerData && playerData.active_slayer_quest) {
      activeSlayerQuest = JSON.parse(playerData.active_slayer_quest);

      // Get combat EXP from mob data (correct field is 'amount')
      const mobCombatExp = baseMobData.baseExp?.amount || 0;

      // Initialize mobKills if it doesn't exist (backward compatibility)
      if (!activeSlayerQuest.mobKills) {
        activeSlayerQuest.mobKills = {};
      }

      // Use dynamic slayer utilities instead of hardcoded logic
      const { createSlayerQuestData } = require("../utils/slayerUtils");
      slayerQuestData = createSlayerQuestData(
        activeSlayerQuest,
        mobCombatExp,
        actualMobKey,
        combatWisdomMultiplier
      );

      if (slayerQuestData) {
        console.log(
          `[Combat][Slayer] Active ${slayerQuestData.questInfo.slayerType} slayer quest detected. Progress: ${slayerQuestData.killsCompleted}/${slayerQuestData.killsNeeded} kills (${slayerQuestData.combatExpGained}/${slayerQuestData.combatExpRequired} EXP)`
        );

        // Store the original slayer type for display purposes
        originalSlayerType = slayerQuestData.questInfo.slayerType;
      }
    }
  } catch (slayerError) {
    console.error("[Combat][Slayer] Error loading slayer quest:", slayerError);
  }

  if (interaction.isResumption) {
    if (interaction.parameters?.cumulativeProgress) {
      const cumulative = interaction.parameters.cumulativeProgress;
      totalExpGained = cumulative.totalExp || 0;
      totalCoinsGained = cumulative.totalCoins || 0;
      Object.assign(itemsGainedAggregated, cumulative.totalItems || {});

      // Use saved pet experience if available, otherwise calculate from total exp
      if (cumulative.totalPetExp !== undefined) {
        totalPetExpGained = cumulative.totalPetExp;
      } else {
        totalPetExpGained = calculateActionDerivedExp(
          totalExpGained,
          baseMobData.baseExp?.sourceSkill || "combat",
          currentCharacterState
        );
      }

      // PATCH: Set startingCycle from cumulative.completedCycles for correct final count
      if (
        typeof cumulative.completedCycles === "number" &&
        cumulative.completedCycles > 0
      ) {
        console.log(
          `[Combat][Resumption] Using cumulative cycle count: ${cumulative.completedCycles} (was ${startingCycle})`
        );
        startingCycle = cumulative.completedCycles;
      }

      if (cumulative.playerDefeated) {
        playerWasDefeated = true;
        if (cumulative.defeatedBy) {
          defeatedByMobDetails = cumulative.defeatedBy;
        }
      }

      // Restore defeated mobs from cumulative progress
      if (cumulative.defeatedMobs) {
        Object.assign(defeatedMobs, cumulative.defeatedMobs);
        console.log(
          `[Combat][Resumption] Restored ${Object.keys(defeatedMobs).length} defeated mob type(s) from previous progress`
        );
      }

      // Restore slayer XP by type from cumulative progress
      if (cumulative.slayer_xp_jsonByType) {
        slayerXpByType = { ...cumulative.slayer_xp_jsonByType };
        // If we have slayer XP but no current quest, try to determine the slayer type from quest data
        if (
          !slayerQuestData &&
          !originalSlayerType &&
          Object.keys(slayerXpByType).length > 0
        ) {
          try {
            // Use conditional import for worker compatibility
            const { getPlayerData } = playerDataModule;
            const currentPlayerData = await getPlayerData(userId);
            if (currentPlayerData && currentPlayerData.active_slayer_quest) {
              const currentQuest = JSON.parse(
                currentPlayerData.active_slayer_quest
              );
              const mobData = configManager.getMob(currentQuest.type);
              if (mobData && mobData.slayer && mobData.slayer.type) {
                originalSlayerType = mobData.slayer.type;
                console.log(
                  `[Combat][Resumption] Determined slayer type from current quest: ${originalSlayerType}`
                );
              }
            }
          } catch (error) {
            console.error(
              "[Combat][Resumption] Error determining slayer type:",
              error
            );
          }
        }
      }

      // Legacy compatibility: migrate old totalSlayerXp to slayerXpByType
      if (cumulative.totalSlayerXp && cumulative.totalSlayerXp > 0) {
        if (!slayerXpByType.zombie) {
          slayerXpByType.zombie = 0;
        }
        slayerXpByType.zombie += cumulative.totalSlayerXp;
        console.log(
          `[Combat][Resumption] Migrated ${cumulative.totalSlayerXp} legacy slayer XP to zombie type`
        );
      }

      // Ensure slayerXpByType is initialized
      if (!cumulative.slayer_xp_jsonByType) {
        cumulative.slayer_xp_jsonByType = {};
      }

      const totalSlayerXpFromAll = Object.values(slayerXpByType).reduce(
        (sum, xp) => sum + xp,
        0
      );
      const slayerTypesText =
        Object.keys(slayerXpByType).length > 0
          ? Object.entries(slayerXpByType)
              .map(([type, xp]) => `${xp} ${type}`)
              .join(", ")
          : "none";

      console.log(
        `[Combat][Resumption] Loaded cumulative progress: ${totalExpGained} exp, ${totalCoinsGained} coins, ${
          Object.keys(itemsGainedAggregated).length
        } item types, ${totalPetExpGained} pet exp, ${Object.keys(defeatedMobs).length} defeated mob types, ${totalSlayerXpFromAll} slayer exp (${slayerTypesText}).`
      );
    } else if (
      interaction.parameters?.cycle_results &&
      Array.isArray(interaction.parameters.cycle_results)
    ) {
      console.log(
        `[Combat][Resumption] Migrating from old cycle_results format (${interaction.parameters.cycle_results.length} entries)`
      );

      for (const savedCycle of interaction.parameters.cycle_results) {
        if (savedCycle) {
          const count = savedCycle._count || 1;
          totalExpGained += (savedCycle.exp || 0) * count;
          totalCoinsGained += (savedCycle.coins || 0) * count;
          // Combat Wisdom bonuses are now calculated once at the end

          if (Array.isArray(savedCycle.items)) {
            savedCycle.items.forEach((itemStack) => {
              if (itemStack && itemStack.itemKey && itemStack.amount > 0) {
                itemsGainedAggregated[itemStack.itemKey] =
                  (itemsGainedAggregated[itemStack.itemKey] || 0) +
                  itemStack.amount * count;
              }
            });
          }

          const petExpFromSavedCycle = calculateActionDerivedExp(
            (savedCycle.exp || 0) * count,
            baseMobData.baseExp?.sourceSkill || "combat",
            currentCharacterState
          );
          totalPetExpGained += petExpFromSavedCycle;

          if (savedCycle.defeated || savedCycle.playerDefeated) {
            playerWasDefeated = true;
            if (savedCycle.defeatedBy) {
              defeatedByMobDetails = savedCycle.defeatedBy;
            } else if (savedCycle.mobName) {
              defeatedByMobDetails = {
                name: savedCycle.mobName,
                emoji: savedCycle.mobEmoji || "💀",
              };
            }
            console.log(
              `[Combat][Resumption] Player was defeated in a pre-restart cycle batch (count: ${count}). Mob: ${defeatedByMobDetails?.name}`
            );
          }
        }
      }

      // For old cycle_results format, we don't have defeated mobs or slayer XP data, so they remain empty/zero
      console.log(
        `[Combat][Resumption] Migrated from old format: ${totalExpGained} exp, ${totalCoinsGained} coins, ${
          Object.keys(itemsGainedAggregated).length
        } item types, ${totalPetExpGained} pet exp. No defeated mobs or slayer XP data from old format.`
      );
    }
  }

  // Get abort signal for stop button functionality
  const { getAbortSignal } = require("../utils/instantStopUtils");
  const signal = getAbortSignal(actionDbId);

  // MESSAGE CYCLING: Track message age to prevent Discord rate limit slowdown
  const MAX_MESSAGE_AGE = 58 * 60 * 1000; // 58 minutes in milliseconds
  let currentMessageId = messageToEdit?.id;
  let messageCreationTime;

  // Helper function to initialize message tracking and check if immediate cycling is needed
  const initializeMessageTracking = async () => {
    try {
      const actionParams =
        await actionPersistenceModule.getActionParameters(actionDbId);

      if (actionParams.messageCreationTime) {
        messageCreationTime = actionParams.messageCreationTime;

        // Check if message is already too old on resumption
        const messageAge = Date.now() - messageCreationTime;
        if (messageAge >= MAX_MESSAGE_AGE) {
          console.log(
            `[Combat][Multi] Message is ${Math.round(messageAge / 1000 / 60)} minutes old on resumption, cycling immediately`
          );
          return true; // Needs immediate cycling
        }

        return false; // No cycling needed
      } else {
        // No timestamp found - check if this is resumption or new action
        if (interaction.isResumption) {
          // COMPATIBILITY: Old resumption action from before the system - force regeneration
          console.log(
            `[Combat][Multi] No timestamp found for resumed action ${actionDbId}, regenerating message for compatibility`
          );
          messageCreationTime = Date.now();
          await actionPersistenceModule.updateActionMessageTimestamp(
            actionDbId,
            messageCreationTime
          );
          return true; // Force immediate cycling to regenerate message
        } else {
          // New action - just set timestamp, no cycling needed
          console.log(
            `[Combat][Multi] Initializing timestamp for new action ${actionDbId}`
          );
          messageCreationTime = Date.now();
          await actionPersistenceModule.updateActionMessageTimestamp(
            actionDbId,
            messageCreationTime
          );
          return false; // No cycling needed for new actions
        }
      }
    } catch (error) {
      console.error(
        `[Combat][Multi] Error initializing message tracking:`,
        error
      );
      messageCreationTime = Date.now();
      return true; // Force cycling on error to be safe
    }
  };

  // Helper function to create new message when current message gets too old
  const cycleToNewMessage = async () => {
    try {
      if (!currentMessageId || !messageToEdit) {
        console.warn(
          `[Combat][Multi] Cannot cycle message - missing message references`
        );
        return;
      }

      // Create a simple cycling embed
      const cyclingEmbed = new EmbedBuilder()
        .setColor(hexToNumber(EMBED_COLORS.LIGHT_GREEN))
        .setTitle("Continuing combat...");

      // Delete the old message
      await interaction.channel.messages.delete(currentMessageId);

      // Create a new message with cycling embed
      const newMessage = await interaction.channel.send({
        embeds: [cyclingEmbed],
      });
      currentMessageId = newMessage.id;
      messageToEdit = newMessage;
      messageCreationTime = Date.now();

      // Update the database with new message ID and timestamp
      await actionPersistenceModule.updateActionMessageId(
        actionDbId,
        currentMessageId
      );
      await actionPersistenceModule.updateActionMessageTimestamp(
        actionDbId,
        messageCreationTime
      );

      console.log(
        `[Combat][Multi] Cycled to new message ${currentMessageId} due to age (58+ minutes)`
      );
    } catch (error) {
      console.error(`[Combat][Multi] Error cycling to new message:`, error);
      // Continue with existing message if cycling fails
    }
  };

  // Shared timing tracker for consistent 500ms delays across all combat updates
  let lastUpdateTime = 0;

  // Track combat start time and last health regen application for in-memory health simulation
  const combatStartTime = Date.now();
  let lastHealthRegenTime = combatStartTime;

  try {
    // Initialize message tracking and check if immediate cycling is needed
    const needsImmediateCycling = await initializeMessageTracking();
    if (needsImmediateCycling) {
      await cycleToNewMessage();
    }

    // PATCH: Remove redundant message cycling for resumption. Trust message provided by resumption system.
    // Only cycle message if editing fails (handled by editReply/followUp logic in resumption system)

    for (let i = startingCycle; i < loopTotalAmount; i++) {
      // MESSAGE CYCLING: Check if message is older than 58 minutes
      // Handle case where messageCreationTime might not be set for old actions
      if (messageCreationTime) {
        const messageAge = Date.now() - messageCreationTime;
        if (messageAge >= MAX_MESSAGE_AGE) {
          await cycleToNewMessage();
        }
      } else {
        // Old action without timestamp - initialize and cycle immediately
        console.log(
          `[Combat][Multi] Action ${actionDbId} missing timestamp during cycle, initializing and cycling for compatibility`
        );
        messageCreationTime = Date.now();
        await actionPersistenceModule.updateActionMessageTimestamp(
          actionDbId,
          messageCreationTime
        );
        await cycleToNewMessage();
      }

      const currentCycleUserFacing = i + 1;

      // Check for stop button before each combat cycle
      if (signal && signal.aborted) {
        console.log(
          `[Combat] Action ${actionDbId} stopped by user - breaking immediately`
        );
        playerInterrupted = true;
        break;
      }

      // IN-MEMORY HEALTH SIMULATION: Apply health regen every 2 seconds during combat
      try {
        const currentTime = Date.now();
        const timeSinceLastRegen = currentTime - lastHealthRegenTime;

        // Apply health regen every 2 seconds (2000ms)
        if (timeSinceLastRegen >= 2000) {
          const { calculateHealthRegen } = require("../utils/healthRegen");
          const { calculateAllStats } = require("../utils/statCalculations");

          const maxHealth = calculateAllStats(currentCharacterState).HEALTH;

          // Only apply regen if player is not at full health
          if (currentCharacterState.current_health < maxHealth) {
            const regenAmount = calculateHealthRegen(currentCharacterState);
            const newHealth = Math.min(
              maxHealth,
              currentCharacterState.current_health + regenAmount
            );

            if (newHealth > currentCharacterState.current_health) {
              const actualRegen =
                newHealth - currentCharacterState.current_health;
              currentCharacterState.current_health = newHealth;
              console.log(
                `[Combat][Loop] Applied ${actualRegen.toFixed(1)} health regen for fight ${currentCycleUserFacing} (in-memory simulation)`
              );
            }
          }

          // Update the last regen time
          lastHealthRegenTime = currentTime;
        }
      } catch (healthRegenError) {
        console.error(
          `[Combat][Loop] Failed to calculate in-memory health regen for fight ${currentCycleUserFacing}:`,
          healthRegenError
        );
      }

      if (currentCharacterState.current_health <= 0) {
        console.log(
          `[Combat][Loop] User ${userId} health is ${currentCharacterState.current_health} before fight ${currentCycleUserFacing}. Ending sequence.`
        );

        console.log(
          `[Combat][Loop] DEATH DETECTED: Player already dead before fight (health: ${currentCharacterState.current_health}).`
        );
        playerWasDefeated = true;
        if (!defeatedByMobDetails)
          defeatedByMobDetails = { name: "Previously defeated", emoji: "💀" };
        break;
      }

      const mobInstanceForFight = {
        ...baseMobData,
        mobKey: actualMobKey, // Add mobKey for animal axe bonus lookup
        stats: calculateInstanceStats(baseMobData.baseStats),
      };

      const fightResult = await runTurnBasedFight({
        interaction: interaction,
        mobInstanceData: mobInstanceForFight,
        character: currentCharacterState,
        currentFightNum: currentCycleUserFacing,
        totalFights: loopTotalAmount,
        actionId: actionDbId,
        isSeaCreatureSubCombat: auxData.isSeaCreatureSubCombat || false,
        preCalculatedStats: auxData.preCalculatedStats || null,
        slayerQuestData: slayerQuestData,
        messageId: messageToEdit?.id,
        channel: interaction.channel,
        lastUpdateTime: lastUpdateTime,
      });

      currentCharacterState = fightResult.finalCharacterState;
      lastUpdateTime = fightResult.lastUpdateTime; // Update shared timing tracker

      // Note: Slayer boss fights are now handled as special encounters above,
      // similar to sea creature spawns in fishing

      // Combat stop functionality removed - no abort signal checks
      if (!fightResult.victory) {
        if (fightResult.fled) {
          playerInterrupted = true;
          console.log(
            `[Combat Handler] Player stopped during individual fight for action ID ${actionDbId}.`
          );
          // No cleanup needed for new abort system
        } else {
          playerWasDefeated = true;
          defeatedByMobDetails = {
            name: mobInstanceForFight.name,
            emoji: mobInstanceForFight.emoji,
          };

          console.log(
            `[Combat Handler] DEATH DETECTED: Stopping combat immediately for user ${userId}.`
          );
          break;
        }
      }

      // Handle instant stop case where rewards might be undefined
      if (!fightResult.rewards) {
        console.log(
          `[Combat Handler] No rewards found for fight result (likely instant stop). Skipping reward processing.`
        );
        // If player was interrupted/fled, break out of the loop
        if (playerInterrupted || fightResult.fled) {
          break;
        }
        // If no rewards but fight wasn't interrupted, continue to next fight
        continue;
      }

      const rewardsThisFight = fightResult.rewards;
      totalExpGained += rewardsThisFight.exp || 0;
      totalCoinsGained += rewardsThisFight.coins || 0;
      for (const [key, val] of Object.entries(rewardsThisFight.items || {})) {
        itemsGainedAggregated[key] = (itemsGainedAggregated[key] || 0) + val;
      }

      // Combat Wisdom bonuses are now calculated once at the end

      const petExpThisFight = calculateActionDerivedExp(
        rewardsThisFight.exp || 0,
        baseMobData.baseExp?.sourceSkill || "combat",
        currentCharacterState
      );
      totalPetExpGained += petExpThisFight;

      fightsActuallyCompletedThisSession++;

      // Track defeated mob for combat results (use optimized format for consistency)
      if (!defeatedMobs[actualMobKey]) {
        defeatedMobs[actualMobKey] = 0;
      }
      defeatedMobs[actualMobKey]++;

      // Handle slayer quest progress tracking
      // Handle slayer quest progress tracking
      if (
        slayerQuestData &&
        slayerQuestData.isActive &&
        slayerQuestData.isValidMob
      ) {
        const currentMobKey = actualMobKey.toLowerCase();

        console.log(
          `[Combat][Slayer] Kill tracking: currentMobKey='${currentMobKey}', targetMobType='${activeSlayerQuest.targetMobType}'`
        );
        console.log(
          `[Combat][Slayer] Kill tracking validation: isValidMob=${slayerQuestData.isValidMob}`
        );

        // Increment kill count for this mob type
        if (!activeSlayerQuest.mobKills[currentMobKey]) {
          activeSlayerQuest.mobKills[currentMobKey] = 0;
        }
        activeSlayerQuest.mobKills[currentMobKey]++;

        // -------------------------------------------------------------
        // SLAYER QUEST PROGRESS TRACKING (WITH COMBAT WISDOM)
        // -------------------------------------------------------------
        // For slayer quest progress tracking, we apply Combat Wisdom to show accurate kill counts
        // but we'll track this to prevent double-application at the end.

        // Calculate current weapon Combat Wisdom bonus for this mob type (non-accumulating)
        let currentWeaponWisdomBonus = 0;
        if (currentCharacterState.equipment?.equipped?.WEAPON) {
          const weaponItemKey = currentCharacterState.equipment.equipped.WEAPON;
          const equippedWeapon = currentCharacterState.inventory.equipment.find(
            (eq) => eq.itemKey === weaponItemKey && eq.isEquipped
          );
          if (equippedWeapon) {
            const allItems = require("../utils/configManager").getAllItems();
            const weaponDef = allItems[equippedWeapon.itemKey];
            if (weaponDef?.abilities) {
              for (const [, ability] of Object.entries(weaponDef.abilities)) {
                // Check if current mob is valid for this ability
                let isTargetMob = false;
                if (ability.targetMobType && baseMobData.mobType) {
                  isTargetMob =
                    baseMobData.mobType.toLowerCase() ===
                    ability.targetMobType.toLowerCase();
                }

                if (
                  isTargetMob &&
                  ability.type === "STAT_BONUS" &&
                  ability.stat === "COMBAT_WISDOM"
                ) {
                  currentWeaponWisdomBonus += ability.value || 0;
                }
              }
            }

            // Also check for single ability (for items like Recluse Fang)
            if (weaponDef?.ability) {
              const ability = weaponDef.ability;
              // Check if current mob is valid for this ability
              let isTargetMob = false;
              if (ability.targetMobType && baseMobData.mobType) {
                isTargetMob =
                  baseMobData.mobType.toLowerCase() ===
                  ability.targetMobType.toLowerCase();
              }

              if (
                isTargetMob &&
                ability.type === "STAT_BONUS" &&
                ability.stat === "COMBAT_WISDOM"
              ) {
                currentWeaponWisdomBonus += ability.value || 0;
              }
            }
          }
        }

        // Calculate current Combat Wisdom multiplier for quest progress
        const currentCombatWisdomMultiplier =
          1 + (baseCombatWisdom + currentWeaponWisdomBonus) / 100;

        // Use the stored original base EXP for calculations
        const originalBaseMobExp =
          slayerQuestData.originalBaseMobExp ||
          baseMobData.baseExp?.amount ||
          0;
        const wisdomBoostedMobExp = parseFloat(
          (originalBaseMobExp * currentCombatWisdomMultiplier).toFixed(2)
        );

        // Update quest parameters with Combat Wisdom-boosted values
        slayerQuestData.mobCombatExp = wisdomBoostedMobExp;
        slayerQuestData.killsNeeded = Math.ceil(
          slayerQuestData.combatExpRequired / wisdomBoostedMobExp
        );

        // Recalculate combatExpGained from all kills with Combat Wisdom applied
        let newCombatExpGained = 0;
        for (const [mobType, killCount] of Object.entries(
          activeSlayerQuest.mobKills
        )) {
          const mobs = require("../utils/configManager").getAllMobs();
          const mobData = mobs[mobType.toUpperCase()];
          if (mobData && mobData.baseExp && mobData.baseExp.amount) {
            // Apply Combat Wisdom multiplier to base Combat EXP
            const baseExp = mobData.baseExp.amount;
            const wisdomBoostedExp = parseFloat(
              (baseExp * currentCombatWisdomMultiplier).toFixed(2)
            );
            newCombatExpGained += killCount * wisdomBoostedExp;
          }
        }

        // Update quest data with Combat Wisdom-boosted values
        activeSlayerQuest.combatExpGained = newCombatExpGained;
        slayerQuestData.combatExpGained = newCombatExpGained;

        // Update dynamic kill counts with Combat Wisdom-boosted values
        slayerQuestData.killsCompleted = Math.floor(
          newCombatExpGained / wisdomBoostedMobExp
        );
        slayerQuestData.killsRemaining = Math.max(
          0,
          slayerQuestData.killsNeeded - slayerQuestData.killsCompleted
        );

        console.log(
          `[Combat][Slayer] Kill recorded: ${currentMobKey} (${activeSlayerQuest.mobKills[currentMobKey]} total). Progress: ${slayerQuestData.killsCompleted}/${slayerQuestData.killsNeeded} kills (${newCombatExpGained}/${slayerQuestData.combatExpRequired} EXP) [Combat Wisdom: ${currentCombatWisdomMultiplier.toFixed(2)}x]`
        );

        // Check if boss should spawn on next cycle
        if (
          newCombatExpGained >= slayerQuestData.combatExpRequired &&
          !slayerBossToSpawn
        ) {
          const { getBossKeyForQuest } = require("../utils/slayerUtils");
          const bossKey = getBossKeyForQuest(slayerQuestData.questType);

          slayerBossToSpawn = bossKey;
          console.log(
            `[Combat][Slayer] Boss ${bossKey} will spawn on next cycle!`
          );
        }

        // Update slayer quest progress in database
        try {
          const { dbRunQueued } = require("../utils/dbUtils");
          await dbRunQueued(
            "UPDATE players SET active_slayer_quest = ? WHERE discord_id = ?",
            [JSON.stringify(activeSlayerQuest), userId]
          );
        } catch (slayerUpdateError) {
          console.error(
            "[Combat][Slayer] Error updating slayer progress:",
            slayerUpdateError
          );
        }

        // Check for mini-boss spawn (only if main boss isn't spawning)
        // Check for mini-boss spawn (only if main boss isn't spawning)
        if (
          !slayerBossToSpawn &&
          newCombatExpGained < slayerQuestData.combatExpRequired
        ) {
          const {
            rollForMiniBossSpawn,
            getQuestTier,
          } = require("../utils/slayerUtils");
          const questTier = getQuestTier(slayerQuestData.questType);
          const slayerType = slayerQuestData.questInfo.slayerType;
          const targetMobType = slayerQuestData.questInfo.targetMobType;

          const miniBossKey = rollForMiniBossSpawn(
            slayerType,
            questTier,
            targetMobType
          );

          if (miniBossKey) {
            console.log(
              `[Combat][Slayer] Mini-boss ${miniBossKey} will spawn as special encounter!`
            );

            // Add 1000ms delay before mini-boss spawn
            try {
              await new Promise((resolve) => setTimeout(resolve, 1000));
            } catch (error) {
              console.warn(
                `[Combat][Slayer] Pre-mini-boss delay failed: ${error.message}`
              );
            }

            // Load mini-boss mob data
            const miniBossMobData = require("../utils/configManager").getMob(
              miniBossKey
            );
            //const miniBossMobData = getMobByKey(miniBossKey);

            if (miniBossMobData) {
              console.log(
                `[Combat][Slayer] Fighting mini-boss ${miniBossMobData.name}!`
              );

              // Create mini-boss instance for the fight
              const miniBossInstanceForFight = {
                ...miniBossMobData,
                mobKey: miniBossKey, // Add mobKey for animal axe bonus lookup
                stats: calculateInstanceStats(miniBossMobData.baseStats),
              };

              // Fight the mini-boss as a special encounter
              const miniBossFightResult = await runTurnBasedFight({
                interaction: interaction,
                mobInstanceData: miniBossInstanceForFight,
                character: currentCharacterState,
                currentFightNum: "Mini-Boss",
                totalFights: loopTotalAmount,
                actionId: actionDbId,
                isSeaCreatureSubCombat: false,
                preCalculatedStats: auxData.preCalculatedStats || null,
                slayerQuestData: slayerQuestData,
                messageId: messageToEdit?.id,
                channel: interaction.channel,
                lastUpdateTime: lastUpdateTime,
              });

              currentCharacterState = miniBossFightResult.finalCharacterState;
              lastUpdateTime = miniBossFightResult.lastUpdateTime; // Update shared timing tracker

              // Handle mini-boss fight outcome
              if (miniBossFightResult.victory) {
                console.log(
                  `[Combat][Slayer] Mini-boss ${miniBossInstanceForFight.name} defeated!`
                );

                // Add mini-boss rewards to totals
                const miniBossRewards = miniBossFightResult.rewards || {
                  exp: 0,
                  coins: 0,
                  items: {},
                };
                totalExpGained += miniBossRewards.exp || 0;
                totalCoinsGained += miniBossRewards.coins || 0;
                for (const [key, val] of Object.entries(
                  miniBossRewards.items || {}
                )) {
                  itemsGainedAggregated[key] =
                    (itemsGainedAggregated[key] || 0) + val;
                }

                const miniBossPetExp = calculateActionDerivedExp(
                  miniBossRewards.exp,
                  miniBossMobData.baseExp?.sourceSkill || "combat",
                  currentCharacterState
                );
                totalPetExpGained += miniBossPetExp;

                // Track defeated mini-boss (use optimized format for consistency)
                if (!defeatedMobs[miniBossKey]) {
                  defeatedMobs[miniBossKey] = 0;
                }
                defeatedMobs[miniBossKey]++;

                // Track mini-boss kill in slayer quest mobKills (MISSING FIX)
                const miniBossSlayerKey = miniBossKey.toLowerCase();
                if (!activeSlayerQuest.mobKills[miniBossSlayerKey]) {
                  activeSlayerQuest.mobKills[miniBossSlayerKey] = 0;
                }
                activeSlayerQuest.mobKills[miniBossSlayerKey]++;

                // Add mini-boss combat EXP to slayer quest progress
                const miniBossCombatExp = miniBossMobData.baseExp?.amount || 0;
                const wisdomBoostedMiniBossExp = parseFloat(
                  (miniBossCombatExp * currentCombatWisdomMultiplier).toFixed(2)
                );
                activeSlayerQuest.combatExpGained += wisdomBoostedMiniBossExp;

                // Recalculate combatExpGained from all kills (including mini-boss) with Combat Wisdom applied
                let recalculatedCombatExp = 0;
                for (const [mobType, killCount] of Object.entries(
                  activeSlayerQuest.mobKills
                )) {
                  const mobs = require("../utils/configManager").getAllMobs();
                  const mobData = mobs[mobType.toUpperCase()];
                  if (mobData && mobData.baseExp && mobData.baseExp.amount) {
                    // Apply Combat Wisdom multiplier to base Combat EXP
                    const baseExp = mobData.baseExp.amount;
                    const wisdomBoostedExp = parseFloat(
                      (baseExp * currentCombatWisdomMultiplier).toFixed(2)
                    );
                    recalculatedCombatExp += killCount * wisdomBoostedExp;
                  }
                }

                // Update quest data with recalculated values
                activeSlayerQuest.combatExpGained = recalculatedCombatExp;
                slayerQuestData.combatExpGained = recalculatedCombatExp;
                slayerQuestData.killsCompleted = Math.floor(
                  recalculatedCombatExp / slayerQuestData.mobCombatExp
                );
                slayerQuestData.killsRemaining = Math.max(
                  0,
                  slayerQuestData.killsNeeded - slayerQuestData.killsCompleted
                );

                console.log(
                  `[Combat][Slayer] Mini-boss kill recorded: ${miniBossSlayerKey} (${activeSlayerQuest.mobKills[miniBossSlayerKey]} total). New progress: ${slayerQuestData.killsCompleted}/${slayerQuestData.killsNeeded} kills (${recalculatedCombatExp}/${slayerQuestData.combatExpRequired} EXP) [Combat Wisdom: ${currentCombatWisdomMultiplier.toFixed(2)}x]`
                );

                // Check if main boss should now spawn
                if (
                  slayerQuestData.combatExpGained >=
                    slayerQuestData.combatExpRequired &&
                  !slayerBossToSpawn
                ) {
                  const {
                    getBossKeyForQuest,
                  } = require("../utils/slayerUtils");
                  const bossKey = getBossKeyForQuest(slayerQuestData.questType);

                  slayerBossToSpawn = bossKey;
                  console.log(
                    `[Combat][Slayer] Mini-boss pushed quest to completion! Boss ${bossKey} will spawn on next cycle!`
                  );
                }

                // Update slayer quest progress in database
                try {
                  const { dbRunQueued } = require("../utils/dbUtils");
                  await dbRunQueued(
                    "UPDATE players SET active_slayer_quest = ? WHERE discord_id = ?",
                    [JSON.stringify(activeSlayerQuest), userId]
                  );
                } catch (slayerUpdateError) {
                  console.error(
                    "[Combat][Slayer] Error updating slayer progress after mini-boss:",
                    slayerUpdateError
                  );
                }
              } else if (
                !miniBossFightResult.victory &&
                !miniBossFightResult.fled
              ) {
                // Player died to mini-boss
                console.log(
                  `[Combat][Slayer] Player died to mini-boss ${miniBossInstanceForFight.name}.`
                );

                playerWasDefeated = true;
                defeatedByMobDetails = {
                  name: miniBossInstanceForFight.name,
                  emoji: miniBossInstanceForFight.emoji,
                };

                break; // End combat sequence
              } else if (miniBossFightResult.fled) {
                // Player fled from mini-boss
                console.log(
                  `[Combat][Slayer] Player fled from mini-boss ${miniBossInstanceForFight.name}.`
                );
                playerInterrupted = true;
                break; // End combat sequence
              }
            }
          }
        }
      }

      const leanCycleResultForDb = {
        exp: rewardsThisFight.exp || 0,
        items: Object.entries(rewardsThisFight.items || {}).map(
          ([itemKey, amount]) => ({ itemKey, amount })
        ),
        droppedItems: rewardsThisFight.droppedItems || [], // Include droppedItems array for action persistence
        coins: rewardsThisFight.coins || 0,
        combatWisdomBonus: fightResult.combatWisdomBonus || 0,
        timestamp: Date.now(),
        resourceKey: actualMobKey,
        mobName: mobInstanceForFight.name,
        mobEmoji: mobInstanceForFight.emoji,
        defeatedMobData: (() => {
          // Use consistent format: store mob key directly for optimized format
          return { [actualMobKey]: 1 }; // Single defeat in this cycle
        })(),
      };

      // Always update combat progress to ensure crash recovery
      if (actionDbId)
        await actionPersistenceModule.updateActionProgress(
          actionDbId,
          currentCycleUserFacing,
          leanCycleResultForDb
        );

      // Add 1000ms delay between fights to reduce Discord API load during long combat sessions
      // This delay happens AFTER the progress update to ensure message edits have time to complete
      if (currentCycleUserFacing < loopTotalAmount) {
        // Only delay if not the last fight
        // Combat stop functionality removed - simple delay without abort checks
        try {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        } catch (error) {
          // If delay fails for other reasons, continue without delay
          console.warn(
            `[Combat Handler] Inter-fight delay failed for action ID ${actionDbId}: ${error.message}`
          );
        }
      }

      // Check if we need to spawn a slayer boss (like a sea creature encounter)
      if (slayerBossToSpawn) {
        console.log(
          `[Combat][Slayer] Spawning boss ${slayerBossToSpawn} as special encounter`
        );

        // Add 500ms delay before boss spawn to reduce Discord API load
        // Combat stop functionality removed - simple delay without abort checks
        try {
          await new Promise((resolve) => setTimeout(resolve, 500));
        } catch (error) {
          // If delay fails for other reasons, continue without delay
          console.warn(
            `[Combat][Slayer] Pre-boss delay failed for action ID ${actionDbId}: ${error.message}`
          );
        }

        // Load boss mob data
        const bossMobData = require("../utils/configManager").getMob(
          slayerBossToSpawn
        );
        console.log(
          `[Combat][Slayer] Retrieved boss mob data for ${slayerBossToSpawn}:`,
          bossMobData
        );

        if (bossMobData) {
          // Store the boss key before clearing the spawn flag
          const currentBossKey = slayerBossToSpawn;

          // Clear the boss spawn flag immediately
          slayerBossToSpawn = null;

          console.log(
            `[Combat][Slayer] Fighting boss ${bossMobData.name} as special encounter!`
          );

          // Create boss instance for the fight
          const bossInstanceForFight = {
            ...bossMobData,
            mobKey: slayerBossToSpawn, // Add mobKey for animal axe bonus lookup
            stats: calculateInstanceStats(bossMobData.baseStats),
          };
          console.log(
            `[Combat][Slayer] Created boss instance:`,
            bossInstanceForFight
          );
          console.log(
            `[Combat][Slayer] Boss instance slayer property:`,
            bossInstanceForFight.slayer
          );

          // Fight the boss as a special encounter (doesn't count toward cycle)
          const bossFightResult = await runTurnBasedFight({
            interaction: interaction,
            mobInstanceData: bossInstanceForFight,
            character: currentCharacterState,
            currentFightNum: "Boss", // Special indicator for boss fight
            totalFights: loopTotalAmount,
            actionId: actionDbId,
            isSeaCreatureSubCombat: false,
            preCalculatedStats: auxData.preCalculatedStats || null,
            slayerQuestData: slayerQuestData,
            messageId: messageToEdit?.id,
            channel: interaction.channel,
            lastUpdateTime: lastUpdateTime,
          });

          currentCharacterState = bossFightResult.finalCharacterState;
          lastUpdateTime = bossFightResult.lastUpdateTime; // Update shared timing tracker

          // Handle boss fight outcome
          if (bossFightResult.victory) {
            console.log(
              `[Combat][Slayer] Slayer boss ${bossInstanceForFight.name} defeated! Completing quest.`
            );

            // Award slayer XP
            console.log(
              `[Combat][Slayer] Boss instance slayer data:`,
              bossInstanceForFight.slayer
            );
            const slayerXpGained = bossInstanceForFight.slayer?.xpReward || 0;
            const slayerType = bossInstanceForFight.slayer?.type;
            console.log(
              `[Combat][Slayer] Extracted slayerXpGained: ${slayerXpGained}, slayerType: ${slayerType}`
            );
            // Track slayer XP by type
            if (!slayerXpByType[slayerType]) {
              slayerXpByType[slayerType] = 0;
            }
            slayerXpByType[slayerType] += slayerXpGained;

            // Send dedicated slayer boss defeat embed
            if (slayerXpGained > 0 && slayerType) {
              const slayerTypeCapitalized =
                slayerType.charAt(0).toUpperCase() + slayerType.slice(1);
              const slayerDefeatEmbed = new EmbedBuilder()
                .setColor(EMBED_COLORS.GREEN)
                .setDescription(
                  `You have defeated ${bossInstanceForFight.emoji || ""} **${bossInstanceForFight.name}**\n` +
                    `**REWARDS**\n` +
                    `**+${slayerXpGained} ${slayerTypeCapitalized} Slayer EXP**`
                );

              try {
                if (messageToEdit && messageToEdit.edit) {
                  // Store the defeat embed timestamp for later removal
                  const defeatEmbedTimestamp = Date.now();
                  messageToEdit.slayerDefeatEmbed = {
                    embed: slayerDefeatEmbed,
                    timestamp: defeatEmbedTimestamp,
                    duration: 3000, // 3 seconds
                  };

                  console.log(
                    `[Combat][Slayer] Slayer defeat embed will be shown for 3 seconds`
                  );
                } else {
                  // Use channel from interaction instead of interaction.channel to avoid token issues
                  const channel = interaction.channel || messageToEdit?.channel;
                  if (channel) {
                    const slayerMessage = await channel.send({
                      embeds: [slayerDefeatEmbed],
                    });
                    // Delete the message after 3 seconds if it's a new message
                    setTimeout(async () => {
                      try {
                        await slayerMessage.delete();
                      } catch (deleteError) {
                        console.error(
                          "[Combat][Slayer] Error deleting slayer defeat embed:",
                          deleteError
                        );
                      }
                    }, 3000);
                  }
                }
              } catch (embedError) {
                console.error(
                  "[Combat][Slayer] Error handling slayer defeat embed:",
                  embedError
                );
              }
            }

            // Calculate and add boss rewards
            const bossRewards = bossFightResult.rewards || {
              exp: 0,
              coins: 0,
              items: {},
            };
            totalExpGained += bossRewards.exp || 0;
            totalCoinsGained += bossRewards.coins || 0;
            for (const [key, val] of Object.entries(bossRewards.items || {})) {
              itemsGainedAggregated[key] =
                (itemsGainedAggregated[key] || 0) + val;
            }

            // Combat Wisdom bonuses are now calculated once at the end

            const bossPetExp = calculateActionDerivedExp(
              bossRewards.exp,
              bossMobData.baseExp?.sourceSkill || "combat",
              currentCharacterState
            );
            totalPetExpGained += bossPetExp;

            // Track defeated boss (use optimized format for consistency)
            if (!defeatedMobs[currentBossKey]) {
              defeatedMobs[currentBossKey] = 0;
            }
            defeatedMobs[currentBossKey]++;

            // Save boss defeat and slayer XP to cumulative progress immediately
            if (actionDbId) {
              try {
                const currentParams =
                  await actionPersistenceModule.getActionParameters(actionDbId);

                // Ensure cumulative progress exists and has required fields
                if (!currentParams.cumulativeProgress) {
                  currentParams.cumulativeProgress = {
                    totalExp: 0,
                    totalCoins: 0,
                    totalItems: {},
                    totalCombatWisdomBonus: 0,
                    seaCreaturesDefeated: {},
                    petsGained: [],
                    completedCycles: 0,
                    playerDefeated: false,
                    defeatedBy: null,
                    defeatedMobs: {},
                    slayerXpByType: {},
                  };
                }
                if (!currentParams.cumulativeProgress.defeatedMobs) {
                  currentParams.cumulativeProgress.defeatedMobs = {};
                }
                if (!currentParams.cumulativeProgress.slayer_xp_jsonByType) {
                  currentParams.cumulativeProgress.slayer_xp_jsonByType = {};
                }

                // Update cumulative progress with boss defeat and slayer XP (use optimized format)
                if (
                  !currentParams.cumulativeProgress.defeatedMobs[currentBossKey]
                ) {
                  currentParams.cumulativeProgress.defeatedMobs[
                    currentBossKey
                  ] = 0;
                }
                currentParams.cumulativeProgress.defeatedMobs[currentBossKey]++;

                // Update slayer XP by type
                if (
                  !currentParams.cumulativeProgress.slayer_xp_jsonByType[
                    slayerType
                  ]
                ) {
                  currentParams.cumulativeProgress.slayer_xp_jsonByType[
                    slayerType
                  ] = 0;
                }
                currentParams.cumulativeProgress.slayer_xp_jsonByType[
                  slayerType
                ] += slayerXpGained || 0;

                // CRITICAL FIX: Save boss rewards to cumulative progress
                currentParams.cumulativeProgress.totalExp +=
                  bossRewards.exp || 0;
                currentParams.cumulativeProgress.totalCoins +=
                  bossRewards.coins || 0;

                // Save boss items to cumulative progress
                if (!currentParams.cumulativeProgress.totalItems) {
                  currentParams.cumulativeProgress.totalItems = {};
                }
                for (const [itemKey, amount] of Object.entries(
                  bossRewards.items || {}
                )) {
                  currentParams.cumulativeProgress.totalItems[itemKey] =
                    (currentParams.cumulativeProgress.totalItems[itemKey] ||
                      0) + amount;
                }

                // CRITICAL FIX: Save boss pet experience to cumulative progress
                if (!currentParams.cumulativeProgress.totalPetExp) {
                  currentParams.cumulativeProgress.totalPetExp = 0;
                }
                currentParams.cumulativeProgress.totalPetExp += bossPetExp || 0;

                await actionPersistenceModule.updateActionParameters(
                  actionDbId,
                  currentParams
                );
                console.log(
                  `[Combat][Slayer] Saved boss defeat, ${slayerXpGained} slayer XP, ${bossRewards.exp} exp, ${bossRewards.coins} coins, and ${Object.keys(bossRewards.items || {}).length} item types to cumulative progress`
                );
              } catch (persistError) {
                console.error(
                  "[Combat][Slayer] Error saving boss defeat to cumulative progress:",
                  persistError
                );
                // Even if persistence fails, continue with the combat to avoid losing progress
                // The player will still get rewards, just not persistence for resumption
              }
            }

            // Removed combat stop functionality - post-processing check

            // Update player's slayer XP and clear quest
            try {
              // Use conditional import for worker compatibility
              const { getPlayerData, savePlayerData } = playerDataModule;
              const playerData = await getPlayerData(userId);

              let slayerXpData = {};
              if (playerData && playerData.slayerXp) {
                slayerXpData = playerData.slayerXp;
              }

              const slayerType = bossInstanceForFight.slayer?.type;
              if (slayerType) {
                const oldSlayerXp = slayerXpData[slayerType] || 0;
                slayerXpData[slayerType] = oldSlayerXp + slayerXpGained;

                // Check for slayer level ups and create notification embeds
                const {
                  checkSlayerLevelUp,
                } = require("../utils/slayerLevelUtils");
                const slayerLevelUpEmbeds = await checkSlayerLevelUp(
                  userId,
                  slayerType,
                  oldSlayerXp,
                  slayerXpData[slayerType]
                );

                // Send slayer level up notifications if any occurred
                if (slayerLevelUpEmbeds && slayerLevelUpEmbeds.length > 0) {
                  try {
                    console.log(
                      `[Combat][Slayer] Sending ${slayerLevelUpEmbeds.length} slayer level up notification(s) for ${slayerType} slayer`
                    );
                    const channel =
                      interaction.channel || messageToEdit?.channel;
                    if (channel) {
                      await channel.send({ embeds: slayerLevelUpEmbeds });
                    }
                  } catch (levelUpNotificationError) {
                    console.error(
                      `[Combat][Slayer] Error sending slayer level up notifications:`,
                      levelUpNotificationError
                    );
                  }
                }

                // Check and notify for Disblock XP from slayer level-ups
                try {
                  const {
                    checkAndNotifyDisblockXP,
                  } = require("../utils/disblockXpSystem");
                  const channel = interaction.channel || messageToEdit?.channel;

                  // Preserve original interaction properties for proper nickname updates
                  const channelInteraction = channel
                    ? {
                        ...interaction,
                        channel,
                        member: interaction.member,
                        guild: interaction.guild,
                      }
                    : interaction;

                  await checkAndNotifyDisblockXP(userId, channelInteraction);
                } catch (disblockXpError) {
                  console.error(
                    "[Combat][Slayer] Error checking Disblock XP after slayer level-up:",
                    disblockXpError
                  );
                }
              }

              console.log(
                `[Combat][Slayer] Saving slayerXpData:`,
                slayerXpData
              );
              await savePlayerData(
                userId,
                {
                  slayerXp: slayerXpData,
                  active_slayer_quest: null, // Clear the active quest
                },
                ["slayerXp", "active_slayer_quest"]
              );
              console.log(`[Combat][Slayer] Slayer XP data saved successfully`);

              console.log(
                `[Combat][Slayer] Awarded ${slayerXpGained} ${slayerType} slayer XP. Total: ${slayerXpData[slayerType]}`
              );

              // Removed combat stop functionality - auto-quest processing check

              // Check for auto quest setting and start new quest if enabled
              try {
                // Use conditional import for worker compatibility
                const { getPlayerData } = playerDataModule;
                const playerDataForAutoQuest = await getPlayerData(userId);
                console.log(
                  `[Combat][Slayer] Player settings data:`,
                  playerDataForAutoQuest?.settings
                );
                const autoQuestsEnabled =
                  playerDataForAutoQuest?.settings?.autoQuests || false;
                console.log(
                  `[Combat][Slayer] Auto quests enabled: ${autoQuestsEnabled}`
                );
                if (autoQuestsEnabled) {
                  console.log(
                    `[Combat][Slayer] Auto quests enabled for player ${userId}, attempting to start new quest`
                  );
                  const { autoStartSlayerQuest } = require("./talk");
                  const channel = interaction.channel || messageToEdit?.channel;
                  const questStarted = await autoStartSlayerQuest(
                    userId,
                    activeSlayerQuest.type,
                    channel
                  );
                  if (questStarted) {
                    console.log(
                      `[Combat][Slayer] Auto-started new ${activeSlayerQuest.type} quest for player ${userId}`
                    );

                    // Removed combat stop functionality - quest reload check

                    // Reload the new quest data for continued combat
                    try {
                      // Use conditional import for worker compatibility
                      const { getPlayerData } = playerDataModule;
                      const newPlayerData = await getPlayerData(userId);

                      if (newPlayerData && newPlayerData.active_slayer_quest) {
                        activeSlayerQuest = JSON.parse(
                          newPlayerData.active_slayer_quest
                        );
                        console.log(
                          `[Combat][Slayer] Reloaded new quest data:`,
                          activeSlayerQuest
                        );

                        // Get combat EXP from original mob data
                        const mobCombatExp =
                          originalMobData.baseExp?.amount || 0;

                        // Initialize mobKills if it doesn't exist
                        if (!activeSlayerQuest.mobKills) {
                          activeSlayerQuest.mobKills = {};
                        }

                        // Use dynamic slayer utilities instead of hardcoded logic
                        const {
                          createSlayerQuestData,
                        } = require("../utils/slayerUtils");
                        slayerQuestData = createSlayerQuestData(
                          activeSlayerQuest,
                          mobCombatExp,
                          originalMobKey,
                          combatWisdomMultiplier
                        );

                        if (slayerQuestData) {
                          console.log(
                            `[Combat][Slayer] New quest loaded for continued combat. Progress: ${slayerQuestData.killsCompleted}/${slayerQuestData.killsNeeded} kills`
                          );
                        }

                        // CRITICAL: Refresh character state after auto-slayer to get updated coin balance
                        try {
                          const refreshedCharacterData =
                            await getPlayerData(userId);
                          if (refreshedCharacterData) {
                            // Preserve combat state but update currencies and other data that may have changed
                            currentCharacterState = {
                              ...refreshedCharacterData,
                              current_health:
                                currentCharacterState.current_health, // Preserve current health from combat
                            };
                            console.log(
                              `[Combat][Slayer] Refreshed current character state after auto-slayer. Coins: ${currentCharacterState.coins || 0}`
                            );
                          }
                        } catch (refreshError) {
                          console.error(
                            `[Combat][Slayer] Error refreshing character state after auto-slayer:`,
                            refreshError
                          );
                        }
                      } else {
                        // Clear quest tracking variables if no new quest was created
                        activeSlayerQuest = null;
                        slayerQuestData = null;
                        console.log(
                          `[Combat][Slayer] No new quest data found, clearing quest tracking`
                        );
                      }
                    } catch (reloadError) {
                      console.error(
                        `[Combat][Slayer] Error reloading new quest data:`,
                        reloadError
                      );
                      // Clear quest tracking variables on error
                      activeSlayerQuest = null;
                      slayerQuestData = null;
                    }
                  } else {
                    console.log(
                      `[Combat][Slayer] Failed to auto-start new quest for player ${userId}`
                    );
                    // Clear quest tracking variables if auto-start failed
                    activeSlayerQuest = null;
                    slayerQuestData = null;
                  }
                } else {
                  console.log(
                    `[Combat][Slayer] Auto quests disabled for player ${userId}, not starting new quest`
                  );
                  // Clear quest tracking variables if auto quests are disabled
                  activeSlayerQuest = null;
                  slayerQuestData = null;
                }
              } catch (autoQuestError) {
                console.error(
                  `[Combat][Slayer] Error with auto quest system:`,
                  autoQuestError
                );
                // Clear quest tracking variables on error
                activeSlayerQuest = null;
                slayerQuestData = null;
              }

              console.log(
                `[Combat][Slayer] Quest completed! Continuing with original mob: ${originalMobData.name}`
              );

              // Add 1000ms delay after slayer boss defeat before continuing regular combat
              if (currentCycleUserFacing < loopTotalAmount) {
                // Only delay if more regular fights remain
                // Removed combat stop functionality - post-boss delay check
                await new Promise((resolve) => setTimeout(resolve, 1000));
              }
            } catch (slayerXpError) {
              console.error(
                "[Combat][Slayer] Error awarding slayer XP:",
                slayerXpError
              );
            }
          } else if (!bossFightResult.fled) {
            // Player died to boss - quest fails
            console.log(
              `[Combat][Slayer] Player died to slayer boss ${bossInstanceForFight.name}. Cancelling quest.`
            );

            // Removed combat stop functionality - boss defeat cleanup check

            try {
              await playerDataModule.savePlayerData(
                userId,
                {
                  active_slayer_quest: null,
                },
                ["active_slayer_quest"]
              );

              activeSlayerQuest = null;
              slayerQuestData = null;

              // Set defeat details and end combat
              playerWasDefeated = true;
              defeatedByMobDetails = {
                name: bossInstanceForFight.name,
                emoji: bossInstanceForFight.emoji,
              };

              console.log(
                `[Combat][Slayer] Quest failed! Ending combat sequence.`
              );
              break;
            } catch (questClearError) {
              console.error(
                "[Combat][Slayer] Error clearing failed quest:",
                questClearError
              );
            }
          } else {
            // Player fled from boss - quest continues
            console.log(
              `[Combat][Slayer] Player fled from slayer boss. Quest continues.`
            );
            playerInterrupted = true;
            break;
          }
        }
      }
    }
  } catch (loopError) {
    console.error(
      `[Combat][Loop] Error during sequence (Action ID ${actionDbId}):`,
      loopError
    );
    try {
      if (messageToEdit && messageToEdit.edit) {
        console.log(
          `[Combat][StopButton] Loop error - clearing all components`
        );
        await messageToEdit.edit({
          content: "An error occurred during combat. Action terminated.",
          embeds: [],
          // components: [], // Don't touch components to prevent button recreation
        });
      }
    } catch (editError) {
      console.error(
        "[Combat Handler] Error editing message after loop error:",
        editError
      );
    }
    playerWasDefeated = true;
  }

  const totalFightsCompletedOverall =
    startingCycle + fightsActuallyCompletedThisSession;

  console.log(
    `[Combat][Results][DEBUG] Final counts: startingCycle=${startingCycle}, fightsActuallyCompletedThisSession=${fightsActuallyCompletedThisSession}, totalFightsCompletedOverall=${totalFightsCompletedOverall}`
  );
  let finalTotalExpGained = totalExpGained;

  // Check if this is a slayer quest
  const isSlayerQuest =
    activeSlayerQuest && slayerQuestData && slayerQuestData.isActive;

  if (currentCharacterState && currentCharacterState.stats) {
    const { calculateTotalStat } = require("../utils/statCalculations");
    const baseCombatWisdom = calculateTotalStat(
      currentCharacterState.stats.COMBAT_WISDOM || {}
    );

    // Calculate weapon Combat Wisdom bonus for current mob (non-accumulative)
    let weaponCombatWisdomBonus = 0;
    if (currentCharacterState.equipment?.equipped?.WEAPON) {
      const weaponItemKey = currentCharacterState.equipment.equipped.WEAPON;
      const equippedWeapon = currentCharacterState.inventory.equipment.find(
        (eq) => eq.itemKey === weaponItemKey && eq.isEquipped
      );
      if (equippedWeapon) {
        const allItems = require("../utils/configManager").getAllItems();
        const weaponDef = allItems[equippedWeapon.itemKey];
        if (weaponDef?.abilities) {
          for (const [, ability] of Object.entries(weaponDef.abilities)) {
            // Check if current mob is valid for this ability
            let isTargetMob = false;
            if (ability.targetMobType && baseMobData.mobType) {
              isTargetMob =
                baseMobData.mobType.toLowerCase() ===
                ability.targetMobType.toLowerCase();
            }

            if (
              isTargetMob &&
              ability.type === "STAT_BONUS" &&
              ability.stat === "COMBAT_WISDOM"
            ) {
              weaponCombatWisdomBonus += ability.value || 0;
            }
          }
        }
      }
    }

    // Calculate total Combat Wisdom (base + weapon bonus, not accumulated)
    const totalCombatWisdom = baseCombatWisdom + weaponCombatWisdomBonus;

    if (totalCombatWisdom > 0) {
      const wisdomMultiplier = 1 + totalCombatWisdom / 100;
      finalTotalExpGained = parseFloat(
        (totalExpGained * wisdomMultiplier).toFixed(2)
      );

      if (isSlayerQuest) {
      } else {
      }
    }
  }

  const rewardApplicationResult = await applyActionRewards(
    userId,
    currentCharacterState,
    baseMobData.baseExp?.sourceSkill || "combat",
    finalTotalExpGained,
    totalPetExpGained,
    itemsGainedAggregated,
    totalCoinsGained,
    actualMobKey,
    totalFightsCompletedOverall
  );
  currentCharacterState = rewardApplicationResult.character;

  // Apply death system for defeated players
  if (playerWasDefeated) {
    const { processPlayerDeath } = require("../utils/deathSystem");

    const deathResult = await processPlayerDeath(
      userId,
      currentCharacterState,
      defeatedByMobDetails,
      "direct_combat"
    );

    console.log(
      `[Combat][Death] Death system processed for user ${userId}. Penalty: ${deathResult.deathPenalty}, Auto-revived: ${deathResult.autoRevived}`
    );
  }

  // Apply pending coin rewards BEFORE creating the embed to ensure accurate display
  const channel = interaction.channel || messageToEdit?.channel;
  const channelInteraction = channel
    ? { ...interaction, channel }
    : interaction;

  // Fetch fresh character data AFTER all rewards (including coins) are applied
  let finalCharacterForEmbed;
  try {
    const freshCharacterData = await playerDataModule.getPlayerData(userId);
    finalCharacterForEmbed =
      freshCharacterData ||
      rewardApplicationResult.character ||
      currentCharacterState;
    console.log(
      `[Combat][Results] Using fresh character data for embed after coin application. Coins: ${finalCharacterForEmbed.coins?.purse || finalCharacterForEmbed.coins || 0}`
    );
  } catch (fetchError) {
    console.error(
      `[Combat][Results] Failed to fetch fresh character data:`,
      fetchError
    );
    finalCharacterForEmbed =
      rewardApplicationResult.character || currentCharacterState;
  }

  const { getTamingExpFromPetExp } = require("../utils/expGain");
  const totalTamingExpGained = getTamingExpFromPetExp(totalPetExpGained);

  // Calculate net coin change (final balance - starting balance) to account for auto-slayer costs
  const finalCoinBalance = finalCharacterForEmbed?.coins || 0;
  const netCoinChange = finalCoinBalance - startingCoinBalance;

  // Calculate total slayer XP from all types and determine primary slayer type
  const totalSlayerExpGained = Object.values(slayerXpByType).reduce(
    (sum, xp) => sum + xp,
    0
  );
  let slayerType = null;
  if (totalSlayerExpGained > 0) {
    // Use the slayer type with the most XP gained, or the original slayer type if available
    if (Object.keys(slayerXpByType).length > 0) {
      // Get the slayer type with the highest XP gain
      slayerType = Object.entries(slayerXpByType).sort(
        ([, a], [, b]) => b - a
      )[0][0];
    }

    // Fallback to original slayer type if we have it
    if (!slayerType && originalSlayerType) {
      slayerType = originalSlayerType;
    }
  }

  const actionDuration = Date.now() - actionStartTime;

  const resultObj = {
    skillName: "combat",
    exp: finalTotalExpGained,
    petExp: totalPetExpGained,
    tamingExp: totalTamingExpGained,
    items: itemsGainedAggregated,
    coins: netCoinChange, // Use net change instead of just combat rewards
    killedCount: totalFightsCompletedOverall,
    mobEmoji: baseMobData.emoji,
    mobName: baseMobData.name,
    defeatedMobs: (() => {
      // Since we now use optimized format throughout combat, check if it's already optimized
      if (!defeatedMobs || Object.keys(defeatedMobs).length === 0) {
        return {};
      }

      // Check if already in optimized format (values are numbers)
      const isAlreadyOptimized = Object.values(defeatedMobs).every(
        (value) => typeof value === "number"
      );

      if (isAlreadyOptimized) {
        return defeatedMobs; // Already optimized
      } else {
        // Still in detailed format, optimize it
        const {
          optimizeDefeatedMobsForStorage,
        } = require("../utils/mobDisplayUtils");
        return optimizeDefeatedMobsForStorage(defeatedMobs);
      }
    })(),
    color: playerInterrupted
      ? combatSkillConfig.colors.multiActionStopped
      : playerWasDefeated
        ? "#DC143C"
        : "#00FF00",
    finalCharacterData: finalCharacterForEmbed,
    finalSkillData:
      rewardApplicationResult.updatedSkillData ||
      finalCharacterForEmbed.skills.combat,
    defeated: playerWasDefeated,
    defeatedBy: playerWasDefeated ? defeatedByMobDetails : undefined,
    isInterrupted: playerInterrupted,
    originalAmount: loopTotalAmount,
    slayerExp: totalSlayerExpGained,
    slayerXpByType: slayerXpByType, // Pass detailed slayer XP breakdown
    slayerType: slayerType,
    // runtime duration for footer
    duration: Date.now() - actionStartTime,
    actionDuration: actionDuration,
  };
  const finalEmbed = buildFinalSkillResultEmbed(resultObj);

  let repeatComponents = [];
  if (
    !playerWasDefeated &&
    !playerInterrupted &&
    totalFightsCompletedOverall > 0
  ) {
    const repeatButton = new (require("discord.js").ButtonBuilder)()
      .setCustomId(
        `repeat_skill:${userId}:combat:${baseMobData.key}:${loopTotalAmount}:${wasMax ? "max" : "normal"}`
      )
      .setLabel("Repeat")
      .setStyle(require("discord.js").ButtonStyle.Primary);
    repeatComponents = [
      new (require("discord.js").ActionRowBuilder)().addComponents(
        repeatButton
      ),
    ];
  }
  try {
    // Always send final results as a new message so they appear as the latest thing in the channel
    const channel = interaction.channel || messageToEdit?.channel;
    if (channel) {
      await channel.send({
        embeds: [finalEmbed],
        components: repeatComponents,
      });
      console.log(
        `[Combat][Results] Final combat results sent as embed for better visibility`
      );

      // PATCH: Only delete the original action message if the action is fully complete (all cycles done)
      const actionIsFullyComplete =
        totalFightsCompletedOverall >= loopTotalAmount ||
        playerWasDefeated ||
        playerInterrupted;
      if (actionIsFullyComplete && messageToEdit && messageToEdit.delete) {
        try {
          await messageToEdit.delete();
          console.log(
            `[Combat][Results] Original action message deleted for cleaner channel (action complete)`
          );
        } catch (deleteError) {
          console.warn(
            `[Combat][Results] Could not delete original action message:`,
            deleteError.message
          );
        }
      }

      // Combat action ping - AFTER combat results embed
      let shouldPing = false;
      const pingActionId = actionDbId;
      try {
        const player = await playerDataModule.getPlayerData(userId);
        shouldPing = player?.settings?.pingOnMultiAction === true;

        if (
          shouldPing &&
          pingActionId &&
          (!player.pingStates || typeof player.pingStates !== "object")
        ) {
          player.pingStates = {};
        }
      } catch (e) {
        console.error(
          "[Combat Handler] Failed to fetch player data for pingOnMultiAction check:",
          e
        );
      }

      if (
        shouldPing &&
        pingActionId &&
        loopTotalAmount > 0 &&
        !auxData.isSeaCreatureSubCombat
      ) {
        const playerForPingCheck = await playerDataModule.getPlayerData(userId);
        if (
          playerForPingCheck.pingStates &&
          playerForPingCheck.pingStates[pingActionId]
        ) {
          console.log(
            `[Combat Handler][Ping] Action ${pingActionId} already pinged. Skipping.`
          );
        } else {
          const { EmbedBuilder } = require("discord.js");
          const originalRequestedAmount =
            parseInt(
              interaction.isResumption ? originalTotalAmount : amount,
              10
            ) || 1;
          let pingEmbed = null;

          if (!playerInterrupted) {
            if (
              playerWasDefeated &&
              totalFightsCompletedOverall < originalRequestedAmount
            ) {
              // Ping for early defeat - simple format like original
              pingEmbed = new EmbedBuilder()
                .setColor(EMBED_COLORS.ERROR)
                .setDescription(
                  `Your **${originalRequestedAmount} ${baseMobData.emoji ? baseMobData.emoji + " " : ""}${baseMobData.name} Combat Actions** were interrupted after **${totalFightsCompletedOverall} actions** as you were defeated.`
                );
            } else if (
              !playerWasDefeated &&
              totalFightsCompletedOverall === originalRequestedAmount
            ) {
              // Ping for successful completion - simple format
              pingEmbed = new EmbedBuilder()
                .setColor(EMBED_COLORS.GREEN)
                .setDescription(
                  `Your ${baseMobData.emoji ? baseMobData.emoji + " " : ""}**${
                    baseMobData.name
                  } x${originalRequestedAmount}** combat action is finished!`
                );
            }
          }

          if (pingEmbed) {
            try {
              // Add delay after combat results before ping
              await new Promise((resolve) => setTimeout(resolve, 500));

              await interaction.channel.send({
                content: `<@${userId}>`,
                embeds: [pingEmbed],
              });

              await playerDataModule.updatePlayerSetting(userId, "pingStates", {
                ...playerForPingCheck.pingStates,
                [pingActionId]: true,
              });

              console.log(
                `[Combat Handler][Ping] Successfully sent ping for action ${pingActionId}.`
              );
            } catch (pingError) {
              console.error(
                `[Combat Handler][Ping] Failed to send ping for action ${pingActionId}:`,
                pingError
              );
            }
          }
        }
      }

      // Send level up and collection notifications AFTER combat ping
      if (
        rewardApplicationResult.allLevelUpEmbeds?.length > 0 ||
        rewardApplicationResult.collectionNotifications?.length > 0
      ) {
        // Add delay before level up notifications if ping was sent
        if (
          shouldPing &&
          pingActionId &&
          loopTotalAmount > 0 &&
          !auxData.isSeaCreatureSubCombat
        ) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }

        await sendActionFollowups(
          channelInteraction,
          rewardApplicationResult.allLevelUpEmbeds,
          rewardApplicationResult.collectionNotifications,
          rewardApplicationResult.pendingCoinRewards || 0
        );
      }

      // Ensure Disblock XP reconciliation runs after combat so players receive
      // XP and level-up notifications for achievements earned during combat
      try {
        const {
          checkAndNotifyDisblockXP,
        } = require("../utils/disblockXpSystem");
        await checkAndNotifyDisblockXP(userId, channelInteraction);
      } catch (xpError) {
        console.error(
          "[Combat] Error updating Disblock XP after combat:",
          xpError
        );
      }
    } else {
      console.warn(
        "[Combat Handler] No channel available to send final results"
      );
    }
  } catch (e) {
    console.error("[Combat Handler] Error sending final results message:", e);
  }

  // sendActionFollowups was moved earlier to apply coin rewards before embed creation

  let finalCharacterToSave = await playerDataModule.getPlayerData(userId);
  if (!finalCharacterToSave) {
    console.warn(
      `[Combat][SaveFail] Failed to re-fetch player data for ${userId} after coin updates. Attempting to save with currentCharacterState, but coin balance might be incorrect if initial fetch failed.`
    );

    finalCharacterToSave = currentCharacterState;
  }

  await playerDataModule.updatePlayerSkillDataAtomically(
    userId,
    finalCharacterToSave
  );

  if (!auxData.isSeaCreatureSubCombat) {
    if (actionDbId) {
      await actionPersistenceModule.completeAction(actionDbId);

      // Cleanup abort controller for instant stop system
      const { cleanupAbortController } = require("../utils/instantStopUtils");
      cleanupAbortController(actionDbId);
      //console.log(`[InstantStop] Cleaned up AbortController for combat action ${actionDbId}`);
    }
    activityModule.clearActivity(userId);
  }

  return {
    finalCharacterState: finalCharacterToSave,
    exp: finalTotalExpGained,
    coins: totalCoinsGained,
    items: itemsGainedAggregated,
    defeated: playerWasDefeated,
    defeatedBy: playerWasDefeated ? defeatedByMobDetails : undefined,
    isInterrupted: playerInterrupted,
    killedCount: totalFightsCompletedOverall,
  };
}

// Combat handler registration no longer needed - using unified system

module.exports = {
  data: new SlashCommandBuilder()
    .setName("combat")
    .setDescription("Fight a monster.")
    .addStringOption((option) =>
      option
        .setName("mob")
        .setDescription("Name of the mob to fight")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'Number of times to fight (e.g., 5, or type "max"). Default 1.'
        )
        .setRequired(false)
    ),
  async execute(interaction) {
    // Extract and validate parameters before delegation
    const {
      extractAndValidateSkillCommandParams,
    } = require("../utils/commandUtils");
    const params = await extractAndValidateSkillCommandParams(
      interaction,
      combatSkillConfig
    );

    if (!params) {
      // extractAndValidateSkillCommandParams already sent error response
      return;
    }

    // Delegate to worker bot
    const { workerManager } = require("../utils/workerManager");
    await workerManager.delegateAction(
      interaction,
      "combat",
      params.amount,
      params.resourceKey,
      params.wasMax
    );
  },
  async autocomplete(interaction) {
    const focusedOption = interaction.options.getFocused(true);
    const focusedValue = focusedOption.value.toLowerCase();

    if (focusedOption.name !== "mob") {
      if (!interaction.responded) {
        await interaction.respond([]);
      }
      return;
    }

    let character;
    try {
      character = await playerDataModule.getPlayerData(interaction.user.id);
    } catch (error) {
      console.error("[Combat Autocomplete] Error fetching player data:", error);
      if (!interaction.responded) {
        await interaction.respond([]);
      }
      return;
    }

    const mobs = configManager.getAllMobs();

    let choices;
    if (character && character.current_region) {
      const currentRegionKey = character.current_region;
      choices = Object.values(mobs)
        .filter(
          (mob) =>
            Array.isArray(mob.spawnRegions) &&
            mob.spawnRegions.includes(currentRegionKey) &&
            mob.name.toLowerCase().includes(focusedValue)
        )
        .map((mob) => ({
          name: `${mob.name} (Lvl ${mob.level})`,
          value: mob.key,
        }))
        .slice(0, 25);
    } else {
      choices = Object.values(mobs)
        .filter((mob) => mob.name.toLowerCase().includes(focusedValue))
        .map((mob) => ({
          name: `${mob.name} (Lvl ${mob.level})`,
          value: mob.key,
        }))
        .slice(0, 25);
    }

    if (!interaction.responded) {
      await interaction.respond(choices);
    } else {
      console.warn(
        `[Combat Autocomplete] Interaction already responded before final respond. FocusedValue: "${focusedValue}". This might indicate an issue with control flow.`
      );
    }
  },
  combatSkillConfig,
  handleCombatAction,
};
