// Migration Script: 026_fix_crafted_minions_format.js
// Purpose: Re-applies craftedMinions cleanup: standardizes all keys to UPPERCASE, _Tn format, and removes malformed/duplicate keys.
//          Use this if previous migrations were missed or new malformed data was introduced.

const TIERED_KEY_REGEX = /^(.*)_T(\d+)$/;

async function up(db, playerDataUtils) {
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("Running crafted minion format fix migration (026)...");
  let processedCount = 0;
  let migratedCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log("No player IDs found. Migration step 026 complete.");
    return;
  }
  console.log(`Found ${playerIds.length} players to check for migration 026.`);

  for (const userId of playerIds) {
    processedCount++;
    try {
      const characterData = await getPlayerData(userId);
      if (
        !characterData ||
        !Array.isArray(characterData.craftedMinions) ||
        characterData.craftedMinions.length === 0
      ) {
        continue; // Skip players with no or empty craftedMinions array
      }

      const oldCraftedMinions = characterData.craftedMinions;
      const highestTiers = {}; // { baseKey: maxTierReached }
      const baseT1Crafts = new Set(); // { baseKey, ... }
      let needsUpdate = false; // Flag if conversion actually changes the array content/order

      // 1. Analyze the old array
      for (const entry of oldCraftedMinions) {
        if (typeof entry !== "string") continue; // Skip non-string entries just in case

        const match = entry.match(TIERED_KEY_REGEX);
        if (match) {
          // It's a tiered key (e.g., COBBLESTONE_MINION_T5)
          const baseKey = match[1];
          const tier = parseInt(match[2], 10);

          baseT1Crafts.add(baseKey); // Reaching T2+ implies T1 was crafted
          if (!highestTiers[baseKey] || tier > highestTiers[baseKey]) {
            highestTiers[baseKey] = tier;
            needsUpdate = true; // Found a tier, indicating potential format change needed
          }
        } else {
          // It's likely a base key (e.g., COBBLESTONE_MINION)
          if (!baseT1Crafts.has(entry)) {
            baseT1Crafts.add(entry);
            needsUpdate = true; // Adding a base key might change the array
          }
          // Check if this base key exists in tiered data but not as T1 explicitly
          if (
            !highestTiers[entry] &&
            !oldCraftedMinions.some((e) =>
              e.match(new RegExp(`^${entry}_T\\d+$`)),
            )
          ) {
            // This base key existed standalone, possibly indicating only T1 was crafted
          }
        }
      }

      // 2. Build the new array based on analysis
      const newCraftedMinionsSet = new Set(); // Use a set to avoid duplicates initially

      // Add all base T1 crafts (standardize to UPPERCASE)
      baseT1Crafts.forEach((baseKey) =>
        newCraftedMinionsSet.add(baseKey.toUpperCase() + "_T1"),
      );

      // Add tiered crafts (T2+), standardize to UPPERCASE and _T format
      for (const [baseKey, maxTier] of Object.entries(highestTiers)) {
        for (let tier = 2; tier <= maxTier; tier++) {
          newCraftedMinionsSet.add(`${baseKey.toUpperCase()}_T${tier}`);
        }
      }

      // Remove any malformed or duplicate keys (e.g., with lowercase '_t' or extra suffixes)
      // const malformedPattern = /^.*_t\d+.*$/i;
      // const cleanedOld = oldCraftedMinions.filter(key => !malformedPattern.test(key)).map(k => k.toUpperCase());

      // Convert set to array for saving (optional: sort for consistency?)
      const newCraftedMinions = Array.from(newCraftedMinionsSet).sort();

      // 3. Compare and Save if changed
      // Simple length check first
      if (newCraftedMinions.length !== oldCraftedMinions.length) {
        needsUpdate = true;
      } else {
        // More thorough check if lengths are same (order might differ or content might be subtly same)
        const oldSorted = [...oldCraftedMinions].sort();
        if (JSON.stringify(newCraftedMinions) !== JSON.stringify(oldSorted)) {
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        console.log(`  - Player ${userId}: Updating craftedMinions array.`);
        characterData.crafted_minions_json = newCraftedMinions;
        await savePlayerData(userId, characterData);
        migratedCount++;
      }
    } catch (error) {
      console.error(
        `  - Error processing player ${userId} for migration 026:`,
        error,
      );
      errorCount++;
    }
  }

  console.log(
    `Migration 026 complete. Processed: ${processedCount}, Migrated: ${migratedCount}, Errors: ${errorCount}`,
  );
}

module.exports = { up };
