// Migration 059: Add shop_limits_json column for NPC shop purchase limits
// This column stores daily purchase limits per NPC shop item

async function up(db) {
  console.log("Running migration 059: Add shop_limits_json column...");

  try {
    // Check if column already exists
    const columns = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(players)", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const hasShopLimitsColumn = columns.some(
      (col) => col.name === "shop_limits_json",
    );

    if (!hasShopLimitsColumn) {
      console.log("Adding shop_limits_json column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN shop_limits_json TEXT DEFAULT '{}'",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added shop_limits_json column");
    } else {
      console.log("shop_limits_json column already exists, skipping...");
    }

    console.log("Migration 059 completed successfully");
  } catch (error) {
    console.error("Migration 059 failed:", error);
    throw error;
  }
}

module.exports = { up };
