const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const {
  getPlayerData,
  updatePlayerSetting,
} = require("../utils/playerDataManager.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("pingme")
    .setDescription(
      "Toggle getting pinged when a multi-action (combat, fishing, farming, etc.) finishes."
    ),
  async execute(interaction) {
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      return interaction.reply({
        content: "Could not find your player data.",
      });
    }

    // Get current setting value, defaulting to false if not set
    const currentSetting = character.settings?.pingOnMultiAction || false;
    const newSetting = !currentSetting;

    // Atomically update the setting to prevent race conditions
    await updatePlayerSetting(
      interaction.user.id,
      "pingOnMultiAction",
      newSetting
    );

    const statusEmoji = newSetting ? "🔔" : "🔕";
    const statusText = newSetting ? "enabled" : "disabled";
    const actionText = newSetting
      ? "You will **now** receive pings for completed actions."
      : "You will **no longer** receive pings for completed actions.";

    const embed = new EmbedBuilder()
      .setTitle(
        `${statusEmoji} Ping Notifications ${statusText.charAt(0).toUpperCase() + statusText.slice(1)}`
      )
      .setDescription(actionText)
      .setColor(newSetting ? "#57F287" : "#ED4245");

    const reply = await interaction.reply({ embeds: [embed], ephemeral: true });

    setTimeout(() => {
      try {
        reply.delete();
      } catch (error) {
        console.error("[Pingme Command] Error deleting message:", error);
      }
    }, 10000);
  },
};
