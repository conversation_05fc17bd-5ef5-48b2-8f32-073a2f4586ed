const { v4: uuidv4 } = require("uuid");

const STATS = {
  HEALTH: { name: "Health", emoji: "<:health:1269719825429565532>", base: 100 },
  DAMAGE: { name: "Damage", emoji: "<:damage:1270616867484209253>", base: 5 },
  INTELLIGENCE: {
    name: "Intelligence",
    emoji: "<:intelligence:1269719826931384400>",
    base: 100,
  },
  DEFENSE: {
    name: "Defense",
    emoji: "<:defense:1269719821399097456>",
    base: 0,
  },
  STRENGTH: {
    name: "Strength",
    emoji: "<:strength:1269719830148157481>",
    base: 0,
  },
  CRIT_CHANCE: {
    name: "Crit Chance",
    emoji: "<:critchance:1269719818685251675>",
    base: 30,
  },
  CRIT_DAMAGE: {
    name: "Crit Damage",
    emoji: "<:critdamage:1269719819884953610>",
    base: 50,
  },
  HEALTH_REGEN: {
    name: "Health Regen",
    emoji: "<:healthregen:1269719824142172271>",
    base: 100,
  },
  VITALITY: {
    name: "Vitality",
    emoji: "<:vitality:1269719834082545694>",
    base: 100,
  },
  DODGE_CHANCE: {
    name: "Dodge Chance",
    emoji: "<:dodge:1370766857447014591>",
    base: 0,
    isPercentage: true,
  },
  FISHING_SPEED: {
    name: "Fishing Speed",
    emoji: "<:fishing_speed:1270616476428271658>",
    base: 0,
  },
  SEA_CREATURE_CHANCE: {
    name: "Sea Creature Chance",
    emoji: "<:sea_creature_chance:1369190745730449479>",
    base: 20,
  },
  TREASURE_CHANCE: {
    name: "Treasure Chance",
    emoji: "<:treasure_chance:1369190619385167882>",
    base: 0,
    isPercentage: true,
  },
  FORAGING_FORTUNE: {
    name: "Foraging Fortune",
    emoji: "<:foraging_fortune:1270616477824712714>",
    base: 0,
  },
  FARMING_FORTUNE: {
    name: "Farming Fortune",
    emoji: "<:foraging_fortune:1270616477824712714>",
    base: 0,
  },
  MINING_FORTUNE: {
    name: "Mining Fortune",
    emoji: "<:foraging_fortune:1270616477824712714>",
    base: 0,
  },
  FORAGING_SWEEP: {
    name: "Foraging Sweep",
    emoji: "<:foraging_sweep:1385549884186431578>",
    base: 1,
  },
  FARMING_SWEEP: {
    name: "Farming Sweep",
    emoji: "<:farming_sweep:1385551740438450237>",
    base: 1,
  },
  MINING_SWEEP: {
    name: "Mining Sweep",
    emoji: "<:mining_sweep:1385551769420959805>",
    base: 1,
  },
  FARMING_WISDOM: {
    name: "Farming Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  MINING_WISDOM: {
    name: "Mining Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  FORAGING_WISDOM: {
    name: "Foraging Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  FISHING_WISDOM: {
    name: "Fishing Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  COMBAT_WISDOM: {
    name: "Combat Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  ENCHANTING_WISDOM: {
    name: "Enchanting Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  ALCHEMY_WISDOM: {
    name: "Alchemy Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  TAMING_WISDOM: {
    name: "Taming Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  CRAFTING_WISDOM: {
    name: "Crafting Wisdom",
    emoji: "<:wisdom_stat:1369101731278880849>",
    base: 0,
  },
  MAGIC_FIND: {
    name: "Magic Find",
    emoji: "<:stat_magic_find:1399855928894951425>",
    base: 0,
  },
  POTION_DURATION: {
    name: "Potion Duration",
    emoji: "<:awkward_potion:1395356657655087165>",
    base: 0,
    isPercentage: true,
  },
  DAMAGE_MULT: {
    name: "Damage Multiplier",
    emoji: "<:damage:1270616867484209253>",
    base: 0,
    isPercentage: true,
    hideFromDisplay: true,
  },
};

// Define currency names and emojis
const CURRENCY = {
  name: "Coins",
  emoji: "<:purse_coins:1367849116033482772>", // Custom Discord emoji for coins
  purseEmoji: "<:purse_coins:1367849116033482772>",
  bankEmoji: "<:bank_coins:1367849114242383987>",
};

const COPPER = {
  name: "Copper",
  emoji: "<:copper:1396059859539198045>",
};

// Disblock level and gems emoji constants
const DISBLOCK_LEVEL_EMOJI = "<:disblock_level:1381196165696983080>";
const GEMS_EMOJI = "<:emerald:1375550740654981230>"; // Using emerald for gems display
const BITS_EMOJI = "<:Bits:1400387873617739846>"; // Bits currency emoji
const MARKET_EMOJI = "<:market:1399951403656679485>"; // Main bazaar emoji

// Market configuration
const MARKET_LIMITS = {
  MAX_BUY_ORDERS: 8,
  MAX_SELL_ORDERS: 8,
  MAX_PRICE_PER_ITEM: 2_000_000_000, // 2 billion coins maximum per item
};

const EXP = { name: "EXP", emoji: "💠" };

// Define Skill Names
const SKILLS = {
  LIST: [
    "combat",
    "fishing",
    "mining",
    "farming",
    "foraging",
    "alchemy",
    "enchanting",
    "taming",
    "dungeoneering",
    "crafting",
    "cooking",
  ],
  AVERAGE_EXCLUDED: ["cooking", "dungeoneering"],
};

const SKILL_CATEGORIES = {
  GATHERING: {
    name: "Gathering",
    emoji: "⛏️",
    skills: ["mining", "fishing", "foraging", "farming"],
  },
  COMBAT: {
    name: "Combat",
    emoji: "⚔️",
    skills: ["combat", "taming", "dungeoneering"],
  },
  CRAFTING: {
    name: "Crafting",
    emoji: "🔧",
    skills: ["crafting", "cooking", "alchemy", "enchanting"],
  },
};

const ITEM_TYPES = {
  WEAPON: {
    name: "WEAPON",
    subtypes: { SWORD: "SWORD", AXE: "AXE", BOW: "BOW" },
  },
  ARMOR: {
    name: "ARMOR",
    subtypes: {
      HELMET: "HELMET",
      CHESTPLATE: "CHESTPLATE",
      LEGGINGS: "LEGGINGS",
      BOOTS: "BOOTS",
    },
  },
  EQUIPMENT: {
    name: "EQUIPMENT",
    subtypes: {
      NECKLACE: "NECKLACE",
      CLOAK: "CLOAK",
      BELT: "BELT",
      GLOVES: "GLOVES",
      BRACELET: "BRACELET",
    },
  },
  TALISMAN: "TALISMAN",
  MATERIAL: "Material",
  TOOL: {
    name: "TOOL",
    subtypes: {
      ROD: "ROD",
      PICKAXE: "PICKAXE",
      AXE: "AXE",
      SHOVEL: "SHOVEL",
      HOE: "HOE",
      DRILL: "DRILL",
    },
  },
  CONSUMABLE: "CONSUMABLE",
  PET: "PET",
  MINION: "MINION",
};

// Wardrobe Configuration
const WARDROBE = {
  MAX_SLOTS: 3, // Maximum number of wardrobe preset slots
};

const ITEM_RARITY = {
  COMMON: { name: "Common", color: "#FFFFFF" },
  UNCOMMON: { name: "Uncommon", color: "#1EFF00" },
  RARE: { name: "Rare", color: "#0070DD" },
  EPIC: { name: "Epic", color: "#A335EE" },
  LEGENDARY: { name: "Legendary", color: "#FF8000" },
  MYTHIC: { name: "Mythic", color: "#FF1493" },
  SPECIAL: { name: "Special", color: "#FF6B6B" },
};

// --- ADD Pet Rarity Order ---
const PET_RARITIES = ["COMMON", "UNCOMMON", "RARE", "EPIC", "LEGENDARY"];
// ---------------------------

// --- Load ITEMS from JSON ---
const configManager = require("./utils/configManager");
const ITEMS = configManager.getAllItems();
// If ITEMS is used, replace with configManager.getAllItems() or appropriate.
// --- End Re-insert ---

function createItem(itemKeyOrName, additionalStats = {}) {
  const baseItem =
    typeof itemKeyOrName === "object" && itemKeyOrName.name
      ? Object.values(ITEMS).find((item) => item.name === itemKeyOrName.name)
      : ITEMS[itemKeyOrName] ||
        Object.values(ITEMS).find((item) => item.name === itemKeyOrName);
  if (!baseItem) {
    console.error("Item not found:", itemKeyOrName);
    return null;
  }
  return baseItem.unique
    ? {
        id: uuidv4(),
        ...baseItem,
        stats: { ...baseItem.baseStats, ...additionalStats },
        createdAt: new Date().toISOString(),
        subtype: baseItem.subtype,
      }
    : { ...baseItem };
}

const NPC_TYPES = {
  MERCHANT: "MERCHANT",
  QUEST_GIVER: "QUEST_GIVER",
  BANKER: "BANKER",
  EVENT_MERCHANT: "EVENT_MERCHANT",
  PET_SITTER: "PET_SITTER",
  IDENTITY_CHANGER: "IDENTITY_CHANGER",
  COMMUNITY_SHOP: "COMMUNITY_SHOP",
};

// *** NEW: Minion Slot Configuration ***
const BASE_MINION_SLOTS = 5;

// *** Combat Configuration ***
// static damage multiplier to compensate for turn-based combat vs minecraft's real-time combat
const STATIC_DAMAGE_MULTIPLIER = 1;
const MINION_SLOT_UNLOCKS = [
  { crafts: 5, bonus: 1 },
  { crafts: 15, bonus: 2 },
  { crafts: 30, bonus: 3 },
  { crafts: 50, bonus: 4 },
  { crafts: 75, bonus: 5 },
  { crafts: 100, bonus: 6 },
  { crafts: 125, bonus: 7 },
  { crafts: 150, bonus: 8 },
  { crafts: 175, bonus: 9 },
  { crafts: 200, bonus: 10 },
  { crafts: 225, bonus: 11 },
  { crafts: 250, bonus: 12 },
  { crafts: 275, bonus: 13 },
  { crafts: 300, bonus: 14 },
  { crafts: 350, bonus: 15 },
  { crafts: 400, bonus: 16 },
  { crafts: 450, bonus: 17 },
  { crafts: 500, bonus: 18 },
  { crafts: 550, bonus: 19 },
  { crafts: 600, bonus: 20 },
  { crafts: 650, bonus: 21 },
  // Add more tiers if needed, ensure sorted by crafts ascending
];

// --- Add Skill Emojis Map ---
const skillEmojis = {
  alchemy: "<:skill_alchemy:1367753353177600082>",
  combat: "<:skill_combat:1367753354603925514>",
  cooking: "<a:skill_cooking:1398920102837227530>",
  crafting: "<:skill_crafting:1367753356331716659>",
  dungeoneering: "<:skill_dungeoneering:1367753357942325348>",
  enchanting: "<a:enchanting:1403030704400371764>",
  // old to keep as reference <:skill_enchanting:1367753358965870674>
  farming: "<:skill_farming:1367753361511682128>",
  fishing: "<:skill_fishing:1367753362732351589>",
  foraging: "<:skill_foraging:1367753364137443439>",
  mining: "<:skill_mining:1367753365202796636>",
  taming: "<:skill_taming:1367753422018842644>",
  npcs: "<:npc_warpmenu:1386575955027558460>",
  default: "❓", // Fallback emoji
};
// ---------------------------

const MAX_PLACEMENT_DISTANCE = 5; // Example distance in meters or units

// Default player structure
const defaultPlayer = {
  // ... existing code ...
};

// Standard embed colors used across commands/menus
const EMBED_COLORS = {
  BLUE: "#0099ff", // General info, menus, stats
  GREEN: "#00ff00", // Success / confirmation
  GOLD: "#FFD700", // Achievements, level-ups
  RED: "#ff0000", // Errors, restrictions
  ORANGE: "#FFA500", // Warnings / notifications
  LIGHT_GREEN: "#57F287", // Discord success green
  LIGHT_RED: "#ED4245", // Discord error red
  GREY: "#808080", // Neutral / info
  PURPLE: "#9966CC", // Special NPCs (Maxwell)
  YELLOW: "#FFFF00", // Generic yellow alerts / info
  LIGHT_YELLOW: "#FEE75C", // Discord yellow highlight (sunshine)
  DARK_GOLD: "#DAA520", // Darker gold accent
  MID_BLUE: "#3498DB", // Mid-tone blue variations
  SKY_BLUE: "#77CCEE", // Light sky blue variations
  BROWN: "#8B4513", // Brown for rustic or earth themes
  PINK_RED: "#FF6B6B", // Soft pink-red for special highlights
  PINK: "#FF69B4", // Kat / pink highlights
  DARK_GREEN: "#4CAF50", // Dark green variant
  LIGHT_GREY: "#AAAAAA", // Light grey variant
  BLURPLE: "#7289DA", // Discord blurple
  DARK_BROWN: "#964B00", // Dark brown variant
  DARK_RED: "#D9534F", // Dark red variant
  TEAL: "#00AE86", // Teal variant
  PASTEL_GREEN: "#90EE90", // Soft green for complete notices
  SILVER: "#C0C0C0", // Silver grey
  MID_GREY: "#626262", // Mid grey
  SIENNA: "#A0522D", // Brownish sienna
  ERROR: "#FF6B6B", // Error color for embeds
  SUCCESS: "#57F287", // Success color for embeds
};
// Default footer messages
const WARP_FOOTER = "Use /warp [destination] to travel elsewhere.";

module.exports = {
  CURRENCY,
  COPPER,
  EXP,
  ITEMS, // Export the ITEMS loaded from JSON
  ITEM_TYPES,
  ITEM_RARITY,
  STATS,
  SKILLS,
  SKILL_CATEGORIES,
  NPC_TYPES,
  BASE_MINION_SLOTS,
  MINION_SLOT_UNLOCKS,
  STATIC_DAMAGE_MULTIPLIER,
  skillEmojis,
  MAX_PLACEMENT_DISTANCE,
  defaultPlayer,
  createItem,
  PET_RARITIES,
  WARDROBE,
  EMBED_COLORS,
  DISBLOCK_LEVEL_EMOJI,
  GEMS_EMOJI,
  BITS_EMOJI,
  MARKET_EMOJI,
  MARKET_LIMITS,
  WARP_FOOTER,
};
