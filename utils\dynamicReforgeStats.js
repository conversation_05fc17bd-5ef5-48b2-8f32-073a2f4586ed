const { getReforgeStats } = require("../data/reforges");

/**
 * Calculates dynamic reforge stats from the current configuration
 * @param {Object} reforgeData - The reforge data from data_json
 * @param {Object} itemData - The item configuration data
 * @returns {Object} The calculated reforge stats or empty object if no reforge
 */
function calculateDynamicReforgeStats(reforgeData, itemData) {
  if (!reforgeData || !reforgeData.key || !itemData) {
    return {};
  }

  const { ITEM_RARITY } = require("../gameConfig");
  const rarity = ITEM_RARITY[itemData.rarity]?.name || "Common";

  // Get fresh stats from the configuration
  const reforgeStats = getReforgeStats(
    reforgeData.key,
    itemData.type,
    itemData.subtype,
    rarity
  );

  return reforgeStats?.stats || {};
}

/**
 * Gets the reforge name dynamically from configuration
 * @param {Object} reforgeData - The reforge data from data_json
 * @param {Object} itemData - The item configuration data
 * @returns {string} The reforge name or empty string
 */
function getDynamicReforgeName(reforgeData, itemData) {
  if (!reforgeData || !reforgeData.key || !itemData) {
    return "";
  }

  const { ITEM_RARITY } = require("../gameConfig");
  const rarity = ITEM_RARITY[itemData.rarity]?.name || "Common";

  const reforgeStats = getReforgeStats(
    reforgeData.key,
    itemData.type,
    itemData.subtype,
    rarity
  );

  return reforgeStats?.name || "";
}

/**
 * Converts old reforge format to new format
 * @param {Object} oldReforgeData - Old reforge data with hardcoded stats
 * @returns {Object} New reforge data with just the key
 */
function convertToNewReforgeFormat(oldReforgeData) {
  if (!oldReforgeData || !oldReforgeData.key) {
    return null;
  }

  return {
    key: oldReforgeData.key,
  };
}

/**
 * Checks if reforge data is in old format (has hardcoded stats)
 * @param {Object} reforgeData - The reforge data to check
 * @returns {boolean} True if in old format
 */
function isOldReforgeFormat(reforgeData) {
  return (
    reforgeData && reforgeData.stats && typeof reforgeData.stats === "object"
  );
}

module.exports = {
  calculateDynamicReforgeStats,
  getDynamicReforgeName,
  convertToNewReforgeFormat,
  isOldReforgeFormat,
};
