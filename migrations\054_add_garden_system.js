// Migration: 054_add_garden_system.js
// Purpose: Add Garden milestone tracking and Garden XP system

module.exports.up = async function up(db) {
  return new Promise((resolve, reject) => {
    // Add garden_milestones_json column to track crop milestones in the Garden
    db.run(
      "ALTER TABLE players ADD COLUMN garden_milestones_json TEXT",
      (err) => {
        if (err && !err.message.includes("duplicate column")) {
          console.error(
            "[Migration 054] Failed to add garden_milestones_json column:",
            err.message,
          );
          reject(err);
          return;
        }

        // Add garden_xp column to track Garden XP
        db.run(
          "ALTER TABLE players ADD COLUMN garden_xp INTEGER DEFAULT 0",
          (err2) => {
            if (err2 && !err2.message.includes("duplicate column")) {
              console.error(
                "[Migration 054] Failed to add garden_xp column:",
                err2.message,
              );
              reject(err2);
              return;
            }

            console.log(
              "[Migration 054] Garden system columns added successfully.",
            );
            resolve();
          },
        );
      },
    );
  });
};
