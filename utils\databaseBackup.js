const fs = require("fs");
const path = require("path");
const cron = require("node-cron");

const dbPath = path.resolve(__dirname, "..", "data", "database.db");
const backupDir = path.resolve(__dirname, "..", "Database Backups");

function ensureBackupDirectory() {
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
    console.log("Created Database Backups directory");
  }
}

function createBackup() {
  try {
    if (!fs.existsSync(dbPath)) {
      console.error("Database file not found:", dbPath);
      return;
    }

    ensureBackupDirectory();

    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const backupFileName = `database_backup_${timestamp}.db`;
    const backupPath = path.join(backupDir, backupFileName);

    fs.copyFileSync(dbPath, backupPath);
    console.log(`Database backup created: ${backupFileName}`);

    cleanOldBackups();
  } catch (error) {
    console.error("Error creating database backup:", error);
  }
}

function cleanOldBackups() {
  try {
    const files = fs
      .readdirSync(backupDir)
      .filter(
        (file) => file.startsWith("database_backup_") && file.endsWith(".db")
      )
      .map((file) => ({
        name: file,
        path: path.join(backupDir, file),
        time: fs.statSync(path.join(backupDir, file)).mtime,
      }))
      .sort((a, b) => b.time - a.time);

    const twoMonthsAgo = new Date();
    twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

    const filesToDelete = files.filter((file) => file.time < twoMonthsAgo);

    filesToDelete.forEach((file) => {
      fs.unlinkSync(file.path);
      console.log(`Deleted old backup: ${file.name}`);
    });

    if (filesToDelete.length === 0) {
      console.log("No old backups to clean");
    }
  } catch (error) {
    console.error("Error cleaning old backups:", error);
  }
}

function startBackupScheduler() {
  const QUIET_BOOT = process.env.QUIET_BOOT === "true";
  if (!QUIET_BOOT) console.log("Starting database backup scheduler (hourly)");

  cron.schedule("0 0 * * * *", () => {
    console.log("Running scheduled database backup...");
    createBackup();
  });
}

function manualBackup() {
  console.log("Creating manual database backup...");
  createBackup();
}

module.exports = {
  startBackupScheduler,
  manualBackup,
  createBackup,
};
