// Conditional imports based on whether this is running on a worker bot
const { ActionRowBuilder } = require("discord.js");
const isWorkerBot = process.env.IS_WORKER_BOT === "true";
const playerDataModule = isWorkerBot
  ? require("./workerDatabaseProxy")
  : require("./playerDataManager");
const inventoryModule = isWorkerBot
  ? require("./workerDatabaseProxy")
  : require("./inventory");
const actionPersistenceModule = isWorkerBot
  ? require("./workerDatabaseProxy")
  : require("./actionPersistence");
const activityModule = isWorkerBot
  ? require("./workerDatabaseProxy")
  : require("./activityManager");
const {
  buildAnimationUIFromEmbed,
  buildFishingUIFromEmbed,
} = require("./animationContainers");

const { getPlayerData, savePlayerData, updatePlayerSkillDataAtomically } =
  playerDataModule;
const { updateInventoryAtomically } = isWorkerBot
  ? playerDataModule
  : inventoryModule;
const { addSkillExp } = require("./skillExpManager");
const { getTamingExpFromPetExp, addPetExp } = require("./expGain");
const configManager = require("./configManager");

/**
 * Sends multiple embeds/containers as separate messages with delays between them
 * @param {Object} channel - Discord channel object
 * @param {Array<EmbedBuilder|Object>} items - Array of embeds or { embed, components } objects to send
 * @param {number} delayMs - Delay in milliseconds between messages (default 500ms)
 */
async function sendEmbedsWithDelay(channel, items, delayMs = 500) {
  if (!items || items.length === 0) return;

  for (let i = 0; i < items.length; i++) {
    try {
      const item = items[i];

      if (item && item.embed) {
        // Send embed with optional components
        await channel.send({
          embeds: [item.embed],
          components: item.components || [],
        });
      } else {
        // Legacy embed format
        await channel.send({ embeds: [item] });
      }

      // Add delay between messages (except after the last one)
      if (i < items.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    } catch (error) {
      console.error(
        `[sendEmbedsWithDelay] Error sending item ${i + 1}/${items.length}:`,
        error
      );
    }
  }
}

const { EmbedBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");
const {
  startAction,
  completeAction,
  getActionById,
  getActionParameters,
  updateActionMessageId,
  updateActionMessageTimestamp,
} = actionPersistenceModule;
const { batcher } = require("./actionProgressBatcher");
// messageThrottler removed - using manual stop button creation
const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
// Removed old stop system - now using simple abort signal
const { getPetLevel } = require("./petUtils");
const {
  createActionResultEmbed,
  buildFinalSkillResultEmbed,
} = require("./displayUtils");
const { clearActivity, setActivity, isConflict, getCurrentActivity } =
  activityModule;
const { logSkillActionCrash, logActivityLockCrash } = require("./crashLogger");
const { addCollectionProgress } = require("./collectionUtils");
const {
  persistPendingSkillAction,
  clearPendingSkillAction,
} = require("./skillStateUtils");

const { getLevelFromExp } = require("./expFunctions");
const { getPetTotalExp } = require("./petUtils");
const {
  createAbortController,
  getAbortSignal,
  cleanupAbortController,
  ActionCancelledError,
} = require("./instantStopUtils");

/**
 * Calculates derived EXP (Pet EXP) based on primary skill EXP gained.
 * Considers the skill being performed and the pet's associated skillType.
 * @param {number} primaryExpGain - The amount of EXP gained for the primary skill.
 * @param {string} skillName - The name of the skill being performed (e.g., 'mining', 'combat').
 * @param {object} character - The player's character object.
 * @returns {number} The calculated Pet EXP gain (can be fractional).
 */
function calculateActionDerivedExp(primaryExpGain, skillName, character) {
  let petExpGained = 0;
  let activePet = null;

  // Use character.active_pet_id for lookup
  if (
    !character ||
    primaryExpGain <= 0 ||
    !character.active_pet_id ||
    !character.pets
  ) {
    return 0;
  }

  activePet = character.pets.find((p) => p.id === character.active_pet_id);

  if (activePet) {
    // Use the raw experience value to calculate taming level
    const tamingExp = character.skills?.taming?.exp || 0;
    const tamingLevel = getLevelFromExp(tamingExp).level;
    const petExpMultiplier = 1 + tamingLevel * 0.01; // Taming level bonus

    const allItems = configManager.getAllItems();
    const petBaseData = allItems[activePet.petKey];

    let petExpRatio = 1 / 3; // Default ratio for non-matching skill
    // Check if pet's skillType matches the skill being performed
    if (petBaseData?.skillType && petBaseData.skillType === skillName) {
      petExpRatio = 1.0; // 100% EXP if pet skill matches the action
    }

    const rawPetExpGain = primaryExpGain * petExpRatio * petExpMultiplier;
    petExpGained = rawPetExpGain; // Assign directly
  } else {
    // No active pet found
  }

  return petExpGained; // Return only petExpGained
}

/**
 * Dynamically calculates total EXP from all sources during an action for Pet EXP calculation.
 * This ensures Pet EXP accounts for all EXP types earned during the action (primary skill + secondary encounters).
 *
 * @param {number} primaryExp - Primary skill EXP (e.g., Mining EXP)
 * @param {number} combatExp - Combat EXP from secondary encounters (e.g., Endermites)
 * @param {object} [futureExpTypes={}] - Object containing any future EXP types that might be added
 * @returns {number} Total action EXP for Pet EXP calculation
 */
function calculateTotalActionExp(
  primaryExp,
  combatExp = 0,
  futureExpTypes = {}
) {
  let totalExp = (primaryExp || 0) + (combatExp || 0);

  // Dynamically add any future EXP types
  for (const [amount] of Object.entries(futureExpTypes)) {
    if (typeof amount === "number" && amount > 0) {
      totalExp += amount;
    }
  }

  return totalExp;
}

/**
 * Applies ALL rewards (Inventory, Skill EXP, Pet EXP, Collections) for completed action(s).
 * Performs atomic inventory update internally.
 * IMPORTANT: Returns the LATEST character object AFTER all updates.
 *
 * @param {string} userId
 * @param {object} initialCharacter - Player object state BEFORE this action's rewards.
 * @param {string} skillName
 * @param {number} totalSkillExpGained
 * @param {number} totalPetExpGained
 * @param {object} totalItemsGainedMap - Map of item keys to amounts.
 * @param {number} [totalCoinsGained=0]
 * @returns {Promise<{character: object, updatedSkillData: object, collectionNotifications: Array<EmbedBuilder>, allLevelUpEmbeds: Array<EmbedBuilder>}>} Object containing the final character state and any generated notification embeds.
 */
async function applyActionRewards(
  userId,
  initialCharacter,
  skillName,
  totalSkillExpGained,
  totalPetExpGained,
  totalItemsGainedMap,
  totalCoinsGained = 0,
  mobKey = undefined,
  killedCount = undefined,
  accessoriesToAdd = [],
  petsToAdd = [],
  totalItemsConsumedMap = {}
) {
  // CRITICAL FIX: Always fetch fresh character data to prevent overwriting concurrent changes
  // This ensures that any changes made during the action (like admin /give commands) are preserved
  //console.log(`[applyActionRewards] Fetching fresh character data for ${userId} to prevent overwriting concurrent changes`);
  const freshCharacterData = await getPlayerData(userId);
  // cache items config to avoid repeated lookups in loops
  const allItems = configManager.getAllItems();

  if (!freshCharacterData) {
    throw new Error(
      `[applyActionRewards] Failed to fetch fresh character data for ${userId} - no fallback support`
    );
  }

  // Use fresh data as the base for updates, but preserve any action-specific state that might be in initialCharacter
  // Copy over any action-specific data that wouldn't be in the database
  const characterDataForUpdates = {
    ...freshCharacterData,
    // Preserve any temporary action state if it exists in initialCharacter
    ...(initialCharacter.actionSpecificData || {}),
  };
  //console.log(`[applyActionRewards] Using fresh character data for ${userId} - coins: ${freshCharacterData.coins || 0}`);

  // Mob kill tracking
  if (mobKey && killedCount) {
    if (!characterDataForUpdates.mobKills) {
      characterDataForUpdates.mobKills = {};
    }
    if (!characterDataForUpdates.mobKills[mobKey]) {
      characterDataForUpdates.mobKills[mobKey] = 0;
    }
    characterDataForUpdates.mobKills[mobKey] += killedCount;
  }

  let updatedSkillData;
  let collectionNotifications = [];
  let allLevelUpEmbeds = [];

  // Separate items by storage type
  const itemsToChange = [];
  const equipmentToAdd = [];
  const accessoriesToAddForInventory = [];
  const petsToAddForInventory = [];

  // Process items gained
  for (const [itemKey, amount] of Object.entries(totalItemsGainedMap)) {
    if (amount > 0) {
      const itemData = allItems[itemKey];
      if (itemData && itemData.unique) {
        if (itemData.type === "ACCESSORY") {
          // Accessories go to accessories storage
          for (let i = 0; i < amount; i++) {
            accessoriesToAddForInventory.push({ itemKey: itemKey });
          }
        } else if (itemData.type === "PET") {
          // Pets go to pets storage
          for (let i = 0; i < amount; i++) {
            petsToAddForInventory.push({ itemKey: itemKey });
          }
        } else {
          // Other unique items (armor) go to equipment storage
          for (let i = 0; i < amount; i++) {
            equipmentToAdd.push({ itemKey: itemKey });
          }
        }
      } else {
        // Stackable items go to regular inventory
        itemsToChange.push({ itemKey: itemKey, amount: amount });
      }
    }
  }

  // Add accessories from the accessoriesToAdd parameter
  if (Array.isArray(accessoriesToAdd)) {
    accessoriesToAddForInventory.push(...accessoriesToAdd);
  }

  // Add pets from the petsToAdd parameter
  if (Array.isArray(petsToAdd)) {
    petsToAddForInventory.push(...petsToAdd);
  }

  // Process items consumed (subtract from inventory)
  for (const [itemKey, amount] of Object.entries(totalItemsConsumedMap)) {
    if (amount > 0) {
      // Add consumed items as negative amounts to itemsToChange
      itemsToChange.push({ itemKey: itemKey, amount: -amount });
    }
  }

  let accumulatedCoinsToAdd = totalCoinsGained; // Start with initial coins from the action

  try {
    // Apply Pet EXP (modifies characterDataForUpdates in memory)
    let petLeveledUp = false;
    if (characterDataForUpdates.active_pet_id && totalPetExpGained > 0) {
      const petResult = await addPetExp(
        characterDataForUpdates,
        characterDataForUpdates.active_pet_id,
        totalPetExpGained
      );
      if (petResult.levelUpEmbeds) {
        allLevelUpEmbeds = allLevelUpEmbeds.concat(petResult.levelUpEmbeds);
      }
      petLeveledUp = petResult.petLeveledUp || false;
    }

    // Apply Primary Skill EXP (modifies characterDataForUpdates in memory)
    if (totalSkillExpGained > 0) {
      const primarySkillResult = await addSkillExp(
        characterDataForUpdates,
        skillName,
        totalSkillExpGained,
        null, // no interaction - we'll batch notifications later
        true // defer notifications
      );
      updatedSkillData = { exp: characterDataForUpdates.skills[skillName].exp }; // Get updated exp from character
      if (
        primarySkillResult.levelUpEmbeds &&
        primarySkillResult.levelUpEmbeds.length > 0
      ) {
        allLevelUpEmbeds = allLevelUpEmbeds.concat(
          primarySkillResult.levelUpEmbeds
        );
        console.log(
          `[applyActionRewards] Added ${primarySkillResult.levelUpEmbeds.length} level up embeds for ${skillName}`
        );
      }
      if (primarySkillResult.totalLevelUpCoinReward > 0) {
        accumulatedCoinsToAdd += primarySkillResult.totalLevelUpCoinReward; // Add level-up coins
      }
    } else {
      // If no EXP gained, construct current skill data from characterDataForUpdates
      const currentSkill = characterDataForUpdates?.skills?.[skillName];
      const totalExp = currentSkill?.exp || 0;
      updatedSkillData = {
        exp: totalExp,
      };
    }

    // Apply Taming EXP (modifies characterDataForUpdates in memory)
    if (totalPetExpGained > 0) {
      // Taming EXP is derived from pet EXP
      const tamingXpToAdd = getTamingExpFromPetExp(totalPetExpGained);
      if (tamingXpToAdd > 0) {
        try {
          const tamingSkillUpdate = await addSkillExp(
            characterDataForUpdates,
            "taming",
            tamingXpToAdd,
            null, // no interaction - we'll batch notifications later
            true // defer notifications
          );
          if (
            tamingSkillUpdate?.levelUpEmbeds &&
            tamingSkillUpdate.levelUpEmbeds.length > 0
          ) {
            allLevelUpEmbeds = allLevelUpEmbeds.concat(
              tamingSkillUpdate.levelUpEmbeds
            );
          }
          if (tamingSkillUpdate?.totalLevelUpCoinReward > 0) {
            accumulatedCoinsToAdd += tamingSkillUpdate.totalLevelUpCoinReward; // Add level-up coins
          }
        } catch (tamingExpError) {
          console.error(
            `[applyActionRewards] CRITICAL ERROR during addSkillExp for TAMING! User: ${userId}`,
            tamingExpError
          );
        }
      }
    }

    // Apply Collection progress (modifies characterDataForUpdates in memory)
    // Allow conversion for enchanted items obtained as drops (e.g., from fishing, combat)
    for (const [itemKey, itemAmount] of Object.entries(totalItemsGainedMap)) {
      if (itemAmount > 0) {
        const collectionResult = await addCollectionProgress(
          userId,
          itemKey,
          itemAmount,
          characterDataForUpdates,
          { allowConversion: true }
        );
        if (collectionResult.character) {
          // addCollectionProgress might return a new object or modify in place.
          // Assuming it modifies characterDataForUpdates in place based on typical pattern.
          // If it returns a new object, characterDataForUpdates should be reassigned:
          // characterDataForUpdates = collectionResult.character;
        } else {
          console.error(
            `[applyActionRewards] addCollectionProgress returned null character for ${itemKey}`
          );
        }
        if (collectionResult.notifications) {
          collectionNotifications = collectionNotifications.concat(
            collectionResult.notifications
          );
        }
      }
    }

    // --- Update items immediately, but delay coin rewards until after level-up messages ---
    if (
      itemsToChange.length > 0 ||
      equipmentToAdd.length > 0 ||
      accessoriesToAddForInventory.length > 0 ||
      petsToAddForInventory.length > 0
    ) {
      // Added robust retry logic to handle transient SQLITE_BUSY / SQLITE_LOCKED errors
      const maxRetries = 3;
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          await updateInventoryAtomically(
            userId,
            0, // No coin change at this stage
            itemsToChange,
            equipmentToAdd,
            [], // equipmentIdsToRemove
            0, // bankCoinsToAdd
            [], // equipmentDataUpdates
            null, // islandJsonString
            null, // collectionsJsonString
            false, // useExistingTransaction
            accessoriesToAddForInventory // Add accessories to player storage
          ); // Success – break out of the retry loop
          break;
        } catch (error) {
          const isRetryable =
            error?.message?.includes("database is locked") ||
            error?.message?.includes("no transaction is active") ||
            error?.code === "SQLITE_BUSY" ||
            error?.code === "SQLITE_LOCKED";

          console.warn(
            `[applyActionRewards] Attempt ${attempt}/${maxRetries} failed for ${userId}: ${error.message}`
          );

          if (!isRetryable || attempt === maxRetries) {
            // Non-retryable or max retries reached – re-throw so caller can handle
            console.error(
              `[applyActionRewards][ERROR] Failed to update items for ${userId} after ${attempt} attempt(s).`,
              error
            );
            throw error;
          }

          // Exponential back-off (100 ms, 200 ms, 400 ms)
          const delay = Math.min(100 * Math.pow(2, attempt - 1), 1000);
          await new Promise((res) => setTimeout(res, delay));
        }
      }
    }

    // --- Handle pet drops separately (similar to crafting system) ---
    if (petsToAddForInventory.length > 0) {
      try {
        //console.log(`[applyActionRewards] Adding ${petsToAddForInventory.length} pet instance(s) from drops.`);

        // Ensure pets array exists
        if (
          !characterDataForUpdates.pets ||
          !Array.isArray(characterDataForUpdates.pets)
        ) {
          characterDataForUpdates.pets = [];
        }

        // Create pet instances for each dropped pet
        const { v4: uuidv4 } = require("uuid");
        for (const petDrop of petsToAddForInventory) {
          const petData = configManager.getAllItems()[petDrop.itemKey];
          if (petData && petData.type === "PET") {
            const petInstance = {
              id: uuidv4(),
              petKey: petDrop.itemKey,
              rarity: petData.defaultRarity || "COMMON",
              level: 1,
              totalExp: 0,
              obtainedTimestamp: Date.now(),
            };
            characterDataForUpdates.pets.push(petInstance);
            console.log(
              `[applyActionRewards] Added pet: ${petDrop.itemKey} (${petInstance.rarity})`
            );
          }
        }
      } catch (petAddError) {
        //console.error(`[applyActionRewards] Error adding pets for user ${userId}:`, petAddError);
      }
    }

    // Removed verbose coin logging for routine rewards

    // Store coin rewards to be applied after level-up messages are sent
    const pendingCoinRewards = accumulatedCoinsToAdd;

    // --- Save all OTHER changes made to characterDataForUpdates (EXP, pets, collections etc.) ---
    // These were modified in-memory on characterDataForUpdates by addPetExp, addSkillExp, addCollectionProgress.
    // updateInventoryAtomically only saves coins/items to its own tables.
    // The player object's other fields (skills, pets, collections etc.) need to be saved.
    // Use atomic update for skill-related data including collections to prevent race conditions
    await updatePlayerSkillDataAtomically(userId, characterDataForUpdates);

    // --- Refetch the player data to ensure it's the absolute latest state from DB for returning ---
    // This includes coin/item changes from updateInventoryAtomically AND other changes from savePlayerData.
    const fullyUpdatedCharacterData = await getPlayerData(userId);

    if (!fullyUpdatedCharacterData) {
      throw new Error(
        `[applyActionRewards] Failed to fetch updated player data for user ${userId} after all updates - no fallback support`
      );
    }

    // Return the pending coin rewards so they can be passed to sendActionFollowups
    // This ensures coins are only given after the level-up message is displayed
    // Only log level ups, not routine rewards
    if (allLevelUpEmbeds.length > 0) {
      console.log(
        `[applyActionRewards] Level up! User ${userId} gained ${allLevelUpEmbeds.length} level(s) in ${skillName}`
      );
    }

    return {
      character: fullyUpdatedCharacterData,
      updatedSkillData,
      collectionNotifications,
      allLevelUpEmbeds,
      pendingCoinRewards, // Include the pending coin rewards in the return value
      petLeveledUp, // Include pet level up flag
    };
  } catch (error) {
    console.error(
      `[applyActionRewards] Error applying rewards for ${userId}, skill ${skillName}:`,
      error
    );
    // Attempt to return the initial state on error, plus current skill data
    const currentSkill = initialCharacter?.skills?.[skillName]; // Use initialCharacter for fallback here
    updatedSkillData = {
      exp: currentSkill?.exp || 0,
    };
    return {
      character: initialCharacter,
      updatedSkillData,
      collectionNotifications,
      allLevelUpEmbeds,
      petLeveledUp: false, // No pet level up on error
    };
  }
}

/**
 * Sends follow-up messages for level-ups and collection unlocks.
 * @param {Interaction} interaction - The originating Discord interaction.
 * @param {Array<EmbedBuilder>|null} allLevelUpEmbeds - Combined embeds for skill, pet, and taming level ups.
 * @param {Array<EmbedBuilder>} collectionNotifications - Embeds for collection unlocks.
 * @param {number} [pendingCoinRewards=0] - Coin rewards to be given after messages are sent.
 * @param {boolean} [petLeveledUp=false] - Whether a pet leveled up during this action.
 * @returns {Promise<void>}
 */
async function sendActionFollowups(
  interaction,
  allLevelUpEmbeds,
  collectionNotifications,
  pendingCoinRewards = 0
) {
  const userId = interaction.user.id;
  const followUpMessages = [];

  // Use the combined list directly
  if (allLevelUpEmbeds && allLevelUpEmbeds.length > 0) {
    followUpMessages.push(...allLevelUpEmbeds);
  }
  if (collectionNotifications && collectionNotifications.length > 0) {
    followUpMessages.push(...collectionNotifications);
  }

  // Only log when there are actually messages to send
  if (followUpMessages.length > 0) {
    // Messages to send
  }

  try {
    // Validate interaction has channel access for sending level up notifications
    if (
      !interaction?.channel ||
      typeof interaction.channel.send !== "function"
    ) {
      console.error(
        `Invalid interaction or missing channel access for user ${userId}`
      );
      throw new Error("Invalid interaction or missing channel access");
    }

    // Send level up notifications separately with delays to avoid interaction timeout issues
    if (followUpMessages.length > 0) {
      try {
        console.log(
          `[sendActionFollowups] Sending ${followUpMessages.length} notification(s) with delays for user ${userId}`
        );
        await sendEmbedsWithDelay(interaction.channel, followUpMessages, 500);
      } catch (channelError) {
        console.error(
          `Error sending level up messages for user ${userId}:`,
          channelError
        );
        throw channelError; // Re-throw to be caught by outer try-catch
      }
    }

    // After sending level-up messages, apply coin rewards if any
    if (pendingCoinRewards > 0) {
      try {
        await updateInventoryAtomically(userId, pendingCoinRewards, []);
      } catch (coinError) {
        console.error(
          `Failed to award ${pendingCoinRewards} coins to ${userId}:`,
          coinError
        );

        // If coin reward fails, log it but don't crash - continue silently
      }
    }
  } catch (e) {
    console.error(
      `Failed to send level up messages for user ${userId}, interaction ${interaction?.id}:`,
      e
    );
    console.error(
      `Interaction state - replied: ${interaction?.replied}, deferred: ${interaction?.deferred}, user: ${userId}`
    );

    // Log the missed notifications for manual review
    if (followUpMessages.length > 0) {
      console.log(`MISSED LEVEL UP NOTIFICATION for user ${userId}:`);
      followUpMessages.forEach((embed, index) => {
        const embedData = embed.toJSON();
        console.log(
          `Embed ${index + 1}: ${embedData.title} - ${embedData.description}`
        );
      });
    }

    // If we couldn't send messages, we should still try to give the coins
    if (pendingCoinRewards > 0) {
      try {
        console.log(
          `[Recovery] Attempting to award ${pendingCoinRewards} coins to ${userId} after message failure`
        );
        await updateInventoryAtomically(userId, pendingCoinRewards, []);
        console.log(
          `[Recovery] Successfully awarded coins to ${userId} after message failure`
        );
      } catch (recoveryError) {
        console.error(
          `[Recovery] Failed to award coins to ${userId} after message failure:`,
          recoveryError
        );
      }
    }
  }
}

/**
 * Handles the core validation and dispatching logic for starting a skill action.
 * Assumes player data, resource key (if applicable), and amount have been determined.
 * @param {Interaction} interaction - The Discord interaction object.
 * @param {object} character - The player's character data object.
 * @param {object} skillConfig - Skill-specific configuration.
 * @param {string} skillConfig.skillName - The name of the skill (e.g., 'farming').
 * @param {string|null} skillConfig.resourceInputName - The interaction option name ('crop', 'ore', 'wood', null).
 * @param {string} skillConfig.regionActivity - The activity name to check in the region (e.g., 'farming').
 * @param {string|null} skillConfig.regionResourceKey - The key for available resources in region data (e.g., 'availableCrops', null).
 * @param {Set} skillConfig.busySet - The Set tracking busy players for this skill.
 * @param {Function} skillConfig.handleActionFunction - The single handler function.
 * @param {string} resourceKey - The specific item key for the resource (e.g., 'WHEAT', 'COBBLESTONE', null).
 * @param {number} amount - The number of times to perform the action.
 * @param {boolean} isAgain - Whether the action was triggered by an 'Again' button.
 */
async function initiateSkillAction(
  interaction,
  character,
  skillConfig,
  resourceKey,
  amount,
  isAgain
) {
  const userId = character.discordId;
  const {
    skillName,

    handleActionFunction, // ADD: The single handler function
  } = skillConfig;

  // --- Atomic busy lock using setActivity ---
  const existingActivity = setActivity(userId, skillName);
  if (existingActivity) {
    let busyMsg;
    if (isConflict(existingActivity, skillName)) {
      busyMsg = `You cannot start ${skillName} while you are ${existingActivity}!`;
    } else {
      busyMsg = `You are already busy (${existingActivity})!`;
    }
    if (isAgain)
      await interaction
        .followUp({ content: busyMsg, flags: [MessageFlags.Ephemeral] })
        .catch((e) =>
          console.error(
            `[${skillName.toUpperCase()}] Error sending busy followUp:`,
            e
          )
        );
    else
      await interaction
        .editReply({ content: busyMsg, components: [], embeds: [] })
        .catch((e) =>
          console.error(
            `[${skillName.toUpperCase()}] Error sending busy editReply:`,
            e
          )
        );
    return;
  }

  // Check region permissions if applicable
  // (Region checks removed for now)

  // --- Action Execution ---
  try {
    // Call the single provided handler function, passing the amount
    await handleActionFunction(
      interaction,
      character,
      resourceKey,
      amount,
      isAgain
    );
  } catch (error) {
    console.error(
      `[initiateSkillAction][${skillName}] Error during action execution for ${userId}:`,
      error
    );
    try {
      const errorMsg = `An error occurred while performing the ${skillName} action. Please try again.`;
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({ content: errorMsg, flags: [MessageFlags.Ephemeral] })
          .catch(() => {});
      } else {
        // Use channel.send() for errors during long-running actions to avoid interaction timeout
        const channel = interaction.channel;
        if (channel) {
          await channel
            .send({
              content: `<@${interaction.user.id}> ${errorMsg}`,
            })
            .catch(() => {});
        }
      }
    } catch (e) {
      console.error(
        `[initiateSkillAction][${skillName}] Failed to send error message to user ${userId}:`,
        e
      );
    }
  } finally {
    clearActivity(userId);
  }
}

/**
 * Handles the initial setup for a new multi-action skill command.
 * Sends the initial message, starts tracking in DB, adds the stop button.
 * @param {import('discord.js').CommandInteraction} interaction - The interaction object.
 * @param {string} skillName - Lowercase skill name (e.g., 'farming').
 * @param {string} resourceKey - The specific resource being gathered.
 * @param {number} amount - The total number of actions.
 * @param {object} initialEmbedOptions - Options for the initial embed.
 * @param {string} initialEmbedOptions.color - Hex color code.
 * @param {string} initialEmbedOptions.emoji - Emoji for the title.
 * @param {string} initialEmbedOptions.actionName - Name for title (e.g., 'Farming', 'Mining').
 * @param {object} dbParams - Parameters specific to this skill for the DB action record (e.g., { crop: resourceKey }).
 * @returns {Promise<{messageId: string, actionId: number}>} - The sent message ID and the DB action ID.
 * @throws Will re-throw errors related to message sending or DB interaction if they occur.
 */
async function setupNewMultiAction(
  interaction,
  skillName,
  resourceKey,
  amount,
  initialEmbedOptions,
  dbParams,
  character = null
) {
  const userId = interaction.user.id;
  const channelId = interaction.channel.id;
  let messageId;
  let actionId;

  let resourceDisplayName;
  if (resourceKey) {
    const resourceData = configManager.getItem(resourceKey);
    resourceDisplayName = resourceData?.name || resourceKey;
  } else {
    // For actions without a specific resource, don't use a resource name
    resourceDisplayName = null;
  }

  const titleEmoji =
    initialEmbedOptions.emoji || skillEmojis[skillName] || "⚙️";

  // --- FIX: Construct title - Show first cycle immediately ---
  let titleText;
  if (resourceDisplayName) {
    // For actions with a specific resource - show first cycle progress
    titleText = `${titleEmoji} ${initialEmbedOptions.actionName} ${resourceDisplayName}... (1/${amount})`;
  } else {
    // For actions without a specific resource (like Fishing) - show first cycle progress
    titleText = `${titleEmoji} ${initialEmbedOptions.actionName}... (1/${amount})`;
  }
  // --- End Title Fix ---

  const initialEmbed = new EmbedBuilder()
    .setColor(initialEmbedOptions.color)
    .setTitle(titleText); // Use the correctly constructed title

  // create initial animation description with stop button ready
  // we'll send the message with both animation and button in one go to avoid multiple edits
  let initialAnimationDescription = "";
  if (skillName === "farming") {
    const configManager = require("../utils/configManager");
    const resourceData = configManager.getAllItems()[resourceKey];
    const farmerEmoji = "👨‍🌾";
    const displayEmoji = resourceData?.emoji || "❓";
    const trackLength = 15;

    // start animation at position 0 immediately
    const movingPart = `${farmerEmoji}${" ".repeat(trackLength)}`;
    initialAnimationDescription = `\`${movingPart}\` ${displayEmoji}`;
  } else if (skillName === "mining") {
    const configManager = require("../utils/configManager");
    const resourceData = configManager.getAllItems()[resourceKey];
    const pickaxeEmoji = "⛏️";
    const displayEmoji = resourceData?.emoji || "🪨";
    const trackLength = 15;

    // start animation at position 0 immediately
    const movingPart = `${pickaxeEmoji}${" ".repeat(trackLength)}`;
    initialAnimationDescription = `\`${movingPart}\` ${displayEmoji}`;
  } else if (skillName === "foraging") {
    const configManager = require("../utils/configManager");
    const resourceData = configManager.getAllItems()[resourceKey];
    const axeEmoji = "🪓";
    const displayEmoji = resourceData?.emoji || "🌳";
    const trackLength = 15;

    // start animation at position 0 immediately
    const movingPart = `${axeEmoji}${" ".repeat(trackLength)}`;
    initialAnimationDescription = `\`${movingPart}\` ${displayEmoji}`;
  } else if (skillName === "alchemy") {
    const configManager = require("../utils/configManager");
    const resourceData = configManager.getAllItems()[resourceKey];
    const cauldronEmoji = "🧪";
    const displayEmoji = resourceData?.emoji || "⚗️";
    const trackLength = 15;

    // start animation at position 0 immediately
    const movingPart = `${cauldronEmoji}${" ".repeat(trackLength)}`;
    initialAnimationDescription = `\`${movingPart}\` ${displayEmoji}`;
  } else if (skillName === "fishing") {
    const rodEmoji = "🎣";
    const fishEmoji = "🐟";
    const trackLength = 15;

    // get the player's preferred bait to show correct status from the start
    let baitStatus = "❌ **No bait** used";
    try {
      const { enhancedBaitManager } = require("./enhancedBaitManager");
      const preferredBait = await enhancedBaitManager.getPreferredBait(userId);

      if (preferredBait.baitKey) {
        const configManager = require("./configManager");
        const allItems = configManager.getAllItems();
        const baitData = allItems[preferredBait.baitKey];
        const baitEmoji = baitData?.emoji || "❓";
        const baitName = baitData?.name || preferredBait.baitKey;
        // use "used" instead of "will be used" to match what animation shows
        baitStatus = `${baitEmoji} **${baitName}** used`;
      }
    } catch (error) {
      console.error(
        "[setupNewMultiAction] Error getting preferred bait:",
        error
      );
      // fallback to default "no bait" message
    }

    // start animation at position 0 immediately - fish moves from left to right towards rod
    const movingPart = `${fishEmoji}${" ".repeat(trackLength)}`;
    initialAnimationDescription = `\`${movingPart}\` ${rodEmoji}\n\n${baitStatus}`;
  }

  // set the initial animation description if we have one
  if (initialAnimationDescription) {
    initialEmbed.setDescription(initialAnimationDescription);
  }

  // Add crop milestone footer for farming in Garden
  if (
    skillName === "farming" &&
    character &&
    character.current_region === "garden" &&
    resourceKey
  ) {
    try {
      const { formatMilestoneProgress } = require("./gardenSystem");

      const progressText = formatMilestoneProgress(character, resourceKey);

      initialEmbed.setFooter({
        text: `${progressText}`,
      });
    } catch (error) {
      console.error(
        "[setupNewMultiAction] Error adding crop milestone footer:",
        error
      );
    }
  }

  // Ensure the interaction is deferred or replied before followUp
  if (!interaction.replied && !interaction.deferred) {
    await interaction.deferReply();
  }

  let initialMessage;
  // actionId is already declared at function scope, no need to redeclare here

  try {
    // Check if actionId is already provided (from main bot delegation)
    if (interaction.actionId) {
      // This is a delegated action - use the provided actionId
      actionId = interaction.actionId;
      console.log(
        `[setupNewMultiAction] Using delegated actionId: ${actionId}`
      );
    } else {
      // This is a direct slash command - create new action
      const workerId =
        interaction.workerId || interaction.assignedWorkerId || null;

      // SAFETY: Warn if workerId is null (shouldn't happen in normal operation)
      if (!workerId) {
        console.warn(
          `[setupNewMultiAction] Creating action with NULL workerId for skill ${skillName}. This may indicate a delegation issue.`
        );
        console.warn(`[setupNewMultiAction] Interaction details:`, {
          hasWorkerId: !!interaction.workerId,
          hasAssignedWorkerId: !!interaction.assignedWorkerId,
          isWorkerBot: !!process.env.WORKER_BOT_ID,
          isActionDelegation: !!interaction.actionId,
          userId: userId,
        });
      }

      actionId = await startAction(
        userId,
        skillName,
        resourceKey,
        dbParams,
        amount,
        channelId,
        null, // messageId will be updated after we get it
        workerId // pass workerId for DB persistence
      );
      console.log(`[setupNewMultiAction] Created new actionId: ${actionId}`);
    }

    // create stop button with actionId immediately
    // (Button creation is now handled inside the animation container)

    // send message with embed + components
    let embedForAnim, buttons;
    if (skillName === "fishing") {
      // Get current bait info for fishing
      let currentBaitInfo;
      try {
        const { getCurrentBaitInfo } = require("./fishingButtons");
        currentBaitInfo = await getCurrentBaitInfo(userId);
      } catch (error) {
        // Fallback if bait info fails
        currentBaitInfo = { emoji: "🎣", name: "No Bait" };
      }
      const result = buildFishingUIFromEmbed(
        initialEmbed,
        actionId,
        userId,
        initialEmbedOptions.actionName,
        currentBaitInfo
      );
      embedForAnim = result.embed;
      buttons = result.buttons;
    } else {
      const result = buildAnimationUIFromEmbed(
        initialEmbed,
        actionId,
        userId,
        initialEmbedOptions.actionName
      );
      embedForAnim = result.embed;
      buttons = result.buttons;
    }

    // Create action row for buttons
    const actionRow = new ActionRowBuilder().addComponents(...buttons);

    initialMessage = await interaction.followUp({
      embeds: [embedForAnim],
      components: [actionRow],
      fetchReply: true,
    });
    messageId = initialMessage.id;

    console.log(
      `[setupNewMultiAction] Initial message created for action ${actionId}, messageId: ${messageId}`
    );

    // update the action with the actual messageId now that we have it
    await updateActionMessageId(actionId, messageId);

    // initialize message timestamp for message cycling
    await updateActionMessageTimestamp(actionId, Date.now());

    // create AbortController for instant stop functionality
    createAbortController(actionId);
    console.log(`[InstantStop] Created AbortController for action ${actionId}`);
  } catch (error) {
    const errorCode = `[${skillName.toUpperCase()}-MUL-DB-001]`;
    console.error(
      `[${initialEmbedOptions.actionName}][Multi] Failed to start action or send message for user ${userId}:`,
      error
    );

    // cleanup action if it was created but message failed
    if (actionId) {
      try {
        await completeAction(actionId);
        console.log(
          `[${initialEmbedOptions.actionName}][Multi] Cleaned up failed action ${actionId}`
        );
      } catch (cleanupErr) {
        console.error(
          `[${initialEmbedOptions.actionName}][Multi] Failed to cleanup action ${actionId}:`,
          cleanupErr
        );
      }
    }

    throw error; // propagate error
  }

  if (!actionId) {
    // This case should ideally be caught by the error handling above, but as a safeguard:
    console.error(
      `[${initialEmbedOptions.actionName}][Multi-Start] Action ID is unexpectedly null/undefined after DB interaction. Cannot proceed.`
    );
    throw new Error("Failed to obtain a valid action ID from the database.");
  }

  // Note: Fishing buttons are now created in the initial setup above
  // No need for additional button setup for any skill

  return { messageId, actionId };
}

/**
 * Generic handler for executing single or multi-part skill actions.
 *
 * @param {import('discord.js').CommandInteraction} interaction - The interaction object.
 * @param {object} character - The player's character object.
 * @param {string|null} resourceKey - The specific resource being gathered, or null if not applicable.
 * @param {number} amount - The number of times to perform the action.
 * @param {boolean} isAgain - Whether this was triggered by an 'again' mechanism.
 * @param {object} skillConfig - Skill-specific configuration.
 * @param {string} skillConfig.skillName - Lowercase skill name (e.g., 'farming').
 * @param {string} skillConfig.actionName - Capitalized action name (e.g., 'Farming').
 * @param {string} skillConfig.resourceNameSingular - Singular name (e.g., 'crop', 'ore').
 * @param {string|null} skillConfig.resourceKeyParamName - Key used for resource in DB params (e.g., 'crop', null).
 * @param {string} skillConfig.emoji - Default emoji for the skill.
 * @param {{singleAction: string, multiActionLoop: string, multiActionStopped: string, multiActionComplete: string}} skillConfig.colors - Embed colors.
 * @param {(character: object) => number} skillConfig.calculateTimeFn - Function to get action time.
 * @param {(message: import('discord.js').Message, embed: EmbedBuilder, time: number, stopMap: Set<string> | null, actionId: number | null, ...args: any[]) => Promise<void>} skillConfig.animationFn - Animation function.
 * @param {(character: object, resourceKey: string|null) => { items: Array<{itemKey: string, amount: number}>, exp: number }} skillConfig.goFn - Function to get cycle results.
 * @param {(userId: string, characterForAction: object) => Promise<{character: object, baitUsedKey: string|false}>} [skillConfig.consumeResourceFn] - Optional function to consume a resource per cycle (like bait).
 */
async function handleSkillActionExecution(
  interaction,
  character,
  resourceKey,
  amount,
  isAgain,
  skillConfig,
  wasMax = false
  // auxData = null // Unused tracking variable
) {
  const currentActionId = interaction.actionId || "N/A"; // Get actionId for logging
  let startTime;
  const isResumption = interaction.isResumption || false;
  const skillNameForLog = skillConfig?.skillName || "unknown_skill";
  // Use persisted start_timestamp for resumed actions
  if (isResumption && interaction.actionId) {
    try {
      const actionRecord = await getActionById(interaction.actionId);
      if (actionRecord && actionRecord.start_timestamp) {
        startTime = actionRecord.start_timestamp;
      } else {
        startTime = Date.now();
      }
    } catch (e) {
      startTime = Date.now();
    }
  } else {
    startTime = Date.now();
  }

  if (!character) {
    console.error(
      `[skillActionUtils][${currentActionId}][${skillNameForLog}] CRITICAL: Character data is null. Resumption: ${isResumption}.`
    );
    if (isResumption) {
      console.log(
        `[skillActionUtils][${currentActionId}][${skillNameForLog}] Resumption: Character data is null. Resumption: ${isResumption}.`
      );
    }
    if (currentActionId && currentActionId !== "N/A") {
      try {
        await completeAction(currentActionId);
        if (interaction.user?.id) {
          await clearActivity(interaction.user.id); // Clear activity from active_actions table
        }
        console.log(
          `[handleSkillActionExecution] Cleaned up stuck action in DB for actionId: ${currentActionId}`
        );
      } catch (cleanupErr) {
        console.error(
          `[handleSkillActionExecution] Failed to clean up stuck action in DB for actionId: ${currentActionId}:`,
          cleanupErr
        );
      }
    }
    throw new Error(
      "Character data missing for action resumption. Action has been cleaned up."
    );
  }
  // Refresh character stats before action execution to ensure pet stats are current
  // Note: On workers, stats recalculation is handled by main bot via API
  const { recalculateAndSaveStats } = isWorkerBot
    ? { recalculateAndSaveStats: async () => {} } // Stub for workers
    : playerDataModule;
  if (!isWorkerBot) {
    await recalculateAndSaveStats(character.discordId, character);
  }
  character = await getPlayerData(character.discordId);

  // Initialize a flag on the interaction object to track if a combat ping has been sent for this specific interaction
  // This helps prevent double pings if this handler is somehow re-entered or part of it re-evaluated for combat.
  if (skillConfig && skillConfig.skillName === "combat") {
    interaction.combatPingSent = interaction.combatPingSent || false;
  }

  const userId = character.discordId;
  const {
    skillName,
    actionName,
    /*resourceNameSingular,*/ resourceKeyParamName,
    emoji,
    colors,
    calculateTimeFn,
    animationFn,
    goFn /*, consumeResourceFn*/, // consumeResourceFn might become unused
  } = skillConfig;
  let actionId = null;

  const resourceData = resourceKey ? configManager.getItem(resourceKey) : null;
  const resourceDisplayName =
    resourceData?.name || (resourceKey ? resourceKey : actionName);

  const resultSent = false;
  try {
    if (skillName === "combat") {
      // --- Use the polished multi-combat flow from commands/combat.js ---
      // Import handleCombatAction from combat.js and call it with amount > 1
      const { handleCombatAction } = require("../commands/combat");
      await handleCombatAction(
        interaction,
        resourceKey,
        amount, // Pass the requested amount for multi-combat
        isAgain,
        null, // originalTotalAmount
        character,
        wasMax
      );
      return;
    } else {
      // --- Multi Action Logic ---
      // OPTIMIZATION: Use selective deep copy to prevent object corruption while maintaining performance
      // Deep copy all properties that get modified during action loops, shallow copy the rest
      let currentCharacterLoopState = {
        ...character,
        stats: character.stats ? { ...character.stats } : {},
        inventory: {
          ...character.inventory,
          items: character.inventory?.items
            ? { ...character.inventory.items }
            : {},
          equipment: character.inventory?.equipment
            ? [...character.inventory.equipment]
            : [],
        },
        skills: character.skills ? { ...character.skills } : {},
        pets: character.pets ? [...character.pets] : [],
        equipment: character.equipment ? { ...character.equipment } : null,
        coins: character.coins || 0,
        bank: character.bank || 0,
        mobKills: character.mobKills ? { ...character.mobKills } : {},
        // Deep copy health-related properties that get modified during combat
        current_health: character.current_health,
        maxHealth: character.maxHealth,
      };

      // Calculate all relevant stats once at the start of the activity (or retrieve from resumption)
      const {
        calculateAllStats,
        calculateTotalStat,
      } = require("./statCalculations");
      let preCalculatedStats = {
        // Core combat stats for sea creatures and combat (required for fishing sea creatures)
        allStats: calculateAllStats(currentCharacterLoopState),
        // Skill-specific stats
        baseFishingSpeed: calculateTotalStat(
          currentCharacterLoopState.stats.FISHING_SPEED || {}
        ), // Base speed without bait
        farmingFortune: calculateTotalStat(
          currentCharacterLoopState.stats.FARMING_FORTUNE || {}
        ),
        miningFortune: calculateTotalStat(
          currentCharacterLoopState.stats.MINING_FORTUNE || {}
        ),
        foragingFortune: calculateTotalStat(
          currentCharacterLoopState.stats.FORAGING_FORTUNE || {}
        ),
        farmingWisdom: calculateTotalStat(
          currentCharacterLoopState.stats.FARMING_WISDOM || {}
        ),
        farmingSweep: (() => {
          // Hybrid DR: first 5 points at full efficiency, then power-law for additional
          const rawFarmingSweep = calculateTotalStat(
            currentCharacterLoopState.stats.FARMING_SWEEP || {}
          );
          const exponent = 0.75; // sub-linear exponent for extra points
          if (rawFarmingSweep <= 5) {
            return rawFarmingSweep;
          } else {
            return 5 + Math.floor(Math.pow(rawFarmingSweep - 5, exponent));
          }
        })(),
        miningWisdom: calculateTotalStat(
          currentCharacterLoopState.stats.MINING_WISDOM || {}
        ),
        miningSweep: (() => {
          // Hybrid DR: first 2 points at full efficiency, then power-law for additional
          const rawMiningSweep = calculateTotalStat(
            currentCharacterLoopState.stats.MINING_SWEEP || {}
          );
          const exponent = 0.75;
          if (rawMiningSweep <= 2) {
            return rawMiningSweep;
          } else {
            return 2 + Math.floor(Math.pow(rawMiningSweep - 2, exponent));
          }
        })(),
        foragingSweep: calculateTotalStat(
          currentCharacterLoopState.stats.FORAGING_SWEEP || {}
        ),
        fishingWisdom: calculateTotalStat(
          currentCharacterLoopState.stats.FISHING_WISDOM || {}
        ),
        seaCreatureChance: calculateTotalStat(
          currentCharacterLoopState.stats.SEA_CREATURE_CHANCE || {}
        ),
        treasureChance: calculateTotalStat(
          currentCharacterLoopState.stats.TREASURE_CHANCE || {}
        ),
        miningSpeed: calculateTotalStat(
          currentCharacterLoopState.stats.MINING_SPEED || {}
        ),
        farmingSpeed: calculateTotalStat(
          currentCharacterLoopState.stats.FARMING_SPEED || {}
        ),
        foragingSpeed: calculateTotalStat(
          currentCharacterLoopState.stats.FORAGING_SPEED || {}
        ),
      };

      // Pre-calculated stats for consistent performance during the activity

      const itemsGained = {}; // Keep this - used to accumulate items

      // For fishing: track all sea creatures defeated across cycles
      const seaCreaturesDefeatedMap = {};
      let totalSeaCreatures = 0;

      // For mining: track all mobs defeated across cycles (e.g., Endermites)
      const defeatedMobsMap = {};
      let totalDefeatedMobs = 0;

      // For fishing: track pets gained across cycles
      const petsGained = [];

      const isResumption = interaction.isResumption || false;
      let messageId;
      let startingCycle = 0;
      let totalCyclesToRun = amount;
      let nextCycleSeaCreature = null; // Initialize for the broader scope

      // Removed old stop system - now using simple abort signal

      // --- MODIFIED: Pre-calculate rewards for cycles completed BEFORE restart ---
      let preRestartExp = 0;
      const preRestartItems = {};
      let preRestartCoins = 0;

      if (isResumption) {
        actionId = interaction.actionId; // Should be same as currentActionId
        console.log(
          `[skillActionUtils][${actionId}][${skillNameForLog}] Resuming action. InitialCompleted: ${interaction.initialCompletedCycles}, TotalAmt: ${interaction.totalAmount}`
        );
        messageId = interaction.messageToEdit?.id || interaction.messageId;
        totalCyclesToRun = interaction.totalAmount || amount;

        // Retrieve pre-calculated stats from stored parameters with enhanced validation
        if (interaction.parameters?.preCalculatedStats) {
          const storedStats = interaction.parameters.preCalculatedStats;

          // Validate that the stored stats have the essential properties
          const requiredStats = [
            "allStats",
            "farmingFortune",
            "miningFortune",
            "foragingFortune",
            "fishingWisdom",
          ];
          const hasValidStats = requiredStats.some(
            (stat) =>
              storedStats[stat] !== undefined &&
              (typeof storedStats[stat] === "object" ||
                typeof storedStats[stat] === "number")
          );

          if (hasValidStats) {
            preCalculatedStats = storedStats;
            console.log(
              `[${skillNameForLog}][Resume] Using stored pre-calculated stats for action ${actionId}`
            );
          } else {
            console.warn(
              `[${skillNameForLog}][Resume] Stored stats appear corrupted, recalculating for action ${actionId}`
            );
            preCalculatedStats = null; // Force recalculation
          }
        } else {
          //console.log(`[${skillNameForLog}][Resume] No pre-calculated stats found in parameters for action ${actionId}, recalculating from scratch`);
        }

        // Retrieve wasMax flag from stored parameters for proper repeat button functionality
        if (interaction.parameters?.wasMax !== undefined) {
          wasMax = interaction.parameters.wasMax;
          //console.log(`[${skillNameForLog}][Resume] Retrieved wasMax flag: ${wasMax} for action ${actionId}`);
        } else {
          //console.log(`[${skillNameForLog}][Resume] No wasMax flag found in parameters for action ${actionId}, defaulting to false`);
        }

        // Enhanced fallback: calculate stats with proper error handling
        if (!preCalculatedStats) {
          console.log(
            `[${skillNameForLog}][Resume] Recalculating stats for action ${actionId}`
          );
          try {
            // Ensure character stats are properly initialized before calculation
            if (!character.stats || typeof character.stats !== "object") {
              console.warn(
                `[${skillNameForLog}][Resume] Character stats missing, initializing defaults for user ${character.discordId}`
              );
              character.stats = {};
            }

            // Recalculate stats to ensure they're current
            // Note: On workers, stats recalculation is handled by main bot via API
            const { recalculateAndSaveStats } = isWorkerBot
              ? { recalculateAndSaveStats: async () => {} } // Stub for workers
              : playerDataModule;
            if (!isWorkerBot) {
              await recalculateAndSaveStats(character.discordId, character);
            }

            // Reload character after stat recalculation
            const updatedCharacter = await getPlayerData(character.discordId);
            if (updatedCharacter) {
              // Update the character object with recalculated stats
              character.stats = updatedCharacter.stats;
              character.equipment = updatedCharacter.equipment;
              character.pets = updatedCharacter.pets;
              character.accessories = updatedCharacter.accessories;
              console.log(
                `[${skillNameForLog}][Resume] Updated character data after stat recalculation for user ${character.discordId}`
              );
            }

            const {
              calculateAllStats,
              calculateTotalStat,
            } = require("./statCalculations");

            preCalculatedStats = {
              // Core combat stats for sea creatures and combat (required for fishing sea creatures)
              allStats: calculateAllStats(character),
              // Skill-specific stats with safe defaults
              baseFishingSpeed: calculateTotalStat(
                character.stats?.FISHING_SPEED || {}
              ), // Base speed without bait
              farmingFortune: calculateTotalStat(
                character.stats?.FARMING_FORTUNE || {}
              ),
              miningFortune: calculateTotalStat(
                character.stats?.MINING_FORTUNE || {}
              ),
              foragingFortune: calculateTotalStat(
                character.stats?.FORAGING_FORTUNE || {}
              ),
              farmingWisdom: calculateTotalStat(
                character.stats?.FARMING_WISDOM || {}
              ),
              farmingSweep: calculateTotalStat(
                character.stats?.FARMING_SWEEP || {}
              ),
              miningWisdom: calculateTotalStat(
                character.stats?.MINING_WISDOM || {}
              ),
              miningSweep: calculateTotalStat(
                character.stats?.MINING_SWEEP || {}
              ),
              foragingWisdom: calculateTotalStat(
                character.stats?.FORAGING_WISDOM || {}
              ),
              foragingSweep: calculateTotalStat(
                character.stats?.FORAGING_SWEEP || {}
              ),
              fishingWisdom: calculateTotalStat(
                character.stats?.FISHING_WISDOM || {}
              ),
              seaCreatureChance: calculateTotalStat(
                character.stats?.SEA_CREATURE_CHANCE || {}
              ),
              treasureChance: calculateTotalStat(
                character.stats?.TREASURE_CHANCE || {}
              ),
              miningSpeed: calculateTotalStat(
                character.stats?.MINING_SPEED || {}
              ),
              farmingSpeed: calculateTotalStat(
                character.stats?.FARMING_SPEED || {}
              ),
              foragingSpeed: calculateTotalStat(
                character.stats?.FORAGING_SPEED || {}
              ),
            };

            // Validate that the calculation was successful
            if (
              !preCalculatedStats.allStats ||
              typeof preCalculatedStats.allStats.HEALTH !== "number"
            ) {
              console.error(
                `[${skillNameForLog}][Resume] Stat calculation failed for user ${character.discordId}, using minimal defaults`
              );
              // Provide minimal viable stats to prevent crashes
              preCalculatedStats = {
                allStats: { HEALTH: 100, DAMAGE: 1, DEFENSE: 0 },
                baseFishingSpeed: 0,
                farmingFortune: 0,
                miningFortune: 0,
                foragingFortune: 0,
                farmingWisdom: 0,
                farmingSweep: 0,
                miningWisdom: 0,
                miningSweep: 0,
                foragingWisdom: 0,
                foragingSweep: 0,
                fishingWisdom: 0,
                seaCreatureChance: 0,
                treasureChance: 0,
                miningSpeed: 0,
                farmingSpeed: 0,
                foragingSpeed: 0,
              };
            }

            console.log(
              `[${skillNameForLog}][Resume] Successfully recalculated stats for action ${actionId}`
            );
          } catch (statCalcError) {
            console.error(
              `[${skillNameForLog}][Resume] CRITICAL: Stat calculation failed for user ${character.discordId}:`,
              statCalcError
            );
            throw new Error(
              `[${skillNameForLog}][Resume] Cannot proceed without valid stats - no fallback support`
            );
          }
        }

        if (interaction.parameters?.pendingSkillAction) {
          const pending = interaction.parameters.pendingSkillAction;
          startingCycle =
            pending.cycleIndex ?? (interaction.initialCompletedCycles || 0);

          if (
            skillName === "fishing" &&
            pending.seaCreature &&
            pending.seaCreature.mobKey
          ) {
            nextCycleSeaCreature = pending.seaCreature;
          } else if (skillName === "fishing" && pending.seaCreature) {
            console.warn(
              `[${skillNameForLog}][Resume] Pending sea creature data incomplete:`,
              JSON.stringify(pending.seaCreature)
            );
          }
        } else {
          startingCycle = interaction.initialCompletedCycles || 0;
        }

        // --- Use cumulative progress for resumption ---
        if (interaction.parameters?.cumulativeProgress) {
          const cumulative = interaction.parameters.cumulativeProgress;
          preRestartExp = cumulative.totalExp || 0;
          preRestartCoins = cumulative.totalCoins || 0;
          Object.assign(preRestartItems, cumulative.totalItems || {});

          // Load sea creatures defeated (for fishing)
          if (skillName === "fishing" && cumulative.seaCreaturesDefeated) {
            Object.assign(
              seaCreaturesDefeatedMap,
              cumulative.seaCreaturesDefeated
            );
            totalSeaCreatures = Object.values(
              cumulative.seaCreaturesDefeated
            ).reduce((sum, count) => sum + count, 0);
          }

          // Load pets gained
          if (cumulative.petsGained && Array.isArray(cumulative.petsGained)) {
            petsGained.push(...cumulative.petsGained);
          }

          // Check for player defeat
          if (cumulative.playerDefeated) {
            playerDefeated = true;
            if (cumulative.defeatedBy) {
              defeatedByMob = cumulative.defeatedBy;
            }
          }
        } else if (
          interaction.parameters?.cycle_results &&
          Array.isArray(interaction.parameters.cycle_results)
        ) {
          // Migration path: convert old cycle_results to cumulative format
          console.log(
            `[skillActionUtils][${actionId}][${skillNameForLog}] Migrating from old cycle_results format (${interaction.parameters.cycle_results.length} entries)`
          );

          for (const savedCycle of interaction.parameters.cycle_results) {
            if (savedCycle) {
              const count = savedCycle._count || 1;
              preRestartExp += (savedCycle.exp || 0) * count;
              preRestartCoins += (savedCycle.coins || 0) * count;

              if (Array.isArray(savedCycle.items)) {
                savedCycle.items.forEach((itemStack) => {
                  if (itemStack && itemStack.itemKey && itemStack.amount > 0) {
                    preRestartItems[itemStack.itemKey] =
                      (preRestartItems[itemStack.itemKey] || 0) +
                      itemStack.amount * count;
                  }
                });
              }

              // For fishing, accumulate sea creatures defeated, respecting count
              if (
                skillName === "fishing" &&
                savedCycle.seaCreatureKey &&
                !savedCycle.playerDefeated &&
                !savedCycle.defeated
              ) {
                seaCreaturesDefeatedMap[savedCycle.seaCreatureKey] =
                  (seaCreaturesDefeatedMap[savedCycle.seaCreatureKey] || 0) +
                  count;
                totalSeaCreatures += count;
              }

              // Check if player was defeated in a pre-restart cycle
              if (savedCycle.playerDefeated || savedCycle.defeated) {
                playerDefeated = true;
                if (savedCycle.defeatedBy) {
                  defeatedByMob = savedCycle.defeatedBy;
                } else if (savedCycle.mobName) {
                  defeatedByMob = {
                    name: savedCycle.mobName,
                    emoji: savedCycle.mobEmoji || "💀",
                  };
                }
                console.log(
                  `[${actionName}][Multi-Resume] Player was defeated in a pre-restart cycle batch (count: ${count}). Mob: ${defeatedByMob?.name}`
                );
              }
            }
          }
          console.log(
            `[skillActionUtils][${actionId}][${skillNameForLog}] Migrated from old format: ${preRestartExp} exp, ${preRestartCoins} coins, ${
              Object.keys(preRestartItems).length
            } item types, ${totalSeaCreatures} sea creatures defeated.`
          );
        } else if (isResumption && startingCycle > 0) {
          // CRITICAL: This should never happen with current persistence system
          // If we reach here, there's a bug in action persistence that needs to be fixed
          console.error(
            `[${actionName}][Multi-Resume] CRITICAL BUG: Missing progress data for resumption with ${startingCycle} pre-restart cycles. This indicates a persistence system failure.`
          );

          // Don't calculate fallback rewards - this would cause loot duplication
          // Instead, log the issue and continue with no pre-restart rewards
          console.error(
            `[${actionName}][Multi-Resume] Continuing with no pre-restart rewards to prevent loot duplication. User may lose progress from ${startingCycle} cycles.`
          );

          // Ensure pre-restart tracking is reset
          preRestartExp = 0;
          preRestartCoins = 0;
          Object.keys(preRestartItems).forEach(
            (key) => delete preRestartItems[key]
          );
        }

        // --- ADD PRE-RESTART REWARDS TO TOTALS ---
        for (const [key, amount] of Object.entries(preRestartItems)) {
          itemsGained[key] = (itemsGained[key] || 0) + amount;
        }
      } else {
        const dbParams = {
          preCalculatedStats: preCalculatedStats, // Store pre-calculated stats for resumption
          wasMax: wasMax, // Store wasMax flag for repeat button functionality after resumption
        };
        if (resourceKeyParamName && resourceKey) {
          dbParams[resourceKeyParamName] = resourceKey;
        }
        const setupResult = await setupNewMultiAction(
          interaction,
          skillName,
          resourceKey,
          amount,
          {
            color: colors.multiActionLoop,
            emoji: resourceData?.emoji || emoji,
            actionName: actionName,
          },
          dbParams,
          character
        );
        messageId = setupResult.messageId;
        actionId = setupResult.actionId;
      }

      if (!actionId) {
        console.error(
          `[${actionName}][Multi-${
            isResumption ? "Resume" : "Start"
          }] Action ID is unexpectedly null/undefined after setup. Cannot proceed.`
        );
        throw new Error(`${actionName} action ID could not be established.`);
      }

      // brewing: check if action was already cleaned up (stopped during animation)
      if (skillName === "alchemy" && actionId) {
        const actionRecord = await getActionById(actionId);
        if (!actionRecord) {
          console.log(
            `[Brewing][Multi] Action ${actionId} was already cleaned up (stopped during animation), skipping main loop.`
          );
          return;
        }
        if (
          actionRecord.status === "complete" ||
          actionRecord.status === "stopped"
        ) {
          console.log(
            `[Brewing][Multi] Action ${actionId} already marked as complete/stopped, skipping main loop.`
          );
          return;
        }
      }

      let totalExpGainedSim = 0; // Rewards gained *during this run*
      let totalCombatExpGainedSim = 0; // Combat EXP gained *during this run* (e.g., from Endermites)
      const totalItemsGainedSim = {}; // Rewards gained *during this run*
      const totalItemsConsumedSim = {}; // Items consumed *during this run*
      let totalCoinsGainedSim = 0; // Track coins if multi-actions can grant them
      let totalAccessoriesGained = []; // Track accessory drops from sea creature combat
      let totalPetsGained = []; // Track pet drops from sea creature combat
      let totalGardenMilestones = []; // Garden milestones achieved *during this run*
      const totalGardenXpGained = 0; // Garden XP gained *during this run*
      let gardenLevelUpInfo = null; // Garden level-up info from this run
      let playerStopped = false;
      let playerDefeated = false;
      let defeatedByMob = null; // Track the mob that defeated the player
      let completedCyclesInThisRun = 0;

      // Track actual crops harvested for Garden milestone footer
      let totalCropsHarvestedForFooter = 0;

      // Create fresh embed objects each cycle to prevent state accumulation
      // const actionEmoji = emoji || skillEmojis[skillName] || '⚙️';

      // For fishing: track all sea creatures defeated across cycles
      // const seaCreaturesDefeatedMap = {}; // MOVED EARLIER
      // let totalSeaCreatures = 0;           // MOVED EARLIER

      let lastCycleResult = null;

      // Get abort signal for instant stop functionality
      const signal = getAbortSignal(actionId);

      // Create fresh embed for each cycle to prevent state accumulation
      const loopEmbed = new EmbedBuilder().setColor(colors.multiActionLoop);

      // MESSAGE CYCLING: Track message age to prevent Discord rate limit slowdown
      const MAX_MESSAGE_AGE = 58 * 60 * 1000; // 58 minutes in milliseconds
      let currentMessageId = messageId;
      let messageCreationTime;

      // Get or set message creation time from database
      const initializeMessageTracking = async () => {
        try {
          const actionParams = await getActionParameters(actionId);

          if (actionParams.messageCreationTime) {
            messageCreationTime = actionParams.messageCreationTime;

            // Check if message is already too old on resumption
            const messageAge = Date.now() - messageCreationTime;
            if (messageAge >= MAX_MESSAGE_AGE) {
              console.log(
                `[${actionName}][Multi] Message is ${Math.round(messageAge / 1000 / 60)} minutes old on resumption, cycling immediately`
              );
              return true; // Needs immediate cycling
            }

            return false; // No cycling needed
          } else {
            // NEW ACTION: Set timestamp but don't force cycling for brand new actions
            messageCreationTime = Date.now();
            await updateActionMessageTimestamp(actionId, messageCreationTime);
            return false; // New actions don't need immediate cycling
          }
        } catch (error) {
          console.error(
            `[${actionName}][Multi] Error initializing message tracking:`,
            error
          );
          messageCreationTime = Date.now();
          return true; // Force cycling on error to be safe
        }
      };

      // Helper function to create new message when current message gets too old
      const cycleToNewMessage = async () => {
        try {
          // Store old message ID before deletion to avoid race conditions
          const oldMessageId = currentMessageId;

          // Build a fresh embed and button row based on the current loopEmbed
          const {
            buildAnimationUIFromEmbed,
            buildFishingUIFromEmbed,
          } = require("./animationContainers");

          let containerResult;
          if (skillName === "fishing") {
            // Provide current bait info for fishing button
            let currentBaitInfo;
            try {
              const { getCurrentBaitInfo } = require("./fishingButtons");
              currentBaitInfo = await getCurrentBaitInfo(userId);
            } catch (_) {
              currentBaitInfo = { emoji: "🎣", name: "No Bait" };
            }
            containerResult = buildFishingUIFromEmbed(
              loopEmbed,
              actionId,
              userId,
              actionName,
              currentBaitInfo
            );
          } else {
            containerResult = buildAnimationUIFromEmbed(
              loopEmbed,
              actionId,
              userId,
              actionName
            );
          }
          const newMessage = await interaction.channel.send({
            embeds: [containerResult.embed],
            components: [
              new (require("discord.js").ActionRowBuilder)().addComponents(
                ...containerResult.buttons
              ),
            ],
          });

          // Update tracking variables immediately after successful creation
          currentMessageId = newMessage.id;
          messageCreationTime = Date.now();

          // Update the database with new message ID and timestamp
          await updateActionMessageId(actionId, currentMessageId);
          await updateActionMessageTimestamp(actionId, messageCreationTime);

          // CRITICAL FIX: Update messageId variable to match currentMessageId after cycling
          // This ensures final results deletion uses the correct message ID
          messageId = currentMessageId;

          console.log(
            `[${actionName}][Multi] Cycled to new message ${currentMessageId} due to age (58+ minutes) with stop button`
          );

          // Delete the old message AFTER creating the new one to prevent gaps
          // This prevents "Unknown Message" errors during resumption
          try {
            if (oldMessageId && oldMessageId !== currentMessageId) {
              await interaction.channel.messages.delete(oldMessageId);
              console.log(
                `[${actionName}][Multi] Successfully deleted old message ${oldMessageId}`
              );
            }
          } catch (deleteError) {
            // Don't fail the entire cycling process if old message deletion fails
            console.warn(
              `[${actionName}][Multi] Could not delete old message ${oldMessageId}:`,
              deleteError.message
            );
          }
        } catch (error) {
          console.error(
            `[${actionName}][Multi] Error cycling to new message:`,
            error
          );
          // Continue with existing message if cycling fails
        }
      };

      try {
        // Initialize message tracking and check if immediate cycling is needed
        const needsImmediateCycling = await initializeMessageTracking();
        if (needsImmediateCycling) {
          await cycleToNewMessage();
        }

        // PERFORMANCE NOTE: Each cycle performs multiple potentially expensive operations:
        // 1. Database reads/writes for progress tracking
        // 2. Discord API calls for message editing/animation
        // 3. Database operations for bait consumption (fishing)
        // 4. Immediate database writes for pet additions
        // Future optimization: Consider batching some of these operations

        for (let i = startingCycle; i < totalCyclesToRun; i++) {
          // MESSAGE CYCLING: Check if message is older than 58 minutes
          const messageAge = Date.now() - messageCreationTime;
          if (messageAge >= MAX_MESSAGE_AGE) {
            await cycleToNewMessage();
          }

          // Check for instant abort at start of each cycle
          if (signal && signal.aborted) {
            console.log(
              `[${actionName}][Multi] Action ${actionId} aborted instantly - stopping immediately`
            );
            playerStopped = true;
            break;
          }
          // --- NEW: Immediately break if player was defeated in a previous cycle ---
          if (playerDefeated) {
            break;
          }

          // DEFENSIVE FIX: Ensure we have the correct message ID from database
          // This prevents issues when message ID was updated during resumption
          if (actionId && !messageId) {
            try {
              const actionRecord = await getActionById(actionId);
              if (actionRecord && actionRecord.message_id) {
                messageId = actionRecord.message_id;
                console.log(
                  `[${actionName}][Multi] Updated message ID from database: ${messageId} for action ${actionId}`
                );
              }
            } catch (fetchError) {
              console.warn(
                `[${actionName}][Multi] Could not fetch message ID for action ${actionId}: ${fetchError.message}`
              );
            }
          }

          // --- REMOVED: Always recalculate and reload stats before each cycle ---
          // try {
          //     await recalculateAndSaveStats(character.discordId, character);
          //     character = await getPlayerData(character.discordId);
          // } catch (statError) {
          //     console.error(`[${actionName}][Multi] Error recalculating stats for user ${userId} (cycle ${i + 1}):`, statError);
          //     // Continue with current character state
          // }

          let auxResourceConsumed = false;
          // No need for characterForThisCycle anymore for bait

          // Reserve auxiliary resource if applicable (e.g., bait for fishing)
          if (skillName === "fishing") {
            try {
              // Use enhanced bait manager for better performance and auto-switching
              const { enhancedBaitManager } = require("./enhancedBaitManager");
              // reserve bait for this cycle
              const baitReservation =
                await enhancedBaitManager.reserveBaitForCycle(userId);

              // Store reservation info for consumption at cycle end
              if (baitReservation.success) {
                auxResourceConsumed = {
                  baitKey: baitReservation.baitKey,
                  fishingSpeedBonus: baitReservation.fishingSpeed,
                  isReserved: true, // Flag to indicate this is reserved, not consumed yet
                  casterSaved: false, // Will be determined at consumption time
                };
              } else {
                auxResourceConsumed = false;
              }

              // Update button emoji to reflect the actual bait being used this cycle
              try {
                const { updateBaitSwapButton } = require("./fishingButtons");
                const message = await interaction.channel.messages.fetch(
                  currentMessageId || messageId
                );
                if (message) {
                  // Get actual bait info based on what's being used this cycle
                  let actualBaitInfo;
                  if (auxResourceConsumed && auxResourceConsumed.baitKey) {
                    const configManager = require("./configManager");
                    const allItems = configManager.getAllItems();
                    const baitData = allItems[auxResourceConsumed.baitKey];
                    // fetch live remaining amount so button shows it immediately
                    let amount = 0;
                    try {
                      const {
                        enhancedBaitManager,
                      } = require("./enhancedBaitManager");
                      const available =
                        await enhancedBaitManager.getAvailableBaits(userId);
                      amount = available[auxResourceConsumed.baitKey] ?? 0;
                    } catch {
                      // ignore amount errors, fallback to 0
                    }
                    actualBaitInfo = {
                      emoji: baitData?.emoji || "🎣",
                      name: baitData?.name || auxResourceConsumed.baitKey,
                      baitKey: auxResourceConsumed.baitKey,
                      amount,
                    };
                  } else {
                    actualBaitInfo = {
                      emoji: "🚫",
                      name: "No Bait",
                      baitKey: null,
                      amount: 0,
                    };
                  }
                  await updateBaitSwapButton(
                    message,
                    actionId,
                    userId,
                    actualBaitInfo
                  );
                }
              } catch (buttonUpdateError) {
                console.warn(
                  `[${actionName}][Multi] Could not update bait button for cycle ${i + 1}:`,
                  buttonUpdateError.message
                );
                // Non-critical error, continue with fishing
              }

              if (!auxResourceConsumed) {
                if (i === startingCycle) {
                  console.log(
                    `[${actionName}][Multi] No bait for user ${userId} at start of action. Proceeding without bait.`
                  );
                } else {
                  console.log(
                    `[${actionName}][Multi] Bait ran out for user ${userId} at cycle ${
                      i + 1
                    }. Proceeding without bait.`
                  );
                }
              }
            } catch (consumeError) {
              console.error(
                `[${actionName}][Multi][Consume] Error consuming bait for ${userId} (Cycle ${
                  i + 1
                }):`,
                consumeError
              );
              // Continue without bait rather than stopping entirely
              auxResourceConsumed = false;
            }
          }

          // Simple stop check - just check abort signal
          if (signal && signal.aborted) {
            console.log(
              `[${actionName}][Multi] Action ${actionId} stopped by user - breaking immediately`
            );
            playerStopped = true;
            break;
          }

          // Calculate time using pre-calculated stats for consistency during the loop
          let actionTime;
          if (skillName === "foraging" || skillName === "mining") {
            // For foraging and mining, pass the resourceKey (item key) and preCalculatedStats
            actionTime = calculateTimeFn(resourceKey, preCalculatedStats);
          } else if (skillName === "alchemy") {
            // For alchemy, pass character, potionKey, amount (use 1 for single cycle time)
            actionTime = calculateTimeFn(preCalculatedStats, resourceKey, 1);
          } else {
            actionTime = calculateTimeFn(
              preCalculatedStats,
              auxResourceConsumed,
              resourceKey
            );
          }
          const currentCycleDisplay =
            startingCycle + completedCyclesInThisRun + 1;

          // NEW: Calculate approximate remaining time (ms) before starting this cycle
          const cyclesLeftIncludingCurrent = totalCyclesToRun - i;
          const timeRemainingMsBeforeCycle =
            cyclesLeftIncludingCurrent * actionTime;

          // --- Emoji logic (using resourceData fetched within this block) ---
          const loopTitleEmoji =
            resourceData?.emoji || emoji || skillEmojis[skillName] || "⚙️";
          // --- End emoji logic ---

          // Construct loop title (using resourceDisplayName from this block)
          let loopTitle = `${loopTitleEmoji} ${actionName}`;
          if (resourceDisplayName && resourceDisplayName !== actionName) {
            loopTitle += ` ${resourceDisplayName}`;
          }
          loopTitle += `... (${currentCycleDisplay}/${totalCyclesToRun})`;

          loopEmbed.setTitle(loopTitle);

          // Set initial milestone footer for farming in Garden (first cycle only)
          if (
            skillName === "farming" &&
            character &&
            character.current_region === "garden" &&
            resourceKey &&
            i === startingCycle
          ) {
            try {
              const {
                getCropMilestoneInfo,
                formatMilestoneProgress,
              } = require("./gardenSystem");

              // For the first cycle, show the current state before any farming
              const originalCropInfo = getCropMilestoneInfo(
                character,
                resourceKey
              );
              const progressText = formatMilestoneProgress(
                character,
                resourceKey
              );

              loopEmbed.setFooter({
                text: `${progressText}`,
              });
            } catch (error) {
              console.error(
                "[skillActionUtils] Error setting initial crop milestone footer:",
                error
              );
            }
          }

          // Animation - Pass the correctly determined resource emoji
          let animationAttempts = 0;
          const maxAnimationAttempts = 3;
          let animationSuccessful = false;
          let skipRemainingAnimations = false; // NEW: Track if we should skip animations for this action

          while (
            animationAttempts < maxAnimationAttempts &&
            !animationSuccessful &&
            !skipRemainingAnimations // NEW: Don't retry animations if we've decided to skip them
          ) {
            try {
              // Stop button updates removed - no longer needed

              // Check for instant abort before animation
              if (signal && signal.aborted) {
                console.log(
                  `[${actionName}][Multi] Action ${actionId} aborted instantly before animation`
                );
                playerStopped = true;
                break;
              }

              // Run the animation
              await animationFn(
                currentMessageId,
                interaction.channel,
                loopEmbed,
                actionTime,
                null, // No longer need stop map - using abort signal
                actionId,
                auxResourceConsumed,
                resourceData?.emoji,
                timeRemainingMsBeforeCycle, // Pass remaining time for footer updates
                currentCharacterLoopState, // Pass character for Garden milestone display
                resourceKey, // Pass resource key for Garden milestone display
                startingCycle === 0 && i === startingCycle // Only treat as first cycle if action started from scratch (not resumed)
              );
              animationSuccessful = true;
            } catch (animationError) {
              animationAttempts++;
              console.error(
                `[${actionName}][Multi] Animation attempt ${animationAttempts}/${maxAnimationAttempts} failed for user ${userId} (cycle ${
                  i + 1
                }):`,
                animationError
              );

              // If this was the last attempt, CONTINUE WITH ACTION PROCESSING instead of breaking
              if (animationAttempts >= maxAnimationAttempts) {
                console.warn(
                  `[${actionName}][Multi] All animation attempts failed for action ${actionId}. CONTINUING action processing without animations.`
                );

                // NEW: Set flag to skip animations for remaining cycles in this action
                skipRemainingAnimations = true;

                console.log(
                  `[${actionName}][Multi] Skipping status embed to honor no-interim-embed policy`
                );
                break; // Exit the retry loop but continue with action processing
              } else {
                // Wait a bit before retrying
                await new Promise((resolve) => {
                  setTimeout(resolve, 1000);
                });
              }
            }
          }

          // NEW: Skip animations for subsequent cycles if we've encountered failures
          if (skipRemainingAnimations && i > startingCycle) {
            console.log(
              `[${actionName}][Multi] Skipping animation for cycle ${currentCycleDisplay} due to previous animation failures`
            );
            // Continue with action processing without animation
          }

          // Simple stop check after animation
          if (signal && signal.aborted) {
            console.log(
              `[${actionName}][Multi] Action ${actionId} stopped by user after animation - breaking immediately`
            );
            playerStopped = true;
            break;
          }

          // Simulate results using original character state (goFn shouldn't modify state)
          // Accumulate rewards for *this run* only
          let cycleResult;
          try {
            // Check for instant abort before action processing
            if (signal && signal.aborted) {
              console.log(
                `[${actionName}][Multi] Action ${actionId} aborted instantly before goFn processing`
              );
              playerStopped = true;
              break;
            }

            cycleResult =
              skillName === "fishing"
                ? await goFn(
                    currentCharacterLoopState,
                    {
                      ...interaction,
                      actionId: actionId,
                      messageId: messageId,
                      channel: interaction.channel,
                    }, // Ensure actionId and messageId are set for sea creature combat
                    preCalculatedStats
                  )
                : skillName === "mining"
                  ? await goFn(
                      currentCharacterLoopState,
                      resourceKey,
                      preCalculatedStats,
                      {
                        ...interaction,
                        actionId: actionId,
                        messageId: messageId,
                        channel: interaction.channel,
                      } // Ensure actionId and messageId are set for mob combat
                    )
                  : skillName === "farming"
                    ? await (async () => {
                        try {
                          const {
                            getFortuneBonusForCrop,
                          } = require("./gardenSystem");
                          const cropBonus =
                            getFortuneBonusForCrop(
                              currentCharacterLoopState,
                              resourceKey
                            ) || 0;
                          const adjustedStats = {
                            ...preCalculatedStats,
                            farmingFortune:
                              (preCalculatedStats.farmingFortune || 0) +
                              cropBonus,
                          };
                          return await goFn(
                            currentCharacterLoopState,
                            resourceKey,
                            adjustedStats,
                            {
                              ...interaction,
                              actionId: actionId,
                              messageId: messageId,
                              channel: interaction.channel,
                            }
                          );
                        } catch (_e) {
                          // Fallback to original preCalculatedStats if bonus calc fails
                          return await goFn(
                            currentCharacterLoopState,
                            resourceKey,
                            preCalculatedStats,
                            {
                              ...interaction,
                              actionId: actionId,
                              messageId: messageId,
                              channel: interaction.channel,
                            }
                          );
                        }
                      })()
                    : await goFn(
                        currentCharacterLoopState,
                        resourceKey,
                        1,
                        preCalculatedStats
                      );

            if (skillName === "foraging") {
              // const actionDuration = actionEndTime - actionStartTime; // Currently unused
              // const treeType = interaction.treeType || resourceKey || "unknown"; // Currently unused
              // Foraging action completed
            }
            lastCycleResult = cycleResult;

            // --- NEW: Consume reserved bait after successful cycle completion ---
            if (
              skillName === "fishing" &&
              auxResourceConsumed &&
              auxResourceConsumed.isReserved
            ) {
              try {
                const {
                  enhancedBaitManager,
                } = require("./enhancedBaitManager");
                // consume reserved bait for this cycle
                const consumptionResult =
                  await enhancedBaitManager.consumeReservedBait(userId);

                // Update auxResourceConsumed with actual consumption results
                if (consumptionResult.success) {
                  auxResourceConsumed.casterSaved =
                    consumptionResult.casterSaved;
                  auxResourceConsumed.isReserved = false; // Mark as consumed

                  // Track consumed bait for results display (only if not saved by Caster)
                  if (
                    !consumptionResult.casterSaved &&
                    consumptionResult.baitKey
                  ) {
                    totalItemsConsumedSim[consumptionResult.baitKey] =
                      (totalItemsConsumedSim[consumptionResult.baitKey] || 0) +
                      1;
                  }
                  // update bait button with post-consumption amount (accurate remaining)
                  try {
                    const {
                      updateBaitSwapButton,
                    } = require("./fishingButtons");
                    const message = await interaction.channel.messages.fetch(
                      currentMessageId || messageId
                    );
                    if (message) {
                      await updateBaitSwapButton(message, actionId, userId);
                    }
                  } catch (postConsumeErr) {
                    console.warn(
                      `[${actionName}][Multi] Could not update bait button after consumption:`,
                      postConsumeErr.message
                    );
                  }
                } else {
                  // If consumption failed, treat as no bait used
                  auxResourceConsumed = false;
                }
              } catch (consumeError) {
                console.error(
                  `[${actionName}][Multi][Consume] Error consuming reserved bait for ${userId} (Cycle ${i + 1}):`,
                  consumeError
                );
                // Continue without bait rather than stopping entirely
                auxResourceConsumed = false;
              }
            }

            // --- NEW: Handle pet rewards from fishing ---
            if (
              skillName === "fishing" &&
              cycleResult &&
              cycleResult.petToAdd
            ) {
              // user received a pet this cycle
              // Add the pet to the character's pets array
              if (!currentCharacterLoopState.pets) {
                currentCharacterLoopState.pets = [];
              }
              currentCharacterLoopState.pets.push(cycleResult.petToAdd);

              // Add the pet to the petsGained array for display in results
              petsGained.push(cycleResult.petToAdd);

              // Save only pets to ensure the pet is properly added without overwriting other fields
              await savePlayerData(
                userId,
                { pets: currentCharacterLoopState.pets },
                ["pets"]
              );
            }

            if (cycleResult.defeated) {
              playerDefeated = true;
              console.log(
                `[${actionName}][Multi] User ${userId} was defeated in cycle ${
                  i + 1
                }/${totalCyclesToRun}.`
              );
              const mob = cycleResult.defeatedBy || cycleResult.mobName;
              if (mob) {
                defeatedByMob = { emoji: mob.emoji || "", name: mob.name };
              }
              break;
            }

            // Check if player fled (manually stopped during sea creature combat)
            if (cycleResult.fled) {
              playerStopped = true;
              console.log(
                `[${actionName}][Multi] User ${userId} fled from encounter in cycle ${
                  i + 1
                }/${totalCyclesToRun}.`
              );
              break;
            }

            // Accumulate XP with wisdom applied per cycle for consistency with other stats
            const rawExpThisCycle = cycleResult.exp || 0;
            if (rawExpThisCycle > 0) {
              // Apply wisdom per cycle (same pattern as fortune/sweep)
              const wisdomStatKey = `${skillName}Wisdom`;
              const wisdom = preCalculatedStats[wisdomStatKey] || 0;
              const wisdomMultiplier = 1 + wisdom / 100;
              const finalExpThisCycle = rawExpThisCycle * wisdomMultiplier; // Allow decimal XP - no Math.floor
              totalExpGainedSim += finalExpThisCycle;

              // Debug log for per-cycle wisdom application
              // applied wisdom bonus for this cycle
            }

            // Accumulate combat EXP separately (e.g., from Endermites during mining)
            totalCombatExpGainedSim += cycleResult.combatExp || 0;

            // Handle unified dropped items array
            if (
              cycleResult.droppedItems &&
              Array.isArray(cycleResult.droppedItems)
            ) {
              cycleResult.droppedItems.forEach((item) => {
                if (item && item.itemKey && item.amount > 0) {
                  if (item.itemType === "ACCESSORY") {
                    // Handle accessories
                    if (!totalAccessoriesGained) {
                      totalAccessoriesGained = [];
                    }
                    for (let i = 0; i < item.amount; i++) {
                      totalAccessoriesGained.push({ itemKey: item.itemKey });
                    }
                    // user received accessory drop(s)
                  } else if (item.itemType === "PET") {
                    // Handle pets
                    if (!totalPetsGained) {
                      totalPetsGained = [];
                    }
                    for (let i = 0; i < item.amount; i++) {
                      totalPetsGained.push({ itemKey: item.itemKey });
                    }
                    // user received pet drop(s)
                  } else {
                    // Handle all other items (regular items, armor, etc.)
                    totalItemsGainedSim[item.itemKey] =
                      (totalItemsGainedSim[item.itemKey] || 0) + item.amount;

                    // Track actual crops harvested for Garden milestone footer
                    if (
                      skillName === "farming" &&
                      character &&
                      character.current_region === "garden" &&
                      resourceKey
                    ) {
                      if (
                        item.itemKey === resourceKey ||
                        (resourceKey === "MUSHROOMS" &&
                          (item.itemKey === "RED_MUSHROOM" ||
                            item.itemKey === "BROWN_MUSHROOM"))
                      ) {
                        totalCropsHarvestedForFooter += item.amount;
                      }
                    }
                  }
                }
              });
            }

            // Handle consumed items array (for alchemy)
            if (
              cycleResult.consumedItems &&
              Array.isArray(cycleResult.consumedItems)
            ) {
              cycleResult.consumedItems.forEach((itemStack) => {
                if (itemStack && itemStack.itemKey && itemStack.amount > 0) {
                  totalItemsConsumedSim[itemStack.itemKey] =
                    (totalItemsConsumedSim[itemStack.itemKey] || 0) +
                    itemStack.amount;
                }
              });
            }

            // Add coin handling if goFn can return coins
            totalCoinsGainedSim += cycleResult.coins || 0;

            // Update crop milestone footer after cycle completion for farming in Garden
            if (
              skillName === "farming" &&
              character &&
              character.current_region === "garden" &&
              resourceKey
            ) {
              try {
                const {
                  getCropMilestoneInfo,
                  formatMilestoneProgress,
                } = require("./gardenSystem");

                // Calculate actual crop harvested so far in this action
                // Use the tracked total from our running counter
                const originalCropInfo = getCropMilestoneInfo(
                  character,
                  resourceKey
                );
                const currentHarvestCount =
                  originalCropInfo.harvested + totalCropsHarvestedForFooter;

                // Create a temporary character object with updated harvest count for milestone calculation
                const tempCharacter = { ...character };
                if (!tempCharacter.gardenMilestones) {
                  tempCharacter.gardenMilestones = "{}";
                }

                let milestones;
                try {
                  milestones = JSON.parse(tempCharacter.gardenMilestones);
                } catch {
                  milestones = {};
                }

                milestones[resourceKey] = currentHarvestCount;
                tempCharacter.gardenMilestones = JSON.stringify(milestones);

                // Get updated milestone info and format progress text
                const progressText = formatMilestoneProgress(
                  tempCharacter,
                  resourceKey
                );

                // Update the footer for the next cycle (if there will be one)
                if (i < totalCyclesToRun - 1) {
                  // Only update if there's a next cycle
                  loopEmbed.setFooter({
                    text: `${progressText}`,
                  });
                }
              } catch (error) {
                console.error(
                  "[skillActionUtils] Error updating crop milestone footer after cycle completion:",
                  error
                );
              }
            }

            // Accumulate Garden notification data (for farming in Garden)
            if (cycleResult.gardenNotifications) {
              if (
                cycleResult.gardenNotifications.milestones &&
                Array.isArray(cycleResult.gardenNotifications.milestones)
              ) {
                totalGardenMilestones = totalGardenMilestones.concat(
                  cycleResult.gardenNotifications.milestones
                );
              }
              // Store Garden level-up info from the last cycle that had one
              if (
                cycleResult.gardenNotifications.levelUp &&
                cycleResult.gardenNotifications.levelUp.leveledUp
              ) {
                gardenLevelUpInfo = cycleResult.gardenNotifications.levelUp;
              }
            }

            // Handle Fig Hew tracking (for foraging)
            if (
              skillName === "foraging" &&
              cycleResult.figHewTracking &&
              cycleResult.figHewTracking.shouldTrack
            ) {
              const equippedTool = currentCharacterLoopState.equipment?.tool; // Move outside try block for catch access
              try {
                // updateInventoryAtomically is already imported at the top with conditional logic

                if (
                  equippedTool &&
                  (equippedTool.itemKey === "FIG_HEW" ||
                    equippedTool.itemKey === "FIGSTONE_SPLITTER")
                ) {
                  // Parse existing data_json or initialize
                  let toolData = {};
                  if (equippedTool.data_json) {
                    try {
                      // Handle both string and object data_json
                      if (typeof equippedTool.data_json === "string") {
                        toolData = JSON.parse(equippedTool.data_json);
                      } else {
                        toolData = equippedTool.data_json;
                      }
                    } catch {
                      console.warn(
                        `[${equippedTool.itemKey}] Failed to parse data_json: "${equippedTool.data_json}" is not valid JSON`
                      );
                      toolData = {};
                    }
                  }

                  // Initialize or update logs broken count
                  if (!toolData.logsOnRegion) {
                    toolData.logsOnRegion = {};
                  }
                  if (!toolData.logsOnRegion.galatea) {
                    toolData.logsOnRegion.galatea = 0;
                  }
                  const logsToAdd = cycleResult.figHewTracking.logsToAdd;
                  const prevLogs = toolData.logsOnRegion.galatea;

                  // Increment log count first
                  toolData.logsOnRegion.galatea = prevLogs + logsToAdd;

                  // --------------------------------------------------
                  // Progressive Ability Level-Up Detection & Notification
                  // --------------------------------------------------
                  try {
                    const allItems = configManager.getAllItems();
                    const itemData = allItems[equippedTool.itemKey];

                    const progAbility =
                      itemData?.ability &&
                      itemData.ability.type === "PROGRESSIVE"
                        ? itemData.ability
                        : null;

                    if (progAbility) {
                      const logsPerBonus = progAbility.logsPerBonus || 1;
                      const maxBonus = progAbility.maxBonus || 0;

                      const calcBonus = (logs) =>
                        Math.min(Math.floor(logs / logsPerBonus), maxBonus);

                      const oldBonus = calcBonus(prevLogs);
                      const newBonus = calcBonus(prevLogs + logsToAdd);

                      if (newBonus > oldBonus) {
                        // Build embed
                        const { STATS } = require("../gameConfig");
                        const statKey = progAbility.stat;
                        const statConfig = STATS[statKey] || {
                          name: statKey,
                          emoji: "",
                        };
                        const statEmoji = statConfig.emoji || "";

                        // Convert STAT_KEY to "Foraging Sweep" style if we lack config name
                        const readableStatName =
                          statConfig.name ||
                          statKey
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (c) => c.toUpperCase());

                        const abilityName = progAbility.name || "Ability";

                        const embed = new EmbedBuilder()
                          .setColor(EMBED_COLORS.GOLD)
                          .setDescription(
                            `Your ${itemData.emoji || ""} **${itemData.name}**'s ${abilityName} leveled up!\n\n` +
                              "**New Stats**\n" +
                              `${statEmoji} **${oldBonus} > ${newBonus} ${readableStatName}**`
                          );

                        try {
                          const levelUpMsg = await interaction.channel.send({
                            embeds: [embed],
                          });
                          setTimeout(async () => {
                            try {
                              await levelUpMsg.delete();
                            } catch {
                              /* ignore */
                            }
                          }, 20000);
                        } catch (notifyErr) {
                          console.error(
                            "[Progressive Level-Up] Error sending notification:",
                            notifyErr
                          );
                        }
                      }
                    }
                  } catch (progNotifyErr) {
                    console.error(
                      "[Progressive Level-Up] Error processing notification:",
                      progNotifyErr
                    );
                  }

                  // Update the tool's data_json
                  const updatedDataJson = JSON.stringify(toolData);

                  // Update the tool in equipment table using equipmentDataUpdates
                  await updateInventoryAtomically(
                    userId,
                    0, // No coin change
                    [], // No items to change
                    [], // No equipment to add
                    [], // No equipment to remove
                    0, // No bank coins
                    [
                      {
                        // Equipment data updates
                        equipmentId: equippedTool.id,
                        dataJson: updatedDataJson,
                      },
                    ]
                  );

                  // Update the character's equipped tool data for stat recalculation
                  currentCharacterLoopState.equipment.tool.data_json =
                    updatedDataJson;
                }
              } catch (figHewError) {
                console.error(
                  `[${equippedTool.itemKey || "Progressive Tool"}] Error updating log tracking:`,
                  figHewError
                );
              }
            }

            // Track sea creatures defeated (for fishing)
            if (
              skillName === "fishing" &&
              cycleResult.seaCreatureKey &&
              !cycleResult.defeated &&
              !cycleResult.fled
            ) {
              seaCreaturesDefeatedMap[cycleResult.seaCreatureKey] =
                (seaCreaturesDefeatedMap[cycleResult.seaCreatureKey] || 0) + 1;
              totalSeaCreatures++;
            }

            // Track Endermites defeated (for mining End Stone)
            if (
              skillName === "mining" &&
              resourceKey === "END_STONE" &&
              cycleResult.endermiteDefeated &&
              !cycleResult.defeated
            ) {
              // Track Endermite defeats similar to sea creatures
              defeatedMobsMap["endermite"] =
                (defeatedMobsMap["endermite"] || 0) + 1;
              totalDefeatedMobs++;
              console.log(
                `[${skillName}][Multi] User ${userId} defeated an Endermite while mining End Stone`
              );
            }
          } catch (cycleError) {
            console.error(
              `[SkillLoop][${skillName}] Error in goFn/processing for cycle ${currentCycleDisplay}/${totalCyclesToRun} (Action ID ${actionId}):`,
              cycleError
            );

            // Log crash to file for debugging
            logSkillActionCrash(cycleError, {
              userId,
              skillName,
              actionId,
              cycleNumber: currentCycleDisplay,
              totalCycles: totalCyclesToRun,
              resourceKey,
              isResumption: interaction.isResumption,
              crashPoint: "cycle_execution",
            }).catch((logErr) => {
              console.error("[CrashLogger] Failed to log cycle crash:", logErr);
            });

            if (
              interaction.isResumption &&
              skillConfig.skillName === "fishing"
            ) {
              console.warn(
                `[SkillLoop][${skillName}] Resumed fishing action cycle ${currentCycleDisplay} errored. Skipping rewards for this cycle and continuing.`
              );
              // Do NOT set playerInterrupted or playerDefeated here.
              // Do NOT break. Allow the loop to continue to the next fishing cycle.
              // Any items/exp from this errored cycle won't be added to totals because `baseCycleReward` might be compromised.

              // NEW: Still increment completedCyclesInThisRun for resumed fishing even when cycle errors
              // This ensures the final count reflects attempted cycles, not just successful ones
              try {
                completedCyclesInThisRun++;
                console.log(
                  `[${skillName}][Multi] Incremented completed cycles to ${completedCyclesInThisRun} despite cycle error (resumed fishing)`
                );
              } catch (incrementError) {
                console.error(
                  `[${skillName}][Multi] Critical error incrementing cycle count:`,
                  incrementError
                );
              }
            } else {
              // Original behavior for non-resumed fishing, other skills, or non-resumed actions that aren't fishing
              playerStopped = true;
              console.error(
                `[${skillName}][Multi] Error in cycle ${currentCycleDisplay}. Action halted.`
              );
              break; // Stop the loop
            }
          }

          // NEW: Always increment completedCyclesInThisRun in a protected way (unless already done in error handling)
          // This is critical for repeat buttons and ping notifications to work
          const shouldIncrementCycles =
            !playerStopped &&
            !playerDefeated &&
            !(
              interaction.isResumption &&
              skillConfig.skillName === "fishing" &&
              !lastCycleResult
            );

          if (shouldIncrementCycles) {
            try {
              completedCyclesInThisRun++;
            } catch (incrementError) {
              console.error(
                `[${skillName}][Multi] Critical error incrementing cycle count for cycle ${currentCycleDisplay}:`,
                incrementError
              );
              // This is critical - if we can't increment, we still need to track progress
              completedCyclesInThisRun = Math.max(
                completedCyclesInThisRun,
                i - startingCycle + 1
              );
              // fallback: set completed cycles based on loop position
            }
          }

          const currentTotalCompleted =
            startingCycle + completedCyclesInThisRun;

          // Garden Visitor Timer Reduction - reduce timer after each farming cycle in garden
          if (
            shouldIncrementCycles &&
            skillName === "farming" &&
            character.current_region === "garden"
          ) {
            try {
              const { reduceFarmingTimer } = require("./gardenVisitors");
              await reduceFarmingTimer(userId, 1); // Reduce by 5 seconds per cycle
            } catch (gardenTimerError) {
              console.error(
                `[${skillName}][Multi] Error reducing garden visitor timer:`,
                gardenTimerError
              );
              // Don't fail the entire action if garden timer fails
            }
          }

          try {
            // --- Create a lean version of cycleResult for persistence ---
            const leanCycleResultForDb = {
              exp: cycleResult?.exp || 0,
              items:
                cycleResult?.items?.map((item) => ({
                  itemKey: item.itemKey,
                  amount: item.amount,
                })) || [],
              droppedItems:
                cycleResult?.droppedItems?.map((item) => ({
                  itemKey: item.itemKey,
                  amount: item.amount,
                  itemType: item.itemType,
                })) || [],
              coins: cycleResult?.coins || 0,
              timestamp: Date.now(), // ADDED: Store timestamp for each cycle
            };

            // IMPROVED: Store resource key for all skill types to improve resumption accuracy
            if (resourceKey) {
              leanCycleResultForDb.resourceKey = resourceKey;
            }

            if (skillName === "fishing") {
              if (
                cycleResult?.seaCreatureKey &&
                !cycleResult?.defeated &&
                !cycleResult?.playerDefeated &&
                !cycleResult?.fled
              ) {
                // Only if player won/wasn't defeated by it and didn't flee
                leanCycleResultForDb.seaCreatureKey =
                  cycleResult.seaCreatureKey;
              }
              leanCycleResultForDb.defeated = cycleResult?.defeated || false; // From sea creature sub-combat
              leanCycleResultForDb.playerDefeated =
                cycleResult?.playerDefeated || false; // Overall player defeat in cycle
              if (cycleResult?.defeatedBy) {
                leanCycleResultForDb.defeatedBy = {
                  name: cycleResult.defeatedBy.name,
                  emoji: cycleResult.defeatedBy.emoji,
                };
              } else if (cycleResult?.mobName) {
                // Fallback from older structure if needed
                leanCycleResultForDb.defeatedBy = {
                  name: cycleResult.mobName,
                  emoji: cycleResult.mobEmoji || "💀",
                };
              }
              if (cycleResult?.petToAdd) {
                leanCycleResultForDb.petToAdd = {
                  petKey: cycleResult.petToAdd.petKey,
                  rarity: cycleResult.petToAdd.rarity,
                  // Do NOT include the full pet object (id, exp, etc.)
                };
              }
            }
            // --- End creating lean version ---

            // Use batched progress updates for better performance
            // Critical operations (pets, defeats, completion) are still processed immediately
            const isCritical =
              currentTotalCompleted === totalCyclesToRun ||
              playerStopped ||
              playerDefeated;
            await batcher.addProgressUpdate(
              actionId,
              currentTotalCompleted,
              leanCycleResultForDb,
              isCritical
            );
          } catch (dbUpdateError) {
            console.warn(
              `[${skillName.toUpperCase()}-LOOP-PROG-001] Failed to update progress for action ID ${actionId} (cycle ${currentTotalCompleted}):`,
              dbUpdateError
            );
          }

          // OPTIMIZATION: Reduce state persistence frequency to improve performance
          // Only persist state every 10 cycles, when critical events occur, or on completion
          const shouldPersistState =
            currentTotalCompleted % 10 === 0 ||
            currentTotalCompleted === totalCyclesToRun ||
            playerStopped ||
            playerDefeated ||
            (skillName === "fishing" && cycleResult?.seaCreatureKey); // Always persist sea creatures

          if (shouldPersistState) {
            try {
              if (skillName === "fishing") {
                // If a sea creature was spawned for the next cycle, persist only its identity
                if (cycleResult && cycleResult.seaCreatureKey) {
                  await persistPendingSkillAction(userId, actionId, {
                    cycleIndex: i + 1,
                    seaCreature: {
                      mobKey: cycleResult.seaCreatureKey,
                      // Add other identity info if needed (e.g., level)
                    },
                  });
                } else {
                  await persistPendingSkillAction(userId, actionId, {
                    cycleIndex: i + 1,
                  });
                }
              } else {
                await persistPendingSkillAction(userId, actionId, {
                  cycleIndex: i + 1,
                  // Add any other state you want to persist here
                });
              }
            } catch (persistError) {
              console.error(
                `[${actionName}][Multi] Error persisting state after cycle ${
                  i + 1
                }/${totalCyclesToRun} for user ${userId}:`,
                persistError
              );
              // This is a non-critical error, so we'll continue
            }
          }

          // In the fishing cycle loop, at the start of each cycle:
          if (
            skillName === "fishing" &&
            typeof nextCycleSeaCreature !== "undefined" &&
            nextCycleSeaCreature
          ) {
            // Start the fight fresh with the pending sea creature
            try {
              const configManager = require("../utils/configManager");
              const seaCreature = configManager.getMob(
                nextCycleSeaCreature.mobKey
              );
              if (seaCreature) {
                // Build a new mob instance for the fight
                const instanceStats =
                  require("./mobUtils").calculateInstanceStats(
                    seaCreature.baseStats
                  );
                const mobInstanceData = {
                  mobKey: seaCreature.key,
                  name: seaCreature.name,
                  level: seaCreature.level,
                  stats: instanceStats,
                  baseExp: seaCreature.baseExp,
                  loot: seaCreature.loot,
                  color: seaCreature.color,
                  emoji: seaCreature.emoji,
                };
                // Fetch current health from database for real-time health in sea creature combat
                console.log(
                  `[${actionName}][Multi] Starting sea creature fight for user ${userId} in cycle ${
                    i + 1
                  }/${totalCyclesToRun}.`
                );

                try {
                  // Get current health from database for accurate combat
                  const currentHealthFromDB = await getPlayerData(userId);
                  if (
                    currentHealthFromDB &&
                    typeof currentHealthFromDB.current_health === "number"
                  ) {
                    currentCharacterLoopState.current_health =
                      currentHealthFromDB.current_health;
                    console.log(
                      `[${actionName}][Multi] Updated character health to ${currentCharacterLoopState.current_health} for sea creature combat`
                    );
                  }

                  // Handle sea creature encounter using fishing engine directly (not combat.js)
                  const {
                    runTurnBasedFight,
                    calculateRewardsFromMob,
                  } = require("./combatEngine");

                  // DEFENSIVE FIX: Ensure we have the most current message before sea creature combat
                  // always use interaction.messageToEdit.id as the authoritative messageId for combat
                  let seaCreatureMessageId = interaction.messageToEdit?.id;
                  if (!seaCreatureMessageId && actionId) {
                    // fallback: get latest from database
                    try {
                      const actionRecord = await getActionById(actionId);
                      if (actionRecord && actionRecord.message_id) {
                        seaCreatureMessageId = actionRecord.message_id;
                        console.log(
                          `[${actionName}][Multi] Fallback to DB messageId for sea creature combat: ${seaCreatureMessageId}`
                        );
                      }
                    } catch (fetchError) {
                      console.warn(
                        `[${actionName}][Multi] Could not fetch messageId from DB for sea creature combat: ${fetchError.message}`
                      );
                    }
                  }

                  // Create a modified interaction object that uses the correct message
                  const currentInteraction = {
                    ...interaction,
                    editReply: async (options) => {
                      if (seaCreatureMessageId && interaction.channel) {
                        const fetchedMessage =
                          await interaction.channel.messages.fetch(
                            seaCreatureMessageId
                          );
                        if (fetchedMessage && fetchedMessage.editable) {
                          return await fetchedMessage.edit(options);
                        }
                      }
                      // fallback to channel send if message is not available
                      if (interaction.channel) {
                        return await interaction.channel.send(options);
                      }
                      return { id: "no-message-available" };
                    },
                    replied: interaction.replied,
                    deferred: interaction.deferred,
                    deferReply: interaction.deferReply?.bind(interaction),
                    user: interaction.user,
                    channel: interaction.channel,
                    guild: interaction.guild,
                    channelId: interaction.channelId,
                    guildId: interaction.guildId,
                    isModifiedForCurrentMessage: true,
                  };

                  const fightResult = await runTurnBasedFight({
                    interaction: currentInteraction,
                    mobInstanceData,
                    character: currentCharacterLoopState,
                    currentFightNum: 1,
                    totalFights: 1,
                    restoreState: null,
                    actionId: actionId,
                    isSeaCreatureSubCombat: true,
                    preCalculatedStats: preCalculatedStats,
                    messageId: seaCreatureMessageId, // always use the correct messageId
                    channel: interaction.channel,
                  });

                  // Convert fight result to combat result format for compatibility
                  let combatResult = null;
                  if (fightResult) {
                    if (fightResult.victory) {
                      // Sea creature defeated - calculate rewards
                      const rewards = await calculateRewardsFromMob(
                        mobInstanceData,
                        fightResult.finalCharacterState
                      );
                      const exp = seaCreature.baseExp?.amount || 0;

                      combatResult = {
                        items: rewards.items,
                        coins: rewards.coins,
                        exp: exp,
                        accessoriesToAdd: rewards.accessoriesToAdd || [],
                        finalCharacterState: fightResult.finalCharacterState,
                      };
                    } else if (fightResult.fled) {
                      // Player fled (manually stopped) - don't count as defeat
                      combatResult = {
                        items: {},
                        coins: 0,
                        exp: 0,
                        finalCharacterState: fightResult.finalCharacterState,
                        fled: true, // Preserve fled status
                      };
                    } else {
                      // Player was defeated
                      combatResult = {
                        items: {},
                        coins: 0,
                        exp: 0,
                        finalCharacterState: fightResult.finalCharacterState,
                        defeated: true, // Mark as defeated
                      };
                    }
                  }

                  if (combatResult) {
                    // Update currentCharacterLoopState with the result of the combat
                    currentCharacterLoopState = combatResult.finalCharacterState
                      ? { ...combatResult.finalCharacterState }
                      : currentCharacterLoopState;

                    // Check if player fled (manually stopped)
                    if (combatResult.fled) {
                      console.log(
                        `[${actionName}][Multi] Sea creature combat: User ${userId} fled (manually stopped).`
                      );
                      playerStopped = true;
                      // Don't add any rewards since the player fled
                      // Don't count the sea creature as defeated
                      // Exit the action loop immediately
                      break;
                    }
                    // If player lost, break out of the loop
                    else if (
                      combatResult.defeated ||
                      currentCharacterLoopState.current_health <= 0
                    ) {
                      console.log(
                        `[${actionName}][Multi] Sea creature combat: User ${userId} was defeated.`
                      );
                      playerDefeated = true;
                      // Always set defeatedByMob for the result embed, even on first cycle
                      defeatedByMob = {
                        emoji: seaCreature.emoji,
                        name: seaCreature.name,
                      };
                      // ROBUST DEATH: Immediately break to stop the action entirely
                      console.log(
                        `[${actionName}][Multi] DEATH DETECTED: Stopping action immediately for user ${userId}.`
                      );
                      break;
                    } else {
                      console.log(
                        `[${actionName}][Multi] Sea creature combat: User ${userId} was victorious.`
                      );
                      // Add sea creature rewards to totals
                      if (combatResult.exp)
                        totalExpGainedSim += combatResult.exp || 0;
                      if (combatResult.coins)
                        totalCoinsGainedSim += combatResult.coins || 0;

                      // Handle unified dropped items from combat
                      if (
                        combatResult.droppedItems &&
                        Array.isArray(combatResult.droppedItems)
                      ) {
                        combatResult.droppedItems.forEach((item) => {
                          if (item && item.itemKey && item.amount > 0) {
                            if (item.itemType === "ACCESSORY") {
                              // Handle accessories
                              if (!totalAccessoriesGained) {
                                totalAccessoriesGained = [];
                              }
                              for (let i = 0; i < item.amount; i++) {
                                totalAccessoriesGained.push({
                                  itemKey: item.itemKey,
                                });
                              }
                            } else if (item.itemType === "PET") {
                              // Handle pets
                              if (!totalPetsGained) {
                                totalPetsGained = [];
                              }
                              for (let i = 0; i < item.amount; i++) {
                                totalPetsGained.push({ itemKey: item.itemKey });
                              }
                            } else {
                              // Handle all other items (regular items, armor, etc.)
                              totalItemsGainedSim[item.itemKey] =
                                (totalItemsGainedSim[item.itemKey] || 0) +
                                item.amount;
                            }
                          }
                        });
                      }
                    }
                  } else {
                    console.log(
                      `[${actionName}][Multi] Sea creature combat: No result returned for user ${userId}.`
                    );
                  }
                } catch (combatError) {
                  console.error(
                    `[${actionName}][Multi] Error during sea creature combat for user ${userId}:`,
                    combatError
                  );
                  // Don't break the loop for combat errors - continue with the next cycle
                }

                // Always clear the pending sea creature so it doesn't repeat

                nextCycleSeaCreature = null;
                await clearPendingSkillAction(actionId);

                // Stop button persistence is now handled by the unified system in setupNewMultiAction

                // After sea creature fight, if defeated, break out of the main loop immediately
                if (playerDefeated) {
                  break;
                }
              } else {
                console.log(
                  `[${actionName}][Multi] Sea creature not found for key: ${nextCycleSeaCreature.mobKey} (user ${userId}).`
                );
                // Clear the pending sea creature if we can't find it
                nextCycleSeaCreature = null;
                await clearPendingSkillAction(actionId);
              }
            } catch (seaCreatureError) {
              console.error(
                `[${actionName}][Multi] Error handling sea creature for user ${userId}:`,
                seaCreatureError
              );
              // Log more details to help debug the issue
              console.log(
                `[${actionName}][Multi][Error] Sea creature data:`,
                nextCycleSeaCreature
              );
              // Clear the pending sea creature to avoid getting stuck
              nextCycleSeaCreature = null;
              try {
                await clearPendingSkillAction(actionId);
              } catch (clearError) {
                console.error(
                  `[${actionName}][Multi] Error clearing pending sea creature for user ${userId}:`,
                  clearError
                );
              }
              // Continue with the next cycle - don't exit the fishing loop for sea creature errors
              // This allows the fishing to continue even if there was an error with the sea creature handling
            }
          }

          // ADDITIONAL CONTINUE CHECK: If we're near the end of the loop and we haven't already broken out,
          // verify that we're still ok to continue by checking player status and activity flags
          if (i < totalCyclesToRun - 1) {
            // inter-cycle pause for skills as akin wants - 500ms between cycles
            // fishing, alchemy, and foraging use dynamic timing based on speed/brewing time/tree toughness
            if (skillName === "farming" || skillName === "mining") {
              try {
                await new Promise((resolve) => setTimeout(resolve, 500));
              } catch (pauseError) {
                console.log(
                  `[${actionName}][Multi] Minor error during inter-cycle pause:`,
                  pauseError
                );
                // non-critical, continue
              }
            }

            // Only check if we're not on the last cycle
            try {
              // Check if the player is still allowed to continue (in case of external changes)
              const currentActivity = await getCurrentActivity(userId);
              if (currentActivity && currentActivity !== skillName) {
                console.log(
                  `[${actionName}][Multi] User ${userId} activity changed to ${currentActivity} from ${skillName}. Stopping.`
                );
                playerStopped = true;
                break;
              }

              // Double-check our action is still valid
              const activeAction = await getActionById(actionId);
              if (!activeAction) {
                console.log(
                  `[${actionName}][Multi] Action ${actionId} no longer exists in DB. Stopping.`
                );
                playerStopped = true;
                break;
              }
            } catch (continueCheckError) {
              // Treat transient network/DB hiccups as soft errors; don't abort the action loop
              const msg = String(
                continueCheckError?.message || ""
              ).toLowerCase();
              const code = continueCheckError?.code || "";
              const transient =
                code === "ECONNRESET" ||
                code === "ECONNREFUSED" ||
                code === "ETIMEDOUT" ||
                msg.includes("socket hang up");
              if (transient) {
                console.warn(
                  `[${actionName}][Multi] Transient continue-check error for user ${userId} – will retry next cycle:`,
                  continueCheckError?.message
                );
              } else {
                console.error(
                  `[${actionName}][Multi] Error checking if action can continue for user ${userId}:`,
                  continueCheckError
                );
              }
              // Continue despite errors
            }
          }
        }
      } catch (mainLoopError) {
        // Check if this was an instant abort
        if (
          mainLoopError.name === "AbortError" ||
          mainLoopError instanceof ActionCancelledError
        ) {
          console.log(
            `[${actionName}][Multi-Loop] Action ${actionId} cancelled instantly by user - processing completed cycles only`
          );
          playerStopped = true;
          // Only give rewards for completed cycles, not the current one being processed
        } else {
          console.error(
            `[${actionName}][Multi-Loop] Critical error in main loop for user ${userId} (actionId ${actionId}):`,
            mainLoopError
          );
          console.log(
            `[${actionName}][Multi-Loop] Loop state at error: completedCycles=${completedCyclesInThisRun}, playerDefeated=${playerDefeated}, playerStopped=${playerStopped}`
          );

          // Save whatever progress we have so far instead of losing everything
          if (completedCyclesInThisRun > 0) {
            console.log(
              `[${actionName}][Multi-Loop] Will still process ${completedCyclesInThisRun} completed cycles despite error.`
            );
            // Continue to finalization instead of stopping with no rewards
          } else {
            playerStopped = true;
          }
        }
      } finally {
        // Clear any pending bait reservations for fishing actions
        if (skillName === "fishing") {
          try {
            const { enhancedBaitManager } = require("./enhancedBaitManager");
            enhancedBaitManager.clearReservation(userId);
            console.log(
              `[${actionName}][Multi] Cleared bait reservation for user ${userId}`
            );
          } catch (clearBaitError) {
            console.error(
              `[${actionName}][Multi] Error clearing bait reservation for user ${userId}:`,
              clearBaitError
            );
            // Non-critical error, continue
          }
        }

        // After loop, clear pending state and cleanup abort controller
        try {
          await clearPendingSkillAction(actionId);
        } catch (clearError) {
          console.error(
            `[${actionName}][Multi] Error clearing pending action state for actionId ${actionId}:`,
            clearError
          );
          // Non-critical error, continue
        }

        // Cleanup abort controller for instant stop system
        if (actionId) {
          cleanupAbortController(actionId);
          //console.log(`[InstantStop] Cleaned up AbortController for action ${actionId}`);
        }
      }

      // --- Ensure defeatedByMob is set if possible ---
      if (
        playerDefeated &&
        !defeatedByMob &&
        typeof lastCycleResult === "object"
      ) {
        if (lastCycleResult.killerName || lastCycleResult.killerKey) {
          defeatedByMob = {
            name: lastCycleResult.killerName || lastCycleResult.killerKey,
          };
        }
      }

      // --- Finalization Logic (Multi) ---
      const finalCompletedCycles = startingCycle + completedCyclesInThisRun;

      // NEW: Add debugging output for very long actions to help diagnose issues
      /*if (amount >= 100) { // Consider actions with 100+ cycles as "long"
                console.log(
                    `[${actionName}][Multi-Debug] LONG ACTION COMPLETION DEBUG:
          - actionId: ${actionId}
          - startingCycle: ${startingCycle}
          - completedCyclesInThisRun: ${completedCyclesInThisRun}
          - finalCompletedCycles: ${finalCompletedCycles}
          - playerStopped: ${playerStopped}
          - playerDefeated: ${playerDefeated}
          - originalAmount: ${amount}
          - totalCyclesToRun: ${totalCyclesToRun}`
                );
            }*/

      // NEW: Safeguard against edge cases where finalCompletedCycles might be unexpectedly low
      const expectedMinimumCycles = Math.min(amount, totalCyclesToRun);
      if (
        finalCompletedCycles === 0 &&
        !playerStopped &&
        !playerDefeated &&
        startingCycle === 0
      ) {
        console.warn(
          `[${actionName}][Multi-Safeguard] CRITICAL: finalCompletedCycles is 0 but action wasn't stopped or defeated. This suggests a bug.
          - actionId: ${actionId}
          - Expected minimum cycles: ${expectedMinimumCycles}
          - Current finalCompletedCycles: ${finalCompletedCycles}
          - Applying emergency fix...`
        );

        // Emergency fix: Assume at least 1 cycle was completed if we got this far without stopping/defeat
        completedCyclesInThisRun = Math.max(1, completedCyclesInThisRun);
        console.log(
          `[${actionName}][Multi-Safeguard] Emergency fix applied: completedCyclesInThisRun set to ${completedCyclesInThisRun}`
        );
      }

      // Recalculate after potential emergency fix
      const finalCompletedCyclesFixed =
        startingCycle + completedCyclesInThisRun;

      // Finalization logic for multi-action
      if (
        finalCompletedCyclesFixed > 0 ||
        (playerStopped && startingCycle > 0) ||
        (playerDefeated && finalCompletedCyclesFixed === 0)
      ) {
        // let finalCharacterData = character; // Start with original character for applying rewards - THIS IS THE OLD LINE
        // Use currentCharacterLoopState which has been updated throughout the loop
        const finalCharacterDataForRewards = { ...currentCharacterLoopState };

        try {
          // --- Calculate FINAL rewards – include any pre-restart gains if this is a resumption ---
          const isResumedAction = startingCycle > 0;

          // Experience
          const expGainedTotal =
            (isResumedAction ? preRestartExp : 0) + totalExpGainedSim;

          // Combat Experience (from secondary encounters like Endermites during this run only)
          const combatExpGainedTotal = totalCombatExpGainedSim;

          // Coins
          const coinsGainedTotal =
            (isResumedAction ? preRestartCoins : 0) + totalCoinsGainedSim;

          // Items – merge pre-restart + this-run deltas
          const itemsGainedTotal = {
            ...(isResumedAction ? preRestartItems : {}),
          };
          for (const [key, amt] of Object.entries(totalItemsGainedSim)) {
            itemsGainedTotal[key] = (itemsGainedTotal[key] || 0) + amt;
          }

          // --- Calculate PET EXP derived from TOTAL EXP gain (all types) ---
          // Dynamically sum all EXP types earned during this action
          const totalActionExp = calculateTotalActionExp(
            expGainedTotal,
            combatExpGainedTotal
          );
          const petExpGainedTotal = calculateActionDerivedExp(
            totalActionExp,
            skillName,
            finalCharacterDataForRewards
          );

          // --- Apply ALL rewards now ---
          // Calculate consumed items total for alchemy
          const itemsConsumedTotal = { ...(isResumedAction ? {} : {}) };
          for (const [key, amt] of Object.entries(totalItemsConsumedSim)) {
            itemsConsumedTotal[key] = (itemsConsumedTotal[key] || 0) + amt;
          }

          const rewardResults = await applyActionRewards(
            userId,
            finalCharacterDataForRewards,
            skillName,
            expGainedTotal,
            petExpGainedTotal,
            itemsGainedTotal,
            coinsGainedTotal,
            undefined, // mobKey
            undefined, // killedCount
            totalAccessoriesGained, // Include accumulated accessories
            totalPetsGained, // Include accumulated pets
            itemsConsumedTotal // Include consumed items for alchemy
          );

          // Debug log for wisdom consistency
          const wisdomUsed = preCalculatedStats?.[`${skillName}Wisdom`];
          if (wisdomUsed > 0) {
            console.log(
              `[${skillName}][Multi-Finalize] Used pre-calculated ${skillName} wisdom: ${wisdomUsed}% applied per cycle (not at end)`
            );
          }

          // --- Aggregate mob kill tracking locally instead of multiple DB writes ---
          // ISSUE FIX: Multiple sequential applyActionRewards() calls with 0 XP were sometimes
          // fetching stale pre-XP state (due to queued writes) and re-saving it, wiping newly gained XP.
          // We now merge mob kill counts in-memory (rewardResults.character already has updated XP)
          // and persist exactly once below.
          const hasSeaCreatureKills =
            skillName === "fishing" &&
            Object.keys(seaCreaturesDefeatedMap).length > 0;
          const hasOtherMobKills = Object.keys(defeatedMobsMap).length > 0;
          if (hasSeaCreatureKills || hasOtherMobKills) {
            const charForMobMerge = rewardResults.character;
            if (!charForMobMerge.mobKills) charForMobMerge.mobKills = {};

            if (hasSeaCreatureKills) {
              for (const [mobKey, killedCount] of Object.entries(
                seaCreaturesDefeatedMap
              )) {
                if (killedCount > 0) {
                  charForMobMerge.mobKills[mobKey] =
                    (charForMobMerge.mobKills[mobKey] || 0) + killedCount;
                }
              }
            }
            if (hasOtherMobKills) {
              for (const [mobKey, killedCount] of Object.entries(
                defeatedMobsMap
              )) {
                if (killedCount > 0) {
                  charForMobMerge.mobKills[mobKey] =
                    (charForMobMerge.mobKills[mobKey] || 0) + killedCount;
                }
              }
            }
            // Defer persistence to the single final updatePlayerSkillDataAtomically call later
          }
          // finalCharacterData = rewardResults.character; // Get up-to-date character (with equipped pet)
          // This variable is used for the final embed and auto-revive.
          const finalCharacterData = rewardResults.character;

          // --- If defeated, apply unified death system ---
          let netCoinsChange = coinsGainedTotal;
          if (playerDefeated) {
            const { processPlayerDeath } = require("./deathSystem");

            // Determine the scenario based on skill type
            let scenario = "skill_encounter";
            if (skillName === "fishing") {
              scenario = "sea_creature_combat";
            } else if (skillName === "mining" && resourceKey === "END_STONE") {
              scenario = "endermite_combat";
            }

            const deathResult = await processPlayerDeath(
              userId,
              finalCharacterData,
              defeatedByMob,
              scenario
            );

            netCoinsChange = coinsGainedTotal + deathResult.netCoinChange;
            console.log(
              `[${actionName}][DeathSystem] Applied death system for ${scenario}. Penalty: ${deathResult.deathPenalty}, Auto-revived: ${deathResult.autoRevived}`
            );
          }

          // Merge notifications/embeds if needed
          const updatedSkillData = rewardResults.updatedSkillData;
          const collectionNotifications = rewardResults.collectionNotifications;
          const allLevelUpEmbeds = rewardResults.allLevelUpEmbeds;

          // --- Auto-revive handled by death system above ---
          // The unified death system handles auto-revive, but we keep this as a safety net
          if (finalCharacterData.current_health <= 0) {
            console.log(
              `[${actionName}][Multi] SAFETY NET: Player ${userId} still dead after death system, force reviving (health: ${finalCharacterData.current_health}).`
            );
            const { respawnCharacter } = require("./respawn");
            respawnCharacter(finalCharacterData);
            // Persist only the health change to avoid overwriting other fields
            await savePlayerData(
              userId,
              { current_health: finalCharacterData.current_health },
              ["current_health"]
            );
          }

          // --- Construct Final Title (Multi-Action) ---
          let petEmoji = undefined;
          if (finalCharacterData.active_pet_id && finalCharacterData.pets) {
            const activePet = finalCharacterData.pets.find(
              (p) => p.id === finalCharacterData.active_pet_id
            );
            if (activePet) {
              const petData = configManager.getAllItems()[activePet.petKey];
              petEmoji = petData?.emoji || undefined;
            }
          }
          // Build sea creatures defeated summary lines (for items section)
          let seaCreaturesBlock = "";
          if (skillName === "fishing" && totalSeaCreatures > 0) {
            const mobs = require("../utils/configManager").getAllMobs();
            const lines = [];
            for (const [mobKey, count] of Object.entries(
              seaCreaturesDefeatedMap
            )) {
              const mob = mobs[mobKey];
              if (mob) {
                lines.push(
                  `${mob.emoji ? mob.emoji + " " : ""}\`x${count} ${
                    mob.name
                  } Lvl ${mob.level}\``
                );
              } else {
                lines.push(`\`x${count} ${mobKey}\``);
              }
            }
            seaCreaturesBlock = lines.join("\n");
          }

          // Build defeated mobs summary lines (for items section)
          let defeatedMobsBlock = "";
          if (totalDefeatedMobs > 0) {
            const mobs = require("../utils/configManager").getAllMobs();
            const lines = [];
            for (const [mobKey, count] of Object.entries(defeatedMobsMap)) {
              const mob = mobs[mobKey];
              if (mob) {
                lines.push(
                  `${mob.emoji ? mob.emoji + " " : ""}\`x${count} ${
                    mob.name
                  } Lvl ${mob.level}\``
                );
              } else {
                lines.push(`\`x${count} ${mobKey}\``);
              }
            }
            defeatedMobsBlock = lines.join("\n");
          }
          // Items consumed – already calculated above for applyActionRewards

          // Use the centralized builder for all skills
          const resultObj = {
            mobEmoji: petEmoji || resourceData?.emoji || "👾",
            mobName: resourceDisplayName,
            mobLevel: resourceData?.level || "",
            killedCount:
              skillName === "combat" ? finalCompletedCyclesFixed : undefined,
            color: playerStopped
              ? colors.multiActionStopped
              : playerDefeated
                ? "#DC143C"
                : colors.multiActionComplete, // Updated color for defeat
            skillName: skillName,
            exp: expGainedTotal,
            combatExp: combatExpGainedTotal, // Combat EXP from secondary encounters (e.g., Endermites)
            petExp: petExpGainedTotal,
            tamingExp: getTamingExpFromPetExp(petExpGainedTotal),
            items: itemsGainedTotal,
            consumedItems: itemsConsumedTotal, // Add consumed items to the result object
            petsGained: [...petsGained, ...totalPetsGained], // Add pets gained to the result object (including combat drops)
            accessoriesGained: totalAccessoriesGained, // Add accessories gained to the result object
            finalCharacterData: finalCharacterData,
            finalSkillData: {
              ...updatedSkillData,
              exp: finalCharacterData.skills?.[skillName]?.exp || 0,
            },
            petEmoji,
            resourceName: resourceData?.name || resourceKey,
            resourceAmount: finalCompletedCyclesFixed,
            coins: netCoinsChange,
            ...(skillName === "fishing" && seaCreaturesBlock
              ? { seaCreaturesBlock }
              : {}),
            ...(defeatedMobsBlock ? { defeatedMobsBlock } : {}),
            ...(skillName === "fishing" && lastCycleResult?.descriptionPrefix
              ? { descriptionPrefix: lastCycleResult.descriptionPrefix }
              : {}),
            // Garden milestone data (for farming in Garden)
            ...(totalGardenMilestones.length > 0
              ? {
                  gardenMilestones: totalGardenMilestones,
                  gardenXpGained: totalGardenXpGained,
                }
              : {}),
            // Garden level-up data (for farming in Garden)
            ...(gardenLevelUpInfo && gardenLevelUpInfo.leveledUp
              ? { gardenLevelUp: gardenLevelUpInfo }
              : {}),
            // More explicit handling for defeatedBy information
            defeatedBy:
              playerDefeated && defeatedByMob ? defeatedByMob : undefined,
            defeated: playerDefeated, // Explicitly include defeated flag
            duration: Date.now() - startTime, // Total action duration in ms (now uses persisted startTime)
          };

          // add 500ms pause before showing results for single-cycle actions as akin wants
          // fishing, alchemy, and foraging use dynamic timing based on speed/brewing time/tree toughness
          if (
            (skillName === "farming" || skillName === "mining") &&
            totalCyclesToRun === 1
          ) {
            try {
              await new Promise((resolve) => setTimeout(resolve, 500));
            } catch (pauseError) {
              console.log(
                `[${actionName}][Multi] Minor error during results pause:`,
                pauseError
              );
              // non-critical, continue
            }
          }

          const resultEmbed = buildFinalSkillResultEmbed(resultObj);

          // --- Add Repeat button if action was not stopped or if using fishing (even when defeated) ---
          let repeatComponents = [];
          // For fishing, show if not stopped AND (defeated OR cycles > 0)
          // For other skills, show if not stopped AND not defeated AND cycles > 0
          if (
            (skillName === "fishing" &&
              !playerStopped &&
              (playerDefeated || finalCompletedCyclesFixed > 0)) ||
            (!playerStopped &&
              !playerDefeated &&
              finalCompletedCyclesFixed > 0 &&
              skillName !== "fishing")
          ) {
            const repeatButton = new ButtonBuilder()
              .setCustomId(
                `repeat_skill:${userId}:${skillName}:${resourceKey || ""}:${playerDefeated ? amount || 1 : finalCompletedCyclesFixed}:${wasMax ? "max" : "normal"}`
              ) // If defeated, repeat original amount
              .setLabel("Repeat")
              .setStyle(ButtonStyle.Primary);
            repeatComponents = [
              new ActionRowBuilder().addComponents(repeatButton),
            ];
          }

          try {
            // Always send final results as a new message so they appear as the latest thing in the channel
            const channel = interaction.channel;
            if (channel) {
              await channel.send({
                embeds: [resultEmbed],
                components: repeatComponents,
              });
              //console.log(`[${actionName}][Multi-Finalize] Final results sent as new message for better visibility`);

              // Delete the current action message now that results are posted
              // Use currentMessageId if available (after cycling), otherwise fallback to original messageId
              const messageIdToDelete = currentMessageId || messageId;
              if (messageIdToDelete) {
                try {
                  await channel.messages.delete(messageIdToDelete);
                  //console.log(`[${actionName}][Multi-Finalize] Original action message deleted for cleaner channel`);
                } catch (deleteError) {
                  console.warn(
                    `[${actionName}][Multi-Finalize] Could not delete original action message:`,
                    deleteError.message
                  );
                }
              }
            } else {
              console.error(
                `[${actionName}][Multi-Finalize] No channel available to send final results for action ${actionId}`
              );
            }
          } catch (finalSendError) {
            console.error(
              `[${actionName}][Multi-Finalize] Failed to send final result message for action ${actionId}:`,
              finalSendError
            );
          }

          // Use targeted update to avoid overwriting minion storage and other concurrent changes
          await updatePlayerSkillDataAtomically(userId, finalCharacterData);
          // Pet XP and skill data saved successfully
          // Removed verbose XP logging for routine saves

          // Ping user if they have pingOnMultiAction enabled - MOVED TO APPEAR FIRST

          let shouldPing = false;
          if (
            finalCharacterData.settings?.pingOnMultiAction &&
            !playerStopped
          ) {
            // Player manually stopped, so no ping
            if (playerDefeated) {
              shouldPing = true; // Ping on any defeat (if not manually stopped)
            } else if (
              /* !playerStopped is already checked */ !playerDefeated &&
              finalCompletedCyclesFixed > 0
            ) {
              shouldPing = true; // Ping on normal completion with cycles (if not manually stopped)
            }
            // Edge case: if startingCycle > 0 and completedCyclesInThisRun is 0, but it wasn't a defeat or stop, it implies completion through downtime. Downtime has separate ping logic.
          }

          if (shouldPing) {
            if (
              !finalCharacterData.pingStates ||
              !finalCharacterData.pingStates[actionId]
            ) {
              try {
                // Validate interaction has channel access for ping notifications
                if (
                  !interaction?.channel ||
                  typeof interaction.channel.send !== "function"
                ) {
                  console.error(
                    `[PingMe] Invalid interaction or missing channel access for user ${userId} (actionId: ${actionId})`
                  );
                  throw new Error(
                    "Invalid interaction or missing channel access"
                  );
                }

                const resourceEmoji =
                  resourceData && resourceData.emoji
                    ? resourceData.emoji
                    : emoji || "";
                const resourceDisplay = resourceEmoji
                  ? `${resourceEmoji} `
                  : "";

                // Special handling for fishing which doesn't have a resourceKey
                let resourceQty = "";
                if (skillName === "fishing") {
                  resourceQty = `**Fishing x${finalCompletedCyclesFixed}**`;
                } else {
                  resourceQty =
                    resourceDisplayName && finalCompletedCyclesFixed
                      ? `**${resourceDisplayName} x${finalCompletedCyclesFixed}**`
                      : resourceDisplayName
                        ? `**${resourceDisplayName}**`
                        : "";
                }

                let pingEmbed;
                if (playerDefeated) {
                  // Ping for early defeat - simple format matching combat
                  let defeatDescription;
                  if (skillName === "fishing") {
                    // Get sea creature info for fishing
                    let seaCreatureInfo = "Sea Creature";
                    if (defeatedByMob?.name) {
                      const mobs =
                        require("../utils/configManager").getAllMobs();
                      const mobData = Object.values(mobs).find(
                        (mob) => mob.name === defeatedByMob.name
                      );
                      seaCreatureInfo = mobData?.emoji
                        ? `${mobData.emoji} ${defeatedByMob.name}`
                        : defeatedByMob.name;
                    }
                    defeatDescription = `Your **${amount} 🎣 Fishing Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated by ${seaCreatureInfo}.`;
                  } else if (skillName === "mining") {
                    const resourceEmoji = resourceData?.emoji || "⛏️";
                    let mobInfo = "";
                    if (defeatedByMob?.name) {
                      const mobs =
                        require("../utils/configManager").getAllMobs();
                      const mobData = Object.values(mobs).find(
                        (mob) => mob.name === defeatedByMob.name
                      );
                      mobInfo = mobData?.emoji
                        ? `${mobData.emoji} ${defeatedByMob.name}`
                        : defeatedByMob.name;
                      defeatDescription = `Your **${amount} ${resourceEmoji} ${resourceDisplayName} Mining Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated by ${mobInfo}.`;
                    } else {
                      defeatDescription = `Your **${amount} ${resourceEmoji} ${resourceDisplayName} Mining Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated.`;
                    }
                  } else if (skillName === "farming") {
                    const resourceEmoji = resourceData?.emoji || "🌾";
                    if (defeatedByMob?.name) {
                      const mobs =
                        require("../utils/configManager").getAllMobs();
                      const mobData = Object.values(mobs).find(
                        (mob) => mob.name === defeatedByMob.name
                      );
                      const mobInfo = mobData?.emoji
                        ? `${mobData.emoji} ${defeatedByMob.name}`
                        : defeatedByMob.name;
                      defeatDescription = `Your **${amount} ${resourceEmoji} ${resourceDisplayName} Farming Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated by ${mobInfo}.`;
                    } else {
                      defeatDescription = `Your **${amount} ${resourceEmoji} ${resourceDisplayName} Farming Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated.`;
                    }
                  } else if (skillName === "foraging") {
                    const resourceEmoji = resourceData?.emoji || "🪓";
                    if (defeatedByMob?.name) {
                      const mobs =
                        require("../utils/configManager").getAllMobs();
                      const mobData = Object.values(mobs).find(
                        (mob) => mob.name === defeatedByMob.name
                      );
                      const mobInfo = mobData?.emoji
                        ? `${mobData.emoji} ${defeatedByMob.name}`
                        : defeatedByMob.name;
                      defeatDescription = `Your **${amount} ${resourceEmoji} ${resourceDisplayName} Foraging Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated by ${mobInfo}.`;
                    } else {
                      defeatDescription = `Your **${amount} ${resourceEmoji} ${resourceDisplayName} Foraging Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated.`;
                    }
                  } else {
                    // Generic fallback for any other skills
                    if (defeatedByMob?.name) {
                      const mobs =
                        require("../utils/configManager").getAllMobs();
                      const mobData = Object.values(mobs).find(
                        (mob) => mob.name === defeatedByMob.name
                      );
                      const mobInfo = mobData?.emoji
                        ? `${mobData.emoji} ${defeatedByMob.name}`
                        : defeatedByMob.name;
                      defeatDescription = `Your **${amount} ${actionName} Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated by ${mobInfo}.`;
                    } else {
                      defeatDescription = `Your **${amount} ${actionName} Actions** were interrupted after **${finalCompletedCyclesFixed} actions** as you were defeated.`;
                    }
                  }
                  pingEmbed = new EmbedBuilder()
                    .setColor(colors.error || "#ff0000")
                    .setDescription(defeatDescription);
                } else {
                  // Normal completion message - simple format
                  const completionDescription = `Your ${resourceDisplay}${resourceQty} ${actionName.toLowerCase()} action has finished!`;

                  pingEmbed = new EmbedBuilder()
                    .setColor(colors.multiActionComplete)
                    .setDescription(completionDescription);
                }
                // Send as a regular channel message instead of interaction follow-up
                await interaction.channel.send({
                  content: `<@${userId}>`,
                  embeds: [pingEmbed],
                });
                if (!finalCharacterData.pingStates)
                  finalCharacterData.pingStates = {};
                finalCharacterData.pingStates[actionId] = true;
                // Player data with updated pingStates will be saved by the existing savePlayerData call shortly after this block
              } catch (pingErr) {
                console.error(
                  `[PingMe] Failed to send ping for ${userId} (actionId: ${actionId}):`,
                  pingErr
                );
              }
            } else {
              // Ping already sent for this action
            }
          }

          // Add delay after action ping before other notifications
          if (
            shouldPing &&
            (allLevelUpEmbeds.length > 0 ||
              collectionNotifications.length > 0 ||
              (skillName === "farming" &&
                (totalGardenMilestones.length > 0 ||
                  (gardenLevelUpInfo && gardenLevelUpInfo.leveledUp))))
          ) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }

          // Send level up and collection notifications AFTER action ping
          await sendActionFollowups(
            interaction,
            allLevelUpEmbeds,
            collectionNotifications,
            rewardResults.pendingCoinRewards || 0
          );

          // Add delay before garden notifications
          if (
            skillName === "farming" &&
            (totalGardenMilestones.length > 0 ||
              (gardenLevelUpInfo && gardenLevelUpInfo.leveledUp)) &&
            (allLevelUpEmbeds.length > 0 || collectionNotifications.length > 0)
          ) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }

          // Send garden milestone and level-up notifications (for farming in Garden)
          if (
            skillName === "farming" &&
            (totalGardenMilestones.length > 0 ||
              (gardenLevelUpInfo && gardenLevelUpInfo.leveledUp))
          ) {
            try {
              const {
                sendGardenNotifications,
              } = require("./gardenNotifications");
              await sendGardenNotifications(
                interaction,
                totalGardenMilestones,
                gardenLevelUpInfo
              );
            } catch (gardenNotificationError) {
              console.error(
                "[Skill Action] Error sending garden notifications:",
                gardenNotificationError
              );
            }
          }

          // Check and notify for Disblock XP updates AFTER all other notifications
          try {
            const { checkAndNotifyDisblockXP } = require("./disblockXpSystem");
            await checkAndNotifyDisblockXP(userId, interaction);
          } catch (xpError) {
            console.error(
              `Error updating Disblock XP for user ${userId}:`,
              xpError
            );
            // Continue even if Disblock XP update fails
          }
        } catch (rewardError) {
          const errorCode = `[${skillName.toUpperCase()}-FIN-REW-001]`;
          console.error(
            `[${actionName}][Multi-Finalize] Error applying rewards/saving data for action ${actionId} (Completed Cycles: ${finalCompletedCyclesFixed}):`,
            rewardError
          );
          try {
            if (messageId) {
              await interaction.channel.messages.edit(messageId, {
                content: `${errorCode} An error occurred finalizing the ${skillName} results. Your progress might not be fully saved. Please report this code.`,
                embeds: [],
                components: [],
              });
            } else {
              console.error(
                `[${actionName}][Multi-Finalize] Message ID missing, cannot edit after reward error for action ${actionId}`
              );
            }
          } catch (editErr) {
            console.error(
              `[${skillName.toUpperCase()}-REPLY-ERR-001] Also failed to edit message after reward error:`,
              editErr
            );
          }
        } finally {
          try {
            await completeAction(actionId);
            await clearActivity(userId); // Clear activity from active_actions table
          } catch (dbCompleteError) {
            const errorCode = `[${skillName.toUpperCase()}-FIN-CMP-001]`;
            console.error(
              `${errorCode} Failed to mark action ${actionId} as complete in DB:`,
              dbCompleteError
            );
          }
        }
      } else if (
        finalCompletedCyclesFixed === 0 &&
        playerStopped &&
        startingCycle === 0
      ) {
        // User stopped before any cycles completed: show a result embed
        const resultEmbedStoppedEarly = buildFinalSkillResultEmbed({
          title: `${actionName} Stopped`,
          color: colors.multiActionStopped,
          skillName,
          description:
            "Action was stopped before any cycles were completed. No rewards earned.",
        });
        try {
          // Always send stopped-early results as a new message so they appear as the latest thing in the channel
          const channel = interaction.channel;
          if (channel) {
            await channel.send({
              embeds: [resultEmbedStoppedEarly],
            });
            console.log(
              `[${actionName}][Multi-Finalize] Stopped-early results sent as embed for better visibility`
            );

            // Delete the current action message now that results are posted
            // Use currentMessageId if available (after cycling), otherwise fallback to original messageId
            const messageIdToDelete = currentMessageId || messageId;
            if (messageIdToDelete) {
              try {
                await channel.messages.delete(messageIdToDelete);
                console.log(
                  `[${actionName}][Multi-Finalize] Original stopped-early action message deleted for cleaner channel`
                );
              } catch (deleteError) {
                console.warn(
                  `[${actionName}][Multi-Finalize] Could not delete original stopped-early action message:`,
                  deleteError.message
                );
              }
            }
          } else {
            console.error(
              `[${actionName}][Multi-Finalize] No channel available to send stopped-early results for action ${actionId}`
            );
          }
        } catch (finalSendError) {
          console.error(
            `[${actionName}][Multi-Finalize] Failed to send stopped-early result message for action ${actionId}:`,
            finalSendError
          );
        }
        // No rewards to save, but still clear activity and complete action
        try {
          await completeAction(actionId);
          await clearActivity(userId); // Clear activity from active_actions table
        } catch (dbCompleteError) {
          const errorCode = `[${skillName.toUpperCase()}-FIN-CMP-003]`;
          console.error(
            `${errorCode} Failed to mark action ${actionId} as complete in DB (stopped before any cycles):`,
            dbCompleteError
          );
        }
      } else {
        // Edge case: Resumed action completed 0 cycles *in this run* AND wasn't stopped
        // This could happen if the action finished exactly during downtime.
        // The preRestart calculations should handle the reward granting if startingCycle > 0.
        // If startingCycle was 0 and completedCyclesInThisRun is 0, then nothing happened.
        console.log(
          `[${actionName}][Multi-Finalize] Action ID ${actionId} completed ${finalCompletedCyclesFixed} total cycles. No rewards simulated in this run. Pre-restart rewards (if any) were calculated. Check if finalization occurred.`
        );
        // Ensure the action is completed in DB if it had pre-restart cycles but finished during downtime
        if (actionId && startingCycle > 0 && !playerStopped) {
          console.log(
            `[${actionName}][Multi-Finalize] Action ID ${actionId} likely finished during downtime. Applying pre-restart rewards only.`
          );
          // let finalCharacterData = character; // OLD LINE
          const finalCharacterDataForDowntimeRewards = {
            ...currentCharacterLoopState,
          };
          try {
            // Apply only pre-restart rewards
            const finalTotalPetExp = calculateActionDerivedExp(
              preRestartExp,
              skillName,
              finalCharacterDataForDowntimeRewards
            );
            const rewardResults = await applyActionRewards(
              userId,
              finalCharacterDataForDowntimeRewards,
              skillName,
              preRestartExp,
              finalTotalPetExp,
              preRestartItems,
              preRestartCoins,
              undefined, // mobKey
              undefined, // killedCount
              [], // No accessories
              [], // No pets
              {} // No consumed items
            );
            // finalCharacterData = rewardResults.character; // OLD LINE
            const finalCharacterData = rewardResults.character;
            const updatedSkillData = rewardResults.updatedSkillData;
            const collectionNotifications =
              rewardResults.collectionNotifications;
            const allLevelUpEmbeds = rewardResults.allLevelUpEmbeds;

            // --- Construct Final Title (Multi-Action - Downtime Completion) ---
            const finalTitleDowntime = `${actionName} Results: x${startingCycle} ${resourceDisplayName} (Completed during downtime)`;

            // --- Create Result Embed (Multi-Action - Downtime Completion) ---
            let petEmoji = undefined;
            if (finalCharacterData.active_pet_id && finalCharacterData.pets) {
              const activePet = finalCharacterData.pets.find(
                (p) => p.id === finalCharacterData.active_pet_id
              );
              if (activePet) {
                const petData = configManager.getAllItems()[activePet.petKey];
                petEmoji = petData?.emoji || undefined;
              }
            }
            const resourceEmoji =
              resourceData && resourceData.emoji
                ? resourceData.emoji
                : emoji || "";
            const resourceDisplay = resourceEmoji ? `${resourceEmoji} ` : "";

            // Special handling for fishing which doesn't have a resourceKey
            let resourceQtyDowntime = "";
            if (skillName === "fishing") {
              resourceQtyDowntime = `**Fishing x${startingCycle}**`;
            } else {
              resourceQtyDowntime =
                resourceDisplayName && startingCycle
                  ? `**${resourceDisplayName} x${startingCycle}**`
                  : resourceDisplayName
                    ? `**${resourceDisplayName}**`
                    : "";
            }

            const pingEmbedDowntime = new EmbedBuilder()
              .setColor(colors.multiActionComplete)
              .setDescription(
                `Your ${resourceDisplay}${resourceQtyDowntime} ${actionName.toLowerCase()} action finished during downtime!`
              );
            const resultEmbedDowntime = createActionResultEmbed({
              title: finalTitleDowntime,
              color: colors.multiActionComplete,
              skillName: skillName,
              expGained: preRestartExp,
              petExpGained: finalTotalPetExp,
              tamingExpGained: getTamingExpFromPetExp(finalTotalPetExp),
              itemsGained: preRestartItems,
              petsGained: petsGained, // Add pets gained to downtime result
              finalCharacterData: finalCharacterData,
              finalSkillData: {
                ...updatedSkillData,
                exp: finalCharacterData.skills?.[skillName]?.exp || 0,
              },
              petEmoji, // Pass the pet emoji
              resourceName: resourceData?.name || resourceKey, // For non-combat skills
              resourceAmount: startingCycle,
              coinsGained: preRestartCoins, // Pass coins gained for downtime completion
              // REMOVED: ...(skillName === 'fishing' && baseCycleReward?.descriptionPrefix ? { descriptionPrefix: baseCycleReward.descriptionPrefix } : {})
            });

            try {
              // Always send downtime results as a new message so they appear as the latest thing in the channel
              const channel = interaction.channel;
              if (channel) {
                await channel.send({
                  embeds: [resultEmbedDowntime],
                });
                console.log(
                  `[${actionName}][Multi-Finalize] Downtime results sent as embed for better visibility`
                );

                // Delete the current action message now that results are posted
                // Use currentMessageId if available (after cycling), otherwise fallback to original messageId
                const messageIdToDelete = currentMessageId || messageId;
                if (messageIdToDelete) {
                  try {
                    await channel.messages.delete(messageIdToDelete);
                    console.log(
                      `[${actionName}][Multi-Finalize] Original downtime action message deleted for cleaner channel`
                    );
                  } catch (deleteError) {
                    console.warn(
                      `[${actionName}][Multi-Finalize] Could not delete original downtime action message:`,
                      deleteError.message
                    );
                  }
                }
              } else {
                console.error(
                  `[${actionName}][Multi-Finalize] No channel available to send downtime results for action ${actionId}`
                );
              }
            } catch (finalSendError) {
              console.error(
                `[${actionName}][Multi-Finalize] Failed to send downtime result message for action ${actionId}:`,
                finalSendError
              );
            }

            // Use targeted update to avoid overwriting minion storage and other concurrent changes
            await updatePlayerSkillDataAtomically(userId, finalCharacterData);
            // DEBUG: Log pet XP after saving (multi-action downtime)
            if (finalCharacterData.active_pet_id && finalCharacterData.pets) {
              const pet = finalCharacterData.pets.find(
                (p) => p.id === finalCharacterData.active_pet_id
              );
              if (pet) {
                console.log(
                  `[DEBUG][PetXP][Multi][Saved][Downtime] Skill: ${skillName}, Pet: ${
                    pet.petKey
                  }, Level: ${getPetLevel(pet)}, TotalExp: ${getPetTotalExp(
                    pet
                  )}`
                );
              }
            }
            // Debug log: print saved XP for the skill
            console.log(
              `[DEBUG][SAVE][Multi][Downtime] Saved XP for ${skillName}:`,
              finalCharacterData.skills?.[skillName]
            );

            // Ping user if they have pingOnMultiAction enabled - MOVED TO APPEAR FIRST
            console.log(
              `[PingMe][DEBUG] Checking ping for user ${userId} (downtime). settings:`,
              finalCharacterData.settings,
              "pingStates:",
              finalCharacterData.pingStates
            );
            if (
              finalCharacterData.settings &&
              finalCharacterData.settings.pingOnMultiAction &&
              startingCycle > 0
            ) {
              // Ping if any cycles completed before downtime
              if (
                !finalCharacterData.pingStates ||
                !finalCharacterData.pingStates[actionId]
              ) {
                try {
                  console.log(
                    `[PingMe][DEBUG] Sending ping for user ${userId} (actionId: ${actionId}) (downtime) in channel ${interaction.channel.id}`
                  );
                  // Send as a regular channel message instead of interaction follow-up
                  await interaction.channel.send({
                    content: `<@${userId}>`,
                    embeds: [pingEmbedDowntime],
                  });
                  if (!finalCharacterData.pingStates)
                    finalCharacterData.pingStates = {};
                  finalCharacterData.pingStates[actionId] = true;
                  // Player data with updated pingStates will be saved by the existing savePlayerData call shortly after this block
                } catch (pingErr) {
                  console.error(
                    `[PingMe] Failed to send ping for ${userId} (actionId: ${actionId}) (downtime):`,
                    pingErr
                  );
                }
              } else {
                console.log(
                  `[PingMe][DEBUG] Ping already sent for user ${userId} (actionId: ${actionId}) (downtime)`
                );
              }
            }

            // Add delay after downtime ping before other notifications
            if (
              finalCharacterData.settings?.pingOnMultiAction &&
              startingCycle > 0 &&
              (allLevelUpEmbeds.length > 0 ||
                collectionNotifications.length > 0 ||
                (skillName === "farming" &&
                  (totalGardenMilestones.length > 0 ||
                    (gardenLevelUpInfo && gardenLevelUpInfo.leveledUp))))
            ) {
              await new Promise((resolve) => setTimeout(resolve, 500));
            }

            // Send level up and collection notifications AFTER action ping
            await sendActionFollowups(
              interaction,
              allLevelUpEmbeds,
              collectionNotifications,
              rewardResults.pendingCoinRewards || 0
            );

            // Add delay before garden notifications
            if (
              skillName === "farming" &&
              (totalGardenMilestones.length > 0 ||
                (gardenLevelUpInfo && gardenLevelUpInfo.leveledUp)) &&
              (allLevelUpEmbeds.length > 0 ||
                collectionNotifications.length > 0)
            ) {
              await new Promise((resolve) => setTimeout(resolve, 500));
            }

            // Send garden milestone and level-up notifications for downtime completion (farming in Garden)
            if (
              skillName === "farming" &&
              (totalGardenMilestones.length > 0 ||
                (gardenLevelUpInfo && gardenLevelUpInfo.leveledUp))
            ) {
              try {
                const {
                  sendGardenNotifications,
                } = require("./gardenNotifications");
                await sendGardenNotifications(
                  interaction,
                  totalGardenMilestones,
                  gardenLevelUpInfo
                );
              } catch (gardenNotificationError) {
                console.error(
                  "[Skill Action] Error sending garden notifications for downtime completion:",
                  gardenNotificationError
                );
              }
            }

            // Check and notify for Disblock XP updates AFTER all other notifications (downtime completion)
            try {
              const {
                checkAndNotifyDisblockXP,
              } = require("./disblockXpSystem");
              await checkAndNotifyDisblockXP(userId, interaction);
            } catch (xpError) {
              console.error(
                `[Downtime] Error updating Disblock XP for user ${userId}:`,
                xpError
              );
              // Continue even if Disblock XP update fails
            }
          } catch (rewardError) {
            const errorCode = `[${skillName.toUpperCase()}-FIN-REW-002]`;
            console.error(
              `[${actionName}][Multi-Finalize][Downtime] Error applying downtime rewards/saving data for action ${actionId} (Completed Cycles: ${startingCycle}):`,
              rewardError
            );
            try {
              if (messageId) {
                await interaction.channel.messages.edit(messageId, {
                  content: `${errorCode} An error occurred finalizing the ${skillName} downtime results. Your progress might not be fully saved. Please report this code.`,
                  embeds: [],
                  components: [],
                });
              } else {
                console.error(
                  `[${actionName}][Multi-Finalize][Downtime] Message ID missing, cannot edit after downtime reward error for action ${actionId}`
                );
              }
            } catch (editErr) {
              console.error(
                `[${skillName.toUpperCase()}-REPLY-ERR-002] Also failed to edit message after downtime reward error:`,
                editErr
              );
            }
          }
          try {
            await completeAction(actionId);
            await clearActivity(userId); // Clear activity from active_actions table
          } catch (dbCompleteError) {
            const errorCode = `[${skillName.toUpperCase()}-FIN-CMP-002]`;
            console.error(
              `${errorCode} Failed to mark action ${actionId} as complete in DB (downtime):`,
              dbCompleteError
            );
          }
        }
      }
    }
  } catch (error) {
    const errorCode = `[${skillName.toUpperCase()}-HNDL-UNK-001]`;
    console.error(
      `[${actionName}][handleSkillActionExecution] Unhandled critical error for ${userId} (ActionID: ${
        actionId || "N/A"
      }):`,
      error
    );

    // Log crash to file for debugging
    logSkillActionCrash(error, {
      userId,
      skillName,
      actionId: actionId || "N/A",
      crashPoint: "top_level_handler",
      errorCode,
    }).catch((logErr) => {
      console.error("[CrashLogger] Failed to log top-level crash:", logErr);
    });
    try {
      if (!resultSent) {
        const errorMsg = `${errorCode} An unexpected critical error occurred while handling the ${skillName} action. Please report this code. (Details: ${
          error.message || "Unknown"
        })`;
        if (!interaction.replied && !interaction.deferred) {
          await interaction
            .reply({ content: errorMsg, flags: [MessageFlags.Ephemeral] })
            .catch(() => {});
        } else {
          // Use channel.send() for errors during long-running actions to avoid interaction timeout
          const channel = interaction.channel;
          if (channel) {
            await channel
              .send({
                content: `<@${interaction.user.id}> ${errorMsg}`,
              })
              .catch(() => {});
          }
        }
      }
    } catch (notifyError) {
      const notifyErrorCode = `[${skillName.toUpperCase()}-REPLY-ERR-001]`;
      console.error(
        `${notifyErrorCode} Failed to notify user about top-level error for action ${
          actionId || "N/A"
        }:`,
        notifyError
      );
    }
    // Try to clean up DB action if error occurred mid-way and ID exists
    if (actionId) {
      completeAction(actionId).catch((e) =>
        console.error(
          `[${actionName}][handleSkillActionExecution] Error trying to complete action ${actionId} in DB after top-level error:`,
          e
        )
      );
      clearActivity(userId).catch((e) =>
        console.error(
          `[${actionName}][handleSkillActionExecution] Error trying to clear activity for user ${userId} after top-level error:`,
          e
        )
      );
    }
  } finally {
    // --- Ensure activity lock is always cleared BEFORE sending repeat button ---
    try {
      await clearActivity(userId);
    } catch (clearError) {
      console.error(
        `[ActivityLock] Failed to clear activity for user ${userId}:`,
        clearError
      );
      // Log activity lock crash to file
      logActivityLockCrash(clearError, {
        userId,
        skillName,
        actionId: actionId || "N/A",
        operation: "clearActivity_finally",
      }).catch((logErr) => {
        console.error(
          "[CrashLogger] Failed to log activity lock crash:",
          logErr
        );
      });
    }
    // --- END ADDED ---
  }
}

module.exports = {
  calculateActionDerivedExp,
  calculateTotalActionExp,
  applyActionRewards,
  sendActionFollowups,
  sendEmbedsWithDelay,
  initiateSkillAction,
  setupNewMultiAction,
  handleSkillActionExecution,
};
