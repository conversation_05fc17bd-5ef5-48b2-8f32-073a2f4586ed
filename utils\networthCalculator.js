const { dbAll } = require("./dbUtils");
const configManager = require("./configManager");
const { safeJsonParse } = require("./playerDataManager");

// Import enchantments from the enchant command to avoid duplication
const { ENCHANTMENTS } = require("../commands/enchant");

// Import NPC data for prices
const NPCS = require("../data/npcs");

// Bee Pet rarity prices from Bea NPC (from talk.js)
const BEE_PET_RARITY_PRICES = {
  COMMON: 5000,
  UNCOMMON: 15000,
  RARE: 50000,
  EPIC: 200000,
  LEGENDARY: 650000,
};

// Helper function to get NPC price for an item
function getNPCPrice(itemKey, rarity = null) {
  // Special case for Bee Pet - use rarity-based pricing
  if (itemKey === "BEE_PET" && rarity) {
    return BEE_PET_RARITY_PRICES[rarity] || BEE_PET_RARITY_PRICES.COMMON;
  }

  // Check all NPCs for this item
  for (const npc of Object.values(NPCS)) {
    if (npc.shopInventory) {
      const shopItem = npc.shopInventory.find(
        (item) => item.itemKey === itemKey
      );
      if (shopItem) {
        return shopItem.price;
      }
    }
  }

  return 0;
}

// Helper function to calculate raw craft cost recursively
function calculateCraftCostRecursive(itemKey, allItems, visited) {
  if (visited.has(itemKey)) return 0; // Prevent infinite recursion
  visited.add(itemKey);

  const itemDef = allItems[itemKey];
  if (!itemDef) {
    visited.delete(itemKey);
    return 0;
  }

  // Priority 1: Calculate cost from recipe (raw craft cost)
  if (itemDef.recipes && itemDef.recipes.length > 0) {
    const recipe = itemDef.recipes[0];
    if (recipe.ingredients) {
      let totalCost = 0;
      for (const ingredient of recipe.ingredients) {
        const ingredientCost = calculateCraftCostRecursive(
          ingredient.itemKey,
          allItems,
          visited
        );
        totalCost += ingredientCost * (ingredient.amount || 1);
      }
      visited.delete(itemKey);
      return totalCost;
    }
  }

  // Priority 2: If item has a sell price, use that as the base cost
  if (typeof itemDef.sellPrice === "number") {
    visited.delete(itemKey);
    return itemDef.sellPrice;
  }

  // Priority 3: Check NPC prices for items without sellPrice or recipes
  const npcPrice = getNPCPrice(itemKey);
  if (npcPrice > 0) {
    visited.delete(itemKey);
    return npcPrice;
  }

  visited.delete(itemKey);
  return 0;
}

// Helper function to get total value of an enchantment
function getEnchantmentValue(enchantKey, level, enchantmentsData) {
  if (!enchantmentsData || !enchantmentsData[enchantKey]) return 0;
  const enchantDef = enchantmentsData[enchantKey];
  if (!enchantDef || !enchantDef.cost) return 0;
  let totalValue = 0;
  for (let i = 1; i <= level; i++) {
    totalValue += enchantDef.cost(i) || 0;
  }
  return totalValue;
}

// Helper function to calculate minion value based on cumulative craft/upgrade costs
function calculateMinionValue(minion, allItems) {
  const minionDef = allItems[minion.itemKey];
  if (!minionDef || !minionDef.tiers) return 0;

  let totalValue = 0;

  // Add base craft cost
  if (minionDef.recipes && minionDef.recipes.length > 0) {
    totalValue += calculateCraftCostRecursive(
      minion.itemKey,
      allItems,
      new Set()
    );
  }

  // Add cumulative upgrade costs from tier 2 to current tier
  for (let tier = 2; tier <= minion.tier; tier++) {
    const tierData = minionDef.tiers[tier];
    if (tierData && tierData.upgradeCost) {
      for (const ingredient of tierData.upgradeCost) {
        const ingredientCost = calculateCraftCostRecursive(
          ingredient.itemKey,
          allItems,
          new Set()
        );
        totalValue += ingredientCost * (ingredient.amount || 1);
      }
    }
  }

  // Add value of minion upgrades (compactors, storage upgrades, etc.)
  if (minion.upgrades && Array.isArray(minion.upgrades)) {
    for (const upgradeKey of minion.upgrades) {
      if (upgradeKey && allItems[upgradeKey]) {
        const upgradeItem = allItems[upgradeKey];
        if (upgradeItem.sellPrice) {
          totalValue += upgradeItem.sellPrice;
        } else if (upgradeItem.recipes && upgradeItem.recipes.length > 0) {
          totalValue += calculateCraftCostRecursive(
            upgradeKey,
            allItems,
            new Set()
          );
        }
      }
    }
  }

  return totalValue;
}

// Helper function to calculate pet value based on craft cost + Kat upgrade costs
function calculatePetValue(pet, allItems) {
  const petDef = allItems[pet.itemKey];
  if (!petDef) {
    console.log(`[Networth] Pet definition not found for ${pet.itemKey}`);
    return 0;
  }

  let baseValue = 0;

  // Special case for Bee Pet - use rarity-based NPC pricing
  if (pet.itemKey === "BEE_PET") {
    baseValue = getNPCPrice(pet.itemKey, pet.rarity);
  } else if (pet.itemKey === "SQUID_PET") {
    // Squid pets are fishing drops, use base value calculation
    baseValue = 0; // No direct crafting cost
  } else {
    // For other pets, use direct crafting recipe cost
    const petData = allItems[pet.itemKey];
    if (petData && petData.recipes && petData.recipes[0]) {
      const recipe = petData.recipes[0];
      if (recipe.ingredients) {
        for (const ingredient of recipe.ingredients) {
          const ingredientItem = allItems[ingredient.itemKey];
          if (ingredientItem && ingredientItem.sellPrice) {
            baseValue += ingredientItem.sellPrice * ingredient.amount;
          }
        }
      }
    }
  }

  // Note: Kat upgrade costs are no longer included in networth calculations
  // to prevent artificial inflation from easily craftable upgrades

  return baseValue;
}

// Helper function to get item value (base + enchantments + Hot Potato Books)
function getItemValue(itemObject, allItems, enchantmentsData, isPet = false) {
  const itemKeyToLookup = itemObject.item_key || itemObject.itemKey;
  if (!itemKeyToLookup) return 0;

  const itemDef = allItems[itemKeyToLookup];
  if (!itemDef) return 0;

  // Calculate both NPC price and craft cost, then use the higher value
  const npcPrice = getNPCPrice(itemKeyToLookup);
  const craftCost = calculateCraftCostRecursive(
    itemKeyToLookup,
    allItems,
    new Set()
  );
  const baseValue = Math.max(npcPrice, craftCost);

  let enchantValue = 0;
  let hotPotatoBookValue = 0;
  if (!isPet && itemObject.data_json) {
    let dataJson = itemObject.data_json;
    if (typeof dataJson === "string") {
      try {
        dataJson = JSON.parse(dataJson);
      } catch (e) {
        console.error(
          `[Networth] Error parsing data_json for item ${itemObject.item_key || itemObject.itemKey}:`,
          e
        );
        dataJson = {};
      }
    }
    if (dataJson.enchantments) {
      for (const [enchantKey, level] of Object.entries(dataJson.enchantments)) {
        enchantValue += getEnchantmentValue(
          enchantKey,
          level,
          enchantmentsData
        );
      }
    }
    if (dataJson.hotPotatoBooks) {
      // Hot Potato Books have a sell price of 51,200 coins each
      const hotPotatoBookSellPrice =
        allItems["HOT_POTATO_BOOK"]?.sellPrice || 51200;
      hotPotatoBookValue = dataJson.hotPotatoBooks * hotPotatoBookSellPrice;
    }
  }
  return baseValue + enchantValue + hotPotatoBookValue;
}

/**
 * Calculate comprehensive networth for a player
 * @param {string} discordId - The Discord ID of the player
 * @returns {Promise<number>} The player's total networth
 */
async function calculatePlayerNetworth(discordId) {
  try {
    let allItems = configManager.getAllItems();

    // If configManager isn't initialized (empty items), try to initialize it
    if (Object.keys(allItems).length === 0) {
      console.log("[Networth] ConfigManager not initialized, initializing...");
      await configManager.initialize();
      allItems = configManager.getAllItems();
    }

    // Get player data
    const playerData = await dbAll(
      "SELECT coins, bank, pets_json, island_json, minion_storage_json, cakeBag, pet_upgrade_json FROM players WHERE discord_id = ?",
      [discordId]
    );
    if (!playerData || playerData.length === 0) return 0;

    const player = playerData[0];
    let networth = (player.coins || 0) + (player.bank || 0);

    // Value equipment and storage
    const playerEquipment = await dbAll(
      "SELECT item_key, is_equipped, data_json FROM player_equipment WHERE discord_id = ?",
      [discordId]
    );
    playerEquipment.forEach((equipItem) => {
      const itemDef = allItems[equipItem.item_key];
      if (!itemDef) return;

      if (equipItem.is_equipped) {
        networth += getItemValue(equipItem, allItems, ENCHANTMENTS);
      } else {
        // Item is in storage
        if (itemDef.unique) {
          // Only count unique items in storage
          networth += getItemValue(equipItem, allItems, ENCHANTMENTS);
        }
      }
    });

    // Value accessories (both equipped and unequipped)
    const playerAccessories = await dbAll(
      "SELECT item_key, is_equipped, data_json FROM player_accessories WHERE discord_id = ?",
      [discordId]
    );
    playerAccessories.forEach((accessory) => {
      const itemDef = allItems[accessory.item_key];
      if (!itemDef) return;

      // Include all accessories in networth calculation (both equipped and unequipped)
      networth += getItemValue(accessory, allItems, ENCHANTMENTS);
    });

    // Value stackable items
    const playerStackableItems = await dbAll(
      "SELECT item_name, amount FROM player_inventory_items WHERE discord_id = ?",
      [discordId]
    );
    playerStackableItems.forEach((stackableItem) => {
      const itemDef = allItems[stackableItem.item_name];
      if (
        itemDef &&
        typeof itemDef.sellPrice === "number" &&
        stackableItem.amount > 0
      ) {
        networth += itemDef.sellPrice * stackableItem.amount;
      }
    });

    // Value pets using comprehensive cost-based system with enhanced fallback
    const pets = safeJsonParse(player.pets, []);
    if (Array.isArray(pets)) {
      pets.forEach((pet) => {
        if (pet && pet.itemKey && pet.rarity) {
          // Standard case: pet has proper itemKey
          networth += calculatePetValue(pet, allItems);
        } else if (pet && pet.rarity) {
          // Fallback case: try to determine pet type from various sources
          let petItemKey = null;

          // Method 1: Check if pet has 'petKey' field (most common)
          if (pet.petKey) {
            petItemKey = pet.petKey;
          }
          // Method 2: Check if id contains '_PET_' pattern
          else if (pet.id && pet.id.includes("_PET_")) {
            petItemKey = pet.id.split("_PET_")[0] + "_PET";
          }
          // Method 3: Check if pet has a 'type' field
          else if (pet.type) {
            petItemKey = pet.type.toUpperCase() + "_PET";
          }
          // Method 4: Check if pet has a 'name' field that indicates type
          else if (pet.name) {
            const nameUpper = pet.name.toUpperCase();
            // Common pet name patterns
            const petTypeMap = {
              CHICKEN: "CHICKEN_PET",
              RABBIT: "RABBIT_PET",
              BEE: "BEE_PET",
              SILVERFISH: "SILVERFISH_PET",
              SQUID: "SQUID_PET",
              WOLF: "WOLF_PET",
              CAT: "CAT_PET",
              PARROT: "PARROT_PET",
              PIG: "PIG_PET",
              SHEEP: "SHEEP_PET",
              COW: "COW_PET",
              HORSE: "HORSE_PET",
            };

            for (const [petName, petKey] of Object.entries(petTypeMap)) {
              if (nameUpper.includes(petName)) {
                petItemKey = petKey;
                break;
              }
            }
          }

          // If we found a valid pet item key, calculate its value
          if (petItemKey && allItems[petItemKey]) {
            const fallbackPet = {
              itemKey: petItemKey,
              rarity: pet.rarity || "COMMON",
              level: pet.level || 1,
              totalExp: pet.totalExp || 0,
            };
            networth += calculatePetValue(fallbackPet, allItems);
          } else {
            console.warn("[calculatePlayerNetworth] Unrecognized pet:", {
              id: pet.id,
              petKey: pet.petKey,
              type: pet.type,
              name: pet.name,
              rarity: pet.rarity,
            });
          }
        }
      });
    }

    // Value pets being upgraded at Kat
    const petUpgrade = safeJsonParse(player.petUpgrade, null);
    // Handle both null and empty object cases (empty object can occur due to database storage issues)
    const validPetUpgrade =
      !petUpgrade ||
      (typeof petUpgrade === "object" && Object.keys(petUpgrade).length === 0)
        ? null
        : petUpgrade;
    if (
      validPetUpgrade &&
      validPetUpgrade.petKey &&
      validPetUpgrade.targetRarity
    ) {
      // Calculate value based on the target rarity (what the pet will become)
      const upgradingPet = {
        itemKey: validPetUpgrade.petKey,
        rarity: validPetUpgrade.targetRarity,
      };
      networth += calculatePetValue(upgradingPet, allItems);
    }

    // Value placed minions
    const islandData = safeJsonParse(player.island, {});
    const placedMinions = islandData.placedMinions || [];
    placedMinions.forEach((minion) => {
      if (minion && minion.itemKey && minion.tier) {
        networth += calculateMinionValue(minion, allItems);
      }
    });

    // Value minions in storage
    const minionStorage = safeJsonParse(player.minionStorage, []);
    minionStorage.forEach((minion) => {
      if (minion && minion.itemKey && minion.tier) {
        networth += calculateMinionValue(minion, allItems);
      }
    });

    // Value cake bag items (New Year Cakes)
    const cakeBag = safeJsonParse(player.cakeBag, []);
    if (Array.isArray(cakeBag) && cakeBag.length > 0) {
      const cakeItem = allItems["NEW_YEAR_CAKE_BASE"];
      if (cakeItem) {
        // Each cake is unique and valuable - assign a base value
        // Since they're collectible and limited, give them a reasonable value
        const cakeValue = cakeItem.sellPrice || 10000; // 10k coins per cake if no sell price
        networth += cakeValue * cakeBag.length;
      }
    }

    return Math.floor(networth);
  } catch (error) {
    console.error("[calculatePlayerNetworth] Error:", error);
    return 0;
  }
}

module.exports = {
  calculateCraftCostRecursive,
  calculateMinionValue,
  calculatePetValue,
  getItemValue,
  ENCHANTMENTS,
  getNPCPrice,
  getEnchantmentValue,
  calculatePlayerNetworth,
};
