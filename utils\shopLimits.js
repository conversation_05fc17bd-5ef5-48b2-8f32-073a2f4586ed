// Shop Limits System - handles per-item purchase limits for NPCs
// Limits reset every 3 DisBlock days
// test, hello :)

const { getDisBlockDateTime } = require("./timeUtils");
const { safeJsonParse } = require("./playerDataManager");
const { dbGet, dbRunQueued } = require("./dbUtils");

/**
 * Get the current DisBlock day identifier for resetting limits
 * Limits reset every 3 DisBlock days
 * @param {object} timeState - Current time state from bot.js
 * @returns {string} - Period identifier like "2025-5-period-4" (every 3 days)
 */
function getCurrentDisBlockDay(timeState) {
  const dateTime = getDisBlockDateTime(timeState);

  // calculate which 3-day period we're in
  // day 1-3 = period 1, day 4-6 = period 2, etc.
  const dayOfMonth = dateTime.day;
  const period = Math.ceil(dayOfMonth / 3);

  return `${dateTime.year}-${dateTime.month}-period-${period}`;
}

/**
 * Get default item limits for an NPC shop item
 * Simple universal limit - 640 per 3 DisBlock days for all items
 * @param {string} _itemKey - The item key (e.g., "WHEAT", "IRON_INGOT")
 * @param {object} _npc - The NPC data
 * @returns {number} - Purchase limit (640 for all items every 3 DisBlock days)
 */
function getDefaultItemLimit(_itemKey, _npc) {
  // universal limit for all items in any npc shop
  return 640;
}

/**
 * Get or initialize shop limits for a player
 * @param {string} userId - Discord user ID
 * @param {object} timeState - Current time state
 * @returns {Promise<object>} - Shop limits object
 */
async function getPlayerShopLimits(userId, timeState) {
  //console.log(`[ShopLimits] Getting player shop limits for user ${userId}`);

  // Use direct database query like other systems do
  const playerData = await dbGet(
    "SELECT shop_limits_json FROM players WHERE discord_id = ?",
    [userId]
  );

  let shopLimits = {};

  if (playerData) {
    shopLimits = safeJsonParse(playerData.shop_limits_json, {});
  } else {
    console.warn(`[ShopLimits] No player data found for user ${userId}`);
  }

  // Clean up old day data (keep only current and previous day)
  const currentDay = getCurrentDisBlockDay(timeState);
  const cleanedLimits = {};

  // Only keep current day data
  if (shopLimits[currentDay]) {
    cleanedLimits[currentDay] = shopLimits[currentDay];
    //console.log(`[ShopLimits] Cleaned limits for current period:`, cleanedLimits);
  } else {
    //console.log(`[ShopLimits] No data found for current period ${currentDay}`);
  }

  return cleanedLimits;
}

/**
 * Check if a player can purchase an item from an NPC
 * @param {string} userId - Discord user ID
 * @param {string} npcKey - NPC identifier
 * @param {string} itemKey - Item key to purchase
 * @param {number} quantity - Quantity to purchase
 * @param {object} npc - NPC data object
 * @param {object} timeState - Current time state
 * @returns {Promise<object>} - { canPurchase: boolean, remainingLimit: number, message?: string }
 */
async function checkPurchaseLimit(
  userId,
  npcKey,
  itemKey,
  quantity,
  npc,
  timeState
) {
  //console.log(`[ShopLimits] Checking purchase limit for user ${userId}, NPC ${npcKey}, item ${itemKey}, quantity ${quantity}`);

  const shopLimits = await getPlayerShopLimits(userId, timeState);
  const currentDay = getCurrentDisBlockDay(timeState);

  // Get the default limit for this item (640 every 3 DisBlock days)
  const dailyLimit = getDefaultItemLimit(itemKey, npc);

  // Get current purchases for this day
  const dayLimits = shopLimits[currentDay] || {};
  const npcLimits = dayLimits[npcKey] || {};
  const currentPurchases = npcLimits[itemKey] || 0;

  //console.log(`[ShopLimits] Current period: ${currentDay}, current purchases for ${itemKey}: ${currentPurchases}, limit: ${dailyLimit}`);

  const remainingLimit = dailyLimit - currentPurchases;

  if (quantity <= remainingLimit) {
    return {
      canPurchase: true,
      remainingLimit: remainingLimit - quantity,
    };
  } else {
    return {
      canPurchase: false,
      remainingLimit: remainingLimit,
      message:
        `❌ **Shop Limit Exceeded!**\n` +
        `**${itemKey}** has a limit of **${dailyLimit}** every 3 DisBlock days.\n` +
        `Trying to buy: **${quantity}** | Available: **${remainingLimit}**\n\n` +
        `💡 Use \`"max"\` to buy the maximum allowed amount.`,
    };
  }
}

/**
 * Record a purchase in the player's shop limits
 * @param {string} userId - Discord user ID
 * @param {string} npcKey - NPC identifier
 * @param {string} itemKey - Item key purchased
 * @param {number} quantity - Quantity purchased
 * @param {object} timeState - Current time state
 * @returns {Promise<void>}
 */
async function recordPurchase(userId, npcKey, itemKey, quantity, timeState) {
  //console.log(`[ShopLimits] Recording purchase for user ${userId}, NPC ${npcKey}, item ${itemKey}, quantity ${quantity}`);

  const shopLimits = await getPlayerShopLimits(userId, timeState);
  const currentDay = getCurrentDisBlockDay(timeState);

  // Initialize nested structure if needed
  if (!shopLimits[currentDay]) {
    shopLimits[currentDay] = {};
  }
  if (!shopLimits[currentDay][npcKey]) {
    shopLimits[currentDay][npcKey] = {};
  }
  if (!shopLimits[currentDay][npcKey][itemKey]) {
    shopLimits[currentDay][npcKey][itemKey] = 0;
  }

  // Record the purchase
  shopLimits[currentDay][npcKey][itemKey] += quantity;

  //console.log(`[ShopLimits] Updated purchases for ${itemKey}: ${shopLimits[currentDay][npcKey][itemKey]}`);

  // Save back to database using direct SQL like other systems do
  await dbRunQueued(
    "UPDATE players SET shop_limits_json = ? WHERE discord_id = ?",
    [JSON.stringify(shopLimits), userId]
  );

  //console.log(`[ShopLimits] Saved shop limits: ${JSON.stringify(shopLimits)}`);
}

/**
 * Get remaining limits for all items in an NPC's shop
 * @param {string} userId - Discord user ID
 * @param {object} npc - NPC data object
 * @param {object} timeState - Current time state
 * @returns {Promise<object>} - Object with itemKey -> remainingLimit mapping
 */
async function getNPCShopLimits(userId, npc, timeState) {
  const shopLimits = await getPlayerShopLimits(userId, timeState);
  const currentDay = getCurrentDisBlockDay(timeState);
  const npcKey = npc.key;

  //console.log(`[ShopLimits] Getting shop limits for user ${userId}, NPC ${npcKey}, period ${currentDay}`);
  //console.log(`[ShopLimits] Current shop limits data:`, JSON.stringify(shopLimits));

  const result = {};

  // Process each item in the NPC's shop
  if (npc.shopInventory && Array.isArray(npc.shopInventory)) {
    for (const shopItem of npc.shopInventory) {
      const itemKey = shopItem.itemKey;
      const dailyLimit = getDefaultItemLimit(itemKey, npc);

      const dayLimits = shopLimits[currentDay] || {};
      const npcLimits = dayLimits[npcKey] || {};
      const currentPurchases = npcLimits[itemKey] || 0;
      result[itemKey] = Math.max(0, dailyLimit - currentPurchases);

      //console.log(`[ShopLimits] Item ${itemKey}: purchased ${currentPurchases}, remaining ${result[itemKey]}`);
    }
  }

  //console.log(`[ShopLimits] Final result:`, JSON.stringify(result));
  return result;
}

/**
 * Format remaining limits for display
 * @param {object} limits - Limits object from getNPCShopLimits
 * @param {object} allItems - All items config
 * @returns {string} - Formatted string for display
 */
function formatShopLimits(limits, allItems) {
  const lines = [];

  for (const [itemKey, remaining] of Object.entries(limits)) {
    const itemData = allItems[itemKey];
    const itemName = itemData?.name || itemKey;
    const emoji = itemData?.emoji || "❓";

    if (remaining === 0) {
      lines.push(`${emoji} **${itemName}** : ❌ Limit reached`);
    } else {
      lines.push(`${emoji} **${itemName}** : ${remaining} remaining`);
    }
  }

  return lines.join("\n") || "No items available.";
}

module.exports = {
  getCurrentDisBlockDay,
  getDefaultItemLimit,
  getPlayerShopLimits,
  checkPurchaseLimit,
  recordPurchase,
  getNPCShopLimits,
  formatShopLimits,
};
