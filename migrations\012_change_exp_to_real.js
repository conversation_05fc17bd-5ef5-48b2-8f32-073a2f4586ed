// Utilities not needed - using custom promisified helpers instead

// Helper to promisify db.run
const run = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        console.error("Error running sql " + sql);
        console.error(err);
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
};

// Helper to promisify db.get
const get = (db, sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        console.error("Error running sql get " + sql);
        console.error(err);
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

// Migration UP
async function up(db) {
  console.log("Applying migration: 012_change_exp_to_real...");

  // --- Modify player_skills table ---
  console.log("  -> Modifying player_skills table...");
  try {
    // SQLite doesn't directly support ALTER COLUMN TYPE. Common workaround:
    // 1. Rename old table
    await run(db, "ALTER TABLE player_skills RENAME TO player_skills_old;");
    // 2. Create new table with correct schema
    await run(
      db,
      `
            CREATE TABLE player_skills (
                discord_id TEXT NOT NULL,
                skill_name TEXT NOT NULL,
                exp REAL DEFAULT 0,  -- Changed to REAL
                level INTEGER DEFAULT 1,
                PRIMARY KEY (discord_id, skill_name)
            );
        `,
    );
    // 3. Copy data from old table to new table
    await run(
      db,
      "INSERT INTO player_skills (discord_id, skill_name, exp, level) SELECT discord_id, skill_name, exp, level FROM player_skills_old;",
    );
    // 4. Drop old table
    await run(db, "DROP TABLE player_skills_old;");
    console.log("  -> Successfully changed player_skills.exp to REAL.");
  } catch (err) {
    console.error("  -> ERROR modifying player_skills table:", err);
    throw err; // Re-throw error to trigger rollback
  }

  // --- Modify player_pets table (Conditional) ---
  console.log("  -> Checking if player_pets table exists...");
  const petTableExists = await get(
    db,
    "SELECT name FROM sqlite_master WHERE type='table' AND name='player_pets';",
  );

  if (petTableExists) {
    console.log("  -> Modifying player_pets table...");
    try {
      await run(db, "ALTER TABLE player_pets RENAME TO player_pets_old;");
      await run(
        db,
        `
                 CREATE TABLE player_pets (
                     discord_id TEXT NOT NULL,
                     pet_id TEXT NOT NULL PRIMARY KEY,
                     pet_key TEXT NOT NULL,
                     rarity TEXT,
                     level INTEGER DEFAULT 1,
                     exp REAL DEFAULT 0, -- Changed to REAL
                     active INTEGER DEFAULT 0,
                     timestamp INTEGER
                 );
             `,
      );
      await run(
        db,
        "INSERT INTO player_pets (discord_id, pet_id, pet_key, rarity, level, exp, active, timestamp) SELECT discord_id, pet_id, pet_key, rarity, level, exp, active, timestamp FROM player_pets_old;",
      );
      await run(db, "DROP TABLE player_pets_old;");
      console.log("  -> Successfully changed player_pets.exp to REAL.");
    } catch (err) {
      console.error("  -> ERROR modifying player_pets table:", err);
      throw err;
    }
  } else {
    console.log(
      "  -> Skipping player_pets modification as table does not exist.",
    );
  }

  console.log("Migration 012 applied successfully.");
}

module.exports = { up };
