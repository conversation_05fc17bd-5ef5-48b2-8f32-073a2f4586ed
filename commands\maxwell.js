const { EMBED_COLORS, WARP_FOOTER } = require("../gameConfig.js");
const {
  <PERSON>lash<PERSON>ommandBuilder,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  MessageFlags,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");
const { dbRunQueued } = require("../utils/dbUtils");
const {
  ACCESSORY_POWERS,
  calculateAccessoryStat,
} = require("../utils/accessoryPowers");
const { calculateMagicalPower } = require("../utils/magicalPowerUtils");
const { getStatEmoji } = require("../utils/statUtils");
const { isBoosterCookieActive } = require("../utils/boosterCookieManager");

function getPowerBonusText(power, magicalPower) {
  const sortedStats = STAT_DISPLAY_ORDER.filter(
    (statName) => power.stats[statName]
  ).map((statName) => {
    const statData = power.stats[statName];
    const statValue = calculateStat(
      statData.basePower,
      statData.multiplier,
      magicalPower
    );
    const statEmoji = getStatEmoji(statName);
    return `${statEmoji}+${statValue.toFixed(1)}`;
  });

  return sortedStats.join(" ");
}

const STAT_DISPLAY_ORDER = [
  "HEALTH",
  "DEFENSE",
  "STRENGTH",
  "CRIT_CHANCE",
  "CRIT_DAMAGE",
  "INTELLIGENCE",
];

function calculateStat(basePower, statMultiplier, magicalPower) {
  return (
    Math.round(
      calculateAccessoryStat(basePower, statMultiplier, magicalPower) * 10
    ) / 10
  );
}

async function getCurrentAccessoryPower(userId) {
  try {
    const playerData = await getPlayerData(userId);
    return playerData?.accessory_power || null;
  } catch (error) {
    console.error("[getCurrentAccessoryPower] Error:", error);
    return null;
  }
}

async function setAccessoryPower(userId, powerKey) {
  try {
    await dbRunQueued(
      `UPDATE players SET accessory_power = ? WHERE discord_id = ?`,
      [powerKey, userId]
    );
    return true;
  } catch (error) {
    console.error("[setAccessoryPower] Error:", error);
    return false;
  }
}

async function returnToMainInterface(interaction) {
  const userId = interaction.user.id;

  try {
    if (interaction.replied || interaction.deferred) {
      console.warn("[returnToMainInterface] Interaction already handled");
      return;
    }

    const magicalPower = await calculateMagicalPower(userId);
    const currentPower = await getCurrentAccessoryPower(userId);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PURPLE)
      .setTitle("<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist")
      .addFields(
        {
          name: "Your Magical Power",
          value: `<:accessory_bag:1384159284123664516> ${magicalPower.toLocaleString()} MP`,
          inline: false,
        },
        {
          name: "Current Power",
          value: currentPower
            ? `\`${ACCESSORY_POWERS[currentPower]?.name || "Unknown"}\``
            : "None Selected",
          inline: false,
        }
      );

    if (currentPower && ACCESSORY_POWERS[currentPower]) {
      const power = ACCESSORY_POWERS[currentPower];
      const bonusText = getPowerBonusText(power, magicalPower);

      embed.addFields({
        name: "Current Bonus",
        value: bonusText,
        inline: false,
      });
    } else {
      embed.addFields({
        name: "Current Bonus",
        value: "No Power selected",
        inline: false,
      });
    }

    const powerOptions = Object.entries(ACCESSORY_POWERS).map(
      ([key, power]) => ({
        label: power.name,
        value: key,
        description:
          power.description.length > 100
            ? power.description.substring(0, 97) + "..."
            : power.description,
      })
    );

    powerOptions.unshift({
      label: "No Power",
      value: "NO_POWER",
      description: "Remove your current accessory power",
      emoji: "❌",
    });

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("maxwell_power_select")
      .setPlaceholder("Select a power...")
      .addOptions(powerOptions);

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const buttonRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("maxwell_view_powers")
        .setLabel("View All Powers")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId("maxwell_close")
        .setLabel("Close")
        .setEmoji("🚪")
        .setStyle(ButtonStyle.Secondary)
    );

    await interaction.update({
      embeds: [embed],
      components: [selectRow, buttonRow],
    });
  } catch (error) {
    console.error("[returnToMainInterface] Error:", error);
    if (!interaction.replied && !interaction.deferred) {
      try {
        await interaction.update({
          content:
            "An error occurred while accessing Maxwell. Please try again.",
          embeds: [],
          components: [],
        });
      } catch (updateError) {
        console.error(
          "[returnToMainInterface] Failed to update interaction:",
          updateError
        );
      }
    }
  }
}

async function handleMaxwellInteraction(interaction) {
  const userId = interaction.user.id;

  try {
    const playerData = await getPlayerData(userId);

    // check if player is in The Hub or has active booster cookie
    const hasActiveBoosterCookie = isBoosterCookieActive(playerData);
    const isInHub = playerData.current_region === "the_hub";

    if (!isInHub && !hasActiveBoosterCookie) {
      const locationEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.PINK_RED)
        .setDescription(
          "Maxwell is only available in **The Hub**.\n\n💡 **Tip:** You can access Maxwell from anywhere with an active Booster Cookie!"
        )
        .setFooter({ text: WARP_FOOTER });

      return interaction.reply({
        embeds: [locationEmbed],
      });
    }

    const magicalPower = await calculateMagicalPower(userId);
    const currentPower = await getCurrentAccessoryPower(userId);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PURPLE)
      .setTitle("<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist")
      .addFields(
        {
          name: "Your Magical Power",
          value: `<:accessory_bag:1384159284123664516> **${magicalPower.toLocaleString()} MP**`,
          inline: false,
        },
        {
          name: "Current Power",
          value: currentPower
            ? `\`${ACCESSORY_POWERS[currentPower]?.name || "Unknown"}\``
            : "None Selected",
          inline: false,
        }
      );

    if (currentPower && ACCESSORY_POWERS[currentPower]) {
      const power = ACCESSORY_POWERS[currentPower];
      const bonusText = getPowerBonusText(power, magicalPower);

      embed.addFields({
        name: "Current Bonus",
        value: bonusText,
        inline: false,
      });
    } else {
      embed.addFields({
        name: "Current Bonus",
        value: "No Power selected",
        inline: false,
      });
    }

    const powerOptions = Object.entries(ACCESSORY_POWERS).map(
      ([key, power]) => ({
        label: power.name,
        value: key,
        description:
          power.description.length > 100
            ? power.description.substring(0, 97) + "..."
            : power.description,
      })
    );

    powerOptions.unshift({
      label: "No Power",
      value: "NO_POWER",
      description: "Remove your current accessory power",
      emoji: "❌",
    });

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("maxwell_power_select")
      .setPlaceholder("Select Power")
      .addOptions(powerOptions);

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);

    const buttonRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("maxwell_view_powers")
        .setLabel("View All Powers")
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId("maxwell_close")
        .setLabel("Close")
        .setEmoji("🚪")
        .setStyle(ButtonStyle.Secondary)
    );

    await interaction.reply({
      embeds: [embed],
      components: [selectRow, buttonRow],
    });

    const reply = await interaction.fetchReply();
    let isManuallyClosed = false;
    const collector = reply.createMessageComponentCollector({
      filter: (i) => i.user.id === userId,
      time: 300000,
    });

    collector.on("collect", async (i) => {
      try {
        if (i.customId === "maxwell_power_select") {
          const selectedPower = i.values[0];

          if (selectedPower === "NO_POWER") {
            const success = await setAccessoryPower(userId, null);

            if (success) {
              const newMagicalPower = await calculateMagicalPower(userId);

              const newEmbed = new EmbedBuilder()
                .setColor(EMBED_COLORS.PURPLE)
                .setTitle(
                  "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist"
                )
                .addFields(
                  {
                    name: "Your Magical Power",
                    value: `<:accessory_bag:1384159284123664516> **${newMagicalPower.toLocaleString()} MP**`,
                    inline: false,
                  },
                  {
                    name: "Current Power",
                    value: "None Selected",
                    inline: false,
                  },
                  {
                    name: "Current Bonus",
                    value: "No Power selected",
                    inline: false,
                  }
                );

              await i.update({
                embeds: [newEmbed],
                components: [selectRow, buttonRow],
              });
            } else {
              if (!i.replied && !i.deferred) {
                await i.reply({
                  content:
                    "Failed to remove your accessory power. Please try again.",
                  flags: [MessageFlags.Ephemeral],
                });
              }
            }
            return;
          }

          const power = ACCESSORY_POWERS[selectedPower];

          if (!power) {
            if (!i.replied && !i.deferred) {
              await i.reply({
                content: "Invalid power selected. Please try again.",
                flags: [MessageFlags.Ephemeral],
              });
            }
            return;
          }

          const success = await setAccessoryPower(userId, selectedPower);

          if (success) {
            const newMagicalPower = await calculateMagicalPower(userId);

            const newEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.PURPLE)
              .setTitle(
                "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist"
              )
              .addFields(
                {
                  name: "Your Magical Power",
                  value: `<:accessory_bag:1384159284123664516> **${newMagicalPower.toLocaleString()} MP**`,
                  inline: false,
                },
                {
                  name: "Current Power",
                  value: `\`${power.name}\``,
                  inline: false,
                }
              );

            const bonusText = getPowerBonusText(power, newMagicalPower);
            newEmbed.addFields({
              name: "Current Bonus",
              value: bonusText,
              inline: false,
            });

            await i.update({
              embeds: [newEmbed],
              components: [selectRow, buttonRow],
            });
          } else {
            if (!i.replied && !i.deferred) {
              await i.reply({
                content:
                  "Failed to set your accessory power. Please try again.",
                flags: [MessageFlags.Ephemeral],
              });
            }
          }
        } else if (i.customId === "maxwell_view_powers") {
          await handleViewAllPowers(i, magicalPower, collector);
        } else if (i.customId === "maxwell_close") {
          isManuallyClosed = true;
          const closeEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.PURPLE)
            .setTitle(
              "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist"
            )
            .setDescription("Maxwell session closed.");

          await i.update({
            embeds: [closeEmbed],
            components: [],
          });
          collector.stop();
        }
      } catch (error) {
        console.error("[Maxwell Collector] Error:", error);
        if (!i.replied && !i.deferred) {
          await i.reply({
            content: "An error occurred. Please try again.",
            flags: [MessageFlags.Ephemeral],
          });
        }
      }
    });

    collector.on("end", () => {
      if (!isManuallyClosed) {
        const disabledSelectRow = new ActionRowBuilder().addComponents(
          StringSelectMenuBuilder.from(selectRow.components[0]).setDisabled(
            true
          )
        );
        const disabledButtonRow = new ActionRowBuilder().addComponents(
          buttonRow.components.map((button) =>
            ButtonBuilder.from(button).setDisabled(true)
          )
        );

        interaction
          .editReply({ components: [disabledSelectRow, disabledButtonRow] })
          .catch(() => {});
      }
    });
  } catch (error) {
    console.error("[handleMaxwellInteraction] Error:", error);
    await interaction.reply({
      content: "An error occurred while accessing Maxwell. Please try again.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/* async function handlePowerSelection(interaction, magicalPower) {
  const options = Object.entries(ACCESSORY_POWERS).map(([key, power]) => {
    const bonusText = getPowerBonusText(power, magicalPower);
    return {
      label: `${power.name}`,
      description: `${power.description} | ${bonusText}`,
      value: key,
      emoji: power.emoji,
    };
  });

  // Add "No Power" option
  options.unshift({
    label: "No Power",
    description: "Remove your current accessory power",
    value: "NO_POWER",
    emoji: "❌",
  });

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("maxwell_power_select")
    .setPlaceholder("Choose an Accessory Power...")
    .addOptions(options);

  const selectRow = new ActionRowBuilder().addComponents(selectMenu);

  // Add back button
  const backRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("maxwell_back")
      .setLabel("Back")
      .setEmoji("◀️")
      .setStyle(ButtonStyle.Secondary)
  );

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PURPLE)
    .setTitle(
      "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist - Select Accessory Power"
    )
    .addFields(
      {
        name: "Your Magical Power",
        value: `<:accessory_bag:1384159284123664516> **${magicalPower.toLocaleString()} MP**`,
        inline: true,
      },
      {
        name: "Available Powers",
        value:
          "Select a power below to convert your Magical Power into stat bonuses.",
        inline: false,
      }
    )
    .setFooter({ text: "Choose a power from the dropdown menu below" });

  await interaction.update({
    embeds: [embed],
    components: [selectRow, backRow],
  });

  // Create collector for select menu and back button
  const reply = await interaction.fetchReply();
  const selectCollector = reply.createMessageComponentCollector({
    filter: i =>
      (i.customId === "maxwell_power_select" ||
        i.customId === "maxwell_back") &&
      i.user.id === interaction.user.id,
    time: 60000, // 1 minute
  });

  selectCollector.on("collect", async i => {
    try {
      if (i.customId === "maxwell_back") {
        // Return to main Maxwell interface
        selectCollector.stop(); // Stop this collector
        await returnToMainInterface(i);
        return;
      }

      const selectedPower = i.values[0];

      if (selectedPower === "NO_POWER") {
        // Handle "No Power" selection
        const success = await setAccessoryPower(interaction.user.id, null);

        if (success) {
          const successEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.PINK_RED)
            .setTitle(
              "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist - Power Removed!"
            )
            .addFields(
              {
                name: "Selected Power",
                value: "❌ **No Power**",
                inline: true,
              },
              {
                name: "Stat Bonuses",
                value: "None",
                inline: true,
              },
              {
                name: "Magical Power",
                value: `<:accessory_bag:1384159284123664516> **${magicalPower.toLocaleString()} MP** (Unused)`,
                inline: true,
              }
            )
            .setDescription("Your Accessory Power has been removed!")
            .setFooter({ text: "Use /stats to see your updated bonuses" });

          selectCollector.stop(); // Stop collector after successful selection
          await i.update({ embeds: [successEmbed], components: [] });
        } else {
          if (!i.replied && !i.deferred) {
            await i.reply({
              content:
                "Failed to remove your accessory power. Please try again.",
              flags: [MessageFlags.Ephemeral],
            });
          }
        }
        return;
      }

      const power = ACCESSORY_POWERS[selectedPower];

      if (!power) {
        if (!i.replied && !i.deferred) {
          await i.reply({
            content: "Invalid power selected. Please try again.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        return;
      }

      // Set the new power
      const success = await setAccessoryPower(
        interaction.user.id,
        selectedPower
      );

      if (success) {
        const bonusText = getPowerBonusText(power, magicalPower);

        const successEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setTitle(
            "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist - Power Selected!"
          )
          .addFields(
            {
              name: "Selected Power",
              value: `${power.emoji} **${power.name}**`,
              inline: true,
            },
            {
              name: "Stat Bonuses",
              value: bonusText,
              inline: true,
            },
            {
              name: "Magical Power Used",
              value: `<:accessory_bag:1384159284123664516> **${magicalPower.toLocaleString()} MP**`,
              inline: true,
            }
          )
          .setDescription("Your Accessory Power has been successfully updated!")
          .setFooter({ text: "Use /stats to see your updated bonuses" });

        selectCollector.stop(); // Stop collector after successful selection
        await i.update({ embeds: [successEmbed], components: [] });
      } else {
        if (!i.replied && !i.deferred) {
          await i.reply({
            content: "Failed to set your accessory power. Please try again.",
            flags: [MessageFlags.Ephemeral],
          });
        }
      }
    } catch (error) {
      console.error("[handlePowerSelection] Collector error:", error);
      if (!i.replied && !i.deferred) {
        await i.reply({
          content: "An error occurred. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    }
  });

  selectCollector.on("end", (collected, reason) => {
    if (reason === "time") {
      // Disable components on timeout
      const disabledRow = new ActionRowBuilder().addComponents(
        selectRow.components.map(component => {
          if (component.data.type === 3) {
            // StringSelectMenu
            return StringSelectMenuBuilder.from(component).setDisabled(true);
          } else {
            // Button
            return ButtonBuilder.from(component).setDisabled(true);
          }
        })
      );
      interaction.editReply({ components: [disabledRow] }).catch(() => {});
    }
  });
} */

/* async function handleDetailsView(interaction, magicalPower) {
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PURPLE)
    .setTitle(
      "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist - Magical Power Details"
    )
    .addFields(
      {
        name: "Your Magical Power",
        value: `<:accessory_bag:1384159284123664516> **${magicalPower.toLocaleString()} MP**`,
        inline: true,
      },
      {
        name: "How Magical Power Works",
        value:
          "• Accessories provide Magical Power based on their rarity\n• Select a Power to convert your MP into stat bonuses\n• **Common:** 3 MP • **Uncommon:** 5 MP • **Rare:** 8 MP\n• **Epic:** 12 MP • **Legendary:** 16 MP",
        inline: false,
      }
    )
    .setFooter({ text: "Learn how to maximize your Magical Power!" });

  const backButton = new ButtonBuilder()
    .setCustomId("maxwell_back")
    .setLabel("Back")
    .setStyle(ButtonStyle.Secondary)
    .setEmoji("◀️");

  const row = new ActionRowBuilder().addComponents(backButton);

  await interaction.update({ embeds: [embed], components: [row] });

  // Handle back button
  const reply = await interaction.fetchReply();
  const backCollector = reply.createMessageComponentCollector({
    filter: i =>
      i.customId === "maxwell_back" && i.user.id === interaction.user.id,
    time: 60000,
  });

  backCollector.on("collect", async i => {
    try {
      backCollector.stop(); // Stop this collector
      await returnToMainInterface(i);
    } catch (error) {
      console.error("[handleDetailsView] Back button error:", error);
      if (!i.replied && !i.deferred) {
        await i.reply({
          content: "An error occurred. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    }
  });

  backCollector.on("end", (collected, reason) => {
    if (reason === "time") {
      // Disable back button on timeout
      const disabledRow = new ActionRowBuilder().addComponents(
        ButtonBuilder.from(row.components[0]).setDisabled(true)
      );
      interaction.editReply({ components: [disabledRow] }).catch(() => {});
    }
  });
} */

async function handleViewAllPowers(interaction, magicalPower, mainCollector) {
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PURPLE)
    .setTitle(
      "<:npc_maxwell:1385952510078091424> Maxwell The Thaumaturgist - All Available Powers"
    )
    .addFields(
      {
        name: "Your Magical Power",
        value: `<:accessory_bag:1384159284123664516> **${magicalPower.toLocaleString()} MP**`,
        inline: true,
      },
      {
        name: "Power Conversion",
        value: "Each power converts your MP into different stat bonuses.",
        inline: true,
      }
    );

  Object.values(ACCESSORY_POWERS).forEach((power) => {
    const bonusText = getPowerBonusText(power, magicalPower);
    embed.addFields({
      name: power.name,
      value: bonusText,
      inline: true,
    });
  });

  embed.addFields({
    name: "Magical Power Values",
    value:
      "**Common:** 3 MP • **Uncommon:** 5 MP • **Rare:** 8 MP\n**Epic:** 12 MP • **Legendary:** 16 MP",
    inline: false,
  });

  embed.setFooter({
    text: "Collect more accessories to increase your Magical Power!",
  });

  const backButton = new ButtonBuilder()
    .setCustomId("maxwell_back")
    .setLabel("Back")
    .setStyle(ButtonStyle.Secondary)
    .setEmoji("◀️");

  const row = new ActionRowBuilder().addComponents(backButton);

  await interaction.update({ embeds: [embed], components: [row] });

  const handleBackButton = async (i) => {
    if (i.customId === "maxwell_back" && i.user.id === interaction.user.id) {
      try {
        await returnToMainInterface(i);
        mainCollector.removeListener("collect", handleBackButton);
      } catch (error) {
        console.error("[handleViewAllPowers] Back button error:", error);
        if (!i.replied && !i.deferred) {
          await i.reply({
            content: "An error occurred. Please try again.",
            flags: [MessageFlags.Ephemeral],
          });
        }
      }
    }
  };

  mainCollector.on("collect", handleBackButton);
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("maxwell")
    .setDescription("Talk to Maxwell about Accessory Powers and Magical Power"),

  async execute(interaction) {
    await handleMaxwellInteraction(interaction);
  },

  handleMaxwellInteraction,
};
