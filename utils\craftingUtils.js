const configManager = require("./configManager.js");
const { updateInventoryAtomically } = require("./inventory.js");
const {
  getPlayerData,
  savePlayerData,
  getPlayerSkillLevel,
} = require("./playerDataManager.js");
const {
  getCollectionInfo,
  getPlayerItemCollectionLevel,
} = require("./collectionUtils.js");
const { v4: uuidv4 } = require("uuid");
const {
  MINION_SLOT_UNLOCKS,
  ITEM_RARITY,
  EMBED_COLORS,
} = require("../gameConfig.js");
const { EmbedBuilder } = require("discord.js"); // Needed for potential result formatting if desired later
const { createPetInstance } = require("./petUtils"); // Still needed for pet creation

const { getPlayerAccessories } = require("./accessoryManager");
// Note: Activity manager imports removed - crafting is allowed during activities

/**
 * Calculates the maximum number of times a recipe can be crafted based on ingredient availability.
 * @param {object} inventory - The player's current inventory object.
 * @param {object} recipe - The recipe object containing the ingredients array.
 * @param {object} allItems - All item definitions for checking.
 * @returns {number} The maximum number of times the item can be crafted.
 */
function calculateMaxCraftable(inventory, recipe, allItems) {
  if (!recipe?.ingredients?.length) return 0;

  let maxCraftable = Infinity;

  for (const ingredient of recipe.ingredients) {
    if (!ingredient?.itemKey) continue;

    const itemData = allItems[ingredient.itemKey];
    // Always get uniqueness from the actual item definition, not from the recipe
    const isUnique = itemData?.unique === true;

    if (isUnique) {
      // For unique items, we can only craft 1 at a time
      // Check if the player has at least one of this unique item (including equipped items)
      const hasItem =
        inventory.equipment?.some((eq) => eq.itemKey === ingredient.itemKey) ||
        false;
      if (!hasItem) {
        // If we don't have this unique item, we can't craft at all
        return 0;
      }
      maxCraftable = Math.min(maxCraftable, 1);
    } else {
      // For stackable items, calculate based on inventory count
      // Calculate how many can be crafted based on inventory
      const craftable = Math.floor(
        (inventory.items?.[ingredient.itemKey] || 0) / (ingredient.amount || 1)
      );
      maxCraftable = Math.min(maxCraftable, craftable);
    }

    // If we can't craft even one, no need to check further
    if (maxCraftable === 0) break;
  }

  return isFinite(maxCraftable) ? Math.max(0, maxCraftable) : 0;
}

/**
 * Handles pet status checking functionality
 * @param {object} petData - The pet data to check
 * @returns {Promise<object>} The status of the pet
 */
async function handleCheckPetStatus(petData) {
  if (!petData) {
    throw new Error("Pet data is required");
  }

  const now = new Date();
  const lastFed = petData.lastFed ? new Date(petData.lastFed) : null;
  const hoursSinceFed = lastFed ? (now - lastFed) / (1000 * 60 * 60) : Infinity;

  // Calculate happiness based on time since last fed
  let happiness = petData.happiness || 0;
  if (hoursSinceFed > 12) {
    happiness = Math.max(0, happiness - 10);
  } else if (hoursSinceFed > 6) {
    happiness = Math.max(0, happiness - 5);
  }

  return {
    active: petData.active || false,
    lastFed: lastFed,
    happiness: Math.min(100, Math.max(0, happiness)),
    needsAttention: hoursSinceFed > 12,
  };
}

/**
 * Creates a selection embed for a single unique item.
 * @param {string} itemName - The name of the unique item to select
 * @param {string} emoji - The emoji for the unique item
 * @returns {EmbedBuilder} - Small embed for unique item selection
 */
function createUniqueItemSelectionEmbed(itemName, emoji = "") {
  const { EmbedBuilder } = require("discord.js");

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.MID_BLUE) // Blue color
    .setDescription(`Select which ${emoji} **${itemName}** to use:`);

  return embed;
}

/**
 * Creates a selection embed for multiple unique items.
 * @param {string} craftingItemName - The name of the item being crafted
 * @param {string} craftingItemEmoji - The emoji for the item being crafted
 * @param {Array} uniqueIngredientInfo - Array of objects with itemKey, itemName, emoji, count
 * @returns {EmbedBuilder} - Embed for multi-unique item selection
 */
function createMultiUniqueItemSelectionEmbed(
  craftingItemName,
  craftingItemEmoji = "",
  uniqueIngredientInfo = []
) {
  const { EmbedBuilder } = require("discord.js");

  const totalNeeded = uniqueIngredientInfo.reduce(
    (sum, info) => sum + (info.needed || 1),
    0
  );

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.MID_BLUE) // Blue color
    .setTitle(`${craftingItemEmoji} Crafting: ${craftingItemName}`)
    .setDescription(
      `This recipe requires multiple unique items. Please select the required quantities:`
    );

  // Add fields for each unique ingredient type
  uniqueIngredientInfo.forEach((info) => {
    embed.addFields({
      name: `${info.emoji} ${info.itemName}`,
      value: `Required: ${info.needed || 1}\nAvailable: ${info.available || info.count || 0}`,
      inline: true,
    });
  });

  // Add spacer if odd number of fields
  if (uniqueIngredientInfo.length % 2 !== 0) {
    embed.addFields({
      name: "\u200b",
      value: "\u200b",
      inline: true,
    });
  }

  embed.setFooter({
    text: `Select ${totalNeeded} items total`,
  });

  return embed;
}

/**
 * Calculates Crafting XP based on ingredient sell prices
 * @param {Array} ingredients - Recipe ingredients array
 * @param {number} craftAmount - Amount being crafted
 * @param {number} craftingWisdom - Player's Crafting Wisdom stat
 * @returns {number} - Crafting XP to award
 */
function calculateCraftingXP(ingredients, craftAmount) {
  const allItems = configManager.getAllItems();
  let totalSellValue = 0;

  // Calculate total sell value of all ingredients
  for (const ingredient of ingredients) {
    const itemData = allItems[ingredient.itemKey];
    if (!itemData) continue;

    // Check if this is a Block_Of item (should not give XP)
    if (ingredient.itemKey.startsWith("BLOCK_OF_")) {
      continue;
    }

    // Only count items that are sellable to NPCs
    if (itemData.sellable && itemData.sellPrice) {
      const ingredientAmount = ingredient.amount * craftAmount;
      totalSellValue += itemData.sellPrice * ingredientAmount;
    }
  }

  // Calculate base XP (3% of total sell value)
  const baseXP = Math.floor(totalSellValue * 0.03);

  // Wisdom multiplier now applied centrally in addSkillExp function
  return baseXP;
}

/**
 * Checks if a crafted item should give Crafting XP
 * @param {string} itemKey - The item being crafted
 * @param {Array} ingredients - Recipe ingredients
 * @returns {boolean} - Whether this craft should give XP
 */
function shouldGiveCraftingXP(itemKey, ingredients) {
  // Don't give XP for Block_Of items
  if (itemKey.startsWith("BLOCK_OF_")) {
    return false;
  }

  // Check if any ingredients are sellable (has NPC sell price)
  const allItems = configManager.getAllItems();
  for (const ingredient of ingredients) {
    const itemData = allItems[ingredient.itemKey];
    if (itemData && itemData.sellable && itemData.sellPrice) {
      return true;
    }
  }

  return false;
}

/**
 * Awards Crafting XP and handles notifications
 * @param {string} userId - Player's Discord ID
 * @param {object} playerData - Player data object
 * @param {number} xpAmount - Amount of XP to award
 * @param {object} interaction - Discord interaction for notifications
 * @returns {Promise<void>}
 */
async function awardCraftingXP(userId, playerData, xpAmount, interaction) {
  if (xpAmount <= 0) return;

  // Use centralized skill notification system
  const { addSkillExp } = require("./skillExpManager");
  await addSkillExp(userId, "crafting", xpAmount, interaction);

  // Update disblock XP system (this is also handled in the centralized system but kept for compatibility)
  if (interaction) {
    const { checkAndNotifyDisblockXP } = require("./disblockXpSystem");
    await checkAndNotifyDisblockXP(userId, interaction, playerData);
  }
}

/**
 * Creates a craft success embed with the new format
 * @param {object} craftResult - Result from attemptCraft
 * @returns {EmbedBuilder} - Formatted embed
 */
function createCraftSuccessEmbed(craftResult) {
  const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
  const { EmbedBuilder } = require("discord.js");

  const itemEmoji = craftResult.itemData?.emoji || "❓";
  const itemName = craftResult.itemData?.name || craftResult.itemKey;
  const amount = craftResult.amount || 1;
  const xpGained = craftResult.craftingXpGained || 0;

  let description = `**Crafted ${itemEmoji} x${amount} ${itemName}**`;

  // Add XP line if XP was gained
  if (xpGained > 0) {
    const craftingEmoji = skillEmojis.crafting || "🔧";
    description += `\n\n${craftingEmoji} **\`EXP: Crafting +${xpGained}\`**`;
  }

  return new EmbedBuilder()
    .setColor(EMBED_COLORS.LIGHT_GREEN)
    .setDescription(description);
}

/**
 * Attempts to craft an item for a player.
 * Checks requirements, consumes ingredients, and adds the result.
 * Handles normal items, unique items, minions, and probabilistic pet crafts.
 * @param {string} discordId - The Discord ID of the player.
 * @param {string} itemKeyToCraft - The key of the item the player wants to craft.
 * @param {number} requestedAmount - How many the player wants to craft.
 * @param {string|Array} selectedEquipmentId - The ID of the equipment to remove, or array of {ingredientKey, itemId} objects for multi-select.
 * @returns {Promise<{success: boolean, message: string}>} Result object with status and message.
 */
async function attemptCraft(
  discordId,
  itemKeyToCraft,
  requestedAmount,
  selectedEquipmentId = null,
  interaction = null,
  opts = {}
) {
  // --- REMOVED initial state capture for recipe unlock check ---
  // let craftableBefore = new Set();
  // let allItems; // Will be fetched inside try block
  // try { ... } catch { ... }
  // --- End REMOVED initial state capture ---

  let initialCharacterData;
  let itemDataToCraft;
  let recipe;

  // --- Declare type flags ONCE here ---
  let isMinion = false;
  let isUnique = false;
  let isProbabilisticPetRecipe = false;

  // --- Store final notification message if needed ---
  let uniqueCraftNotificationEmbed = null;

  // --- Store Fig Hew data for transfer to Figstone Splitter ---
  let figHewDataToTransfer = null;

  // --- Store equipment data for transfer to upgraded equipment ---
  let equipmentDataToTransfer = null;

  // --- XP tracking variables ---
  let craftingXpGained = 0;
  let petXpGained = 0;
  let tamingXpGained = 0;

  try {
    // 1. Get Player Data & Item Data
    initialCharacterData = await getPlayerData(discordId);
    itemDataToCraft = configManager.getAllItems()[itemKeyToCraft];

    // ++ Determine if crafted before (for notification trigger) ++
    const wasCraftedBefore =
      initialCharacterData.craftedMinions?.includes(`${itemKeyToCraft}_T1`) ||
      false;

    // 2. Validate Item and Recipe Existence
    if (!itemDataToCraft) {
      return {
        success: false,
        message: `❌ Unknown item key: \`${itemKeyToCraft}\``,
      };
    }
    if (
      !itemDataToCraft.recipes ||
      !Array.isArray(itemDataToCraft.recipes) ||
      itemDataToCraft.recipes.length === 0
    ) {
      return {
        success: false,
        message: `ℹ️ No crafting recipe found for ${
          itemDataToCraft.emoji || "❓"
        } \`${
          itemDataToCraft.name || itemKeyToCraft
        }\`. This item cannot be crafted.`,
      };
    }
    // support selecting specific recipe index (for items with multiple recipes)
    let recipeIndex = 0;
    if (
      Number.isInteger(opts.recipeIndex) &&
      opts.recipeIndex >= 0 &&
      opts.recipeIndex < itemDataToCraft.recipes.length
    ) {
      recipeIndex = opts.recipeIndex;
    }
    recipe = itemDataToCraft.recipes[recipeIndex];
    if (!recipe.ingredients || !Array.isArray(recipe.ingredients)) {
      console.error(
        `[attemptCraft] Invalid ingredients format for ${itemKeyToCraft}`
      );
      return {
        success: false,
        message: "❌ Internal error: Invalid recipe ingredients format.",
      };
    }

    // 3. Validate Amount and Item Type Constraints
    // --- Assign type flags here ---
    isMinion = itemDataToCraft.isMinion === true;
    isUnique = itemDataToCraft.unique === true;
    isProbabilisticPetRecipe =
      !!itemDataToCraft.probabilities && !!itemDataToCraft.resultPetKey;

    if (isMinion && requestedAmount > 1) {
      return {
        success: false,
        message: "You can only craft Minions one at a time.",
      };
    }
    // Allow crafting multiple probabilistic pets at once if desired?
    // For now, assume unique items (including pets from probabilistic recipes) can only be 1
    if (isUnique && requestedAmount > 1) {
      return {
        success: false,
        message: `You can only craft one ${itemDataToCraft.emoji || "❓"} \`${
          itemDataToCraft.name || itemKeyToCraft
        }\` at a time, as it's a unique item.`,
      };
    }
    if (isProbabilisticPetRecipe && requestedAmount > 1) {
      return {
        success: false,
        message: "You can only attempt to craft one Mystery Pet at a time.",
      };
    }

    // --- Requirements Check ---
    const requirements = itemDataToCraft.craftingRequirements;
    if (requirements) {
      // Check Skills
      if (requirements.skills) {
        for (const [skillKey, requiredLevel] of Object.entries(
          requirements.skills
        )) {
          const playerLevel = getPlayerSkillLevel(
            initialCharacterData,
            skillKey.toLowerCase()
          );
          if (playerLevel < requiredLevel) {
            return {
              success: false,
              message: `❌ You need \`${skillKey.toUpperCase()}\` Skill Level ${requiredLevel} to craft this.`,
            };
          }
        }
      }
      // Check Collections
      if (requirements.collections) {
        for (const [collectionKey, requiredLevel] of Object.entries(
          requirements.collections
        )) {
          const playerLevel = getPlayerItemCollectionLevel(
            initialCharacterData,
            collectionKey
          );

          if (playerLevel < requiredLevel) {
            const collectionInfo = getCollectionInfo(collectionKey);
            const collectionName =
              collectionInfo?.collectionDef?.name || collectionKey;
            const emoji =
              collectionInfo?.collectionDef?.emoji ||
              configManager.getItem(collectionKey)?.emoji ||
              "❓";
            return {
              success: false,
              message: `❌ You need ${emoji} **${collectionName}** Collection Level ${requiredLevel} to craft this.`,
            };
          }
        }
      }
      // Check Slayers
      if (requirements.slayers) {
        const { getSlayerLevelFromExp } = require("./slayerLevelUtils");
        const slayerXpData = initialCharacterData.slayerXp || {};

        for (const [slayerType, requiredLevel] of Object.entries(
          requirements.slayers
        )) {
          const slayerXp = slayerXpData[slayerType] || 0;
          const playerLevel = getSlayerLevelFromExp(slayerXp).level;
          if (playerLevel < requiredLevel) {
            const slayerEmoji =
              slayerType === "zombie"
                ? "<:revenant_horror:1389646540460658970>"
                : "<:mob_spider:1370526342927618078>";
            const slayerName =
              slayerType.charAt(0).toUpperCase() + slayerType.slice(1);
            return {
              success: false,
              message: `❌ You need ${slayerEmoji} **${slayerName} Slayer Level ${requiredLevel}** to craft this.`,
            };
          }
        }
      }
    }
    // --- End Requirements Check ---

    // 4. Check Ingredients & Prepare Removal List
    const inventory = initialCharacterData.inventory?.items || {};
    const equipment = initialCharacterData.inventory?.equipment || []; // Get player's equipment
    const accessories = await getPlayerAccessories(discordId); // Get player's accessories
    const itemsToRemoveForUpdate = []; // For stackable items
    const equipmentIdsToRemove = []; // For unique items (equipment)
    const accessoryIdsToRemove = []; // For unique items (accessories)
    const accessoriesToAdd = []; // For accessories
    const requiredUniqueItems = new Map(); // Track unique item keys and their required quantities
    const providedUniqueItems = new Map(); // Track unique item keys and their provided quantities
    const allItems = configManager.getAllItems(); // Get all items for uniqueness check

    // Build map of required unique items with quantities
    for (const ingredient of recipe.ingredients) {
      const itemData = allItems[ingredient.itemKey];
      const isUnique = itemData?.unique === true;

      if (isUnique) {
        const needed = ingredient.amount * requestedAmount;
        requiredUniqueItems.set(ingredient.itemKey, needed);
        providedUniqueItems.set(ingredient.itemKey, 0);
      }
    }

    // Convert selectedEquipmentId to a lookup map for easier handling
    const selectedItemsMap = new Map();
    if (selectedEquipmentId) {
      if (Array.isArray(selectedEquipmentId)) {
        // Multiple selections: {ingredientKey, itemId}
        for (const selection of selectedEquipmentId) {
          if (!selectedItemsMap.has(selection.ingredientKey)) {
            selectedItemsMap.set(selection.ingredientKey, []);
          }
          selectedItemsMap.get(selection.ingredientKey).push(selection.itemId);
        }
      } else {
        // Single selection: string ID - need to figure out which ingredient it's for
        const uniqueIngredients = recipe.ingredients.filter((ing) => {
          const ingData = configManager.getAllItems()[ing.itemKey];
          return ingData?.unique === true;
        });

        if (uniqueIngredients.length === 1) {
          selectedItemsMap.set(uniqueIngredients[0].itemKey, [
            selectedEquipmentId,
          ]);
        } else {
          // This shouldn't happen with the new logic, but handle it gracefully
          console.warn(
            `[attemptCraft] Single selection provided for multi-unique recipe: ${itemKeyToCraft}`
          );
          return {
            success: false,
            message: `❌ This recipe requires multiple unique items. Please use the multi-select interface.`,
          };
        }
      }
    }

    // Process unique ingredients first to validate selections
    for (const ingredient of recipe.ingredients) {
      const itemData = allItems[ingredient.itemKey];
      const isUnique = itemData?.unique === true;

      if (isUnique) {
        const needed = ingredient.amount * requestedAmount;
        const selectedItemIds = selectedItemsMap.get(ingredient.itemKey) || [];

        if (selectedItemIds.length === 0) {
          return {
            success: false,
            message: `❌ A unique item (${ingredient.itemKey}) is required for this craft, but none was selected.`,
          };
        }

        if (selectedItemIds.length !== needed) {
          return {
            success: false,
            message: `❌ This recipe requires ${needed}x ${ingredient.itemKey}, but you selected ${selectedItemIds.length}.`,
          };
        }

        // Validate each selected item
        for (const selectedItemId of selectedItemIds) {
          const selectedEquipment = equipment.find(
            (eq) => eq.id === selectedItemId
          );
          const selectedAccessory = accessories.find(
            (acc) =>
              acc.id === selectedItemId && acc.itemKey === ingredient.itemKey
          );

          if (!selectedEquipment && !selectedAccessory) {
            return {
              success: false,
              message: `❌ The selected item (ID: ${selectedItemId}) is not found in your inventory.`,
            };
          }

          if (
            selectedEquipment &&
            selectedEquipment.itemKey !== ingredient.itemKey
          ) {
            return {
              success: false,
              message: `❌ The selected equipment (ID: ${selectedItemId}) is not a valid ${ingredient.itemKey}.`,
            };
          }

          if (
            selectedAccessory &&
            selectedAccessory.itemKey !== ingredient.itemKey
          ) {
            return {
              success: false,
              message: `❌ The selected accessory (ID: ${selectedItemId}) is not a valid ${ingredient.itemKey}.`,
            };
          }

          if (selectedEquipment) {
            equipmentIdsToRemove.push(selectedItemId);

            // Special case: If crafting Figstone Splitter and using Fig Hew, preserve its data
            if (
              itemKeyToCraft === "FIGSTONE_SPLITTER" &&
              ingredient.itemKey === "FIG_HEW" &&
              selectedEquipment.data_json
            ) {
              // Store the Fig Hew's data to transfer to the new Figstone Splitter
              figHewDataToTransfer = selectedEquipment.data_json;
            }

            // Special case: If upgrading Undead Sword to Revenant Falchion, preserve weapon data
            if (
              itemKeyToCraft === "REVENANT_FALCHION" &&
              ingredient.itemKey === "UNDEAD_SWORD" &&
              selectedEquipment.data_json
            ) {
              // Store the weapon's data to transfer to the new Revenant Falchion
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            // Special case: If upgrading Revenant Falchion to Reaper Falchion, preserve weapon data
            if (
              itemKeyToCraft === "REAPER_FALCHION" &&
              ingredient.itemKey === "REVENANT_FALCHION" &&
              selectedEquipment.data_json
            ) {
              // Store the weapon's data to transfer to the new Reaper Falchion
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            // Special case: If upgrading Zombie Heart to Crystallized Heart, preserve helmet data
            if (
              itemKeyToCraft === "CRYSTALLIZED_HEART" &&
              ingredient.itemKey === "ZOMBIE_HEART" &&
              selectedEquipment.data_json
            ) {
              // Store the helmet's data to transfer to the new Crystallized Heart
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            // Special case: If upgrading Crystallized Heart to Revived Heart, preserve helmet data
            if (
              itemKeyToCraft === "REVIVED_HEART" &&
              ingredient.itemKey === "CRYSTALLIZED_HEART" &&
              selectedEquipment.data_json
            ) {
              // Store the helmet's data to transfer to the new Revived Heart
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            // Special case: If upgrading Revived Heart to Reaper Mask, preserve helmet data
            if (
              itemKeyToCraft === "REAPER_MASK" &&
              ingredient.itemKey === "REVIVED_HEART" &&
              selectedEquipment.data_json
            ) {
              // Store the helmet's data to transfer to the new Reaper Mask
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            // Cropie Armor upgrades - preserve data from Melon Armor
            if (
              itemKeyToCraft === "CROPIE_HELMET" &&
              ingredient.itemKey === "MELON_HELMET" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            if (
              itemKeyToCraft === "CROPIE_CHESTPLATE" &&
              ingredient.itemKey === "MELON_CHESTPLATE" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            if (
              itemKeyToCraft === "CROPIE_LEGGINGS" &&
              ingredient.itemKey === "MELON_LEGGINGS" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            if (
              itemKeyToCraft === "CROPIE_BOOTS" &&
              ingredient.itemKey === "MELON_BOOTS" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            // Squash Armor upgrades - preserve data from Cropie Armor
            if (
              itemKeyToCraft === "SQUASH_HELMET" &&
              ingredient.itemKey === "CROPIE_HELMET" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            if (
              itemKeyToCraft === "SQUASH_CHESTPLATE" &&
              ingredient.itemKey === "CROPIE_CHESTPLATE" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            if (
              itemKeyToCraft === "SQUASH_LEGGINGS" &&
              ingredient.itemKey === "CROPIE_LEGGINGS" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }

            if (
              itemKeyToCraft === "SQUASH_BOOTS" &&
              ingredient.itemKey === "CROPIE_BOOTS" &&
              selectedEquipment.data_json
            ) {
              equipmentDataToTransfer = selectedEquipment.data_json;
            }
          } else if (selectedAccessory) {
            accessoryIdsToRemove.push(selectedItemId);
          }
        }

        // Update provided count
        providedUniqueItems.set(ingredient.itemKey, selectedItemIds.length);
      }
    }

    // Process stackable ingredients
    for (const ingredient of recipe.ingredients) {
      const itemData = allItems[ingredient.itemKey];
      const isUnique = itemData?.unique === true;

      if (!isUnique) {
        const needed = ingredient.amount * requestedAmount;
        const possessed = inventory[ingredient.itemKey] || 0;

        if (possessed < needed) {
          const itemInfo = configManager.getAllItems()[ingredient.itemKey];
          return {
            success: false,
            message: `You don't have enough ingredients. You need ${needed.toLocaleString()}x ${
              itemInfo?.emoji || "❓"
            } \`${
              itemInfo?.name || ingredient.itemKey
            }\`, but you only have ${possessed.toLocaleString()}.`,
            needsDetailedBreakdown: true, // flag for enhanced error display
          };
        }

        // If sufficient stackable items are available, add to removal list
        itemsToRemoveForUpdate.push({
          itemKey: ingredient.itemKey,
          amount: -needed,
        });
      }
    }

    // --- All ingredients checked and available. Proceed with crafting logic. ---

    // --- Crafting Logic (Determine results) ---
    const coinsToAdd = 0; // Usually 0 for crafting
    const itemsToAddForUpdate = []; // Standard items to add
    const equipmentToAdd = []; // Equipment items to add
    const petsToAddInstances = []; // Pet instances to add via playerDataManager
    let successMessage;

    if (isProbabilisticPetRecipe) {
      // --- Probabilistic Pet Craft ---

      const probabilities = itemDataToCraft.probabilities;
      const possibleRarities = Object.keys(probabilities);
      const rand = Math.random();
      let cumulative = 0;
      let determinedRarity = null;

      for (const rarity of possibleRarities) {
        cumulative += probabilities[rarity];
        if (rand <= cumulative) {
          determinedRarity = rarity;
          break;
        }
      }

      if (!determinedRarity) {
        console.error(
          `[attemptCraft] Failed to determine rarity for ${itemKeyToCraft}. Probabilities:`,
          probabilities
        );
        return {
          success: false,
          message: "❌ Internal error determining pet rarity.",
        };
      }

      const actualPetKey = itemDataToCraft.resultPetKey;
      const actualPetData = configManager.getAllItems()[actualPetKey];

      if (!actualPetData || actualPetData.type !== "PET") {
        console.error(
          `[attemptCraft] resultPetKey ${actualPetKey} for ${itemKeyToCraft} is not a valid PET item.`
        );
        return {
          success: false,
          message: "❌ Internal error: Result item is not a valid pet.",
        };
      }

      // ADDED: Create the pet instance
      const newPetInstance = createPetInstance(actualPetKey, determinedRarity);

      if (newPetInstance) {
        petsToAddInstances.push(newPetInstance);

        const rarityInfo = ITEM_RARITY[determinedRarity] || {
          name: determinedRarity,
        };
        successMessage = `${
          actualPetData.emoji || "❓"
        } **Pet Crafted!**\nYou've successfully crafted a ${
          actualPetData.emoji || "❓"
        } **${rarityInfo.name} ${actualPetData.name}**! View in /pet list.`;
      } else {
        console.error(
          `[attemptCraft] Failed to create pet instance via utility for: ${actualPetKey}`
        );
        return {
          success: false,
          message: "❌ Internal error creating pet instance.",
        };
      }
    } else if (isMinion) {
      // --- Minion Craft ---

      // Minions don't add items to inventory, they get added to minion storage after inventory updates
      // Set up the success message
      successMessage = `Crafted ${itemDataToCraft.emoji || "❓"} \`${
        itemDataToCraft.name || itemKeyToCraft
      }\`! Check \`/minions list\`.`;
    } else if (isUnique && !isMinion && !isProbabilisticPetRecipe) {
      // Handle non-pet, non-minion unique items (Equipment)
      // --- Unique Item Craft (Equipment/Tools etc) ---

      // Check if this is an accessory and handle accordingly
      if (itemDataToCraft.type === "ACCESSORY") {
        // Add to accessories instead of equipment
        accessoriesToAdd.push({ itemKey: itemKeyToCraft });
      } else {
        // Prepare for updateInventoryAtomically (it handles UUID generation)
        const newEquipment = { itemKey: itemKeyToCraft };

        // Special case: If crafting Figstone Splitter and we have Fig Hew data to transfer
        if (itemKeyToCraft === "FIGSTONE_SPLITTER" && figHewDataToTransfer) {
          newEquipment.dataJson = figHewDataToTransfer;

          // Clear the stored data
          figHewDataToTransfer = null;
        }

        // Special case: If upgrading weapons/helmets/armor and we have data to transfer
        if (
          (itemKeyToCraft === "REVENANT_FALCHION" ||
            itemKeyToCraft === "REAPER_FALCHION" ||
            itemKeyToCraft === "CRYSTALLIZED_HEART" ||
            itemKeyToCraft === "REVIVED_HEART" ||
            itemKeyToCraft === "REAPER_MASK" ||
            itemKeyToCraft === "CROPIE_HELMET" ||
            itemKeyToCraft === "CROPIE_CHESTPLATE" ||
            itemKeyToCraft === "CROPIE_LEGGINGS" ||
            itemKeyToCraft === "CROPIE_BOOTS" ||
            itemKeyToCraft === "SQUASH_HELMET" ||
            itemKeyToCraft === "SQUASH_CHESTPLATE" ||
            itemKeyToCraft === "SQUASH_LEGGINGS" ||
            itemKeyToCraft === "SQUASH_BOOTS") &&
          equipmentDataToTransfer
        ) {
          newEquipment.dataJson = equipmentDataToTransfer;

          // Clear the stored data
          equipmentDataToTransfer = null;
        }

        equipmentToAdd.push(newEquipment);
      }
      successMessage = `Crafted ${itemDataToCraft.emoji || "❓"} \`${
        itemDataToCraft.name || itemKeyToCraft
      }\`!`;
    } else {
      // Handles non-unique, non-minion, non-probabilistic-pet items
      // --- Standard Item Craft ---
      const resultAmount = (recipe.resultAmount || 1) * requestedAmount;
      // Prepare for updateInventoryAtomically
      itemsToAddForUpdate.push({
        itemKey: itemKeyToCraft,
        amount: resultAmount,
      });
      successMessage = `Crafted ${resultAmount.toLocaleString()}x ${
        itemDataToCraft.emoji || "❓"
      } \`${itemDataToCraft.name || itemKeyToCraft}\`!`;
    }

    // --- End Crafting Logic ---

    // 5. Execute ALL crafting operations atomically
    // This ensures either everything succeeds together or everything fails together
    console.log(
      `[attemptCraft] Starting atomic crafting transaction for ${discordId}: ${itemKeyToCraft}`
    );

    try {
      // First, perform the main inventory update
      const itemsToChange = [...itemsToRemoveForUpdate, ...itemsToAddForUpdate];

      await updateInventoryAtomically(
        discordId,
        coinsToAdd,
        itemsToChange,
        equipmentToAdd, // Pass equipment (for non-minion unique crafts)
        equipmentIdsToRemove, // Remove the selected equipment if needed
        0, // bankCoinsToAdd
        [], // equipmentDataUpdates
        null, // islandJsonString
        null, // collectionsJsonString
        false, // useExistingTransaction
        accessoriesToAdd, // Add accessories to player's accessory bag
        accessoryIdsToRemove // Remove the selected accessories if needed
      );

      console.log(
        `[attemptCraft] Inventory update completed successfully for ${discordId}`
      );

      // 6. Add Pets (if any pets were crafted) - CRITICAL OPERATION
      if (petsToAddInstances.length > 0) {
        console.log(
          `[attemptCraft] Adding ${petsToAddInstances.length} pet instance(s) via PlayerDataManager.`
        );

        const currentCharacterData = await getPlayerData(discordId);

        if (!currentCharacterData) {
          throw new Error(
            `Failed to reload player data for pet addition: ${discordId}`
          );
        }

        // Ensure pets array exists
        if (
          !currentCharacterData.pets ||
          !Array.isArray(currentCharacterData.pets)
        ) {
          currentCharacterData.pets = [];
        }

        currentCharacterData.pets.push(...petsToAddInstances);
        await savePlayerData(discordId, { pets: currentCharacterData.pets }, [
          "pets",
        ]);

        console.log(
          `[attemptCraft] Pet addition completed successfully for ${discordId}`
        );
      }

      // 7. Handle Minion Storage Updates (for minions) - CRITICAL OPERATION
      if (isMinion) {
        console.log(
          `[attemptCraft] Handling minion storage for ${discordId}: ${itemKeyToCraft}`
        );

        // Get current player data
        const currentCharacterData = await getPlayerData(discordId);
        if (!currentCharacterData) {
          throw new Error("Failed to reload player data for minion crafting");
        }

        // Create minion instance
        const newMinionInstance = {
          id: uuidv4(),
          itemKey: itemKeyToCraft,
          tier: 1,
          resourcesStored: {},
          lastCollectionTimestamp: Date.now(),
          createdAt: Date.now(),
        };

        // Initialize arrays if needed
        if (
          !currentCharacterData.minionStorage ||
          !Array.isArray(currentCharacterData.minionStorage)
        ) {
          currentCharacterData.minionStorage = [];
        }
        if (
          !currentCharacterData.craftedMinions ||
          !Array.isArray(currentCharacterData.craftedMinions)
        ) {
          currentCharacterData.craftedMinions = [];
        }

        // Add minion to storage
        currentCharacterData.minionStorage.push(newMinionInstance);
        console.log(`[attemptCraft] Added minion to storage for ${discordId}`);

        // Handle unique minion tracking
        if (!wasCraftedBefore) {
          const craftedKey = `${itemKeyToCraft.toUpperCase()}_T1`;
          if (!currentCharacterData.craftedMinions.includes(craftedKey)) {
            currentCharacterData.craftedMinions.push(craftedKey);
            console.log(
              `[attemptCraft] Added ${craftedKey} to craftedMinions for ${discordId}`
            );

            // Build unique craft notification embed
            const itemInfo = configManager.getAllItems()[itemKeyToCraft];
            let nextSlotText = "";
            const uniqueCraftsCount =
              currentCharacterData.craftedMinions.length;
            let nextMilestoneCrafts = null;

            for (const unlock of MINION_SLOT_UNLOCKS) {
              if (unlock.crafts > uniqueCraftsCount) {
                nextMilestoneCrafts = unlock.crafts;
                break;
              }
            }

            if (nextMilestoneCrafts !== null) {
              const needed = nextMilestoneCrafts - uniqueCraftsCount;
              nextSlotText = ` (${needed} more for next slot!)`;
            }

            uniqueCraftNotificationEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.GREEN)
              .setDescription(
                `${itemInfo?.emoji || "❓"} **[T1] ${itemInfo?.name || itemKeyToCraft}** - New Unique Minion! ${nextSlotText}`
              )
              .setFooter({ text: "Check /minions slots for progress" });
          }
        }

        // Save updated player data with only minionStorage and craftedMinions
        await savePlayerData(
          discordId,
          {
            minionStorage: currentCharacterData.minionStorage,
            craftedMinions: currentCharacterData.craftedMinions,
          },
          ["minionStorage", "craftedMinions"]
        );
        console.log(
          `[attemptCraft] Minion storage update completed successfully for ${discordId}`
        );
      }

      // 8. Award Crafting XP - CRITICAL OPERATION
      // Check if this craft should give XP and calculate it
      if (shouldGiveCraftingXP(itemKeyToCraft, recipe.ingredients)) {
        // Calculate XP based on ingredients (wisdom now applied centrally)
        const xpAmount = calculateCraftingXP(
          recipe.ingredients,
          requestedAmount
        );
        craftingXpGained = xpAmount;

        // Award the XP with notifications and capture pet/taming XP
        if (xpAmount > 0) {
          const { addSkillExp } = require("./skillExpManager");
          const xpResult = await addSkillExp(
            discordId,
            "crafting",
            xpAmount,
            interaction
          );

          // Capture pet and taming XP from the result
          petXpGained = xpResult.petExpGained || 0;
          tamingXpGained = xpResult.tamingExpGained || 0;

          console.log(
            `[attemptCraft] Awarded ${xpAmount} Crafting XP to ${discordId} for crafting ${requestedAmount}x ${itemKeyToCraft}`
          );
          if (petXpGained > 0) {
            console.log(
              `[attemptCraft] Pet gained ${petXpGained} XP, Taming gained ${tamingXpGained} XP`
            );
          }
        }
      }

      console.log(
        `[attemptCraft] All crafting operations completed successfully for ${discordId}`
      );
    } catch (atomicError) {
      // If ANY operation fails, log the error and return failure
      console.error(
        `[attemptCraft] ATOMIC TRANSACTION FAILED for ${discordId} crafting ${itemKeyToCraft}:`,
        atomicError
      );

      // Return specific error message based on what failed
      let errorMessage = "❌ Crafting failed due to an unexpected error.";
      if (atomicError.message.includes("pet addition")) {
        errorMessage =
          "❌ Crafting failed: Unable to add pet to your collection.";
      } else if (atomicError.message.includes("minion")) {
        errorMessage = "❌ Crafting failed: Unable to add minion to storage.";
      } else if (atomicError.message.includes("XP")) {
        errorMessage = "❌ Crafting failed: Unable to award experience points.";
      } else if (atomicError.message.includes("inventory")) {
        errorMessage = "❌ Crafting failed: Unable to update inventory.";
      } else if (atomicError.message.includes("Insufficient")) {
        errorMessage = `❌ ${atomicError.message}`;
      }

      return {
        success: false,
        message: errorMessage,
        uniqueCraftEmbed: null,
      };
    }

    // Calculate consumed items for display
    const consumedItems = {};
    for (const ingredient of recipe.ingredients) {
      const totalAmount = ingredient.amount * requestedAmount;
      consumedItems[ingredient.itemKey] = totalAmount;
    }

    // Return success message AND the potential notification embed
    return {
      success: true,
      message: successMessage,
      uniqueCraftEmbed: uniqueCraftNotificationEmbed, // Pass the embed back
      craftingXpGained: craftingXpGained, // Include XP data for custom embeds
      petXpGained: petXpGained, // Pet XP gained
      tamingXpGained: tamingXpGained, // Taming XP gained
      consumedItems: consumedItems, // Items consumed in the recipe
      itemKey: itemKeyToCraft,
      itemData: itemDataToCraft,
      // Return the actual number of items produced so the success embed shows the correct count
      amount:
        isMinion || isUnique || isProbabilisticPetRecipe
          ? 1
          : (recipe.resultAmount || 1) * requestedAmount,
    };
  } catch (error) {
    console.error(
      `[attemptCraft] Error crafting ${itemKeyToCraft} for ${discordId}:`,
      error
    );
    // Return failure message
    let errorMessage =
      "❌ An unexpected error occurred during crafting. Please check logs or report this issue.";
    if (
      error.message &&
      (error.message.startsWith("Insufficient") ||
        error.message.startsWith("Player") ||
        error.message.includes("Failed to reload"))
    ) {
      errorMessage = `❌ ${error.message}`;
    }
    return {
      success: false,
      message: errorMessage,
      uniqueCraftEmbed: null, // Ensure embed is null on failure
    };
  }
}

// Export functions for the main module
module.exports = {
  attemptCraft,
  calculateMaxCraftable,
  handleCheckPetStatus,
  createUniqueItemSelectionEmbed,
  createMultiUniqueItemSelectionEmbed,
  calculateCraftingXP,
  shouldGiveCraftingXP,
  awardCraftingXP,
  createCraftSuccessEmbed,
};

// Unit test for uniqueness detection - will only run if called directly
if (require.main === module) {
  const _fs = require("fs");
  const _path = require("path");

  async function testUniquenessDetection() {
    console.log("==== Testing Uniqueness Detection ====");

    const configManager = require("./configManager.js");
    const allItems = configManager.getAllItems();
    console.log(`Loaded ${Object.keys(allItems).length} items`);

    // Find a unique item and a non-unique item for testing
    let uniqueItemKey = null;
    let nonUniqueItemKey = null;

    console.log("\nLooking for test items...");
    for (const [key, item] of Object.entries(allItems)) {
      if (item.unique === true && !uniqueItemKey) {
        uniqueItemKey = key;
        console.log(`Found unique item: ${key} (${item.name})`);
      } else if (item.unique !== true && !nonUniqueItemKey) {
        nonUniqueItemKey = key;
        console.log(`Found non-unique item: ${key} (${item.name})`);
      }

      if (uniqueItemKey && nonUniqueItemKey) break;
    }

    if (!uniqueItemKey || !nonUniqueItemKey) {
      console.error("Couldn't find suitable test items. Test cannot proceed.");
      return;
    }

    // Test case 1: A recipe with a unique item
    const testRecipe1 = {
      ingredients: [
        {
          itemKey: uniqueItemKey,
          amount: 1,
        },
      ],
    };

    // Test case 2: A recipe with a non-unique item
    const testRecipe2 = {
      ingredients: [
        {
          itemKey: nonUniqueItemKey,
          amount: 64,
        },
      ],
    };

    // Mock inventory with both items
    const mockInventory = {
      items: {
        [nonUniqueItemKey]: 100,
      },
      equipment: [{ id: "test-id", itemKey: uniqueItemKey, isEquipped: false }],
    };

    // Test calculateMaxCraftable function
    console.log("\nTesting calculateMaxCraftable function:");
    const maxCraftable1 = calculateMaxCraftable(
      mockInventory,
      testRecipe1,
      allItems
    );
    console.log(
      `Max craftable for recipe with ${uniqueItemKey}: ${maxCraftable1} (should be 1)`
    );

    const maxCraftable2 = calculateMaxCraftable(
      mockInventory,
      testRecipe2,
      allItems
    );
    console.log(
      `Max craftable for recipe with ${nonUniqueItemKey}: ${maxCraftable2} (should be 1)`
    );

    // Check if items are correctly identified as unique
    console.log("\nChecking item uniqueness:");
    const uniqueItem = allItems[uniqueItemKey];
    const nonUniqueItem = allItems[nonUniqueItemKey];
    console.log(
      `${uniqueItemKey}.unique = ${uniqueItem.unique} (should be true)`
    );
    console.log(
      `${nonUniqueItemKey}.unique = ${nonUniqueItem.unique === true ? true : "falsy"} (should be falsy)`
    );

    console.log("\n==== Uniqueness Detection Test Complete ====");
  }

  // Run the test
  testUniquenessDetection().catch(console.error);
}
