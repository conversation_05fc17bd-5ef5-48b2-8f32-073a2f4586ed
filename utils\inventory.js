const { v4: uuidv4 } = require("uuid");
const db = require("./database"); // SQLite connection
const { dbGet, dbAll, dbRunQueued } = require("./dbUtils"); // SQLite helpers
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("./playerDataManager");
const configManager = require("./configManager");
// Remove both: const ITEMS = require("../data/items.json");

/**
 * Atomically adds or removes stackable items and equipment from the database (SQLite version using separate tables).
 *
 * @param {string} userId
 * @param {number} coinsToAdd Positive for adding, negative for removing coins.
 * @param {Array<{itemKey: string, amount: number}>} itemsToChange Array of stackable items to change (positive amount to add, negative to remove).
 * @param {Array<{itemKey: string, id?: string}>} equipmentToAdd Array of equipment items to add. If id is omitted, a new UUID is generated.
 * @param {Array<string>} equipmentIdsToRemove Array of equipment UUIDs to remove.
 * @param {number} bankCoinsToAdd Positive for adding, negative for removing bank coins.
 * @param {Array<{equipmentId: string, dataJson: string}>} equipmentDataUpdates Array of objects to update equipment_data. dataJson should be a string.
 * @param {string} islandJsonString Island JSON string to update.
 * @param {string} collectionsJsonString Collections JSON string to update.
 * @returns {Promise<void>} Resolves on success, rejects on error.
 */
async function updateInventoryAtomically(
  userId,
  coinsToAdd = 0,
  itemsToChange = [],
  equipmentToAdd = [],
  equipmentIdsToRemove = [],
  bankCoinsToAdd = 0,
  equipmentDataUpdates = [],
  islandJsonString = null,
  collectionsJsonString = null,
  useExistingTransaction = false, // New parameter to handle existing transactions
  accessoriesToAdd = [],
  accessoryIdsToRemove = []
  // accessoryDataUpdates = [] // Unused parameter
) {
  // Enhanced input validation
  if (!userId || typeof userId !== "string") {
    throw new Error(
      "Valid userId string is required for updateInventoryAtomically"
    );
  }
  if (typeof coinsToAdd !== "number" || !Number.isFinite(coinsToAdd)) {
    throw new Error("coinsToAdd must be a finite number");
  }
  if (typeof bankCoinsToAdd !== "number" || !Number.isFinite(bankCoinsToAdd)) {
    throw new Error("bankCoinsToAdd must be a finite number");
  }

  // Prevent extremely large transactions that could indicate bugs
  const MAX_TRANSACTION = **********; // 1 billion coins
  if (Math.abs(coinsToAdd) > MAX_TRANSACTION) {
    console.error(
      `[updateInventoryAtomically] Suspicious large coin transaction blocked for ${userId}: ${coinsToAdd}`
    );
    throw new Error(
      `Coin transaction amount ${coinsToAdd} exceeds maximum allowed`
    );
  }
  if (Math.abs(bankCoinsToAdd) > MAX_TRANSACTION) {
    console.error(
      `[updateInventoryAtomically] Suspicious large bank transaction blocked for ${userId}: ${bankCoinsToAdd}`
    );
    throw new Error(
      `Bank transaction amount ${bankCoinsToAdd} exceeds maximum allowed`
    );
  }

  // Normalize all item keys to uppercase for stackable items
  if (Array.isArray(itemsToChange)) {
    for (const item of itemsToChange) {
      if (item && typeof item.itemKey === "string") {
        item.itemKey = item.itemKey.toUpperCase();
      }
    }
  }
  // Normalize all item keys to uppercase for equipment
  if (Array.isArray(equipmentToAdd)) {
    for (const equip of equipmentToAdd) {
      if (equip && typeof equip.itemKey === "string") {
        equip.itemKey = equip.itemKey.toUpperCase();
      }
    }
  }
  // Normalize all item keys to uppercase for accessories
  if (Array.isArray(accessoriesToAdd)) {
    for (const accessory of accessoriesToAdd) {
      if (accessory && typeof accessory.itemKey === "string") {
        accessory.itemKey = accessory.itemKey.toUpperCase();
      }
    }
  }

  // Define the main transaction logic
  const executeTransaction = async () => {
    // --- 1. Update Player Coins (in players table) with enhanced security ---
    if (coinsToAdd !== 0) {
      // First, get current balance (raw value to handle decimals properly)
      const currentBalanceSql =
        "SELECT coins FROM players WHERE discord_id = ?";
      const result = await dbGet(currentBalanceSql, [userId]);

      if (!result) {
        throw new Error(`Player ${userId} not found.`);
      }

      // Round to 2 decimal places for consistency
      const currentCoins = Math.round(result.coins * 100) / 100;

      // Validate current balance is a number
      if (typeof currentCoins !== "number" || isNaN(currentCoins)) {
        console.error(
          `[updateInventoryAtomically] Invalid coin balance type for ${userId}: ${typeof currentCoins}`
        );
        throw new Error("Invalid coin balance data type detected.");
      }

      const newCoins = Math.round((currentCoins + coinsToAdd) * 100) / 100;

      // Validate the new balance won't be negative
      if (newCoins < 0) {
        throw new Error(
          `Insufficient coins. Have ${
            currentCoins
          }, trying to remove ${Math.abs(coinsToAdd)}.`
        );
      }

      // Perform atomic update with race condition protection
      const updateCoinsSql =
        "UPDATE players SET coins = ? WHERE discord_id = ? AND ROUND(coins * 100) / 100 = ?";
      const updateResult = await dbRunQueued(updateCoinsSql, [
        newCoins,
        userId,
        currentCoins,
      ]);

      if (updateResult.changes === 0) {
        console.warn(
          `[updateInventoryAtomically] Concurrent coin modification detected for ${userId}`
        );
        throw new Error("Concurrent modification detected. Please try again.");
      }

      // Log significant coin changes for audit purposes
      if (Math.abs(coinsToAdd) > 100000) {
        console.log(
          `[updateInventoryAtomically] Large coin change for ${userId}: ${
            currentCoins
          } -> ${newCoins} (${coinsToAdd >= 0 ? "+" : ""}${coinsToAdd})`
        );
      }
    }

    // --- 2. Update Bank Coins (in players table) with enhanced security ---
    if (bankCoinsToAdd !== 0) {
      // First, get current bank balance (raw value to handle decimals properly)
      const currentBankBalanceSql =
        "SELECT bank FROM players WHERE discord_id = ?";
      const result = await dbGet(currentBankBalanceSql, [userId]);

      if (!result) {
        throw new Error(`Player ${userId} not found.`);
      }

      // Round to 2 decimal places for consistency
      const currentBank = Math.round(result.bank * 100) / 100;

      // Validate current bank balance is a number
      if (typeof currentBank !== "number" || isNaN(currentBank)) {
        console.error(
          `[updateInventoryAtomically] Invalid bank balance type for ${userId}: ${typeof currentBank}`
        );
        throw new Error("Invalid bank balance data type detected.");
      }

      const newBankCoins =
        Math.round((currentBank + bankCoinsToAdd) * 100) / 100;

      // Validate the new bank balance won't be negative
      if (newBankCoins < 0) {
        throw new Error(
          `Insufficient bank coins. Have ${
            currentBank
          }, trying to remove ${Math.abs(bankCoinsToAdd)}.`
        );
      }

      // Perform atomic update with race condition protection
      const updateBankCoinsSql =
        "UPDATE players SET bank = ? WHERE discord_id = ? AND ROUND(bank * 100) / 100 = ?";
      const updateResult = await dbRunQueued(updateBankCoinsSql, [
        newBankCoins,
        userId,
        currentBank,
      ]);

      if (updateResult.changes === 0) {
        console.warn(
          `[updateInventoryAtomically] Concurrent bank modification detected for ${userId}`
        );
        throw new Error(
          "Concurrent bank modification detected. Please try again."
        );
      }

      // Log significant bank changes for audit purposes
      if (Math.abs(bankCoinsToAdd) > 100000) {
        console.log(
          `[updateInventoryAtomically] Large bank change for ${userId}: ${
            currentBank
          } -> ${newBankCoins} (${bankCoinsToAdd >= 0 ? "+" : ""}${bankCoinsToAdd})`
        );
      }
    }

    // --- 2. Update Stackable Items (in player_inventory_items table) ---
    for (const item of itemsToChange) {
      if (
        !item.itemKey ||
        typeof item.amount !== "number" ||
        item.amount === 0
      ) {
        console.warn("[Inv Atomic] Skipping invalid item change:", item);
        continue;
      }
      const itemKey = item.itemKey;
      const amountChange = item.amount;

      if (amountChange > 0) {
        // Adding items
        const addItemSql = `
                        INSERT INTO player_inventory_items (discord_id, item_name, amount)
                        VALUES (?, ?, ?)
                        ON CONFLICT(discord_id, item_name) DO UPDATE SET amount = amount + excluded.amount;
                    `;
        await dbRunQueued(addItemSql, [userId, itemKey, amountChange]);
      } else {
        // Removing items

        const removeItemSql = `
                        UPDATE player_inventory_items
                        SET amount = amount - ?
                        WHERE discord_id = ? AND item_name = ? AND amount >= ?;
                    `;
        const amountToRemove = Math.abs(amountChange);

        // Force type conversion - ensures all parameters are properly typed
        const removeResult = await dbRunQueued(removeItemSql, [
          Number(amountToRemove),
          String(userId),
          String(itemKey),
          Number(amountToRemove),
        ]);
        if (removeResult.changes === 0) {
          // Check if the item exists at all or just had insufficient amount
          const checkExistsSql =
            "SELECT amount FROM player_inventory_items WHERE discord_id = ? AND item_name = ?";
          const existingItem = await dbGet(checkExistsSql, [userId, itemKey]);
          if (!existingItem || existingItem.amount < amountToRemove) {
            throw new Error(
              `Insufficient amount of ${itemKey} to remove ${amountToRemove}. Have ${
                existingItem ? existingItem.amount : 0
              }.`
            );
          } else {
            console.error(
              `[Inv Atomic] Failed to remove ${itemKey} for unknown reason despite sufficient amount check passing.`
            );
            throw new Error(`Database error removing ${itemKey}.`);
          }
        }
        // Clean up row if amount becomes zero or less
        const cleanupSql =
          "DELETE FROM player_inventory_items WHERE discord_id = ? AND item_name = ? AND amount <= 0";
        await dbRunQueued(cleanupSql, [userId, itemKey]);
      }
    }

    // --- 3. Add Equipment (in player_equipment table) ---
    const addEquipSql =
      "INSERT INTO player_equipment (equipment_id, discord_id, item_key, is_equipped, data_json) VALUES (?, ?, ?, 0, ?)"; // Always add as unequipped
    for (const equip of equipmentToAdd) {
      if (!equip.itemKey) {
        console.warn("[Inv Atomic] Skipping invalid equipment add:", equip);
        continue;
      }
      const equipId = equip.id || uuidv4(); // Allow passing ID or generate one

      // Check if this is a unique item and add timestamp if so
      const itemData = configManager.getItem(equip.itemKey);
      let dataJson = "{}";

      // Use provided dataJson if available, otherwise create default for unique items
      if (equip.dataJson) {
        dataJson =
          typeof equip.dataJson === "string"
            ? equip.dataJson
            : JSON.stringify(equip.dataJson);
      } else if (itemData && itemData.unique === true) {
        dataJson = JSON.stringify({
          createdAt: new Date().toISOString(),
        });
      }

      await dbRunQueued(addEquipSql, [
        equipId,
        userId,
        equip.itemKey,
        dataJson,
      ]);
    }

    // --- 4. Remove Equipment (from player_equipment table) ---
    for (const equipId of equipmentIdsToRemove) {
      if (!equipId) continue;

      // Check if the equipment exists before attempting to remove it
      const checkEquipSql =
        "SELECT equipment_id FROM player_equipment WHERE discord_id = ? AND equipment_id = ?";
      const existingEquip = await dbGet(checkEquipSql, [userId, equipId]);

      if (!existingEquip) {
        // If the item doesn't exist, throw an error to prevent coin addition
        throw new Error(
          `Equipment with ID ${equipId} not found for user ${userId}.`
        );
      }

      const removeEquipSql =
        "DELETE FROM player_equipment WHERE discord_id = ? AND equipment_id = ?";
      const removeResult = await dbRunQueued(removeEquipSql, [userId, equipId]);
      if (removeResult.changes === 0) {
        // This case should ideally be caught by the check above, but as a fallback
        console.warn(
          `[Inv Atomic] Attempted to remove non-existent equipment ID ${equipId} for user ${userId}.`
        );
        throw new Error(`Failed to remove equipment with ID ${equipId}.`);
      }
    }

    // --- 5. Add Accessories (in player_accessories table) ---
    const addAccessorySql =
      "INSERT INTO player_accessories (accessory_id, discord_id, item_key, is_equipped, data_json) VALUES (?, ?, ?, 0, ?)"; // Always add as unequipped
    for (const accessory of accessoriesToAdd) {
      if (!accessory.itemKey) {
        console.warn("[Inv Atomic] Skipping invalid accessory add:", accessory);
        continue;
      }
      const accessoryId = accessory.id || uuidv4(); // Allow passing ID or generate one

      // Check if this is a unique item and add timestamp if so
      const itemData = configManager.getItem(accessory.itemKey);
      let dataJson = "{}";
      if (itemData && itemData.unique === true) {
        dataJson = JSON.stringify({
          createdAt: new Date().toISOString(),
        });
      }

      await dbRunQueued(addAccessorySql, [
        accessoryId,
        userId,
        accessory.itemKey,
        dataJson,
      ]);
    }

    // --- 6. Remove Accessories (from player_accessories table) ---
    for (const accessoryId of accessoryIdsToRemove) {
      if (!accessoryId) continue;

      // Check if the accessory exists before attempting to remove it
      const checkAccessorySql =
        "SELECT accessory_id FROM player_accessories WHERE discord_id = ? AND accessory_id = ?";
      const existingAccessory = await dbGet(checkAccessorySql, [
        userId,
        accessoryId,
      ]);

      if (!existingAccessory) {
        // If the item doesn't exist, throw an error to prevent coin addition
        throw new Error(
          `Accessory with ID ${accessoryId} not found for user ${userId}.`
        );
      }

      const removeAccessorySql =
        "DELETE FROM player_accessories WHERE discord_id = ? AND accessory_id = ?";
      const removeResult = await dbRunQueued(removeAccessorySql, [
        userId,
        accessoryId,
      ]);
      if (removeResult.changes === 0) {
        // This case should ideally be caught by the check above, but as a fallback
        console.warn(
          `[Inv Atomic] Attempted to remove non-existent accessory ID ${accessoryId} for user ${userId}.`
        );
        throw new Error(`Failed to remove accessory with ID ${accessoryId}.`);
      }
    }

    // --- 7. Update Equipment Data (e.g., for enchantments) ---
    if (equipmentDataUpdates.length > 0) {
      const updateEquipDataSql =
        "UPDATE player_equipment SET data_json = ? WHERE equipment_id = ? AND discord_id = ?";
      for (const update of equipmentDataUpdates) {
        if (!update.equipmentId || typeof update.dataJson !== "string") {
          console.warn(
            "[Inv Atomic] Skipping invalid equipment data update:",
            update
          );
          continue;
        }
        // Ensure dataJson is a string, even if it's an empty object stringified
        const dataJsonString =
          typeof update.dataJson === "object"
            ? JSON.stringify(update.dataJson)
            : update.dataJson;

        const result = await dbRunQueued(updateEquipDataSql, [
          dataJsonString,
          update.equipmentId,
          userId,
        ]);
        if (result.changes === 0) {
          console.warn(
            `[Inv Atomic] Failed to update data_json for equipment ID ${update.equipmentId} for user ${userId}. Item might not exist or belong to user.`
          );
          throw new Error(
            `Failed to update data_json for equipment ID ${update.equipmentId}. Item not found or not owned by user.`
          );
        }
      }
    }

    // --- 7. Update Island and Collections Data (in players table) ---
    if (islandJsonString !== null && collectionsJsonString !== null) {
      const updateIslandCollectionsSql =
        "UPDATE players SET island_json = ?, collections_json = ? WHERE discord_id = ?";
      const result = await dbRunQueued(updateIslandCollectionsSql, [
        islandJsonString,
        collectionsJsonString,
        userId,
      ]);
      if (result.changes === 0) {
        console.warn(
          `[Inv Atomic] Attempted to update island/collections data for non-existent user ${userId}.`
        );
        // Decide if this should be an error or just a warning. For now, warning.
      }
    } else if (islandJsonString !== null) {
      const updateIslandSql =
        "UPDATE players SET island_json = ? WHERE discord_id = ?";
      const result = await dbRunQueued(updateIslandSql, [
        islandJsonString,
        userId,
      ]);
      if (result.changes === 0) {
        console.warn(
          `[Inv Atomic] Attempted to update island data for non-existent user ${userId}.`
        );
      }
    } else if (collectionsJsonString !== null) {
      const updateCollectionsSql =
        "UPDATE players SET collections_json = ? WHERE discord_id = ?";
      const result = await dbRunQueued(updateCollectionsSql, [
        collectionsJsonString,
        userId,
      ]);
      if (result.changes === 0) {
        console.warn(
          `[Inv Atomic] Attempted to update collections data for non-existent user ${userId}.`
        );
      }
    }

    // If all operations succeeded and we're not using an existing transaction
    if (!useExistingTransaction) {
      // Batched operations are automatically committed
    }

    // Add final verification for ENCHANTED_RAW_SALMON operations
    if (itemsToChange.some((item) => item.itemKey === "ENCHANTED_RAW_SALMON")) {
      console.log(
        "[SALMON DEBUG][FINAL] Transaction committed. Checking final state of ENCHANTED_RAW_SALMON:"
      );
      const checkSql =
        "SELECT amount FROM player_inventory_items WHERE discord_id = ? AND item_name = ?";
      try {
        const finalItem = await dbGet(checkSql, [
          userId,
          "ENCHANTED_RAW_SALMON",
        ]);
        console.log(
          `[SALMON DEBUG][FINAL] Final amount in DB: ${
            finalItem ? finalItem.amount : "Not found (consumed all)"
          }`
        );
      } catch (checkErr) {
        console.error(
          "[SALMON DEBUG][FINAL] Error checking final state:",
          checkErr
        );
      }
    }
  };

  // Execute with or without transaction wrapper based on parameter
  if (useExistingTransaction) {
    // Use existing transaction - just execute the logic
    return executeTransaction();
  } else {
    // Create new transaction
    return new Promise((resolve, reject) => {
      db.serialize(async () => {
        try {
          // Batching system handles transactions automatically
          await executeTransaction();
          resolve();
        } catch (err) {
          const msg = String(err.message || "");
          const isExpected =
            msg.includes("Insufficient coins") ||
            msg.includes("Insufficient bank coins") ||
            msg.includes("Insufficient amount") ||
            msg.includes("Concurrent modification detected");

          if (!isExpected) {
            console.error(
              `[Inv Atomic] Error during transaction for ${userId}, rolling back:`,
              {
                error: err.message,
                code: err.code,
                errno: err.errno,
                stack: err.stack,
              }
            );
            console.log(
              `[Inv Atomic] Transaction automatically rolled back for ${userId}`
            );
          } else {
            // Keep logs minimal for expected failures
            //console.warn(`[Inv Atomic] Expected failure for ${userId}: ${msg}`);
          }
          const enhancedError = new Error(`Transaction failed: ${err.message}`);
          enhancedError.originalError = err;
          reject(enhancedError);
        }
      });
    });
  }
}

async function getInventory(userId) {
  try {
    // Fetch items
    const itemsSql =
      "SELECT item_name, amount FROM player_inventory_items WHERE discord_id = ?";
    const itemRows = await dbAll(itemsSql, [userId]);
    const items = {};
    itemRows.forEach((row) => {
      items[row.item_name] = row.amount;
    });

    // Fetch equipment - Update to include data_json for enchantments
    const equipSql =
      "SELECT equipment_id, item_key, is_equipped, data_json FROM player_equipment WHERE discord_id = ?";
    const equipRows = await dbAll(equipSql, [userId]);
    const equipment = equipRows.map((row) => {
      // Parse data_json if it exists
      let dataJson = {};
      if (row.data_json) {
        try {
          dataJson =
            typeof row.data_json === "string"
              ? JSON.parse(row.data_json)
              : row.data_json;
        } catch (e) {
          console.error(
            `Error parsing data_json for equipment_id ${row.equipment_id}:`,
            e
          );
          dataJson = {};
        }
      }

      return {
        id: row.equipment_id,
        itemKey: row.item_key,
        isEquipped: Boolean(row.is_equipped),
        data_json: dataJson,
      };
    });

    return { items, equipment };
  } catch (error) {
    console.error(`Error getting inventory for ${userId}:`, error);
    throw error;
  }
}

async function unequipItemAtomically(userId, equipmentId) {
  try {
    // Instead of removing, set is_equipped = 0 for the equipmentId
    const updateSql =
      "UPDATE player_equipment SET is_equipped = 0 WHERE discord_id = ? AND equipment_id = ?";
    const result = await dbRunQueued(updateSql, [userId, equipmentId]);
    if (result.changes === 0) {
      return {
        success: false,
        message: "Equipment not found or already unequipped.",
      };
    }

    // Recalculate player stats
    const player = await getPlayerData(userId);
    await recalculateAndSaveStats(userId, player);

    return { success: true };
  } catch (err) {
    return { success: false, message: err.message };
  }
}

async function equipItemAtomically(userId, equipmentId) {
  try {
    // Find the item_key for the equipmentId
    const getItemKeySql =
      "SELECT item_key FROM player_equipment WHERE equipment_id = ? AND discord_id = ?";
    const row = await dbGet(getItemKeySql, [equipmentId, userId]);
    if (!row) {
      return { success: false, message: "Equipment not found." };
    }
    const itemKey = row.item_key;
    // Unequip any currently equipped item in the SAME SLOT
    const subtypeToSlot = {
      SWORD: "WEAPON",
      BOW: "WEAPON",
      PICKAXE: "PICKAXE",
      SHOVEL: "SHOVEL",
      HOE: "HOE",
      ROD: "ROD",
      HELMET: "HELMET",
      CHESTPLATE: "CHESTPLATE",
      LEGGINGS: "LEGGINGS",
      BOOTS: "BOOTS",
      NECKLACE: "NECKLACE",
      CLOAK: "CLOAK",
      BELT: "BELT",
      GLOVES: "GLOVES",
      BRACELET: "GLOVES",
    };
    const itemDef = configManager.getItem(itemKey);
    const slotKey = itemDef?.subtype || itemDef?.type;
    let slot;
    // Special handling for AXE and FARMING_AXE as they can be both a weapon and a tool
    if (slotKey === "AXE" || slotKey === "FARMING_AXE") {
      if (itemDef.type === "TOOL") {
        slot = "AXE"; // Tool axes go in AXE slot
      } else {
        slot = "WEAPON"; // Weapon axes go in WEAPON slot
      }
    } else {
      // Normal slot assignment for non-axe items
      slot =
        slotKey && subtypeToSlot[slotKey.toUpperCase()]
          ? subtypeToSlot[slotKey.toUpperCase()]
          : slotKey?.toUpperCase();
    }
    // Find all currently equipped items for this user
    const equippedSql =
      "SELECT equipment_id, item_key FROM player_equipment WHERE discord_id = ? AND is_equipped = 1";
    const equippedRows = await dbAll(equippedSql, [userId]);
    let unequippedItemKey = null;
    for (const eq of equippedRows) {
      const eqDef = configManager.getItem(eq.item_key);
      const eqSlotKey = eqDef?.subtype || eqDef?.type;
      // Special handling for equipped AXE and FARMING_AXE items
      let eqSlot;
      if (eqSlotKey === "AXE" || eqSlotKey === "FARMING_AXE") {
        if (eqDef.type === "TOOL") {
          eqSlot = "AXE"; // Tool axes are in AXE slot
        } else {
          eqSlot = "WEAPON"; // Weapon axes are in WEAPON slot
        }
      } else {
        // Normal slot assignment for non-axe items
        eqSlot =
          eqSlotKey && subtypeToSlot[eqSlotKey.toUpperCase()]
            ? subtypeToSlot[eqSlotKey.toUpperCase()]
            : eqSlotKey?.toUpperCase();
      }
      if (eqSlot === slot) {
        // Unequip this item
        const unequipSql =
          "UPDATE player_equipment SET is_equipped = 0 WHERE discord_id = ? AND equipment_id = ?";
        await dbRunQueued(unequipSql, [userId, eq.equipment_id]);
        unequippedItemKey = eq.item_key;
      }
    }
    // Now, equip the new item
    const equipSql =
      "UPDATE player_equipment SET is_equipped = 1 WHERE discord_id = ? AND equipment_id = ?";
    const result = await dbRunQueued(equipSql, [userId, equipmentId]);
    if (result.changes === 0) {
      return {
        success: false,
        message: "Equipment not found or already equipped.",
      };
    }
    // Recalculate player stats
    const player = await getPlayerData(userId);
    await recalculateAndSaveStats(userId, player);
    return { success: true, unequippedItemKey };
  } catch (err) {
    return { success: false, message: err.message };
  }
}

module.exports = {
  updateInventoryAtomically,
  getInventory, // Exporting again as it reads from separate tables
  unequipItemAtomically,
  equipItemAtomically,
};
