/**
 * Migration 039: Add cake_notified column to player_last_active_channel table
 * Used for Baker cake notifications
 */
module.exports = {
  /**
   * @param {import('sqlite3').Database} db
   * @returns {Promise<void>}
   */
  up: async (db) => {
    return new Promise((resolve, reject) => {
      db.run(
        `
                ALTER TABLE player_last_active_channel 
                ADD COLUMN cake_notified INTEGER DEFAULT NULL
            `,
        (err) => {
          if (err) {
            reject(err);
          } else {
            console.log(
              "Migration 039: Added cake_notified column to player_last_active_channel table",
            );
            resolve();
          }
        },
      );
    });
  },
};
