const { dbGet } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");
const configManager = require("./configManager");

/**
 * Gets the equipped fishing rod for a player
 * @param {string} userId - The Discord user ID
 * @returns {Object|null} The equipped fishing rod data or null if none equipped
 */
async function getEquippedFishingRod(userId) {
  try {
    // Get player data to check if they have a fishing rod equipped
    const playerData = await getPlayerData(userId);
    if (
      !playerData ||
      !playerData.inventory ||
      !playerData.inventory.equipment
    ) {
      return null;
    }

    // Get all items data to check item types
    const allItems = configManager.getAllItems();

    // Find the equipped fishing rod in the equipment array
    // A fishing rod has type: "TOOL" and subtype: "ROD"
    const equippedRod = playerData.inventory.equipment.find((item) => {
      if (!item.isEquipped) return false;
      const itemData = allItems[item.itemKey];
      return itemData && itemData.type === "TOOL" && itemData.subtype === "ROD";
    });

    if (!equippedRod) {
      return null;
    }

    // Get the full equipment data from the database
    const fishingRodData = await dbGet(
      "SELECT * FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
      [equippedRod.id, userId]
    );

    return fishingRodData;
  } catch (error) {
    console.error(
      "[equipmentUtils] Error getting equipped fishing rod:",
      error
    );
    return null;
  }
}

module.exports = {
  getEquippedFishingRod,
};
