/**
 * Disblock XP and Level System
 * Handles XP calculation, level progression, and notifications
 */

const { EmbedBuilder } = require("discord.js");
const { COLLECTIONS } = require("../data/collections");
const configManager = require("./configManager");
const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
const { calculateMagicalPower } = require("./magicalPowerUtils");

// Logging function
function logGardenXP(message, data = null) {
  const timestamp = new Date().toISOString();
  if (data) {
    //console.log(`[${timestamp}] [DisblockXP-Garden] ${message}`, data);
  } else {
    //console.log(`[${timestamp}] [DisblockXP-Garden] ${message}`);
  }
}

// Helper function to get minion emoji
function getMinionEmoji(minionKey) {
  // Remove tier suffix if present (e.g., COBBLESTONE_MINION_T5 -> COBBLESTONE_MINION)
  const baseKey = minionKey.replace(/_T\d+$/, "");
  return configManager.getItem(baseKey)?.emoji || "❓";
}

// XP Configuration
const XP_PER_LEVEL = 100;

// XP Rewards Configuration
const XP_REWARDS = {
  MINION_CRAFT: {
    1: 1,
    2: 1,
    3: 1,
    4: 1,
    5: 1,
    6: 1,
    7: 2,
    8: 3,
    9: 4,
    10: 6,
    // 11+ for future proofing
    default: 12,
  },
  SKILL_LEVEL: {
    "1-10": 5,
    "11-25": 10,
    "26-50": 20,
    "50+": 30, // Future proofing
  },
  SLAYER_LEVEL: {
    zombie: {
      1: 15,
      2: 25,
      3: 35,
      4: 50,
      5: 75,
      6: 100,
      7: 125,
      8: 150,
      9: 150,
    },
    spider: {
      1: 15,
      2: 25,
      3: 35,
      4: 50,
      5: 75,
      6: 100,
      7: 125,
      8: 150,
      9: 150,
    },
  },
  COLLECTION_TIER: 4, // Fixed amount per collection milestone
  MAGICAL_POWER: 1, // 1 XP per Magical Power
  PET_SCORE: 3, // 3 XP per Pet Score point (matches Hypixel SkyBlock)
  GARDEN_LEVEL: 10, // 10 XP per Garden level beyond level 1
};

// Garden Level Configuration
const GARDEN_LEVEL_XP_REWARD = 10; // XP per Garden level beyond 1
const GARDEN_STARTING_LEVEL = 1; // Starting Garden level

// Pet Score values by rarity (based on Hypixel Skyblock Pet Score system)
const PET_SCORE_VALUES = {
  COMMON: 1,
  UNCOMMON: 2,
  RARE: 3,
  EPIC: 4,
  LEGENDARY: 5,
};

/**
 * Calculate Disblock Level from XP
 * @param {number} xp - Total Disblock XP
 * @returns {object} - { level, xpInCurrentLevel, xpForNextLevel, percentage }
 */
function calculateDisblockLevel(xp) {
  const level = Math.floor(xp / XP_PER_LEVEL);
  const xpInCurrentLevel = xp % XP_PER_LEVEL;
  const xpForNextLevel = XP_PER_LEVEL - xpInCurrentLevel;
  const percentage = (xpInCurrentLevel / XP_PER_LEVEL) * 100;

  return {
    level,
    xpInCurrentLevel,
    xpForNextLevel,
    percentage: Math.round(percentage),
  };
}

/**
 * Format Disblock Level for display
 * @param {number} xp - Total Disblock XP
 * @returns {string} - Formatted level string (e.g., "3.34")
 */
function formatDisblockLevel(xp) {
  const { level, percentage } = calculateDisblockLevel(xp);
  return `${level}.${percentage.toString().padStart(2, "0")}`;
}

/**
 * Calculate XP for minion craft/upgrade
 * @param {number} tier - Minion tier
 * @returns {number} - XP amount
 */
function calculateMinionXP(tier) {
  const tierNum = Number(tier);
  if (isNaN(tierNum)) {
    return 0; // Default to 0 XP if tier is invalid
  }
  if (tierNum >= 11) return XP_REWARDS.MINION_CRAFT.default || 0;
  return XP_REWARDS.MINION_CRAFT[tierNum] || 0;
}

/**
 * Calculate XP for skill level up
 * @param {number} level - Skill level
 * @returns {number} - XP amount
 */
function calculateSkillXP(level) {
  if (level >= 51) return XP_REWARDS.SKILL_LEVEL["50+"];
  if (level >= 26) return XP_REWARDS.SKILL_LEVEL["26-50"];
  if (level >= 11) return XP_REWARDS.SKILL_LEVEL["11-25"];
  if (level >= 1) return XP_REWARDS.SKILL_LEVEL["1-10"];
  return 0;
}

/**
 * Calculate XP for slayer level up
 * @param {string} slayerType - Slayer type (zombie/spider)
 * @param {number} level - Slayer level
 * @returns {number} - XP amount
 */
function calculateSlayerXP(slayerType, level) {
  const slayerRewards = XP_REWARDS.SLAYER_LEVEL[slayerType];
  if (!slayerRewards) {
    return 0; // Unknown slayer type
  }
  return slayerRewards[level] || 0;
}

/**
 * Calculate XP for collection tier unlock
 * @returns {number} - XP amount (always 4)
 */
function calculateCollectionXP() {
  return XP_REWARDS.COLLECTION_TIER;
}

/**
 * Calculate XP from Magical Power
 * @param {number} magicalPower - Total Magical Power
 * @returns {number} - XP amount
 */
function calculateMagicalPowerXP(magicalPower) {
  return magicalPower * XP_REWARDS.MAGICAL_POWER;
}

/**
 * Calculate Pet Score from all pets owned (matches Hypixel SkyBlock mechanics)
 * Only counts unique pets by type, using highest rarity if multiple exist
 * +1 score for each max level pet
 * @param {object} playerData - Player data object containing pets array
 * @returns {number} - Total Pet Score
 */
function calculatePetScore(playerData) {
  if (!playerData || !playerData.pets || !Array.isArray(playerData.pets)) {
    return 0;
  }

  const { calculateLevelFromTotalXp } = require("../data/petLeveling");
  const uniquePets = new Map(); // Map to store highest rarity pet of each type

  // Group pets by type and keep only the highest rarity
  for (const pet of playerData.pets) {
    if (pet && pet.petKey && pet.rarity && pet.totalExp !== undefined) {
      const petType = pet.petKey;
      const currentRarityValue = PET_SCORE_VALUES[pet.rarity] || 0;

      if (
        !uniquePets.has(petType) ||
        currentRarityValue >
          (PET_SCORE_VALUES[uniquePets.get(petType).rarity] || 0)
      ) {
        uniquePets.set(petType, pet);
      }
    }
  }

  let totalPetScore = 0;

  // Calculate score from unique pets
  for (const [, pet] of uniquePets) {
    const baseScore = PET_SCORE_VALUES[pet.rarity] || 0;
    totalPetScore += baseScore;

    // Check if pet is max level (level 100) and add +1 bonus
    const petLevel = calculateLevelFromTotalXp(pet.rarity, pet.totalExp).level;
    if (petLevel >= 100) {
      totalPetScore += 1; // +1 score for max level pets
    }
  }

  return totalPetScore;
}

/**
 * Calculate XP from Pet Score
 * @param {number} petScore - Total Pet Score
 * @returns {number} - XP amount
 */
function calculatePetScoreXP(petScore) {
  return Math.floor(petScore * XP_REWARDS.PET_SCORE);
}

/**
 * Calculate Magic Find bonus from Pet Score
 * @param {number} petScore - Total Pet Score
 * @returns {number} - Magic Find bonus amount
 */
function calculateMagicFindFromPetScore(petScore) {
  if (petScore >= 500) return 13;
  if (petScore >= 400) return 12;
  if (petScore >= 350) return 11;
  if (petScore >= 300) return 10;
  if (petScore >= 250) return 9;
  if (petScore >= 200) return 8;
  if (petScore >= 175) return 7;
  if (petScore >= 150) return 6;
  if (petScore >= 125) return 5;
  if (petScore >= 100) return 4;
  if (petScore >= 50) return 3;
  if (petScore >= 25) return 2;
  if (petScore >= 10) return 1;
  return 0;
}

/**
 * Calculate detailed XP breakdown from player data
 * @param {object} playerData - Player data object
 * @param {string} userId - Discord user ID (for Magical Power calculation)
 * @returns {Promise<object>} - Detailed XP breakdown with totals
 */
async function calculateXPBreakdown(playerData, userId = null) {
  const breakdown = {
    skills: { total: 0, details: {} },
    collections: { total: 0, details: {} },
    minions: { total: 0, count: 0 },
    magicalPower: { total: 0, mp: 0 },
    petScore: { total: 0, score: 0 },
    slayers: { total: 0, details: {} },
    gardenLevel: { total: 0, level: 0 },
    grandTotal: 0,
  };

  // XP from skill levels
  const { getLevelFromExp } = require("./expFunctions");
  if (playerData.skills) {
    for (const [skillName, skillData] of Object.entries(playerData.skills)) {
      if (skillData && typeof skillData.exp === "number") {
        const { level } = getLevelFromExp(skillData.exp);
        let skillXP = 0;

        // Calculate XP based on skill level ranges (cumulative)
        if (level >= 1 && level <= 10) {
          skillXP = level * XP_REWARDS.SKILL_LEVEL["1-10"];
        } else if (level >= 11 && level <= 25) {
          skillXP =
            10 * XP_REWARDS.SKILL_LEVEL["1-10"] +
            (level - 10) * XP_REWARDS.SKILL_LEVEL["11-25"];
        } else if (level >= 26 && level <= 50) {
          skillXP =
            10 * XP_REWARDS.SKILL_LEVEL["1-10"] +
            15 * XP_REWARDS.SKILL_LEVEL["11-25"] +
            (level - 25) * XP_REWARDS.SKILL_LEVEL["26-50"];
        } else if (level > 50) {
          skillXP =
            10 * XP_REWARDS.SKILL_LEVEL["1-10"] +
            15 * XP_REWARDS.SKILL_LEVEL["11-25"] +
            25 * XP_REWARDS.SKILL_LEVEL["26-50"] +
            (level - 50) * XP_REWARDS.SKILL_LEVEL["50+"];
        }

        breakdown.skills.details[skillName] = { level, xp: skillXP };
        breakdown.skills.total += skillXP;
      }
    }
  }

  // XP from slayer levels
  if (playerData.slayerXp) {
    const { getAllSlayerLevels } = require("./slayerLevelUtils");
    const slayerLevels = getAllSlayerLevels(playerData.slayerXp);

    for (const [slayerType, slayerData] of Object.entries(slayerLevels)) {
      if (slayerData && typeof slayerData.level === "number") {
        const currentLevel = slayerData.level;
        let slayerXP = 0;

        // Calculate XP based on slayer level (cumulative)
        for (let level = 1; level <= currentLevel; level++) {
          slayerXP += calculateSlayerXP(slayerType, level);
        }

        breakdown.slayers.details[slayerType] = {
          level: currentLevel,
          xp: slayerXP,
        };
        breakdown.slayers.total += slayerXP;
      }
    }
  }

  // XP from collection tiers
  if (playerData.collections) {
    let totalTiers = 0;

    for (const [itemKey, amount] of Object.entries(playerData.collections)) {
      if (typeof amount === "number") {
        // Find the collection definition across all categories
        let collectionDef = null;
        for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
          if (categoryCollections[itemKey]) {
            collectionDef = categoryCollections[itemKey];
            break;
          }
        }

        if (collectionDef && collectionDef.tiers) {
          const unlockedTiers = collectionDef.tiers.filter(
            (tier) => amount >= tier.amount
          ).length;
          totalTiers += unlockedTiers;
          breakdown.collections.details[itemKey] = {
            amount,
            tiers: unlockedTiers,
          };
        }
      }
    }
    breakdown.collections.total = totalTiers * XP_REWARDS.COLLECTION_TIER;
  }

  // XP from unique minions (tier-based)
  if (playerData.craftedMinions) {
    let totalMinionXP = 0;
    let uniqueCount = 0;

    if (Array.isArray(playerData.craftedMinions)) {
      uniqueCount = playerData.craftedMinions.length;

      // Calculate XP based on tier for each crafted minion
      for (const minionKey of playerData.craftedMinions) {
        // Extract tier from key like "COBBLESTONE_MINION_T5"
        const tierMatch = minionKey.match(/_T(\d+)$/);
        if (tierMatch) {
          const tier = parseInt(tierMatch[1]);
          totalMinionXP += calculateMinionXP(tier);
        } else {
          // Fallback for old format without tier
          totalMinionXP += XP_REWARDS.MINION_CRAFT[1];
        }
      }
    } else if (
      typeof playerData.craftedMinions === "object" &&
      playerData.craftedMinions !== null
    ) {
      const keys = Object.keys(playerData.craftedMinions);
      uniqueCount = keys.length;

      // Calculate XP based on tier for each crafted minion
      for (const minionKey of keys) {
        const tierMatch = minionKey.match(/_T(\d+)$/);
        if (tierMatch) {
          const tier = parseInt(tierMatch[1]);
          totalMinionXP += calculateMinionXP(tier);
        } else {
          // Fallback for old format without tier
          totalMinionXP += XP_REWARDS.MINION_CRAFT[1];
        }
      }
    }

    breakdown.minions.count = uniqueCount;
    breakdown.minions.total = totalMinionXP;
  }

  // XP from Magical Power (shows total XP that would be earned from current MP)
  if (userId) {
    const magicalPower = await calculateMagicalPower(userId);
    breakdown.magicalPower.mp = magicalPower;
    breakdown.magicalPower.total = calculateMagicalPowerXP(magicalPower);
  }

  // XP from Garden Level (shows total XP that would be earned from current Garden level)
  let gardenLevel = GARDEN_STARTING_LEVEL;
  let gardenLevelXP = 0;

  try {
    const { getGardenLevel } = require("./gardenSystem");

    // Validate and sanitize Garden XP value
    let gardenXp = playerData.garden_xp || 0;
    if (typeof gardenXp !== "number" || isNaN(gardenXp) || gardenXp < 0) {
      logGardenXP(
        `Invalid Garden XP value detected: ${gardenXp}, defaulting to 0`
      );
      gardenXp = 0;
    }

    gardenLevel = getGardenLevel(gardenXp);

    // Validate Garden Level result
    if (
      typeof gardenLevel !== "number" ||
      isNaN(gardenLevel) ||
      gardenLevel < GARDEN_STARTING_LEVEL
    ) {
      logGardenXP(
        `Invalid Garden Level calculated: ${gardenLevel}, defaulting to ${GARDEN_STARTING_LEVEL}`
      );
      gardenLevel = GARDEN_STARTING_LEVEL;
    }

    // Calculate XP from Garden Level (10 XP per level beyond level 1)
    if (gardenLevel > GARDEN_STARTING_LEVEL) {
      gardenLevelXP =
        (gardenLevel - GARDEN_STARTING_LEVEL) * GARDEN_LEVEL_XP_REWARD;
      logGardenXP(
        `Calculated Garden Level XP: Level ${gardenLevel} = ${gardenLevelXP} XP`
      );
    }
  } catch (error) {
    logGardenXP(
      `Error calculating Garden Level XP: ${error.message}, using fallback values`
    );
    gardenLevel = GARDEN_STARTING_LEVEL;
    gardenLevelXP = 0;
  }

  breakdown.gardenLevel.level = gardenLevel;
  breakdown.gardenLevel.total = gardenLevelXP;

  // XP from Garden Crop Milestones (shows total XP that would be earned from current crop milestones)
  breakdown.gardenCropMilestones = { total: 0, details: {} };

  try {
    const {
      getGardenMilestones,
      getCropMilestoneInfo,
      CROP_MILESTONES,
    } = require("./gardenSystem");

    if (playerData.gardenMilestones) {
      const gardenMilestones = getGardenMilestones(playerData);

      // Ensure we have a proper object
      if (typeof gardenMilestones !== "object" || gardenMilestones === null) {
        logGardenXP(
          `ERROR: getGardenMilestones returned invalid type: ${typeof gardenMilestones}`
        );
        breakdown.gardenCropMilestones.total = 0;
      } else {
        let totalCropMilestoneXP = 0;

        // For each crop that has been harvested, calculate total XP from milestones
        for (const [cropKey, harvestedAmount] of Object.entries(
          gardenMilestones
        )) {
          if (
            typeof harvestedAmount === "number" &&
            harvestedAmount > 0 &&
            CROP_MILESTONES[cropKey]
          ) {
            const cropInfo = getCropMilestoneInfo(playerData, cropKey);
            const cropMilestoneXP = cropInfo.currentMilestone; // 1 XP per milestone

            breakdown.gardenCropMilestones.details[cropKey] = {
              harvested: harvestedAmount,
              milestones: cropInfo.currentMilestone,
              xp: cropMilestoneXP,
            };

            totalCropMilestoneXP += cropMilestoneXP;
          } else {
            // Skip invalid entries
          }
        }

        breakdown.gardenCropMilestones.total = totalCropMilestoneXP;
        logGardenXP(
          `Calculated Garden Crop Milestone XP: ${totalCropMilestoneXP} XP from ${Object.keys(breakdown.gardenCropMilestones.details).length} crops`
        );
      }
    } else {
      // No garden milestones data found
    }
  } catch (error) {
    logGardenXP(`Error calculating Garden Crop Milestone XP: ${error.message}`);
    breakdown.gardenCropMilestones.total = 0;
  }

  // XP from Pet Score (shows total XP that would be earned from current pets)
  const petScore = calculatePetScore(playerData);
  breakdown.petScore.score = petScore;
  breakdown.petScore.total = calculatePetScoreXP(petScore);

  breakdown.grandTotal =
    breakdown.skills.total +
    breakdown.collections.total +
    breakdown.minions.total +
    breakdown.magicalPower.total +
    breakdown.petScore.total +
    breakdown.slayers.total +
    breakdown.gardenLevel.total +
    breakdown.gardenCropMilestones.total;
  return breakdown;
}

/**
 * Calculate total XP from existing player data (for backwards compatibility)
 * @param {object} playerData - Player data object
 * @param {object} allItems - All items data
 * @param {string} userId - Discord user ID (for Magical Power calculation)
 * @returns {Promise<number>} - Total calculated XP
 */
async function calculateExistingPlayerXP(playerData, allItems, userId = null) {
  const breakdown = await calculateXPBreakdown(playerData, userId);
  return breakdown.grandTotal;
}

/**
 * Create XP gain notification embed
 * @param {number} xpGained - Amount of XP gained
 * @param {string} source - Source of XP (e.g., "Minion Craft", "Skill Level")
 * @param {string} details - Additional details (e.g., "Cobblestone Minion T5")
 * @returns {EmbedBuilder} - Discord embed
 */
function createXPGainEmbed(xpGained, source, details) {
  return new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setDescription(
      `💠 **+${xpGained} Disblock XP** from ${source}${details ? `\n${details}` : ""}`
    );
}

/**
 * Create grouped XP notification embed for multiple achievements of the same type
 * @param {Array} achievements - Array of achievements
 * @param {number} totalXP - Total XP gained from this group
 * @returns {EmbedBuilder} - Discord embed
 */
function createGroupedXPEmbed(achievements, totalXP) {
  const firstAchievement = achievements[0];
  const allItems = configManager.getAllItems();
  // const { skillEmojis } = require("../gameConfig");

  let description;

  if (firstAchievement.type === "skill") {
    const skillName =
      firstAchievement.skill.charAt(0).toUpperCase() +
      firstAchievement.skill.slice(1);
    const skillEmoji =
      skillEmojis[firstAchievement.skill.toLowerCase()] || "❓";

    if (achievements.length === 1) {
      // Single level up - simplified format
      description = `**+${totalXP} Disblock XP** from ${skillEmoji} **${skillName} Level ${achievements[0].level - 1} > ${achievements[0].level}**`;
    } else {
      // Multiple levels - detailed format
      const minLevel = Math.min(...achievements.map((a) => a.level));
      const maxLevel = Math.max(...achievements.map((a) => a.level));
      description = `**+${totalXP} Disblock XP** from ${skillEmoji} **${skillName} Level ${minLevel - 1} > ${maxLevel}**`;
    }
  } else if (firstAchievement.type === "slayer") {
    const slayerType = firstAchievement.slayerType;
    const slayerName = slayerType.charAt(0).toUpperCase() + slayerType.slice(1);

    // Get slayer emoji
    const SLAYER_EMOJIS = {
      zombie: "<:revenant_horror:1389646540460658970>",
      spider: "<:mob_spider:1370526342927618078>",
    };
    const slayerEmoji = SLAYER_EMOJIS[slayerType] || "⭐";

    if (achievements.length === 1) {
      // Single level up - simplified format
      description = `**+${totalXP} Disblock XP** from ${slayerEmoji} **${slayerName} Slayer Level ${achievements[0].level - 1} > ${achievements[0].level}**`;
    } else {
      // Multiple levels - detailed format
      const minLevel = Math.min(...achievements.map((a) => a.level));
      const maxLevel = Math.max(...achievements.map((a) => a.level));
      description = `**+${totalXP} Disblock XP** from ${slayerEmoji} **${slayerName} Slayer Level ${minLevel - 1} > ${maxLevel}**`;
    }
  } else if (firstAchievement.type === "collection") {
    // Find collection info
    let collectionDisplayName = firstAchievement.item
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());
    let itemEmoji = "❓";

    // Try to find the collection in COLLECTIONS data
    for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
      if (categoryCollections[firstAchievement.item]) {
        const collectionDef = categoryCollections[firstAchievement.item];
        if (collectionDef.name) {
          collectionDisplayName = collectionDef.name;
        }
        // Use collection emoji if available, otherwise try to get from items
        if (collectionDef.emoji) {
          itemEmoji = collectionDef.emoji;
        } else if (
          allItems[firstAchievement.item] &&
          allItems[firstAchievement.item].emoji
        ) {
          itemEmoji = allItems[firstAchievement.item].emoji;
        }
        break;
      }
    }

    if (achievements.length === 1) {
      // Single tier - simplified format
      description = `**+${totalXP} Disblock XP** from ${itemEmoji} **${collectionDisplayName} Collection Tier ${achievements[0].tier - 1} > ${achievements[0].tier}**`;
    } else {
      // Multiple tiers - detailed format
      const minTier = Math.min(...achievements.map((a) => a.tier));
      const maxTier = Math.max(...achievements.map((a) => a.tier));
      description = `**+${totalXP} Disblock XP** from ${itemEmoji} **${collectionDisplayName} Collection Tier ${minTier - 1} > ${maxTier}**`;
    }
  } else if (firstAchievement.type === "minion") {
    const minionName = firstAchievement.minion
      .replace(/_MINION_T\d+$/, "")
      .replace(/_/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (l) => l.toUpperCase());

    // Get minion emoji using helper function
    const minionEmoji = getMinionEmoji(firstAchievement.minion);

    if (achievements.length === 1) {
      // Single minion - simplified format (no progression for single minion)
      description = `**+${totalXP} Disblock XP** from ${minionEmoji} **${minionName} Minion Tier ${achievements[0].tier}**`;
    } else {
      // Multiple minion tiers - detailed format
      const minTier = Math.min(...achievements.map((a) => a.tier));
      const maxTier = Math.max(...achievements.map((a) => a.tier));
      description = `**+${totalXP} Disblock XP** from ${minionEmoji} **${minionName} Minion Tier ${minTier} > ${maxTier}**`;
    }
  } else if (firstAchievement.type === "magicalPower") {
    // Magical Power milestone
    const mpGained = firstAchievement.mpGained;
    const currentMP = firstAchievement.currentMP;
    const previousMP = firstAchievement.previousMP;

    description = `**+${totalXP} Disblock XP** from <:accessory_bag:1384159284123664516> **Magical Power ${previousMP} > ${currentMP}** (+${mpGained} MP)`;
  } else if (firstAchievement.type === "petScore") {
    // Pet Score milestone
    const scoreGained = firstAchievement.scoreGained;
    const currentScore = firstAchievement.currentScore;
    const previousScore = firstAchievement.previousScore;

    description = `**+${totalXP} Disblock XP** from <:diamond:1371269746586161193> **Pet Score ${previousScore} > ${currentScore}** (+${scoreGained} Score)`;
  } else if (firstAchievement.type === "gardenLevel") {
    // Garden Level milestone
    const gardenEmoji = "<:garden:1394656922623410237>"; // Garden custom emoji

    if (achievements.length === 1) {
      // Single level up - simplified format
      description = `**+${totalXP} Disblock XP** from ${gardenEmoji} **Garden Level ${achievements[0].level - 1} > ${achievements[0].level}**`;
    } else {
      // Multiple levels - detailed format
      const minLevel = Math.min(...achievements.map((a) => a.level));
      const maxLevel = Math.max(...achievements.map((a) => a.level));
      description = `**+${totalXP} Disblock XP** from ${gardenEmoji} **Garden Level ${minLevel - 1} > ${maxLevel}**`;
    }
  } else if (firstAchievement.type === "gardenCropMilestone") {
    // Garden Crop Milestone
    const configManager = require("./configManager");
    const cropName =
      configManager.getItem(firstAchievement.cropKey)?.name ||
      firstAchievement.cropKey;
    const cropEmoji =
      configManager.getItem(firstAchievement.cropKey)?.emoji || "🌾";

    if (achievements.length === 1) {
      // Single milestone - simplified format
      description = `**+${totalXP} Disblock XP** from ${cropEmoji} **${cropName} Milestone ${achievements[0].milestone}**`;
    } else {
      // Multiple milestones - detailed format
      const minMilestone = Math.min(...achievements.map((a) => a.milestone));
      const maxMilestone = Math.max(...achievements.map((a) => a.milestone));
      description = `**+${totalXP} Disblock XP** from ${cropEmoji} **${cropName} Milestone ${minMilestone} > ${maxMilestone}**`;
    }
  }

  return new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setDescription(description);
}

/**
 * Create level up notification embed
 * @param {number} newLevel - New level achieved
 * @param {number} totalXP - Total XP
 * @returns {EmbedBuilder} - Discord embed
 */
function createLevelUpEmbed(newLevel, totalXP, fromLevel = null) {
  let description;
  if (fromLevel !== null && fromLevel !== newLevel - 1) {
    // Show actual level transition when it's not consecutive
    description = `Your Disblock level increased from \`${fromLevel}\` to \`${newLevel}\`!`;
  } else {
    // Standard consecutive level-up message
    description = `You are now Disblock Level \`${newLevel}\`!`;
  }

  return new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle("<:disblock_level:1381196165696983080> Disblock Level Up!")
    .setDescription(description);
}

/**
 * Award XP to player and handle notifications
 * @param {string} userId - Discord user ID
 * @param {number} xpAmount - Amount of XP to award
 * @param {string} source - Source of XP
 * @param {string} details - Additional details
 * @param {object} interaction - Discord interaction (optional, for sending notifications)
 * @returns {Promise<object>} - { leveledUp: boolean, newLevel: number, totalXP: number }
 */
async function awardDisblockXP(
  userId,
  xpAmount,
  source,
  details,
  interaction = null
) {
  try {
    const { getPlayerData, savePlayerData } = require("./playerDataManager");

    // Get current player data
    const playerData = await getPlayerData(userId);

    // Initialize disblockXP if it doesn't exist
    if (typeof playerData.disblock_xp !== "number") {
      playerData.disblock_xp = 0;
    }

    const oldLevel = calculateDisblockLevel(playerData.disblock_xp).level;
    playerData.disblock_xp += xpAmount;
    const newLevel = calculateDisblockLevel(playerData.disblock_xp).level;

    // Save updated XP
    await savePlayerData(userId, { disblock_xp: playerData.disblock_xp }, [
      "disblock_xp",
    ]);

    const leveledUp = newLevel > oldLevel;

    // Send notifications if interaction is provided
    if (interaction && interaction.channel) {
      try {
        console.log(
          `[awardDisblockXP] Sending XP notification for ${source}: ${details}`
        );
        // Send XP gain notification as independent message
        const xpEmbed = createXPGainEmbed(xpAmount, source, details);
        await interaction.channel.send({ embeds: [xpEmbed] });

        // Send level up notification if applicable as independent message
        if (leveledUp) {
          const levelUpEmbed = createLevelUpEmbed(
            newLevel,
            playerData.disblock_xp,
            oldLevel
          );
          await interaction.channel.send({ embeds: [levelUpEmbed] });
        }

        // Update Discord nickname with current level and in-game username (on ANY XP gain)
        try {
          await updatePlayerNickname(
            userId,
            interaction,
            playerData.disblock_xp
          );
        } catch {
          // Error updating nickname
        }
      } catch {
        // Error sending notification

        // CRITICAL FALLBACK: Ensure level-up notifications are ALWAYS sent
        try {
          if (leveledUp) {
            // FALLBACK: Sending level-up notification
            const fallbackLevelUpEmbed = createLevelUpEmbed(
              newLevel,
              playerData.disblock_xp,
              oldLevel
            );
            await interaction.channel.send({ embeds: [fallbackLevelUpEmbed] });

            // Also try to update nickname in fallback
            try {
              await updatePlayerNickname(
                userId,
                interaction,
                playerData.disblock_xp
              );
            } catch {
              // FALLBACK: Error updating nickname
            }
          }
        } catch {
          // CRITICAL: Even fallback notification failed
        }
      }
    }

    return {
      leveledUp,
      newLevel,
      totalXP: playerData.disblock_xp,
      xpGained: xpAmount,
    };
  } catch {
    // Error awarding XP
    return {
      leveledUp: false,
      newLevel: 0,
      totalXP: 0,
      xpGained: 0,
    };
  }
}

/**
 * Get player's Disblock XP data and breakdown without triggering updates
 * @param {string} userId - Discord user ID
 * @returns {Promise<object>} - { breakdown: object, currentXP: number, level: object }
 */
async function getPlayerDisblockXPData(userId) {
  try {
    const { getPlayerData } = require("./playerDataManager");

    // Get specific fields needed for XP calculation
    const playerData = await getPlayerData(userId);

    // Calculate XP breakdown (now async and includes Magical Power)
    const breakdown = await calculateXPBreakdown(playerData, userId);
    const currentXP = playerData.disblock_xp || 0;
    const level = calculateDisblockLevel(currentXP);

    return {
      breakdown,
      currentXP,
      level,
      calculatedXP: breakdown.grandTotal,
    };
  } catch {
    return {
      breakdown: {
        skills: { total: 0, details: {} },
        collections: { total: 0, details: {} },
        minions: { total: 0, count: 0 },
        magicalPower: { total: 0, mp: 0 },
        petScore: { total: 0, score: 0 },
        slayers: { total: 0, details: {} },
        gardenLevel: { total: 0, level: 0 },
        grandTotal: 0,
      },
      currentXP: 0,
      level: {
        level: 0,
        xpInCurrentLevel: 0,
        xpForNextLevel: 100,
        percentage: 0,
      },
      calculatedXP: 0,
    };
  }
}

/**
 * Create detailed XP breakdown notification embeds
 * @param {object} breakdown - XP breakdown from calculateXPBreakdown
 * @param {number} totalXPGained - Total XP gained (currently unused)
 * @returns {Array<EmbedBuilder>} - Array of Discord embeds
 */
function createDetailedXPNotifications(breakdown) {
  const embeds = [];
  const allItems = configManager.getAllItems();

  // Create individual XP gain messages for each source
  if (breakdown.skills.total > 0) {
    for (const [skillName, skillData] of Object.entries(
      breakdown.skills.details
    )) {
      if (skillData.xp > 0) {
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setDescription(
            `**+${skillData.xp} Disblock XP** from ${skillName.charAt(0).toUpperCase() + skillName.slice(1)} Level ${skillData.level}`
          );
        embeds.push(embed);
      }
    }
  }

  if (breakdown.slayers.total > 0) {
    for (const [slayerType, slayerData] of Object.entries(
      breakdown.slayers.details
    )) {
      if (slayerData.xp > 0) {
        const slayerName =
          slayerType.charAt(0).toUpperCase() + slayerType.slice(1);
        const SLAYER_EMOJIS = {
          zombie: "<:revenant_horror:1389646540460658970>",
          spider: "<:mob_spider:1370526342927618078>",
        };
        const slayerEmoji = SLAYER_EMOJIS[slayerType] || "⭐";

        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setDescription(
            `**+${slayerData.xp} Disblock XP** from ${slayerEmoji} ${slayerName} Slayer Level ${slayerData.level}`
          );
        embeds.push(embed);
      }
    }
  }

  if (breakdown.collections.total > 0) {
    for (const [itemKey, collectionData] of Object.entries(
      breakdown.collections.details
    )) {
      if (collectionData.tiers > 0) {
        const xpFromCollection =
          collectionData.tiers * XP_REWARDS.COLLECTION_TIER;

        // Get collection display name and emoji
        let collectionDisplayName = itemKey;
        let itemEmoji = "";

        // Find collection definition to get display name
        for (const categoryName in COLLECTIONS) {
          if (COLLECTIONS[categoryName][itemKey]) {
            collectionDisplayName = COLLECTIONS[categoryName][itemKey].name;
            break;
          }
        }

        // Get item emoji from items.json
        if (allItems[itemKey] && allItems[itemKey].emoji) {
          itemEmoji = allItems[itemKey].emoji + " ";
        }

        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setDescription(
            `**+${xpFromCollection} Disblock XP** from ${itemEmoji}**${collectionDisplayName}** Collection (${collectionData.tiers} tiers)`
          );
        embeds.push(embed);
      }
    }
  }

  if (breakdown.minions.total > 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setDescription(
        `**+${breakdown.minions.total} Disblock XP** from ${breakdown.minions.count} Unique Minions`
      );
    embeds.push(embed);
  }

  if (breakdown.petScore && breakdown.petScore.total > 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setDescription(
        `**+${breakdown.petScore.total} Disblock XP** from Pet Score (${breakdown.petScore.score} Score)`
      );
    embeds.push(embed);
  }

  if (breakdown.gardenLevel && breakdown.gardenLevel.total > 0) {
    const gardenEmoji = "<:garden:1394656922623410237>"; // Garden custom emoji
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setDescription(
        `**+${breakdown.gardenLevel.total} Disblock XP** from ${gardenEmoji} **Garden Level ${breakdown.gardenLevel.level}**`
      );
    embeds.push(embed);
  }

  return embeds;
}

/**
 * Check and notify for new Disblock XP milestones only
 * @param {string} userId - Discord user ID
 * @param {object} interaction - Discord interaction (for sending notifications)
 * @returns {Promise<object>} - { totalXP: number, xpGained: number, leveledUp: boolean, newLevel: number }
 */
async function checkAndNotifyDisblockXP(
  userId,
  interaction,
  playerData = null
) {
  try {
    const { getPlayerData, savePlayerData } = require("./playerDataManager");
    const { getLevelFromExp } = require("./expFunctions");

    // Use provided player data or fetch it if not provided (for backwards compatibility)
    if (!playerData) {
      playerData = await getPlayerData(userId);
    }

    // Initialize milestone tracking if it doesn't exist
    const milestones = playerData.disblockMilestones || {
      skills: {},
      collections: {},
      minions: [],
      magicalPower: 0, // Track highest MP achieved
      petScore: 0, // Track highest Pet Score achieved
      slayers: {}, // Track highest slayer levels achieved
      gardenLevel: 0, // Track highest Garden level achieved
      gardenCropMilestones: {}, // Track highest crop milestones achieved
    };

    // Ensure skills field exists (for existing players who might not have it)
    if (!milestones.skills) {
      milestones.skills = {};
    }

    // Ensure collections field exists (for existing players who might not have it)
    if (!milestones.collections) {
      milestones.collections = {};
    }

    // Ensure slayers field exists (for existing players who might not have it)
    if (!milestones.slayerXp) {
      milestones.slayerXp = {};
    }

    // Ensure gardenLevel field exists (for existing players who might not have it)
    if (typeof milestones.gardenLevel !== "number") {
      milestones.gardenLevel = 0;
    }

    // Ensure gardenCropMilestones field exists (for existing players who might not have it)
    if (
      !milestones.gardenCropMilestones ||
      typeof milestones.gardenCropMilestones !== "object"
    ) {
      milestones.gardenCropMilestones = {};
    }

    const currentXP = playerData.disblock_xp || 0;
    const oldLevel = calculateDisblockLevel(currentXP).level;

    // Calculate accurate total XP using the same method as /level command
    const accurateBreakdown = await calculateXPBreakdown(playerData, userId);
    const accurateTotalXP = accurateBreakdown.grandTotal;

    // Check if there's any XP difference (new achievements)
    const xpDifference = accurateTotalXP - currentXP;

    // Find new achievements for notification purposes
    const newAchievements = [];
    let totalXPGained = xpDifference;

    // PURE MILESTONE RECONCILIATION: Calculate what milestones SHOULD be vs what they ARE

    // Calculate what milestones should be based on current player data
    const calculatedMilestones = {
      skills: {},
      collections: {},
      minions: [],
      magicalPower: 0,
      petScore: 0,
      slayers: {},
      gardenLevel: 0,
      gardenCropMilestones: {},
    };

    // Skills: Calculate what skill milestones should be
    if (playerData.skills) {
      for (const [skillName, skillData] of Object.entries(playerData.skills)) {
        if (skillData && typeof skillData.exp === "number") {
          const { level } = getLevelFromExp(skillData.exp);
          calculatedMilestones.skills[skillName] = level;
        }
      }
    }

    // Slayers: Calculate what slayer milestones should be
    if (playerData.slayerXp) {
      const { getAllSlayerLevels } = require("./slayerLevelUtils");
      const slayerLevels = getAllSlayerLevels(playerData.slayerXp);

      for (const [slayerType, slayerData] of Object.entries(slayerLevels)) {
        if (slayerData && typeof slayerData.level === "number") {
          calculatedMilestones.slayers[slayerType] = slayerData.level;
        }
      }
    }

    // Collections: Calculate what collection milestones should be
    if (playerData.collections) {
      for (const [itemKey, amount] of Object.entries(playerData.collections)) {
        if (typeof amount === "number") {
          // Find the collection definition
          let collectionDef = null;
          for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
            if (categoryCollections[itemKey]) {
              collectionDef = categoryCollections[itemKey];
              break;
            }
          }

          if (collectionDef && collectionDef.tiers) {
            const unlockedTiers = collectionDef.tiers.filter(
              (tier) => amount >= tier.amount
            ).length;
            calculatedMilestones.collections[itemKey] = unlockedTiers;
          }
        }
      }
    }

    // Minions: Calculate what minion milestones should be
    if (playerData.craftedMinions) {
      calculatedMilestones.minions = Array.isArray(playerData.craftedMinions)
        ? [...playerData.craftedMinions]
        : Object.keys(playerData.craftedMinions);
    }

    // Magical Power: Calculate what MP milestone should be
    calculatedMilestones.magicalPower = await calculateMagicalPower(userId);

    // Pet Score: Calculate what pet score milestone should be
    calculatedMilestones.petScore = calculatePetScore(playerData);

    // Garden Level: Calculate what garden level milestone should be
    try {
      const { getGardenLevel } = require("./gardenSystem");

      // Validate and sanitize Garden XP value
      let gardenXp = playerData.garden_xp || 0;
      if (typeof gardenXp !== "number" || isNaN(gardenXp) || gardenXp < 0) {
        logGardenXP(
          `Invalid Garden XP value in reconciliation: ${gardenXp}, defaulting to 0`
        );
        gardenXp = 0;
      }

      const gardenLevel = getGardenLevel(gardenXp);

      // Validate Garden Level result
      if (
        typeof gardenLevel !== "number" ||
        isNaN(gardenLevel) ||
        gardenLevel < GARDEN_STARTING_LEVEL
      ) {
        logGardenXP(
          `Invalid Garden Level in reconciliation: ${gardenLevel}, defaulting to ${GARDEN_STARTING_LEVEL}`
        );
        calculatedMilestones.gardenLevel = GARDEN_STARTING_LEVEL;
      } else {
        calculatedMilestones.gardenLevel = gardenLevel;
      }
    } catch (error) {
      logGardenXP(
        `Error calculating Garden Level in reconciliation: ${error.message}, using fallback`
      );
      calculatedMilestones.gardenLevel = GARDEN_STARTING_LEVEL;
    }

    // Garden Crop Milestones: Calculate what crop milestones should be based on harvest totals
    try {
      const {
        getGardenMilestones,
        getCropMilestoneInfo,
        CROP_MILESTONES,
      } = require("./gardenSystem");

      if (playerData.gardenMilestones) {
        const gardenMilestones = getGardenMilestones(playerData);

        // For each crop that has been harvested, calculate current milestone
        for (const [cropKey, harvestedAmount] of Object.entries(
          gardenMilestones
        )) {
          if (
            typeof harvestedAmount === "number" &&
            harvestedAmount > 0 &&
            CROP_MILESTONES[cropKey]
          ) {
            const cropInfo = getCropMilestoneInfo(playerData, cropKey);
            calculatedMilestones.gardenCropMilestones[cropKey] =
              cropInfo.currentMilestone;
          }
        }
      }
    } catch (error) {
      logGardenXP(
        `Error calculating Garden Crop Milestones in reconciliation: ${error.message}`
      );
      calculatedMilestones.gardenCropMilestones = {};
    }

    // Now compare calculated milestones to stored milestones and find differences

    // Skills: Compare and find differences
    for (const [skillName, calculatedLevel] of Object.entries(
      calculatedMilestones.skills
    )) {
      const storedLevel = milestones.skills[skillName] || 0;

      if (calculatedLevel > storedLevel) {
        // Progress detected - award XP for levels gained
        for (let level = storedLevel + 1; level <= calculatedLevel; level++) {
          const xpForLevel = calculateSkillXP(level);
          newAchievements.push({
            type: "skill",
            skill: skillName,
            level: level,
            xp: xpForLevel,
          });
        }
      } else if (calculatedLevel < storedLevel) {
        // Regression detected - log but don't award XP
        console.log(
          `[DisblockXP] ${skillName} skill level decreased from ${storedLevel} to ${calculatedLevel} - resetting milestone`
        );
      }
    }

    // Slayers: Compare and find differences
    for (const [slayerType, calculatedLevel] of Object.entries(
      calculatedMilestones.slayers
    )) {
      const storedLevel = milestones.slayerXp[slayerType] || 0;

      if (calculatedLevel > storedLevel) {
        // Progress detected - award XP for levels gained
        for (let level = storedLevel + 1; level <= calculatedLevel; level++) {
          const xpForLevel = calculateSlayerXP(slayerType, level);
          if (xpForLevel > 0) {
            newAchievements.push({
              type: "slayer",
              slayerType: slayerType,
              level: level,
              xp: xpForLevel,
            });
          }
        }
      } else if (calculatedLevel < storedLevel) {
        // Regression detected - log but don't award XP
        console.log(
          `[DisblockXP] ${slayerType} slayer level decreased from ${storedLevel} to ${calculatedLevel} - resetting milestone`
        );
      }
    }

    // Collections: Compare and find differences
    for (const [itemKey, calculatedTiers] of Object.entries(
      calculatedMilestones.collections
    )) {
      const storedTiers = milestones.collections[itemKey] || 0;

      if (calculatedTiers > storedTiers) {
        // Progress detected - award XP for tiers gained
        for (
          let tierIndex = storedTiers;
          tierIndex < calculatedTiers;
          tierIndex++
        ) {
          const xpForTier = calculateCollectionXP();
          // Find tier data for notification
          let tierData = null;
          for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
            if (
              categoryCollections[itemKey] &&
              categoryCollections[itemKey].tiers
            ) {
              tierData = categoryCollections[itemKey].tiers[tierIndex];
              break;
            }
          }

          newAchievements.push({
            type: "collection",
            item: itemKey,
            tier: tierIndex + 1,
            tierData: tierData,
            xp: xpForTier,
          });
        }
      } else if (calculatedTiers < storedTiers) {
        // Regression detected - log but don't award XP
        console.log(
          `[DisblockXP] ${itemKey} collection tiers decreased from ${storedTiers} to ${calculatedTiers} - resetting milestone`
        );
      }
    }

    // Minions: Compare and find differences
    const storedMinions = milestones.craftedMinions || [];
    const newMinions = calculatedMilestones.minions.filter(
      (minionKey) => !storedMinions.includes(minionKey)
    );
    const lostMinions = storedMinions.filter(
      (minionKey) => !calculatedMilestones.minions.includes(minionKey)
    );

    if (newMinions.length > 0) {
      // Progress detected - award XP for new minions
      for (const minionKey of newMinions) {
        const tierMatch = minionKey.match(/_T(\d+)$/);
        const tier = tierMatch ? parseInt(tierMatch[1]) : 1;
        const xpForMinion = calculateMinionXP(tier);

        newAchievements.push({
          type: "minion",
          minion: minionKey,
          tier: tier,
          xp: xpForMinion,
        });
      }
    }

    if (lostMinions.length > 0) {
      // Regression detected - log but don't award XP
      console.log(
        `[DisblockXP] Lost ${lostMinions.length} minion(s): ${lostMinions.join(", ")} - removing from milestones`
      );
    }

    // Magical Power: Compare and find differences (tracks highest ever achieved)
    const storedMP = milestones.magicalPower || 0;
    if (calculatedMilestones.magicalPower > storedMP) {
      // Progress detected - award XP for MP gained
      const mpGained = calculatedMilestones.magicalPower - storedMP;
      const xpForMP = calculateMagicalPowerXP(mpGained);

      newAchievements.push({
        type: "magicalPower",
        previousMP: storedMP,
        currentMP: calculatedMilestones.magicalPower,
        mpGained: mpGained,
        xp: xpForMP,
      });
    }
    // Note: MP milestone is NOT reset on decreases - tracks highest ever achieved

    // Pet Score: Compare and find differences (tracks highest ever achieved)
    const storedPetScore = milestones.petScore || 0;
    if (calculatedMilestones.petScore > storedPetScore) {
      // Progress detected - award XP for score gained
      const petScoreGained = calculatedMilestones.petScore - storedPetScore;
      const xpForPetScore = calculatePetScoreXP(petScoreGained);

      newAchievements.push({
        type: "petScore",
        previousScore: storedPetScore,
        currentScore: calculatedMilestones.petScore,
        scoreGained: petScoreGained,
        xp: xpForPetScore,
      });
    }
    // Note: Pet Score milestone is NOT reset on decreases - tracks highest ever achieved

    // Garden Level: Compare and find differences (tracks highest ever achieved)
    const storedGardenLevel = milestones.gardenLevel || 0;
    if (calculatedMilestones.gardenLevel > storedGardenLevel) {
      // Progress detected - award XP for levels gained
      logGardenXP(
        `Garden Level progress detected: ${storedGardenLevel} -> ${calculatedMilestones.gardenLevel}`
      );

      for (
        let level = storedGardenLevel + 1;
        level <= calculatedMilestones.gardenLevel;
        level++
      ) {
        // Only award XP for levels beyond the starting level (level 1)
        if (level > GARDEN_STARTING_LEVEL) {
          const xpForLevel = GARDEN_LEVEL_XP_REWARD;
          logGardenXP(`Awarding ${xpForLevel} XP for Garden Level ${level}`);

          newAchievements.push({
            type: "gardenLevel",
            level: level,
            previousLevel: level - 1,
            currentLevel: level,
            xp: xpForLevel,
          });
        } else {
          logGardenXP(
            `Skipping XP award for Garden Level ${level} (at or below starting level ${GARDEN_STARTING_LEVEL})`
          );
        }
      }
    } else if (calculatedMilestones.gardenLevel < storedGardenLevel) {
      // Garden Level decreased - log but don't reset milestone (tracks highest ever achieved)
      logGardenXP(
        `Garden Level decreased from ${storedGardenLevel} to ${calculatedMilestones.gardenLevel} - keeping highest milestone`
      );
    }
    // Note: Garden Level milestone is NOT reset on decreases - tracks highest ever achieved

    // Garden Crop Milestones: Compare and find differences (tracks highest ever achieved)
    for (const [cropKey, calculatedMilestone] of Object.entries(
      calculatedMilestones.gardenCropMilestones
    )) {
      const storedMilestone = milestones.gardenCropMilestones[cropKey] || 0;

      if (calculatedMilestone > storedMilestone) {
        // Progress detected - award 1 XP for each milestone gained
        logGardenXP(
          `Garden Crop Milestone progress detected for ${cropKey}: ${storedMilestone} -> ${calculatedMilestone}`
        );

        for (
          let milestone = storedMilestone + 1;
          milestone <= calculatedMilestone;
          milestone++
        ) {
          logGardenXP(`Awarding 1 XP for ${cropKey} Milestone ${milestone}`);

          newAchievements.push({
            type: "gardenCropMilestone",
            cropKey: cropKey,
            milestone: milestone,
            previousMilestone: milestone - 1,
            currentMilestone: milestone,
            xp: 1, // 1 XP per crop milestone
          });
        }
      } else if (calculatedMilestone < storedMilestone) {
        // Crop milestone decreased - log but don't reset milestone (tracks highest ever achieved)
        logGardenXP(
          `Garden Crop Milestone for ${cropKey} decreased from ${storedMilestone} to ${calculatedMilestone} - keeping highest milestone`
        );
      }
    }
    // Note: Garden Crop Milestones are NOT reset on decreases - tracks highest ever achieved

    // Always update milestones to match calculated reality
    milestones.skills = calculatedMilestones.skills;
    milestones.slayerXp = calculatedMilestones.slayers;
    milestones.collections = calculatedMilestones.collections;
    milestones.craftedMinions = calculatedMilestones.minions;

    // For MP, Pet Score, and Garden Level, only update if they increased (track highest ever achieved)
    if (calculatedMilestones.magicalPower > (milestones.magicalPower || 0)) {
      milestones.magicalPower = calculatedMilestones.magicalPower;
    }
    if (calculatedMilestones.petScore > (milestones.petScore || 0)) {
      milestones.petScore = calculatedMilestones.petScore;
    }
    if (calculatedMilestones.gardenLevel > (milestones.gardenLevel || 0)) {
      milestones.gardenLevel = calculatedMilestones.gardenLevel;
    }

    // For Garden Crop Milestones, only update if they increased (track highest ever achieved)
    for (const [cropKey, calculatedMilestone] of Object.entries(
      calculatedMilestones.gardenCropMilestones
    )) {
      if (
        calculatedMilestone > (milestones.gardenCropMilestones[cropKey] || 0)
      ) {
        milestones.gardenCropMilestones[cropKey] = calculatedMilestone;
      }
    }

    // Calculate total XP from achievements
    const achievementXP = newAchievements.reduce(
      (sum, achievement) => sum + achievement.xp,
      0
    );
    totalXPGained += achievementXP;

    // Only proceed if there are new achievements or XP difference
    if (newAchievements.length > 0 || totalXPGained > 0) {
      // Use accurate total XP directly instead of adding to current XP to avoid double-counting
      const finalXP = accurateTotalXP;
      const finalLevel = calculateDisblockLevel(finalXP).level;

      // UPDATE DATABASE FIRST - This ensures XP is never lost even if notifications fail
      await savePlayerData(
        userId,
        {
          disblock_xp: finalXP,
          disblockMilestones: milestones,
        },
        ["disblock_xp", "disblockMilestones"]
      );

      // Immediately update Discord nickname with final level (before notifications)
      try {
        await updatePlayerNickname(userId, interaction, finalXP);
      } catch {
        // Error updating nickname
      }

      // Send notifications if interaction is provided
      // About to check interaction for notifications
      if (interaction && interaction.channel) {
        // Interaction and channel valid, sending notifications
        try {
          // Items data available if needed for notifications

          // Group achievements by type and specific item/skill
          const groupedAchievements = {};

          for (const achievement of newAchievements) {
            let groupKey;
            if (achievement.type === "skill") {
              groupKey = `skill_${achievement.skill}`;
            } else if (achievement.type === "collection") {
              groupKey = `collection_${achievement.item}`;
            } else if (achievement.type === "slayer") {
              groupKey = `slayer_${achievement.slayerType}`;
            } else if (achievement.type === "minion") {
              const minionType = achievement.minion.replace(/_T\d+$/, "");
              groupKey = `minion_${minionType}`;
            } else if (achievement.type === "magicalPower") {
              groupKey = "magicalPower";
            } else if (achievement.type === "petScore") {
              groupKey = "petScore";
            } else if (achievement.type === "gardenLevel") {
              groupKey = "gardenLevel";
            } else if (achievement.type === "gardenCropMilestone") {
              groupKey = `gardenCropMilestone_${achievement.cropKey}`;
            }

            if (!groupedAchievements[groupKey]) {
              groupedAchievements[groupKey] = [];
            }
            groupedAchievements[groupKey].push(achievement);
          }

          // Track level progression for accurate level-up notifications
          let runningXP = currentXP;
          const levelUpsToNotify = [];

          // First pass: collect all level-ups that will occur
          for (const [groupKey, achievements] of Object.entries(
            groupedAchievements
          )) {
            const groupXP = achievements.reduce((sum, ach) => sum + ach.xp, 0);
            const previousLevel = calculateDisblockLevel(runningXP).level;
            runningXP += groupXP;
            const newLevelAfterXP = calculateDisblockLevel(runningXP).level;

            // Record level-ups for this group
            if (newLevelAfterXP > previousLevel) {
              for (
                let level = previousLevel + 1;
                level <= newLevelAfterXP;
                level++
              ) {
                levelUpsToNotify.push({
                  level,
                  xp: runningXP,
                  afterGroup: groupKey,
                  fromLevel: previousLevel,
                  toLevel: newLevelAfterXP,
                });
              }
            }
          }

          // Reset running XP for notification phase
          runningXP = currentXP;

          // Second pass: send notifications in correct order
          for (const [groupKey, achievements] of Object.entries(
            groupedAchievements
          )) {
            // Calculate total XP for this group
            const groupXP = achievements.reduce((sum, ach) => sum + ach.xp, 0);
            const previousLevel = calculateDisblockLevel(runningXP).level;
            runningXP += groupXP;
            const newLevelAfterXP = calculateDisblockLevel(runningXP).level;

            // Send XP gain notification first
            const embed = createGroupedXPEmbed(achievements, groupXP);
            await interaction.channel.send({ embeds: [embed] });

            // Send level-up notifications immediately after XP gain for this group
            if (newLevelAfterXP > previousLevel) {
              for (
                let level = previousLevel + 1;
                level <= newLevelAfterXP;
                level++
              ) {
                // Small delay before level-up notification
                await new Promise((resolve) => {
                  setTimeout(resolve, 500);
                });

                const levelUpEmbed = createLevelUpEmbed(
                  level,
                  runningXP,
                  previousLevel
                );
                await interaction.channel.send({ embeds: [levelUpEmbed] });
              }
            }

            // Add delay between groups if there are more to process
            if (
              groupKey !==
              Object.keys(groupedAchievements)[
                Object.keys(groupedAchievements).length - 1
              ]
            ) {
              await new Promise((resolve) => {
                setTimeout(resolve, 750);
              });
            }
          }
        } catch {
          // Error sending notifications
          // Database was already updated above, so XP is safe even if notifications fail

          // CRITICAL FALLBACK: Ensure level-up notifications are ALWAYS sent
          try {
            if (finalLevel > oldLevel) {
              // FALLBACK: Sending level-up notification
              const fallbackLevelUpEmbed = createLevelUpEmbed(
                finalLevel,
                finalXP,
                oldLevel
              );
              await interaction.channel.send({
                embeds: [fallbackLevelUpEmbed],
              });

              // Also try to update nickname in fallback
              try {
                await updatePlayerNickname(userId, interaction, finalXP);
              } catch {
                // FALLBACK: Error updating nickname
              }
            }
          } catch {
            // CRITICAL: Even fallback notification failed
          }
        }
      }

      // Return the same data regardless of interaction presence
      return {
        totalXP: finalXP,
        xpGained: totalXPGained,
        leveledUp: finalLevel > oldLevel,
        newLevel: finalLevel,
      };
    } else {
      // No new achievements, just return current state
      return {
        totalXP: currentXP,
        xpGained: 0,
        leveledUp: false,
        newLevel: oldLevel,
      };
    }
  } catch {
    return {
      totalXP: 0,
      xpGained: 0,
      leveledUp: false,
      newLevel: 0,
    };
  }
}

/**
 * Update Discord nickname to reflect current Disblock level
 * @param {string} userId - Discord user ID
 * @param {object} interaction - Discord interaction
 * @param {number} currentXP - Current Disblock XP
 * @returns {Promise<boolean>} - Whether nickname was updated successfully
 */
async function updatePlayerNickname(userId, interaction, currentXP) {
  try {
    console.log(
      `[updatePlayerNickname] Called for user ${userId} with currentXP: ${currentXP}`
    );
    console.log(
      `[updatePlayerNickname] Has interaction: ${!!interaction}, Has client: ${!!interaction?.client}`
    );

    // detect if we're running on a worker bot
    const isWorkerBot = process.env.IS_WORKER_BOT === "true";

    if (isWorkerBot) {
      console.log(
        `[updatePlayerNickname] Running on worker bot, delegating to main bot API`
      );
      try {
        const { axiosClient } = require("./workerDatabaseProxy");
        const response = await axiosClient.post(
          "/api/update-nickname",
          { userId, currentXP },
          { timeout: 10000 }
        );
        if (response.data.success) {
          console.log(
            `[updatePlayerNickname] API call successful:`,
            response.data
          );
          return response.data.updated || false;
        } else {
          console.log(
            `[updatePlayerNickname] API call failed:`,
            response.data.error
          );
          return false;
        }
      } catch (apiError) {
        console.error(
          `[updatePlayerNickname] Error calling main bot API:`,
          apiError.message
        );
        return false;
      }
    }

    // running on main bot - use existing logic
    console.log(
      `[updatePlayerNickname] Running on main bot, using direct Discord client`
    );

    // check if we have either interaction with member/guild OR interaction with client
    if (!interaction) {
      console.log(`[updatePlayerNickname] No interaction provided`);
      return false;
    }

    const { getPlayerData } = require("./playerDataManager");
    const characterData = await getPlayerData(userId);

    if (!characterData?.name) {
      console.log(
        `[updatePlayerNickname] No character data or name found for user ${userId}`
      );
      return false;
    }

    const integerLevel = calculateDisblockLevel(currentXP || 0).level;
    const expectedNickname = `[${integerLevel}] ${characterData.name}`;
    console.log(
      `[updatePlayerNickname] Expected nickname: "${expectedNickname}"`
    );

    let guild, member;

    // try to get guild and member from interaction first
    if (interaction.guild && interaction.member) {
      guild = interaction.guild;
      member = interaction.member;
      console.log(
        `[updatePlayerNickname] Using guild and member from interaction`
      );
    }
    // fallback to using client and config
    else if (interaction.client) {
      const config = require("../config.json");
      guild = interaction.client.guilds.cache.get(config.guildId);
      if (!guild) {
        console.log(
          `[updatePlayerNickname] Could not find guild with ID: ${config.guildId}`
        );
        return false;
      }
      member = await guild.members.fetch(userId);
      console.log(
        `[updatePlayerNickname] Fetched guild and member using client and config`
      );
    } else {
      console.log(
        `[updatePlayerNickname] No way to access guild - missing both guild/member and client`
      );
      return false;
    }

    const currentNickname = member.displayName;
    console.log(
      `[updatePlayerNickname] Current nickname: "${currentNickname}"`
    );

    // only update if the nickname is different from expected
    if (currentNickname !== expectedNickname) {
      console.log(
        `[updatePlayerNickname] Nickname needs updating from "${currentNickname}" to "${expectedNickname}"`
      );

      if (guild.members.me.permissions.has("ManageNicknames")) {
        console.log(
          `[updatePlayerNickname] Bot has ManageNicknames permission`
        );

        if (member.id !== guild.ownerId) {
          console.log(
            `[updatePlayerNickname] Target is not guild owner, proceeding with nickname update`
          );
          await member.setNickname(expectedNickname);
          console.log(
            `[updatePlayerNickname] Successfully updated nickname to "${expectedNickname}"`
          );
          return true;
        } else {
          console.log(
            `[updatePlayerNickname] Cannot update nickname - target is guild owner`
          );
        }
      } else {
        console.log(
          `[updatePlayerNickname] Bot lacks ManageNicknames permission`
        );
      }
      return false;
    } else {
      console.log(
        `[updatePlayerNickname] Nickname already correct, no update needed`
      );
    }
    return true;
  } catch (error) {
    console.error(
      `[updatePlayerNickname] Error updating nickname for ${userId}:`,
      error
    );
    return false;
  }
}

/**
 * Update player's Disblock XP based on current progress (backwards compatibility)
 * @param {string} userId - Discord user ID
 * @param {object} interaction - Discord interaction (optional, for sending notifications)
 * @returns {Promise<object>} - { totalXP: number, xpGained: number, leveledUp: boolean, newLevel: number }
 */
async function updatePlayerDisblockXP(userId, interaction = null) {
  try {
    const { getPlayerData, savePlayerData } = require("./playerDataManager");
    const allItems = configManager.getAllItems();

    // Get specific fields needed for XP calculation
    const playerData = await getPlayerData(userId);

    // Calculate XP from existing progress (now async and includes Magical Power)
    const calculatedXP = await calculateExistingPlayerXP(
      playerData,
      allItems,
      userId
    );

    // Initialize disblockXP if it doesn't exist
    const currentXP = playerData.disblock_xp || 0;

    if (calculatedXP > currentXP) {
      // Updating XP

      const oldLevel = calculateDisblockLevel(currentXP).level;
      const newLevel = calculateDisblockLevel(calculatedXP).level;
      const xpGained = calculatedXP - currentXP;
      const leveledUp = newLevel > oldLevel;

      // Update XP in database using targeted field update
      await savePlayerData(userId, { disblock_xp: calculatedXP }, [
        "disblock_xp",
      ]);

      // Send notifications if interaction is provided
      // About to check interaction for notifications
      if (interaction && interaction.channel) {
        // Interaction and channel valid, sending notifications
        try {
          // Send XP gain notification as independent message
          const xpEmbed = createXPGainEmbed(
            xpGained,
            "Progress Calculation",
            "Based on your current achievements"
          );
          await interaction.channel.send({ embeds: [xpEmbed] });

          // Send level up notification if applicable as independent message
          if (leveledUp) {
            const levelUpEmbed = createLevelUpEmbed(
              newLevel,
              calculatedXP,
              oldLevel
            );
            await interaction.channel.send({ embeds: [levelUpEmbed] });
          }

          // Update Discord nickname with current level and in-game username (on ANY XP gain)
          try {
            await updatePlayerNickname(userId, interaction, calculatedXP);
          } catch {
            // Error updating nickname
          }
        } catch {
          // Error sending notification

          // CRITICAL FALLBACK: Ensure level-up notifications are ALWAYS sent
          try {
            if (leveledUp) {
              // FALLBACK: Sending level-up notification
              const fallbackLevelUpEmbed = createLevelUpEmbed(
                newLevel,
                calculatedXP,
                oldLevel
              );
              await interaction.channel.send({
                embeds: [fallbackLevelUpEmbed],
              });

              // Also try to update nickname in fallback
              try {
                await updatePlayerNickname(userId, interaction, calculatedXP);
              } catch {
                // FALLBACK: Error updating nickname
              }
            }
          } catch {
            // CRITICAL: Even fallback notification failed
          }
        }
      }

      return {
        totalXP: calculatedXP,
        xpGained,
        leveledUp,
        newLevel,
      };
    } else {
      // No XP update needed
      return {
        totalXP: currentXP,
        xpGained: 0,
        leveledUp: false,
        newLevel: calculateDisblockLevel(currentXP).level,
      };
    }
  } catch {
    return {
      totalXP: 0,
      xpGained: 0,
      leveledUp: false,
      newLevel: 0,
    };
  }
}

module.exports = {
  calculateDisblockLevel,
  formatDisblockLevel,
  calculateMinionXP,
  calculateSkillXP,
  calculateSlayerXP,
  calculateCollectionXP,
  calculateMagicalPowerXP,
  calculatePetScore,
  calculatePetScoreXP,
  calculateMagicFindFromPetScore,
  calculateXPBreakdown,
  calculateExistingPlayerXP,
  getPlayerDisblockXPData,
  createXPGainEmbed,
  createGroupedXPEmbed,
  createLevelUpEmbed,
  createDetailedXPNotifications,
  awardDisblockXP,
  updatePlayerDisblockXP,
  updatePlayerNickname,
  checkAndNotifyDisblockXP,
  XP_PER_LEVEL,
  XP_REWARDS,
};
