// No imports needed - using db parameter directly

/**
 * Migration 050: Remove unused columns from players table
 *
 * Removing unused legacy columns:
 * - inventory_items_json (legacy, replaced by player_inventory_items table)
 * - equipment_json (legacy, replaced by player_equipment table)
 * - last_actions_json (legacy, never actually used - current action tracking uses active_actions table)
 */
module.exports = {
  async up(db) {
    console.log("[Migration 050] Starting removal of unused player columns...");

    // Check if columns exist before attempting to drop them
    const columns = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(players)", [], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const columnNames = columns.map((col) => col.name);
    console.log(
      `[Migration 050] Current players table columns: ${columnNames.join(", ")}`,
    );

    const removedColumns = [];

    // Remove last_actions_json if it exists
    if (columnNames.includes("last_actions_json")) {
      try {
        // Log current usage
        const lastActionsUsage = await new Promise((resolve, reject) => {
          db.get(
            "SELECT COUNT(*) as count FROM players WHERE last_actions_json IS NOT NULL AND last_actions_json != '{}'",
            [],
            (err, row) => {
              if (err) reject(err);
              else resolve(row.count);
            },
          );
        });
        console.log(
          `[Migration 050] Players with last_actions_json data: ${lastActionsUsage}`,
        );

        await new Promise((resolve, reject) => {
          db.run(
            "ALTER TABLE players DROP COLUMN last_actions_json",
            [],
            function (err) {
              if (err) reject(err);
              else resolve();
            },
          );
        });
        console.log("[Migration 050] ✅ Removed last_actions_json column");
        removedColumns.push("last_actions_json");
      } catch (error) {
        console.error(
          `[Migration 050] ❌ Failed to remove last_actions_json: ${error.message}`,
        );
        throw error;
      }
    } else {
      console.log(
        "[Migration 050] ℹ️ last_actions_json column not found (already removed)",
      );
    }

    // Remove inventory_items_json if it exists
    if (columnNames.includes("inventory_items_json")) {
      try {
        // Log current usage
        const inventoryItemsUsage = await new Promise((resolve, reject) => {
          db.get(
            "SELECT COUNT(*) as count FROM players WHERE inventory_items_json IS NOT NULL AND inventory_items_json != '{}'",
            [],
            (err, row) => {
              if (err) reject(err);
              else resolve(row.count);
            },
          );
        });
        console.log(
          `[Migration 050] Players with inventory_items_json data: ${inventoryItemsUsage}`,
        );

        await new Promise((resolve, reject) => {
          db.run(
            "ALTER TABLE players DROP COLUMN inventory_items_json",
            [],
            function (err) {
              if (err) reject(err);
              else resolve();
            },
          );
        });
        console.log("[Migration 050] ✅ Removed inventory_items_json column");
        removedColumns.push("inventory_items_json");
      } catch (error) {
        console.error(
          `[Migration 050] ❌ Failed to remove inventory_items_json: ${error.message}`,
        );
        throw error;
      }
    } else {
      console.log(
        "[Migration 050] ℹ️ inventory_items_json column not found (already removed)",
      );
    }

    // Remove equipment_json if it exists
    if (columnNames.includes("equipment_json")) {
      try {
        // Log current usage
        const equipmentUsage = await new Promise((resolve, reject) => {
          db.get(
            "SELECT COUNT(*) as count FROM players WHERE equipment_json IS NOT NULL AND equipment_json != '[]'",
            [],
            (err, row) => {
              if (err) reject(err);
              else resolve(row.count);
            },
          );
        });
        console.log(
          `[Migration 050] Players with equipment_json data: ${equipmentUsage}`,
        );

        await new Promise((resolve, reject) => {
          db.run(
            "ALTER TABLE players DROP COLUMN equipment_json",
            [],
            function (err) {
              if (err) reject(err);
              else resolve();
            },
          );
        });
        console.log("[Migration 050] ✅ Removed equipment_json column");
        removedColumns.push("equipment_json");
      } catch (error) {
        console.error(
          `[Migration 050] ❌ Failed to remove equipment_json: ${error.message}`,
        );
        throw error;
      }
    } else {
      console.log(
        "[Migration 050] ℹ️ equipment_json column not found (already removed)",
      );
    }

    if (removedColumns.length > 0) {
      console.log(
        `[Migration 050] ✅ Successfully removed columns: ${removedColumns.join(", ")}`,
      );
    } else {
      console.log("[Migration 050] ℹ️ No columns needed to be removed");
    }

    console.log("[Migration 050] Migration completed successfully");
  },

  async down(db) {
    console.log("[Migration 050] Rolling back unused player column removal...");

    // Note: SQLite doesn't support adding columns back easily with the same data
    // This rollback just recreates the column structure

    try {
      // Re-add last_actions_json column
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN last_actions_json TEXT DEFAULT '{}'",
          [],
          function (err) {
            if (err && !err.message.includes("duplicate column name")) {
              reject(err);
            } else {
              resolve();
            }
          },
        );
      });
      console.log("[Migration 050] ✅ Re-added last_actions_json column");

      // Re-add inventory_items_json column
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN inventory_items_json TEXT DEFAULT '{}'",
          [],
          function (err) {
            if (err && !err.message.includes("duplicate column name")) {
              reject(err);
            } else {
              resolve();
            }
          },
        );
      });
      console.log("[Migration 050] ✅ Re-added inventory_items_json column");

      // Re-add equipment_json column
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN equipment_json TEXT DEFAULT '[]'",
          [],
          function (err) {
            if (err && !err.message.includes("duplicate column name")) {
              reject(err);
            } else {
              resolve();
            }
          },
        );
      });
      console.log("[Migration 050] ✅ Re-added equipment_json column");

      console.log(
        "[Migration 050] ⚠️  Rollback completed - columns recreated but data was not restored",
      );
    } catch (error) {
      console.error(`[Migration 050] ❌ Rollback failed: ${error.message}`);
      throw error;
    }
  },
};
