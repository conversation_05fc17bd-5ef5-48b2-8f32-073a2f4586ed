function up(db) {
  console.log("Adding disblockMilestones column to players table...");

  // Add the disblockMilestones column as JSON
  db.run(
    `
        ALTER TABLE players 
        ADD COLUMN disblockMilestones TEXT DEFAULT NULL;
    `,
    (err) => {
      if (err && !err.message.includes("duplicate column name")) {
        console.error("Error adding disblockMilestones column:", err.message);
      } else {
        console.log("disblockMilestones column added successfully");
      }
    },
  );
}

function down() {
  console.log("Removing disblockMilestones column from players table...");

  // SQLite doesn't support DROP COLUMN directly, so we'd need to recreate the table
  // For now, just log that this would require manual intervention
  console.log(
    "Note: SQLite does not support DROP COLUMN. Manual intervention required to rollback.",
  );
}

module.exports = { up, down };
