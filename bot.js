// --- API endpoints for worker communication will be registered inside startBot() ---
// @ts-nocheck
// IMPORTANT: Make sure to import `instrument.js` at the top of your file.
require("./instrument.js");

// All other imports below
require("dotenv").config();
// Only mark as main bot if we're not a worker process
if (process.env.IS_WORKER_BOT !== "true") {
  process.env.IS_MAIN_BOT = "true"; // Mark this process as the main bot
}
const Sentry = require("@sentry/node");

const fs = require("fs").promises;
const fsSync = require("fs");
const path = require("node:path");
const {
  Client,
  GatewayIntentBits,
  Collection,
  ActionRowBuilder,
  ButtonBuilder,
  MessageFlags,
  EmbedBuilder,
  ComponentType,
} = require("discord.js");
const { EMBED_COLORS, WARP_FOOTER } = require("./gameConfig");
// @ts-ignore
const config = require("./config.json");
const db = require("./utils/database");
const { dbGet, dbRun, dbRunQueued } = require("./utils/dbUtils");
const {
  getPlayerData,
  savePlayerData,
  getAllPlayerIds,
} = require("./utils/playerDataManager");
const { applyHealthRegenToAllPlayers } = require("./utils/healthRegen.js");
const {
  applyCoinGenerationToAllPlayers,
  initializeAllPlayersCoinGeneration,
} = require("./utils/coinGeneration.js");
const {
  DISBLOCK_SECONDS_PER_REAL_SECOND,
  isBakerSpawnWindow,
} = require("./utils/timeUtils.js"); // Import the renamed constant
const { REST } = require("@discordjs/rest");
const { Routes } = require("discord-api-types/v10");
const {
  resumeAllPendingActions,
  handleResumedStop,
} = require("./utils/actionResumption.js");
// Removed sharedState imports - using simple abort system now
const configManager = require("./utils/configManager"); // Import the manager
const collectionsManager = require("./utils/collectionsManager.js");
const commandToggleManager = require("./utils/commandToggleManager");
const { getPlacedMinionsFullStatus } = require("./utils/minionUtils.js");
const { checkAndProcessCompletedPayments } = require("./utils/stripeUtils.js");
// Import robust timer system for time tracking only
const {
  safeSetInterval,
  safeClearInterval,
  robustTimer,
} = require("./utils/robustTimer");
const { startBackupScheduler } = require("./utils/databaseBackup");

// Initialize graceful error handling early
const { GracefulErrorHandler } = require("./utils/gracefulErrorHandler");
const { crashMonitor } = require("./utils/crashMonitor");
const gracefulErrorHandler = new GracefulErrorHandler({
  processName: "MainBot",
  maxCriticalErrors: 5,
  criticalErrorWindow: 300000, // 5 minutes
  gracefulShutdownTimeout: 30000, // 30 seconds
});

const VERBOSE_LOGGING = false;
const QUIET_BOOT = process.env.QUIET_BOOT === "true";
const logSync = (message, verbose = false) =>
  (VERBOSE_LOGGING || !verbose) && console.log(message);
// @ts-ignore
const MIGRATIONS_DIR = path.join(__dirname, "migrations");

/**
 * Detects if a customId belongs to a temporary collector (usually awaitMessageComponent)
 * These typically use timestamps for uniqueness and are handled by the command's collectors
 * @param {string} customId - The interaction customId to check
 * @returns {boolean} - True if likely handled by a temporary collector
 */
function isTemporaryCollectorInteraction(customId) {
  // Check if customId ends with a timestamp (13-digit number from Date.now())
  const timestampPattern = /_\d{13}$/;

  // Check for common temporary collector patterns
  const temporaryPatterns = [
    /^trade_accept_.*_\d{13}$/,
    /^trade_decline_.*_\d{13}$/,
    /_select_\d{13}$/,
    /_menu_\d{13}$/,
    /_modal_\d{13}$/,
    /_button_\d{13}$/,
    // Market interaction patterns
    /^market_view_orderbook$/,
    /^cancel_order_select$/,
    /^create_sell_order_select$/,
    /^quick_buy_select$/,
    /^quick_sell_select$/,
    /^market_sell_select$/,
  ];

  // If it ends with a timestamp, it's likely a temporary collector
  if (timestampPattern.test(customId)) {
    return true;
  }

  // Check against known temporary patterns
  return temporaryPatterns.some((pattern) => pattern.test(customId));
}

// Track user's last active channel
async function updateUserActiveChannel(userId, channelId) {
  const { dbRunQueued } = require("./utils/dbUtils");
  const now = Date.now();
  try {
    await dbRunQueued(
      "INSERT INTO player_last_active_channel (discord_id, channel_id, updated_at) VALUES (?, ?, ?) ON CONFLICT(discord_id) DO UPDATE SET channel_id = ?, updated_at = ?",
      [userId, channelId, now, channelId, now]
    );
  } catch (err) {
    console.error("[MinionSystem] Error updating active channel:", err);
    throw err;
  }
}

// Clean up expired potion effects from all players
async function cleanupExpiredPotionEffects() {
  logSync("[PotionSystem] Cleaning up expired potion effects...", true);

  try {
    const { dbAll } = require("./utils/dbUtils");
    const { cleanupExpiredEffects } = require("./utils/potionEffects");

    // Get all players with active effects
    const players = await dbAll(
      "SELECT discord_id FROM players WHERE active_effects_json IS NOT NULL",
      []
    );

    let cleanedCount = 0;
    for (const player of players) {
      try {
        const character = await getPlayerData(player.discord_id);
        if (character && character.activeEffects) {
          const wasCleanedUp = cleanupExpiredEffects(character);
          if (wasCleanedUp) {
            await savePlayerData(player.discord_id, character);
            cleanedCount++;
          }
        }
      } catch (playerError) {
        console.error(
          `[PotionSystem] Error cleaning up effects for player ${player.discord_id}:`,
          playerError
        );
      }
    }

    if (cleanedCount > 0) {
      logSync(
        `[PotionSystem] Cleaned up expired effects for ${cleanedCount} players`,
        true
      );
    }
  } catch (error) {
    console.error(
      "[PotionSystem] Error in cleanupExpiredPotionEffects:",
      error
    );
  }
}

// Check for Baker cake availability and notify players
async function checkAndNotifyBakerCake(client) {
  logSync("[BakerSystem] Checking for Baker cake notifications...", true);

  try {
    // Get current time state
    const timeState = getTimeState();

    // Only proceed if Baker is currently available
    if (!isBakerSpawnWindow(timeState)) {
      return;
    }

    const { dbAll } = require("./utils/dbUtils");
    const { getEndingYear } = require("./utils/timeUtils");

    // Get the current ending year for cake eligibility
    const endingYear = getEndingYear(timeState) + 1; // Add 1 to match talk.js logic

    // Get all players who haven't claimed their cake and haven't been notified recently
    const players = await dbAll(
      `
      SELECT p.discord_id, p.lastClaimedYear, plac.channel_id, plac.cake_notified
      FROM players p
      LEFT JOIN player_last_active_channel plac ON p.discord_id = plac.discord_id
      WHERE plac.channel_id IS NOT NULL
      AND (
        plac.cake_notified IS NULL 
        OR plac.cake_notified < ?
      )
      AND (p.lastClaimedYear IS NULL OR p.lastClaimedYear < ?)
    `,
      [Date.now() - 23 * 60 * 60 * 1000, endingYear]
    ); // 23 hour cooldown and haven't claimed this year

    for (const player of players) {
      try {
        // Player is already filtered to only include those who haven't claimed this year's cake
        const hasClaimedCake = false;

        if (!hasClaimedCake) {
          // Try to notify the user
          try {
            const channel = await client.channels.fetch(player.channel_id);
            if (channel && channel.isTextBased()) {
              const embed = new EmbedBuilder()
                .setTitle("🎂 Baker Cake Available!")
                .setDescription(
                  "The Baker is in the hub giving out New Year Cake! Don't miss out on your free cake."
                )
                .setColor(EMBED_COLORS.GOLD) // Gold color
                .setFooter({ text: "Use /talk baker to claim your cake" });

              await channel.send({
                content: `<@${player.discord_id}>`,
                embeds: [embed],
              });

              logSync(
                `[BakerSystem] Notified user ${player.discord_id} about available cake`,
                true
              );

              // Update cake_notified timestamp
              await dbRunQueued(
                "UPDATE player_last_active_channel SET cake_notified = ? WHERE discord_id = ?",
                [Date.now(), player.discord_id]
              );
            }
          } catch {
            // Silent error handling
          }
        }
      } catch {
        // Silent error handling
      }
    }
  } catch (error) {
    console.error("[BakerSystem] Error in checkAndNotifyBakerCake:", error);
  }
}

// Process visitor spawning for eligible players
// Process bits collection for all players with active booster cookies
async function processAllPlayersBitsCollection() {
  try {
    const { dbAll } = require("./utils/dbUtils");
    const { collectPendingBits } = require("./utils/bitsManager");

    // get all players with active booster cookies
    const currentTime = Date.now();
    const playersWithCookies = await dbAll(
      `
      SELECT discord_id, booster_cookie_expiry, bits_available, bits_multiplier, 
             base_bits_from_cookies, last_bits_collection, bits
      FROM players 
      WHERE booster_cookie_expiry > ? AND bits_available > 0
    `,
      [currentTime]
    );

    let totalPlayersProcessed = 0;
    let totalBitsCollected = 0;

    for (const playerRow of playersWithCookies) {
      try {
        // create a minimal character object with the needed fields
        const character = {
          booster_cookie_expiry: playerRow.booster_cookie_expiry,
          bits_available: playerRow.bits_available || 0,
          bits_multiplier: playerRow.bits_multiplier || 1.0,
          base_bits_from_cookies: playerRow.base_bits_from_cookies || 0,
          last_bits_collection: playerRow.last_bits_collection,
          bits: playerRow.bits || 0,
        };

        const bitsResult = await collectPendingBits(
          playerRow.discord_id,
          character
        );

        if (bitsResult.bitsCollected > 0) {
          totalPlayersProcessed++;
          totalBitsCollected += bitsResult.bitsCollected;
        }
      } catch (error) {
        console.error(
          `[BitsCollection] Error processing bits for player ${playerRow.discord_id}:`,
          error
        );
      }
    }

    if (totalPlayersProcessed > 0) {
      logSync(
        `[BitsCollection] Processed ${totalPlayersProcessed} players, collected ${totalBitsCollected} total bits`,
        true
      );
    }
  } catch (error) {
    console.error(
      "[BitsCollection] Error in background bits collection:",
      error
    );
  }
}

async function processVisitorSpawning() {
  try {
    logSync("[Garden Visitors] Starting visitor spawning check...", true);
    const { dbAll } = require("./utils/dbUtils");
    const {
      spawnVisitor,
      checkVisitorSpawn,
    } = require("./utils/gardenVisitors");

    // Get all players who might be eligible for visitor spawning
    const players = await dbAll(
      `
      SELECT discord_id, garden_visitor_timer, garden_xp 
      FROM players 
      WHERE garden_visitor_timer <= ? AND garden_xp > 0
    `,
      [Date.now()]
    );

    logSync(
      `[Garden Visitors] Found ${players.length} players eligible for spawning`,
      true
    );

    for (const player of players) {
      try {
        const spawnCheck = await checkVisitorSpawn(player.discord_id);
        if (spawnCheck.canSpawn) {
          await spawnVisitor(player.discord_id);
          logSync(
            `[Garden Visitors] Spawned visitor for player ${player.discord_id}`,
            true
          );
        } else {
          logSync(
            `[Garden Visitors] Cannot spawn for ${player.discord_id}: ${spawnCheck.reason}`,
            true
          );
        }
      } catch (error) {
        console.error(
          `[Garden Visitors] Error spawning visitor for player ${player.discord_id}:`,
          error
        );
      }
    }
    logSync("[Garden Visitors] Visitor spawning check complete", true);
  } catch (error) {
    console.error(
      "[Garden Visitors] Error in visitor spawning process:",
      error
    );
  }
}

// Check for completed pet upgrades and notify players
async function checkAndNotifyPetUpgrades(client) {
  logSync("[PetSystem] Checking for completed pet upgrades...", true);

  try {
    // Get all players with active pet upgrades
    const { dbAll, dbGet } = require("./utils/dbUtils");
    const { checkPetUpgradeStatus } = require("./utils/petUpgradeUtils");
    const { ITEM_RARITY, EMBED_COLORS } = require("./gameConfig");
    const configManager = require("./utils/configManager");

    // Get all players with pet_upgrade_json not null
    const playersWithUpgrades = await dbAll(
      "SELECT discord_id, pet_upgrade_json FROM players WHERE pet_upgrade_json IS NOT NULL",
      []
    );

    for (const player of playersWithUpgrades) {
      const userId = player.discord_id;

      // Check if the upgrade is complete
      const upgradeStatus = await checkPetUpgradeStatus(userId);

      if (upgradeStatus.upgrading && upgradeStatus.isComplete) {
        // Get the player's last active channel
        const channelRow = await dbGet(
          "SELECT channel_id, pet_upgrade_notified FROM player_last_active_channel WHERE discord_id = ?",
          [userId]
        );

        // If no channel row exists or pet_upgrade_notified is null/0, we need to notify
        const needsNotification =
          !channelRow || !channelRow.pet_upgrade_notified;

        if (channelRow && channelRow.channel_id && needsNotification) {
          try {
            // Get pet information
            const allItems = configManager.getAllItems();
            const petInfo = allItems[upgradeStatus.pet_upgrade_json.petKey];
            const originalRarityInfo =
              ITEM_RARITY[upgradeStatus.pet_upgrade_json.originalRarity];
            const targetRarityInfo =
              ITEM_RARITY[upgradeStatus.pet_upgrade_json.targetRarity];

            if (petInfo && originalRarityInfo && targetRarityInfo) {
              // Try to notify the user
              const channel = await client.channels.fetch(
                channelRow.channel_id
              );
              if (channel && channel.isTextBased()) {
                const embed = new EmbedBuilder()
                  .setTitle("Pet Upgrade Complete!")
                  .setDescription(
                    `Your ${petInfo.emoji || "🐾"} **${
                      originalRarityInfo.name
                    } ${petInfo.name}** has been upgraded to **${
                      targetRarityInfo.name
                    } ${petInfo.name}**!`
                  )
                  .setColor(targetRarityInfo.color || EMBED_COLORS.PINK)
                  .setFooter({
                    text: "Use /talk with Kat to pick up your pet",
                  });

                await channel.send({
                  content: `<@${userId}>`,
                  embeds: [embed],
                });
                logSync(
                  `[PetSystem] Notified user ${userId} in channel ${channelRow.channel_id} about completed pet upgrade`,
                  true
                );

                // Mark as notified
                await dbRunQueued(
                  "UPDATE player_last_active_channel SET pet_upgrade_notified = ? WHERE discord_id = ?",
                  [1, userId]
                );
              }
            }
          } catch (notifyError) {
            console.error(
              `[PetSystem] Failed to notify user ${userId} about completed pet upgrade:`,
              notifyError
            );
          }
        }
      } else if (upgradeStatus.upgrading && !upgradeStatus.isComplete) {
        // Reset notification flag if upgrade is still in progress but not complete
        await dbRunQueued(
          "UPDATE player_last_active_channel SET pet_upgrade_notified = NULL WHERE discord_id = ?",
          [userId]
        );
      }
    }
  } catch (error) {
    console.error(
      "[PetSystem] Error checking for completed pet upgrades:",
      error
    );
  }
}

// Check for full minions and notify players (also triggers generation)
async function checkAndNotifyMinionFullness(client) {
  logSync(
    "[MinionSystem] Processing all players with minions for generation and fullness check (10s cycle)...",
    true
  );

  // Get ALL players with placed minions, not just those who have used commands recently
  db.all(
    `
        SELECT p.discord_id, plac.channel_id, plac.last_notified,
               plac.any_full_notified
        FROM players p
        LEFT JOIN player_last_active_channel plac ON p.discord_id = plac.discord_id
        WHERE p.island_json IS NOT NULL
        AND json_extract(p.island_json, '$.placedMinions') IS NOT NULL
        AND json_array_length(json_extract(p.island_json, '$.placedMinions')) > 0
    `,
    [],
    async (err, rows) => {
      if (err) {
        console.error(
          "[MinionSystem] Error retrieving players with minions:",
          err
        );
        return;
      }

      for (const row of rows) {
        try {
          const userId = row.discord_id;
          const channelId = row.channel_id;
          const lastAllFullNotified = row.last_notified;
          const lastAnyFullNotified = row.any_full_notified;

          // Trigger generation for all minions and evaluate fullness status
          const { anyFull, allFull } = await getPlacedMinionsFullStatus(userId);

          // ---- Handle ANY-FULL notification ----
          if (anyFull) {
            if (lastAnyFullNotified === null && channelId) {
              try {
                const channel = await client.channels.fetch(channelId);
                if (channel && channel.isTextBased()) {
                  const embed = new EmbedBuilder()
                    .setTitle("Minions Notification")
                    .setDescription(
                      "One or more of your minions are full and ready to collect!"
                    )
                    .setColor(EMBED_COLORS.ORANGE)
                    .setFooter({ text: WARP_FOOTER });

                  await channel.send({
                    content: `<@${userId}>`,
                    embeds: [embed],
                  });

                  logSync(
                    `[MinionSystem] Notified (any full) user ${userId} in channel ${channelId}`,
                    true
                  );

                  // Update any_full_notified timestamp
                  const { dbRunQueued } = require("./utils/dbUtils");
                  await dbRunQueued(
                    "INSERT INTO player_last_active_channel (discord_id, channel_id, updated_at, any_full_notified) VALUES (?, ?, ?, ?) ON CONFLICT(discord_id) DO UPDATE SET any_full_notified = ?",
                    [userId, channelId, Date.now(), Date.now(), Date.now()]
                  );
                }
              } catch (notifyErr) {
                console.error(
                  `[MinionSystem] Failed to notify user ${userId} (any full):`,
                  notifyErr
                );
              }
            }
          } else {
            // Reset any_full_notified if no minion is full
            if (lastAnyFullNotified !== null && channelId) {
              const { dbRunQueued } = require("./utils/dbUtils");
              try {
                await dbRunQueued(
                  "UPDATE player_last_active_channel SET any_full_notified = NULL WHERE discord_id = ?",
                  [userId]
                );
                logSync(
                  `[MinionSystem] Reset any_full_notified for ${userId} as no minions are full`,
                  true
                );
              } catch (resetErr) {
                console.error(
                  `[MinionSystem] Failed to reset any_full_notified for ${userId}:`,
                  resetErr
                );
              }
            }
          }

          // ---- Handle ALL-FULL notification ----
          if (allFull) {
            // Only notify if this is the first time we're notifying AND we have a channel to notify in
            if (lastAllFullNotified === null && channelId) {
              try {
                const channel = await client.channels.fetch(channelId);
                if (channel && channel.isTextBased()) {
                  const embed = new EmbedBuilder()
                    .setTitle("Minions Notification")
                    .setDescription(
                      "All your minions are full and ready to collect!"
                    )
                    .setColor(EMBED_COLORS.ORANGE) // Orange color
                    .setFooter({ text: WARP_FOOTER });

                  await channel.send({
                    content: `<@${userId}>`,
                    embeds: [embed],
                  });
                  logSync(
                    `[MinionSystem] Notified (all full) user ${userId} in channel ${channelId}`,
                    true
                  );

                  const { dbRunQueued } = require("./utils/dbUtils");
                  await dbRunQueued(
                    "INSERT INTO player_last_active_channel (discord_id, channel_id, updated_at, last_notified) VALUES (?, ?, ?, ?) ON CONFLICT(discord_id) DO UPDATE SET last_notified = ?",
                    [userId, channelId, Date.now(), Date.now(), Date.now()]
                  );
                }
              } catch (notifyError) {
                console.error(
                  `[MinionSystem] Failed to notify user ${userId} (all full):`,
                  notifyError
                );
              }
            }
          } else {
            if (lastAllFullNotified !== null && channelId) {
              const { dbRunQueued } = require("./utils/dbUtils");
              try {
                await dbRunQueued(
                  "UPDATE player_last_active_channel SET last_notified = NULL WHERE discord_id = ?",
                  [userId]
                );
                logSync(
                  `[MinionSystem] Reset last_notified for ${userId} as minions are not all full`,
                  true
                );
              } catch (resetErr) {
                console.error(
                  `[MinionSystem] Failed to reset last_notified for ${userId}:`,
                  resetErr
                );
              }
            }
          }
        } catch (userError) {
          console.error(
            `[MinionSystem] Error processing user ${row.discord_id}:`,
            userError
          );
        }
      }
    }
  );
}

/**
 * Check and notify players when their garden visitors reach maximum capacity (5)
 * @param {Client} client - Discord client
 */
async function checkAndNotifyVisitorsFullness(client) {
  logSync(
    "[VisitorSystem] Processing all players with visitors for fullness check (10s cycle)...",
    true
  );

  // Get ALL players with active garden visitors
  db.all(
    `
        SELECT p.discord_id, p.personal_channel_id, plac.visitors_full_notified
        FROM players p
        LEFT JOIN player_last_active_channel plac ON p.discord_id = plac.discord_id
        WHERE p.discord_id IN (
          SELECT DISTINCT discord_id FROM active_garden_visitors
        )
    `,
    [],
    async (err, rows) => {
      if (err) {
        console.error(
          "[VisitorSystem] Error retrieving players with visitors:",
          err
        );
        return;
      }

      for (const row of rows) {
        try {
          const userId = row.discord_id;
          const personalChannelId = row.personal_channel_id;
          const lastVisitorsFullNotified = row.visitors_full_notified;

          // Import the function here to avoid circular dependency issues
          const {
            areVisitorsAtMaxCapacity,
          } = require("./utils/gardenVisitors");
          const { getPlayerData } = require("./utils/playerDataManager");

          // Check if visitors are at max capacity (5)
          const visitorsAtMax = await areVisitorsAtMaxCapacity(userId);

          // Get player settings to check if notifications are enabled
          const playerData = await getPlayerData(userId);
          const visitorsFullPingEnabled =
            playerData?.settings?.visitorsFullPing !== false; // default to true

          // ---- Handle VISITORS-FULL notification ----
          if (visitorsAtMax && visitorsFullPingEnabled) {
            if (lastVisitorsFullNotified === null && personalChannelId) {
              try {
                const channel = client.channels.cache.get(personalChannelId);
                if (channel && channel.isTextBased()) {
                  const embed = new EmbedBuilder()
                    .setColor(EMBED_COLORS.YELLOW)
                    .setDescription(
                      "Your garden has **5/5 visitors** and is at maximum capacity!"
                    );

                  await channel.send({
                    content: `<@${userId}>`,
                    embeds: [embed],
                  });

                  logSync(
                    `[VisitorSystem] Notified user ${userId} in personal channel ${personalChannelId} about visitors being full`,
                    true
                  );

                  // Update visitors_full_notified timestamp
                  const { dbRunQueued } = require("./utils/dbUtils");
                  await dbRunQueued(
                    "INSERT INTO player_last_active_channel (discord_id, channel_id, updated_at, visitors_full_notified) VALUES (?, ?, ?, ?) ON CONFLICT(discord_id) DO UPDATE SET visitors_full_notified = ?",
                    [
                      userId,
                      personalChannelId,
                      Date.now(),
                      Date.now(),
                      Date.now(),
                    ]
                  );
                }
              } catch (notifyErr) {
                // Handle Discord API errors gracefully
                if (notifyErr.code === 50001 || notifyErr.status === 403) {
                  // Missing Access or Forbidden - channel doesn't exist or bot can't access it
                  logSync(
                    `[VisitorSystem] Can't access personal channel ${personalChannelId} for user ${userId} (Missing Access). Channel may have been deleted.`,
                    true
                  );

                  // For personal channels, we don't clear the channel_id since it's stored in players table
                  // The channel may be temporarily inaccessible or deleted - let user recreate if needed
                } else {
                  // Other errors - log them but don't clear the channel
                  console.error(
                    `[VisitorSystem] Failed to notify user ${userId} about visitors being full:`,
                    notifyErr
                  );
                }
              }
            }
          } else {
            // Reset visitors_full_notified if visitors are not at max capacity or notifications are disabled
            if (lastVisitorsFullNotified !== null && personalChannelId) {
              const { dbRunQueued } = require("./utils/dbUtils");
              try {
                await dbRunQueued(
                  "UPDATE player_last_active_channel SET visitors_full_notified = NULL WHERE discord_id = ?",
                  [userId]
                );
                logSync(
                  `[VisitorSystem] Reset visitors_full_notified for ${userId} as visitors are not at max capacity`,
                  true
                );
              } catch (resetErr) {
                console.error(
                  `[VisitorSystem] Failed to reset visitors_full_notified for ${userId}:`,
                  resetErr
                );
              }
            }
          }
        } catch (userError) {
          console.error(
            `[VisitorSystem] Error processing user ${row.discord_id}:`,
            userError
          );
        }
      }
    }
  );
}

// +++ Process Stop Button Interaction +++
/**
 * Processes stop button interactions for both new and resumed actions
 * @param {ButtonInteraction} interaction - The button interaction
 * @returns {Promise<boolean>} Returns true if the interaction was handled, false otherwise
 */
async function processStopButtonInteraction(interaction) {
  try {
    const customId = interaction.customId;

    if (customId.startsWith("stop_resumed_action:")) {
      // Handle resumed action stop
      const actionId = parseInt(customId.split(":")[1], 10);
      if (isNaN(actionId)) {
        console.warn(
          `[Stop Button] Invalid action ID in customId: ${customId}`
        );
        return false;
      }

      await handleResumedStop(interaction, actionId);
      return true;
    } else if (customId.startsWith("stop_action:")) {
      // Handle action stop - simple and instant
      const parts = customId.split(":");
      const actionId = parseInt(parts[1], 10);
      const userId = parts[2];

      if (isNaN(actionId) || !userId) {
        console.warn(
          `[Stop Button] Invalid action ID or user ID in customId: ${customId}`
        );
        return false;
      }

      // Verify the user clicking the button is the same user who started the action
      if (interaction.user.id !== userId) {
        await interaction.reply({
          content: "You can only stop your own actions.",
          ephemeral: true,
        });
        return true;
      }

      // Get action details to route to the correct worker (actions always run on workers)
      const { getActionById } = require("./utils/actionPersistence");
      const actionRecord = await getActionById(actionId);
      const workerId = actionRecord?.worker_id;

      // Actions always run on workers, so always route stop requests to workers
      if (workerId) {
        console.log(
          `[Stop Button] Routing stop request for action ${actionId} to worker ${workerId}`
        );

        try {
          const axios = require("axios");
          const config = require("./config.json");

          // Find the worker's API URL
          const worker = Object.values(config.multiBot?.workerBots || {}).find(
            (w) => w.id === workerId
          );
          if (worker) {
            const workerUrl = `http://127.0.0.1:${worker.port}`;

            // Route the stop request to the worker
            await axios.post(
              `${workerUrl}/api/stop-action`,
              {
                actionId: actionId,
                userId: userId,
                interactionId: interaction.id,
                channelId: interaction.channelId,
              },
              {
                timeout: 5000,
              }
            );

            console.log(
              `[Stop Button] Successfully routed stop request for action ${actionId} to worker ${workerId}`
            );

            // Acknowledge the button press
            await interaction.deferUpdate();
            return true;
          } else {
            console.error(
              `[Stop Button] Worker ${workerId} not found in config - cannot stop action ${actionId}`
            );
            await interaction.reply({
              content: "❌ Unable to stop action - worker not found.",
              ephemeral: true,
            });
            return true;
          }
        } catch (routingError) {
          console.error(
            `[Stop Button] Failed to route stop request to worker ${workerId}:`,
            routingError.message
          );
          await interaction.reply({
            content: "❌ Error communicating with worker. Please try again.",
            ephemeral: true,
          });
          return true;
        }
      } else {
        console.error(
          `[Stop Button] No worker_id found for action ${actionId} - cannot stop action`
        );
        await interaction.reply({
          content: "❌ Unable to stop action - worker information not found.",
          ephemeral: true,
        });
        return true;
      }

      // Acknowledge the button press
      try {
        await interaction.deferUpdate();
        console.log(
          `[Stop Button] Successfully acknowledged button press for action ${actionId}`
        );
      } catch (updateError) {
        console.warn(
          `[Stop Button] Failed to defer update for stop button interaction (action ${actionId}):`,
          updateError.message
        );
      }

      return true;
    }

    return false; // Not a stop button we handle
  } catch (error) {
    console.error(
      "[Stop Button] Error processing stop button interaction:",
      error
    );
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "Error processing stop request. Please try again.",
          ephemeral: true,
        });
      }
    } catch (replyError) {
      console.warn("[Stop Button] Failed to send error reply:", replyError);
    }
    return false;
  }
}

// Function to apply migrations
async function runMigrations() {
  if (!QUIET_BOOT) logSync("Checking database schema version...");
  try {
    // Ensure migrations directory exists
    try {
      // @ts-ignore
      await fs.access(MIGRATIONS_DIR);
    } catch (err) {
      if (err.code === "ENOENT") {
        logSync(`Migrations directory not found, creating: ${MIGRATIONS_DIR}`);
        // @ts-ignore
        await fs.mkdir(MIGRATIONS_DIR, { recursive: true });
      } else {
        throw err;
      }
    }

    // Get all available migration files, parse versions, and sort
    // @ts-ignore
    const migrationFiles = (await fs.readdir(MIGRATIONS_DIR))
      .filter((file) => file.endsWith(".js"))
      .map((file) => ({ name: file, version: parseInt(file.split("_")[0]) }))
      .filter((file) => !isNaN(file.version))
      .sort((a, b) => a.version - b.version);

    // Dynamically determine the latest migration version
    const latestMigration =
      migrationFiles.length > 0
        ? migrationFiles[migrationFiles.length - 1].version
        : 0;
    if (!QUIET_BOOT)
      logSync(`Latest migration version found: ${latestMigration}`);

    // --- Refactored Migration Loop ---
    let currentVersion;
    let done = false;
    while (!done) {
      // Get the current version FROM THE DATABASE inside the loop
      const metaRow = await dbGet(
        "SELECT value FROM db_metadata WHERE key = ?",
        ["schema_version"]
      );
      currentVersion = metaRow ? parseInt(metaRow.value, 10) : 0;
      if (!QUIET_BOOT)
        logSync(
          `Current DB version: ${currentVersion}, Latest available migration: ${latestMigration}`
        );

      if (currentVersion >= latestMigration) {
        logSync("Database schema is up to date.");
        done = true;
        break; // Exit the loop
      }

      // Find the *next* migration file to apply
      const nextVersion = currentVersion + 1;
      const migrationToApply = migrationFiles.find(
        (f) => f.version === nextVersion
      );

      if (!migrationToApply) {
        // If we haven't reached the target, but there's no file for the next version, it's an error
        throw new Error(
          `Migration gap detected: Cannot find migration file for version ${nextVersion}. Current version is ${currentVersion}, Latest is ${latestMigration}.`
        );
      }

      const file = migrationToApply.name;
      const fileVersion = migrationToApply.version; // Should be === nextVersion

      logSync(`Applying migration: ${file} (Version ${fileVersion})...`);
      // @ts-ignore
      const migrationScriptPath = path.join(MIGRATIONS_DIR, file);
      const migration = require(migrationScriptPath);

      if (typeof migration.up !== "function") {
        throw new Error(
          `Migration script ${file} must export an async function named 'up'.`
        );
      }

      // +++ Require playerDataManager here and pass needed functions +++
      const playerDataUtils = {
        getPlayerData,
        savePlayerData,
        getAllPlayerIds,
      };

      // Run migration within a transaction
      try {
        await dbRun("BEGIN TRANSACTION");
        await migration.up(db, playerDataUtils); // Pass the db connection AND utils
        // Update version number AFTER script execution
        await dbRun("UPDATE db_metadata SET value = ? WHERE key = ?", [
          fileVersion,
          "schema_version",
        ]);
        await dbRun("COMMIT");
        logSync(
          `Successfully applied migration ${file} and updated DB version to ${fileVersion}.`
        );
        // currentVersion will be updated on the next loop iteration when read from DB
      } catch (migrationError) {
        logSync(`Error applying migration ${file}: ${migrationError}`, false);
        await dbRun("ROLLBACK");
        logSync("Transaction rolled back.");
        throw migrationError; // Stop further execution
      }
    }

    // --- End Refactored Migration Loop ---
  } catch (err) {
    logSync(`DATABASE MIGRATION FAILED: ${err.message}`, false);
    logSync("Halting bot startup due to migration failure.", false);
    // @ts-ignore
    process.exit(1);
  }
}
// --- End Database Migration Setup ---

// +++ Time Tracking +++
// @ts-ignore
const timeStateFilePath = path.join(__dirname, "data", "time_state.json");
// Define default epoch here for clarity
const DEFAULT_DISBLOCK_EPOCH_UTC = new Date("2025-05-12T00:00:00Z").getTime();

// Rename state variable and fields
let disBlockTimeState = {
  disBlockEpochUTC: DEFAULT_DISBLOCK_EPOCH_UTC,
  lastSavedRealTimestampUTC: Date.now(),
  disBlockSecondsElapsed: 0, // Will be recalculated on load
};

// Function to get the current time state
function getTimeState() {
  return disBlockTimeState;
}

// Function to load time state and synchronize with epoch
function loadTimeState() {
  if (!QUIET_BOOT) console.log("[Time] Loading DisBlock time state...");
  let loadedEpoch = DEFAULT_DISBLOCK_EPOCH_UTC;

  // Try to load existing epoch from file, but prioritize the default if file is bad
  if (fsSync.existsSync(timeStateFilePath)) {
    try {
      // @ts-ignore
      const data = fsSync.readFileSync(timeStateFilePath, "utf8");
      const loadedState = JSON.parse(data);
      if (loadedState && typeof loadedState.disBlockEpochUTC === "number") {
        loadedEpoch = loadedState.disBlockEpochUTC;
        // Optional: Log if loaded epoch differs from default, could indicate manual change
        if (loadedEpoch !== DEFAULT_DISBLOCK_EPOCH_UTC) {
          console.warn(
            `[Time] Loaded epoch ${loadedEpoch} differs from default ${DEFAULT_DISBLOCK_EPOCH_UTC}. Using loaded value.`
          );
        }
      } else {
        console.warn(
          "[Time] Invalid or missing epoch in time state file. Using default epoch."
        );
      }
    } catch (err) {
      console.error(
        "[Time] Error reading time state file on epoch load attempt:",
        err
      );
      console.warn("[Time] Using default epoch due to file read error.");
    }
  } else {
    if (!QUIET_BOOT)
      console.log("[Time] Time state file not found. Using default epoch.");
    // Ensure file is created with defaults if it doesn't exist
    initializeTimeStateFile();
  }

  // --- Always recalculate elapsed time from the determined epoch ---
  const currentRealTimestampUTC = Date.now();
  const totalRealSecondsSinceEpoch =
    (currentRealTimestampUTC - loadedEpoch) / 1000;
  const authoritativeDisBlockSecondsElapsed =
    totalRealSecondsSinceEpoch * DISBLOCK_SECONDS_PER_REAL_SECOND;

  // Update the in-memory state
  disBlockTimeState = {
    disBlockEpochUTC: loadedEpoch, // Use the epoch we determined (loaded or default)
    lastSavedRealTimestampUTC: currentRealTimestampUTC, // Set last saved to now
    disBlockSecondsElapsed: authoritativeDisBlockSecondsElapsed, // Set the authoritative time
  };

  if (!QUIET_BOOT)
    console.log(
      `[Time] Synchronized time state with epoch ${loadedEpoch}. Current elapsed seconds: ${authoritativeDisBlockSecondsElapsed.toFixed(
        0
      )}`
    );

  // Initial save of the potentially newly synchronized state
  // This replaces the old immediate save after offline calc.
  saveTimeState();
}

// Function to save time state (synchronous)
function saveTimeState() {
  try {
    disBlockTimeState.lastSavedRealTimestampUTC = Date.now(); // Update timestamp before saving
    const data = JSON.stringify(disBlockTimeState, null, 2);
    // @ts-ignore
    fsSync.writeFileSync(timeStateFilePath, data, "utf8"); // Use fsSync here
    // console.log('[Time] Saved DisBlock time state.'); // Update log (optional)
  } catch (err) {
    console.error("[Time] Error saving DisBlock time state:", err); // Update log
  }
}

// Function to initialize the time state file with defaults
function initializeTimeStateFile() {
  if (!QUIET_BOOT)
    console.log(
      "[Time] Initializing DisBlock time state file with defaults..."
    );
  // Use new names and default epoch
  disBlockTimeState = {
    disBlockEpochUTC: DEFAULT_DISBLOCK_EPOCH_UTC, // Use default
    lastSavedRealTimestampUTC: Date.now(),
    disBlockSecondsElapsed: 0, // Start at 0 when initializing *new* file
  };
  saveTimeState(); // Save the initial state (Epoch, Now, 0 seconds)
}

// Function to advance DisBlock time
function advanceDisBlockTime() {
  // Rename function
  const currentRealTimestampUTC = Date.now(); // Get current time at the start
  const realSecondsElapsed =
    (currentRealTimestampUTC - disBlockTimeState.lastSavedRealTimestampUTC) /
    1000;

  // Use the imported constant (renamed)
  const disBlockSecondsToAdd =
    realSecondsElapsed * DISBLOCK_SECONDS_PER_REAL_SECOND;

  // Silent time advancement

  disBlockTimeState.disBlockSecondsElapsed += disBlockSecondsToAdd;
  disBlockTimeState.lastSavedRealTimestampUTC = currentRealTimestampUTC; // Use the timestamp from the start of the function call

  // Silent completion
}

// Load initial time state (which now includes synchronization)
try {
  loadTimeState();
} catch (err) {
  // Error during loadTimeState should be handled internally now
  console.error("[Time] Critical error during loadTimeState execution:", err);
  // Fallback to absolute defaults if loadTimeState itself fails badly
  disBlockTimeState = {
    disBlockEpochUTC: DEFAULT_DISBLOCK_EPOCH_UTC,
    lastSavedRealTimestampUTC: Date.now(),
    disBlockSecondsElapsed: 0,
  };
  console.log("[Time] Using absolute default time state due to load error.");
}

if (!QUIET_BOOT) console.log("[Time] DisBlock Time tracking initialized.");

// Interval to advance time IN MEMORY frequently (e.g., every 2 seconds)
const TIME_ADVANCE_INTERVAL_MS = 2 * 1000;
const timeAdvanceTimerId = safeSetInterval(() => {
  try {
    advanceDisBlockTime(); // Use renamed function
  } catch (error) {
    console.error("[Time] Error in time advancement:", error);
  }
}, TIME_ADVANCE_INTERVAL_MS);

// Interval to SAVE time state periodically (e.g., every 1 minute)
const TIME_SAVE_INTERVAL_MS = 1 * 60 * 1000;
const timeSaveTimerId = safeSetInterval(() => {
  try {
    saveTimeState();
    // Optional: Log save occurred
    // console.log(`[Time] Auto-saved DisBlock state at interval.`); // Update log
  } catch (error) {
    console.error("[Time] Error in time state saving:", error);
  }
}, TIME_SAVE_INTERVAL_MS);

// Log timer setup with robust timer system
if (timeAdvanceTimerId && timeSaveTimerId) {
  if (!QUIET_BOOT)
    console.log("[Time] Robust timer system initialized for time tracking");

  // Register time tracking cleanup with graceful error handler now that timers are defined
  gracefulErrorHandler.registerCleanupHandler(async () => {
    console.log("[Shutdown] Cleaning up time tracking timers...");
    safeClearInterval(timeAdvanceTimerId);
    safeClearInterval(timeSaveTimerId);
    robustTimer.cleanup();
    console.log("[Shutdown] Time tracking timers cleaned up");
  }, "time-tracking");
} else {
  console.warn("[Time] Failed to initialize some time tracking timers");
}

// Set up time jump monitoring and recovery
robustTimer.on("timeJump", (data) => {
  console.warn(
    `[Time] Time jump detected: ${data.difference}ms difference between real and monotonic time`
  );
});

robustTimer.on("backwardTimeJump", (data) => {
  console.error(`[Time] Backward time jump detected: ${data.difference}ms`);
  console.log(
    "[Time] Attempting to recover from time synchronization issue..."
  );

  // The robust timer system will automatically handle timer recovery
  // Log the current timer status for debugging
  const timerInfo = robustTimer.getTimerInfo();
  console.log(`[Time] Current timer status: ${JSON.stringify(timerInfo)}`);
});

// Time tracking cleanup will be registered after startBot() initializes everything

// --- End Time Tracking ---

// Main bot logic wrapped in an async function to allow await at top level
async function startBot() {
  try {
    logSync("Starting bot initialization...", true);

    // --- Initialize Resilient Express Server ---
    let migrationsComplete = false;
    const {
      ResilientExpressServer,
    } = require("./utils/resilientExpressServer");
    const resilientServer = new ResilientExpressServer({
      port: process.env.API_PORT || 3000,
      host: process.env.API_HOST || "0.0.0.0",
      maxRestartAttempts: 3,
      restartDelay: 5000,
      healthCheckInterval: 30000,
    });

    // Add routes to the resilient server
    resilientServer.addRoutes((app) => {
      const crypto = require("crypto");
      const DASHBOARD_AUTH_MODE = process.env.DASHBOARD_AUTH_MODE || "token"; // 'token' | 'discord'
      const DASHBOARD_MIN_RANK = (
        process.env.DASHBOARD_MIN_RANK || "ADMIN"
      ).toUpperCase();
      const DASHBOARD_COOKIE_SECRET =
        process.env.DASHBOARD_COOKIE_SECRET ||
        process.env.INTERNAL_API_TOKEN ||
        "change-this-secret";
      const DISCORD_CLIENT_ID =
        process.env.DISCORD_OAUTH_CLIENT_ID || process.env.DISCORD_CLIENT_ID;
      const DISCORD_CLIENT_SECRET =
        process.env.DISCORD_OAUTH_CLIENT_SECRET ||
        process.env.DISCORD_CLIENT_SECRET;
      const DISCORD_REDIRECT_URI =
        process.env.DISCORD_OAUTH_REDIRECT_URI ||
        process.env.DISCORD_REDIRECT_URI ||
        `${process.env.PUBLIC_BASE_URL || ""}/auth/discord/callback`;

      function base64url(input) {
        return Buffer.from(input)
          .toString("base64")
          .replace(/=/g, "")
          .replace(/\+/g, "-")
          .replace(/\//g, "_");
      }
      function signPayload(payload) {
        return crypto
          .createHmac("sha256", DASHBOARD_COOKIE_SECRET)
          .update(payload)
          .digest("base64url");
      }
      function setAuthCookie(res, userId) {
        const exp = Date.now() + 7 * 24 * 60 * 60 * 1000;
        const payloadObj = { u: String(userId), exp };
        const payload = base64url(JSON.stringify(payloadObj));
        const sig = signPayload(payload);
        const value = `${payload}.${sig}`;
        res.setHeader(
          "Set-Cookie",
          `dashboard_auth=${value}; Path=/; HttpOnly; SameSite=Lax; Max-Age=${7 * 24 * 60 * 60}`
        );
      }
      function clearAuthCookie(res) {
        res.setHeader(
          "Set-Cookie",
          "dashboard_auth=; Path=/; HttpOnly; SameSite=Lax; Max-Age=0"
        );
      }
      function parseCookies(req) {
        const header = req.headers["cookie"];
        if (!header) return {};
        const out = {};
        header.split(";").forEach((part) => {
          const idx = part.indexOf("=");
          const key = part.slice(0, idx).trim();
          const val = part.slice(idx + 1).trim();
          out[key] = decodeURIComponent(val);
        });
        return out;
      }
      async function verifySession(req) {
        try {
          const cookies = parseCookies(req);
          const raw = cookies["dashboard_auth"];
          if (!raw) return null;
          const [payload, sig] = raw.split(".");
          if (!payload || !sig) return null;
          const expected = signPayload(payload);
          if (sig !== expected) return null;
          const { u, exp } = JSON.parse(
            Buffer.from(
              payload.replace(/-/g, "+").replace(/_/g, "/"),
              "base64"
            ).toString("utf8")
          );
          if (!u || !exp || Date.now() > exp) return null;
          const { getPlayerData } = require("./utils/playerDataManager");
          const character = await getPlayerData(String(u));
          const { checkRankPermission } = require("./utils/permissionUtils");
          if (!checkRankPermission(character, DASHBOARD_MIN_RANK)) return null;
          return { userId: String(u), rank: character?.rank || "MEMBER" };
        } catch {
          try {
            channel = await client.channels.fetch(channelId);
          } catch {
            /* ignore */
          }
        }
      }
      // Internal API auth middleware
      const CONFIGURED_TOKEN = process.env.INTERNAL_API_TOKEN;
      const PROTECT_DASHBOARD =
        process.env.DASHBOARD_PROTECT === "true" ||
        DASHBOARD_AUTH_MODE === "discord";
      if (!CONFIGURED_TOKEN) {
        console.warn(
          "[Main Bot] INTERNAL_API_TOKEN not set. Internal routes are not authenticated. Set it in .env for production or multi-process."
        );
      } else {
        app.use(async (req, res, next) => {
          const isInternal =
            req.path.startsWith("/register-worker") ||
            req.path.startsWith("/worker-notification") ||
            req.path.startsWith("/api/database");
          const isDashboard =
            PROTECT_DASHBOARD &&
            (req.path.startsWith("/api/active-actions") ||
              req.path.startsWith("/api/cluster-stats") ||
              req.path.startsWith("/worker-stats") ||
              req.path.startsWith("/api/debug") ||
              req.path === "/dashboard" ||
              req.path === "/");
          if (
            req.path.startsWith("/auth/discord") ||
            req.path === "/auth/logout"
          )
            return next();
          if (!isInternal && !isDashboard) return next();
          const token = req.header("x-internal-token");
          if (token && token === CONFIGURED_TOKEN) return next();
          if (isDashboard && DASHBOARD_AUTH_MODE === "discord") {
            const session = await verifySession(req);
            if (session) return next();
            const wantsHtml =
              (req.headers["accept"] || "").includes("text/html") ||
              req.path === "/dashboard" ||
              req.path === "/";
            const returnTo = encodeURIComponent(
              req.originalUrl || "/dashboard"
            );
            if (wantsHtml)
              return res.redirect(`/auth/discord/login?returnTo=${returnTo}`);
            return res.status(403).json({ error: "forbidden" });
          }
          return res.status(403).json({ error: "forbidden" });
        });
      }

      if (DASHBOARD_AUTH_MODE === "discord") {
        app.get("/auth/discord/login", (req, res) => {
          if (
            !DISCORD_CLIENT_ID ||
            !DISCORD_CLIENT_SECRET ||
            !DISCORD_REDIRECT_URI
          ) {
            return res
              .status(500)
              .send(
                "Discord OAuth not configured. Set DISCORD_OAUTH_CLIENT_ID, DISCORD_OAUTH_CLIENT_SECRET, and DISCORD_OAUTH_REDIRECT_URI"
              );
          }
          const statePayload = base64url(
            JSON.stringify({
              t: Date.now(),
              r: String(req.query.returnTo || "/dashboard"),
            })
          );
          const stateSig = signPayload(statePayload);
          const state = `${statePayload}.${stateSig}`;
          const params = new URLSearchParams({
            client_id: DISCORD_CLIENT_ID,
            response_type: "code",
            scope: "identify",
            redirect_uri: DISCORD_REDIRECT_URI,
            state,
          });
          res.redirect(
            `https://discord.com/api/oauth2/authorize?${params.toString()}`
          );
        });

        app.get("/auth/discord/callback", async (req, res) => {
          try {
            const { code, state } = req.query;
            if (!code || !state)
              return res.status(400).send("Missing code or state");
            const [spayload, ssig] = String(state).split(".");
            if (!spayload || !ssig || signPayload(spayload) !== ssig)
              return res.status(400).send("Invalid state");
            const { t, r } = JSON.parse(
              Buffer.from(
                spayload.replace(/-/g, "+").replace(/_/g, "/"),
                "base64"
              ).toString("utf8")
            );
            if (!t || Date.now() - t > 10 * 60 * 1000)
              return res.status(400).send("State expired");

            const body = new URLSearchParams({
              client_id: DISCORD_CLIENT_ID,
              client_secret: DISCORD_CLIENT_SECRET,
              grant_type: "authorization_code",
              code: String(code),
              redirect_uri: DISCORD_REDIRECT_URI,
            });
            const tokenResp = await fetch(
              "https://discord.com/api/oauth2/token",
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/x-www-form-urlencoded",
                },
                body,
              }
            );
            if (!tokenResp.ok) {
              const txt = await tokenResp.text();
              return res
                .status(502)
                .send(`OAuth token exchange failed: ${txt}`);
            }
            const tokenData = await tokenResp.json();
            const userResp = await fetch("https://discord.com/api/users/@me", {
              headers: { Authorization: `Bearer ${tokenData.access_token}` },
            });
            if (!userResp.ok)
              return res.status(502).send("Failed to fetch user");
            const user = await userResp.json();
            const userId = user.id;
            const { getPlayerData } = require("./utils/playerDataManager");
            const { checkRankPermission } = require("./utils/permissionUtils");
            const character = await getPlayerData(String(userId));
            if (!checkRankPermission(character, DASHBOARD_MIN_RANK)) {
              return res.status(403).send("Not authorized");
            }
            setAuthCookie(res, String(userId));
            res.redirect(r || "/dashboard");
          } catch (e) {
            res.status(500).send("Authentication failed");
          }
        });

        app.get("/auth/logout", (req, res) => {
          clearAuthCookie(res);
          res.redirect("/");
        });
      }
      // Setup worker management routes
      const { setupWorkerRoutes } = require("./utils/workerManager");
      setupWorkerRoutes(app);
      const { workerManager } = require("./utils/workerManager");

      // API endpoint for worker repeat button requests (re-route through main delegation system)
      app.post("/api/repeat-action-request", async (req, res) => {
        try {
          const {
            userId,
            skillName,
            resourceKey,
            amount: rawAmount,
            channelId,
            wasMax,
          } = req.body;
          if (!userId || !skillName || !channelId) {
            return res
              .status(400)
              .json({ success: false, error: "Missing required fields" });
          }

          const {
            trySetActivity,
            getCurrentActivity,
            clearActivity,
          } = require("./utils/activityManager");
          // Busy check
          const currentActivity = await getCurrentActivity(userId);
          if (currentActivity) {
            return res
              .status(200)
              .json({ success: false, reason: "busy", currentActivity });
          }
          // Attempt to set activity
          const activitySet = await trySetActivity(userId, skillName);
          if (!activitySet) {
            const stillBusy = await getCurrentActivity(userId);
            return res.status(200).json({
              success: false,
              reason: "busy",
              currentActivity: stillBusy,
            });
          }

          // Recalculate amount if wasMax flag provided (always recompute to reflect new level caps)
          let amount = rawAmount || 1;
          if (wasMax) {
            try {
              const {
                getPlayerData,
                getPlayerSkillLevel,
              } = require("./utils/playerDataManager");
              const character = await getPlayerData(userId);
              if (character) {
                const {
                  calculateMaxActionAmount,
                } = require("./utils/skillLimits");
                const lvl = getPlayerSkillLevel(character, skillName) || 0;
                amount = calculateMaxActionAmount(lvl);
              }
            } catch (e) {
              console.error(
                "[RepeatActionAPI] Failed to recalc max amount:",
                e.message
              );
            }
          }

          // Region lock validation (skip for alchemy)
          if (skillName !== "alchemy") {
            try {
              const { getPlayerData } = require("./utils/playerDataManager");
              const { checkRegionPermissions } = require("./utils/regionUtils");
              const characterForRegion = await getPlayerData(userId);
              if (characterForRegion) {
                const regionCheck = checkRegionPermissions(
                  characterForRegion,
                  skillName,
                  null,
                  resourceKey,
                  "resource"
                );
                if (!regionCheck.allowed) {
                  // clear activity since we won't proceed
                  await clearActivity(userId);
                  return res
                    .status(200)
                    .json({ success: false, reason: "region_locked" });
                }
              }
            } catch (regionErr) {
              console.error(
                "[RepeatActionAPI] Region validation failed:",
                regionErr?.message || regionErr
              );
              // proceed anyway – worker will likely enforce again
            }
          }

          // Map skill to command name
          const skillToCommand = {
            farming: "farm",
            foraging: "forage",
            mining: "mine",
            fishing: "fish",
            combat: "combat",
            alchemy: "brew",
          };
          const commandName = skillToCommand[skillName] || skillName;
          const command = client.commands.get(commandName);
          if (!command) {
            await clearActivity(userId);
            return res
              .status(404)
              .json({ success: false, error: "Command not found" });
          }

          // Resolve channel & user objects
          let channel = client.channels.cache.get(channelId);
          if (!channel) {
            try {
              channel = await client.channels.fetch(channelId);
            } catch (e) {
              /* ignore */
            }
          }
          if (!channel) {
            await clearActivity(userId);
            return res
              .status(404)
              .json({ success: false, error: "Channel not found" });
          }
          let userObj = client.users.cache.get(userId);
          if (!userObj) {
            try {
              userObj = await client.users.fetch(userId);
            } catch {
              /* ignore */
            }
          }
          if (!userObj) {
            await clearActivity(userId);
            return res
              .status(404)
              .json({ success: false, error: "User not found" });
          }

          // Param name mapping for option simulation
          const skillParamMap = {
            fishing: null,
            farming: "crop",
            mining: "resource",
            foraging: "resource",
            combat: "mob",
            alchemy: "potion",
          };
          const paramName = skillParamMap[skillName];

          // Build persistent interaction-like object for delegation path
          let replyMessage = null; // will not usually be used because delegateAction deletes reply
          const persistentInteraction = {
            user: userObj,
            channel,
            channelId,
            guild: channel.guild || null,
            commandName,
            deferred: false,
            replied: false,
            isFromRepeatButton: true,
            options: {
              getString: (name) => {
                if (paramName && name === paramName) return resourceKey || null;
                if (name === "amount")
                  return wasMax ? "max" : amount > 1 ? String(amount) : null;
                return null;
              },
              getInteger: (name) => {
                if (name === "amount")
                  return wasMax ? null : amount > 1 ? amount : null;
                return null;
              },
              getBoolean: () => null,
              getUser: () => null,
              getMember: () => null,
              getChannel: () => null,
              getRole: () => null,
              getMentionable: () => null,
              getNumber: () => null,
              getAttachment: () => null,
              getFocused: () => null,
              getSubcommand: () => null,
              getSubcommandGroup: () => null,
            },
            reply: async (content) => {
              if (!replyMessage) {
                replyMessage = await channel.send(
                  typeof content === "string" ? { content } : content
                );
                persistentInteraction.replied = true;
                return replyMessage;
              }
              return replyMessage.edit(
                typeof content === "string" ? { content } : content
              );
            },
            editReply: async (content) => {
              if (!replyMessage) {
                replyMessage = await channel.send(
                  typeof content === "string" ? { content } : content
                );
                persistentInteraction.replied = true;
                return replyMessage;
              }
              return replyMessage.edit(
                typeof content === "string" ? { content } : content
              );
            },
            deferReply: async () => {
              persistentInteraction.deferred = true;
            },
            deleteReply: async () => {
              if (replyMessage) {
                try {
                  await replyMessage.delete();
                } catch (_) {}
              }
            },
          };

          try {
            await command.execute(persistentInteraction);
          } catch (execErr) {
            console.error(
              "[RepeatActionAPI] Command execution error:",
              execErr
            );
            await clearActivity(userId); // cleanup on failure
            return res
              .status(500)
              .json({ success: false, error: execErr.message });
          }

          return res.json({ success: true, delegated: true });
        } catch (err) {
          console.error("[RepeatActionAPI] Unexpected error:", err);
          return res.status(500).json({ success: false, error: err.message });
        }
      });

      // API endpoint for worker action started notification

      // Database API endpoints for worker bots (no direct DB access for workers)
      app.post("/api/database/get-player-data", async (req, res) => {
        try {
          const { userId } = req.body;
          if (!userId) {
            return res.status(400).json({ error: "userId is required" });
          }
          const character = await getPlayerData(userId);
          res.json({ success: true, data: character });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/get-player-data error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/save-player-data", async (req, res) => {
        try {
          const { userId, playerData } = req.body;
          if (!userId || !playerData) {
            return res
              .status(400)
              .json({ error: "userId and playerData are required" });
          }
          await savePlayerData(userId, playerData);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/save-player-data error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/update-inventory", async (req, res) => {
        try {
          const { updateInventoryAtomically } = require("./utils/inventory");
          const {
            userId,
            coinsToAdd = 0,
            itemsToChange = [],
            equipmentToAdd = [],
            equipmentIdsToRemove = [],
            bankCoinsToAdd = 0,
            equipmentDataUpdates = [],
            islandJsonString = null,
            collectionsJsonString = null,
            accessoriesToAdd = [],
            accessoryIdsToRemove = [],
          } = req.body;

          if (!userId) {
            return res.status(400).json({ error: "userId is required" });
          }

          await updateInventoryAtomically(
            userId,
            coinsToAdd,
            itemsToChange,
            equipmentToAdd,
            equipmentIdsToRemove,
            bankCoinsToAdd,
            equipmentDataUpdates,
            islandJsonString,
            collectionsJsonString,
            false, // useExistingTransaction
            accessoriesToAdd,
            accessoryIdsToRemove
          );

          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-inventory error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/update-player-skill-data", async (req, res) => {
        try {
          const {
            updatePlayerSkillDataAtomically,
          } = require("./utils/playerDataManager");
          const { userId, updates } = req.body;

          if (!userId || !updates) {
            return res
              .status(400)
              .json({ error: "userId and updates are required" });
          }

          await updatePlayerSkillDataAtomically(userId, updates);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-player-skill-data error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/recalculate-and-save-stats", async (req, res) => {
        try {
          const {
            recalculateAndSaveStats,
          } = require("./utils/playerDataManager");
          const { discordId, character } = req.body;

          if (!discordId || !character) {
            return res
              .status(400)
              .json({ error: "discordId and character are required" });
          }

          await recalculateAndSaveStats(discordId, character);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/recalculate-and-save-stats error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/get-action-by-id", async (req, res) => {
        try {
          const { getActionById } = require("./utils/actionPersistence");
          const { actionId } = req.body;

          if (!actionId) {
            return res.status(400).json({ error: "actionId is required" });
          }

          const action = await getActionById(actionId);
          res.json({ success: true, data: action });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/get-action-by-id error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      // new endpoint for workers to get their own pending actions for self-resumption
      app.post(
        "/api/database/get-pending-actions-for-worker",
        async (req, res) => {
          try {
            const { dbAll } = require("./utils/dbUtils");
            const { workerId } = req.body;

            if (!workerId) {
              return res.status(400).json({ error: "workerId is required" });
            }

            // get all pending actions for this specific worker
            const pendingActions = await dbAll(
              "SELECT * FROM active_actions WHERE worker_id = ? ORDER BY start_timestamp ASC",
              [workerId]
            );

            console.log(
              `[Main Bot] Retrieved ${pendingActions.length} pending actions for worker ${workerId}`
            );
            res.json({ success: true, data: pendingActions });
          } catch (error) {
            console.error(
              "[Main Bot] /api/database/get-pending-actions-for-worker error:",
              error
            );
            res.status(500).json({ error: error.message });
          }
        }
      );

      app.post("/api/database/start-action", async (req, res) => {
        try {
          const { startAction } = require("./utils/actionPersistence");
          const {
            userId,
            actionType,
            resourceKey,
            parameters,
            totalAmount,
            channelId,
            messageId,
            workerId,
          } = req.body;

          if (!userId || !actionType || totalAmount === undefined) {
            return res.status(400).json({
              error: "userId, actionType, and totalAmount are required",
            });
          }

          const actionId = await startAction(
            userId,
            actionType,
            resourceKey,
            parameters,
            totalAmount,
            channelId,
            messageId,
            workerId
          );
          res.json({ success: true, data: actionId });
        } catch (error) {
          console.error("[Main Bot] /api/database/start-action error:", error);
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/update-action-progress", async (req, res) => {
        try {
          const { updateActionProgress } = require("./utils/actionPersistence");
          const { actionId, completedCycles, currentCycleResult } = req.body;

          if (!actionId || completedCycles === undefined) {
            return res
              .status(400)
              .json({ error: "actionId and completedCycles are required" });
          }

          await updateActionProgress(
            actionId,
            completedCycles,
            currentCycleResult
          );
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-action-progress error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/complete-action", async (req, res) => {
        try {
          const { completeAction } = require("./utils/actionPersistence");
          const { actionId } = req.body;

          if (!actionId) {
            return res.status(400).json({ error: "actionId is required" });
          }

          await completeAction(actionId);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/complete-action error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/get-action-parameters", async (req, res) => {
        try {
          const { getActionParameters } = require("./utils/actionPersistence");
          const { actionId } = req.body;

          if (!actionId) {
            return res.status(400).json({ error: "actionId is required" });
          }

          const parameters = await getActionParameters(actionId);
          res.json({ success: true, data: parameters });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/get-action-parameters error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/update-action-parameters", async (req, res) => {
        try {
          const {
            updateActionParameters,
          } = require("./utils/actionPersistence");
          const { actionId, newParams } = req.body;

          if (!actionId || !newParams) {
            return res
              .status(400)
              .json({ error: "actionId and newParams are required" });
          }

          await updateActionParameters(actionId, newParams);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-action-parameters error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/update-action-message-id", async (req, res) => {
        try {
          const {
            updateActionMessageId,
          } = require("./utils/actionPersistence");
          const { actionId, newMessageId } = req.body;

          if (!actionId || !newMessageId) {
            return res
              .status(400)
              .json({ error: "actionId and newMessageId are required" });
          }

          await updateActionMessageId(actionId, newMessageId);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-action-message-id error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post(
        "/api/database/update-action-message-timestamp",
        async (req, res) => {
          try {
            const {
              updateActionMessageTimestamp,
            } = require("./utils/actionPersistence");
            const { actionId, timestamp } = req.body;

            if (!actionId || timestamp === undefined) {
              return res
                .status(400)
                .json({ error: "actionId and timestamp are required" });
            }

            await updateActionMessageTimestamp(actionId, timestamp);
            res.json({ success: true });
          } catch (error) {
            console.error(
              "[Main Bot] /api/database/update-action-message-timestamp error:",
              error
            );
            res.status(500).json({ error: error.message });
          }
        }
      );

      app.post("/api/database/update-action-worker-id", async (req, res) => {
        try {
          const { dbRunQueued } = require("./utils/dbUtils");
          const { actionId, workerId } = req.body;

          if (!actionId || !workerId) {
            return res
              .status(400)
              .json({ error: "actionId and workerId are required" });
          }

          await dbRunQueued(
            "UPDATE active_actions SET worker_id = ? WHERE id = ?",
            [workerId, actionId]
          );
          console.log(
            `[Main Bot] Updated action ${actionId} worker_id to ${workerId}`
          );
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-action-worker-id error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/clear-activity", async (req, res) => {
        try {
          const { clearActivity } = require("./utils/activityManager");
          const { userId } = req.body;

          if (!userId) {
            return res.status(400).json({ error: "userId is required" });
          }

          await clearActivity(userId);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/clear-activity error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/get-current-activity", async (req, res) => {
        try {
          const { getCurrentActivity } = require("./utils/activityManager");
          const { userId } = req.body;

          if (!userId) {
            return res.status(400).json({ error: "userId is required" });
          }

          const activity = await getCurrentActivity(userId);
          res.json({ success: true, activity });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/get-current-activity error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      // new endpoint for detailed activity information (more useful than just action_type)
      app.post("/api/database/get-detailed-activity", async (req, res) => {
        try {
          const { getDetailedActivity } = require("./utils/activityManager");
          const { userId } = req.body;

          if (!userId) {
            return res.status(400).json({ error: "userId is required" });
          }

          const detailedActivity = await getDetailedActivity(userId);
          res.json({ success: true, data: detailedActivity });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/get-detailed-activity error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/set-activity", async (req, res) => {
        try {
          const { setActivity } = require("./utils/activityManager");
          const { userId, activityType, data } = req.body;

          if (!userId || !activityType) {
            return res
              .status(400)
              .json({ error: "userId and activityType are required" });
          }

          const result = await setActivity(userId, activityType, data);
          res.json({ success: true, result });
        } catch (error) {
          console.error("[Main Bot] /api/database/set-activity error:", error);
          res.status(500).json({ error: error.message });
        }
      });

      app.post("/api/database/is-conflict", async (req, res) => {
        try {
          const { isConflict } = require("./utils/activityManager");
          const { userId, activityType } = req.body;

          if (!userId || !activityType) {
            return res
              .status(400)
              .json({ error: "userId and activityType are required" });
          }

          const isConflictResult = await isConflict(userId, activityType);
          res.json({ success: true, isConflict: isConflictResult });
        } catch (error) {
          console.error("[Main Bot] /api/database/is-conflict error:", error);
          res.status(500).json({ error: error.message });
        }
      });

      // update player setting endpoint for worker bots
      app.post("/api/database/update-player-setting", async (req, res) => {
        try {
          const { updatePlayerSetting } = require("./utils/playerDataManager");
          const { userId, settingKey, settingValue } = req.body;

          if (!userId || !settingKey || settingValue === undefined) {
            return res.status(400).json({
              error: "userId, settingKey, and settingValue are required",
            });
          }

          await updatePlayerSetting(userId, settingKey, settingValue);
          res.json({ success: true });
        } catch (error) {
          console.error(
            "[Main Bot] /api/database/update-player-setting error:",
            error
          );
          res.status(500).json({ error: error.message });
        }
      });

      // update player discord nickname endpoint for worker bots
      app.post("/api/update-nickname", async (req, res) => {
        try {
          const { userId, currentXP } = req.body;

          if (!userId) {
            return res.status(400).json({ error: "userId is required" });
          }

          // use the main bot's discord client to update nickname
          const { getPlayerData } = require("./utils/playerDataManager");
          const {
            calculateDisblockLevel,
          } = require("./utils/disblockXpSystem");
          const config = require("./config.json");

          const characterData = await getPlayerData(userId);
          if (!characterData?.name) {
            return res.json({
              success: false,
              error: "No character data found",
            });
          }

          const integerLevel = calculateDisblockLevel(currentXP || 0).level;
          const expectedNickname = `[${integerLevel}] ${characterData.name}`;

          const guild = client.guilds.cache.get(config.guildId);
          if (!guild) {
            return res.json({ success: false, error: "Guild not found" });
          }

          const member = await guild.members.fetch(userId);
          if (!member) {
            return res.json({ success: false, error: "Member not found" });
          }

          const currentNickname = member.displayName;

          // only update if nickname is different
          if (currentNickname !== expectedNickname) {
            if (
              guild.members.me.permissions.has("ManageNicknames") &&
              member.id !== guild.ownerId
            ) {
              await member.setNickname(expectedNickname);
              console.log(
                `[API] Updated nickname for ${userId} to "${expectedNickname}"`
              );
              res.json({
                success: true,
                updated: true,
                nickname: expectedNickname,
              });
            } else {
              res.json({
                success: false,
                error: "Insufficient permissions or target is guild owner",
              });
            }
          } else {
            res.json({
              success: true,
              updated: false,
              nickname: currentNickname,
            });
          }
        } catch (error) {
          console.error("[Main Bot] /api/update-nickname error:", error);
          res.status(500).json({ error: error.message });
        }
      });

      // Health check endpoint
      app.get("/health", (req, res) => {
        res.json({
          status: "healthy",
          botType: "main",
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          ready: migrationsComplete,
          serverHealth: resilientServer.getHealthStatus(),
          errorHandlerHealth: gracefulErrorHandler.getHealthStatus(),
        });
      });

      // Debug endpoint for monitoring main bot state
      app.get("/api/debug", (req, res) => {
        res.json({
          status: "running",
          botType: "main",
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          ready: migrationsComplete,
          workerStats: workerManager.getWorkerStats(),
          serverHealth: resilientServer.getHealthStatus(),
          errorHandlerHealth: gracefulErrorHandler.getHealthStatus(),
          timestamp: new Date().toISOString(),
        });
      });

      app.get("/api/active-actions", async (req, res) => {
        try {
          const { dbAll } = require("./utils/dbUtils");
          const rows = await dbAll(
            `SELECT a.id, a.user_id, p.name AS player_name, a.action_type, a.resource_key, a.completed_cycles, a.total_amount, a.worker_id
              FROM active_actions a
              LEFT JOIN players p ON p.discord_id = a.user_id
              ORDER BY a.start_timestamp DESC
              LIMIT 200`
          );
          res.json({ success: true, data: rows });
        } catch (error) {
          console.error("[Main Bot] /api/active-actions error:", error);
          res.status(500).json({ error: error.message });
        }
      });

      app.get("/api/cluster-stats", async (req, res) => {
        try {
          const mainMem = process.memoryUsage();
          const workers = {};
          let workersRss = 0;
          for (const [id, w] of workerManager.workers.entries()) {
            const m = w.memoryUsage || {};
            workers[id] = {
              rss: m.rss || 0,
              heapTotal: m.heapTotal || 0,
              heapUsed: m.heapUsed || 0,
              external: m.external || 0,
            };
            workersRss += m.rss || 0;
          }
          res.json({
            main: {
              rss: mainMem.rss,
              heapTotal: mainMem.heapTotal,
              heapUsed: mainMem.heapUsed,
              external: mainMem.external,
            },
            workers,
            totalRss: (mainMem.rss || 0) + workersRss,
          });
        } catch (error) {
          console.error("[Main Bot] /api/cluster-stats error:", error);
          res.status(500).json({ error: error.message });
        }
      });

      app.get("/dashboard", (req, res) => {
        res.type("html").send(`<!doctype html>
<html>
<head>
<meta charset='utf-8'>
<meta name='viewport' content='width=device-width, initial-scale=1'>
<title>Disblock Monitor</title>
 <style>
 :root{
   --bg:#0b0f14;--bg-soft:#0e1420;--surface:#0f1724;--surface-2:#111b2b;--border:#1f2a3b;
   --text:#e6edf3;--muted:#9aa4af;--ok:#22c55e;--warn:#eab308;--bad:#ef4444;--accent:#3b82f6;
 }
 html,body{height:100%}
 body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,Cantarell,Noto Sans,sans-serif;margin:0;background:radial-gradient(1400px 1200px at -20% -10%, #132033 0%, var(--bg) 55%) fixed;color:var(--text);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
 .wrap{padding:22px;max-width:1180px;margin:0 auto}
 .header{display:flex;align-items:center;justify-content:space-between;margin-bottom:16px}
 .title{font-weight:700;font-size:20px;margin:0}
 .muted{color:var(--muted);font-size:12px}
 .grid{display:grid;grid-template-columns:repeat(12,1fr);gap:14px}
 .kpi{grid-column:span 2}
 @media (max-width: 1100px){.kpi{grid-column:span 4}}
 @media (max-width: 640px){.kpi{grid-column:span 12}}
 .card{background:linear-gradient(180deg,var(--surface),var(--surface-2));border:1px solid rgba(255,255,255,0.04);outline:1px solid rgba(255,255,255,0.02);border-radius:16px;padding:16px;box-shadow:0 8px 22px rgba(0,0,0,0.28)}
 .val{font-size:24px;font-weight:800;letter-spacing:0.2px}
 .ok{color:var(--ok)}.warn{color:var(--warn)}.bad{color:var(--bad)}
 .section{margin-top:14px}
 .section .title{font-size:16px}
 .divider{height:1px;background:var(--border);margin:8px 0 12px 0}
 .workers-chips{display:flex;flex-wrap:wrap;gap:10px}
 .chip{display:inline-flex;align-items:center;gap:10px;background:linear-gradient(180deg,var(--bg-soft),#0b1320);border:1px solid rgba(255,255,255,0.04);border-radius:999px;padding:8px 12px;box-shadow:0 6px 14px rgba(0,0,0,0.25)}
 .dot{display:inline-block;width:9px;height:9px;border-radius:50%}
 .dot.ok{background:var(--ok)}
 .dot.bad{background:var(--bad)}
 .badge{font-size:12px;border:1px solid rgba(255,255,255,0.08);border-radius:999px;padding:4px 8px;background:rgba(59,130,246,0.08)}
 .badge.ok{color:var(--ok);border-color:rgba(34,197,94,0.4);background:rgba(34,197,94,0.08)}
 .badge.warn{color:var(--warn);border-color:rgba(234,179,8,0.4);background:rgba(234,179,8,0.08)}
 table{width:100%;border-collapse:separate;border-spacing:0}
 th,td{padding:12px 14px;border-bottom:1px solid var(--border);text-align:left;font-size:13px}
 th{color:var(--muted);font-weight:700;background:rgba(255,255,255,0.02);position:sticky;top:0;backdrop-filter:saturate(140%) blur(2px)}
 table.compact tbody tr:nth-child(even){background:rgba(255,255,255,0.015)}
 table.compact tbody tr:hover{background:rgba(59,130,246,0.08)}
 ::-webkit-scrollbar{height:10px;width:10px}
 ::-webkit-scrollbar-thumb{background:var(--border);border-radius:8px}
 .toolbar{display:flex;gap:10px;align-items:center;margin-bottom:8px}
 .input{background:var(--bg-soft);border:1px solid rgba(255,255,255,0.06);border-radius:10px;padding:8px 10px;color:var(--text)}
 .btn{background:linear-gradient(180deg,#1c2b44,#152339);border:1px solid rgba(255,255,255,0.08);border-radius:10px;padding:8px 12px;color:var(--text);cursor:pointer}
 .modal{position:fixed;inset:0;background:rgba(0,0,0,0.45);display:none;align-items:center;justify-content:center}
 .modal.show{display:flex}
 .modal-card{background:linear-gradient(180deg,var(--surface),var(--surface-2));border:1px solid rgba(255,255,255,0.06);border-radius:14px;padding:16px;min-width:280px;max-width:90vw}
 </style>
</style>
</head>
<body>
<div class='wrap'>
  <div class='header'>
    <h2 class='title'>Disblock Monitor</h2>
    <div><span class='muted' id='updated'></span></div>
  </div>
  <div class='grid' id='stats'></div>
  <div class='card' style='margin-top:12px'>
    <h3 class='title'>Workers</h3>
    <div class='muted' id='workersSummary'></div>
    <div style='overflow:auto'>
      <div id='workersChips' class='workers-chips'></div>
    </div>
  </div>
  <div class='card' style='margin-top:12px'>
    <h3 class='title'>Active Actions</h3>
    <div class='toolbar'>
      <input id='search' class='input' placeholder='Search player, type, resource, worker...' />
      <button id='auth' class='btn'>Auth Token</button>
    </div>
    <div style='overflow:auto'>
      <table id='actionsTable' class='compact'>
        <thead><tr>
          <th data-sort='id'>ID</th>
          <th data-sort='player'>Player</th>
          <th data-sort='type'>Type</th>
          <th data-sort='resource'>Resource</th>
          <th data-sort='progress'>Progress</th>
          <th data-sort='worker'>Worker</th>
        </tr></thead>
        <tbody></tbody>
      </table>
    </div>
  </div>
  <div id='modal' class='modal'><div class='modal-card'><div id='modalBody'></div></div></div>
</div>
<script>
function fmtMs(ms){if(!ms||ms<0)return '0s';const s=Math.floor(ms/1000);const d=Math.floor(s/86400);const h=Math.floor((s%86400)/3600);const m=Math.floor((s%3600)/60);const r=[];if(d)r.push(d+'d');if(h)r.push(h+'h');if(m)r.push(m+'m');r.push((s%60)+'s');return r.join(' ')}
function fmtBytes(b){if(b<1024)return b+' B';const u=['KB','MB','GB','TB'];let i=-1;do{b/=1024;i++}while(b>=1024&&i<u.length-1);return b.toFixed(1)+' '+u[i]}
async function fetchJson(url){const headers={};const t=localStorage.getItem('x-internal-token');if(t)headers['x-internal-token']=t;const r=await fetch(url,{headers});return await r.json()}
function el(s){return document.querySelector(s)}
let uptimeBaseMs=0;let uptimeBaseTime=0;let lastCluster=null;let lastActions=[];let sortKey='id';let sortAsc=true;
function renderStats(debug,health,cluster,activeUserCount){lastCluster=cluster;const ws=debug.workerStats||{totalWorkers:0,healthyWorkers:0};const cards=[
 {k:'Status',v:(debug.status==='running'?'Running':'Unknown'),cls:'ok'},
 {k:'Uptime',v:fmtMs(debug.uptime*1000),id:'uptimeVal'},
 {k:'Memory',v:fmtBytes((cluster&&cluster.totalRss)||0),id:'memoryVal'},
 {k:'Workers',v:ws.healthyWorkers+'/'+ws.totalWorkers},
 {k:'Active users',v:String(activeUserCount||0),id:'activeUsersVal'},
 {k:'Errors',v:String((debug.serverHealth||{}).errorCount||0)}
]; el('#stats').innerHTML=cards.map(function(c){return '<div class="card kpi"><div class="muted">'+c.k+'</div><div class="val"'+(c.id?' id="'+c.id+'"':'')+'>'+c.v+'</div></div>'}).join(''); uptimeBaseMs=debug.uptime*1000; uptimeBaseTime=Date.now();}
function tickUptime(){if(!uptimeBaseTime)return;const elapsed=Date.now()-uptimeBaseTime;el('#uptimeVal').textContent=fmtMs(uptimeBaseMs+elapsed)}
function renderWorkers(ws){el('#workersSummary').textContent=ws.totalWorkers+' total, '+ws.healthyWorkers+' healthy';const chips=[];for(const id in ws.workers){const w=ws.workers[id];const healthy=w.healthy?'ok':'bad';const ready=w.status==='ready'?'ok':'warn';chips.push('<div class="chip" data-worker="'+id+'"><span class="dot '+healthy+'"></span><span class="wname">'+id+'</span><span class="badge '+ready+'">'+w.status+'</span></div>')} el('#workersChips').innerHTML=chips.join('')}
function applySearchAndSort(list){const q=(el('#search').value||'').toLowerCase().trim();let filtered=list.filter(function(a){const player=(a.player_name||a.user_id||'').toLowerCase();const type=(a.action_type||'').toLowerCase();const res=(a.resource_key||'').toLowerCase();const worker=(a.worker_id||'').toLowerCase();return !q||player.includes(q)||type.includes(q)||res.includes(q)||worker.includes(q)});filtered.sort(function(a,b){const m={id:(+a.id)-(+b.id),player:String(a.player_name||a.user_id).localeCompare(String(b.player_name||b.user_id)),type:String(a.action_type).localeCompare(String(b.action_type)),resource:String(a.resource_key||'').localeCompare(String(b.resource_key||'')),worker:String(a.worker_id||'').localeCompare(String(b.worker_id||'')),progress:((a.completed_cycles||0)/(a.total_amount||1))-((b.completed_cycles||0)/(b.total_amount||1))};const val=m[sortKey]||0;return sortAsc?val:-val});return filtered}
function renderActions(list){lastActions=list.slice(0);const rows=applySearchAndSort(lastActions).map(function(a){const pct=a.total_amount>0?Math.floor(((a.completed_cycles||0)/a.total_amount*100)):0;return '<tr><td>'+a.id+'</td><td>'+(a.player_name||a.user_id)+'</td><td>'+a.action_type+'</td><td>'+(a.resource_key||'')+'</td><td>'+(a.completed_cycles||0)+'/'+a.total_amount+' ('+pct+'%)</td><td>'+(a.worker_id||'')+'</td></tr>'});el('#actionsTable tbody').innerHTML=rows.join('')}
async function load(){const results=await Promise.all([fetchJson('/api/debug'),fetchJson('/health'),fetchJson('/worker-stats'),fetchJson('/api/active-actions'),fetchJson('/api/cluster-stats')]);const debug=results[0];const health=results[1];const ws=results[2];const acts=results[3];const cluster=results[4];const actList=(acts&&acts.data)||[];const uniqueUsers=new Set(actList.map(function(a){return a.user_id}));renderStats(debug,health,cluster,uniqueUsers.size);renderWorkers(ws);renderActions(actList);el('#updated').textContent='Updated '+new Date().toLocaleTimeString()} 
load();
setInterval(load,3000);
setInterval(tickUptime,1000);

document.getElementById('search').addEventListener('input',function(){renderActions(lastActions)})
document.querySelector('#actionsTable thead').addEventListener('click',function(e){const key=e.target.getAttribute('data-sort');if(!key)return;if(sortKey===key){sortAsc=!sortAsc}else{sortKey=key;sortAsc=true}renderActions(lastActions)})
document.getElementById('auth').addEventListener('click',function(){const cur=localStorage.getItem('x-internal-token')||'';const t=prompt('Enter dashboard token (x-internal-token):',cur);if(t!==null){if(t.trim()){localStorage.setItem('x-internal-token',t.trim())}else{localStorage.removeItem('x-internal-token')}}})
document.getElementById('workersChips').addEventListener('click',function(e){const chip=e.target.closest('.chip');if(!chip)return;const id=chip.getAttribute('data-worker');if(!id||!lastCluster)return;const mem=lastCluster.workers&&lastCluster.workers[id]||{};el('#modalBody').innerHTML='<h3 class="title" style="margin-bottom:8px">'+id+'</h3><div class="muted">Worker memory</div><div class="val" style="margin:6px 0">'+fmtBytes(mem.rss||0)+'</div><div class="muted">Heap</div><div>'+fmtBytes(mem.heapUsed||0)+' / '+fmtBytes(mem.heapTotal||0)+'</div><div style="margin-top:12px"><button class="btn" id="closeModal">Close</button></div>';el('#modal').classList.add('show');document.getElementById('closeModal').addEventListener('click',function(){el('#modal').classList.remove('show')})})
document.getElementById('modal').addEventListener('click',function(e){if(e.target.id==='modal'){el('#modal').classList.remove('show')}})
</script>
</body>
</html>`);
      });

      app.get("/", (req, res) => res.redirect("/dashboard"));
    });

    // Monitor server events
    resilientServer.on("serverError", (error) => {
      console.error("[Main Bot] Express server error:", error);
      Sentry.captureException(error, {
        extra: { source: "express_server" },
      });
    });

    resilientServer.on("maxRestartsExceeded", (error) => {
      console.error(
        "[Main Bot] Express server exceeded max restart attempts:",
        error
      );
      gracefulErrorHandler.emit("criticalError", {
        type: "express_max_restarts",
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    });

    resilientServer.on("unhealthy", (status) => {
      console.warn("[Main Bot] Express server is unhealthy:", status);
    });

    // Start the resilient server
    await resilientServer.start();
    if (!QUIET_BOOT)
      console.log("[Main Bot] Resilient Express server started successfully");

    // Register server cleanup with error handler
    gracefulErrorHandler.registerCleanupHandler(async () => {
      console.log("[Shutdown] Stopping Express server...");
      await resilientServer.stop();
      console.log("[Shutdown] Express server stopped");
    }, "express-server");
    // --- End Resilient Express Server Setup ---

    // --- Initialize Config Manager FIRST ---
    try {
      logSync("Initializing config manager...", true);
      await configManager.initialize();
      logSync("Config manager initialized successfully.", true);
    } catch (initError) {
      logSync(
        `CRITICAL ERROR: Failed to initialize configManager: ${initError}`,
        false
      );
      console.error("Full error:", initError);
      // @ts-ignore
      process.exit(1);
    }
    // --- End Initialize Config Manager ---

    // --- Initialize Collections Manager ---
    try {
      logSync("Initializing collections manager...", true);
      await collectionsManager.initialize();
      logSync("Collections manager initialized successfully.", true);
    } catch (initError) {
      logSync(
        `CRITICAL ERROR: Failed to initialize collectionsManager: ${initError}`,
        false
      );
      console.error("Full error:", initError);
      // @ts-ignore
      process.exit(1);
    }
    // --- End Initialize Collections Manager ---

    // --- Initialize Player Data Manager Field Mappings ---
    try {
      logSync("Initializing player data field mappings...", true);
      const { initializeFieldMappings } = require("./utils/playerDataManager");
      await initializeFieldMappings();
      logSync("Player data field mappings initialized successfully.", true);
    } catch (initError) {
      logSync(
        `WARNING: Failed to initialize player data field mappings: ${initError}`,
        false
      );
      console.error("Field mapping initialization error:", initError);
      // Don't exit - the system can still work with fallback mappings
    }
    // --- End Initialize Player Data Manager Field Mappings ---

    // --- Temporary Cleanup for Action ID 0 ---
    // REMOVED
    // --- End Temporary Cleanup ---

    await runMigrations();
    migrationsComplete = true;

    // --- Load Commands ---
    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
      ],
    });
    client.commands = new Collection();
    const commandsToDeploy = []; // Array to hold command data for deployment

    try {
      // @ts-ignore
      const commandFiles = await fs.readdir("./commands");
      commandFiles
        .filter((file) => file.endsWith(".js"))
        .forEach((file) => {
          try {
            const command = require(`./commands/${file}`);
            if (command.data && command.data.name) {
              client.commands.set(command.data.name, command);
              commandsToDeploy.push(command.data.toJSON()); // Add command data for deployment
              logSync(`Loaded command: ${command.data.name}`, true);
              // Specific check for enchant command (new!)
              if (command.data.name === "enchant") {
                logSync("  -> Successfully loaded enchant command", true);
              }
            } else {
              logSync(
                `Skipping command file ${file} due to missing data or name.`,
                false
              );
            }
          } catch (requireError) {
            logSync(
              `Error loading command file ${file}: ${requireError}`,
              false
            );
          }
        });
    } catch (readdirError) {
      logSync(`Error reading commands directory: ${readdirError}`, false);
      // @ts-ignore
      process.exit(1);
    }
    logSync(`Total commands loaded: ${client.commands.size}`, true);
    // --- End Load Commands ---

    // --- Deploy/Update Slash Commands ---
    if (!QUIET_BOOT)
      logSync("Registering/updating application (/) commands...");
    const rest = new REST({ version: "10" }).setToken(config.token);

    // Start command registration in the background
    // This allows the bot to continue logging in without waiting for command registration to complete
    (async () => {
      try {
        // Check if clientId exists in config
        if (!config.clientId) {
          throw new Error("clientId is missing in config.json");
        }

        // Register global commands (these will be available in all servers the bot is in)
        // Note: Global commands can take up to an hour to propagate to all Discord servers
        if (!QUIET_BOOT)
          logSync("Registering global commands (this may take a moment)...");
        const data = await rest.put(
          // @ts-ignore
          Routes.applicationCommands(config.clientId),
          { body: commandsToDeploy }
        );
        if (!QUIET_BOOT)
          logSync(`Successfully refreshed ${data.length} global (/) commands.`);
      } catch (deployError) {
        logSync(
          `Failed to register application commands: ${deployError}`,
          false
        );
        console.error("Full error:", deployError);
      }
    })();

    if (!QUIET_BOOT) {
      logSync(
        "Command registration started in background. Continuing with bot login..."
      );
    }
    // --- End Deploy/Update Slash Commands ---

    // --- Bot Event Listeners ---
    client.on("ready", async () => {
      logSync("Bot is now online!");
      logSync(`Logged in as ${client.user.tag}`);

      // --- Initialize Performance Optimizations ---
      try {
        const {
          initializeMetadataCache,
        } = require("./utils/playerDataManager");
        await initializeMetadataCache();
        logSync("Performance optimizations initialized successfully");
      } catch (error) {
        console.warn("Failed to initialize performance optimizations:", error);
      }

      // --- Auto-post Setup Embeds ---
      try {
        const { ensureSetupEmbedsExist } = require("./utils/autoSetupEmbeds");
        await ensureSetupEmbedsExist(client);
      } catch (error) {
        console.warn("Failed to check/post setup embeds:", error);
      }

      // --- Validate All Player Channels ---
      try {
        const {
          validateAllPlayerChannels,
        } = require("./utils/channelValidator");
        await validateAllPlayerChannels(client);
      } catch (error) {
        console.warn("Failed to validate player channels:", error);
      }

      // --- Resume Actions AFTER client is ready ---
      await resumeAllPendingActions(client);

      // --- Background timers (simpler than BackgroundTaskManager) ---
      const backgroundTimers = {};

      try {
        await applyHealthRegenToAllPlayers();
      } catch (e) {
        console.error("[Background] healthRegen initial run failed:", e);
      }

      backgroundTimers.healthRegen = safeSetInterval(() => {
        applyHealthRegenToAllPlayers().catch((e) =>
          console.error("[Background] healthRegen failed:", e)
        );
      }, 2000);

      backgroundTimers.bitsCollection = safeSetInterval(() => {
        processAllPlayersBitsCollection().catch((e) =>
          console.error("[Background] bitsCollection failed:", e)
        );
      }, 10000);

      backgroundTimers.minionFullness = safeSetInterval(() => {
        checkAndNotifyMinionFullness(client).catch((e) =>
          console.error("[Background] minionFullness failed:", e)
        );
      }, 10000);

      backgroundTimers.visitorsFullness = safeSetInterval(() => {
        checkAndNotifyVisitorsFullness(client).catch((e) =>
          console.error("[Background] visitorsFullness failed:", e)
        );
      }, 10000);

      backgroundTimers.visitorSpawning = safeSetInterval(() => {
        processVisitorSpawning().catch((e) =>
          console.error("[Background] visitorSpawning failed:", e)
        );
      }, 10000);

      backgroundTimers.bakerNotifications = safeSetInterval(() => {
        checkAndNotifyBakerCake(client).catch((e) =>
          console.error("[Background] bakerNotifications failed:", e)
        );
      }, 30000);

      backgroundTimers.petUpgrades = safeSetInterval(() => {
        checkAndNotifyPetUpgrades(client).catch((e) =>
          console.error("[Background] petUpgrades failed:", e)
        );
      }, 60000);

      backgroundTimers.coinGeneration = safeSetInterval(() => {
        applyCoinGenerationToAllPlayers().catch((e) =>
          console.error("[Background] coinGeneration failed:", e)
        );
      }, 60000);

      backgroundTimers.potionCleanup = safeSetInterval(() => {
        cleanupExpiredPotionEffects().catch((e) =>
          console.error("[Background] potionCleanup failed:", e)
        );
      }, 300000);

      backgroundTimers.stripePayments = safeSetInterval(() => {
        checkAndProcessCompletedPayments(client).catch((e) =>
          console.error("[Background] stripePayments failed:", e)
        );
      }, 6000);

      if (!QUIET_BOOT) console.log("[Background] Timers initialized");

      gracefulErrorHandler.registerCleanupHandler(() => {
        console.log("[Shutdown] Stopping background timers...");
        for (const [name, id] of Object.entries(backgroundTimers)) {
          if (id != null) safeClearInterval(id);
        }
        console.log("[Shutdown] Background timers stopped");
      }, "background-timers");

      // Initialize coin generation for existing players (one-time fix)
      initializeAllPlayersCoinGeneration()
        .then(() => {
          // Silently initialize coin generation
        })
        .catch((error) => {
          console.error("Error during coin generation initialization:", error);
        });

      logSync(
        "[MinionSystem] Minion fullness check system initialized (10-second intervals)."
      );
      logSync("[PetSystem] Pet upgrade notification system initialized.");
      logSync(
        "[BakerSystem] Baker cake notification system initialized (30-second intervals)."
      );

      // --- Initialize Database Backup System ---
      try {
        startBackupScheduler();
        if (!QUIET_BOOT)
          logSync(
            "[BackupSystem] Database backup scheduler initialized (hourly backups)."
          );
      } catch (backupError) {
        console.error(
          "[BackupSystem] Failed to initialize backup scheduler:",
          backupError
        );
      }

      // --- Notify channels that server is back up (Serialized File Operations) ---
      // Delay file operations to avoid concurrent fs operations during startup
      setTimeout(async () => {
        try {
          // @ts-ignore
          const notifyFile = path.join(__dirname, "notifiedChannels.json");

          // Check if file exists using synchronous operation to avoid libuv conflicts
          if (require("fs").existsSync(notifyFile)) {
            logSync(
              "[RestartNotify-Ready] notifiedChannels.json found, processing..."
            );
            try {
              // @ts-ignore
              const data = await fs.readFile(notifyFile, "utf8");
              const channelIds = JSON.parse(data);
              if (Array.isArray(channelIds) && channelIds.length > 0) {
                const upEmbed = new EmbedBuilder()
                  .setTitle("Game Announcement")
                  .setDescription("**Server is back up!**")
                  .setColor(EMBED_COLORS.LIGHT_GREEN) // Green
                  .setFooter({ text: "Thank you for your patience." });
                let notifiedCount = 0;
                for (const channelId of channelIds) {
                  try {
                    const channel = await client.channels.fetch(channelId);
                    if (channel && channel.isTextBased()) {
                      await channel.send({ embeds: [upEmbed] });
                      notifiedCount++;
                    }
                  } catch (e) {
                    console.error(
                      `[RestartNotify-Ready] Failed to send to channel ${channelId}:`,
                      e
                    );
                  }
                }
                logSync(
                  `[RestartNotify-Ready] Attempted to send server back notifications to ${channelIds.length} channels. Successfully sent to ${notifiedCount}.`
                );
              } else {
                logSync(
                  "[RestartNotify-Ready] notifiedChannels.json was empty or did not contain a valid array of channel IDs."
                );
              }
              // @ts-ignore
              await fs.unlink(notifyFile);
              logSync("[RestartNotify-Ready] Deleted notifiedChannels.json.");
            } catch (e) {
              console.error(
                "[RestartNotify-Ready] Error processing notifiedChannels.json:",
                e
              );
            }
          } else {
            // File doesn't exist, normal startup
            logSync(
              "[RestartNotify-Ready] No notifiedChannels.json found, normal startup."
            );
          }
        } catch (error) {
          console.error(
            "[RestartNotify-Ready] Error in notification file processing:",
            error
          );
        }
      }, 5000); // Delay file operations by 5 seconds to avoid startup conflicts
    });

    client.on("interactionCreate", async (interaction) => {
      // Removed verbose interaction logging for routine events

      // Handle Autocomplete First
      if (interaction.isAutocomplete()) {
        const command = client.commands.get(interaction.commandName);
        if (!command) {
          console.error(
            `[InteractionCreate] Autocomplete ERROR: Command "${interaction.commandName}" not found in client.commands collection.`
          );
          return;
        }
        if (command.autocomplete) {
          try {
            await command.autocomplete(interaction);
          } catch (err) {
            console.error(
              `[InteractionCreate] Autocomplete ERROR during execution for "${interaction.commandName}":`,
              err
            );
          }
        }
        return; // Stop further processing for autocomplete
      }

      // Handle Buttons
      if (interaction.isButton()) {
        // Handle visitors menu interactions
        const visitorsHandler = client.commands.get("visitors");
        if (visitorsHandler && visitorsHandler.handleInteraction) {
          const cid = interaction.customId;
          if (
            cid === "refresh_visitors" ||
            cid === "close_visitors" ||
            cid === "select_visitor" ||
            cid.startsWith("accept_offer_") ||
            cid.startsWith("decline_offer_") ||
            cid === "back_to_visitors"
          ) {
            await visitorsHandler.handleInteraction(interaction);
            return;
          }
        }
        // +++ Unified Stop Button Handler +++
        if (interaction.customId.startsWith("unified_stop:")) {
          const {
            handleUnifiedStopButton,
          } = require("./utils/unifiedStopButtons");
          const stopButtonHandled = await handleUnifiedStopButton(interaction);
          if (stopButtonHandled) return;
        }

        // +++ Legacy Stop Button Handler (backward compatibility) +++
        if (
          interaction.customId.startsWith("stop_resumed_action:") ||
          interaction.customId.startsWith("stop_action:")
        ) {
          // Just use the existing handler - no migration needed
          const stopButtonHandled =
            await processStopButtonInteraction(interaction);
          if (stopButtonHandled) return;
        }
        // --- End stop button handlers ---        // Handle fishing bait swap/select buttons via persistent handler in fish.js
        if (
          interaction.customId.startsWith("bait_swap:") ||
          interaction.customId.startsWith("bait_select:")
        ) {
          const fishCommand = client.commands.get("fish");
          if (fishCommand && fishCommand.handleButton) {
            const handled = await fishCommand.handleButton(interaction);
            if (handled) return;
          }
        }

        // Handle Elizabeth Community Shop buttons
        if (
          interaction.customId.startsWith("elizabeth_") ||
          interaction.customId.startsWith("gem_gift_")
        ) {
          const {
            handleElizabethButtonInteraction,
          } = require("./commands/talk");
          const elizabethHandled =
            await handleElizabethButtonInteraction(interaction);
          if (elizabethHandled) return;
        }

        // Handle admin menu buttons
        if (interaction.customId.startsWith("adminmenu_")) {
          const {
            handleAdminMenuInteraction,
          } = require("./commands/adminmenu");
          const adminMenuHandled =
            await handleAdminMenuInteraction(interaction);
          if (adminMenuHandled) return;
        }

        const customId = interaction.customId;

        // Handle setup character creation button
        if (customId === "create_character_setup") {
          const {
            handleSetupCharacterCreation,
          } = require("./utils/setupCharacterCreation");
          const handled = await handleSetupCharacterCreation(
            interaction,
            client
          );
          if (handled) return;
        }

        // --- Wardrobe Management Buttons ---
        if (
          customId.startsWith("wardrobe_equip_") ||
          customId.startsWith("wardrobe_config_") ||
          customId === "wardrobe_configure" ||
          customId === "wardrobe_back" ||
          customId === "wardrobe_stop_configure" ||
          customId.startsWith("wardrobe_rename_") ||
          customId.startsWith("wardrobe_paste_") ||
          customId.startsWith("wardrobe_back_") ||
          customId.startsWith("wardrobe_clear_") ||
          customId.startsWith("wardrobe_prev_") ||
          customId.startsWith("wardrobe_next_") ||
          customId.startsWith("wardrobe_prevpage_") ||
          customId.startsWith("wardrobe_nextpage_") ||
          customId.startsWith("wardrobe_configure_prevpage_") ||
          customId.startsWith("wardrobe_configure_nextpage_")
        ) {
          const wardrobeCommand = client.commands.get("wardrobe");
          if (wardrobeCommand && wardrobeCommand.handleButton) {
            await wardrobeCommand.handleButton(interaction);
            return;
          }
        }

        // --- Settings Management Buttons ---
        if (
          customId.startsWith("toggle_action_pings_") ||
          customId.startsWith("toggle_market_pings_") ||
          customId.startsWith("toggle_market_mode_") ||
          customId.startsWith("toggle_market_notifications_") ||
          customId.startsWith("toggle_visitors_full_ping_") ||
          customId.startsWith("toggle_action_runtime_info_")
        ) {
          const settingsCommand = client.commands.get("settings");
          if (settingsCommand && settingsCommand.handleButton) {
            await settingsCommand.handleButton(interaction);
            return;
          }
        }

        // --- Collections Management Buttons ---
        if (
          customId.startsWith("collections_back_") ||
          customId.startsWith("collections_page_")
        ) {
          const collectionsCommand = client.commands.get("collections");
          if (collectionsCommand && collectionsCommand.handleButton) {
            await collectionsCommand.handleButton(interaction);
            return;
          }
        }

        // --- Desk Management Buttons ---
        if (
          customId === "desk_back" ||
          customId === "coming_soon_back" ||
          customId === "skymart_error_back" ||
          customId === "visitor_npc_list" ||
          customId === "visitor_analytics_back" ||
          customId.startsWith("skymart_buy:") ||
          customId.startsWith("skymart_back:") ||
          customId.startsWith("crop_upgrades_select:") ||
          customId.startsWith("crop_upgrades_back:") ||
          customId.startsWith("crop_upgrades_confirm1:") ||
          customId.startsWith("crop_upgrades_confirmmax:")
        ) {
          const deskCommand = client.commands.get("desk");
          if (deskCommand && deskCommand.handleButton) {
            await deskCommand.handleButton(interaction);
            return;
          }
        }

        // --- Enchant Management Buttons ---
        if (
          customId.startsWith("ench_back:") ||
          customId.startsWith("ench_level:") ||
          customId.startsWith("ench_apply:") ||
          customId.startsWith("ench_max:") ||
          customId.startsWith("ench_custom:") ||
          customId === "ench_close" ||
          customId.startsWith("ench_remove_menu:") ||
          customId.startsWith("ench_remove:") ||
          customId.startsWith("ench_add:")
        ) {
          const enchantCommand = client.commands.get("enchant");
          if (enchantCommand && enchantCommand.handleButton) {
            await enchantCommand.handleButton(interaction);
            return;
          }
        }

        // --- Slayer Management Buttons ---
        if (customId === "slayers_back") {
          const statsCommand = client.commands.get("stats");
          if (statsCommand && statsCommand.handleSlayerBackButton) {
            await statsCommand.handleSlayerBackButton(interaction);
            return;
          }
        }

        // Removed debug logs
        // --- Basic User Verification (Common for most buttons) ---
        if (
          customId.startsWith("stop_action:") ||
          customId.startsWith("repeat_skill:") ||
          customId.startsWith("trade_accept_") ||
          customId.startsWith("trade_decline_") ||
          customId.startsWith("trade_toggle:")
        ) {
          // For stop_action, repeat_skill, trade_accept, and trade_decline, the primary check is that the button interaction itself is valid.
          // The user clicking is the one performing the action. Specific ownership/permissions
          // are (or should be) handled within the respective handlers (e.g., stop_action checks action owner, trade buttons check target user).
        } else {
          // For other buttons (typically menus on a message that might be seen by multiple users)
          if (!interaction.message || !interaction.message.interaction) {
            return; // Silently return if no original interaction context for general buttons
          }
          const originalInteractingUserId =
            interaction.message.interaction.user.id;
          if (interaction.user.id !== originalInteractingUserId) {
            return interaction.reply({
              content: "You cannot interact with someone else's menu.",
              flags: [MessageFlags.Ephemeral],
            });
          }
        }
        // --- End Basic User Verification ---

        // Placeholder for other handlers like 'visit_refresh' if you add them
        // else if (interaction.customId === 'visit_refresh') { ... }

        // --- Leaderboard Pagination ---
        if (customId.startsWith("leaderboard_")) {
          const command = client.commands.get("leaderboards");
          if (command && command.handlePagination) {
            return command.handlePagination(interaction);
          } else {
            console.error(
              `[InteractionCreate] Leaderboard pagination button ${interaction.customId} received, but 'leaderboards' command or its 'handlePagination' handler is missing.`
            );
            if (!interaction.replied && !interaction.deferred) {
              return interaction.reply({
                content:
                  "Error processing this pagination action. Command handler not found.",
                flags: [MessageFlags.Ephemeral],
              });
            }
          }
        }

        // --- Minion Tier Navigation for /recipe and /wiki ---
        if (
          ["minion_left", "minion_right", "minion_tier_modal"].includes(
            interaction.customId
          )
        ) {
          const command = client.commands.get("recipe");
          if (command && command.handleMinionTierNavigation) {
            return command.handleMinionTierNavigation(interaction);
          } else {
            console.error(
              `[InteractionCreate] Minion navigation button ${interaction.customId} received, but 'recipe' command or its 'handleMinionTierNavigation' handler is missing.`
            );
            if (!interaction.replied && !interaction.deferred) {
              return interaction.reply({
                content:
                  "Error processing this navigation action. Command handler not found.",
                flags: [MessageFlags.Ephemeral],
              });
            }
          }
        }
        // REMOVED modal submit check from here
        // ... other button-specific handlers ...

        // --- Pet Release Buttons ---
        if (
          customId.startsWith("pet_release_confirm:") ||
          customId.startsWith("pet_release_cancel:")
        ) {
          const petCommand = require("./commands/pet.js");
          await petCommand.handlePetReleaseComponent(interaction);
          return;
        }

        // +++ Add check for minion upgrade buttons +++
        if (customId.startsWith("minion_upgrade_")) {
          logSync(
            `[Button Handler] Ignoring button ${customId} - expected to be handled by minions command collector.`,
            true
          );
          return; // <<< Let the collector in minions.js handle this
        }

        // +++ End added check +++

        // --- Storage Management Buttons ---
        if (
          customId.startsWith("storage_manage_") ||
          customId.startsWith("storage_sell_") ||
          customId.startsWith("storage_destroy_") ||
          customId.startsWith("storage_cancel_")
        ) {
          logSync(
            `[Button Handler] Ignoring button ${customId} - expected to be handled by storage command collector.`,
            true
          );
          return; // Let the collector in storage.js handle this
        }

        // --- Repeat Skill Button ---
        if (customId.startsWith("repeat_skill:")) {
          // Acknowledge the button interaction immediately to prevent "This interaction failed"
          await interaction.deferUpdate();

          // Format (new): repeat_skill:<ownerId>:<skillName>:<resourceKey>:<amount>:<wasMax>
          // Legacy format: repeat_skill:<skillName>:<resourceKey>:<amount>
          const parts = customId.split(":");
          if (parts.length >= 4) {
            const hasOwner = parts.length >= 5;
            const hasMaxFlag = parts.length >= 6;
            const ownerId = hasOwner ? parts[1] : null;
            const skillName = hasOwner ? parts[2] : parts[1];
            const resourceKey = hasOwner ? parts[3] : parts[2] || null;
            let amount = hasOwner
              ? parts[4]
                ? parseInt(parts[4], 10)
                : 1
              : parts[3]
                ? parseInt(parts[3], 10)
                : 1;
            const wasMax = hasMaxFlag ? parts[5] === "max" : false;
            const userId = interaction.user.id;

            // If the original input was "max", recalculate the maximum amount based on current skill level
            if (wasMax) {
              try {
                // flush the db write queue to guarantee latest player data
                const { drainQueue } = require("./utils/writeQueue");
                await drainQueue();
                const { getPlayerData } = require("./utils/playerDataManager");
                const {
                  calculateMaxActionAmount,
                } = require("./utils/skillLimits");
                const {
                  getPlayerSkillLevel,
                } = require("./utils/playerDataManager");
                // Get the latest character data to ensure we have the current skill level
                const character = await getPlayerData(userId);
                if (character) {
                  // Get the current skill level
                  const skillLevel =
                    getPlayerSkillLevel(character, skillName) || 0;
                  // Recalculate the maximum amount based on the current skill level
                  const maxAllowedAmount = calculateMaxActionAmount(skillLevel);
                  // Update the amount to use the recalculated maximum
                  amount = maxAllowedAmount;
                  console.log(
                    `[RepeatButton] Recalculated max amount for ${skillName}: ${amount}`
                  );
                }
              } catch (error) {
                console.error(
                  "[RepeatButton] Error recalculating max amount:",
                  error
                );
                // Continue with the original amount if there's an error
              }
            }

            // Make sure only the message owner can use the button
            if (ownerId && ownerId !== userId) {
              try {
                await interaction.followUp({
                  content: "You cannot use another player's Repeat button.",
                  ephemeral: true,
                });
              } catch (err) {
                console.error(
                  "[RepeatButton] Failed to send ownership warning:",
                  err
                );
              }
              return;
            }

            // Region lock validation before setting activity (skip for alchemy)
            if (skillName !== "alchemy") {
              try {
                const { drainQueue } = require("./utils/writeQueue");
                await drainQueue();
                const { getPlayerData } = require("./utils/playerDataManager");
                const {
                  checkRegionPermissions,
                } = require("./utils/regionUtils");
                const characterForRegion = await getPlayerData(userId);
                if (characterForRegion) {
                  const resourceNameSingular = "resource";
                  const regionCheck = checkRegionPermissions(
                    characterForRegion,
                    skillName,
                    null,
                    resourceKey,
                    resourceNameSingular
                  );
                  if (!regionCheck.allowed) {
                    try {
                      await interaction.channel.send({
                        embeds: [regionCheck.embed],
                      });
                    } catch (sendErr) {
                      console.warn(
                        "[RepeatButton] Failed to send region lock message:",
                        sendErr?.message || sendErr
                      );
                    }
                    return;
                  }
                }
              } catch (regionErr) {
                console.error(
                  "[RepeatButton] Region validation failed:",
                  regionErr?.message || regionErr
                );
              }
            }

            // Import active action checking functions
            const {
              getActiveActionTypeForUser,
              trySetActivity,
              clearActivity,
              warnUserBusy,
            } = require("./utils/activityManager");

            // ATOMIC OPERATION: Try to set activity first to prevent race conditions
            // This prevents multiple repeat button clicks from creating multiple actions

            // Check current activity before trying to set
            const preCheckActivity = await getActiveActionTypeForUser(userId);

            const activitySet = await trySetActivity(userId, skillName);

            if (!activitySet) {
              // User is already busy with another activity - use our new warning system
              const currentActivity = await getActiveActionTypeForUser(userId);

              if (currentActivity) {
                // Create a fake interaction object for warnUserBusy
                const fakeInteraction = {
                  reply: async (options) => {
                    await interaction.channel.send({
                      content: `<@${userId}>`,
                      embeds: options.embeds,
                    });
                  },
                  commandName:
                    skillName === "mining"
                      ? "mine"
                      : skillName === "farming"
                        ? "farming"
                        : skillName === "alchemy"
                          ? "alchemy"
                          : skillName === "fishing"
                            ? "fishing"
                            : skillName === "foraging"
                              ? "foraging"
                              : skillName,
                };

                try {
                  await warnUserBusy(fakeInteraction, currentActivity);
                } catch (err) {
                  console.error(
                    "[RepeatButton] Failed to send activity warning:",
                    err
                  );
                }
              } else {
                try {
                  await interaction.channel.send({
                    content: `<@${userId}> You are already busy! If you are stuck, try /unstuck or contact an admin.`,
                  });
                } catch (err) {
                  console.error(
                    "[RepeatButton] Failed to send busy warning:",
                    err
                  );
                }
              }
              return;
            }

            // Activity successfully set - proceed with the action

            // REMOVED BUGGY REDUNDANT CHECKS:
            // Both "double-check" and "stuck check" logic were wrong - if trySetActivity returns true,
            // then any subsequent check will see the activity we just set, causing false warnings!

            const skillToCommand = {
              farming: "farm",
              foraging: "forage",
              mining: "mine", // Map the skill name 'mining' to the command name 'mine'
              fishing: "fish",
              combat: "combat",
              alchemy: "brew", // Discord command name is still 'brew', but action type is 'alchemy'
            };
            const commandName = skillToCommand[skillName] || skillName;
            const command = client.commands.get(commandName);

            if (!command) {
              console.error(`[RepeatButton] Command not found: ${commandName}`);
              try {
                await interaction.channel.send({
                  content: `<@${userId}> Command not found: ${commandName}`,
                });
              } catch (err) {
                console.error(
                  "[RepeatButton] Failed to send command not found error:",
                  err
                );
              }
              return;
            }

            // Disable the repeat button and stop button
            const messageToEdit = interaction.message;
            if (messageToEdit?.components?.length > 0) {
              const newComponents = [];

              for (const component of messageToEdit.components) {
                // Preserve any non-ActionRow components as-is (future-proofing)
                if (component.type !== ComponentType.ActionRow) {
                  newComponents.push(component);
                }
                // Only modify ActionRows (button/select rows)
                else if (component.type === ComponentType.ActionRow) {
                  const newRowBuilder = new ActionRowBuilder();
                  for (const subComponent of component.components) {
                    if (subComponent.type === ComponentType.Button) {
                      const buttonBuilder = ButtonBuilder.from(subComponent);
                      // Disable repeat button and any stop buttons
                      if (
                        subComponent.customId === customId ||
                        subComponent.customId?.startsWith("stop_action:") ||
                        subComponent.customId?.startsWith(
                          "stop_resumed_action:"
                        )
                      ) {
                        buttonBuilder.setDisabled(true);
                      }
                      newRowBuilder.addComponents(buttonBuilder);
                    } else {
                      // Preserve non-button components in the row (e.g., select menus)
                      newRowBuilder.addComponents(subComponent);
                    }
                  }
                  if (newRowBuilder.components.length > 0) {
                    newComponents.push(newRowBuilder);
                  }
                }
              }

              try {
                await messageToEdit.edit({ components: newComponents });
              } catch (editErr) {
                console.warn(
                  "[RepeatButton] Failed to disable button:",
                  editErr
                );
              }
            }

            // Map skill names to their parameter names
            const skillParamMap = {
              fishing: null,
              farming: "crop",
              mining: "resource",
              foraging: "resource",
              combat: "mob",
              alchemy: "potion",
            };

            const paramName = skillParamMap[skillName];

            // Create a persistent interaction that doesn't rely on interaction tokens
            let replyMessage = null;

            const persistentInteraction = {
              user: interaction.user,
              member: interaction.member,
              guild: interaction.guild,
              channel: interaction.channel,
              channelId: interaction.channel.id,
              commandName: commandName,
              deferred: false,
              replied: false,

              // Flag to indicate this is from a repeat button - activity is already set
              isFromRepeatButton: true,

              // Type checking methods

              isChatInputCommand: () => true,
              isButton: () => false,

              // Options object with command parameters
              options: {
                getString: (name) => {
                  if (paramName && name === paramName) return resourceKey;
                  if (name === "amount") {
                    // If this was originally a "max" command, return "max" so the command
                    // properly detects it and sets wasMax=true for subsequent repeat buttons
                    if (wasMax) return "max";
                    return amount > 1 ? amount.toString() : null;
                  }
                  return null;
                },
                getInteger: (name) => {
                  if (name === "amount") {
                    // For integer requests, always return the calculated amount
                    return amount > 1 ? amount : null;
                  }
                  return null;
                },
                getBoolean: () => null,
                getUser: () => null,
                getMember: () => null,
                getChannel: () => null,
                getRole: () => null,
                getMentionable: () => null,
                getNumber: () => null,
                getAttachment: () => null,
                getFocused: () => null,
                getSubcommand: () => null,
                getSubcommandGroup: () => null,
              },

              // Reply methods that work without interaction tokens but handle message editing properly
              reply: async (content) => {
                if (!replyMessage) {
                  if (typeof content === "string") {
                    replyMessage = await interaction.channel.send({ content });
                  } else {
                    replyMessage = await interaction.channel.send(content);
                  }
                  persistentInteraction.replied = true;
                  return replyMessage;
                } else {
                  // If we already have a reply message, edit it instead
                  if (typeof content === "string") {
                    return await replyMessage.edit({ content });
                  } else {
                    return await replyMessage.edit(content);
                  }
                }
              },

              editReply: async (content) => {
                if (!replyMessage) {
                  // If no reply message exists yet, create one
                  if (typeof content === "string") {
                    replyMessage = await interaction.channel.send({ content });
                  } else {
                    replyMessage = await interaction.channel.send(content);
                  }
                  persistentInteraction.replied = true;
                  return replyMessage;
                } else {
                  // Edit the existing reply message
                  if (typeof content === "string") {
                    return await replyMessage.edit({ content });
                  } else {
                    return await replyMessage.edit(content);
                  }
                }
              },

              followUp: async (content) => {
                // Follow-ups should always create new messages
                if (typeof content === "string") {
                  return await interaction.channel.send({ content });
                }

                // Handle fetchReply option for persistent message objects
                if (content && content.fetchReply) {
                  const { fetchReply, ...messageContent } = content;
                  const sentMessage =
                    await interaction.channel.send(messageContent);
                  return fetchReply ? sentMessage : undefined;
                }

                return await interaction.channel.send(content);
              },

              deferReply: async () => {
                // For persistent interactions, we'll just mark as deferred
                persistentInteraction.deferred = true;
                return Promise.resolve();
              },

              fetchReply: async () => {
                // Return the current reply message if it exists
                if (replyMessage) {
                  return replyMessage;
                }
                // If no reply message exists, this shouldn't be called
                throw new Error("No reply message exists to fetch");
              },
            };

            console.log(
              `[RepeatButton] DEBUG: Created persistentInteraction for ${commandName}`
            );
            console.log(`[RepeatButton] DEBUG: - skillName: ${skillName}`);
            console.log(`[RepeatButton] DEBUG: - resourceKey: ${resourceKey}`);
            console.log(`[RepeatButton] DEBUG: - amount: ${amount}`);
            console.log(`[RepeatButton] DEBUG: - paramName: ${paramName}`);
            console.log(`[RepeatButton] DEBUG: - wasMax: ${wasMax}`);
            console.log(
              `[RepeatButton] DEBUG: - getString('${paramName}'): ${persistentInteraction.options.getString(paramName)}`
            );
            console.log(
              `[RepeatButton] DEBUG: - getString('amount'): ${persistentInteraction.options.getString("amount")}`
            );
            console.log(
              `[RepeatButton] DEBUG: - getInteger('amount'): ${persistentInteraction.options.getInteger("amount")}`
            );

            try {
              // Execute the command with the persistent interaction
              console.log(
                `[RepeatButton] DEBUG: About to execute command ${commandName} with resourceKey: ${resourceKey}, amount: ${amount}`
              );
              await command.execute(persistentInteraction);
              console.log(
                `[RepeatButton] DEBUG: Command ${commandName} execution completed successfully`
              );
              logSync(
                `[RepeatButton] Successfully repeated ${commandName} ${resourceKey} ${amount}`,
                true
              );
            } catch (executeErr) {
              console.error(
                `[RepeatButton] Error executing ${commandName}:`,
                executeErr
              );
              console.error(`[RepeatButton] Error stack:`, executeErr.stack);
              // Clear the activity we set since the command failed
              clearActivity(userId);
              try {
                await interaction.channel.send({
                  content: `<@${userId}> Error repeating command: ${executeErr.message}`,
                });
              } catch (sendErr) {
                console.error(
                  "[RepeatButton] Failed to send error message:",
                  sendErr
                );
              }
            }
          }
          return;
        }

        // --- Handle Pet Management Buttons (equip, unequip, rename, release) ---
        // Check if the customId matches the pattern for pet management buttons
        const petManageMatch = customId.match(
          /^pet_(equip|unequip|rename|release):(.+)$/
        );

        if (petManageMatch) {
          const petCommand = require("./commands/pet.js");
          await petCommand.handlePetManagementButton(interaction);
          return; // Stop processing, handled by pet command
        }

        // Reforge command interactions are now handled by the talk command with blacksmith NPC

        // --- Handle Blacksmith Interactions ---
        if (customId.startsWith("blacksmith_")) {
          // These are handled by the talk command's collectors
          return;
        }

        // Handle reforge rarity buttons and back button
        if (
          customId.startsWith("reforge_rarity_") ||
          customId === "reforge_back"
        ) {
          const wikiCommand = client.commands.get("wiki");
          if (wikiCommand && wikiCommand.handleReforgeSelection) {
            await wikiCommand.handleReforgeSelection(interaction);
            return;
          }
        }

        // Handle any remaining buttons (should be rare if routing is comprehensive)
      }

      // Handle Select Menus
      if (interaction.isStringSelectMenu()) {
        // --- Visitor selection menu ---
        if (interaction.customId === "select_visitor") {
          const visitorsHandler = client.commands.get("visitors");
          if (visitorsHandler && visitorsHandler.handleInteraction) {
            await visitorsHandler.handleInteraction(interaction);
          }
          return;
        }
        // +++ Handle fishing bait selection +++
        if (interaction.customId.startsWith("bait_select:")) {
          const {
            handleBaitSelectionInteraction,
          } = require("./utils/fishingButtons");
          const baitSelectionHandled =
            await handleBaitSelectionInteraction(interaction);
          if (baitSelectionHandled) return;
        }

        // +++ Handle admin menu select menus +++
        if (interaction.customId.startsWith("adminmenu_toggle_")) {
          const {
            handleAdminMenuInteraction,
          } = require("./commands/adminmenu");
          const adminMenuHandled =
            await handleAdminMenuInteraction(interaction);
          if (adminMenuHandled) return;
        }

        // +++ Handle specific select menu interactions +++
        if (interaction.customId.startsWith("storage_item_select_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by storage command collector.`,
            true
          );
          return; // Let the collector in storage.js handle this
        }
        if (interaction.customId.startsWith("storage_quick_sell_menu_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by storage command collector.`,
            true
          );
          return; // Let the collector in storage.js handle this
        }
        if (interaction.customId.startsWith("minion_storage_select_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by minions command collector.`,
            true
          );
          return; // Let the collector in minions.js handle this
        }

        // Add check for pet feed select menu
        if (interaction.customId.startsWith("pet_feed_select_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by pet command collector.`,
            true
          );
          return; // Let the collector in pet.js handle this
        }

        // Add check for pet manage select menu
        if (interaction.customId.startsWith("pet_manage_select_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by pet command collector.`,
            true
          );
          return; // Let the collector in pet.js handle this
        }

        // Add check for pet upgrade select menu
        if (interaction.customId.startsWith("select_pet_upgrade_")) {
          // Update the existing Kat conversation message instead of creating an ephemeral reply
          await interaction.deferUpdate();
          const talkCommand = require("./commands/talk.js"); // Require inside to avoid circular dep if talk requires bot
          await talkCommand.handlePetUpgradeSelection(interaction);
          return; // Stop processing, handled by talk command
        }

        // Add check for trade select menus
        if (
          interaction.customId.startsWith("item_select_") ||
          interaction.customId.startsWith("gear_select_")
        ) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by trade command collector.`,
            true
          );
          return; // Let the collector in trade_new.js handle this
        }

        // Add check for bee pet rarity select menu
        if (interaction.customId.startsWith("buy_bee_pet_rarity_menu_")) {
          console.log(
            `[Bot.js Debug] Bee pet rarity menu interaction detected: ${interaction.customId}, user: ${interaction.user.id}`
          );
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by talk command collector.`,
            true
          );
          return; // Let the collector in talk.js handle this
        }

        // Add check for gem gift package select menu
        if (interaction.customId.startsWith("gem_gift_package_select_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by talk command collector.`,
            true
          );
          return; // Let the collector in talk.js handle this
        }

        // Add check for buy item select menu
        if (interaction.customId.startsWith("buy_item_menu_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by talk command collector.`,
            true
          );
          return; // Let the collector in talk.js handle this
        }

        // Handle Elizabeth Bits Shop select menu via persistent handler
        if (interaction.customId.startsWith("elizabeth_bits_select_")) {
          const talkCommand = require("./commands/talk.js");
          if (talkCommand && talkCommand.handleElizabethBitsSelectMenu) {
            const handled =
              await talkCommand.handleElizabethBitsSelectMenu(interaction);
            if (handled) return;
          }
        }

        // Add check for wardrobe select menus
        if (
          interaction.customId.startsWith("wardrobe_action_") ||
          interaction.customId.startsWith("wardrobe_items_") ||
          interaction.customId === "wardrobe_configure_preset" ||
          interaction.customId === "wardrobe_equip_preset"
        ) {
          const wardrobeCommand = client.commands.get("wardrobe");
          if (wardrobeCommand && wardrobeCommand.handleSelectMenu) {
            await wardrobeCommand.handleSelectMenu(interaction);
            return;
          }
        }

        // Add check for collections select menus
        if (interaction.customId.startsWith("collections_select_")) {
          const collectionsCommand = client.commands.get("collections");
          if (collectionsCommand && collectionsCommand.handleSelectMenu) {
            await collectionsCommand.handleSelectMenu(interaction);
            return;
          }
        }

        // Add check for desk select menus
        if (
          interaction.customId === "desk_menu" ||
          interaction.customId.startsWith("skymart_menu:")
        ) {
          const deskCommand = client.commands.get("desk");
          if (deskCommand && deskCommand.handleSelectMenu) {
            await deskCommand.handleSelectMenu(interaction);
            return;
          }
        }

        // Add check for slayer select menus
        if (interaction.customId === "slayers_select") {
          const statsCommand = client.commands.get("stats");
          if (statsCommand && statsCommand.handleSlayerSelectMenu) {
            await statsCommand.handleSlayerSelectMenu(interaction);
            return;
          }
        }

        // Add check for accessories select menus
        if (interaction.customId === "missing_accessory_wiki") {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by accessories command collector.`,
            true
          );
          return; // Let the collector in accessories.js handle this
        }

        // Add check for enchant select menus
        if (interaction.customId === "ench_level_select") {
          const enchantCommand = client.commands.get("enchant");
          if (enchantCommand && enchantCommand.handleSelectMenu) {
            await enchantCommand.handleSelectMenu(interaction);
            return;
          }
        }

        // Add check for settings select menus
        if (interaction.customId.startsWith("settings_")) {
          const settingsCommand = client.commands.get("settings");
          if (settingsCommand && settingsCommand.handleSelectMenu) {
            await settingsCommand.handleSelectMenu(interaction);
            return;
          }
        }

        // Add check for minion upgrade select menus
        if (interaction.customId.startsWith("minion_upgrade_slot_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by minions command collector.`,
            true
          );
          return; // Let the collector in minions.js handle this
        }

        // Add check for fuel select menus
        if (interaction.customId.startsWith("fuel_select_")) {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by minions command collector.`,
            true
          );
          return; // Let the collector in minions.js handle this
        }

        // Add check for networth category select menus
        if (interaction.customId === "networth_category") {
          logSync(
            `[Select Menu Handler] Ignoring select menu ${interaction.customId} - expected to be handled by networth command collector.`,
            true
          );
          return; // Let the collector in networth.js handle this
        }

        // Handle pet release select menu
        if (interaction.customId.startsWith("pet_release_select:")) {
          const petCommand = client.commands.get("pet");
          if (petCommand && petCommand.handlePetReleaseComponent) {
            await petCommand.handlePetReleaseComponent(interaction);
            return;
          }
        }

        // Handle reforge category select menu
        if (interaction.customId === "reforge_category_select") {
          const wikiCommand = client.commands.get("wiki");
          if (wikiCommand && wikiCommand.handleReforgeSelection) {
            await wikiCommand.handleReforgeSelection(interaction);
            return;
          }
        }

        // Reforge select menus are now handled by the talk command with blacksmith NPC

        // +++ End added checks +++

        // Placeholder for other select menu handlers
        // Skip logging for interactions likely handled by awaitMessageComponent() collectors
        const isLikelyCollectorHandled = isTemporaryCollectorInteraction(
          interaction.customId
        );

        if (!isLikelyCollectorHandled) {
          logSync(
            `[InteractionCreate] Unhandled Select Menu Interaction: ${interaction.customId}`,
            false
          );
        }
        // You might want to add a default reply here, but be careful not to interfere
      }

      // Handle Modals
      if (interaction.isModalSubmit()) {
        // Handle setup character creation modal
        if (interaction.customId.startsWith("setup_character_modal_")) {
          const {
            handleSetupCharacterModal,
          } = require("./utils/setupCharacterCreation");
          const handled = await handleSetupCharacterModal(interaction, client);
          if (handled) return;
        }

        // Handle wardrobe rename modal
        if (interaction.customId.startsWith("wardrobe_rename_modal_")) {
          const wardrobeCommand = client.commands.get("wardrobe");
          if (wardrobeCommand && wardrobeCommand.handleModal) {
            await wardrobeCommand.handleModal(interaction);
            return;
          }
        }

        // Handle desk SkyMart modals
        if (
          interaction.customId.startsWith("skymart_single_modal:") ||
          interaction.customId.startsWith("skymart_multi_modal:")
        ) {
          const deskCommand = client.commands.get("desk");
          if (deskCommand && deskCommand.handleModal) {
            await deskCommand.handleModal(interaction);
            return;
          }
        }

        // Handle minion tier change modal
        if (interaction.customId === "minion_tier_modal_submit") {
          const command = client.commands.get("recipe");
          if (command && command.handleMinionTierNavigation) {
            await command.handleMinionTierNavigation(interaction);
            return; // Stop processing, handled by recipe command
          }
        }

        // Handle storage management modal (sell/destroy quantity input)
        if (interaction.customId.startsWith("storage_quantity_modal_")) {
          const storageCommand = require("./commands/storage.js");
          if (storageCommand && storageCommand.handleStorageQuantityModal) {
            await storageCommand.handleStorageQuantityModal(interaction);
            return; // Stop processing, handled by storage command
          }
        }

        // Handle Sell Quantity Modal (NPC Talk)
        if (interaction.customId.startsWith("sell_quantity_")) {
          const talkCommand = require("./commands/talk.js");
          await talkCommand.handleSellQuantityModal(interaction);
          return;
        }

        // Handle Pet Rename Modal
        if (interaction.customId.startsWith("pet_rename_modal_")) {
          const petCommand = require("./commands/pet.js");
          await petCommand.handlePetRenameModal(interaction);
          return; // Stop processing, handled by pet command
        }

        // Handle Shady Akin Identity Change Modal
        if (interaction.customId.startsWith("identity_change_modal_")) {
          const talkCommand = require("./commands/talk.js");
          if (talkCommand && talkCommand.handleShadyAkinModalSubmit) {
            const handled =
              await talkCommand.handleShadyAkinModalSubmit(interaction);
            if (handled) return;
          }
        }

        // Handle Gem Gift Modal
        if (interaction.customId.startsWith("gem_gift_modal_")) {
          const talkCommand = require("./commands/talk.js");
          if (talkCommand && talkCommand.handleGemGiftModalSubmit) {
            const handled =
              await talkCommand.handleGemGiftModalSubmit(interaction);
            if (handled) return;
          }
        }

        // Handle Recipe Craft Modal
        if (
          interaction.customId === "recipe_craft_modal" ||
          (typeof interaction.customId === "string" &&
            interaction.customId.startsWith("recipe_craft_modal::"))
        ) {
          const recipeCommand = require("./commands/recipe.js");
          if (recipeCommand && recipeCommand.handleRecipeCraftModal) {
            await recipeCommand.handleRecipeCraftModal(interaction);
            return; // Stop processing, handled by recipe command
          }
        }

        // Handle Pet Level Modal (Wiki)
        if (interaction.customId.startsWith("pet_level_modal_submit::")) {
          const wikiCommand = require("./commands/wiki.js");
          const configManager = require("./utils/configManager.js");
          const allItems = configManager.getAllItems();
          if (wikiCommand && wikiCommand.handlePetLevelModalSubmit) {
            await wikiCommand.handlePetLevelModalSubmit(interaction, allItems);
            return; // Stop processing, handled by wiki command
          }
        }

        // Handle Buy Quantity Modals (NPC Talk)
        if (interaction.customId.startsWith("buy_quantity_")) {
          logSync(
            `[Modal Handler] Ignoring modal ${interaction.customId} - expected to be handled by talk command awaitModalSubmit.`,
            true
          );
          return; // Let the awaitModalSubmit in talk.js handle this
        }

        // Handle Elizabeth Bits quantity modal via persistent handler
        if (interaction.customId.startsWith("elizabeth_bits_qty_")) {
          const talkCommand = require("./commands/talk.js");
          if (talkCommand && talkCommand.handleElizabethBitsQuantityModal) {
            const handled =
              await talkCommand.handleElizabethBitsQuantityModal(interaction);
            if (handled) return;
          }
        }

        // Handle Trade Modals
        if (
          interaction.customId.startsWith("item_quantity_") ||
          interaction.customId.startsWith("coin_amount_")
        ) {
          logSync(
            `[Modal Handler] Ignoring modal ${interaction.customId} - expected to be handled by trade command collector.`,
            true
          );
          return; // Let the awaitModalSubmit in trade_new.js handle this
        }

        // Handle Bank Modals (Deposit/Withdraw)
        if (
          interaction.customId === "deposit_modal" ||
          interaction.customId === "withdraw_modal"
        ) {
          logSync(
            `[Modal Handler] Ignoring modal ${interaction.customId} - expected to be handled by bank command awaitModalSubmit.`,
            true
          );
          return; // Let the awaitModalSubmit in bank.js handle this
        }

        // Handle Fuel Quantity Modals
        if (interaction.customId.startsWith("fuel_quantity_")) {
          logSync(
            `[Modal Handler] Ignoring modal ${interaction.customId} - expected to be handled by minions command awaitModalSubmit.`,
            true
          );
          return; // Let the awaitModalSubmit in minions.js handle this
        }

        // Handle Market Modals
        if (
          interaction.customId.startsWith("create_sell_order_modal:") ||
          interaction.customId === "create_buy_order_modal" ||
          interaction.customId.startsWith("instant_buy_modal:") ||
          interaction.customId.startsWith("instant_sell_modal:") ||
          interaction.customId.startsWith("buy_order_modal:") ||
          interaction.customId.startsWith("sell_order_modal:") ||
          interaction.customId === "market_search_modal"
        ) {
          const marketCommand = require("./commands/market.js");
          if (marketCommand && marketCommand.handleModalSubmit) {
            await marketCommand.handleModalSubmit(interaction);
            return; // Stop processing, handled by market command
          }
        }

        // Handle any remaining modals
        logSync(
          `[InteractionCreate] Unhandled Modal Submit Interaction: ${interaction.customId}`,
          false
        );
        // You might want to add a default reply here
        // await interaction.reply({ content: 'This modal submit does not have a handler.', ephemeral: true });
      }

      // Handle Slash Commands
      if (!interaction.isChatInputCommand()) return;

      // --- WORKER BOT DELEGATION FOR ACTION COMMANDS ---
      const actionCommands = [
        "farm",
        "mine",
        "fish",
        "combat",
        "brew",
        "forage",
      ];

      if (actionCommands.includes(interaction.commandName)) {
        // Delegate action commands to worker bots
        const { workerManager } = require("./utils/workerManager");
        try {
          // Always defer reply before validation to prevent InteractionNotReplied errors
          if (!interaction.deferred && !interaction.replied) {
            await interaction.deferReply();
          }
          // Do basic validation before delegation
          const {
            extractAndValidateSkillCommandParams,
          } = require("./utils/commandUtils");
          const skillConfigs = {
            farm: require("./data/skillConfigs").farmingSkillConfig,
            mine: require("./data/skillConfigs").miningSkillConfig,
            fish: require("./data/skillConfigs").fishingSkillConfig,
            combat: require("./data/skillConfigs").combatSkillConfig,
            brew: require("./data/skillConfigs").alchemySkillConfig, // map brew to alchemy config
            forage: require("./data/skillConfigs").foragingSkillConfig,
          };
          const skillConfig = skillConfigs[interaction.commandName];
          if (skillConfig) {
            // Validate parameters before delegation
            const params = await extractAndValidateSkillCommandParams(
              interaction,
              skillConfig
            );
            if (!params) {
              // Validation failed - extractAndValidateSkillCommandParams handles the reply
              return;
            }
            // Prevent delegation if user has any active activity
            const {
              getCurrentActivity,
              warnUserBusy,
            } = require("./utils/activityManager");
            const currentActivity = await getCurrentActivity(
              interaction.user.id
            );
            if (currentActivity) {
              console.warn(
                `[MainBot] User ${interaction.user.id} is already busy with ${currentActivity}, cannot start ${interaction.commandName}. Skipping delegation.`
              );

              // Use existing warnUserBusy function (handles all embed formatting)
              await warnUserBusy(
                interaction,
                currentActivity,
                interaction.commandName
              );
              return;
            }
            const delegated = await workerManager.delegateAction(
              interaction,
              interaction.commandName,
              params.amount,
              params.resourceKey,
              params.wasMax
            );
            if (delegated) {
              // Successfully delegated to worker bot - we're done
              console.log(
                `[MainBot] Successfully delegated ${interaction.commandName} to worker bot for user ${interaction.user.id}`
              );
              return;
            }
            // If delegation failed, provide user feedback and do NOT run action on main bot
            console.warn(
              `[MainBot] Failed to delegate ${interaction.commandName}, no worker available. Not running action on main bot.`
            );

            // Send user-friendly error message when no workers are available
            const { EmbedBuilder } = require("discord.js");
            const { EMBED_COLORS } = require("./gameConfig");
            const embed = new EmbedBuilder()
              .setDescription(
                "⚠️ All workers are currently busy. Please try again in a moment."
              )
              .setColor(EMBED_COLORS.YELLOW);

            try {
              if (!interaction.deferred && !interaction.replied) {
                await interaction.reply({ embeds: [embed], ephemeral: true });
              } else if (interaction.deferred) {
                await interaction.editReply({ embeds: [embed] });
              }
            } catch (replyError) {
              console.error(
                `[MainBot] Failed to send no-worker-available message:`,
                replyError
              );
            }
            return;
          }
        } catch (delegationError) {
          console.error(
            `[MainBot] Error during delegation of ${interaction.commandName}:`,
            delegationError
          );

          // Send user-friendly error message when delegation fails with an error
          const { EmbedBuilder } = require("discord.js");
          const { EMBED_COLORS } = require("./gameConfig");
          const embed = new EmbedBuilder()
            .setDescription(
              "❌ Unable to process your request at the moment. Please try again."
            )
            .setColor(EMBED_COLORS.ERROR);

          try {
            if (!interaction.deferred && !interaction.replied) {
              await interaction.reply({ embeds: [embed], ephemeral: true });
            } else if (interaction.deferred) {
              await interaction.editReply({ embeds: [embed] });
            }
          } catch (replyError) {
            console.error(
              `[MainBot] Failed to send delegation-error message:`,
              replyError
            );
          }

          // If delegation throws, do NOT run action on main bot, just return
          return;
        }
      }
      // --- END WORKER BOT DELEGATION ---

      const command = client.commands.get(interaction.commandName);

      if (!command) {
        console.error(
          `[InteractionCreate] No command matching ${interaction.commandName} was found.`
        );
        if (interaction.deferred || interaction.replied) {
          return interaction.editReply({
            content: "There was an error while executing this command!",
            ephemeral: true,
          });
        } else {
          return interaction.reply({
            content: "There was an error while executing this command!",
            ephemeral: true,
          });
        }
      }

      // Check if the command is being used in DMs and prevent it
      if (!interaction.guild) {
        const dmErrorMessage =
          "This bot can only be used in servers, not in direct messages. Please use the bot in a Discord server instead.";
        if (interaction.deferred || interaction.replied) {
          return interaction.editReply({
            content: dmErrorMessage,
            ephemeral: true,
          });
        } else {
          return interaction.reply({
            content: dmErrorMessage,
            ephemeral: true,
          });
        }
      }

      try {
        // check if command is disabled
        if (commandToggleManager.isCommandDisabled(interaction.commandName)) {
          const disabledEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("🚫 System Disabled")
            .setDescription(
              `The **${interaction.commandName}** command is currently disabled by administrators.`
            );

          if (interaction.deferred || interaction.replied) {
            return interaction.editReply({
              embeds: [disabledEmbed],
              ephemeral: true,
            });
          } else {
            return interaction.reply({
              embeds: [disabledEmbed],
              ephemeral: true,
            });
          }
        }

        // Update user's last active channel for slash commands
        await updateUserActiveChannel(
          interaction.user.id,
          interaction.channel.id
        );

        // Record channel activity for restart announcements
        const {
          recordActivityForAnnouncements,
        } = require("./utils/activityTracker");
        recordActivityForAnnouncements(interaction.channel.id);

        // Execute the command
        await command.execute(interaction);
      } catch (error) {
        console.error(
          `[InteractionCreate] Error executing command ${interaction.commandName}:`,
          error
        );

        // Report to Sentry with context (skip 'Unknown interaction' errors as they're expected)
        if (error.code !== 10062) {
          Sentry.captureException(error, {
            user: interaction.user.id,
            command: interaction.commandName,
            guild: interaction.guild?.id,
            channel: interaction.channel?.id,
            extra: {
              commandOptions: interaction.options?.data,
              interactionType: interaction.type,
            },
          });
        }

        // Attempt to reply or editReply depending on interaction state
        const errorMessage = "There was an error while executing this command!";

        if (interaction.deferred || interaction.replied) {
          try {
            await interaction.editReply({
              content: errorMessage,
              ephemeral: true,
            });
          } catch (editError) {
            console.error(
              `[InteractionCreate] Failed to edit reply for command ${interaction.commandName} error:`,
              editError
            );
            // Final fallback: if edit fails, log and potentially ignore
          }
        } else {
          try {
            await interaction.reply({ content: errorMessage, ephemeral: true });
          } catch (replyError) {
            console.error(
              `[InteractionCreate] Failed to send initial reply for command ${interaction.commandName} error:`,
              replyError
            );
            // If initial reply fails, not much more can be done via this interaction
          }
        }
      }
    });

    // --- Other Bot Events ---

    // Message Create (for basic text commands or mentions if needed)
    // This part is often removed or limited when primarily using slash commands
    client.on("messageCreate", async (message) => {
      // Ignore bot messages
      if (message.author.bot) return;

      // Example: Reply to mentions (optional)
      // if (message.mentions.users.has(client.user.id)) {
      //     message.reply('Hello there! Use slash commands (type /) to interact with me.');
      // }

      // Ignore non-command messages
      // If you have any text-based commands, process them here
    });

    // Guild Create (Bot added to a new server)
    client.on("guildCreate", async (guild) => {
      console.log(
        `Joined new guild: ${guild.name} (id: ${guild.id}). Member count: ${guild.memberCount}`
      );
      // Optional: Perform initial setup for the guild (e.g., create default channel, send welcome message)
    });

    // Guild Delete (Bot removed from a server)
    client.on("guildDelete", (guild) => {
      console.log(`Left guild: ${guild.name} (id: ${guild.id})`);
      // Optional: Clean up any guild-specific data
    });

    // Error handling for the client
    client.on("error", (error) => {
      console.error("[Discord Client Error]", error);
      Sentry.captureException(error, {
        extra: { source: "discord_client" },
      });
    });

    client.on("warn", (info) => {
      console.warn("[Discord Client Warning]", info);
    });

    client.on("debug", () => {
      // Debug logging disabled for performance
    });

    // --- Login ---
    logSync("Logging in bot...");

    // --- 5-Second Global Compacting Timer ---
    const minionSystemTimerId = safeSetInterval(async () => {
      try {
        const {
          getAllPlayerIds,
          getPlayerData,
          savePlayerData,
        } = require("./utils/playerDataManager.js");
        const configManager = require("./utils/configManager.js");
        const {
          simulateMinionGeneration,
        } = require("./utils/newMinionGeneration.js");
        const { withMinionLock } = require("./utils/minionMutex.js");
        const allItems = configManager.getAllItems();
        const playerIds = await getAllPlayerIds();

        // console.log(`[GlobalCompacting] Processing ${playerIds.length} players for 5s sweep...`);

        // Statistics tracking (currently unused)
        // let playersWithMinions = 0;
        // let totalMinionsProcessed = 0;

        for (const userId of playerIds) {
          // Use minion lock to prevent race conditions with user operations
          try {
            await withMinionLock(userId, "background_generation", async () => {
              const playerData = await getPlayerData(userId, ["island"]);

              if (!playerData || !playerData.island) {
                // Silent handling - no island data
                return;
              }

              let islandData;
              try {
                islandData =
                  typeof playerData.island === "string"
                    ? JSON.parse(playerData.island)
                    : playerData.island;
              } catch {
                // Silent handling - failed to parse island data
                return;
              }

              if (
                !islandData.placedMinions ||
                !Array.isArray(islandData.placedMinions)
              ) {
                // Silent handling - no placed minions
                return;
              }

              if (islandData.placedMinions.length === 0) {
                // Silent handling - empty placedMinions array
                return;
              }

              // Silent minion processing

              let changed = false;
              for (const minion of islandData.placedMinions) {
                // Store original timestamp to detect changes
                const originalTimestamp = minion.lastCollectionTimestamp;
                // Silent minion processing

                const result = simulateMinionGeneration(minion, allItems);

                // Mark as changed if timestamp was updated (generation occurred)
                if (originalTimestamp !== minion.lastCollectionTimestamp) {
                  changed = true;
                  // Silent timestamp update
                }

                // Also mark as changed if any items were generated or compacted
                if (Object.keys(result.generated).length > 0) {
                  changed = true;
                  // Silent item generation
                }
              }

              if (changed) {
                playerData.island_json = islandData;
                await savePlayerData(userId, playerData, ["island"]);
                // Silent save completion
              } else {
                // Silent - no changes needed
              }
            }); // End of withMinionLock
          } catch {
            // Silent handling of lock errors - don't log to avoid spam
            // Individual user lock failures shouldn't stop processing other users
          }
        }

        // Silent sweep completion
      } catch (error) {
        // Enhanced error handling for minion system
        console.error(
          "[MinionSystem] Error in minion generation timer:",
          error
        );
      }
    }, 5000);

    // Log minion system timer initialization
    if (minionSystemTimerId) {
      if (!QUIET_BOOT)
        console.log(
          "[MinionSystem] Robust timer system initialized for minion generation"
        );
    } else {
      console.warn(
        "[MinionSystem] Failed to initialize minion generation timer"
      );
    }

    await client.login(config.token);
    // Login is asynchronous, 'ready' event fires when complete
  } catch (startBotError) {
    console.error("CRITICAL ERROR during bot startup:", startBotError);
    // @ts-ignore
    process.exit(1);
  }
}

// Only auto-start if this file is the entry point (prevents accidental re-initialization when required by workers)
if (require.main === module) {
  // Start the bot
  // This is the entry point for the async startBot function
  startBot();
} else {
  // If a worker or another module requires this, DO NOT start a second Discord client / Express server
  // This avoids EADDRINUSE and duplicated event handlers.
}

// Register database cleanup handler
gracefulErrorHandler.registerCleanupHandler(async () => {
  console.log("[Shutdown] Draining database write queue...");
  // Flush any pending action progress batches first to ensure progress is persisted
  try {
    const { batcher } = require("./utils/actionProgressBatcher");
    await batcher.flushAllBatches();
    console.log("[Shutdown] Action progress batches flushed");
  } catch (e) {
    console.warn(
      "[Shutdown] Failed to flush action progress batches:",
      e?.message || e
    );
  }
  const { drainQueue } = require("./utils/writeQueue");
  await drainQueue();
  console.log("[Shutdown] Database write queue drained");
}, "database-drain");

// Monitor error handler events
gracefulErrorHandler.on("criticalError", (errorInfo) => {
  console.warn(`[ErrorHandler] Critical error logged: ${errorInfo.type}`);

  // Report to Sentry but don't crash
  Sentry.captureException(new Error(errorInfo.error), {
    extra: {
      source: errorInfo.type,
      errorInfo: errorInfo,
    },
  });
});

gracefulErrorHandler.on("recovery", (recoveryInfo) => {
  if (recoveryInfo.success) {
    console.log(
      `[ErrorHandler] Successfully recovered from ${recoveryInfo.type}`
    );
  } else {
    console.warn(`[ErrorHandler] Failed to recover from ${recoveryInfo.type}`);
  }
});

gracefulErrorHandler.on("maxRestartsExceeded", () => {
  console.error(
    "[ErrorHandler] Maximum restart attempts exceeded - system may need manual intervention"
  );
});

module.exports = {
  getTimeState,
  getCurrentTimeState: getTimeState, // Alias for compatibility
  processStopButtonInteraction, // Export for worker bots
};
