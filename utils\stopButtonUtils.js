const { But<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder } = require("discord.js");

/**
 * Ensures a Stop button exists on a message for a multi-action.
 * If a Stop button for the given action already exists the message is untouched.
 *
 * @param {import('discord.js').Message} message Discord message to edit
 * @param {number} actionId Database action ID
 * @param {string} userId Player Discord ID
 * @param {string} [label="Stop"] Label for the button (e.g. "Stop Combat")
 */
async function ensureStopButton(message, actionId, userId, label = "Stop") {
  try {
    if (!message || !message.edit || !actionId || !userId) return;

    const hasStopAlready = (message.components || []).some((row) =>
      row.components.some(
        (c) => c.customId && c.customId.startsWith(`stop_action:${actionId}`),
      ),
    );

    if (hasStopAlready) return; // Nothing to do

    const stopButton = new ButtonBuilder()
      .setCustomId(`stop_action:${actionId}:${userId}`)
      .setLabel(label)
      .setStyle(ButtonStyle.Danger);

    const row = new ActionRowBuilder().addComponents(stopButton);
    await message.edit({ components: [...message.components, row] });
  } catch (err) {
    console.warn(
      `[StopButtonUtils] Failed to ensure Stop button for action ${actionId}:`,
      err,
    );
  }
}

module.exports = { ensureStopButton };
