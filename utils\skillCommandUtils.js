const {
  getCurrentActivity,
  trySetActivity,
  clearActivity,
  warnUserBusy,
} = require("./activityManager");
const { extractAndValidateSkillCommandParams } = require("./commandUtils");
const { MessageFlags } = require("discord.js");

/**
 * Handles the common initial logic for skill commands (activity checks, parameter extraction)
 * and then calls the unified skill handler.
 * @param {import('discord.js').CommandInteraction} interaction - The interaction object.
 * @param {object} skillConfig - Skill-specific configuration.
 */
async function handleSkillCommand(interaction, skillConfig) {
  const userId = interaction.user.id;
  const skillName = skillConfig.skillName;

  // defer the interaction immediately to prevent timeout issues
  // skill commands need time for database operations and validation
  if (!interaction.deferred && !interaction.replied) {
    await interaction.deferReply();
  }

  // Check for active action in DB (skip if this is from a repeat button since activity was already set)
  if (!interaction.isFromRepeatButton) {
    const activeType = await getCurrentActivity(userId);
    if (activeType) {
      return warnUserBusy(interaction, activeType);
    }
  } else {
    console.log(
      `[SkillCommandUtils] Skipping activity check for ${skillName} - called from repeat button`
    );
  }

  // Extract and validate command parameters BEFORE setting activity
  const params = await extractAndValidateSkillCommandParams(
    interaction,
    skillConfig
  );
  if (!params) {
    // extractAndValidateSkillCommandParams handles the reply on failure
    return;
  }

  // Check if user can use commands in this channel
  const { canUseCommandsInChannel } = require("./channelManager");
  const canUseCommands = canUseCommandsInChannel(
    userId,
    interaction.channel.id,
    params.character.personal_channel_id
  );

  if (!canUseCommands) {
    const personalChannelId = params.character.personal_channel_id;
    let errorMessage;

    if (personalChannelId) {
      errorMessage = `You can only use skill commands in your personal channel: <#${personalChannelId}>`;
    } else {
      errorMessage =
        "You need to set up your personal channel first. Please contact an administrator.";
    }

    await interaction
      .editReply({
        content: errorMessage,
        flags: [MessageFlags.Ephemeral],
      })
      .catch(() => {});
    return;
  }

  // ATOMIC OPERATION: Try to set activity first to prevent race conditions
  const activitySet = trySetActivity(userId, skillName);
  if (!activitySet) {
    // User is already busy with another activity
    const currentActivity = await getCurrentActivity(userId);
    let busyMsg;
    if (currentActivity) {
      busyMsg = `You cannot start ${skillName} while you are ${currentActivity}!`;
    } else {
      busyMsg = "You are already busy!";
    }
    await interaction
      .editReply({ content: busyMsg, flags: [MessageFlags.Ephemeral] })
      .catch(() => {});
    return;
  }

  try {
    // Call the unified skill action handler
    const {
      handleGenericSkillAction,
    } = require("../utils/genericSkillHandler");
    await handleGenericSkillAction(
      interaction,
      params.character,
      params.resourceKey,
      params.amount,
      params.isAgain,
      skillConfig,
      params.wasMax
    );
    // Activity tracking now handled globally in bot.js for all slash commands
  } catch (error) {
    console.error(
      `[handleSkillCommand][${skillName}] Error during skill action execution for ${userId}:`,
      error
    );
    // Handle errors from the skill action handler if necessary
    // The handler itself might send replies, but a generic fallback could be here
    if (!interaction.replied && !interaction.deferred) {
      await interaction
        .reply({
          content: `An error occurred while performing the ${skillName} action. Please try again.`,
          flags: [MessageFlags.Ephemeral],
        })
        .catch(() => {});
    } else {
      // If already replied/deferred, try editing the reply
      await interaction
        .editReply({
          content: `An error occurred while performing the ${skillName} action. Please try again.`,
          components: [],
          embeds: [],
        })
        .catch((editError) => {
          // Handle common Discord API errors silently
          if (editError.code === 10008) {
            console.log(
              `[SkillCommand] Original message no longer exists for ${skillName} error handling`
            );
          } else if (editError.code === 500 || editError.code === 50027) {
            console.log(
              `[SkillCommand] Invalid webhook token for ${skillName} error handling`
            );
          } else if (editError.code === 10062) {
            console.log(
              `[SkillCommand] Interaction expired for ${skillName} error handling`
            );
          } else {
            console.warn(
              `[SkillCommand] Failed to edit error reply for ${skillName}:`,
              editError.message
            );
          }
        });
    }
    // Only clear activity on error - normal completion is handled by the action handler itself
    clearActivity(userId);
  }
}

module.exports = {
  handleSkillCommand,
};
