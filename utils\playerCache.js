const { dbAll } = require("./dbUtils");

// Global player cache
const playerCache = {
  players: [],
  lastUpdated: 0,
  isRefreshing: false,
};

// Cache refresh interval (5 minutes)
const CACHE_REFRESH_INTERVAL = 5 * 60 * 1000;

// Refresh the player cache from database
async function refreshPlayerCache() {
  if (playerCache.isRefreshing) {
    return playerCache.players;
  }

  playerCache.isRefreshing = true;
  try {
    const rows = await dbAll(
      "SELECT discord_id, name FROM players WHERE name IS NOT NULL AND name != '' ORDER BY name",
      []
    );
    // Filter out any null or invalid entries
    playerCache.players = rows.filter(
      (player) =>
        player &&
        player.discord_id &&
        player.name &&
        typeof player.name === "string" &&
        player.name.trim().length > 0
    );
    playerCache.lastUpdated = Date.now();
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(
        `[PlayerCache] Refreshed cache with ${playerCache.players.length} valid players`
      );
    return playerCache.players;
  } catch (error) {
    console.error("[PlayerCache] Error refreshing cache:", error);
    // Return existing cache if refresh fails
    return playerCache.players;
  } finally {
    playerCache.isRefreshing = false;
  }
}

// Helper function to get all players with their usernames for autocomplete
async function getAllPlayersWithUsernames() {
  const now = Date.now();
  const cacheAge = now - playerCache.lastUpdated;

  // If cache is empty or older than refresh interval, refresh it
  if (playerCache.players.length === 0 || cacheAge > CACHE_REFRESH_INTERVAL) {
    return await refreshPlayerCache();
  }

  // Return cached data
  return playerCache.players;
}

// Filter players by search term (for autocomplete)
function getFilteredPlayers(searchTerm, limit = 25) {
  const lowerSearchTerm = (searchTerm || "").toLowerCase();
  const filtered = playerCache.players
    .filter(
      (player) =>
        player &&
        player.name &&
        typeof player.name === "string" &&
        player.name.toLowerCase().includes(lowerSearchTerm)
    )
    .slice(0, limit)
    .map((player) => ({
      name: player.name,
      value: player.name,
    }));

  return filtered;
}

// Get player by exact username (case insensitive)
function getPlayerByUsername(username) {
  if (!username || typeof username !== "string") {
    return null;
  }

  return playerCache.players.find(
    (player) =>
      player &&
      player.name &&
      typeof player.name === "string" &&
      player.name.toLowerCase() === username.toLowerCase()
  );
}

// Initialize cache on module load
(async () => {
  try {
    await refreshPlayerCache();
  } catch (error) {
    console.error("[PlayerCache] Failed to initialize cache:", error);
  }
})();

module.exports = {
  getAllPlayersWithUsernames,
  getFilteredPlayers,
  getPlayerByUsername,
  refreshPlayerCache,
};
