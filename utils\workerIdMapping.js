/**
 * Worker ID Mapping Utility
 * Handles the mapping between worker IDs (worker-1, worker-2) and Discord client IDs
 */

const config = require("../config.json");

/**
 * Get the Discord client ID for a given worker ID
 * @param {string} workerId - The worker ID (e.g., "worker-1")
 * @returns {string|null} - The Discord client ID or null if not found
 */
function getDiscordIdForWorker(workerId) {
  if (!workerId) return null;

  // Extract the numeric part from worker-X format
  const workerMatch = workerId.match(/^worker-(\d+)$/);
  if (!workerMatch) return null;

  const workerNumber = workerMatch[1];
  const workerConfig = config.multiBot?.workerBots?.[workerNumber];

  return workerConfig?.clientId || null;
}

/**
 * Get the worker ID for a given Discord client ID
 * @param {string} discordId - The Discord client ID
 * @returns {string|null} - The worker ID (e.g., "worker-1") or null if not found
 */
function getWorkerIdForDiscordId(discordId) {
  if (!discordId) return null;

  const workerBots = config.multiBot?.workerBots;
  if (!workerBots) return null;

  for (const [workerNumber, workerConfig] of Object.entries(workerBots)) {
    if (workerConfig.clientId === discordId) {
      return `worker-${workerNumber}`;
    }
  }

  return null;
}

/**
 * Check if the current process should handle actions for a given worker ID
 * @param {string} actionWorkerId - The worker ID from the action record
 * @param {string} currentDiscordId - The current Discord client ID
 * @returns {boolean} - True if this process should handle the action
 */
function shouldHandleAction(actionWorkerId, currentDiscordId) {
  // CRITICAL: Actions without worker_id indicate system corruption and should NEVER be processed
  if (!actionWorkerId) {
    return false; // Will be handled as corrupted action requiring user notification
  }

  // If this is main bot, it should never handle worker actions
  if (!process.env.WORKER_BOT_ID && actionWorkerId) {
    return false;
  }

  // If this is a worker, check if it's the correct worker AND if the worker exists in config
  if (process.env.WORKER_BOT_ID) {
    // First check if this worker matches the action's worker_id
    if (process.env.WORKER_BOT_ID !== actionWorkerId) {
      return false;
    }

    // SECURITY: Validate that this worker actually exists in the configuration
    // This prevents rogue worker processes from handling actions
    const expectedDiscordId = getDiscordIdForWorker(process.env.WORKER_BOT_ID);
    if (!expectedDiscordId) {
      console.error(
        `[shouldHandleAction] SECURITY WARNING: Worker ${process.env.WORKER_BOT_ID} not found in configuration`
      );
      return false;
    }

    // Validate that the current Discord client ID matches the expected one for this worker
    if (currentDiscordId !== expectedDiscordId) {
      console.error(
        `[shouldHandleAction] SECURITY WARNING: Discord ID mismatch for worker ${process.env.WORKER_BOT_ID}. Expected: ${expectedDiscordId}, Got: ${currentDiscordId}`
      );
      return false;
    }

    return true;
  }

  return false;
}

/**
 * Check if a Discord message was created by the worker that should handle a given action
 * @param {string} messageAuthorId - The Discord ID of the message author
 * @param {string} actionWorkerId - The worker ID from the action record
 * @returns {boolean} - True if the message author matches the action's worker
 */
function isMessageFromCorrectWorker(messageAuthorId, actionWorkerId) {
  if (!messageAuthorId || !actionWorkerId) return false;

  const expectedDiscordId = getDiscordIdForWorker(actionWorkerId);
  return messageAuthorId === expectedDiscordId;
}

/**
 * Check if an action has a corrupted worker_id (null/undefined)
 * @param {string} actionWorkerId - The worker ID from the action record
 * @returns {boolean} - True if the action is corrupted and needs cleanup
 */
function isCorruptedAction(actionWorkerId) {
  return !actionWorkerId;
}

/**
 * Check if a worker_id exists in the current configuration
 * @param {string} workerId - The worker ID to validate
 * @returns {boolean} - True if the worker exists in config
 */
function isValidWorkerId(workerId) {
  if (!workerId) return false;
  return getDiscordIdForWorker(workerId) !== null;
}

module.exports = {
  getDiscordIdForWorker,
  getWorkerIdForDiscordId,
  shouldHandleAction,
  isMessageFromCorrectWorker,
  isCorruptedAction,
  isValidWorkerId,
};
