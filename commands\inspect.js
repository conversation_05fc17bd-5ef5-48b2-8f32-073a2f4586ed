const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager.js");
const configManager = require("../utils/configManager.js");
const { checkRankPermission } = require("../utils/permissionUtils");
const { STATS, EMBED_COLORS, ITEM_RARITY } = require("../gameConfig.js");
const { getItemColor, getPetColor } = require("../utils/rarityUtils");
const { ENCHANTMENTS } = require("./enchant.js");
const { getPetLevel } = require("../utils/petUtils.js");
const { MAGICAL_POWER_VALUES } = require("../utils/accessoryPowers");

const { STAT_ABBREVIATIONS } = require("../utils/statAbbreviations");

function calculateTotalStats(item, itemData, character = null) {
  const { calculateItemStats } = require("../utils/itemStatCalculator.js");
  const statResult = calculateItemStats(item, itemData, character);
  return statResult.totalStats;
}

async function handlePetInspect(interaction, pet) {
  const { EmbedBuilder } = require("discord.js");
  const configManager = require("../utils/configManager.js");
  const { calculatePetStats } = require("../utils/statCalculations.js");

  const allItems = configManager.getAllItems();
  const petData = allItems[pet.petKey];

  if (!petData) {
    return interaction.reply({
      content: "Error: Pet data not found.",
    });
  }

  const petLevel = getPetLevel(pet);

  const tempCharacter = {
    active_pet_id: pet.id,
    pets: [pet],
  };
  const petStats = calculatePetStats(tempCharacter);

  const embed = new EmbedBuilder()
    .setColor(getPetColor(pet))
    .setTitle(`${petData.emoji || "🐾"} ${petData.name}`)
    .setFooter({ text: `Pet ID: ${pet.id}` });

  let description = "";

  if (petData.description) {
    description += `*${petData.description}*\n\n`;
  }
  description += `**Rarity:** ${pet.rarity.toUpperCase()}\n`;
  description += `**Level:** ${petLevel}\n`;
  description += `**Experience:** ${Math.floor(pet.totalExp).toLocaleString()} XP\n`;

  if (pet.createdAt) {
    const createdDate = new Date(pet.createdAt);
    const timestamp = Math.floor(createdDate.getTime() / 1000);
    description += `**Created:** <t:${timestamp}:F> (<t:${timestamp}:R>)\n`;
  }

  if (petStats && Object.keys(petStats).length > 0) {
    description += "\n**Stats:**\n";
    const statsArray = Object.entries(petStats).map(([stat, value]) => {
      const statConfig = STATS[stat];
      if (statConfig) {
        const displayValue = statConfig.isPercentage
          ? `${value > 0 ? "+" : ""}${value}%`
          : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
        const statName =
          STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
        return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
      } else {
        return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
      }
    });

    const formattedStatLines = [];
    for (let i = 0; i < statsArray.length; i += 3) {
      formattedStatLines.push(statsArray.slice(i, i + 3).join(" "));
    }
    description += `${formattedStatLines.join("\n")}\n`;
  }

  embed.setDescription(description);

  // Add upgrade information if pet can be upgraded
  if (pet.rarity !== "LEGENDARY") {
    const {
      getUpgradeRequirements,
      getNextRarity,
    } = require("../utils/petUpgradeUtils");
    const { ITEM_RARITY } = require("../data/itemRarities");
    const upgradeReqs = getUpgradeRequirements(
      pet.petKey,
      pet.rarity,
      petLevel
    );

    if (upgradeReqs) {
      const nextRarity = getNextRarity(pet.rarity);
      const nextRarityInfo = ITEM_RARITY[nextRarity];

      let upgradeStr = `**Upgrade to ${nextRarityInfo.name}:**\n`;
      upgradeStr += `<:purse_coins:1367849116033482772> **${upgradeReqs.coins.toLocaleString()} coins**`;

      // Show level discount info
      if (petLevel > 1) {
        const baseReqs = getUpgradeRequirements(pet.petKey, pet.rarity, 1);
        const discount = baseReqs.coins - upgradeReqs.coins;
        if (discount > 0) {
          upgradeStr += ` *(${discount.toLocaleString()} discount)*`;
        }
      }

      // Show materials
      if (upgradeReqs.materials && upgradeReqs.materials.length > 0) {
        upgradeStr += "\n**Materials:**\n";
        for (const material of upgradeReqs.materials) {
          const materialItem = allItems[material.itemKey];
          const materialName = materialItem
            ? materialItem.name
            : material.itemKey;
          const materialEmoji = materialItem ? materialItem.emoji : "❓";
          upgradeStr += `${materialEmoji} ${material.amount.toLocaleString()}x ${materialName}\n`;
        }
      }

      // Show duration
      const hours = Math.floor(upgradeReqs.duration / 3600);
      const minutes = Math.floor((upgradeReqs.duration % 3600) / 60);
      const seconds = upgradeReqs.duration % 60;
      let durationStr = "";
      if (hours > 0) durationStr += `${hours}h `;
      if (minutes > 0) durationStr += `${minutes}m `;
      if (seconds > 0 || durationStr === "") durationStr += `${seconds}s`;
      upgradeStr += `⏳ **Duration:** ${durationStr.trim()}`;

      upgradeStr += "\n*Upgrade via Kat in The Hub*";

      embed.addFields({
        name: "Pet Upgrade",
        value: upgradeStr.trim(),
        inline: false,
      });
    }
  }

  return interaction.reply({ embeds: [embed] });
}

async function handleMinionInspect(interaction, minion, isPlaced) {
  const { EmbedBuilder } = require("discord.js");
  const configManager = require("../utils/configManager.js");
  const {
    getMinionDisplayInfo,
    getExtraStorageFromUpgrades,
    initializeMinionUpgrades,
  } = require("./minions.js");
  const {
    formatMinionStorageDisplay,
  } = require("../utils/newMinionGeneration.js");

  const allItems = configManager.getAllItems();
  const minionData = allItems[minion.itemKey];

  if (!minionData) {
    return interaction.reply({
      content: "Error: Minion data not found.",
      ephemeral: true,
    });
  }

  initializeMinionUpgrades(minion);
  const minionDisplayInfo = getMinionDisplayInfo(minion);
  const tierData = minionData?.tiers?.[minion.tier];
  const interval = tierData?.generationIntervalSeconds ?? "?";
  const baseStorage = tierData?.maxStorage ?? 0;
  const extraStorage = getExtraStorageFromUpgrades(minion, allItems);
  const maxStorage = baseStorage + extraStorage;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.SKY_BLUE)
    .setTitle(`${minionDisplayInfo.emoji} ${minionDisplayInfo.name}`)
    .setFooter({ text: `Minion ID: ${minion.id}` });

  let description = "";

  description += `**Tier:** T${minion.tier}\n`;
  description += `**Speed:** \`${interval}s\` per item\n`;
  description += `**Storage:** \`${maxStorage}\` items\n`;
  description += `**Status:** ${isPlaced ? "Placed" : "In Storage"}\n`;

  if (minion.createdAt) {
    const createdDate = new Date(minion.createdAt);
    const timestamp = Math.floor(createdDate.getTime() / 1000);
    description += `**Created:** <t:${timestamp}:F> (<t:${timestamp}:R>)\n`;
  }

  if (
    minion.fuel &&
    minion.fuel.expiresAt &&
    Date.now() < minion.fuel.expiresAt
  ) {
    const fuelItem = allItems[minion.fuel.fuelType];
    const fuelName = fuelItem?.name || "Unknown Fuel";
    const fuelEmoji = fuelItem?.emoji || "⚡";
    const quantity = minion.fuel.quantity || 1;

    const displayQuantity = Math.floor(quantity);

    const speedBoostPercent = Math.round((minion.fuel.speedBoost || 0) * 100);
    const expiresTimestamp = Math.floor(minion.fuel.expiresAt / 1000);
    description += `**Fuel:** ${fuelEmoji} \`x${displayQuantity} ${fuelName}\` (+${speedBoostPercent}% speed)\n`;
    description += `**Expires:** <t:${expiresTimestamp}:R>\n`;
  }

  embed.setDescription(description);

  const upgradesDisplay = (minion.upgrades || [])
    .map((upgKey, idx) => {
      if (!upgKey) return `Slot ${idx + 1}: *(Empty)*`;
      const item = allItems[upgKey];
      return `Slot ${idx + 1}: **${item?.name || upgKey}**`;
    })
    .join("\n");

  embed.addFields({
    name: "Upgrades",
    value: upgradesDisplay || "None",
    inline: false,
  });

  const storedDisplay = formatMinionStorageDisplay(minion, allItems);
  embed.addFields({
    name: "Resources Stored",
    value: storedDisplay,
    inline: false,
  });

  return interaction.reply({ embeds: [embed] });
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("inspect")
    .setDescription(
      "Inspect detailed information about an item from your storage or equipped gear."
    )
    .addStringOption((option) =>
      option
        .setName("item")
        .setDescription("The item you want to inspect")
        .setRequired(true)
        .setAutocomplete(true)
    ),

  async execute(interaction) {
    const userId = interaction.user.id;

    try {
      const character = await getPlayerData(userId);
      if (!character) {
        return interaction.reply({
          content: "You don't have a character yet! Visit the setup channel.",
        });
      }

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.reply({
          content:
            "You don't have permission to use this command (Rank Error).",
        });
      }

      const itemId = interaction.options.getString("item");

      let targetItem = null;
      let isEquipped = false;
      let itemType = null;

      if (character.inventory?.equipment) {
        targetItem = character.inventory.equipment.find(
          (eq) => eq.id === itemId
        );
        if (targetItem) {
          isEquipped = targetItem.isEquipped;
          itemType = "equipment";
        }
      }

      if (!targetItem && character.inventory?.accessories) {
        targetItem = character.inventory.accessories.find(
          (acc) => acc.id === itemId
        );
        if (targetItem) {
          isEquipped = targetItem.isEquipped;
          itemType = "accessory";
        }
      }

      if (!targetItem && character.pets) {
        targetItem = character.pets.find((pet) => pet.id === itemId);
        if (targetItem) {
          isEquipped = character.active_pet_id === targetItem.id;
          itemType = "pet";
        }
      }

      if (!targetItem && character.minionStorage) {
        targetItem = character.minionStorage.find(
          (minion) => minion.id === itemId
        );
        if (targetItem) {
          isEquipped = false; // Minions in storage are never "equipped"
          itemType = "minion";
        }
      }

      if (!targetItem && character.island?.placedMinions) {
        const foundMinion = character.island.placedMinions.find(
          (minion) => minion.id === itemId
        );
        if (foundMinion) {
          targetItem = foundMinion;
          isEquipped = true; // Placed minions are considered "equipped"
          itemType = "minion";
        }
      }

      if (!targetItem) {
        return interaction.reply({
          content: "Could not find the selected item, pet, or minion.",
        });
      }

      if (itemType === "pet") {
        return await handlePetInspect(interaction, targetItem, isEquipped);
      }

      if (itemType === "minion") {
        return await handleMinionInspect(interaction, targetItem, isEquipped);
      }

      const allItems = configManager.getAllItems();
      const itemData = allItems[targetItem.itemKey];

      if (!itemData) {
        return interaction.reply({
          content: "Error: Item data not found.",
        });
      }

      let dataJson = {};
      if (targetItem.data_json) {
        try {
          dataJson =
            typeof targetItem.data_json === "string"
              ? JSON.parse(targetItem.data_json)
              : targetItem.data_json;
        } catch (error) {
          console.error("[Inspect] Error parsing data_json:", error);
        }
      }

      let displayName = itemData.name;
      if (dataJson.reforge) {
        const {
          getDynamicReforgeName,
        } = require("../utils/dynamicReforgeStats");
        const dynamicReforgeName = getDynamicReforgeName(
          dataJson.reforge,
          itemData
        );
        displayName = `${dynamicReforgeName} ${itemData.name}`;
        targetItem.reforge = dataJson.reforge;
      }

      const embed = new EmbedBuilder()
        .setColor(getItemColor(itemData))
        .setTitle(`${itemData.emoji || "❓"} ${displayName}`)
        .setFooter({ text: `Item ID: ${targetItem.id}` });

      let description = "";

      if (itemType === "accessory") {
        const magicalPower = MAGICAL_POWER_VALUES[itemData.rarity?.name] || 0;
        const status = isEquipped ? "Equipped" : "Unequipped";

        description += `**Magical Power**: +${magicalPower}\n`;
        description += `**Status**: ${status}`;

        if (itemData.description) {
          description += `\n\n`;
        } else {
          description += `\n`;
        }
      }

      if (itemData.description) {
        description += `*${itemData.description}*\n\n`;
      }

      if (itemData.baseStats && Object.keys(itemData.baseStats).length > 0) {
        const totalStats = calculateTotalStats(targetItem, itemData, character);

        if (totalStats && Object.keys(totalStats).length > 0) {
          description += "\n**Total Stats:**\n";

          const statsArray = Object.entries(totalStats).map(([stat, value]) => {
            const statConfig = STATS[stat];
            if (statConfig) {
              const displayValue = statConfig.isPercentage
                ? `${value > 0 ? "+" : ""}${value}%`
                : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
              const statName =
                STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
              return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
            } else {
              return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
            }
          });

          const formattedStatLines = [];
          for (let i = 0; i < statsArray.length; i += 3) {
            formattedStatLines.push(statsArray.slice(i, i + 3).join(" "));
          }
          description += `${formattedStatLines.join("\n")}\n`;

          // Add rarity field under total stats
          if (itemData.rarity) {
            description += `\n**Rarity:** ${ITEM_RARITY[itemData.rarity]?.name || itemData.rarity}\n`;
          }
        }
      }

      // Handle both single ability and multiple abilities
      const abilities =
        itemData.abilities ||
        (itemData.ability ? { singleAbility: itemData.ability } : {});

      if (Object.keys(abilities).length > 0) {
        const abilityEntries = Object.entries(abilities);

        for (const [, ability] of abilityEntries) {
          description += "\n**Ability:** " + ability.name + "\n";

          if (ability.type === "PROGRESSIVE") {
            const logsOnRegion = dataJson.logsOnRegion || {};
            const currentLogs = logsOnRegion[ability.region] || 0;
            const logsPerBonus = ability.logsPerBonus;
            const maxBonus = ability.maxBonus;
            const currentBonus = Math.min(
              Math.floor(currentLogs / logsPerBonus),
              maxBonus
            );
            // const logsForNextBonus = ((currentBonus + 1) * logsPerBonus) - currentLogs; // Currently unused

            const progressiveStat = STATS[ability.stat];
            const statEmoji = progressiveStat ? progressiveStat.emoji : "⚡";
            const statName = progressiveStat
              ? progressiveStat.name
              : ability.stat;

            description += `Gains +1 ${statEmoji} ${statName} every ${logsPerBonus.toLocaleString()} Logs cut on ${ability.region.charAt(0).toUpperCase() + ability.region.slice(1)}. (Max ${maxBonus})\n`;
            description += `\n**Current Bonus:** +${currentBonus} ${statEmoji}\n`;

            if (currentBonus < maxBonus) {
              const progressTowardsNext = currentLogs % logsPerBonus;
              description += `**Next Upgrade:** +1 ${statEmoji} (${progressTowardsNext}/${logsPerBonus})\n`;
            } else {
              description += `**Status:** Maxed out!\n`;
            }
            description += `**Logs Cut:** ${currentLogs.toLocaleString()}\n`;
          } else if (
            ability.type === "BULWARK" ||
            ability.type === "ZOMBIE_BULWARK"
          ) {
            const targetMobType = ability.targetMobType || "Undead";
            const killPropertyName =
              targetMobType === "Undead" ? "zombieKills" : "bulwarkKills";
            const mobKills = dataJson[killPropertyName] || 0;

            // Find current bonus and next threshold
            let currentBonus = 0;
            let nextKillsNeeded = 0;
            let nextBonus = 0;
            let isMaxed = false;

            for (let i = 0; i < ability.thresholds.length; i++) {
              const threshold = ability.thresholds[i];
              if (mobKills >= threshold.kills) {
                currentBonus = threshold.bonus;
              } else {
                nextKillsNeeded = threshold.kills;
                nextBonus = threshold.bonus;
                break;
              }
            }

            // Check if maxed
            const maxThreshold =
              ability.thresholds[ability.thresholds.length - 1];
            if (mobKills >= maxThreshold.kills) {
              isMaxed = true;
            }

            const defenseEmoji = STATS.DEFENSE.emoji;
            const defenseAbbrev =
              STAT_ABBREVIATIONS[STATS.DEFENSE.name] || STATS.DEFENSE.name;

            description += `${ability.description}\n`;
            description += `\n**Piece Bonus:** ${defenseEmoji} \`${defenseAbbrev}: +${currentBonus}\`\n`;

            if (!isMaxed) {
              description += `**Next Upgrade:** ${defenseEmoji} \`${defenseAbbrev}: +${nextBonus}\` (${mobKills.toLocaleString()}/${nextKillsNeeded.toLocaleString()})\n`;
            } else {
              description += `**Status:** Maxed out!\n`;
            }

            // Show appropriate kill count based on target mob type
            const killLabel =
              targetMobType === "Undead"
                ? "Zombie Kills"
                : `${targetMobType} Kills`;
            description += `**${killLabel}:** ${mobKills.toLocaleString()}\n`;
          } else if (ability.type === "KILL_TRACKER") {
            const totalKills = dataJson.totalKills || 0;
            const killsPerBonus = ability.killsPerBonus || 500;
            const maxBonus = ability.maxBonus || 35;
            const currentBonus = Math.min(
              Math.floor(totalKills / killsPerBonus),
              maxBonus
            );

            const statKey = ability.stat || "DAMAGE";
            const statConf = STATS[statKey] || { name: statKey, emoji: "" };
            const statEmoji = statConf.emoji || "";
            const statNameAbbrev =
              STAT_ABBREVIATIONS[statConf.name] || statConf.name || statKey;

            description += `${ability.description}\n`;
            description += `\n**Current Bonus:** ${statEmoji} \`${statNameAbbrev}: +${currentBonus}\`\n`;

            if (currentBonus < maxBonus) {
              const nextBonus = currentBonus + 1;
              const killsForNextBonus = nextBonus * killsPerBonus;
              description += `**Next Upgrade:** ${statEmoji} \`${statNameAbbrev}: +${nextBonus}\` (${totalKills.toLocaleString()}/${killsForNextBonus.toLocaleString()})\n`;
            } else {
              description += `**Status:** Maxed out!\n`;
            }

            description += `**Total Kills:** ${totalKills.toLocaleString()}\n`;
          } else if (ability.type === "WOOD_COLLECTION_TRACKER") {
            // Calculate total wood collected from all wood collections
            let totalWoodCollected = 0;
            if (character && character.collections) {
              const woodCollectionKeys = [
                "OAK_LOG",
                "SPRUCE_LOG",
                "BIRCH_LOG",
                "JUNGLE_LOG",
                "ACACIA_LOG",
                "DARK_OAK_LOG",
                "CHERRY_LOG",
                "FIG_LOG",
                "MANGROVE_LOG",
              ];

              for (const woodKey of woodCollectionKeys) {
                totalWoodCollected += character.collections[woodKey] || 0;
              }
            }

            const woodPerBonus = ability.woodPerBonus || 500;
            const maxBonus = ability.maxBonus || 100;
            const currentBonus = Math.min(
              Math.floor(totalWoodCollected / woodPerBonus),
              maxBonus
            );

            const statKey = ability.stat || "STRENGTH";
            const statConf = STATS[statKey] || { name: statKey, emoji: "" };
            const statEmoji = statConf.emoji || "";
            const statNameAbbrev =
              STAT_ABBREVIATIONS[statConf.name] || statConf.name || statKey;

            description += `${ability.description}\n`;
            description += `\n**Current Bonus:** ${statEmoji} \`${statNameAbbrev}: +${currentBonus}\`\n`;

            if (currentBonus < maxBonus) {
              const nextBonus = currentBonus + 1;
              const woodForNextBonus = nextBonus * woodPerBonus;
              description += `**Next Upgrade:** ${statEmoji} \`${statNameAbbrev}: +${nextBonus}\` (${totalWoodCollected.toLocaleString()}/${woodForNextBonus.toLocaleString()})\n`;
            } else {
              description += `**Status:** Maxed out!\n`;
            }

            description += `**Total Wood Collected:** ${totalWoodCollected.toLocaleString()}\n`;
          } else {
            description += `${ability.description}\n`;
          }
        }
      }

      if (dataJson.reforge) {
        // Get dynamic reforge stats and name
        const {
          calculateDynamicReforgeStats,
          getDynamicReforgeName,
        } = require("../utils/dynamicReforgeStats");
        const dynamicReforgeStats = calculateDynamicReforgeStats(
          dataJson.reforge,
          itemData
        );
        const dynamicReforgeName = getDynamicReforgeName(
          dataJson.reforge,
          itemData
        );

        description += "\n**Reforge:** " + dynamicReforgeName + "\n";

        const reforgeStatsArray = Object.entries(dynamicReforgeStats).map(
          ([stat, value]) => {
            const statConfig = STATS[stat];
            if (statConfig) {
              const displayValue = statConfig.isPercentage
                ? `${value > 0 ? "+" : ""}${value}%`
                : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
              const statName =
                STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
              return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
            } else {
              return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
            }
          }
        );

        if (reforgeStatsArray.length > 0) {
          const formattedStatLines = [];
          for (let i = 0; i < reforgeStatsArray.length; i += 3) {
            formattedStatLines.push(
              reforgeStatsArray.slice(i, i + 3).join(" ")
            );
          }
          description += `${formattedStatLines.join("\n")}\n`;
        }
      }

      if (
        dataJson.enchantments &&
        Object.keys(dataJson.enchantments).length > 0
      ) {
        description += "\n**Enchantments:**\n";

        const enchantmentGroups = {};
        for (const [enchantKey, level] of Object.entries(
          dataJson.enchantments
        )) {
          const groupKey = `${enchantKey}_${level}`;
          if (!enchantmentGroups[groupKey]) {
            enchantmentGroups[groupKey] = {
              enchantKey,
              level,
              count: 0,
            };
          }
          enchantmentGroups[groupKey].count++;
        }

        for (const group of Object.values(enchantmentGroups)) {
          const { enchantKey, level, count } = group;
          const enchantDef = ENCHANTMENTS[enchantKey];

          if (enchantDef) {
            const countText = count > 1 ? ` (x${count})` : "";
            description += `${enchantDef.emoji || "📖"} **${enchantDef.name} ${level}**${countText}\n`;
          } else {
            const countText = count > 1 ? ` (x${count})` : "";
            description += `📖 **${enchantKey} ${level}**${countText}\n`;
          }
        }
      }

      if (itemData.unique && dataJson.createdAt) {
        const createdDate = new Date(dataJson.createdAt);
        const timestamp = Math.floor(createdDate.getTime() / 1000);
        description += `\n**Created:** <t:${timestamp}:F> (<t:${timestamp}:R>)\n`;
      }

      if (dataJson.hotPotatoBooks && dataJson.hotPotatoBooks > 0) {
        const bookCount = dataJson.hotPotatoBooks;

        // Determine item category for correct stat bonuses
        let category = null;
        if (itemData.type === "ARMOR") category = "ARMOR";
        else if (itemData.type === "WEAPON") category = "WEAPON";

        const HOT_POTATO_STATS = {
          ARMOR: { HEALTH: 4, DEFENSE: 2 },
          WEAPON: { DAMAGE: 2, STRENGTH: 2 },
        };

        description += `\n**Hot Potato Books:** <:hot_potato_book:1374100906483646567> ${bookCount}/10\n`;

        if (category && HOT_POTATO_STATS[category]) {
          const bonusPerBook = HOT_POTATO_STATS[category];
          const relevantStats = [];

          for (const [stat, value] of Object.entries(bonusPerBook)) {
            const totalBonus = value * bookCount;
            if (itemData.baseStats && itemData.baseStats[stat] !== undefined) {
              const statConfig = STATS[stat];
              if (statConfig) {
                const statName =
                  STAT_ABBREVIATIONS[statConfig.name] ||
                  statConfig.name ||
                  stat;
                relevantStats.push(
                  `${statConfig.emoji || ""} \`${statName}: +${totalBonus}\``
                );
              }
            }
          }

          if (relevantStats.length > 0) {
            description += `${relevantStats.join(" ")}\n`;
          }
        }
      }

      const maxDescriptionLength = 4096;
      let embeds = [];
      let components = [];

      if (description.length <= maxDescriptionLength) {
        embed.setDescription(description);
        embeds = [embed];

        if (
          dataJson.enchantments &&
          Object.keys(dataJson.enchantments).length > 0
        ) {
          const showDetailsButton = new ButtonBuilder()
            .setCustomId(`inspect_details_${targetItem.id}`)
            .setLabel("Show Details")
            .setStyle(ButtonStyle.Secondary)
            .setEmoji("📖");

          const row = new ActionRowBuilder().addComponents(showDetailsButton);
          components = [row];
        }
      } else {
        const pages = [];
        const lines = description.split("\n");
        let currentPage = "";
        let currentEmbed = new EmbedBuilder()
          .setColor(getItemColor(itemData))
          .setTitle(`${itemData.emoji || "❓"} ${displayName}`)
          .setFooter({ text: `Item ID: ${targetItem.id} • Page 1` });

        for (const line of lines) {
          if ((currentPage + line + "\n").length > maxDescriptionLength) {
            currentEmbed.setDescription(currentPage);
            pages.push(currentEmbed);

            currentPage = line + "\n";
            currentEmbed = new EmbedBuilder()
              .setColor(getItemColor(itemData))
              .setTitle(`${itemData.emoji || "❓"} ${displayName}`)
              .setFooter({
                text: `Item ID: ${targetItem.id} • Page ${pages.length + 1}`,
              });
          } else {
            currentPage += line + "\n";
          }
        }

        if (currentPage.trim()) {
          currentEmbed.setDescription(currentPage);
          pages.push(currentEmbed);
        }

        embeds = [pages[0]];

        if (pages.length > 1) {
          const prevButton = new ButtonBuilder()
            .setCustomId(`inspect_prev_${targetItem.id}_0`)
            .setLabel("Previous")
            .setStyle(ButtonStyle.Primary)
            .setDisabled(true);

          const nextButton = new ButtonBuilder()
            .setCustomId(`inspect_next_${targetItem.id}_0`)
            .setLabel("Next")
            .setStyle(ButtonStyle.Primary);

          const row = new ActionRowBuilder().addComponents(
            prevButton,
            nextButton
          );
          components = [row];
        }

        if (
          dataJson.enchantments &&
          Object.keys(dataJson.enchantments).length > 0
        ) {
          const showDetailsButton = new ButtonBuilder()
            .setCustomId(`inspect_details_${targetItem.id}`)
            .setLabel("Show Details")
            .setStyle(ButtonStyle.Secondary)
            .setEmoji("📖");

          if (components.length > 0) {
            components[0].addComponents(showDetailsButton);
          } else {
            const row = new ActionRowBuilder().addComponents(showDetailsButton);
            components = [row];
          }
        }
      }

      await interaction.reply({
        embeds,
        components,
      });
      const response = await interaction.fetchReply();

      if (components.length > 0) {
        const collector = response.createMessageComponentCollector({
          componentType: ComponentType.Button,
          time: 300000, // 5 minutes
        });

        collector.on("collect", async (buttonInteraction) => {
          if (buttonInteraction.user.id !== interaction.user.id) {
            return buttonInteraction.reply({
              content: "You can only interact with your own inspect command.",
            });
          }

          const customId = buttonInteraction.customId;

          if (customId.startsWith("inspect_details_")) {
            let detailsDescription = description;

            if (
              dataJson.enchantments &&
              Object.keys(dataJson.enchantments).length > 0
            ) {
              const enchantSection = "\n**Enchantments:**\n";
              let detailedEnchants = enchantSection;

              const enchantmentGroups = {};
              for (const [enchantKey, level] of Object.entries(
                dataJson.enchantments
              )) {
                const groupKey = `${enchantKey}_${level}`;
                if (!enchantmentGroups[groupKey]) {
                  enchantmentGroups[groupKey] = {
                    enchantKey,
                    level,
                    count: 0,
                  };
                }
                enchantmentGroups[groupKey].count++;
              }

              for (const group of Object.values(enchantmentGroups)) {
                const { enchantKey, level, count } = group;
                const enchantDef = ENCHANTMENTS[enchantKey];
                if (enchantDef) {
                  const countText = count > 1 ? ` (x${count})` : "";
                  detailedEnchants += `${enchantDef.emoji || "📖"} **${enchantDef.name} ${level}**${countText}\n`;

                  if (
                    enchantDef.statBonus &&
                    typeof enchantDef.statBonus === "function"
                  ) {
                    // Get the correct stats for this enchantment, passing itemData for farming axe detection
                    const enchantStats = enchantDef.statBonus(
                      level,
                      itemData.subtype,
                      itemData
                    );
                    if (enchantStats && Object.keys(enchantStats).length > 0) {
                      const enchantStatsArray = Object.entries(
                        enchantStats
                      ).map(([stat, value]) => {
                        const statConfig = STATS[stat];
                        if (statConfig) {
                          const displayValue = statConfig.isPercentage
                            ? `${value > 0 ? "+" : ""}${value}%`
                            : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
                          const statName =
                            STAT_ABBREVIATIONS[statConfig.name] ||
                            statConfig.name ||
                            stat;
                          return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
                        } else {
                          return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
                        }
                      });

                      if (enchantStatsArray.length > 0) {
                        const formattedStatLines = [];
                        for (let i = 0; i < enchantStatsArray.length; i += 3) {
                          formattedStatLines.push(
                            enchantStatsArray.slice(i, i + 3).join(" ")
                          );
                        }
                        detailedEnchants += `${formattedStatLines.join("\n")}\n`;
                      }
                    } else {
                      let calculatedDescription = enchantDef.description;
                      if (enchantKey === "VAMPIRISM") {
                        const healPercent = level * 1; // 1% per level
                        calculatedDescription = `Heals for ${healPercent}% of your missing health whenever you kill a mob.`;
                      } else if (enchantKey === "LIFE_STEAL") {
                        const healPercent = level * 0.5; // 0.5% per level
                        calculatedDescription = `Heals for ${healPercent}% of your max health per level on hit.`;
                      } else if (enchantKey === "CRITICAL") {
                        const critChance = level * 10; // 10% per level
                        calculatedDescription = `Increases critical hit chance by ${critChance}%.`;
                      } else if (enchantKey === "EXECUTE") {
                        const dmgPerMissing =
                          ENCHANTMENTS.EXECUTE.missingHealthDamagePercent[
                            level
                          ] || 0;
                        calculatedDescription = `Deals +${dmgPerMissing}% damage for each 1% of the target's missing health.`;
                      } else if (enchantKey === "GIANT_KILLER") {
                        const damageBonus = level * 25; // 25% per level
                        calculatedDescription = `Increases damage dealt against enemies with more max health than you by ${damageBonus}%.`;
                      } else if (enchantKey === "SCAVENGER") {
                        const coinsPerLevel =
                          ENCHANTMENTS.SCAVENGER.coinsPerMobLevel[level] || 0;
                        calculatedDescription = `Enemies drop ${coinsPerLevel} extra coins per enemy level.`;
                      } else if (enchantKey === "FORTUNE") {
                        calculatedDescription = `+${level * 10} Mining Fortune`;
                      } else if (enchantKey === "SMITE") {
                        const damagePercent =
                          ENCHANTMENTS.SMITE?.undeadDamageBonus?.[level] || 0;
                        calculatedDescription = `+${damagePercent}% damage to Undead mobs`;
                      } else if (enchantKey === "BANE_OF_ARTHROPODS") {
                        const damagePercent =
                          ENCHANTMENTS.BANE_OF_ARTHROPODS
                            ?.arthropodDamageBonus?.[level] || 0;
                        calculatedDescription = `+${damagePercent}% damage to Arthropod mobs`;
                      }
                      detailedEnchants += `*${calculatedDescription}*\n`;
                    }
                  } else {
                    let calculatedDescription = enchantDef.description;
                    if (enchantKey === "VAMPIRISM") {
                      const healPercent = level * 1; // 1% per level
                      calculatedDescription = `Heals for ${healPercent}% of your missing health whenever you kill a mob.`;
                    } else if (enchantKey === "LIFE_STEAL") {
                      const healPercent = level * 0.5; // 0.5% per level
                      calculatedDescription = `Heals for ${healPercent}% of your max health per level on hit.`;
                    } else if (enchantKey === "CRITICAL") {
                      const critChance = level * 10; // 10% per level
                      calculatedDescription = `Increases critical hit chance by ${critChance}%.`;
                    } else if (enchantKey === "EXECUTE") {
                      const dmgPerMissing =
                        ENCHANTMENTS.EXECUTE.missingHealthDamagePercent[
                          level
                        ] || 0;
                      calculatedDescription = `Deals +${dmgPerMissing}% damage for each 1% of the target's missing health.`;
                    } else if (enchantKey === "GIANT_KILLER") {
                      const damageBonus = level * 25; // 25% per level
                      calculatedDescription = `Increases damage dealt against enemies with more max health than you by ${damageBonus}%.`;
                    } else if (enchantKey === "SCAVENGER") {
                      const coinsPerLevel =
                        ENCHANTMENTS.SCAVENGER.coinsPerMobLevel[level] || 0;
                      calculatedDescription = `Enemies drop ${coinsPerLevel} extra coins per enemy level.`;
                    }
                    detailedEnchants += `*${calculatedDescription}*\n`;
                  }
                } else {
                  const countText = count > 1 ? ` (x${count})` : "";
                  detailedEnchants += `📖 **${enchantKey} ${level}**${countText}\n`;
                }
              }

              const enchantStart = detailsDescription.indexOf(enchantSection);
              if (enchantStart !== -1) {
                const nextSection = detailsDescription.indexOf(
                  "\n**",
                  enchantStart + enchantSection.length
                );
                const enchantEnd =
                  nextSection !== -1 ? nextSection : detailsDescription.length;

                detailsDescription =
                  detailsDescription.substring(0, enchantStart) +
                  detailedEnchants +
                  detailsDescription.substring(enchantEnd);
              }
            }

            const detailsEmbed = new EmbedBuilder()
              .setColor(getItemColor(itemData))
              .setTitle(`${itemData.emoji || "❓"} ${displayName}`)
              .setDescription(detailsDescription)
              .setFooter({ text: `Item ID: ${targetItem.id}` });

            const hideDetailsButton = new ButtonBuilder()
              .setCustomId(`inspect_hide_details_${targetItem.id}`)
              .setLabel("Hide Details")
              .setStyle(ButtonStyle.Secondary)
              .setEmoji("📖");

            const hideRow = new ActionRowBuilder().addComponents(
              hideDetailsButton
            );

            await buttonInteraction.update({
              embeds: [detailsEmbed],
              components: [hideRow],
            });
          } else if (customId.startsWith("inspect_hide_details_")) {
            await buttonInteraction.update({ embeds, components });
          }
        });

        collector.on("end", () => {
          const disabledComponents = components.map((row) => {
            const newRow = new ActionRowBuilder();
            row.components.forEach((component) => {
              newRow.addComponents(
                ButtonBuilder.from(component).setDisabled(true)
              );
            });
            return newRow;
          });

          interaction
            .editReply({ components: disabledComponents })
            .catch(() => {});
        });
      }
    } catch (error) {
      console.error(`[Inspect Command Error] User: ${userId}`, error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: "An unexpected error occurred while inspecting the item.",
          });
        }
      } catch (replyError) {
        console.error(
          "[Inspect Command] Failed to send error message:",
          replyError
        );
      }
    }
  },

  async autocomplete(interaction) {
    try {
      const character = await getPlayerData(interaction.user.id);
      if (
        !character ||
        (!character.inventory?.equipment && !character.inventory?.accessories)
      )
        return interaction.respond([]);

      const allItemsAutocomplete = configManager.getAllItems();
      const focusedValue = interaction.options.getFocused().toLowerCase();

      const allEquipment = (character.inventory.equipment || [])
        .map((eq) => {
          const itemDetails = allItemsAutocomplete[eq.itemKey];
          if (!itemDetails) return null;

          let displayName = itemDetails.name;
          let dataJson = {};

          if (eq.data_json) {
            try {
              dataJson =
                typeof eq.data_json === "string"
                  ? JSON.parse(eq.data_json)
                  : eq.data_json;

              if (dataJson.reforge) {
                const {
                  getDynamicReforgeName,
                } = require("../utils/dynamicReforgeStats");
                const dynamicReforgeName = getDynamicReforgeName(
                  dataJson.reforge,
                  itemDetails
                );
                displayName = `${dynamicReforgeName} ${itemDetails.name}`;
              }
            } catch (error) {
              console.error(
                "[Inspect Autocomplete] Error parsing data_json:",
                error
              );
            }
          }

          return {
            ...itemDetails,
            displayName,
            id: eq.id,
            itemKey: eq.itemKey,
            isEquipped: eq.isEquipped,
          };
        })
        .filter((item) => item !== null)
        .filter(
          (item) =>
            item.displayName.toLowerCase().includes(focusedValue) ||
            item.id.toLowerCase().startsWith(focusedValue)
        )
        .sort((a, b) => {
          if (a.isEquipped && !b.isEquipped) return -1;
          if (!a.isEquipped && b.isEquipped) return 1;
          return a.displayName.localeCompare(b.displayName);
        })
        .map((item) => ({
          name: `${item.displayName} (${item.id.slice(0, 4)})${item.isEquipped ? " [EQUIPPED]" : ""}`,
          value: item.id,
        }));

      const allAccessories = (character.inventory.accessories || [])
        .map((acc) => {
          const itemDetails = allItemsAutocomplete[acc.itemKey];
          if (!itemDetails) return null;

          let displayName = itemDetails.name;
          let dataJson = {};

          if (acc.data_json) {
            try {
              dataJson =
                typeof acc.data_json === "string"
                  ? JSON.parse(acc.data_json)
                  : acc.data_json;

              if (dataJson.reforge) {
                const {
                  getDynamicReforgeName,
                } = require("../utils/dynamicReforgeStats");
                const dynamicReforgeName = getDynamicReforgeName(
                  dataJson.reforge,
                  itemDetails
                );
                displayName = `${dynamicReforgeName} ${itemDetails.name}`;
              }
            } catch (error) {
              console.error(
                "[Inspect Autocomplete] Error parsing accessory data_json:",
                error
              );
            }
          }

          return {
            ...itemDetails,
            displayName,
            id: acc.id,
            itemKey: acc.itemKey,
            isEquipped: acc.isEquipped,
          };
        })
        .filter((item) => item !== null)
        .filter(
          (item) =>
            item.displayName.toLowerCase().includes(focusedValue) ||
            item.id.toLowerCase().startsWith(focusedValue)
        )
        .map((item) => {
          const rarity = item.rarity?.name || "COMMON";
          const equippedText = item.isEquipped ? " [Equipped]" : "";
          return {
            name: `${rarity.toUpperCase()} ${item.displayName} (${item.id.slice(0, 4)})${equippedText}`,
            value: item.id,
          };
        });

      const allPets = (character.pets || [])
        .map((pet) => {
          const petDetails = allItemsAutocomplete[pet.petKey];
          if (!petDetails) return null;

          const isActive = character.active_pet_id === pet.id;

          return {
            ...petDetails,
            displayName: petDetails.name,
            id: pet.id,
            petKey: pet.petKey,
            isEquipped: isActive,
            rarity: pet.rarity,
          };
        })
        .filter((item) => item !== null)
        .filter(
          (item) =>
            item.displayName.toLowerCase().includes(focusedValue) ||
            item.id.toLowerCase().startsWith(focusedValue)
        )
        .map((item) => {
          const equippedText = item.isEquipped ? " [Equipped]" : "";

          const fullPet = character.pets.find((p) => p.id === item.id);
          const petLevel = fullPet ? getPetLevel(fullPet) : 1;
          return {
            name: `${item.rarity.toUpperCase()} ${item.displayName} Lvl ${petLevel} (${item.id.slice(0, 4)})${equippedText}`,
            value: item.id,
          };
        });

      const allMinions = [];

      if (character.minionStorage) {
        character.minionStorage.forEach((minion) => {
          const minionDetails = allItemsAutocomplete[minion.itemKey];
          if (minionDetails) {
            allMinions.push({
              ...minionDetails,
              displayName: minionDetails.name,
              id: minion.id,
              itemKey: minion.itemKey,
              isEquipped: false,
              tier: minion.tier,
            });
          }
        });
      }

      if (character.island?.placedMinions) {
        character.island.placedMinions.forEach((minion) => {
          const minionDetails = allItemsAutocomplete[minion.itemKey];
          if (minionDetails) {
            allMinions.push({
              ...minionDetails,
              displayName: minionDetails.name,
              id: minion.id,
              itemKey: minion.itemKey,
              isEquipped: true,
              tier: minion.tier,
            });
          }
        });
      }

      const filteredMinions = allMinions
        .filter(
          (item) =>
            item.displayName.toLowerCase().includes(focusedValue) ||
            item.id.toLowerCase().startsWith(focusedValue)
        )
        .map((item) => ({
          name: `${item.displayName} T${item.tier} (${item.id.slice(0, 4)})${item.isEquipped ? " [PLACED]" : ""}`,
          value: item.id,
        }));

      const allItems = [
        ...allEquipment,
        ...allAccessories,
        ...allPets,
        ...filteredMinions,
      ]
        .sort((a, b) => {
          const aEquipped =
            a.name.includes("[EQUIPPED]") ||
            a.name.includes("[ACTIVE]") ||
            a.name.includes("[PLACED]");
          const bEquipped =
            b.name.includes("[EQUIPPED]") ||
            b.name.includes("[ACTIVE]") ||
            b.name.includes("[PLACED]");
          if (aEquipped && !bEquipped) return -1;
          if (!aEquipped && bEquipped) return 1;
          return a.name.localeCompare(b.name);
        })
        .slice(0, 25);

      await interaction.respond(allItems);
    } catch (autocompleteError) {
      console.error("[Inspect Autocomplete Error]:", autocompleteError);
      await interaction.respond([]).catch(() => {});
    }
  },
};
