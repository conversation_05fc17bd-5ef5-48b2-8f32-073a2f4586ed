{"name": "Slime Minion", "emoji": "<:minion_slime:1375843132708491325>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "combat", "resourceItemKey": "SLIMEBALL", "recipes": [{"ingredients": [{"itemKey": "SLIMEBALL", "amount": 64}]}], "craftingRequirements": {"collections": {"SLIMEBALL": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 24, "maxStorage": 192}, {"tier": 2, "generationIntervalSeconds": 24, "maxStorage": 320, "upgradeCost": [{"itemKey": "SLIMEBALL", "amount": 128}]}, {"tier": 3, "generationIntervalSeconds": 22, "maxStorage": 320, "upgradeCost": [{"itemKey": "SLIMEBALL", "amount": 256}]}, {"tier": 4, "generationIntervalSeconds": 22, "maxStorage": 448, "upgradeCost": [{"itemKey": "SLIMEBALL", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 20, "maxStorage": 448, "upgradeCost": [{"itemKey": "ENCHANTED_SLIMEBALL", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 20, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SLIMEBALL", "amount": 24}]}, {"tier": 7, "generationIntervalSeconds": 18, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SLIMEBALL", "amount": 64}]}, {"tier": 8, "generationIntervalSeconds": 18, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_SLIMEBALL", "amount": 128}]}, {"tier": 9, "generationIntervalSeconds": 16, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_SLIMEBALL", "amount": 256}]}, {"tier": 10, "generationIntervalSeconds": 16, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_SLIMEBALL", "amount": 512}]}, {"tier": 11, "generationIntervalSeconds": 14, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_SLIME_BLOCK", "amount": 8}]}], "drops": [{"itemKey": "SLIMEBALL", "chance": 1}]}