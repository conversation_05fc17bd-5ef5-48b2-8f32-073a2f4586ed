const { Embed<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");
const { createStopButton } = require("./unifiedStopButtons");

/**
 * Convert hex color string to number
 * @param {string} hexColor - Hex color string like "#3498DB"
 * @returns {number} Color as number for Discord embeds
 */
function hexToNumber(hexColor) {
  return parseInt(hexColor.replace("#", ""), 16);
}

/**
 * Creates an animation embed for skill actions without integrated buttons
 * @param {Object} options - UI options
 * @param {string} options.skillName - Name of the skill (mining, farming, etc.)
 * @param {string} options.resourceName - Name of the resource being worked on
 * @param {number} options.currentCycle - Current cycle number
 * @param {number} options.totalCycles - Total number of cycles
 * @param {string} options.animationText - The animated progress bar text
 * @param {string} options.skillEmoji - Emoji for the skill
 * @param {string} options.resourceEmoji - Emoji for the resource
 * @param {string} options.color - Hex color for the embed
 * @returns {Object} Animation embed and buttons separately
 */
function createAnimationEmbed(options) {
  const {
    skillName,
    resourceName,
    currentCycle,
    totalCycles,
    animationText,
    actionId,
    userId,
    skillEmoji,
    resourceEmoji,
    color = EMBED_COLORS.SKY_BLUE,
    extraButtons = [],
  } = options;

  // Create main content parts
  const skillNameCapitalized =
    skillName.charAt(0).toUpperCase() + skillName.slice(1);
  const titleText = `${skillEmoji} ${skillNameCapitalized} ${resourceName}... (${currentCycle}/${totalCycles})`;

  // Create embed with animation content only
  const embed = new EmbedBuilder()
    .setColor(hexToNumber(color))
    .setTitle(titleText)
    .setDescription(animationText);

  // Create stop button
  const stopButton = createStopButton(
    actionId,
    userId,
    `Stop ${skillNameCapitalized}`
  );

  // Combine stop button with any extra buttons
  const allButtons = [stopButton, ...extraButtons];

  return { embed, buttons: allButtons };
}

// tiny helper for compact counts (shared style with fishingButtons)
function formatBaitQty(n) {
  if (n == null) return "0"; // caller only uses when bait exists
  if (n < 1000) return n.toString();
  if (n < 1_000_000) return +(n / 1000).toFixed(1).replace(/\.0$/, "") + "k";
  return +(n / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
}

/**
 * Creates a fishing-specific animation embed with bait swap button
 * @param {Object} options - Fishing UI options
 * @returns {Object} Fishing animation embed and buttons separately
 */
function createFishingAnimationEmbed(options) {
  const { actionId, userId, currentBaitInfo } = options;

  const baitSwapButton = new ButtonBuilder()
    .setCustomId(`bait_swap:${actionId}:${userId}`)
    .setEmoji(currentBaitInfo?.emoji || "🎣")
    .setStyle(ButtonStyle.Secondary);
  if (currentBaitInfo?.baitKey) {
    baitSwapButton.setLabel(formatBaitQty(currentBaitInfo.amount));
  }

  const fishingOptions = { ...options, extraButtons: [baitSwapButton] };
  return createAnimationEmbed(fishingOptions);
}

/**
 * Helper to build animation UI from an existing embed
 * @param {EmbedBuilder} embed - The original embed
 * @param {string} actionId - Database action ID
 * @param {string} userId - Discord user ID
 * @param {string} actionName - Name of the action
 * @param {Object} extraButtons - Additional buttons
 * @returns {Object} Animation embed and buttons separately
 */
function buildAnimationUIFromEmbed(
  embed,
  actionId,
  userId,
  actionName,
  extraButtons = []
) {
  // Extract data from embed
  const title = embed.data.title || "Action";
  const description = embed.data.description || "";
  const color = embed.data.color || 0x2f3136;
  const footer = embed.data.footer?.text;

  // Create stop button
  const stopButton = createStopButton(actionId, userId, `Stop ${actionName}`);

  // Combine all buttons
  const allButtons = [stopButton, ...extraButtons];

  // Build embed
  const newEmbed = new EmbedBuilder().setColor(color).setTitle(title);
  if (description && description.trim().length > 0) {
    newEmbed.setDescription(description);
  }
  if (footer) {
    newEmbed.setFooter({ text: footer });
  }

  return { embed: newEmbed, buttons: allButtons };
}

/**
 * Helper to build a fishing UI row from an existing embed
 * @param {EmbedBuilder} embed - The original embed
 * @param {string} actionId - Database action ID
 * @param {string} userId - Discord user ID
 * @param {string} actionName - Name of the action
 * @param {Object} currentBaitInfo - Current bait information
 * @returns {Object} Fishing animation embed and buttons separately
 */
function buildFishingUIFromEmbed(
  embed,
  actionId,
  userId,
  actionName,
  currentBaitInfo
) {
  const baitSwapButton = new ButtonBuilder()
    .setCustomId(`bait_swap:${actionId}:${userId}`)
    .setEmoji(currentBaitInfo?.emoji || "🎣")
    .setStyle(ButtonStyle.Secondary);
  if (currentBaitInfo?.baitKey) {
    baitSwapButton.setLabel(formatBaitQty(currentBaitInfo.amount));
  }
  return buildAnimationUIFromEmbed(embed, actionId, userId, actionName, [
    baitSwapButton,
  ]);
}

/**
 * Update animation embed with new description (for animation frames)
 * @param {string} originalTitle - Original embed title
 * @param {string} newDescription - New animation description
 * @param {string} actionId - Database action ID
 * @param {string} userId - Discord user ID
 * @param {string} actionName - Name of the action
 * @param {number} originalColor - Original embed color
 * @param {string} originalFooter - Original footer text
 * @param {Object[]} extraButtons - Additional buttons
 * @returns {Object} Updated animation embed and buttons separately
 */
function updateAnimationEmbed(
  originalTitle,
  newDescription,
  actionId,
  userId,
  actionName,
  originalColor = 0x2f3136,
  originalFooter = null,
  extraButtons = []
) {
  // Create stop button
  const stopButton = createStopButton(actionId, userId, `Stop ${actionName}`);

  // Combine all buttons
  const allButtons = [stopButton, ...extraButtons];

  // Build embed
  const embed = new EmbedBuilder()
    .setColor(originalColor)
    .setTitle(originalTitle);
  if (newDescription && newDescription.trim().length > 0) {
    embed.setDescription(newDescription);
  }
  if (originalFooter) {
    embed.setFooter({ text: originalFooter });
  }

  return { embed, buttons: allButtons };
}

/**
 * Normalize skill level up embed
 * @param {EmbedBuilder} embed - The skill level up embed
 * @returns {EmbedBuilder} Skill level up embed
 */
function normalizeLevelUpEmbed(embed) {
  // No conversion needed for regular embeds; return the provided embed
  const e = new EmbedBuilder(embed.data || {});
  return e;
}

/**
 * Normalize skill result embed
 * @param {EmbedBuilder} embed - The skill result embed
 * @returns {EmbedBuilder} Skill result embed
 */
function normalizeResultEmbed(embed) {
  // For regular embeds, just ensure fields/description are within limits and return a new embed
  const color = embed.data.color || 0x2f3136;
  const title = embed.data.title || "Results";
  const description = embed.data.description || "";
  const footer = embed.data.footer?.text;
  const fields = embed.data.fields || [];

  const newEmbed = new EmbedBuilder().setColor(color).setTitle(title);
  if (description) newEmbed.setDescription(description);
  if (fields.length > 0) {
    // Copy fields as-is (Discord will enforce limits)
    newEmbed.setFields(
      fields.map((f) => ({ name: f.name, value: f.value, inline: !!f.inline }))
    );
  }
  if (footer) newEmbed.setFooter({ text: footer });
  return newEmbed;
}

module.exports = {
  createAnimationEmbed,
  createFishingAnimationEmbed,
  buildAnimationUIFromEmbed,
  buildFishingUIFromEmbed,
  updateAnimationEmbed,
  normalizeLevelUpEmbed,
  normalizeResultEmbed,
  hexToNumber,
};
