/**
 * Robust Timer Utility
 * Handles Windows time synchronization issues and libuv assertion errors gracefully
 *
 * This utility provides safe wrappers for setTimeout and setInterval that:
 * - Detect backward time adjustments
 * - Handle libuv assertion failures gracefully
 * - Provide automatic recovery mechanisms
 * - Log timing issues for debugging
 */

const { EventEmitter } = require("events");

class RobustTimer extends EventEmitter {
  constructor() {
    super();
    this.timers = new Map(); // timerId -> { type, callback, interval, startTime }
    this.lastMonotonicTime = process.hrtime.bigint();
    this.lastRealTime = Date.now();
    this.timeJumpThreshold = 10000; // 10 seconds - threshold for detecting time jumps
    this.monitoringInterval = null;
    this.nextTimerId = 1;
    this.isMonitoring = false;

    // Start monitoring for time jumps
    this.startMonitoring();

    // Handle process exit to clean up timers
    process.on("exit", () => this.cleanup());
    process.on("SIGINT", () => this.cleanup());
    process.on("SIGTERM", () => this.cleanup());
  }

  /**
   * Start monitoring for time jumps
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.checkForTimeJumps();
    }, 5000); // Check every 5 seconds

    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT) console.log("[RobustTimer] Started time jump monitoring");
  }

  /**
   * Check for backward time adjustments
   */
  checkForTimeJumps() {
    try {
      const currentMonotonicTime = process.hrtime.bigint();
      const currentRealTime = Date.now();

      // Calculate elapsed time according to both clocks
      const monotonicElapsed =
        Number(currentMonotonicTime - this.lastMonotonicTime) / 1000000; // Convert to ms
      const realElapsed = currentRealTime - this.lastRealTime;

      // If real time jumped backward significantly compared to monotonic time
      const timeDifference = Math.abs(realElapsed - monotonicElapsed);

      if (timeDifference > this.timeJumpThreshold) {
        console.warn(
          `[RobustTimer] Time jump detected! Real time: ${realElapsed}ms, Monotonic time: ${monotonicElapsed}ms, Difference: ${timeDifference}ms`
        );

        // Emit time jump event for other systems to handle
        this.emit("timeJump", {
          realElapsed,
          monotonicElapsed,
          difference: timeDifference,
        });

        // If real time went backward, consider restarting affected timers
        if (realElapsed < monotonicElapsed - this.timeJumpThreshold) {
          console.warn(
            "[RobustTimer] Backward time jump detected, considering timer recovery"
          );
          this.emit("backwardTimeJump", { difference: timeDifference });
        }
      }

      this.lastMonotonicTime = currentMonotonicTime;
      this.lastRealTime = currentRealTime;
    } catch (error) {
      console.error("[RobustTimer] Error in time jump detection:", error);
    }
  }

  /**
   * Safe setTimeout wrapper
   */
  safeSetTimeout(callback, delay, ...args) {
    const timerId = this.nextTimerId++;
    const safeDelay = Math.max(1, Math.floor(delay || 0));

    try {
      const handle = setTimeout(() => {
        this.timers.delete(timerId);
        try {
          callback.apply(null, args);
        } catch (error) {
          console.error("[RobustTimer] Error in setTimeout callback:", error);
        }
      }, safeDelay);

      this.timers.set(timerId, {
        type: "timeout",
        handle,
        callback,
        delay: safeDelay,
        startTime: Date.now(),
        args,
      });

      return timerId;
    } catch (error) {
      console.error("[RobustTimer] Failed to create setTimeout:", error);

      // Fallback: use setImmediate for short delays
      if (safeDelay <= 100) {
        const handle = setImmediate(() => {
          this.timers.delete(timerId);
          try {
            callback.apply(null, args);
          } catch (callbackError) {
            console.error(
              "[RobustTimer] Error in fallback setTimeout callback:",
              callbackError
            );
          }
        });

        this.timers.set(timerId, {
          type: "immediate",
          handle,
          callback,
          delay: safeDelay,
          startTime: Date.now(),
          args,
        });

        return timerId;
      }

      // For longer delays, log the error and return null
      console.error(
        "[RobustTimer] Cannot create fallback for long delay timeout"
      );
      return null;
    }
  }

  /**
   * Safe setInterval wrapper
   */
  safeSetInterval(callback, interval, ...args) {
    const timerId = this.nextTimerId++;
    const safeInterval = Math.max(1, Math.floor(interval || 0));

    try {
      const handle = setInterval(() => {
        try {
          callback.apply(null, args);
        } catch (error) {
          console.error("[RobustTimer] Error in setInterval callback:", error);
          // Don't stop the interval on callback errors
        }
      }, safeInterval);

      this.timers.set(timerId, {
        type: "interval",
        handle,
        callback,
        interval: safeInterval,
        startTime: Date.now(),
        args,
      });

      return timerId;
    } catch (error) {
      console.error("[RobustTimer] Failed to create setInterval:", error);
      return null;
    }
  }

  /**
   * Clear a timer
   */
  clearTimer(timerId) {
    const timer = this.timers.get(timerId);
    if (!timer) return false;

    try {
      switch (timer.type) {
        case "timeout":
          clearTimeout(timer.handle);
          break;
        case "interval":
          clearInterval(timer.handle);
          break;
        case "immediate":
          clearImmediate(timer.handle);
          break;
      }

      this.timers.delete(timerId);
      return true;
    } catch (error) {
      console.error("[RobustTimer] Error clearing timer:", error);
      this.timers.delete(timerId); // Remove from tracking even if clear failed
      return false;
    }
  }

  /**
   * Get information about active timers
   */
  getTimerInfo() {
    const info = {
      totalTimers: this.timers.size,
      byType: {
        timeout: 0,
        interval: 0,
        immediate: 0,
      },
    };

    for (const timer of this.timers.values()) {
      info.byType[timer.type]++;
    }

    return info;
  }

  /**
   * Cleanup all timers
   */
  cleanup() {
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(`[RobustTimer] Cleaning up ${this.timers.size} timers`);

    for (const [timerId] of this.timers) {
      this.clearTimer(timerId);
    }

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.isMonitoring = false;
    this.removeAllListeners();
  }

  /**
   * Recover from timing issues by restarting intervals
   */
  recoverIntervals() {
    const currentTime = Date.now();
    let recoveredCount = 0;

    for (const [timerId, timer] of this.timers) {
      if (timer.type === "interval") {
        const timeSinceStart = currentTime - timer.startTime;

        // If interval hasn't run for much longer than expected, restart it
        if (timeSinceStart > timer.interval * 2) {
          const QUIET_BOOT = process.env.QUIET_BOOT === "true";
          if (!QUIET_BOOT)
            console.log(
              `[RobustTimer] Recovering interval timer ${timerId} (${timeSinceStart}ms since start)`
            );

          try {
            clearInterval(timer.handle);
            const newHandle = setInterval(() => {
              try {
                timer.callback.apply(null, timer.args);
              } catch (error) {
                console.error(
                  "[RobustTimer] Error in recovered interval callback:",
                  error
                );
              }
            }, timer.interval);

            timer.handle = newHandle;
            timer.startTime = currentTime;
            recoveredCount++;
          } catch (error) {
            console.error(
              `[RobustTimer] Failed to recover interval timer ${timerId}:`,
              error
            );
          }
        }
      }
    }

    if (recoveredCount > 0) {
      const QUIET_BOOT = process.env.QUIET_BOOT === "true";
      if (!QUIET_BOOT)
        console.log(
          `[RobustTimer] Recovered ${recoveredCount} interval timers`
        );
    }
  }
}

// Create singleton instance
const robustTimer = new RobustTimer();

// Export convenience functions
function safeSetTimeout(callback, delay, ...args) {
  return robustTimer.safeSetTimeout(callback, delay, ...args);
}

function safeSetInterval(callback, interval, ...args) {
  return robustTimer.safeSetInterval(callback, interval, ...args);
}

function safeClearTimeout(timerId) {
  return robustTimer.clearTimer(timerId);
}

function safeClearInterval(timerId) {
  return robustTimer.clearTimer(timerId);
}

function getTimerInfo() {
  return robustTimer.getTimerInfo();
}

function recoverTimers() {
  return robustTimer.recoverIntervals();
}

// Handle libuv assertion errors globally
process.on("uncaughtException", (error) => {
  if (
    error.message &&
    error.message.includes("Assertion failed: new_time >= loop->time")
  ) {
    console.error(
      "[RobustTimer] Detected libuv time assertion error:",
      error.message
    );
    console.error(
      "[RobustTimer] This usually indicates a backward time adjustment"
    );
    console.error("[RobustTimer] Attempting timer recovery...");

    // Attempt to recover by restarting intervals
    robustTimer.recoverIntervals();

    // Log timer status
    const info = robustTimer.getTimerInfo();
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(
        `[RobustTimer] Current timer status: ${JSON.stringify(info)}`
      );

    // Don't let the process crash - return to allow recovery
    return;
  }

  // For other uncaught exceptions, use default behavior
  console.error("Uncaught Exception:", error);
  process.exit(1);
});

module.exports = {
  RobustTimer,
  safeSetTimeout,
  safeSetInterval,
  safeClearTimeout,
  safeClearInterval,
  getTimerInfo,
  recoverTimers,
  robustTimer,
};
