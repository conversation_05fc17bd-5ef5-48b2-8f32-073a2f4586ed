// Garden Visitors System - Core logic for visitor spawning, requests, and rewards

const { getPlayerData, savePlayerData } = require("./playerDataManager");
const { dbRun, dbAll, dbRunQueued } = require("./dbUtils");
const { getGardenLevel } = require("./gardenSystem");
const configManager = require("./configManager");

// Visitor definitions
const GARDEN_VISITORS = {
  ADVENTURER: {
    key: "ADVENTURER",
    name: "Adventurer",
    emoji: "<:npc_adventurer:1384162058194387034>",
    rarity: "UNCOMMON",
    unlockLevel: 1, // Garden Level 1
    dialogue: "I'm in need of some fresh crops for my journey.",
    preferredCrops: ["WHEAT", "CARROT", "POTATO"],
  },
  WEAPONSMITH: {
    key: "WEAPONSMITH",
    name: "Weaponsmith",
    emoji: "<:npc_weaponsmith:1390122701506609162>",
    rarity: "UNCOMMON",
    unlockLevel: 1, // Garden Level 1
    dialogue: "These crops will help fuel my forge!",
    preferredCrops: ["WHEAT", "SUGAR_CANE", "PUMPKIN"],
  },
  BAKER: {
    key: "BAKER",
    name: "Baker",
    emoji: "<:npc_baker:1371675630839533638>",
    rarity: "UNCOMMON",
    unlockLevel: 2, // Garden Level 2
    dialogue: "I need fresh crops for my special cakes.",
    preferredCrops: ["WHEAT", "SUGAR_CANE"],
  },
  FARM_MERCHANT: {
    key: "FARM_MERCHANT",
    name: "Farm Merchant",
    emoji: "<:npc_farm_merchant:1376249676520751144>",
    rarity: "UNCOMMON",
    unlockLevel: 2, // Garden Level 2
    dialogue: "Help me restock my inventory with your finest produce.",
    preferredCrops: ["CARROT", "POTATO", "COCOA_BEANS"],
  },
  KAT: {
    key: "KAT",
    name: "Kat",
    emoji: "<:npc_kat:1375244764575826011>",
    rarity: "UNCOMMON",
    unlockLevel: 3, // Garden Level 3
    dialogue:
      "The pets I'm caring for need nutritious food! Your crops would be perfect.",
    preferredCrops: ["WHEAT", "CARROT", "BROWN_MUSHROOM"],
  },
  JAKE: {
    key: "JAKE",
    name: "Jake",
    emoji: "<:npc_jake:1376187325825945680>",
    rarity: "UNCOMMON",
    unlockLevel: 3, // Garden Level 3
    dialogue: "Making axes is hungry work!",
    preferredCrops: ["WHEAT", "POTATO", "SUGAR_CANE"],
  },
  BLACKSMITH: {
    key: "BLACKSMITH",
    name: "Blacksmith",
    emoji: "<:npc_blacksmith:1378078835752439879>",
    rarity: "RARE",
    unlockLevel: 4, // Garden Level 4
    dialogue: "I need food.",
    preferredCrops: ["COCOA_BEANS", "PUMPKIN", "RED_MUSHROOM"],
  },
  MINE_MERCHANT: {
    key: "MINE_MERCHANT",
    name: "Mine Merchant",
    emoji: "<:npc_mine_merchant:1376249775363588137>",
    rarity: "RARE",
    unlockLevel: 5, // Garden Level 5
    dialogue: "I'll trade you good coin for those crops.",
    preferredCrops: ["POTATO", "CARROT", "BROWN_MUSHROOM"],
  },
  LUMBER_MERCHANT: {
    key: "LUMBER_MERCHANT",
    name: "Lumber Merchant",
    emoji: "<:npc_lumber_merchant:1376249703800242369>",
    rarity: "RARE",
    unlockLevel: 5, // Garden Level 5
    dialogue: "Your crops look perfect for a hearty meal.",
    preferredCrops: ["WHEAT", "SUGAR_CANE", "PUMPKIN"],
  },
  BEA: {
    key: "BEA",
    name: "Bea",
    emoji: "<:npc_bea:1368683794197909595>",
    rarity: "RARE",
    unlockLevel: 6, // Garden Level 6
    dialogue: "I have a boyfriend.",
    preferredCrops: ["SUGAR_CANE", "PUMPKIN", "RED_MUSHROOM"],
  },
  SHADY_AKIN: {
    key: "SHADY_AKIN",
    name: "Shady Akin",
    emoji: "<:developer_akinsoft:1369169613442650204>",
    rarity: "RARE",
    unlockLevel: 6, // Garden Level 6
    dialogue:
      "*Hey... I need these crops for some... business ventures. No questions asked, deal?*",
    preferredCrops: ["RED_MUSHROOM", "BROWN_MUSHROOM", "COCOA_BEANS"],
  },
  MAXWELL: {
    key: "MAXWELL",
    name: "Maxwell",
    emoji: "<:npc_maxwell:1385952510078091424>",
    rarity: "LEGENDARY",
    unlockLevel: 7, // Garden Level 7
    dialogue: "Maxwell is hungry, feed Maxwell.",
    preferredCrops: [
      "WHEAT",
      "CARROT",
      "RED_MUSHROOM",
      "BROWN_MUSHROOM",
      "SUGAR_CANE",
      "PUMPKIN",
    ],
  },
  BANKER_BONSAI: {
    key: "BANKER_BONSAI",
    name: "Banker Bonsai",
    emoji: "<:npc_banker_bonsai:1388827502939476008>",
    rarity: "UNCOMMON",
    unlockLevel: 2,
    dialogue: "Can you supply something unique?",
    preferredCrops: ["COCOA_BEANS", "NETHER_WART"],
  },
  FISH_MERCHANT: {
    key: "FISH_MERCHANT",
    name: "Fish Merchant",
    emoji: "<:npc_fish_merchant:1368683796047593523>",
    rarity: "UNCOMMON",
    unlockLevel: 2,
    dialogue: "Got anything that fish would love?",
    preferredCrops: ["PUMPKIN"],
  },
  HUB_BANKER: {
    key: "HUB_BANKER",
    name: "Hub Banker",
    emoji: "<:npc_hub_banker:1368683797498957944>",
    rarity: "UNCOMMON",
    unlockLevel: 2,
    dialogue: "Got anything fresh?",
    preferredCrops: ["WHEAT"],
  },
  AMAURY: {
    key: "AMAURY",
    name: "Amaury",
    emoji: "<:npc_amaury:1388796541023682701>",
    rarity: "RARE",
    unlockLevel: 4,
    dialogue: "I need crops to keep my energy up.",
    preferredCrops: ["COCOA_BEANS", "MELON"],
  },
  ELIZABETH: {
    key: "ELIZABETH",
    name: "Elizabeth",
    emoji: "<:npc_elizabeth:1399296177601773609>",
    rarity: "LEGENDARY",
    unlockLevel: 6,
    dialogue: "Only the finest crops will do.",
    preferredCrops: ["PUMPKIN", "RED_MUSHROOM", "BROWN_MUSHROOM", "CACTUS"],
  },
};

// Visitor spawn timer (1 minute in milliseconds)
const VISITOR_SPAWN_INTERVAL = 15 * 60 * 1000;
const FARMING_TIME_REDUCTION = 5 * 1000; // 5 seconds per farming action

// Rarity distribution for visitor spawning
const VISITOR_RARITY_WEIGHTS = {
  UNCOMMON: 70,
  RARE: 20,
  LEGENDARY: 8,
  MYTHIC: 1.8,
  SPECIAL: 0.2,
};

// Garden level request amount ranges (exact Hypixel values)
const GARDEN_LEVEL_AMOUNTS = {
  1: { min: 500, max: 1000 },
  2: { min: 1250, max: 2500 },
  3: { min: 2000, max: 4000 },
  4: { min: 5000, max: 10000 },
  5: { min: 10000, max: 20000 },
  6: { min: 20000, max: 40000 },
  7: { min: 30000, max: 60000 },
  8: { min: 40000, max: 80000 },
  9: { min: 50000, max: 100000 },
  10: { min: 70000, max: 140000 },
  11: { min: 75000, max: 150000 },
  12: { min: 80000, max: 160000 },
  13: { min: 85000, max: 170000 },
  14: { min: 90000, max: 180000 },
  15: { min: 95000, max: 190000 },
};

// Rarity multipliers for request amounts
const RARITY_MULTIPLIERS = {
  UNCOMMON: 1.0,
  RARE: 1.5,
  LEGENDARY: 3.0,
  MYTHIC: 3.0,
  SPECIAL: 3.0,
};

// Item quantity multipliers based on harvest yields per action
const ITEM_QUANTITY_MULTIPLIERS = {
  // Tier 1 - Highest Yield (1.0x baseline) - ~5 items per action
  MELON: 1.0,

  // Tier 2 - High Yield (0.8x) - 3 items per action
  CARROT: 0.8,
  POTATO: 0.8,
  COCOA_BEANS: 0.8,
  NETHER_WART: 0.8,

  // Tier 3 - Medium Yield (0.6x) - ~2-2.5 items per action
  WHEAT: 0.6,
  SUGAR_CANE: 0.6,
  CACTUS: 0.6,

  // Tier 4 - Low Yield (0.4x) - 1 item per action
  PUMPKIN: 0.4,
  RED_MUSHROOM: 0.4,
  BROWN_MUSHROOM: 0.4,
};

// Garden XP rewards by level and rarity (exact values from Hypixel Skyblock)
const GARDEN_XP_REWARDS = {
  1: { UNCOMMON: 2, RARE: 4, LEGENDARY: 10, MYTHIC: 10, SPECIAL: 10 },
  2: { UNCOMMON: 3, RARE: 5, LEGENDARY: 13, MYTHIC: 13, SPECIAL: 13 },
  3: { UNCOMMON: 3, RARE: 6, LEGENDARY: 16, MYTHIC: 16, SPECIAL: 16 },
  4: { UNCOMMON: 4, RARE: 8, LEGENDARY: 20, MYTHIC: 20, SPECIAL: 20 },
  5: { UNCOMMON: 5, RARE: 10, LEGENDARY: 24, MYTHIC: 24, SPECIAL: 24 },
  6: { UNCOMMON: 6, RARE: 12, LEGENDARY: 31, MYTHIC: 31, SPECIAL: 31 },
  7: { UNCOMMON: 8, RARE: 15, LEGENDARY: 38, MYTHIC: 38, SPECIAL: 38 },
  8: { UNCOMMON: 10, RARE: 19, LEGENDARY: 48, MYTHIC: 48, SPECIAL: 48 },
  9: { UNCOMMON: 12, RARE: 24, LEGENDARY: 60, MYTHIC: 60, SPECIAL: 60 },
  10: { UNCOMMON: 15, RARE: 30, LEGENDARY: 75, MYTHIC: 75, SPECIAL: 75 },
  11: { UNCOMMON: 15, RARE: 30, LEGENDARY: 75, MYTHIC: 75, SPECIAL: 75 },
  12: { UNCOMMON: 15, RARE: 30, LEGENDARY: 75, MYTHIC: 75, SPECIAL: 75 },
  13: { UNCOMMON: 15, RARE: 30, LEGENDARY: 75, MYTHIC: 75, SPECIAL: 75 },
  14: { UNCOMMON: 15, RARE: 30, LEGENDARY: 75, MYTHIC: 75, SPECIAL: 75 },
  15: { UNCOMMON: 15, RARE: 30, LEGENDARY: 75, MYTHIC: 75, SPECIAL: 75 },
};

// Farming XP formula: OriginalItemAmount * ItemCollectionXPAmount * RarityMultiplier
const FARMING_XP_RARITY_MULTIPLIERS = {
  UNCOMMON: 0.05,
  RARE: 0.06,
  LEGENDARY: 0.075,
  MYTHIC: 0.075,
  SPECIAL: 0.075,
};

// Copper formula: OriginalItemAmount * ItemTypeMultiplier * RarityMultiplier * FinneganBoost
// Item type multipliers for copper calculation
const COPPER_ITEM_TYPE_MULTIPLIERS = {
  // Carrots
  CARROT: 1.0,
  ENCHANTED_CARROT: 1.0,
  ENCHANTED_GOLDEN_CARROT: 1.0,

  // Wheat
  WHEAT: 1.0,
  ENCHANTED_WHEAT: 1.0,
  ENCHANTED_HAY_BALE: 1.0,
  TIGHTLY_TIED_HAY_BALE: 1.0,

  // Potatoes
  POTATO: 1.0,
  ENCHANTED_POTATO: 1.0,
  ENCHANTED_BAKED_POTATO: 1.0,

  // Pumpkins
  PUMPKIN: 1.0,
  ENCHANTED_PUMPKIN: 1.0,
  POLISHED_PUMPKIN: 1.0,

  // Sugar Cane
  SUGAR_CANE: 1.0,
  ENCHANTED_SUGAR_CANE: 1.0,
  ENCHANTED_SUGAR: 1.0,

  // Melons
  MELON: 1.0,
  ENCHANTED_MELON: 1.0,
  ENCHANTED_MELON_BLOCK: 1.0,

  // Cactus
  CACTUS: 1.0,
  ENCHANTED_CACTUS_GREEN: 1.0,
  ENCHANTED_CACTUS: 1.0,

  // Red Mushrooms
  RED_MUSHROOM: 1.0,
  ENCHANTED_RED_MUSHROOM: 1.0,
  ENCHANTED_RED_MUSHROOM_BLOCK: 1.0,

  // Brown Mushrooms
  BROWN_MUSHROOM: 1.0,
  ENCHANTED_BROWN_MUSHROOM: 1.0,
  ENCHANTED_BROWN_MUSHROOM_BLOCK: 1.0,

  // Cocoa Beans
  COCOA_BEANS: 1.0,
  ENCHANTED_COCOA_BEANS: 1.0,
  ENCHANTED_COOKIE: 1.0,

  // Nether Wart
  NETHER_WART: 1.0,
  ENCHANTED_NETHER_WART: 1.0,
  MUTANT_NETHER_WART: 1.0,

  // Seeds
  SEEDS: 1.0,
  ENCHANTED_SEEDS: 1.0,
};

// Copper rarity multipliers (corrected to match Hypixel values)
const COPPER_RARITY_MULTIPLIERS = {
  UNCOMMON: 1,
  RARE: 1.5,
  LEGENDARY: 2,
  MYTHIC: 3,
  SPECIAL: 4,
};

/**
 * Get random visitor rarity based on weights
 * @returns {string} Visitor rarity
 */
function getRandomVisitorRarity() {
  const totalWeight = Object.values(VISITOR_RARITY_WEIGHTS).reduce(
    (sum, weight) => sum + weight,
    0
  );
  const random = Math.random() * totalWeight;

  let currentWeight = 0;
  for (const [rarity, weight] of Object.entries(VISITOR_RARITY_WEIGHTS)) {
    currentWeight += weight;
    if (random <= currentWeight) {
      return rarity;
    }
  }

  // ...existing code...
}

/**
 * Get available visitors for a player based on Garden level
 * @param {number} gardenLevel - Player's Garden level
 * @returns {Array} Available visitor keys
 */
function getAvailableVisitors(gardenLevel) {
  return Object.values(GARDEN_VISITORS)
    .filter((visitor) => gardenLevel >= visitor.unlockLevel)
    .map((visitor) => visitor.key);
}

/**
 * Generate item request for a visitor
 * @param {string} visitorKey - Visitor key
 * @param {string} rarity - Visitor rarity
 * @param {number} gardenLevel - Player's Garden level
 * @returns {Array} Array of requested items
 *
 * Formula: BaseAmount × ItemQuantityMultiplier × VisitorRarityMultiplier
 * - BaseAmount: Random amount based on garden level
 * - ItemQuantityMultiplier: Based on crop harvest yield (0.4x - 1.0x)
 * - VisitorRarityMultiplier: Based on visitor rarity (1.0x - 3.0x)
 */
function generateVisitorRequest(visitorKey, rarity, gardenLevel) {
  const visitor = GARDEN_VISITORS[visitorKey];
  if (!visitor) return [];

  const levelAmounts = GARDEN_LEVEL_AMOUNTS[Math.min(gardenLevel, 15)];
  const baseAmount =
    Math.floor(Math.random() * (levelAmounts.max - levelAmounts.min + 1)) +
    levelAmounts.min;

  // Select exactly 1 random crop from visitor's preferred crops
  const numItems = 1;
  const selectedCrops = [];
  const availableCrops = [...visitor.preferredCrops];

  for (let i = 0; i < numItems && availableCrops.length > 0; i++) {
    const randomIndex = Math.floor(Math.random() * availableCrops.length);
    const cropKey = availableCrops.splice(randomIndex, 1)[0];

    // IMPORTANT: Separate “offer amount” vs “reward basis”
    // - rewardBaseAmount: baseAmount × rarity (NO item yield multiplier)
    // - offerFinalAmount: rewardBaseAmount × item yield multiplier
    const rarityScaledBase = Math.floor(
      baseAmount * RARITY_MULTIPLIERS[rarity]
    );
    const itemQuantityMultiplier = ITEM_QUANTITY_MULTIPLIERS[cropKey] || 1.0;
    const finalAmount = Math.floor(rarityScaledBase * itemQuantityMultiplier);

    // ALWAYS try to compact to enchanted variant for the OFFER display only
    const compactedRequest = tryCompactItem(cropKey, finalAmount);

    // Attach metadata so rewards are calculated from unscaled base crop amount
    selectedCrops.push({
      ...compactedRequest,
      // Rewards should be based on the base crop and the amount BEFORE yield multiplier
      baseCropKey: cropKey,
      unscaledBaseAmount: rarityScaledBase,
    });
  }

  return selectedCrops;
}

/**
 * Try to compact item request to enchanted variant
 * @param {string} itemKey - Base item key
 * @param {number} amount - Requested amount
 * @returns {Object} Request object with item and amount
 */
function tryCompactItem(itemKey, amount) {
  // If the item is already enchanted, don't try to compact it further
  if (itemKey.startsWith("ENCHANTED_")) {
    return { itemKey, amount };
  }

  const allItems = configManager.getAllItems();
  const baseItem = allItems[itemKey];

  if (!baseItem) {
    return { itemKey, amount };
  }

  // For base items, we need to find which enchanted item uses this as an ingredient
  // This handles cases like SUGAR_CANE -> ENCHANTED_SUGAR (not ENCHANTED_SUGAR_CANE)
  let bestCompaction = null;
  let bestRatio = 0;

  // Check all items to find ones that use this base item as an ingredient
  for (const [candidateKey, candidateItem] of Object.entries(allItems)) {
    if (
      !candidateKey.startsWith("ENCHANTED_") ||
      !candidateItem.recipes ||
      !candidateItem.recipes[0]
    ) {
      continue;
    }

    const recipe = candidateItem.recipes[0];
    const baseIngredient = recipe.ingredients.find(
      (ing) => ing.itemKey === itemKey
    );

    if (!baseIngredient) {
      continue;
    }

    const conversionRatio = baseIngredient.amount;
    const exactEnchantedAmount = amount / conversionRatio;
    const roundedEnchantedAmount = Math.round(exactEnchantedAmount);

    // Check both conditions from the wiki:
    // #1: There is enough items to convert (need at least the conversion ratio)
    // #2: The rounded amount is less than 10% different than the original amount

    if (amount >= conversionRatio && roundedEnchantedAmount >= 1) {
      const difference =
        Math.abs(exactEnchantedAmount - roundedEnchantedAmount) /
        exactEnchantedAmount;

      // If the difference is less than 10%, this is a valid compaction
      if (difference < 0.1 && conversionRatio > bestRatio) {
        bestCompaction = {
          itemKey: candidateKey,
          amount: roundedEnchantedAmount,
        };
        bestRatio = conversionRatio;
      }
    }
  }

  // Return the best compaction found, or the original item if none found
  return bestCompaction || { itemKey, amount };
}

/**
 * Recursively find the base farming item and total conversion amount
 * @param {Object} allItems - All items from configManager
 * @param {string} itemKey - Current item key to trace
 * @param {number} amount - Current amount
 * @returns {Object|null} Result with baseItemKey and originalAmount, or null if not found
 */
function findBaseFarmingItem(allItems, itemKey, amount) {
  const item = allItems[itemKey];

  // If this item has sourceSkill === 'Farming', we found our base
  if (item && item.sourceSkill === "Farming") {
    return {
      baseItemKey: itemKey,
      originalAmount: amount,
    };
  }

  // If no recipe, we can't trace further
  if (!item || !item.recipes || !item.recipes[0]) {
    return null;
  }

  const recipe = item.recipes[0];

  // Look for the first ingredient that either:
  // 1. Has sourceSkill === 'Farming' (direct base item)
  // 2. Can be recursively traced to a farming item (enchanted ingredient)
  for (const ingredient of recipe.ingredients) {
    const ingredientItem = allItems[ingredient.itemKey];

    if (ingredientItem) {
      // If this ingredient is a farming item, we found our base
      if (ingredientItem.sourceSkill === "Farming") {
        return {
          baseItemKey: ingredient.itemKey,
          originalAmount: amount * ingredient.amount,
        };
      }

      // If this ingredient is also an enchanted item, recurse
      if (
        ingredient.itemKey.startsWith("ENCHANTED_") ||
        ingredient.itemKey === "POLISHED_PUMPKIN" ||
        ingredient.itemKey === "MUTANT_NETHER_WART"
      ) {
        const result = findBaseFarmingItem(
          allItems,
          ingredient.itemKey,
          amount * ingredient.amount
        );
        if (result) {
          return result;
        }
      }
    }
  }

  return null;
}

/**
 * Calculate rewards for a visitor offer
 * @param {Array} requestItems - Requested items array
 * @param {string} rarity - Visitor rarity
 * @param {number} gardenLevel - Player's Garden level
 * @param {Object} playerData - Player data for Finnegan boost check
 * @returns {Object} Reward calculation
 */
function calculateVisitorRewards(
  requestItems,
  rarity,
  gardenLevel,
  playerData = null
) {
  // Garden XP based on level and rarity
  const gardenXp = GARDEN_XP_REWARDS[Math.min(gardenLevel, 15)][rarity] || 0;

  let farmingXp = 0;
  let copper = 0;

  // Get all items once outside the loop for efficiency
  const allItems = configManager.getAllItems();

  for (const request of requestItems) {
    const itemKey = request.itemKey;
    const amount = request.amount;

    // Rewards should NOT be reduced by the crop yield multiplier.
    // Prefer metadata from the request if present (added in this change).
    let baseItemKey = request.baseCropKey || itemKey;
    let originalAmount = request.unscaledBaseAmount;

    // Backwards compatibility: if metadata is missing (older records),
    // fall back to deriving original amount from the offered item.
    if (originalAmount === undefined) {
      originalAmount = amount;

      // For enchanted items, recursively trace through recipes to find the base farming item
      if (
        itemKey.startsWith("ENCHANTED_") ||
        itemKey === "POLISHED_PUMPKIN" ||
        itemKey === "MUTANT_NETHER_WART"
      ) {
        const result = findBaseFarmingItem(allItems, itemKey, amount);
        if (result) {
          baseItemKey = result.baseItemKey;
          originalAmount = result.originalAmount;
        }
      }
    }

    // Calculate farming XP: OriginalItemAmount * BaseItemCollectionXPAmount * RarityMultiplier
    // Get the actual XP value from item data, fallback to 1 if not found
    let baseItemXP = 1; // fallback value
    const baseItem = allItems[baseItemKey];
    if (baseItem && baseItem.exp && baseItem.sourceSkill === "Farming") {
      baseItemXP = baseItem.exp;
    }

    const farmingRarityMultiplier =
      FARMING_XP_RARITY_MULTIPLIERS[rarity] || 0.05;
    farmingXp += originalAmount * baseItemXP * farmingRarityMultiplier;

    // Calculate copper using Hypixel formula: (OriginalItemAmount/2000) * ItemTypeMultiplier * RarityMultiplier * 1.3
    const itemTypeMultiplier = COPPER_ITEM_TYPE_MULTIPLIERS[baseItemKey] || 1.0;
    const copperRarityMultiplier = COPPER_RARITY_MULTIPLIERS[rarity] || 1;

    // Hypixel formula includes base 1.3 multiplier, with additional boost if Finnegan is equipped
    let finneganBoost = 1.3;
    if (playerData?.pets?.equipped?.name === "FINNEGAN") {
      finneganBoost = 1.3 * 1.1; // Additional 10% boost with Finnegan
    }

    copper +=
      (originalAmount / 2000) *
      itemTypeMultiplier *
      copperRarityMultiplier *
      finneganBoost;
  }

  return {
    gardenXp,
    farmingXp: Math.floor(farmingXp),
    copper: Math.floor(copper),
    bonusRewards: [], // TODO: Implement rare bonus items later
  };
}

/**
 * Check if player can spawn a new visitor
 * @param {string} userId - Player user ID
 * @returns {Object} Spawn check result
 */
async function checkVisitorSpawn(userId) {
  try {
    const character = await getPlayerData(userId);
    if (!character) {
      return { canSpawn: false, reason: "No character found" };
    }

    const currentTime = Date.now();
    const nextSpawnTime = character.garden_visitor_timer || 0;

    if (currentTime < nextSpawnTime) {
      return {
        canSpawn: false,
        reason: "Timer not ready",
        nextSpawnTime,
      };
    }

    // Check current active visitors
    const activeVisitors = await dbAll(
      "SELECT * FROM active_garden_visitors WHERE discord_id = ?",
      [userId]
    );

    if (activeVisitors.length >= 5) {
      return {
        canSpawn: false,
        reason: "Max visitors reached",
      };
    }

    const gardenLevel = getGardenLevel(character.garden_xp || 0);
    const availableVisitors = getAvailableVisitors(gardenLevel);

    if (availableVisitors.length === 0) {
      return {
        canSpawn: false,
        reason: "No visitors unlocked",
      };
    }

    return {
      canSpawn: true,
      gardenLevel,
      availableVisitors,
    };
  } catch (error) {
    console.error("[Garden Visitors] Error checking visitor spawn:", error);
    return { canSpawn: false, reason: "Database error" };
  }
}

/**
 * Spawn a new visitor for a player
 * @param {string} userId - Player user ID
 * @returns {Object} Spawn result
 */
async function spawnVisitor(userId) {
  try {
    const spawnCheck = await checkVisitorSpawn(userId);
    if (!spawnCheck.canSpawn) {
      return { success: false, reason: spawnCheck.reason };
    }

    // Get player data for Finnegan boost check
    const playerData = await getPlayerData(userId);

    // Get currently active visitors to avoid duplicates
    const activeVisitors = await dbAll(
      "SELECT visitor_key FROM active_garden_visitors WHERE discord_id = ?",
      [userId]
    );
    const activeVisitorKeys = activeVisitors.map((v) => v.visitor_key);

    // Filter out already active visitors
    const availableVisitors = spawnCheck.availableVisitors.filter(
      (key) => !activeVisitorKeys.includes(key)
    );

    if (availableVisitors.length === 0) {
      return {
        success: false,
        reason: "All available visitors are already active",
      };
    }

    // Select random visitor from available ones
    const randomVisitorKey =
      availableVisitors[Math.floor(Math.random() * availableVisitors.length)];

    // Get visitor rarity (for now, both are UNCOMMON, but this allows for future expansion)
    const visitor = GARDEN_VISITORS[randomVisitorKey];
    const rarity = visitor.rarity;

    // Generate request
    const requestItems = generateVisitorRequest(
      randomVisitorKey,
      rarity,
      spawnCheck.gardenLevel
    );

    // Calculate rewards with player data for Finnegan boost
    const rewards = calculateVisitorRewards(
      requestItems,
      rarity,
      spawnCheck.gardenLevel,
      playerData
    );

    // Save to database
    await dbRunQueued(
      `
            INSERT INTO active_garden_visitors 
            (discord_id, visitor_key, visitor_rarity, request_items, reward_garden_xp, reward_farming_xp, reward_copper, bonus_rewards, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
      [
        userId,
        randomVisitorKey,
        rarity,
        JSON.stringify(requestItems),
        rewards.gardenXp,
        rewards.farmingXp,
        rewards.copper,
        JSON.stringify(rewards.bonusRewards),
        Date.now(),
      ]
    );

    // Update next spawn timer
    const nextSpawnTime = Date.now() + VISITOR_SPAWN_INTERVAL;
    await savePlayerData(
      userId,
      {
        garden_visitor_timer: nextSpawnTime,
      },
      ["garden_visitor_timer"]
    );

    return {
      success: true,
      visitor: {
        key: randomVisitorKey,
        rarity,
        requestItems,
        rewards,
      },
    };
  } catch (error) {
    console.error("[Garden Visitors] Error spawning visitor:", error);
    return { success: false, reason: "Database error" };
  }
}

/**
 * Get active visitors for a player
 * @param {string} userId - Player user ID
 * @returns {Array} Active visitors
 */
async function getActiveVisitors(userId) {
  try {
    const visitors = await dbAll(
      "SELECT * FROM active_garden_visitors WHERE discord_id = ? ORDER BY created_at ASC",
      [userId]
    );

    return visitors.map((visitor) => {
      // Cache parsed JSON to avoid repeated parsing
      let requestItems, bonusRewards;
      try {
        requestItems = JSON.parse(visitor.request_items);
        bonusRewards = JSON.parse(visitor.bonus_rewards || "[]");
      } catch (parseError) {
        console.error(
          "[Garden Visitors] Error parsing visitor data:",
          parseError
        );
        requestItems = [];
        bonusRewards = [];
      }

      return {
        id: visitor.id,
        key: visitor.visitor_key,
        rarity: visitor.visitor_rarity,
        requestItems,
        rewards: {
          garden_xp: visitor.reward_garden_xp,
          farmingXp: visitor.reward_farming_xp,
          copper: visitor.reward_copper,
          bonusRewards,
        },
        createdAt: visitor.created_at,
      };
    });
  } catch (error) {
    console.error("[Garden Visitors] Error getting active visitors:", error);
    return [];
  }
}

/**
 * Reduce visitor spawn timer after farming action
 * @param {string} userId - Player user ID
 * @param {number} cropsHarvested - Number of crops harvested (defaults to 1)
 */
async function reduceFarmingTimer(userId, cropsHarvested = 1) {
  try {
    const character = await getPlayerData(userId);
    if (!character) return;

    const currentTimer =
      character.garden_visitor_timer || Date.now() + VISITOR_SPAWN_INTERVAL;
    const totalReduction = FARMING_TIME_REDUCTION * cropsHarvested;
    const newTimer = Math.max(Date.now(), currentTimer - totalReduction);

    await savePlayerData(
      userId,
      {
        garden_visitor_timer: newTimer,
      },
      ["garden_visitor_timer"]
    );

    // reduced timer for garden visitor spawn based on crops harvested
  } catch (error) {
    console.error("[Garden Visitors] Error reducing farming timer:", error);
  }
}

/**
 * Check if a player's garden visitors are at maximum capacity (5)
 * @param {string} userId - Player user ID
 * @returns {Promise<boolean>} Whether visitors are at max capacity
 */
async function areVisitorsAtMaxCapacity(userId) {
  try {
    const activeVisitors = await dbAll(
      "SELECT * FROM active_garden_visitors WHERE discord_id = ?",
      [userId]
    );
    return activeVisitors.length >= 5;
  } catch (error) {
    console.error("[Garden Visitors] Error checking visitor capacity:", error);
    return false;
  }
}

module.exports = {
  GARDEN_VISITORS,
  VISITOR_SPAWN_INTERVAL,
  FARMING_TIME_REDUCTION,
  getRandomVisitorRarity,
  getAvailableVisitors,
  generateVisitorRequest,
  calculateVisitorRewards,
  checkVisitorSpawn,
  spawnVisitor,
  getActiveVisitors,
  reduceFarmingTimer,
  tryCompactItem,
  areVisitorsAtMaxCapacity,
};
