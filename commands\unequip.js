const { EMBED_COLORS } = require("../gameConfig.js");
const {
  SlashCommandBuilder,
  EmbedBuilder,
  MessageFlags,
} = require("discord.js");
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager.js");
const configManager = require("../utils/configManager.js");
const { checkRankPermission } = require("../utils/permissionUtils");
const { unequipItemAtomically } = require("../utils/inventory.js");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager.js");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("unequip")
    .setDescription("Unequip an item, returning it to storage")
    .addStringOption((option) =>
      option
        .setName("item")
        .setDescription("The ID of the equipped item you want to unequip")
        .setRequired(true)
        .setAutocomplete(true)
    ),

  async execute(interaction) {
    const userId = interaction.user.id;
    const itemKeyToUnequip = interaction.options.getString("item");

    try {
      const currentActivity = await getCurrentActivity(userId);
      if (currentActivity) {
        return warnUserBusy(interaction, currentActivity, "unequip");
      }

      const character = await getPlayerData(userId);
      if (!character)
        return interaction.reply({
          content: "You don't have a character yet! Visit the setup channel.",
          flags: [MessageFlags.Ephemeral],
        });

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.reply({
          content:
            "You don't have permission to use this command (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const equippedItemEntry = character.inventory?.equipment?.find(
        (eq) => eq.isEquipped && eq.id === itemKeyToUnequip
      );

      if (!equippedItemEntry) {
        return interaction.reply({
          content: "Could not find the selected item in your equipment data.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const itemDef = configManager.getItem(equippedItemEntry.itemKey);
      if (!itemDef) {
        console.error(
          `[Unequip] Item definition not found for equipped item: ${equippedItemEntry.itemKey}`
        );
        return interaction.reply({
          content:
            "Error finding item definition for equipped item. Cannot unequip.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const unequipResult = await unequipItemAtomically(
        userId,
        itemKeyToUnequip
      );

      if (unequipResult.success) {
        const updatedCharacter = await getPlayerData(userId);
        await recalculateAndSaveStats(userId, updatedCharacter);

        let itemName = itemDef.name;
        if (equippedItemEntry.data_json) {
          let dataJson;
          try {
            if (typeof equippedItemEntry.data_json === "string") {
              dataJson = JSON.parse(equippedItemEntry.data_json);
            } else {
              dataJson = equippedItemEntry.data_json;
            }

            if (dataJson && dataJson.reforge) {
              const {
                getDynamicReforgeName,
              } = require("../utils/dynamicReforgeStats");
              const dynamicReforgeName = getDynamicReforgeName(
                dataJson.reforge,
                itemDef
              );
              itemName = `${dynamicReforgeName} ${itemName}`;
            }
          } catch (error) {
            console.error(
              `[Unequip Command] Error parsing data_json for item ${equippedItemEntry.id}:`,
              error
            );
          }
        }

        const unequipEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.LIGHT_YELLOW)
          //.setTitle("Item Unequipped")
          .setDescription(`Unequipped ${itemDef.emoji} **${itemName}**.`);
        await interaction.reply({ embeds: [unequipEmbed] });
      } else {
        console.warn(
          `[Unequip] Failed unequip for ${userId}, item ${itemKeyToUnequip}. Reason: ${unequipResult.message}`
        );
        await interaction.reply({
          content: `Failed to unequip: ${
            unequipResult.message ||
            "Could not confirm unequip in database. Try again?"
          }`,
          flags: [MessageFlags.Ephemeral],
        });
      }
    } catch (error) {
      console.error(
        `[Unequip] Error for user ${userId} unequipping ${itemKeyToUnequip}:`,
        error
      );
      if (interaction.replied || interaction.deferred) {
        await interaction.followUp({
          content: "[UNEQP-EXE-ERR] An unexpected error occurred.",
          flags: [MessageFlags.Ephemeral],
        });
      } else {
        await interaction.reply({
          content: "[UNEQP-EXE-ERR] An unexpected error occurred.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    }
  },

  async autocomplete(interaction) {
    try {
      const character = await getPlayerData(interaction.user.id);
      if (!character || !character.inventory?.equipment)
        return interaction.respond([]);

      const allItemsAutocomplete = configManager.getAllItems();
      const focusedValue = interaction.options.getFocused().toLowerCase();

      const equippedItems = character.inventory.equipment
        .filter((eq) => eq.isEquipped)
        .map((eq) => {
          const itemDetails = allItemsAutocomplete[eq.itemKey];
          return itemDetails
            ? { ...itemDetails, id: eq.id, itemKey: eq.itemKey }
            : null;
        })
        .filter((item) => item !== null)
        .filter(
          (item) =>
            item.name.toLowerCase().includes(focusedValue) ||
            item.id.toLowerCase().startsWith(focusedValue)
        )
        .map((item) => {
          let displayName = item.name;
          try {
            const eq = character.inventory.equipment.find(
              (e) => e.id === item.id
            );
            if (eq && eq.data_json) {
              let dataJson = {};
              if (typeof eq.data_json === "string") {
                dataJson = JSON.parse(eq.data_json);
              } else {
                dataJson = eq.data_json;
              }

              if (dataJson.reforge) {
                const {
                  getDynamicReforgeName,
                } = require("../utils/dynamicReforgeStats");
                const dynamicReforgeName = getDynamicReforgeName(
                  dataJson.reforge,
                  item
                );
                displayName = `${dynamicReforgeName} ${item.name}`;
              }
            }
          } catch (error) {
            console.error(
              "[Unequip Autocomplete] Error parsing reforge data:",
              error
            );
          }

          return {
            name: `${displayName} (${item.id.slice(0, 4)})`,
            value: item.id,
          };
        })
        .slice(0, 25);

      await interaction.respond(equippedItems);
    } catch (autocompleteError) {
      console.error("[Unequip Autocomplete Error]:", autocompleteError);
      await interaction.respond([]).catch(() => {});
    }
  },
};
