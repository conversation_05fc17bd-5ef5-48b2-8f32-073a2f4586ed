const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  Embed<PERSON>uilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
  StringSelectMenuBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
} = require("discord.js");
const {
  getPlayerData,
  savePlayerData,
  calculateMaxMinionSlots,
} = require("../utils/playerDataManager");
const { updateInventoryAtomically } = require("../utils/inventory");
const configManager = require("../utils/configManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const { addCollectionProgress } = require("../utils/collectionUtils");
const { checkAndNotifyDisblockXP } = require("../utils/disblockXpSystem");
const {
  BASE_MINION_SLOTS,
  MINION_SLOT_UNLOCKS,
  EMBED_COLORS,
} = require("../gameConfig");

const {
  simulateMinionGeneration,
  formatMinionStorageDisplay,
} = require("../utils/newMinionGeneration");
const { formatNumber } = require("../utils/displayUtils");

function getMinionDisplayInfo(minionData) {
  const baseKey = minionData.itemKey;
  const tier = minionData.tier;

  const allItems = configManager.getAllItems();
  const baseMinion = allItems[baseKey];

  if (!baseMinion || baseMinion.type !== "MINION") {
    console.warn(
      `[getMinionDisplayInfo] Could not find base minion definition for key: ${baseKey}`
    );
    return { name: "Unknown Minion", emoji: "❓" };
  }

  const resourceItem = allItems[baseMinion.resourceItemKey];

  const emoji = baseMinion.emoji || resourceItem?.emoji || "❓";
  const name = `[T${tier}] ${baseMinion.name}`;
  return { name, emoji };
}

async function calculateMinionGeneration(minion) {
  const allItems = configManager.getAllItems();

  const result = simulateMinionGeneration(minion, allItems);

  return {
    generated: result.generated,
    effectiveTimestamp: minion.lastCollectionTimestamp || Date.now(),
  };
}

function createMinionViewEmbedAndComponents(minion) {
  const { ActionRowBuilder, StringSelectMenuBuilder } = require("discord.js");
  const {
    formatMinionStorageDisplay,
  } = require("../utils/newMinionGeneration");

  const displayInfo = getMinionDisplayInfo(minion);
  const baseKey = minion.itemKey;
  const baseMinion = configManager.getAllItems()[baseKey];
  const tierData = baseMinion?.tiers?.[minion.tier];
  const baseInterval = tierData?.generationIntervalSeconds ?? "?";
  const baseStorage = tierData?.maxStorage ?? 0;
  const extraStorage = getExtraStorageFromUpgrades(
    minion,
    configManager.getAllItems()
  );
  const maxStorage = baseStorage + extraStorage;

  let speedDisplay = `${baseInterval}s per item`;
  if (
    minion.fuel &&
    minion.fuel.expiresAt &&
    Date.now() < minion.fuel.expiresAt &&
    baseInterval !== "?"
  ) {
    const fueledSpeed = baseInterval / (1 + minion.fuel.speedBoost);
    const fueledSpeedFormatted =
      fueledSpeed % 1 === 0 ? fueledSpeed.toString() : fueledSpeed.toFixed(2);
    speedDisplay = `${fueledSpeedFormatted}s per item (${baseInterval}s without fuel)`;
  }

  const upgrades = (minion.upgrades || [])
    .map((u, i) => {
      if (!u) return `Slot ${i + 1}: *(Empty)*`;
      const item = configManager.getAllItems()[u];
      const emoji = item?.emoji ? `${item.emoji} ` : "";
      return `Slot ${i + 1}: ${emoji}**${item?.name || u}**`;
    })
    .join("\n");

  const storedDisplay = formatMinionStorageDisplay(
    minion,
    configManager.getAllItems()
  );

  // Format fuel status
  let fuelStatus = "No fuel active";
  if (
    minion.fuel &&
    minion.fuel.expiresAt &&
    Date.now() < minion.fuel.expiresAt
  ) {
    const speedBoostPercent = Math.round(minion.fuel.speedBoost * 100);
    const expiresTimestamp = Math.floor(minion.fuel.expiresAt / 1000);
    const fuelType = minion.fuel.fuelType;
    const fuelItem = fuelType ? configManager.getAllItems()[fuelType] : null;
    const fuelName = fuelItem?.name || "Unknown Fuel";
    const fuelEmoji = fuelItem?.emoji || "⚡";
    const quantity = minion.fuel.quantity || 1;

    // Format quantity for user-friendly display (always show whole numbers, floor to show complete units)
    const displayQuantity = Math.floor(quantity);

    fuelStatus = `**Type:** ${fuelEmoji} \`x${displayQuantity} ${fuelName}\`\n**Boost:** +${speedBoostPercent}% speed\n**Expires:** <t:${expiresTimestamp}:R>`;
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.SKY_BLUE)
    .setTitle(`${displayInfo.emoji} ${displayInfo.name}`)
    .setDescription(
      `**Tier:** T${minion.tier}\n**Speed:** \`${speedDisplay}\`\n**Storage:** \`${maxStorage}\` items`
    )
    .addFields([
      { name: "Upgrades", value: upgrades || "None", inline: false },
      { name: "Resources Stored", value: storedDisplay, inline: false },
      { name: "Fuel Status", value: fuelStatus, inline: false },
    ]);

  // Create action select menu instead of buttons
  const actionOptions = [
    {
      label: "Collect",
      description: "Collect all stored items in this minion",
      value: "collect",
      emoji: "📦",
    },
    {
      label: "Upgrade",
      description: "Upgrade this minion to the next tier",
      value: "upgrade",
      emoji: "<:skill_crafting:1367753356331716659>",
    },
    {
      label: "Add Fuel",
      description: "Add fuel to boost this minion's speed",
      value: "add_fuel",
      emoji: "<:enchanted_coal:1375851329028755476>",
    },
    {
      label: "Clear Fuel",
      description: "Remove all fuel from this minion",
      value: "clear_fuel",
      emoji: "❌",
    },
    {
      label: "Manage Upgrades",
      description: "Add, remove, or swap minion upgrades",
      value: "manage_upgrades",
      emoji: "<:compactor:1376095584561397800>",
    },
  ];

  const actionSelectMenu = new StringSelectMenuBuilder()
    .setCustomId(`minion_action_${minion.id}`)
    .setPlaceholder("Choose an action for this minion")
    .addOptions(actionOptions);

  const actionRow = new ActionRowBuilder().addComponents(actionSelectMenu);

  return {
    embed,
    components: [actionRow],
  };
}

// Helper to initialize minion upgrades array
function initializeMinionUpgrades(minion) {
  if (!Array.isArray(minion.upgrades)) {
    minion.upgrades = [null, null]; // 2 upgrade slots, can be expanded later
  } else if (minion.upgrades.length < 2) {
    // Pad to 2 slots if needed
    while (minion.upgrades.length < 2) minion.upgrades.push(null);
  }
  return minion;
}

// Helper to sum extra storage from equipped upgrades (storage upgrades stack)
function getExtraStorageFromUpgrades(minion, allItems) {
  if (!Array.isArray(minion.upgrades)) return 0;

  let totalStorageBonus = 0;
  for (const upgradeKey of minion.upgrades) {
    if (!upgradeKey) continue;
    const upgradeItem = allItems[upgradeKey];
    if (
      upgradeItem &&
      upgradeItem.upgradeEffect &&
      typeof upgradeItem.upgradeEffect.extraStorage === "number"
    ) {
      // Storage upgrades stack - add them all together
      totalStorageBonus += upgradeItem.upgradeEffect.extraStorage;
    }
  }
  return totalStorageBonus;
}

// Helper to validate hopper upgrades (only one hopper allowed per minion)
function validateHopperUpgrades(
  minion,
  allItems,
  newUpgradeKey = null,
  slotIndex = -1
) {
  if (!Array.isArray(minion.upgrades)) return { valid: true };

  const hopperUpgrades = ["BUDGET_HOPPER", "ENCHANTED_HOPPER"];
  let hopperCount = 0;
  let existingHopperSlot = -1;

  // Check existing upgrades
  for (let i = 0; i < minion.upgrades.length; i++) {
    const upgradeKey = minion.upgrades[i];
    if (upgradeKey && hopperUpgrades.includes(upgradeKey)) {
      hopperCount++;
      existingHopperSlot = i;
    }
  }

  // If adding a new hopper upgrade
  if (newUpgradeKey && hopperUpgrades.includes(newUpgradeKey)) {
    // If we're replacing an existing hopper in the same slot, that's fine
    if (slotIndex === existingHopperSlot) {
      return { valid: true };
    }
    // If there's already a hopper in a different slot, not allowed
    if (hopperCount > 0) {
      return {
        valid: false,
        message:
          "Only one hopper per minion can be placed in the upgrade slots. Remove the existing hopper first.",
      };
    }
  }

  return { valid: true };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("minions")
    .setDescription("Manage your minions.")
    .addSubcommand((subcommand) =>
      subcommand
        .setName("list")
        .setDescription("View minions in your storage.")
        .addIntegerOption((option) =>
          option
            .setName("page")
            .setDescription("Page number to view")
            .setRequired(false)
            .setMinValue(1)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("place")
        .setDescription("Place a minion from storage onto your island.")
        .addStringOption(
          (
            option // Add ID option *to the place subcommand*
          ) =>
            option
              .setName("id")
              .setDescription("The ID of the minion in storage to place")
              .setRequired(true)
              .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("view")
        .setDescription("View minions currently placed on your island.")
        .addStringOption((option) =>
          option
            .setName("id")
            .setDescription("The ID of the placed minion to view (optional)")
            .setRequired(false)
            .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("collect")
        .setDescription("Collect resources from a specific placed minion.")
        .addStringOption((option) =>
          option
            .setName("id")
            .setDescription("The ID of the placed minion to collect from")
            .setRequired(true)
            .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("upgrade")
        .setDescription(
          "Upgrade a minion (placed or in storage) to the next tier."
        )
        .addStringOption((option) =>
          option
            .setName("id")
            .setDescription(
              "The ID of the minion (placed or storage) to upgrade"
            )
            .setRequired(true)
            .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("remove")
        .setDescription("Remove a placed minion and return it to storage.")
        .addStringOption((option) =>
          option
            .setName("id")
            .setDescription("The ID of the placed minion to remove")
            .setRequired(true)
            .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("collectall")
        .setDescription("Collect resources from ALL placed minions at once.")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("butcher")
        .setDescription(
          "Permanently delete a minion from storage or your island!"
        )
        .addStringOption((option) =>
          option
            .setName("id")
            .setDescription("The ID of the minion to butcher (irreversible!)")
            .setRequired(true)
            .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("slots")
        .setDescription("View your unique minion crafts and slot unlocks.")
    ),
  async execute(interaction) {
    const userId = interaction.user.id;

    const character = await getPlayerData(userId);

    if (!character) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR) // Red color for errors
        .setTitle("Character Not Found")
        .setDescription(
          "You need to create a character first in the setup channel."
        );
      return interaction.reply({ embeds: [embed], ephemeral: true });
    }
    if (!checkRankPermission(character, "MEMBER")) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR) // Red color for errors
        .setTitle("Permission Denied")
        .setDescription(
          "You don't have permission to use this command (Rank Error)."
        );
      return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Get subcommand name for routing
    const subcommand = interaction.options.getSubcommand();

    // Update Disblock XP based on current progress (but not for view command)
    // XP notifications will be handled after data is saved for upgrade subcommand

    // Apply island check conditionally
    // Allow upgrade command to proceed (it will handle island checks internally for placed vs stored minions)
    if (
      !["slots", "list", "upgrade"].includes(subcommand) &&
      (character.current_region !== "Private Island" ||
        character.visiting_island_owner_id !== null)
    ) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.YELLOW) // Yellow color for warnings
        //        .setTitle("Island Required")
        .setDescription("You must be on your **Private Island** to do this.");
      return interaction.reply({ embeds: [embed] });
    }

    // Route based on subcommand name
    if (subcommand === "list") {
      await handleList(interaction, character);
    } else if (subcommand === "place") {
      await handlePlace(interaction, character); // ID is retrieved within the handler now
    } else if (subcommand === "view") {
      await handleView(interaction, character);
    } else if (subcommand === "collect") {
      await handleCollect(interaction);
    } else if (subcommand === "upgrade") {
      await handleUpgrade(interaction);
    } else if (subcommand === "remove") {
      await handleRemove(interaction);
    } else if (subcommand === "collectall") {
      await handleCollectAll(interaction);
    } else if (subcommand === "butcher") {
      await handleButcher(interaction);
    } else if (subcommand === "slots") {
      await handleSlots(interaction);
    } else {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR) // Red color for errors
        .setTitle("Unknown Subcommand")
        .setDescription(
          `[MIN-EXE-004] Unknown minions subcommand: \`${subcommand}\`. Please report this error code to an Admin.`
        );
      await interaction.reply({ embeds: [embed], ephemeral: true });
    }
  },
  async autocomplete(interaction) {
    const subcommand = interaction.options.getSubcommand(false); // Get subcommand context
    const focusedOption = interaction.options.getFocused(true);
    let choices = [];

    // Fetch only necessary minion data for autocomplete using proper field mapping
    const userId = interaction.user.id;
    const { getPlayerData } = require("../utils/playerDataManager");

    let minionData;
    let playerData;
    try {
      const character = await getPlayerData(userId, [
        "minionStorage",
        "island",
        "current_region",
        "visiting_island_owner_id",
      ]);
      if (!character) {
        // No player data found, respond with empty choices
        await interaction.respond([]);
        return;
      }
      minionData = {
        minionStorage: character.minionStorage || [],
        island: character.island || {},
      };
      playerData = {
        current_region: character.current_region,
        visiting_island_owner_id: character.visiting_island_owner_id,
      };
    } catch (error) {
      console.error(
        "[Minions Autocomplete] Error fetching minion data:",
        error
      );
      // On error, respond with empty choices
      try {
        await interaction.respond([]);
      } catch (respondError) {
        if (
          respondError.code !== 10062 &&
          respondError.message !== "Unknown Interaction"
        ) {
          console.error(
            "[Minions Autocomplete] Error responding after fetch error:",
            respondError
          );
        }
      }
      return;
    }

    // Autocomplete for the 'id' option *within the place subcommand*
    if (subcommand === "place" && focusedOption.name === "id") {
      const focusedValue = focusedOption.value.toLowerCase();
      const storedMinions = minionData.minionStorage || [];
      choices = storedMinions
        .filter((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          const minionName = displayInfo.name.toLowerCase();
          const minionId = minion.id.toLowerCase();
          // Match either minion name or ID
          return (
            minionName.includes(focusedValue) ||
            minionId.startsWith(focusedValue)
          );
        })
        .map((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          // Suggestion format remains the same (no emoji)
          return {
            name: `${displayInfo.name} (Storage - ID: ${minion.id.substring(0, 8)}...)`,
            value: minion.id,
          };
        })
        .slice(0, 25);
    }
    // Autocomplete for the 'id' option within the view subcommand (placed minions only)
    else if (subcommand === "view" && focusedOption.name === "id") {
      const placedMinions = minionData.island?.placedMinions || [];
      const focusedValue = focusedOption.value.toLowerCase();
      choices = placedMinions
        .filter((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          const minionName = displayInfo.name.toLowerCase();
          const minionId = minion.id.toLowerCase();
          // Match either minion name or ID
          return (
            minionName.includes(focusedValue) ||
            minionId.startsWith(focusedValue)
          );
        })
        .map((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          return {
            name: `${displayInfo.name} (ID: ${minion.id.substring(0, 8)}...)`,
            value: minion.id,
          };
        })
        .slice(0, 25);
    }
    // Autocomplete for upgrade subcommand with special logic
    else if (subcommand === "upgrade" && focusedOption.name === "id") {
      const placedMinions = minionData.island?.placedMinions || [];
      const storedMinions = minionData.minionStorage || [];
      const focusedValue = focusedOption.value.toLowerCase();

      // Check if player is on their private island
      const isOnPrivateIsland =
        playerData.current_region === "Private Island" &&
        playerData.visiting_island_owner_id === null;

      const combinedMinions = [];

      if (isOnPrivateIsland) {
        // On Private Island: Show placed minions first, then stored minions
        placedMinions.forEach((minion) =>
          combinedMinions.push({ ...minion, location: "Placed" })
        );
        storedMinions.forEach((minion) =>
          combinedMinions.push({ ...minion, location: "Storage" })
        );
      } else {
        // Not on Private Island: Show stored minions only
        storedMinions.forEach((minion) =>
          combinedMinions.push({ ...minion, location: "Storage" })
        );
      }

      choices = combinedMinions
        .filter((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          const minionName = displayInfo.name.toLowerCase();
          const minionId = minion.id.toLowerCase();
          const baseMinion = configManager.getAllItems()[minion.itemKey];
          const canUpgrade =
            baseMinion &&
            baseMinion.tiers &&
            baseMinion.tiers[minion.tier + 1] !== undefined;
          // Match either minion name or ID
          return (
            canUpgrade &&
            (minionName.includes(focusedValue) ||
              minionId.startsWith(focusedValue))
          );
        })
        .map((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          const label = `(${minion.location} - ID: ${minion.id.substring(0, 8)}...)`;
          return { name: `${displayInfo.name} ${label}`, value: minion.id };
        })
        .slice(0, 25);
    }
    // Autocomplete for placed OR stored (butcher)
    // Autocomplete for placed ONLY (collect, remove)
    else if (
      ["collect", "remove", "butcher"].includes(subcommand) &&
      focusedOption.name === "id"
    ) {
      const placedMinions = minionData.island?.placedMinions || [];
      const storedMinions = minionData.minionStorage || [];
      const focusedValue = focusedOption.value.toLowerCase();

      const combinedMinions = [];
      // Include stored minions only if butchering
      if (["butcher"].includes(subcommand)) {
        storedMinions.forEach((minion) =>
          combinedMinions.push({ ...minion, location: "Storage" })
        );
      }
      // Always include placed minions
      placedMinions.forEach((minion) =>
        combinedMinions.push({ ...minion, location: "Placed" })
      );

      choices = combinedMinions
        .filter((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          const minionName = displayInfo.name.toLowerCase();
          const minionId = minion.id.toLowerCase();
          // Match either minion name or ID
          return (
            minionName.includes(focusedValue) ||
            minionId.startsWith(focusedValue)
          );
        })
        .map((minion) => {
          const displayInfo = getMinionDisplayInfo(minion);
          let label = `(${minion.location} - ID: ${minion.id.substring(0, 8)}...)`;
          // Add stored amount hint for specific subcommands when placed
          if (
            minion.location === "Placed" &&
            ["collect", "remove"].includes(subcommand)
          ) {
            // Need base item definition to find resource key
            const allItems = configManager.getAllItems();
            const baseMinionDef = allItems[minion.itemKey]; // Use itemKey
            const resourceKey = baseMinionDef?.resourceItemKey;
            const storedAmount = minion.resourcesStored?.[resourceKey] || 0;
            label = `(${storedAmount}) ${label}`; // Prepend stored amount
          }
          return { name: `${displayInfo.name} ${label}`, value: minion.id };
        })
        .slice(0, 25);
    }

    // Moved respond logic here, outside the specific subcommand blocks but inside the main function
    try {
      await interaction.respond(choices);
    } catch (error) {
      if (error.code !== 10062 && error.message !== "Unknown Interaction") {
        // Ignore common Discord API errors for autocomplete
        console.error("[Minions Autocomplete] Error responding:", error);
      }
    }
  }, // End autocomplete function body
}; // End module.exports

// Add helper functions to the exports for testing
module.exports.getMinionDisplayInfo = getMinionDisplayInfo;
module.exports.calculateMinionGeneration = calculateMinionGeneration;
module.exports.validateHopperUpgrades = validateHopperUpgrades;
module.exports.getExtraStorageFromUpgrades = getExtraStorageFromUpgrades;
module.exports.initializeMinionUpgrades = initializeMinionUpgrades;

// Function to show fuel selection menu
async function showFuelMenu(interaction, character, minion) {
  const { getInventory } = require("../utils/inventory");
  const allItems = configManager.getAllItems();

  // Stop any existing fuel menu collectors for this user/minion
  const userId = interaction.user.id;
  const fuelCollectorKey = `fuel_${userId}_${minion.id}`;
  if (activeCollectors.has(fuelCollectorKey)) {
    const existingCollector = activeCollectors.get(fuelCollectorKey);
    existingCollector.stop("new_fuel_menu_opened");
    activeCollectors.delete(fuelCollectorKey);
  }

  // Get player's inventory
  const inventoryData = await getInventory(character.discordId);
  const inventory = inventoryData.items;

  // Check if minion already has fuel and get the fuel type
  let existingFuelType = null;
  if (
    minion.fuel &&
    minion.fuel.expiresAt &&
    Date.now() < minion.fuel.expiresAt &&
    minion.fuel.fuelType
  ) {
    existingFuelType = minion.fuel.fuelType;
  }

  // If minion already has fuel, show quantity modal directly
  if (existingFuelType) {
    const fuelData = allItems[existingFuelType];
    if (
      !fuelData ||
      !inventory[existingFuelType] ||
      inventory[existingFuelType] < 1
    ) {
      // Defer the interaction before using editReply
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate();
      }
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("No Fuel Available")
        .setDescription(
          `You don't have any more **${fuelData?.name || "fuel"}** to add.`
        );
      return interaction.editReply({ embeds: [embed], components: [] });
    }

    // Show quantity modal directly
    const maxQuantity = Math.min(inventory[existingFuelType], 64);
    const modal = new ModalBuilder()
      .setCustomId(`fuel_quantity_${minion.id}_${existingFuelType}`)
      .setTitle(`Add ${fuelData.name}`);

    const quantityInput = new TextInputBuilder()
      .setCustomId("quantity")
      .setLabel("Quantity")
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(`1-${maxQuantity}`)
      .setValue("1")
      .setMinLength(1)
      .setMaxLength(2)
      .setRequired(true);

    const firstActionRow = new ActionRowBuilder().addComponents(quantityInput);
    modal.addComponents(firstActionRow);

    // Handle modal submission for direct fuel quantity
    const handleDirectFuelModal = async (modalInteraction) => {
      if (
        !modalInteraction.isModalSubmit() ||
        modalInteraction.customId !==
          `fuel_quantity_${minion.id}_${existingFuelType}`
      )
        return;

      // Remove the event listener immediately to prevent duplicate handling
      interaction.client.off("interactionCreate", handleDirectFuelModal);

      try {
        await modalInteraction.deferUpdate();
      } catch {
        console.log(
          "[DEBUG] Modal interaction already expired, using followUp instead"
        );
        // If the modal interaction is expired, we can't defer it
        // We'll handle this case below
      }

      const quantity = parseInt(
        modalInteraction.fields.getTextInputValue("quantity")
      );

      // Get current inventory for validation
      const { getInventory } = require("../utils/inventory");
      const inventoryData = await getInventory(character.discordId);
      const currentInventory = inventoryData.items;

      // Validate quantity
      if (
        isNaN(quantity) ||
        quantity < 1 ||
        quantity > currentInventory[existingFuelType] ||
        quantity > 64
      ) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("Invalid Quantity")
          .setDescription(
            `Please enter a valid quantity between 1 and ${Math.min(currentInventory[existingFuelType], 64)}.`
          );
        return modalInteraction.editReply({
          embeds: [errorEmbed],
          components: [],
        });
      }

      // Apply fuel to minion
      const { updateInventoryAtomically } = require("../utils/inventory");
      const { savePlayerData } = require("../utils/playerDataManager");

      // Remove fuel from inventory
      await updateInventoryAtomically(character.discordId, 0, [
        { itemKey: existingFuelType, amount: -quantity },
      ]);

      // Process any accumulated generation BEFORE adding fuel to preserve timing
      const {
        simulateMinionGeneration,
      } = require("../utils/newMinionGeneration");
      // Process accumulated generation before adding fuel
      simulateMinionGeneration(minion, allItems);

      // Add fuel to minion (extend duration)
      const currentTime = Date.now();
      const durationMs =
        fuelData.minionFuel.durationMinutes * 60 * 1000 * quantity;
      const currentExpiresAt = minion.fuel?.expiresAt || currentTime;

      minion.fuel = {
        speedBoost: fuelData.minionFuel.speedBoost,
        expiresAt: Math.max(currentTime, currentExpiresAt) + durationMs,
        fuelType: existingFuelType,
        quantity: (minion.fuel?.quantity || 0) + quantity,
      };

      // Update timestamp - this is now safe because we processed accumulated generation above
      minion.lastCollectionTimestamp = currentTime;

      // Save character data
      await savePlayerData(interaction.user.id, character);

      // Update the minion view directly instead of calling handleView
      const { getPlayerData } = require("../utils/playerDataManager");
      const updatedCharacter = await getPlayerData(interaction.user.id);
      const updatedMinion = updatedCharacter.island?.placedMinions?.find(
        (m) => m.id === minion.id
      );

      if (updatedMinion) {
        // Use the unified function to create embed and components
        const { embed, components } =
          createMinionViewEmbedAndComponents(updatedMinion);

        // Try to update the interaction, fallback to original interaction if modal is expired
        try {
          if (modalInteraction.deferred || modalInteraction.replied) {
            await modalInteraction.editReply({ embeds: [embed], components });
          } else {
            await modalInteraction.update({ embeds: [embed], components });
          }
        } catch {
          console.log(
            "[DEBUG] Modal interaction failed, updating original interaction instead"
          );
          // If modal interaction fails, update the original interaction
          try {
            if (interaction.deferred || interaction.replied) {
              await interaction.editReply({ embeds: [embed], components });
            } else {
              await interaction.update({ embeds: [embed], components });
            }
          } catch {
            // Both interactions failed, sending followup
            await interaction.followUp({
              embeds: [embed],
              components,
              ephemeral: true,
            });
          }
        }
      }
    };

    // Set up listener for modal submissions
    interaction.client.on("interactionCreate", handleDirectFuelModal);

    // Set a timeout to clean up the event listener if modal isn't submitted
    setTimeout(() => {
      interaction.client.off("interactionCreate", handleDirectFuelModal);
    }, 300000); // 5 minutes timeout

    // Check if interaction is still valid before showing modal
    if (!interaction.deferred && !interaction.replied) {
      return await interaction.showModal(modal);
    } else {
      // If interaction is already used, we can't show a modal
      // This can happen when the button is pressed multiple times quickly
      // Cannot show modal - interaction already used
      // Clean up the event listener since we're not showing the modal
      interaction.client.off("interactionCreate", handleDirectFuelModal);
      return;
    }
  }

  // Find fuel items in inventory
  const fuelItems = [];
  for (const [itemKey, amount] of Object.entries(inventory)) {
    const itemData = allItems[itemKey];
    if (itemData && itemData.minionFuel && amount > 0) {
      fuelItems.push({
        itemKey,
        itemData,
        amount,
      });
    }
  }

  if (fuelItems.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("No Fuel Available")
      .setDescription(
        "You don't have any fuel items in your inventory.\n\nFuel items: Coal, Block of Coal, Enchanted Coal"
      );
    return interaction.editReply({ embeds: [embed], components: [] });
  }

  // Create select menu for fuel items
  const options = fuelItems.map((fuel) => {
    const speedBoostPercent = Math.round(
      fuel.itemData.minionFuel.speedBoost * 100
    );
    const durationHours = Math.floor(
      fuel.itemData.minionFuel.durationMinutes / 60
    );
    const durationMins = fuel.itemData.minionFuel.durationMinutes % 60;

    let durationText = "";
    if (durationHours > 0) {
      durationText =
        durationMins > 0
          ? `${durationHours}h ${durationMins}m`
          : `${durationHours}h`;
    } else {
      durationText = `${durationMins}m`;
    }

    return {
      label: `${fuel.itemData.name} (${fuel.amount})`,
      description: `+${speedBoostPercent}% speed for ${durationText}`,
      value: fuel.itemKey,
      emoji: fuel.itemData.emoji,
    };
  });

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId(`fuel_select_${minion.id}`)
    .setPlaceholder("Choose a fuel to add")
    .addOptions(options);

  const row = new ActionRowBuilder().addComponents(selectMenu);

  let title = "Add Fuel to Minion";
  let description =
    "Select a fuel item from your inventory to boost this minion's speed.";
  if (existingFuelType) {
    const existingFuelData = allItems[existingFuelType];
    title = "Add More Fuel";
    description = `This minion has **${existingFuelData?.name || existingFuelType}** fuel active.\nSelect the same fuel type to extend the duration.`;
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.SKY_BLUE)
    .setTitle(title)
    .setDescription(description);

  // Defer the interaction if it hasn't been deferred yet (for fuel selection menu)
  if (!existingFuelType) {
    await interaction.deferUpdate();
  }

  await interaction.editReply({ embeds: [embed], components: [row] });

  // Set up collector for fuel selection
  const filter = (i) =>
    i.user.id === interaction.user.id &&
    i.customId.startsWith(`fuel_select_${minion.id}`);
  const collector = interaction.message.createMessageComponentCollector({
    filter,
    componentType: ComponentType.StringSelect,
  }); // No timeout - collector stays active indefinitely

  // Store the collector for cleanup
  activeCollectors.set(fuelCollectorKey, collector);

  // Clean up collector reference when it ends
  collector.on("end", () => {
    activeCollectors.delete(fuelCollectorKey);
  });

  collector.on("collect", async (i) => {
    const selectedFuelKey = i.values[0];
    const fuelData = allItems[selectedFuelKey];

    if (!fuelData || !fuelData.minionFuel) {
      // Check if interaction is already acknowledged before deferring
      if (!i.replied && !i.deferred) {
        await i.deferUpdate();
      }
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Invalid Fuel")
        .setDescription("Selected item is not a valid fuel.");
      return i.editReply({ embeds: [errorEmbed], components: [] });
    }

    // Check if player has the fuel item
    if (!inventory[selectedFuelKey] || inventory[selectedFuelKey] < 1) {
      // Check if interaction is already acknowledged before deferring
      if (!i.replied && !i.deferred) {
        await i.deferUpdate();
      }
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Insufficient Fuel")
        .setDescription("You don't have enough of this fuel item.");
      return i.editReply({ embeds: [errorEmbed], components: [] });
    }

    // Show quantity selection modal
    const maxQuantity = Math.min(inventory[selectedFuelKey], 64); // Limit to 64 or available amount
    const modal = new ModalBuilder()
      .setCustomId(`fuel_quantity_${minion.id}_${selectedFuelKey}`)
      .setTitle(`Add ${fuelData.name}`);

    const quantityInput = new TextInputBuilder()
      .setCustomId("quantity")
      .setLabel("Quantity")
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(`1-${maxQuantity}`)
      .setValue("1")
      .setMinLength(1)
      .setMaxLength(2)
      .setRequired(true);

    const firstActionRow = new ActionRowBuilder().addComponents(quantityInput);
    modal.addComponents(firstActionRow);

    await i.showModal(modal);
  });

  // Handle modal submission for fuel quantity
  const handleModalSubmit = async (modalInteraction) => {
    if (
      !modalInteraction.isModalSubmit() ||
      !modalInteraction.customId.startsWith(`fuel_quantity_${minion.id}`)
    )
      return;

    // Remove the event listener immediately to prevent duplicate handling
    interaction.client.off("interactionCreate", handleModalSubmit);

    try {
      await modalInteraction.deferUpdate();
    } catch {
      console.log(
        "[DEBUG] Modal interaction already expired in handleModalSubmit"
      );
      // If the modal interaction is expired, we can't defer it
      // We'll handle this case below
    }

    const selectedFuelKey = modalInteraction.customId
      .split("_")
      .slice(3)
      .join("_");
    const quantity = parseInt(
      modalInteraction.fields.getTextInputValue("quantity")
    );
    const fuelData = allItems[selectedFuelKey];

    // Get current inventory for validation
    const { getInventory } = require("../utils/inventory");
    const inventoryData = await getInventory(character.discordId);
    const currentInventory = inventoryData.items;

    // Validate quantity
    if (
      isNaN(quantity) ||
      quantity < 1 ||
      quantity > currentInventory[selectedFuelKey] ||
      quantity > 64
    ) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Invalid Quantity")
        .setDescription(
          `Please enter a valid quantity between 1 and ${Math.min(currentInventory[selectedFuelKey], 64)}.`
        );
      return modalInteraction.editReply({
        embeds: [errorEmbed],
        components: [],
      });
    }

    // Apply fuel to minion
    const { updateInventoryAtomically } = require("../utils/inventory");
    const { savePlayerData } = require("../utils/playerDataManager");

    // Remove fuel from inventory
    await updateInventoryAtomically(character.discordId, 0, [
      { itemKey: selectedFuelKey, amount: -quantity },
    ]);

    // Process any accumulated generation BEFORE adding fuel to preserve timing
    const {
      simulateMinionGeneration,
    } = require("../utils/newMinionGeneration");
    // Process accumulated generation before adding fuel
    simulateMinionGeneration(minion, allItems);

    // Add fuel to minion (extend duration if fuel already exists)
    const currentTime = Date.now();
    const durationMs =
      fuelData.minionFuel.durationMinutes * 60 * 1000 * quantity;
    const currentExpiresAt = minion.fuel?.expiresAt || currentTime;

    minion.fuel = {
      speedBoost: fuelData.minionFuel.speedBoost,
      expiresAt: Math.max(currentTime, currentExpiresAt) + durationMs,
      fuelType: selectedFuelKey,
      quantity: (minion.fuel?.quantity || 0) + quantity,
    };

    // Update timestamp - this is now safe because we processed accumulated generation above
    minion.lastCollectionTimestamp = currentTime;

    // Save character data
    await savePlayerData(interaction.user.id, character);

    // Create a mock interaction with the minion ID to call handleView
    const { getPlayerData } = require("../utils/playerDataManager");
    const updatedCharacter = await getPlayerData(interaction.user.id);

    // Create a robust mock interaction that can handle expired modal interactions
    const createRobustReply = async (content) => {
      try {
        if (modalInteraction.deferred || modalInteraction.replied) {
          return await modalInteraction.editReply(content);
        } else {
          return await modalInteraction.update(content);
        }
      } catch {
        console.log(
          "[DEBUG] Modal interaction failed in handleModalSubmit, using original interaction"
        );
        try {
          if (interaction.deferred || interaction.replied) {
            return await interaction.editReply(content);
          } else {
            return await interaction.update(content);
          }
        } catch {
          console.log(
            "[DEBUG] Both interactions failed in handleModalSubmit, using followup"
          );
          return await interaction.followUp({ ...content, ephemeral: true });
        }
      }
    };

    const mockInteraction = {
      ...modalInteraction,
      options: {
        getString: (key) => (key === "id" ? minion.id : null),
      },
      reply: createRobustReply,
      editReply: createRobustReply,
      fetchReply: modalInteraction.fetchReply
        ? modalInteraction.fetchReply.bind(modalInteraction)
        : async () => modalInteraction.message,
    };

    await handleView(mockInteraction, updatedCharacter);

    // Restart the collector after fuel is successfully added to keep buttons active
    // Find the original interaction's message and restart its collector
    try {
      const originalMessage = await interaction.fetchReply();
      if (
        originalMessage &&
        originalMessage.components &&
        originalMessage.components.length > 0
      ) {
        // The collector will be restarted by the handleView function's setupCollector
      }
    } catch (error) {
      console.log(
        "[DEBUG] Could not restart collector after fuel addition:",
        error.message
      );
    }
  };

  // Set up listener for modal submissions
  interaction.client.on("interactionCreate", handleModalSubmit);

  // Set a timeout to clean up the event listener if modal isn't submitted
  setTimeout(() => {
    interaction.client.off("interactionCreate", handleModalSubmit);
  }, 300000); // 5 minutes timeout

  // Clean up the modal submit listener when the collector ends
  collector.on("end", async (collected, reason) => {
    // Clean up the modal submit event listener
    interaction.client.off("interactionCreate", handleModalSubmit);

    // Remove from active collectors
    activeCollectors.delete(fuelCollectorKey);

    // If the collector ended due to timeout or cancellation (not because an item was selected),
    // restore the original minion view
    if (reason === "time" && collected.size === 0) {
      try {
        // Get fresh character data
        const freshCharacter = await getPlayerData(interaction.user.id);
        const updatedMinion = freshCharacter?.island?.placedMinions?.find(
          (m) => m.id === minion.id
        );

        if (updatedMinion) {
          const { embed, components } =
            createMinionViewEmbedAndComponents(updatedMinion);

          try {
            await interaction.editReply({ embeds: [embed], components });

            // Set up new main collector after restoring view
            const mainCollectorKey = `main_${interaction.user.id}_${updatedMinion.id}`;
            const sentMessage = await interaction.fetchReply();
            const mainFilter = (mainI) =>
              mainI.user.id === interaction.user.id &&
              mainI.customId === `minion_action_${updatedMinion.id}`;

            const mainCollector = sentMessage.createMessageComponentCollector({
              filter: mainFilter,
              componentType: ComponentType.StringSelect,
            });

            activeCollectors.set(mainCollectorKey, mainCollector);

            // Set up the main collector handlers
            mainCollector.on("collect", async (mainI) => {
              if (mainI.replied || mainI.deferred) return;

              const interactionAge = Date.now() - mainI.createdTimestamp;
              if (interactionAge > 14 * 60 * 1000) return;

              const selectedAction = mainI.values[0];

              if (selectedAction !== "add_fuel") {
                try {
                  await mainI.deferUpdate();
                } catch (error) {
                  console.error(
                    "[Restored Main Collector] Failed to defer interaction:",
                    error
                  );
                  return;
                }
              }

              try {
                if (selectedAction === "collect") {
                  await handleCollect(mainI);
                } else if (selectedAction === "upgrade") {
                  await handleUpgrade(mainI);
                } else if (selectedAction === "manage_upgrades") {
                  await showUpgradeManagementMenu(
                    mainI,
                    freshCharacter,
                    updatedMinion
                  );
                } else if (selectedAction === "add_fuel") {
                  await showFuelMenu(mainI, freshCharacter, updatedMinion);
                } else if (selectedAction === "clear_fuel") {
                  await handleClearFuel(mainI, freshCharacter, updatedMinion);
                }
              } catch (error) {
                console.error(
                  `[Restored Main Collector] Error handling ${selectedAction}:`,
                  error
                );
              }
            });

            mainCollector.on("end", () => {
              activeCollectors.delete(mainCollectorKey);
            });
          } catch (editError) {
            console.error(
              "[Fuel Menu Collector End] Failed to restore minion view:",
              editError
            );
          }
        }
      } catch (error) {
        console.error("[Fuel Menu Collector End] Error restoring view:", error);
      }
    }
  });
}

async function handleList(interaction, character) {
  const storedMinions = character.minionStorage || [];
  const rankPrefix = character.rank === "PLAYER" ? "" : `[${character.rank}] `;

  // Get page from options or default to 1, then convert to 0-based indexing
  let page = 0;
  if (
    interaction.options.getInteger &&
    interaction.options.getInteger("page") !== null
  ) {
    page = interaction.options.getInteger("page") - 1; // Convert from 1-based user input to 0-based indexing
  }

  const itemsPerPage = 24;
  const totalPages = Math.ceil(storedMinions.length / itemsPerPage) || 1;
  const currentPage = Math.max(0, Math.min(page, totalPages - 1));
  const startIdx = currentPage * itemsPerPage;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.SKY_BLUE)
    .setTitle(`${rankPrefix}${interaction.user.username}'s Minion Storage`);

  if (storedMinions.length === 0) {
    embed.setDescription(
      "You have no minions in your storage. Craft some using /craft!"
    );
  } else {
    const pageMinions = storedMinions.slice(startIdx, startIdx + itemsPerPage);
    const fields = pageMinions.map((minion) => {
      const displayInfo = getMinionDisplayInfo(minion);
      const baseKey = minion.itemKey;
      const allItems = configManager.getAllItems();
      const baseMinion = allItems[baseKey];
      const tierData = baseMinion?.tiers?.[minion.tier];
      const _interval = tierData?.generationIntervalSeconds ?? "?"; // underscore-prefixed to indicate intentional unused
      void _interval; // mark as used to avoid linting error

      return {
        name: `${displayInfo.emoji} ${displayInfo.name}`,
        value: `ID: \`${minion.id.substring(0, 4)}\` Rate: \`${_interval}s\``,
        inline: true,
      };
    });

    embed.addFields(fields);
    embed.setFooter({
      text: `Page ${currentPage + 1} of ${totalPages} • Showing ${pageMinions.length} of ${storedMinions.length} stored minions`,
    });
  }

  // Add pagination buttons if there are multiple pages
  const components = [];
  if (totalPages > 1) {
    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("minion_list_prev")
        .setLabel("◀ Previous")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === 0),
      new ButtonBuilder()
        .setCustomId("minion_list_page")
        .setLabel(`${currentPage + 1}/${totalPages}`)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(true),
      new ButtonBuilder()
        .setCustomId("minion_list_next")
        .setLabel("Next ▶")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === totalPages - 1)
    );
    components.push(row);
  }

  const reply = await interaction.reply({
    embeds: [embed],
    components,
  });

  // Only set up collector if there are multiple pages
  if (totalPages > 1) {
    const filter = (i) =>
      i.user.id === interaction.user.id &&
      (i.customId === "minion_list_prev" || i.customId === "minion_list_next");
    const collector = reply.createMessageComponentCollector({
      filter,
      componentType: ComponentType.Button,
    }); // No timeout - collector stays active indefinitely

    let currentPageState = currentPage;

    collector.on("collect", async (i) => {
      try {
        // Check if interaction is already acknowledged before deferring
        if (!i.replied && !i.deferred) {
          await i.deferUpdate();
        }

        let newPage = currentPageState;
        if (i.customId === "minion_list_prev") {
          newPage = Math.max(0, currentPageState - 1);
        } else if (i.customId === "minion_list_next") {
          newPage = Math.min(totalPages - 1, currentPageState + 1);
        }

        if (newPage !== currentPageState) {
          currentPageState = newPage;

          const startIdx = newPage * itemsPerPage;
          const pageMinions = storedMinions.slice(
            startIdx,
            startIdx + itemsPerPage
          );

          const updatedFields = pageMinions.map((minion) => {
            const displayInfo = getMinionDisplayInfo(minion);
            const baseKey = minion.itemKey;
            const allItems = configManager.getAllItems();
            const baseMinion = allItems[baseKey];
            const tierData = baseMinion?.tiers?.[minion.tier];
            const _interval = tierData?.generationIntervalSeconds ?? "?"; // underscore-prefixed to indicate intentional unused
            void _interval; // mark as used to avoid linting error

            return {
              name: `${displayInfo.emoji} ${displayInfo.name}`,
              value: `ID: \`${minion.id.substring(0, 4)}\` Rate: \`${_interval}s\``,
              inline: true,
            };
          });

          const updatedEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.SKY_BLUE)
            .setTitle(
              `${rankPrefix}${interaction.user.username}'s Minion Storage`
            )
            .addFields(updatedFields)
            .setFooter({
              text: `Page ${newPage + 1} of ${totalPages} • Showing ${pageMinions.length} of ${storedMinions.length} stored minions`,
            });

          const updatedRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId("minion_list_prev")
              .setLabel("◀ Previous")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(newPage === 0),
            new ButtonBuilder()
              .setCustomId("minion_list_page")
              .setLabel(`${newPage + 1}/${totalPages}`)
              .setStyle(ButtonStyle.Primary)
              .setDisabled(true),
            new ButtonBuilder()
              .setCustomId("minion_list_next")
              .setLabel("Next ▶")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(newPage === totalPages - 1)
          );

          await i.editReply({
            embeds: [updatedEmbed],
            components: [updatedRow],
          });
        }
      } catch (error) {
        // Ignore interaction timeout and already acknowledged errors
        if (error.code !== 10062 && error.code !== 40060) {
          console.error(
            "[Minions List Pagination] Error handling button interaction:",
            error
          );
        }
      }
    });
  }
}

async function handlePlace(interaction, character) {
  const minionIdToPlace = interaction.options.getString("id");
  const { atomicMinionPlace } = require("../utils/atomicMinionOperations");

  // check if minion exists in storage
  const storageIndex = character.minionStorage.findIndex(
    (m) => m.id === minionIdToPlace
  );

  if (storageIndex === -1) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR) // Red color for errors
      .setTitle("Minion Not Found")
      .setDescription(
        `Minion with ID \`${minionIdToPlace}\` not found in your storage.`
      );
    return interaction.reply({ embeds: [embed], ephemeral: true });
  }

  const allItems = configManager.getAllItems();
  const minionToPlace = character.minionStorage[storageIndex];
  const baseMinionDef = allItems[minionToPlace.itemKey]; // Use itemKey

  if (!baseMinionDef) {
    console.error(
      `[Minions Place] Failed to find base definition for minion ${minionToPlace.itemKey} (ID: ${minionIdToPlace})`
    );
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR) // Red color for errors
      .setTitle("Placement Error")
      .setDescription(
        "[MIN-PLC-001] An error occurred finding minion data. Please report this."
      );
    return interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // check minion slot limit before attempting placement
  if (!character.island) character.island = {}; // Ensure island object exists
  if (!character.island.placedMinions) character.island.placedMinions = []; // Ensure array exists
  const maxSlots = calculateMaxMinionSlots(character);
  if (character.island.placedMinions.length >= maxSlots) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.YELLOW) // Yellow color for warnings
      .setTitle("Minion Slot Limit Reached")
      .setDescription(
        `You have reached your minion slot limit! (${maxSlots} slots unlocked)\nRemove a minion before placing another.`
      );
    return interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // ensure upgrades are initialized properly
  initializeMinionUpgrades(minionToPlace);

  // perform atomic placement operation
  const result = await atomicMinionPlace(interaction.user.id, minionIdToPlace);

  if (!result.success) {
    console.error(
      `[Minions Place] Atomic placement failed for ${interaction.user.id}, minion ${minionIdToPlace}:`,
      result.error
    );

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR) // Red color for errors
      .setTitle("Placement Failed")
      .setDescription(
        `[MIN-PLC-002] ${result.error}. Please report this error code to an Admin.`
      );
    return interaction.reply({ embeds: [embed], ephemeral: true });
  }

  // success - send confirmation with updated character data
  const updatedCharacter = result.character;
  const displayInfo = getMinionDisplayInfo(minionToPlace);
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.LIGHT_GREEN) // Green color for success
    //.setTitle("Minion Placed!")
    .setDescription(
      `Successfully placed ${displayInfo.emoji} **${displayInfo.name}** on your island! (**${updatedCharacter.island.placedMinions.length}/${calculateMaxMinionSlots(updatedCharacter)}**)`
    );
  await interaction.reply({ embeds: [embed] });
}

// ++ Update handleView function ++
async function handleView(interaction, character) {
  const placedMinions = character.island?.placedMinions || [];

  const allItemsView = configManager.getAllItems();
  const id = interaction.options.getString("id");

  // If no individual minion ID was supplied we are about to build a potentially
  // large summary embed of every placed minion. That extra processing time can
  // exceed Discord's 3-second response window, so proactively defer the reply
  // to avoid an "application did not respond" timeout.
  if (!id && !interaction.deferred && !interaction.replied) {
    try {
      await interaction.deferReply();
    } catch (error) {
      console.error("[Minions View] Failed to defer reply:", error);
      // If deferring fails we'll fall back to a normal reply further below.
    }
  }

  const rankPrefix = character.rank === "PLAYER" ? "" : `[${character.rank}] `;

  const maxSlots = calculateMaxMinionSlots(character);

  if (id) {
    // Detailed view for a single minion
    const minion = placedMinions.find((m) => m.id === id);

    if (!minion) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR) // Red color for errors
        .setTitle("Minion Not Found")
        .setDescription(`Could not find a placed minion with ID \`${id}\`.`);
      return interaction.reply({ embeds: [embed], ephemeral: true });
    }
    // View function should NOT generate items or consume fuel - only display current state
    // Minion details are now handled by the unified createMinionViewEmbedAndComponents function

    // Fuel status is now handled by the unified createMinionViewEmbedAndComponents function

    // Use the unified function to create embed and components
    const { embed, components } = createMinionViewEmbedAndComponents(minion);
    await interaction.reply({ embeds: [embed], components });
    // Set up collector for select menu interactions
    // Fetch the reply message after sending (no fetchReply: true)
    const sentMessage = await interaction.fetchReply();

    // Clean up any existing collectors for this user/minion combination
    const collectorKey = `main_${interaction.user.id}_${minion.id}`;
    if (activeCollectors.has(collectorKey)) {
      const existingCollector = activeCollectors.get(collectorKey);
      existingCollector.stop("new_view_opened");
      activeCollectors.delete(collectorKey);
    }

    const filter = (i) =>
      i.user.id === interaction.user.id &&
      i.customId === `minion_action_${minion.id}`;

    // Create a function to handle collector events
    const handleCollectorEvents = (collector) => {
      collector.on("collect", async (i) => {
        // Check if interaction is still valid before processing
        if (i.replied || i.deferred) {
          console.warn(
            "[Minion Collector] Interaction already handled, skipping"
          );
          return;
        }

        // Check interaction age (Discord interactions expire after 15 minutes)
        const interactionAge = Date.now() - i.createdTimestamp;
        if (interactionAge > 14 * 60 * 1000) {
          // 14 minutes to be safe
          console.warn("[Minion Collector] Interaction too old, skipping");
          return;
        }

        const selectedAction = i.values[0];

        // Don't defer for Add Fuel interactions as they may need to show modals
        // Clear Fuel can be deferred since it doesn't show modals
        if (selectedAction !== "add_fuel") {
          try {
            await i.deferUpdate();
          } catch (error) {
            console.error(
              "[Minion Collector] Failed to defer interaction:",
              error
            );
            // If deferUpdate fails, the interaction might be expired or already handled
            return;
          }
        }

        // Route to appropriate handler based on selected action
        try {
          if (selectedAction === "collect") {
            await handleCollect(i);
          } else if (selectedAction === "upgrade") {
            await handleUpgrade(i);
          } else if (selectedAction === "manage_upgrades") {
            await showUpgradeManagementMenu(i, character, minion);
          } else if (selectedAction === "add_fuel") {
            await showFuelMenu(i, character, minion);
          } else if (selectedAction === "clear_fuel") {
            await handleClearFuel(i, character, minion);
          }
        } catch (error) {
          console.error(
            `[Minion Collector] Error handling ${selectedAction}:`,
            error
          );
          // Try to send error message if interaction allows it
          if (!i.replied && !i.deferred) {
            try {
              await i.reply({
                content: "An error occurred. Please try again.",
                ephemeral: true,
              });
            } catch (replyError) {
              console.error(
                "[Minion Collector] Failed to send error reply:",
                replyError
              );
            }
          }
        }
      });

      // Clean up collector reference when it ends
      collector.on("end", () => {
        activeCollectors.delete(collectorKey);
      });
    };

    // Create initial collector
    const collector = sentMessage.createMessageComponentCollector({
      filter,
      componentType: ComponentType.StringSelect,
    }); // No timeout - collector stays active indefinitely

    // Store collector for cleanup
    activeCollectors.set(collectorKey, collector);
    handleCollectorEvents(collector);

    return;
  }
  // Default: summary list as before
  let embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.SKY_BLUE)
    .setTitle(
      `${rankPrefix}${character.name}'s Placed Minions (${placedMinions.length}/${maxSlots})`
    );

  let componentsToSend = []; // For potential pagination buttons
  let paginationSetup = null; // Will hold pagination helpers if needed

  if (placedMinions.length === 0) {
    embed.setDescription("You have no minions placed on your island.");

    // Add Manage Minions button for the empty state
    const manageButton = new ButtonBuilder()
      .setCustomId("manage_minions")
      .setLabel("Manage Minions")
      .setStyle(ButtonStyle.Primary);
    const manageRow = new ActionRowBuilder().addComponents(manageButton);
    componentsToSend.push(manageRow);
  } else {
    // Build field data for all minions once
    const allFields = await Promise.all(
      placedMinions.map(async (minion) => {
        const displayInfo = getMinionDisplayInfo(minion);
        const baseKey = minion.itemKey;
        const baseMinion = allItemsView[baseKey];
        const tierData = baseMinion?.tiers?.[minion.tier];
        const _interval = tierData?.generationIntervalSeconds ?? "?";
        void _interval; // keep linter happy – not displayed in this view

        // Compact storage display (no generation)
        const storedDisplay = formatMinionStorageDisplay(
          minion,
          allItemsView,
          true
        );

        // Header line with emoji, tier + name, short id
        let minionBaseName = (baseMinion?.name || baseKey).replace(
          /\s+Minion$/i,
          ""
        );
        // special case for cobblestone minion to display as "Cobble"
        if (minionBaseName.toLowerCase() === "cobblestone") {
          minionBaseName = "Cobble";
        }
        const headerLine = `${displayInfo.emoji} **[T${minion.tier}] ${minionBaseName}** \`${minion.id.substring(0, 4)}\``;

        // Split storedDisplay into item-line + total-line
        const storageLines = storedDisplay.split("\n");
        let itemsLine = storageLines[0] || "";
        let totalRaw = storageLines.length > 1 ? storageLines[1] : itemsLine;
        if (itemsLine.startsWith("**Total:")) {
          totalRaw = itemsLine;
          itemsLine = "";
        }

        const totalFormatted = totalRaw
          .replace(/\*\*Total:\s*/i, "")
          .replace(/\*\*/g, "")
          .trim();
        const storageEmoji = "<:minion_storage:1370941654437072916>";
        const storageLine = `${storageEmoji} **${totalFormatted}**`;

        // create upgrade line showing equipped upgrade emojis
        let upgradeLine = "";
        if (minion.upgrades && Array.isArray(minion.upgrades)) {
          const upgradeEmojis = minion.upgrades
            .map((upgradeKey) => {
              if (!upgradeKey) return null;
              const upgradeItem = allItemsView[upgradeKey];
              return upgradeItem?.emoji || "❓";
            })
            .filter((emoji) => emoji !== null); // only show equipped upgrades

          if (upgradeEmojis.length > 0) {
            upgradeLine = `⧎  ${upgradeEmojis.join(" ")}`;
          }
        }

        const valueLines = [headerLine];
        if (itemsLine) valueLines.push(itemsLine);
        valueLines.push(storageLine);
        if (upgradeLine) valueLines.push(upgradeLine);

        return {
          name: "\u200b",
          value: valueLines.join("\n"),
          inline: true,
        };
      })
    );

    // Pagination setup
    const ITEMS_PER_PAGE = 15;
    const totalPages = Math.max(
      1,
      Math.ceil(allFields.length / ITEMS_PER_PAGE)
    );
    const currentPage = 0;

    const buildEmbedForPage = (pageIdx) => {
      const pageFields = allFields.slice(
        pageIdx * ITEMS_PER_PAGE,
        pageIdx * ITEMS_PER_PAGE + ITEMS_PER_PAGE
      );
      const pageEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.SKY_BLUE)
        .setTitle(
          `${rankPrefix}${character.name}'s Placed Minions (${placedMinions.length}/${maxSlots})`
        )
        .addFields(pageFields);

      if (totalPages > 1) {
        pageEmbed.setFooter({ text: `Page ${pageIdx + 1} of ${totalPages}` });
      }
      if (pageFields.length === 0) {
        pageEmbed.setDescription("You have no minions placed on your island.");
      }
      return pageEmbed;
    };

    const buildNavRow = (pageIdx) => {
      if (totalPages === 1) return [];
      return [
        new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId("minion_view_prev")
            .setLabel("Prev")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(pageIdx === 0),
          new ButtonBuilder()
            .setCustomId("minion_view_page")
            .setLabel(`${pageIdx + 1}/${totalPages}`)
            .setStyle(ButtonStyle.Primary)
            .setDisabled(true),
          new ButtonBuilder()
            .setCustomId("minion_view_next")
            .setLabel("Next")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(pageIdx === totalPages - 1)
        ),
      ];
    };

    // Initial embed & components
    embed = buildEmbedForPage(currentPage);
    componentsToSend = buildNavRow(currentPage);

    // Add Manage Minions button (always available)
    const manageButton = new ButtonBuilder()
      .setCustomId("manage_minions")
      .setLabel("Manage Minions")
      .setStyle(ButtonStyle.Primary);
    const manageRow = new ActionRowBuilder().addComponents(manageButton);
    componentsToSend.push(manageRow);

    // paginationSetup remains the same
    paginationSetup = {
      totalPages,
      buildEmbedForPage,
      buildNavRow,
      currentPage,
    };
  }
  // Send or edit the reply, then set up pagination collector if needed
  let sentMessage;
  if (interaction.deferred || interaction.replied) {
    sentMessage = await interaction.editReply({
      embeds: [embed],
      components: componentsToSend,
    });
  } else {
    sentMessage = await interaction.reply({
      embeds: [embed],
      components: componentsToSend,
      fetchReply: true,
    });
  }

  // -- Set up collectors --
  if (placedMinions.length === 0) {
    // Simple collector for empty state (just manage button)
    const filter = (i) =>
      i.user.id === interaction.user.id && i.customId === "manage_minions";
    const collector = sentMessage.createMessageComponentCollector({
      filter,
      componentType: ComponentType.Button,
    });

    collector.on("collect", async (i) => {
      try {
        if (!i.replied && !i.deferred) {
          await i.deferUpdate();
        }
        await showManageMenu(i, character);
        collector.stop("manage_menu_opened");
      } catch (err) {
        if (err.code !== 10062 && err.code !== 40060) {
          console.error(
            "[Minions View Empty State] Error handling button:",
            err
          );
        }
      }
    });
  } else if (paginationSetup) {
    let { currentPage } = paginationSetup;
    const { totalPages, buildEmbedForPage, buildNavRow } = paginationSetup;
    // Ensure we have the actual message object (editReply returns it, reply with fetchReply returns it)
    const filter = (i) =>
      i.user.id === interaction.user.id &&
      (i.customId === "minion_view_prev" ||
        i.customId === "minion_view_next" ||
        i.customId === "manage_minions");

    const collector = sentMessage.createMessageComponentCollector({
      filter,
      componentType: ComponentType.Button,
    }); // No timeout

    collector.on("collect", async (i) => {
      try {
        // Check if interaction is already acknowledged before deferring
        if (!i.replied && !i.deferred) {
          await i.deferUpdate();
        }

        let updatedPage = currentPage;
        if (i.customId === "minion_view_prev") {
          updatedPage = Math.max(0, currentPage - 1);
        } else if (i.customId === "minion_view_next") {
          updatedPage = Math.min(totalPages - 1, currentPage + 1);
        } else if (i.customId === "manage_minions") {
          await showManageMenu(i, character);
          collector.stop("manage_menu_opened"); // Stop pagination collector when entering manage menu
          return;
        }

        if (updatedPage !== currentPage) {
          currentPage = updatedPage;

          const updatedEmbed = buildEmbedForPage(currentPage);
          const updatedComponents = buildNavRow(currentPage);

          // Re-add Manage Minions button (always available)
          const manageButton = new ButtonBuilder()
            .setCustomId("manage_minions")
            .setLabel("Manage Minions")
            .setStyle(ButtonStyle.Primary);
          const manageRow = new ActionRowBuilder().addComponents(manageButton);
          updatedComponents.push(manageRow);

          await i.editReply({
            embeds: [updatedEmbed],
            components: updatedComponents,
          });
        }
      } catch (err) {
        // Ignore interaction timeout and already acknowledged errors
        if (err.code !== 10062 && err.code !== 40060) {
          console.error(
            "[Minions View Pagination] Error handling button:",
            err
          );
        }
      }
    });
  }
}

// ++ Implement handleCollect function correctly ++
async function handleCollect(interaction) {
  // Only defer if not already deferred or replied
  if (!interaction.deferred && !interaction.replied) {
    try {
      if (interaction.isButton && interaction.isButton()) {
        await interaction.deferUpdate();
      } else if (
        interaction.isStringSelectMenu &&
        interaction.isStringSelectMenu()
      ) {
        // For select menu interactions, we should already be deferred by the collector
        // but if not, use deferUpdate to maintain consistency
        await interaction.deferUpdate();
      } else {
        await interaction.deferReply(); // Fallback for other interaction types
      }
    } catch (error) {
      console.error("[HandleCollect] Failed to defer interaction:", error);
      if (error.code === 10062) {
        console.error(
          "[HandleCollect] Interaction expired (Unknown interaction)"
        );
        return;
      }
    }
  }
  const userId = interaction.user.id;

  // Use minion lock to prevent race conditions
  const { withMinionLock } = require("../utils/minionMutex");

  return await withMinionLock(userId, "collect", async () => {
    // --- Extract minion ID from options or customId ---
    let id;
    if (
      interaction.options &&
      typeof interaction.options.getString === "function"
    ) {
      id = interaction.options.getString("id");
    } else if (
      interaction.customId &&
      interaction.customId.startsWith("minion_collect_")
    ) {
      id = interaction.customId.replace("minion_collect_", "");
    } else if (
      interaction.customId &&
      interaction.customId.startsWith("minion_action_")
    ) {
      // Extract minion ID from select menu custom ID format: minion_action_${minionId}
      id = interaction.customId.replace("minion_action_", "");
    }

    let character = await getPlayerData(userId); // Use 'let' as it will be reassigned
    if (!character) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR) // Red color for errors
        .setTitle("Character Data Error")
        .setDescription("Could not load your character data.");
      return interaction.editReply({ embeds: [embed] });
    }

    const placedMinions = character.island?.placedMinions || [];
    const minionIndex = placedMinions.findIndex((minion) => minion.id === id);

    if (minionIndex === -1) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR) // Red color for errors
        .setTitle("Minion Not Found")
        .setDescription(
          `Could not find a minion with ID \`${id}\` placed on your island. Use the autocomplete suggestions.`
        );
      return interaction.editReply({ embeds: [embed] });
    }

    const minionToCollect = placedMinions[minionIndex]; // This is a reference to the minion in character.island.placedMinions

    // Generate items (includes automatic compacting)
    const allItems = configManager.getAllItems();
    const { generated, effectiveTimestamp } =
      await calculateMinionGeneration(minionToCollect);

    const itemsToCollect = {};
    let hasAnyToCollect = false;

    // Collect ALL stored resources (after generation and compacting)
    // This includes both newly generated items and previously stored items (now compacted)
    if (minionToCollect.resourcesStored) {
      for (const key of Object.keys(minionToCollect.resourcesStored)) {
        const currentlyStoredAmount = minionToCollect.resourcesStored[key];
        if (currentlyStoredAmount > 0) {
          itemsToCollect[key] =
            (itemsToCollect[key] || 0) + currentlyStoredAmount;
          hasAnyToCollect = true;
        }
      }
    }

    if (!hasAnyToCollect) {
      let needsTimestampSave = false;
      // Only update timestamp if it actually changed and minion didn't generate anything new to collect
      if (
        effectiveTimestamp !== (minionToCollect.lastCollectionTimestamp || 0) &&
        Object.keys(generated).length === 0
      ) {
        minionToCollect.lastCollectionTimestamp = effectiveTimestamp;
        needsTimestampSave = true;
      }
      if (needsTimestampSave) {
        try {
          await savePlayerData(userId, character);
        } catch (error) {
          console.error(
            `[Minions Collect] Error saving timestamp update for ${userId} (no resources):`,
            error
          );
        }
      }
      const noResourcesEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.YELLOW) // Yellow for warning
        .setTitle("No New Resources Generated")
        .setDescription(
          "That minion has not generated any new resources to collect at this time."
        );
      return interaction.editReply({ embeds: [noResourcesEmbed] });
    }

    // ---- START REORDERED LOGIC ----
    // 1. In-memory updates to the minion object (referenced via 'character')
    // And prepare items for collection progress and atomic update
    const collectionNotifications = [];
    const itemsToChangeForAtomic = [];
    let coinsToAdd = 0;

    for (const [itemKey, amountToCollectThisKey] of Object.entries(
      itemsToCollect
    )) {
      if (amountToCollectThisKey > 0) {
        // Update minion's internal storage (in memory)
        if (
          minionToCollect.resourcesStored &&
          typeof minionToCollect.resourcesStored[itemKey] === "number"
        ) {
          minionToCollect.resourcesStored[itemKey] -= amountToCollectThisKey;
          if (minionToCollect.resourcesStored[itemKey] <= 0) {
            delete minionToCollect.resourcesStored[itemKey];
          }
        }

        if (itemKey === "COINS") {
          // Handle coins separately - they go to currency, not inventory
          coinsToAdd += amountToCollectThisKey;
        } else {
          // Add to list for atomic update
          itemsToChangeForAtomic.push({
            itemKey: itemKey,
            amount: amountToCollectThisKey,
          });
          // Add to collection progress (this also modifies 'character' in memory)
          // Use allowConversion for minion collection to handle enchanted items and blocks
          const progressResult = await addCollectionProgress(
            userId,
            itemKey,
            amountToCollectThisKey,
            character,
            { allowConversion: true }
          );
          if (progressResult.character) character = progressResult.character; // Re-assign character if modified by addCollectionProgress
          if (progressResult.notifications)
            collectionNotifications.push(...progressResult.notifications);
        }
      }
    }
    minionToCollect.lastCollectionTimestamp = effectiveTimestamp;
    if (Object.keys(minionToCollect.resourcesStored || {}).length === 0) {
      minionToCollect.resourcesStored = {}; // Ensure it's an empty object if all collected
    }

    // Use atomic update to save both inventory changes and minion state
    try {
      await updateInventoryAtomically(
        userId,
        coinsToAdd,
        itemsToChangeForAtomic,
        [], // equipmentToAdd
        [], // equipmentIdsToRemove
        0, // bankCoinsToAdd
        [], // equipmentDataUpdates
        JSON.stringify(character.island), // Save island data
        JSON.stringify(character.collections || {}), // Save collections
        false // Not using existing transaction
      );

      // 4. Send success messages (if savePlayerData also succeeded)
      // Refetch final character data to display accurate totals in the embed
      const finalCharacterDataForEmbed = await getPlayerData(userId);
      if (!finalCharacterDataForEmbed) {
        console.error(
          `[Minions Collect] Failed to refetch player data for embed for ${userId} after successful save.`
        );
        // Fallback: use the 'character' object we have, though totals might be slightly off if other async changes occurred.
        // For simplicity, we'll just send a generic success message if this happens.
        const genericSuccessEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.LIGHT_GREEN)
          .setTitle("Collection Successful!")
          .setDescription("Resources added to your inventory.");
        await interaction.editReply({ embeds: [genericSuccessEmbed] }); // Use editReply for the primary message
        if (collectionNotifications.length > 0) {
          await interaction.followUp({ embeds: collectionNotifications });
        }
        return;
      }

      const successEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.LIGHT_GREEN)
        .setTitle("Successfully Collected!");

      const collectedItemsDescription = [];
      for (const [itemKey, amountCollected] of Object.entries(itemsToCollect)) {
        const itemDetails = allItems[itemKey];
        if (itemKey === "COINS") {
          // Handle coins specially - they go to player currency, not inventory
          const finalCoins = finalCharacterDataForEmbed.coins || 0;
          const purseEmoji = "<:purse_coins:1367849116033482772>";
          collectedItemsDescription.push(
            `${purseEmoji} **Coins**: +${amountCollected.toLocaleString()} (${finalCoins.toLocaleString()})`
          );
        } else {
          const finalTotalInInventory =
            finalCharacterDataForEmbed.inventory?.items?.[itemKey] || 0;
          collectedItemsDescription.push(
            `${itemDetails?.emoji || "❓"} **${itemDetails?.name || itemKey}**: +${amountCollected.toLocaleString()} (${finalTotalInInventory.toLocaleString()})`
          );
        }
      }

      successEmbed.setDescription(
        collectedItemsDescription.join("\n") ||
          "No items were collected this time."
      ); // Add fallback

      await interaction.editReply({ embeds: [successEmbed] }); // Use editReply for the primary message

      if (collectionNotifications.length > 0) {
        try {
          await interaction.followUp({ embeds: collectionNotifications });
        } catch (followUpError) {
          console.error(
            `[Minions Collect] Failed to send collection notifications for ${userId}:`,
            followUpError
          );
        }
      }

      // Check for Disblock XP achievements after collection
      try {
        await checkAndNotifyDisblockXP(userId, interaction);
      } catch (xpError) {
        console.error("[Minions Collect] Error updating Disblock XP:", xpError);
        // Continue with minions command even if XP update fails
      }

      // After 5 seconds, revert back to the minion menu
      setTimeout(async () => {
        try {
          // Get updated character data to ensure we have the latest minion state
          const updatedCharacter = await getPlayerData(userId);
          if (!updatedCharacter) {
            console.error(
              `[Minions Collect] Failed to get updated character data for menu revert for ${userId}`
            );
            return;
          }

          // Find the updated minion
          const updatedPlacedMinions =
            updatedCharacter.island?.placedMinions || [];
          const updatedMinion = updatedPlacedMinions.find((m) => m.id === id);
          if (!updatedMinion) {
            console.error(
              `[Minions Collect] Could not find minion ${id} for menu revert for ${userId}`
            );
            return;
          }

          // Create the minion view embed and components
          const { embed, components } =
            createMinionViewEmbedAndComponents(updatedMinion);

          // Edit the reply back to the minion menu
          await interaction.editReply({ embeds: [embed], components });
        } catch (revertError) {
          console.error(
            `[Minions Collect] Error reverting to minion menu for ${userId}:`,
            revertError
          );
          // Don't throw the error as this is a background operation
        }
      }, 5000); // 5 seconds delay
    } catch (atomicError) {
      console.error(
        `[Minions Collect] Error during atomic inventory update for ${userId}, minion ${id}:`,
        atomicError
      );
      // In-memory changes to 'character' (minion state, collections) are not saved.
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Collection Failed")
        .setDescription(
          `[MIN-COL-001] An error occurred while adding items to your inventory: ${atomicError.message || "Unknown error"}. Please report this code to an Admin.`
        );
      await interaction.editReply({ embeds: [errorEmbed] });
    }
    // ---- END REORDERED LOGIC ----
  }); // Close withMinionLock
}

async function handleUpgrade(interaction) {
  const userId = interaction.user.id;
  let minionId;
  const isComponentInteraction =
    interaction.isButton() || interaction.isStringSelectMenu();

  // Deferral for slash commands, other interactions are deferred in the collector
  if (interaction.isCommand && !interaction.deferred && !interaction.replied) {
    await interaction.deferReply(); // Removed Ephemeral Flag
  }

  // Use minion lock to prevent race conditions
  const { withMinionLock } = require("../utils/minionMutex");

  return await withMinionLock(userId, "upgrade", async () => {
    if (isComponentInteraction) {
      if (
        interaction.customId &&
        interaction.customId.startsWith("minion_upgrade_")
      ) {
        minionId = interaction.customId.replace("minion_upgrade_", "");
      } else if (
        interaction.customId &&
        interaction.customId.startsWith("minion_action_")
      ) {
        // Extract minion ID from select menu custom ID format: minion_action_${minionId}
        minionId = interaction.customId.replace("minion_action_", "");
      }
    } else if (interaction.isCommand()) {
      minionId = interaction.options.getString("id");
    }

    if (!minionId) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Upgrade Error")
        .setDescription("Could not determine the minion ID to upgrade.");
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] }); // Removed Ephemeral Flag
    }

    const currentCharacter = await getPlayerData(userId);
    if (!currentCharacter) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Character Data Error")
        .setDescription("Could not load your character data.");
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] }); // Removed Ephemeral Flag
    }

    const allItems = configManager.getAllItems();
    let minionToUpgrade = null;
    let minionLocation = null;

    const storageIndex = currentCharacter.minionStorage?.findIndex(
      (m) => m.id === minionId
    );
    if (storageIndex !== -1 && currentCharacter.minionStorage[storageIndex]) {
      minionToUpgrade = currentCharacter.minionStorage[storageIndex];
      minionLocation = "storage";
    } else {
      const placedIndex = currentCharacter.island?.placedMinions?.findIndex(
        (m) => m.id === minionId
      );
      if (
        placedIndex !== -1 &&
        currentCharacter.island.placedMinions[placedIndex]
      ) {
        minionToUpgrade = currentCharacter.island.placedMinions[placedIndex];
        minionLocation = "placed";
      }
    }

    if (!minionToUpgrade) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Minion Not Found")
        .setDescription(`Minion with ID \`${minionId}\` not found.`);
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] }); // Removed Ephemeral Flag
    }

    // Check island requirements for placed minions
    if (
      minionLocation === "placed" &&
      (currentCharacter.current_region !== "Private Island" ||
        currentCharacter.visiting_island_owner_id !== null)
    ) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.YELLOW)
        //        .setTitle("Island Required")
        .setDescription(
          "You must be on your own Private Island to upgrade placed minions.\nStored minions can be upgraded anywhere."
        );
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] });
    }

    const baseMinionDef = allItems[minionToUpgrade.itemKey];
    if (!baseMinionDef || !baseMinionDef.tiers) {
      console.error(
        `[Minions Upgrade] Could not find base definition or tiers for minion key: ${minionToUpgrade.itemKey}`
      );
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Upgrade Error")
        .setDescription("Error finding minion definition. Cannot upgrade.");
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] }); // Removed Ephemeral Flag
    }

    const currentTier = minionToUpgrade.tier;
    const nextTierNum = currentTier + 1;

    if (
      nextTierNum >= baseMinionDef.tiers.length ||
      !baseMinionDef.tiers[nextTierNum] ||
      !baseMinionDef.tiers[nextTierNum].upgradeCost
    ) {
      const displayInfo = getMinionDisplayInfo(minionToUpgrade);
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.YELLOW)
        .setTitle("Max Tier Reached")
        .setDescription(
          `${displayInfo.emoji} **${displayInfo.name}** is already at its maximum tier (Tier ${currentTier}).`
        );
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] }); // Removed Ephemeral Flag
    }

    const nextTierData = baseMinionDef.tiers[nextTierNum];
    const upgradeCosts = nextTierData.upgradeCost;

    const missingItems = [];
    for (const cost of upgradeCosts) {
      const playerAmount =
        currentCharacter.inventory?.items?.[cost.itemKey] || 0;
      if (playerAmount < cost.amount) {
        const itemDef = allItems[cost.itemKey];
        const itemName = itemDef?.name || cost.itemKey;
        const itemEmoji = itemDef?.emoji || "❓";
        missingItems.push(
          `${itemEmoji} ${itemName}: Need ${cost.amount.toLocaleString()}, Have ${playerAmount.toLocaleString()}`
        );
      }
    }

    if (missingItems.length > 0) {
      const displayInfo = getMinionDisplayInfo(minionToUpgrade);
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Insufficient Resources")
        .setDescription(
          `${displayInfo.emoji} **${displayInfo.name}** (Tier ${currentTier}) → Tier ${nextTierNum}\n\nInsufficient resources to upgrade:\n${missingItems.join("\n")}`
        );
      return isComponentInteraction
        ? interaction.followUp({ embeds: [embed] })
        : interaction.editReply({ embeds: [embed] }); // Removed Ephemeral Flag
    }

    try {
      // ++ ATOMIC UPGRADE FIX: Perform tier change and item deduction in single transaction ++
      // Update minion tier in memory first
      minionToUpgrade.tier = nextTierNum;
      if (minionLocation === "placed") {
        minionToUpgrade.lastCollectionTimestamp = Date.now();
        // Note: resourcesStored is preserved during upgrades - items should not be wiped
      }

      // ++ Add to craftedMinions list for unique tracking ++
      // Initialize craftedMinions array if it doesn't exist
      if (
        !currentCharacter.craftedMinions ||
        !Array.isArray(currentCharacter.craftedMinions)
      ) {
        currentCharacter.craftedMinions = [];
      }

      const uniqueMinionTierKey = `${minionToUpgrade.itemKey.toUpperCase()}_T${nextTierNum}`;
      let uniqueUpgradeNotificationEmbed = null;
      if (!currentCharacter.craftedMinions.includes(uniqueMinionTierKey)) {
        currentCharacter.craftedMinions.push(uniqueMinionTierKey);
        console.log(
          `[Minions Upgrade] Added ${uniqueMinionTierKey} to craftedMinions for ${userId}.`
        );

        // Build unique upgrade notification embed
        const itemInfo = allItems[minionToUpgrade.itemKey];
        const uniqueCraftsCount = currentCharacter.craftedMinions.length;
        let nextSlotText = "";
        let nextMilestoneCrafts = null;
        for (const unlock of MINION_SLOT_UNLOCKS) {
          if (unlock.crafts > uniqueCraftsCount) {
            nextMilestoneCrafts = unlock.crafts;
            break;
          }
        }
        if (nextMilestoneCrafts !== null) {
          const needed = nextMilestoneCrafts - uniqueCraftsCount;
          nextSlotText = ` (${needed} more for next slot!)`;
        }

        uniqueUpgradeNotificationEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setDescription(
            `${itemInfo?.emoji || "❓"} **[T${nextTierNum}] ${itemInfo?.name || minionToUpgrade.itemKey}** - New Unique Minion! ${nextSlotText}`
          )
          .setFooter({ text: "Check /minions slots for progress" });

        // XP will be handled by the achievement system after saving player data
      }

      // Prepare items to deduct
      const itemsToChange = upgradeCosts.map((cost) => ({
        itemKey: cost.itemKey,
        amount: -cost.amount,
      }));

      // ATOMIC OPERATION: Deduct items AND save minion state in single transaction
      const islandJsonString =
        minionLocation === "placed"
          ? JSON.stringify(currentCharacter.island)
          : null;

      await updateInventoryAtomically(
        userId,
        0,
        itemsToChange,
        [],
        [],
        0,
        [],
        islandJsonString,
        null
      );

      // Save minionStorage separately if this was a storage upgrade (updateInventoryAtomically doesn't handle minionStorage field)
      if (minionLocation === "storage") {
        await savePlayerData(userId, currentCharacter, [
          "minionStorage",
          "craftedMinions",
        ]);
      } else {
        // For placed minions, only save craftedMinions since island was already saved atomically
        await savePlayerData(userId, currentCharacter, ["craftedMinions"]);
      }

      // Get updated character data for final display
      const updatedCharacterData = await getPlayerData(userId);
      if (!updatedCharacterData) {
        console.error(
          `[Minions Upgrade] CRITICAL: Failed to refetch player data for ${userId} after atomic upgrade.`
        );
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("Upgrade Error")
          .setDescription(
            "Upgrade may have processed but failed to load final state. Please check your minion and report to an Admin if the tier is incorrect."
          );
        return isComponentInteraction
          ? interaction.followUp({ embeds: [embed] })
          : interaction.editReply({ embeds: [embed] });
      }

      // Find the updated minion reference for display
      let minionRefInUpdatedData;
      if (minionLocation === "storage") {
        const storageIdx = updatedCharacterData.minionStorage?.findIndex(
          (m) => m.id === minionId
        );
        if (storageIdx !== -1)
          minionRefInUpdatedData =
            updatedCharacterData.minionStorage[storageIdx];
      } else {
        const placedIdx = updatedCharacterData.island?.placedMinions?.findIndex(
          (m) => m.id === minionId
        );
        if (placedIdx !== -1)
          minionRefInUpdatedData =
            updatedCharacterData.island.placedMinions[placedIdx];
      }

      if (!minionRefInUpdatedData) {
        console.error(
          `[Minions Upgrade] CRITICAL: Failed to find minion ${minionId} in refetched player data after atomic upgrade.`
        );
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("Upgrade Error")
          .setDescription(
            "Upgrade completed but failed to load updated minion. Please check your minion and report to an Admin if there are issues."
          );
        return isComponentInteraction
          ? interaction.followUp({ embeds: [embed] })
          : interaction.editReply({ embeds: [embed] });
      }

      // --- Post-save verification to guard against any remaining edge cases ---
      try {
        const verifySnapshot = await getPlayerData(userId);

        let needsPatch = false;

        const verifyArray =
          minionLocation === "storage"
            ? verifySnapshot.minionStorage
            : verifySnapshot.island?.placedMinions;

        const verifyIdx = verifyArray?.findIndex((m) => m.id === minionId);

        if (verifyIdx !== -1 && verifyArray[verifyIdx].tier !== nextTierNum) {
          console.warn(
            `[Upgrade Verify] Tier mismatch for ${userId} after atomic upgrade – expected ${nextTierNum}, got ${verifyArray[verifyIdx].tier}. This should be rare now. Correcting.`
          );
          verifyArray[verifyIdx].tier = nextTierNum;
          needsPatch = true;
        }

        const craftedKey = `${minionRefInUpdatedData.itemKey.toUpperCase()}_T${nextTierNum}`;
        if (!verifySnapshot.craftedMinions?.includes(craftedKey)) {
          console.warn(
            `[Upgrade Verify] craftedMinions missing ${craftedKey} for ${userId} after atomic upgrade. Adding.`
          );
          verifySnapshot.craftedMinions = verifySnapshot.craftedMinions || [];
          verifySnapshot.craftedMinions.push(craftedKey);
          needsPatch = true;
        }

        if (needsPatch) {
          console.warn(
            `[Upgrade Verify] Patching data inconsistency for ${userId}. This should be rare with atomic upgrades.`
          );
          const fieldsToUpdate = [
            minionLocation === "storage" ? "minionStorage" : "island",
            "craftedMinions",
          ];
          await savePlayerData(userId, verifySnapshot, fieldsToUpdate);
        }
      } catch (verifyErr) {
        console.error(
          `[Upgrade Verify] Verification failed for ${userId}:`,
          verifyErr
        );
      }

      // Check for Disblock XP achievements after data is saved (for upgrade subcommand)
      const currentSubcommand =
        interaction.options?.getSubcommand() || "upgrade";
      if (currentSubcommand === "upgrade") {
        try {
          await checkAndNotifyDisblockXP(userId, interaction);
        } catch (xpError) {
          console.error("[Minions] Error updating Disblock XP:", xpError);
          // Continue with minions command even if XP update fails
        }
      }

      const finalMinionDisplay = getMinionDisplayInfo(minionRefInUpdatedData);
      const successEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.LIGHT_GREEN)
        .setTitle(
          `${finalMinionDisplay.emoji} Minion Upgraded to Tier ${nextTierNum}!`
        )
        .setDescription(
          `Your **${finalMinionDisplay.name}** is now Tier ${nextTierNum}.`
        );
      const costFields = [];
      for (const cost of upgradeCosts) {
        const itemDef = allItems[cost.itemKey];
        const itemName = itemDef?.name || cost.itemKey;
        const itemEmoji = itemDef?.emoji || "❓";
        const remainingAmount =
          updatedCharacterData.inventory?.items?.[cost.itemKey] || 0;
        costFields.push({
          name: `${itemEmoji} ${itemName}`,
          value: `Used: ${cost.amount.toLocaleString()}\nRemaining: ${remainingAmount.toLocaleString()}`,
          inline: true,
        });
      }
      if (costFields.length > 0) {
        successEmbed.addFields({ name: "Resources Used", value: "\u200B" });
        successEmbed.addFields(costFields);
      }

      if (isComponentInteraction) {
        // Rebuild the minion view using the same helper that /minions view uses
        // so that the UI remains consistent (includes Add/Clear Fuel buttons etc.)
        const { embed: updatedViewEmbed, components: updatedComponents } =
          createMinionViewEmbedAndComponents(minionRefInUpdatedData);

        // Disable the upgrade option if the minion reached max tier
        const baseMinion = allItems[minionRefInUpdatedData.itemKey];
        const nextTierNumAfterUpgrade = minionRefInUpdatedData.tier + 1;
        const isMaxTierNow =
          nextTierNumAfterUpgrade >= baseMinion.tiers.length ||
          !baseMinion.tiers[nextTierNumAfterUpgrade] ||
          !baseMinion.tiers[nextTierNumAfterUpgrade].upgradeCost;

        if (isMaxTierNow) {
          // Traverse component rows to find the upgrade option and disable it
          for (const row of updatedComponents) {
            for (const component of row.components) {
              if (
                component.data?.custom_id ===
                `minion_action_${minionRefInUpdatedData.id}`
              ) {
                // Find and disable the upgrade option
                const options = component.options || [];
                const upgradeOption = options.find(
                  (opt) => opt.value === "upgrade"
                );
                if (upgradeOption) {
                  upgradeOption.description =
                    "Minion is already at maximum tier";
                  // Note: We can't actually disable individual select menu options in Discord.js
                  // But we can change the description to indicate it's maxed
                }
              }
            }
          }
        }

        await interaction.editReply({
          embeds: [updatedViewEmbed],
          components: updatedComponents,
        });

        // For unique upgrades, show the unique notification embed
        if (uniqueUpgradeNotificationEmbed) {
          await interaction.followUp({
            embeds: [uniqueUpgradeNotificationEmbed],
          });
        } else {
          await interaction.followUp({ embeds: [successEmbed] }); // Detailed success costs as follow-up (Removed Ephemeral Flag)
        }
      } else {
        // Was a slash command
        // Always show the main upgrade success embed first
        await interaction.editReply({ embeds: [successEmbed] });

        // For unique upgrades, send the unique notification as a follow-up
        if (uniqueUpgradeNotificationEmbed) {
          await interaction.followUp({
            embeds: [uniqueUpgradeNotificationEmbed],
          });
        }
      }
    } catch (error) {
      console.error(
        `[Minions Upgrade] Error during upgrade process for ${userId}, minion ${minionId}:`,
        error
      );
      const errorContent =
        "An error occurred during the upgrade. Resources might not have been deducted. Please check or contact an Admin.";
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Upgrade Failed")
        .setDescription(errorContent);
      if (isComponentInteraction) {
        // If it's a component, its original message was deferred. Follow up with error.
        await interaction.followUp({ embeds: [errorEmbed] }); // Removed Ephemeral Flag
      } else if (!interaction.replied && !interaction.deferred) {
        // Should not happen for slash command due to initial deferReply
        await interaction.reply({ embeds: [errorEmbed] }); // Removed Ephemeral Flag
      } else {
        // Slash command, already deferred
        await interaction.editReply({
          content: errorContent,
          embeds: [],
          components: [],
        });
      }
    }
  }); // Close withMinionLock
}

async function handleClearFuel(interaction, character, minion) {
  try {
    // Check if minion has fuel
    if (
      !minion.fuel ||
      !minion.fuel.expiresAt ||
      Date.now() >= minion.fuel.expiresAt
    ) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.PINK_RED)
        .setTitle("No Fuel to Clear")
        .setDescription("This minion does not have any active fuel to clear.");
      return interaction.followUp({ embeds: [embed], ephemeral: true });
    }

    // Show confirmation dialog
    const confirmEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ORANGE)
      .setTitle("Clear Fuel Confirmation")
      .setDescription("Are you sure you want to clear the fuel in this minion?")
      .setFooter({
        text: "Warning: This action cannot be undone and the fuel will be permanently lost.",
      });

    const confirmRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`confirm_clearfuel_${minion.id}`)
        .setLabel("Confirm")
        .setStyle(ButtonStyle.Danger),
      new ButtonBuilder()
        .setCustomId(`cancel_clearfuel_${minion.id}`)
        .setLabel("Cancel")
        .setStyle(ButtonStyle.Secondary)
    );

    const confirmMessage = await interaction.followUp({
      embeds: [confirmEmbed],
      components: [confirmRow],
    });

    // Set up collector for confirmation
    const filter = (i) =>
      i.user.id === interaction.user.id &&
      (i.customId === `confirm_clearfuel_${minion.id}` ||
        i.customId === `cancel_clearfuel_${minion.id}`);

    const collector = confirmMessage.createMessageComponentCollector({
      filter,
      time: 30000,
      max: 1,
    });

    collector.on("collect", async (i) => {
      await i.deferUpdate();

      if (i.customId === `confirm_clearfuel_${minion.id}`) {
        // Clear the fuel
        minion.fuel = null;

        // Save the updated character data
        const { savePlayerData } = require("../utils/playerDataManager");
        await savePlayerData(interaction.user.id, character);

        // Refresh the minion view
        const mockInteraction = {
          user: interaction.user,
          guild: interaction.guild,
          channel: interaction.channel,
          options: {
            getString: (key) => {
              if (key === "id") return minion.id;
              return null;
            },
          },
          reply: async (options) => {
            try {
              await interaction.editReply(options);
            } catch (error) {
              console.error("[Clear Fuel] Error updating interaction:", error);
            }
          },
          editReply: async (options) => {
            try {
              await interaction.editReply(options);
            } catch (error) {
              console.error("[Clear Fuel] Error editing reply:", error);
            }
          },
          fetchReply: () => interaction.fetchReply(),
          deferred: true,
          replied: false,
        };

        await handleView(mockInteraction, character, minion);
      }

      // Delete the confirmation message
      try {
        await confirmMessage.delete();
      } catch (error) {
        console.error(
          "[Clear Fuel] Error deleting confirmation message:",
          error
        );
      }
    });

    collector.on("end", async (collected) => {
      if (collected.size === 0) {
        // Timeout - delete the confirmation message
        try {
          await confirmMessage.delete();
        } catch (error) {
          console.error(
            "[Clear Fuel] Error deleting confirmation message on timeout:",
            error
          );
        }
      }
    });
  } catch (error) {
    console.error("[Clear Fuel] Error:", error);
    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("Error")
      .setDescription(
        "An error occurred while clearing the fuel. Please try again."
      );
    await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
  }
}

async function handleRemove(interaction) {
  await interaction.deferReply();
  const userId = interaction.user.id;
  const id = interaction.options.getString("id");
  const { atomicMinionRemove } = require("../utils/atomicMinionOperations");

  // get character data to validate minion exists
  const character = await getPlayerData(userId);
  if (!character) {
    return interaction.editReply("Could not load your character data.");
  }

  const placedMinions = character.island?.placedMinions || [];
  const minionIndex = placedMinions.findIndex((minion) => minion.id === id);
  if (minionIndex === -1) {
    return interaction.editReply(
      `Could not find a minion with ID \`${id}\` placed on your island.`
    );
  }

  const minionToRemove = placedMinions[minionIndex];

  // perform atomic removal operation
  const result = await atomicMinionRemove(userId, id);

  if (!result.success) {
    console.error(
      `[Minions Remove] Atomic removal failed for ${userId}, minion ${id}:`,
      result.error
    );

    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("Minion Removal Failed")
      .setDescription(
        `[MIN-REM-002] ${result.error}. Please report this error code to an Admin.`
      );
    return interaction.editReply({ embeds: [errorEmbed] });
  }

  // success - send confirmation message
  const minionDisplayInfo = getMinionDisplayInfo(minionToRemove);
  let successMessage = `Successfully removed ${minionDisplayInfo.emoji} **${minionDisplayInfo.name}** from your island and returned it to your minion storage.`;

  const itemsReturned = result.itemsReturned || [];
  const coinsReturned = result.coinsReturned || 0;

  const resourceItems = itemsReturned.filter(
    (item) => item.itemKey !== "COINS"
  );
  const upgradeItems = itemsReturned.filter((item) => {
    const allItems = configManager.getAllItems();
    const itemDef = allItems[item.itemKey];
    return itemDef?.upgradeEffect;
  });

  if (resourceItems.length > 0 && upgradeItems.length > 0) {
    successMessage +=
      " All stored resources and upgrades have been returned to your inventory.";
  } else if (resourceItems.length > 0 || coinsReturned > 0) {
    successMessage +=
      " All stored resources have been returned to your inventory.";
  } else if (upgradeItems.length > 0) {
    successMessage += " Any upgrades have been returned to your inventory.";
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.LIGHT_GREEN)
    //.setTitle("Minion Removed")
    .setDescription(successMessage);
  await interaction.editReply({ embeds: [embed] });
}

async function handleCollectAll(interaction) {
  await interaction.deferReply();
  const userId = interaction.user.id;

  // Use minion lock to prevent race conditions with individual collection operations
  const { withMinionLock } = require("../utils/minionMutex");

  return await withMinionLock(userId, "collect_all", async () => {
    const VERBOSE =
      process.env.VERBOSE_LOGGING === "true" ||
      global?.VERBOSE_LOGGING === true;
    const tStart = Date.now();
    let character = await getPlayerData(userId);

    if (!character) {
      return interaction.editReply("Could not load your character data.");
    }
    if (
      !character.island ||
      !character.island.placedMinions ||
      character.island.placedMinions.length === 0
    ) {
      return interaction.editReply(
        "You have no minions placed on your island to collect from."
      );
    }

    // Limit number of minions to prevent memory issues
    const MAX_MINIONS_PER_COLLECT_ALL = 50;
    if (character.island.placedMinions.length > MAX_MINIONS_PER_COLLECT_ALL) {
      return interaction.editReply(
        `❌ You have too many minions placed (${character.island.placedMinions.length}). ` +
          `Collect All is limited to ${MAX_MINIONS_PER_COLLECT_ALL} minions for performance reasons. ` +
          `Please collect from minions individually or remove some minions.`
      );
    }

    const allItems = configManager.getAllItems();
    const totalCollectedItemsMap = {};
    const totalCoinsCostFromButcher = 0;
    const collectionNotifications = [];
    let anyCollected = false;

    // --- Batch 1: In-memory updates and aggregation ---
    const tGenStart = Date.now();
    for (const minion of character.island.placedMinions) {
      // Generate items (includes automatic compacting) - same as background checks
      const { effectiveTimestamp } = await calculateMinionGeneration(minion);
      const itemsCollectedFromThisMinion = {};
      let collectedFromThisMinionFlag = false;

      // Collect ALL stored resources (after generation and compacting)
      // This includes both newly generated items and previously stored items (now compacted)
      if (minion.resourcesStored) {
        for (const key of Object.keys(minion.resourcesStored)) {
          const currentlyStoredAmount = minion.resourcesStored[key];
          if (currentlyStoredAmount > 0) {
            itemsCollectedFromThisMinion[key] =
              (itemsCollectedFromThisMinion[key] || 0) + currentlyStoredAmount;
            collectedFromThisMinionFlag = true;
          }
        }
      }

      if (collectedFromThisMinionFlag) {
        anyCollected = true;
        for (const [itemKey, amountCollected] of Object.entries(
          itemsCollectedFromThisMinion
        )) {
          totalCollectedItemsMap[itemKey] =
            (totalCollectedItemsMap[itemKey] || 0) + amountCollected;

          // In-memory update for this minion's stored resources
          if (
            minion.resourcesStored &&
            typeof minion.resourcesStored[itemKey] === "number"
          ) {
            minion.resourcesStored[itemKey] -= amountCollected;
            if (minion.resourcesStored[itemKey] <= 0) {
              delete minion.resourcesStored[itemKey];
            }
          }
        }
        // Ensure minion storage is completely cleared after collection
        minion.resourcesStored = {};
      }
      minion.lastCollectionTimestamp = effectiveTimestamp; // Always update timestamp
    }
    const tGenEnd = Date.now();

    if (!anyCollected) {
      // Still save if only timestamps changed for some minions
      try {
        // Use targeted update for island data only (no collections to save)
        const { dbRunQueued } = require("../utils/dbUtils");
        await dbRunQueued(
          "UPDATE players SET island_json = ? WHERE discord_id = ?",
          [JSON.stringify(character.island), userId]
        );
      } catch (saveError) {
        console.error(
          "[Minions CollectAll] Error saving timestamp updates (no resources collected):",
          saveError
        );
      }
      return interaction.editReply(
        "No new resources were generated by any of your minions to collect at this time."
      );
    }

    // --- New: Apply collection progress once per item key (aggregate) ---
    // This reduces CPU work and avoids emitting duplicate notifications
    const nonCoinEntries = Object.entries(totalCollectedItemsMap).filter(
      ([key]) => key !== "COINS"
    );
    const tCollStart = Date.now();
    if (nonCoinEntries.length > 0) {
      for (const [itemKey, totalAmount] of nonCoinEntries) {
        const progressResult = await addCollectionProgress(
          userId,
          itemKey,
          totalAmount,
          character,
          { allowConversion: true }
        );
        if (progressResult.character) character = progressResult.character;
        if (progressResult.notifications)
          collectionNotifications.push(...progressResult.notifications);
      }
    }
    const tCollEnd = Date.now();

    // --- Batch 2: Atomic inventory update and saving ---
    const itemsToChangeForAtomic = Object.entries(totalCollectedItemsMap).map(
      ([key, amount]) => ({ itemKey: key, amount })
    );

    // Separate coins from regular items for atomic update
    const coinsToAdd = totalCollectedItemsMap["COINS"] || 0;
    const itemsToChangeForAtomicFiltered = itemsToChangeForAtomic.filter(
      (item) => item.itemKey !== "COINS"
    );

    try {
      const tAtomicStart = Date.now();
      // Pass island and collections data to the atomic update with retry logic
      const maxRetries = 3;

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          // Only include collections JSON if we updated any non-coin collections this run
          const collectionsJsonString =
            nonCoinEntries.length > 0
              ? JSON.stringify(character.collections || {})
              : null;

          await updateInventoryAtomically(
            userId,
            -totalCoinsCostFromButcher + coinsToAdd,
            itemsToChangeForAtomicFiltered,
            [],
            [],
            0,
            [],
            JSON.stringify(character.island),
            collectionsJsonString
          );
          break; // Success, exit retry loop
        } catch (retryError) {
          console.warn(
            `[Minions CollectAll] Attempt ${attempt}/${maxRetries} failed for ${userId}: ${retryError.message}`
          );

          // Check if this is a transaction-related error that might benefit from retry
          const isRetryableError =
            retryError.message?.includes("cannot commit") ||
            retryError.message?.includes("no transaction is active") ||
            retryError.message?.includes("database is locked") ||
            retryError.code === "SQLITE_BUSY" ||
            retryError.code === "SQLITE_LOCKED";

          if (!isRetryableError || attempt === maxRetries) {
            throw retryError; // Re-throw if not retryable or max attempts reached
          }

          // Wait before retry with exponential backoff
          const delay = Math.min(100 * Math.pow(2, attempt - 1), 1000); // 100ms, 200ms, 400ms (max 1s)
          await new Promise((resolve) => {
            setTimeout(resolve, delay);
          });
        }
      }

      // Compute final totals locally (avoid an extra DB read)
      const computeFinalTotal = (key, delta) => {
        if (key === "COINS") {
          const before = character.coins || 0;
          return before + (coinsToAdd || 0);
        }
        const before = character.inventory?.items?.[key] || 0;
        return before + (delta || 0);
      };

      // Build the success output and paginate to respect Discord's 4096-char embed description limit
      const lines = [];
      for (const [key, amt] of Object.entries(totalCollectedItemsMap)) {
        const item = allItems[key] || {
          name: key.replace(/_/g, " ").toUpperCase(),
          emoji: "",
        };
        const total = computeFinalTotal(key, amt);
        lines.push(
          `- ${item.emoji} **${item.name}**: ${formatNumber(amt)} (${formatNumber(total)})`
        );
      }

      const MAX_DESC = 3800; // headroom under 4096
      const pages = [];
      if (lines.length === 0) {
        pages.push("No items were collected this time.");
      } else {
        let buf = "";
        for (const l of lines) {
          if ((buf + l + "\n").length > MAX_DESC) {
            pages.push(buf.trim());
            buf = "";
          }
          buf += l + "\n";
        }
        if (buf.trim().length > 0) pages.push(buf.trim());
      }

      const buildEmbedForPage = (idx) => {
        const e = new EmbedBuilder()
          .setColor(EMBED_COLORS.LIGHT_GREEN)
          .setTitle("All Resources Collected!")
          .setDescription(pages[idx]);
        if (pages.length > 1)
          e.setFooter({ text: `Page ${idx + 1} / ${pages.length}` });
        return e;
      };

      const buildNavRow = (idx) => {
        if (pages.length <= 1) return [];
        return [
          new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId("collectall_prev")
              .setLabel("◀ Previous")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(idx === 0),
            new ButtonBuilder()
              .setCustomId("collectall_page")
              .setLabel(`${idx + 1}/${pages.length}`)
              .setStyle(ButtonStyle.Primary)
              .setDisabled(true),
            new ButtonBuilder()
              .setCustomId("collectall_next")
              .setLabel("Next ▶")
              .setStyle(ButtonStyle.Secondary)
              .setDisabled(idx === pages.length - 1)
          ),
        ];
      };

      let currentPage = 0;
      const initialEmbed = buildEmbedForPage(currentPage);
      const initialComponents = buildNavRow(currentPage);

      await interaction.editReply({
        embeds: [initialEmbed],
        components: initialComponents,
      });

      // Set up pagination collector if multiple pages
      if (pages.length > 1) {
        try {
          const message = await interaction.fetchReply();
          const filter = (i) =>
            i.user.id === interaction.user.id &&
            ["collectall_prev", "collectall_next"].includes(i.customId);
          const collector = message.createMessageComponentCollector({
            filter,
            componentType: ComponentType.Button,
          });

          collector.on("collect", async (i) => {
            try {
              if (!i.replied && !i.deferred) {
                await i.deferUpdate();
              }
            } catch (e) {
              if (e?.code !== 10062 && e?.code !== 40060) {
                console.warn(
                  "[Minions CollectAll] deferUpdate failed:",
                  e?.message || e
                );
              }
              return;
            }

            if (i.customId === "collectall_prev")
              currentPage = Math.max(0, currentPage - 1);
            if (i.customId === "collectall_next")
              currentPage = Math.min(pages.length - 1, currentPage + 1);

            try {
              await i.editReply({
                embeds: [buildEmbedForPage(currentPage)],
                components: buildNavRow(currentPage),
              });
            } catch (e) {
              if (e?.code !== 10062 && e?.code !== 40060) {
                console.error(
                  "[Minions CollectAll] editReply failed:",
                  e?.message || e
                );
              }
            }
          });
        } catch (e) {
          console.error(
            "[Minions CollectAll] Failed to set up pagination:",
            e?.message || e
          );
        }
      }

      const tAtomicEnd = Date.now();

      if (collectionNotifications.length > 0) {
        try {
          // Send one-by-one as brand new channel messages with 500ms spacing
          const chan = interaction.channel;
          for (const embed of collectionNotifications) {
            if (chan && typeof chan.send === "function") {
              await chan.send({ embeds: [embed] });
            } else {
              // Fallback if channel unavailable
              await interaction.followUp({ embeds: [embed] });
            }
            await new Promise((res) => setTimeout(res, 500));
          }
        } catch (followUpError) {
          console.error(
            `[Minions CollectAll] Failed to send collection notifications for ${userId}:`,
            followUpError
          );
        }
      }

      // Check for Disblock XP achievements after collect all
      try {
        await checkAndNotifyDisblockXP(userId, interaction);
      } catch (xpError) {
        console.error(
          "[Minions CollectAll] Error updating Disblock XP:",
          xpError
        );
        // Continue with minions command even if XP update fails
      }

      if (VERBOSE) {
        const tEnd = Date.now();
        console.log(
          `[Minions CollectAll] Timings for ${userId} -> gen: ${tGenEnd - tGenStart}ms, collections: ${tCollEnd - tCollStart}ms, atomic: ${tAtomicEnd - tAtomicStart}ms, total: ${tEnd - tStart}ms, notifications: ~${collectionNotifications.length * 500}ms (user-facing)`
        );
      }
    } catch (atomicError) {
      console.error(
        `[Minions CollectAll] Error during atomic inventory update for ${userId}:`,
        atomicError
      );
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Collect All Failed")
        .setDescription(
          `[MIN-COLALL-001] An error occurred while adding items to your inventory: ${atomicError.message || "Unknown error"}. No items were collected, and minion states were not updated. Please report this code to an Admin.`
        );
      await interaction.editReply({ embeds: [errorEmbed] });
    }
  }); // Close withMinionLock
}

async function handleButcher(interaction) {
  const notImplementedEmbed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ORANGE) // Orange for informational
    .setTitle("Feature Not Implemented")
    .setDescription(
      "The `/minions butcher` functionality is not yet available. Please check back later."
    );
  await interaction.reply({ embeds: [notImplementedEmbed], ephemeral: true });
}

async function handleSlots(interaction) {
  try {
    // Defer the reply immediately to prevent timeout
    await interaction.deferReply();

    const userId = interaction.user.id;
    const character = await getPlayerData(userId);

    if (!character) {
      const charLoadErrorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Character Data Error")
        .setDescription("Could not load your character data.");
      return interaction.editReply({ embeds: [charLoadErrorEmbed] });
    }

    const uniqueCraftsCount = character.craftedMinions?.length || 0;
    const totalSlots = calculateMaxMinionSlots(character);

    // Find next unlock goal
    let nextGoal = null;
    let nextBonus = 0;
    for (const tier of MINION_SLOT_UNLOCKS) {
      if (uniqueCraftsCount < tier.crafts) {
        nextGoal = tier.crafts;
        nextBonus = tier.bonus;
        break; // Found the next tier
      }
    }
    const totalSlotsAtNextGoal = BASE_MINION_SLOTS + nextBonus; // Calculate total slots for next goal

    // Organize minions by category
    const allItems = configManager.getAllItems();
    const playerCraftedSet = new Set(character.craftedMinions || []);
    const minionItems = Object.entries(allItems).filter(
      ([, item]) => item.type === "MINION"
    );

    // Group minions by category
    const minionsByCategory = {};
    const categoryEmojis = {
      mining: "⛏️",
      farming: "🌾",
      combat: "⚔️",
      fishing: "🎣",
      foraging: "🪓",
      other: "❓",
    };

    for (const [baseKey, minionDef] of minionItems) {
      if (!minionDef.tiers || minionDef.tiers.length <= 1) continue;

      const category = minionDef.category || "other";
      if (!minionsByCategory[category]) {
        minionsByCategory[category] = [];
      }
      minionsByCategory[category].push([baseKey, minionDef]);
    }

    // Sort categories and minions within each category
    const sortedCategories = Object.keys(minionsByCategory).sort();
    for (const category of sortedCategories) {
      minionsByCategory[category].sort(([, a], [, b]) =>
        (a.name || "").localeCompare(b.name || "")
      );
    }

    // Create pagination data with compact display
    const pages = [];

    for (const category of sortedCategories) {
      const categoryMinions = minionsByCategory[category];
      const categoryEmoji = categoryEmojis[category] || "❓";
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);

      const checklistLines = [];
      let allMinionsInCategoryMaxed = true;
      const categoryProgress = { crafted: 0, total: 0 };

      for (const [baseKey, minionDef] of categoryMinions) {
        const emoji = minionDef.emoji || "❓";
        const name = minionDef.name || baseKey;
        const maxTier = minionDef.tiers.length - 1;

        let craftedTiers = 0;
        for (let t = 1; t <= maxTier; t++) {
          const tierKey = `${baseKey.toUpperCase()}_T${t}`;
          if (playerCraftedSet.has(tierKey)) {
            craftedTiers++;
          }
        }

        if (craftedTiers < maxTier) {
          allMinionsInCategoryMaxed = false;
        }

        categoryProgress.crafted += craftedTiers;
        categoryProgress.total += maxTier;

        // Status indicator
        let statusIcon = "🔴"; // Not started
        if (craftedTiers === maxTier) {
          statusIcon = "✅"; // Complete
        } else if (craftedTiers > 0) {
          statusIcon = "🟡"; // In progress
        }

        checklistLines.push(
          `${statusIcon} ${emoji} **${name}** \`${craftedTiers}/${maxTier}\``
        );
      }

      let description = checklistLines.join("\n");

      // Add category summary
      const categoryPercent =
        categoryProgress.total > 0
          ? Math.round(
              (categoryProgress.crafted / categoryProgress.total) * 100
            )
          : 0;
      description = `**Category Progress: ${categoryProgress.crafted}/${categoryProgress.total} (${categoryPercent}%)**\n\n${description}`;

      if (allMinionsInCategoryMaxed && checklistLines.length > 0) {
        description += "\n\n✨ **All minions in this category completed!** ✨";
      }

      pages.push({
        category,
        title: `${categoryEmoji} ${categoryName} Minions`,
        description,
        count: categoryMinions.length,
      });
    }

    // If no pages, create a default page
    if (pages.length === 0) {
      pages.push({
        category: "none",
        title: "No Minions Available",
        description: "No minions have been defined in the game yet.",
        count: 0,
      });
    }

    // Show pagination
    await showMinionSlotsPage(
      interaction,
      character,
      pages,
      0,
      uniqueCraftsCount,
      totalSlots,
      nextGoal,
      totalSlotsAtNextGoal
    );
  } catch (error) {
    console.error("[Minions Slots] Error in handleSlots:", error);
    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("Slots Command Error")
      .setDescription(
        "An error occurred while loading your minion slots. Please try again."
      );

    try {
      await interaction.editReply({ embeds: [errorEmbed] });
    } catch (replyError) {
      console.error(
        "[Minions Slots] Failed to send error message:",
        replyError
      );
    }
  }
}

// Helper function to show minion slots page with pagination
async function showMinionSlotsPage(
  interaction,
  character,
  pages,
  currentPage,
  uniqueCraftsCount,
  totalSlots,
  nextGoal,
  totalSlotsAtNextGoal,
  existingCollector = null
) {
  const page = pages[currentPage];
  const totalPages = pages.length;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.DARK_GOLD) // Gold-ish color
    .setTitle(`${character.name}'s Minion Slots`)
    .setDescription(page.description);

  // Add fields (Unique Crafts, Total Slots)
  embed.addFields(
    {
      name: "Unique Minions Crafted",
      value: `${uniqueCraftsCount.toLocaleString()}`,
      inline: true,
    },
    { name: "Total Minion Slots", value: `**${totalSlots}**`, inline: true }
  );

  // Add Next Unlock field
  if (nextGoal !== null) {
    const needed = nextGoal - uniqueCraftsCount;
    embed.addFields({
      name: "Next Unlock",
      value: `Craft **${needed}** more **unique minions** to reach **${totalSlotsAtNextGoal}** minion slots.`,
      inline: false,
    });
  } else {
    embed.addFields({
      name: "Next Unlock",
      value: "You have unlocked all available minion slots!",
      inline: false,
    });
  }

  // Add footer with page info
  embed.setFooter({
    text: `${page.title} • Page ${currentPage + 1}/${totalPages}`,
  });

  // Create navigation buttons
  const row = new ActionRowBuilder();

  // Previous button
  row.addComponents(
    new ButtonBuilder()
      .setCustomId("minion_slots_prev")
      .setLabel("◀ Previous")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage === 0)
  );

  // Page indicator
  row.addComponents(
    new ButtonBuilder()
      .setCustomId("minion_slots_page")
      .setLabel(`${currentPage + 1}/${totalPages}`)
      .setStyle(ButtonStyle.Primary)
      .setDisabled(true)
  );

  // Next button
  row.addComponents(
    new ButtonBuilder()
      .setCustomId("minion_slots_next")
      .setLabel("Next ▶")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage === totalPages - 1)
  );

  const components = totalPages > 1 ? [row] : [];

  // Update the message
  if (interaction.replied || interaction.deferred) {
    await interaction.editReply({ embeds: [embed], components });
  } else {
    await interaction.reply({ embeds: [embed], components });
  }

  // Only set up collector on the first call (when existingCollector is null)
  if (totalPages > 1 && !existingCollector) {
    const message = await interaction.fetchReply();
    const filter = (i) =>
      i.user.id === interaction.user.id &&
      i.customId.startsWith("minion_slots_");
    const collector = message.createMessageComponentCollector({
      filter,
      componentType: ComponentType.Button,
    }); // No timeout - collector stays active indefinitely

    let currentPageState = currentPage;

    collector.on("collect", async (i) => {
      try {
        // Check if interaction is already acknowledged
        if (i.replied || i.deferred) {
          return;
        }

        await i.deferUpdate();

        let newPage = currentPageState;
        if (i.customId === "minion_slots_prev") {
          newPage = Math.max(0, currentPageState - 1);
        } else if (i.customId === "minion_slots_next") {
          newPage = Math.min(totalPages - 1, currentPageState + 1);
        }

        if (newPage !== currentPageState) {
          currentPageState = newPage;
          await showMinionSlotsPage(
            i,
            character,
            pages,
            newPage,
            uniqueCraftsCount,
            totalSlots,
            nextGoal,
            totalSlotsAtNextGoal,
            collector
          );
        }
      } catch (error) {
        // Ignore interaction timeout and already acknowledged errors
        if (error.code !== 10062 && error.code !== 40060) {
          console.error(
            "[Minions Slots Pagination] Error handling button interaction:",
            error
          );
        }
      }
    });

    // Removed automatic component disabling on collector end
  }
}

// ++ New Helper Function: handleStorageOverflow ++
async function handleStorageOverflow(
  characterData,
  minionToUpdate,
  allItems,
  unequippedUpgradeKey = null,
  slotIndexOfUnequipped = -1
) {
  const messages = [];
  const baseMinionDef = allItems[minionToUpdate.itemKey];
  const tierData = baseMinionDef?.tiers?.[minionToUpdate.tier];
  const baseStorage = tierData?.maxStorage ?? 0;

  // Calculate effective upgrades *after* the unequip has notionally happened for this check
  const effectiveUpgrades = [...minionToUpdate.upgrades]; // Create a mutable copy
  if (
    unequippedUpgradeKey &&
    slotIndexOfUnequipped !== -1 &&
    effectiveUpgrades[slotIndexOfUnequipped] === unequippedUpgradeKey
  ) {
    // If we are simulating an unequip for calculation, temporarily remove it
    effectiveUpgrades[slotIndexOfUnequipped] = null;
  }

  let newExtraStorage = 0;
  for (const upgradeKey of effectiveUpgrades) {
    if (!upgradeKey) continue;
    const upgradeItem = allItems[upgradeKey];
    if (
      upgradeItem &&
      upgradeItem.upgradeEffect &&
      typeof upgradeItem.upgradeEffect.extraStorage === "number"
    ) {
      // Storage upgrades stack - add them all together
      newExtraStorage += upgradeItem.upgradeEffect.extraStorage;
    }
  }
  const newMaxStorage = baseStorage + newExtraStorage;

  let totalCurrentlyStored = 0;
  if (minionToUpdate.resourcesStored) {
    for (const [key, amount] of Object.entries(
      minionToUpdate.resourcesStored
    )) {
      if (key !== "COINS") {
        // Exclude coins from storage count
        totalCurrentlyStored += amount;
      }
    }
  }

  const itemsToMoveToInventory = []; // Declare this outside the if block so it's always available

  if (totalCurrentlyStored > newMaxStorage) {
    let overflowAmount = totalCurrentlyStored - newMaxStorage;

    // Prioritize removing items to get back to maxStorage
    // Iterate over a copy of keys to allow modification of resourcesStored
    const storedResourceKeys = Object.keys(
      minionToUpdate.resourcesStored || {}
    );

    for (const resourceKey of storedResourceKeys) {
      if (overflowAmount <= 0) break;
      if (resourceKey === "COINS") continue; // Skip coins - they don't count toward storage limit

      const amountStored = minionToUpdate.resourcesStored[resourceKey];
      const amountToRemoveFromThisStack = Math.min(
        amountStored,
        overflowAmount
      );

      itemsToMoveToInventory.push({
        itemKey: resourceKey,
        amount: amountToRemoveFromThisStack,
      });
      minionToUpdate.resourcesStored[resourceKey] =
        amountStored - amountToRemoveFromThisStack;
      if (minionToUpdate.resourcesStored[resourceKey] <= 0) {
        delete minionToUpdate.resourcesStored[resourceKey];
      }
      overflowAmount -= amountToRemoveFromThisStack;
    }

    if (itemsToMoveToInventory.length > 0) {
      messages.push("Minion storage exceeded after unequip!");
      itemsToMoveToInventory.forEach((item) => {
        const itemDetails = allItems[item.itemKey];
        messages.push(
          `Moved ${item.amount.toLocaleString()} ${itemDetails?.emoji || ""} **${itemDetails?.name || item.itemKey}** to your inventory.`
        );
      });
    }
  }
  // Ensure characterData reflects the changes to minionToUpdate.resourcesStored, as it's a reference
  // No direct save here, should be saved by the calling function.
  return {
    messages,
    itemsMoved: itemsToMoveToInventory,
    character: characterData,
  }; // Return items that need to be moved to inventory
}

// Global collector storage for cleanup
const activeCollectors = new Map();

// Rate limiting for rapid clicks
const lastInteractionTime = new Map();
const INTERACTION_COOLDOWN = 1000; // 1 second cooldown

async function showUpgradeManagementMenu(interaction, character, minion) {
  // The 'interaction' here is the StringSelectMenuInteraction from handleView's collector.
  // It should have been deferred with deferUpdate() already.

  // Check if interaction is still valid
  if (
    !interaction ||
    interaction.deferred === false ||
    (!interaction.deferred && !interaction.replied)
  ) {
    console.error("[ShowUpgradeMenu] Invalid interaction state:", {
      exists: !!interaction,
      deferred: interaction?.deferred,
      replied: interaction?.replied,
    });
    return;
  }

  // Stop any existing collectors for this user/minion
  const userId = interaction.user.id;
  const upgradeCollectorKey = `upgrade_${userId}_${minion.id}`;
  const mainCollectorKey = `main_${userId}_${minion.id}`;

  // Stop upgrade collector if it exists
  if (activeCollectors.has(upgradeCollectorKey)) {
    const existingCollector = activeCollectors.get(upgradeCollectorKey);
    existingCollector.stop("new_menu_opened");
    activeCollectors.delete(upgradeCollectorKey);
  }

  // Stop main collector when entering upgrade management
  if (activeCollectors.has(mainCollectorKey)) {
    const existingCollector = activeCollectors.get(mainCollectorKey);
    existingCollector.stop("upgrade_menu_opened");
    activeCollectors.delete(mainCollectorKey);
  }

  initializeMinionUpgrades(minion);
  const allItems = configManager.getAllItems();
  const inventory = character.inventory?.items || {};
  const availableUpgrades = Object.keys(inventory)
    .filter(
      (key) => allItems[key]?.type === "MINION_UPGRADE" && inventory[key] > 0
    )
    .map((key) => ({
      key,
      name: allItems[key].name,
      emoji: allItems[key].emoji || "",
      amount: inventory[key],
    }));

  // Handle backwards compatibility - detect and consolidate duplicate upgrades
  // Use mutex to prevent race conditions during consolidation
  const { withMinionLock } = require("../utils/minionMutex");

  const currentlyEquipped = minion.upgrades.filter(
    (upgrade) => upgrade !== null
  );
  const duplicateConsolidationMessages = [];

  // Count occurrences of each upgrade type
  const upgradeTypeCounts = {};
  currentlyEquipped.forEach((upgradeKey) => {
    upgradeTypeCounts[upgradeKey] = (upgradeTypeCounts[upgradeKey] || 0) + 1;
  });

  // Check for duplicates and consolidate them with mutex protection
  const consolidatedUpgrades = [];
  const itemsToReturnToInventory = [];

  for (const [upgradeKey, count] of Object.entries(upgradeTypeCounts)) {
    consolidatedUpgrades.push(upgradeKey); // Keep one of each type

    if (count > 1) {
      const extraCount = count - 1;
      const upgradeItem = allItems[upgradeKey];

      // Return extras to inventory
      itemsToReturnToInventory.push({
        itemKey: upgradeKey,
        amount: extraCount,
      });

      duplicateConsolidationMessages.push(
        `Found ${count}x ${upgradeItem?.emoji || ""} **${upgradeItem?.name || upgradeKey}** equipped. Kept 1 and returned ${extraCount} to inventory.`
      );
    }
  }

  // If we found duplicates, update the minion and inventory atomically with mutex protection
  if (itemsToReturnToInventory.length > 0) {
    await withMinionLock(
      interaction.user.id,
      "consolidate_upgrades",
      async () => {
        // Get fresh character data to ensure we're working with current state
        const freshCharacter = await getPlayerData(interaction.user.id);
        const freshMinion = freshCharacter?.island?.placedMinions?.find(
          (m) => m.id === minion.id
        );

        if (!freshMinion) {
          console.error(
            "[Upgrade Management] Minion not found during consolidation"
          );
          return;
        }

        // Re-check if consolidation is still needed with fresh data
        const freshCurrentlyEquipped = freshMinion.upgrades.filter(
          (upgrade) => upgrade !== null
        );
        const freshUpgradeTypeCounts = {};
        freshCurrentlyEquipped.forEach((upgradeKey) => {
          freshUpgradeTypeCounts[upgradeKey] =
            (freshUpgradeTypeCounts[upgradeKey] || 0) + 1;
        });

        const freshItemsToReturn = [];
        const freshConsolidatedUpgrades = [];

        for (const [upgradeKey, count] of Object.entries(
          freshUpgradeTypeCounts
        )) {
          freshConsolidatedUpgrades.push(upgradeKey);
          if (count > 1) {
            freshItemsToReturn.push({
              itemKey: upgradeKey,
              amount: count - 1,
            });
          }
        }

        // Only proceed if duplicates still exist
        if (freshItemsToReturn.length > 0) {
          // Update minion upgrades array
          const newUpgradesArray = new Array(freshMinion.upgrades.length).fill(
            null
          );
          freshConsolidatedUpgrades.forEach((upgradeKey, index) => {
            if (index < newUpgradesArray.length) {
              newUpgradesArray[index] = upgradeKey;
            }
          });
          freshMinion.upgrades = newUpgradesArray;

          // Return duplicates to inventory atomically
          await updateInventoryAtomically(
            interaction.user.id,
            0,
            freshItemsToReturn,
            [],
            [],
            0,
            [],
            JSON.stringify(freshCharacter.island),
            null
          );

          // Update character reference for the rest of the function
          character = await getPlayerData(interaction.user.id);

          // Update minion reference to point to the fresh consolidated version
          const consolidatedMinion = character?.island?.placedMinions?.find(
            (m) => m.id === minion.id
          );
          if (consolidatedMinion) {
            Object.assign(minion, consolidatedMinion);
          }

          // Show consolidation message
          if (duplicateConsolidationMessages.length > 0) {
            const consolidationEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.ORANGE)
              .setTitle("Duplicate Upgrades Consolidated")
              .setDescription(
                `The new upgrade system only allows one of each upgrade type per minion. The following duplicates were automatically returned to your inventory:\n\n${duplicateConsolidationMessages.join("\n")}`
              )
              .setFooter({
                text: "This is a one-time conversion to the new system.",
              });

            await interaction.followUp({
              embeds: [consolidationEmbed],
              ephemeral: true,
            });
          }
        }
      }
    );
  }

  // Create simple upgrade selection menu - show all available upgrades
  // Currently equipped upgrades will be pre-selected (now deduplicated)
  const currentlyEquippedUnique = new Set(
    minion.upgrades.filter((upgrade) => upgrade !== null)
  );

  // Combine currently equipped + available upgrades, remove duplicates
  const allPossibleUpgrades = new Map();

  // Add currently equipped upgrades (even if not in inventory)
  for (const equippedKey of currentlyEquippedUnique) {
    if (equippedKey && allItems[equippedKey]) {
      allPossibleUpgrades.set(equippedKey, {
        key: equippedKey,
        name: allItems[equippedKey].name,
        emoji: allItems[equippedKey].emoji || "",
        isEquipped: true,
        available: inventory[equippedKey] || 0,
      });
    }
  }

  // Add available upgrades from inventory
  for (const upg of availableUpgrades) {
    if (!allPossibleUpgrades.has(upg.key)) {
      allPossibleUpgrades.set(upg.key, {
        key: upg.key,
        name: upg.name,
        emoji: upg.emoji,
        isEquipped: false,
        available: upg.amount,
      });
    } else {
      // Update available count for equipped items
      allPossibleUpgrades.get(upg.key).available = upg.amount;
    }
  }

  const upgradeOptions = Array.from(allPossibleUpgrades.values()).map((upg) => {
    const statusIcon = upg.isEquipped ? "✅" : "⬜";
    const availableText =
      upg.available > 0 ? ` (${upg.available} available)` : "";

    return {
      label: `${statusIcon} ${upg.name}${availableText}`,
      value: upg.key,
      emoji: upg.emoji || "❓",
      description: upg.isEquipped
        ? "Currently equipped - unselect to remove"
        : "Available to equip - select to add",
      // Pre-select currently equipped items
      default: upg.isEquipped,
    };
  });

  if (upgradeOptions.length === 0) {
    upgradeOptions.push({
      label: "No upgrades available",
      value: "none",
      description: "You don't have any minion upgrades to equip",
      emoji: "❌",
    });
  }

  const upgradeSelectMenu = new StringSelectMenuBuilder()
    .setCustomId(`minion_upgrade_simple_${minion.id}`)
    .setPlaceholder("Select which upgrades you want on this minion")
    .addOptions(upgradeOptions)
    .setMinValues(0) // Allow selecting nothing (remove all upgrades)
    .setMaxValues(Math.min(upgradeOptions.length, minion.upgrades.length)); // Limit to available slots

  const upgradeRow = new ActionRowBuilder().addComponents(upgradeSelectMenu);

  // Add a back button row
  const backButtonRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`minion_back_${minion.id}`)
      .setLabel("← Back")
      .setStyle(ButtonStyle.Secondary)
  );

  const rows = [upgradeRow, backButtonRow];

  let selectMenuMessage;
  try {
    // Use the same detailed embed as the main minion view
    const { embed: detailedEmbed } = createMinionViewEmbedAndComponents(
      minion,
      character
    );
    // Update the title to indicate we're in upgrade management mode
    detailedEmbed.setTitle(`${detailedEmbed.data.title} - Manage Upgrades`);
    // Add upgrade selection instruction
    detailedEmbed.setFooter({ text: "Select an upgrade for each slot below" });

    await interaction.editReply({
      embeds: [detailedEmbed],
      components: rows,
    });
    selectMenuMessage = await interaction.fetchReply();
  } catch (error) {
    console.error(
      "[ShowUpgradeMenu] Failed to interaction.editReply with select menus:",
      error
    );

    // Handle specific Discord API errors
    if (error.code === 10062) {
      console.error(
        "[ShowUpgradeMenu] Interaction expired (Unknown interaction)"
      );
      return; // Don't try fallback for expired interactions
    }
    try {
      // Use the same detailed embed for fallback as well
      const { embed: fallbackDetailedEmbed } =
        createMinionViewEmbedAndComponents(minion, character);
      fallbackDetailedEmbed.setTitle(
        `${fallbackDetailedEmbed.data.title} - Manage Upgrades (Fallback)`
      );
      fallbackDetailedEmbed.setFooter({
        text: "Select an upgrade for each slot below",
      });

      selectMenuMessage = await interaction.followUp({
        embeds: [fallbackDetailedEmbed],
        components: rows,
      });
    } catch (followUpError) {
      console.error(
        "[ShowUpgradeMenu] Fallback interaction.followUp also FAILED:",
        followUpError
      );
      const menuOpenErrorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Menu Error")
        .setDescription(
          "Sorry, couldn't open the upgrade menu. Please try again."
        );
      await interaction.editReply({
        embeds: [menuOpenErrorEmbed],
        components: [],
      });
      return;
    }
  }

  if (!selectMenuMessage) {
    console.error(
      "[ShowUpgradeMenu] CRITICAL: selectMenuMessage is null after attempting to send it."
    );
    const criticalMenuErrorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("Critical Menu Error")
      .setDescription(
        "Could not create upgrade menu. Please report this issue."
      );
    await interaction.editReply({
      embeds: [criticalMenuErrorEmbed],
      components: [],
    });
    return;
  }

  const filter = (i) =>
    i.user.id === interaction.user.id &&
    (i.customId === `minion_upgrade_simple_${minion.id}` ||
      i.customId === `minion_back_${minion.id}`);
  const collector = selectMenuMessage.createMessageComponentCollector({
    filter,
  }); // No timeout - collector stays active indefinitely

  // Store the collector for cleanup
  const collectorKey = `upgrade_${userId}_${minion.id}`;
  activeCollectors.set(collectorKey, collector);

  // Clean up collector reference when it ends
  collector.on("end", () => {
    activeCollectors.delete(collectorKey);
    // Clean up rate limiting entries for this user/minion
    const userKey = `${userId}_${minion.id}`;
    lastInteractionTime.delete(userKey);
  });

  collector.on("collect", async (i) => {
    try {
      // Handle back button without deferring first
      if (i.customId === `minion_back_${minion.id}`) {
        // Check if interaction is still valid
        if (i.replied || i.deferred) {
          console.warn(
            "[Upgrade Menu Back Button] Interaction already handled, skipping"
          );
          return;
        }

        // Rate limiting to prevent rapid clicking
        const userKey = `${i.user.id}_${minion.id}`;
        const now = Date.now();
        const lastTime = lastInteractionTime.get(userKey) || 0;
        if (now - lastTime < INTERACTION_COOLDOWN) {
          console.warn("[Upgrade Menu Back Button] Rate limited, skipping");
          return;
        }
        lastInteractionTime.set(userKey, now);

        // Additional check for interaction age (Discord interactions expire after 15 minutes)
        const interactionAge = Date.now() - i.createdTimestamp;
        if (interactionAge > 14 * 60 * 1000) {
          // 14 minutes to be safe
          console.warn(
            "[Upgrade Menu Back Button] Interaction too old, skipping"
          );
          return;
        }

        // Stop the current upgrade collector before going back
        collector.stop("back_button_pressed");

        // Fetch fresh character data to get updated minion state
        const freshCharacter = await getPlayerData(i.user.id);
        const updatedMinion = freshCharacter?.island?.placedMinions?.find(
          (m) => m.id === minion.id
        );

        if (!updatedMinion) {
          console.error(
            "[Upgrade Menu Back Button] Could not find updated minion"
          );
          return;
        }

        const { embed, components } = createMinionViewEmbedAndComponents(
          updatedMinion,
          freshCharacter
        );

        try {
          await i.update({ embeds: [embed], components });

          // Set up new main collector after successful navigation back
          const mainCollectorKey = `main_${i.user.id}_${updatedMinion.id}`;
          const sentMessage = await i.fetchReply();
          const mainFilter = (mainI) =>
            mainI.user.id === i.user.id &&
            mainI.customId === `minion_action_${updatedMinion.id}`;

          const mainCollector = sentMessage.createMessageComponentCollector({
            filter: mainFilter,
            componentType: ComponentType.StringSelect,
          });

          activeCollectors.set(mainCollectorKey, mainCollector);

          // Set up the main collector handlers (reusing the same logic from handleView)
          mainCollector.on("collect", async (mainI) => {
            // Check if interaction is still valid before processing
            if (mainI.replied || mainI.deferred) {
              console.warn(
                "[Main Collector] Interaction already handled, skipping"
              );
              return;
            }

            // Check interaction age
            const interactionAge = Date.now() - mainI.createdTimestamp;
            if (interactionAge > 14 * 60 * 1000) {
              console.warn("[Main Collector] Interaction too old, skipping");
              return;
            }

            const selectedAction = mainI.values[0];

            if (selectedAction !== "add_fuel") {
              try {
                await mainI.deferUpdate();
              } catch (error) {
                console.error(
                  "[Main Collector] Failed to defer interaction:",
                  error
                );
                return;
              }
            }

            try {
              if (selectedAction === "collect") {
                await handleCollect(mainI);
              } else if (selectedAction === "upgrade") {
                await handleUpgrade(mainI);
              } else if (selectedAction === "manage_upgrades") {
                await showUpgradeManagementMenu(
                  mainI,
                  freshCharacter,
                  updatedMinion
                );
              } else if (selectedAction === "add_fuel") {
                await showFuelMenu(mainI, freshCharacter, updatedMinion);
              } else if (selectedAction === "clear_fuel") {
                await handleClearFuel(mainI, freshCharacter, updatedMinion);
              }
            } catch (error) {
              console.error(
                `[Main Collector] Error handling ${selectedAction}:`,
                error
              );
            }
          });

          mainCollector.on("end", () => {
            activeCollectors.delete(mainCollectorKey);
          });
        } catch (error) {
          console.error(
            "[Upgrade Menu Back Button] Failed to update interaction:",
            error
          );
          // Don't attempt fallback for expired interactions
          if (error.code === 10062) {
            console.warn(
              "[Upgrade Menu Back Button] Interaction expired, cannot respond"
            );
          }
        }
        return;
      }

      // Defer update for simple upgrade menu
      // Check if interaction is already acknowledged before deferring
      if (!i.replied && !i.deferred) {
        await i.deferUpdate();
      }

      // Handle simple upgrade selection with mutex protection to prevent duplication
      const userId = i.user.id;
      const selectedUpgradeKeys = i.values; // Array of upgrade keys user wants equipped

      // Use mutex to prevent race conditions during upgrade changes
      const { withMinionLock } = require("../utils/minionMutex");

      await withMinionLock(userId, "upgrade_selection", async () => {
        const modCharacter = await getPlayerData(userId);
        if (!modCharacter) {
          await i.followUp({
            content: "Could not load your character data.",
            ephemeral: true,
          });
          return;
        }

        const minionToUpdate = modCharacter.island?.placedMinions?.find(
          (m) => m.id === minion.id
        );
        if (!minionToUpdate) {
          await i.followUp({
            content: "Could not find the specified minion to update.",
            ephemeral: true,
          });
          return;
        }
        initializeMinionUpgrades(minionToUpdate);

        const allItems = configManager.getAllItems();

        // Skip if user selected "none" (no upgrades available)
        if (
          selectedUpgradeKeys.length === 1 &&
          selectedUpgradeKeys[0] === "none"
        ) {
          return;
        }

        // Get currently equipped upgrades
        const currentlyEquipped = new Set(
          minionToUpdate.upgrades.filter((upgrade) => upgrade !== null)
        );
        const desiredUpgrades = new Set(selectedUpgradeKeys);

        // Determine what to add and remove
        const toRemove = [...currentlyEquipped].filter(
          (key) => !desiredUpgrades.has(key)
        );
        const toAdd = [...desiredUpgrades].filter(
          (key) => !currentlyEquipped.has(key)
        );

        // Validate we can equip the desired upgrades
        const finalUpgradeCount = desiredUpgrades.size;
        if (finalUpgradeCount > minionToUpdate.upgrades.length) {
          await i.followUp({
            content: `Cannot equip ${finalUpgradeCount} upgrades. This minion only has ${minionToUpdate.upgrades.length} upgrade slots.`,
            ephemeral: true,
          });
          return;
        }

        // Check for conflicts in desired upgrades
        const compactorUpgrades = ["COMPACTOR", "SUPER_COMPACTOR_3000"];
        const hopperUpgrades = ["BUDGET_HOPPER", "ENCHANTED_HOPPER"];

        const desiredCompactors = [...desiredUpgrades].filter((key) =>
          compactorUpgrades.includes(key)
        );
        const desiredHoppers = [...desiredUpgrades].filter((key) =>
          hopperUpgrades.includes(key)
        );

        if (desiredCompactors.length > 1) {
          await i.followUp({
            content:
              "Cannot equip multiple compactors on the same minion. Only one compactor per minion is allowed.",
            ephemeral: true,
          });
          return;
        }

        if (desiredHoppers.length > 1) {
          await i.followUp({
            content:
              "Cannot equip multiple hoppers on the same minion. Only one hopper per minion is allowed.",
            ephemeral: true,
          });
          return;
        }

        // Check inventory for items to add
        for (const itemKey of toAdd) {
          const playerHasAmount = modCharacter.inventory?.items?.[itemKey] || 0;
          if (playerHasAmount < 1) {
            const item = allItems[itemKey];
            await i.followUp({
              content: `You don't have any ${item?.emoji || ""} **${item?.name || itemKey}** to equip.`,
              ephemeral: true,
            });
            return;
          }
        }

        // Prepare inventory changes
        const itemsToAddToInventory = toRemove.map((key) => ({
          itemKey: key,
          amount: 1,
        }));
        const itemsToRemoveFromInventory = toAdd.map((key) => ({
          itemKey: key,
          amount: 1,
        }));

        // Update minion upgrades intelligently
        const newUpgrades = new Array(minionToUpdate.upgrades.length).fill(
          null
        );
        let slotIndex = 0;

        // Fill slots with desired upgrades
        for (const upgradeKey of desiredUpgrades) {
          if (slotIndex < newUpgrades.length) {
            newUpgrades[slotIndex] = upgradeKey;
            slotIndex++;
          }
        }

        minionToUpdate.upgrades = newUpgrades;

        // Handle storage overflow for removed storage upgrades
        const removedStorageUpgrades = toRemove.filter((key) => {
          const item = allItems[key];
          return item?.upgradeEffect?.extraStorage > 0;
        });

        if (removedStorageUpgrades.length > 0) {
          const { itemsMoved } = await handleStorageOverflow(
            modCharacter,
            minionToUpdate,
            allItems
          );
          if (itemsMoved && itemsMoved.length > 0) {
            itemsMoved.forEach((movedItem) => {
              const existing = itemsToAddToInventory.find(
                (it) => it.itemKey === movedItem.itemKey
              );
              if (existing) existing.amount += movedItem.amount;
              else itemsToAddToInventory.push(movedItem);
            });
          }
        }

        // Track hopper timestamps
        if (!minionToUpdate.upgradeTimestamps) {
          minionToUpdate.upgradeTimestamps = {};
        }

        // Add timestamps for new hoppers
        for (const hopperKey of toAdd.filter((key) =>
          hopperUpgrades.includes(key)
        )) {
          minionToUpdate.upgradeTimestamps[hopperKey] = Date.now();
        }

        // Remove timestamps for removed hoppers
        for (const hopperKey of toRemove.filter((key) =>
          hopperUpgrades.includes(key)
        )) {
          delete minionToUpdate.upgradeTimestamps[hopperKey];
        }

        // Combine inventory changes
        const allItemChanges = [
          ...itemsToAddToInventory,
          ...itemsToRemoveFromInventory.map((item) => ({
            itemKey: item.itemKey,
            amount: -item.amount,
          })),
        ];

        // Perform atomic update
        const islandJsonString = JSON.stringify(modCharacter.island);
        await updateInventoryAtomically(
          userId,
          0,
          allItemChanges,
          [],
          [],
          0,
          [],
          islandJsonString,
          null
        );

        // Check for Disblock XP achievements
        try {
          await checkAndNotifyDisblockXP(userId, i);
        } catch (xpError) {
          console.error(
            "[Minion Upgrade Management] Error updating Disblock XP:",
            xpError
          );
        }

        // Refresh UI with updated minion state
        const finalCharacterData = await getPlayerData(userId);
        const finalMinion = finalCharacterData?.island?.placedMinions?.find(
          (m) => m.id === minion.id
        );

        if (finalMinion) {
          initializeMinionUpgrades(finalMinion);
          const { embed: updatedMinionEmbed } =
            createMinionViewEmbedAndComponents(finalMinion, finalCharacterData);
          updatedMinionEmbed.setTitle(
            `${updatedMinionEmbed.data.title} - Manage Upgrades`
          );
          updatedMinionEmbed.setFooter({
            text: "Select which upgrades you want on this minion",
          });

          // Recreate the simple upgrade menu with updated state
          const currentInventory = finalCharacterData?.inventory?.items || {};
          const newAvailableUpgrades = Object.keys(currentInventory)
            .filter(
              (key) =>
                allItems[key]?.type === "MINION_UPGRADE" &&
                currentInventory[key] > 0
            )
            .map((key) => ({
              key,
              name: allItems[key].name,
              emoji: allItems[key].emoji || "",
              amount: currentInventory[key],
            }));

          const newCurrentlyEquipped = new Set(
            finalMinion.upgrades.filter((upgrade) => upgrade !== null)
          );
          const newAllPossibleUpgrades = new Map();

          // Add currently equipped upgrades
          for (const equippedKey of newCurrentlyEquipped) {
            if (equippedKey && allItems[equippedKey]) {
              newAllPossibleUpgrades.set(equippedKey, {
                key: equippedKey,
                name: allItems[equippedKey].name,
                emoji: allItems[equippedKey].emoji || "",
                isEquipped: true,
                available: currentInventory[equippedKey] || 0,
              });
            }
          }

          // Add available upgrades from inventory
          for (const upg of newAvailableUpgrades) {
            if (!newAllPossibleUpgrades.has(upg.key)) {
              newAllPossibleUpgrades.set(upg.key, {
                key: upg.key,
                name: upg.name,
                emoji: upg.emoji,
                isEquipped: false,
                available: upg.amount,
              });
            } else {
              newAllPossibleUpgrades.get(upg.key).available = upg.amount;
            }
          }

          const newUpgradeOptions = Array.from(
            newAllPossibleUpgrades.values()
          ).map((upg) => {
            const statusIcon = upg.isEquipped ? "✅" : "⬜";
            const availableText =
              upg.available > 0 ? ` (${upg.available} available)` : "";

            return {
              label: `${statusIcon} ${upg.name}${availableText}`,
              value: upg.key,
              emoji: upg.emoji || "❓",
              description: upg.isEquipped
                ? "Currently equipped - unselect to remove"
                : "Available to equip - select to add",
              default: upg.isEquipped,
            };
          });

          if (newUpgradeOptions.length === 0) {
            newUpgradeOptions.push({
              label: "No upgrades available",
              value: "none",
              description: "You don't have any minion upgrades to equip",
              emoji: "❌",
            });
          }

          const newUpgradeSelectMenu = new StringSelectMenuBuilder()
            .setCustomId(`minion_upgrade_simple_${finalMinion.id}`)
            .setPlaceholder("Select which upgrades you want on this minion")
            .addOptions(newUpgradeOptions)
            .setMinValues(0)
            .setMaxValues(
              Math.min(newUpgradeOptions.length, finalMinion.upgrades.length)
            );

          const newUpgradeRow = new ActionRowBuilder().addComponents(
            newUpgradeSelectMenu
          );
          const newBackButtonRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId(`minion_back_${finalMinion.id}`)
              .setLabel("← Back")
              .setStyle(ButtonStyle.Secondary)
          );

          const newRows = [newUpgradeRow, newBackButtonRow];

          try {
            await i.editReply({
              embeds: [updatedMinionEmbed],
              components: newRows,
            });
          } catch (editError) {
            console.error(
              "[Minion Upgrade] Failed to update minion view:",
              editError
            );
          }
        }
      }); // End of withMinionLock
    } catch (error) {
      console.error(
        "[Minion Manage Upgrades Multi-Select Collector] ERROR:",
        error
      );
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Upgrade Error")
        .setDescription("An error occurred. Please try again.");
      // Check if interaction i can be replied to
      if (!i.replied && !i.deferred) {
        await i.reply({ embeds: [errorEmbed], ephemeral: true });
      } else {
        await i.followUp({ embeds: [errorEmbed], ephemeral: true });
      }
    }
  });

  // Removed automatic component disabling on collector end
}

// Duplicate helper definitions removed (kept earlier versions)

function filterAndMapUpgrades(upgrades, allItems) {
  return upgrades.map((upgradeKey) => {
    const upgrade = allItems[upgradeKey];
    return {
      key: upgradeKey,
      name: upgrade?.name || "Unknown Upgrade",
      emoji: upgrade?.emoji || "❓",
    };
  });
}

function createPaginationComponents(currentPage, totalPages) {
  return [
    new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("prev_page")
        .setLabel("Previous")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === 0),
      new ButtonBuilder()
        .setCustomId("page_info")
        .setLabel(`${currentPage + 1}/${totalPages}`)
        .setStyle(ButtonStyle.Primary)
        .setDisabled(true),
      new ButtonBuilder()
        .setCustomId("next_page")
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === totalPages - 1)
    ),
  ];
}

// Helper function to get ordered minions list
function getOrderedMinions(character) {
  const stored = character.minionStorage || [];
  const placed = character.island?.placedMinions || [];

  // Order: placed minions first, then recently crafted, then recently removed, then older storage
  const placedMinions = placed.map((m) => ({ ...m, location: "placed" }));

  // Separate storage minions into recently crafted vs previously used
  const recentlyCrafted = [];
  const previouslyUsed = [];

  for (const minion of stored) {
    // Recently crafted minions are "clean" - no collection timestamp, no upgrades, empty storage
    const isRecentlyCrafted =
      !minion.lastCollectionTimestamp &&
      (!minion.upgrades ||
        minion.upgrades.length === 0 ||
        minion.upgrades.every((u) => !u)) &&
      (!minion.resourcesStored ||
        Object.keys(minion.resourcesStored).length === 0);

    if (isRecentlyCrafted) {
      recentlyCrafted.push({ ...minion, location: "storage" });
    } else {
      previouslyUsed.push({ ...minion, location: "storage" });
    }
  }

  // Recently removed should appear first among previously used (reverse order)
  previouslyUsed.reverse();

  return [...placedMinions, ...recentlyCrafted, ...previouslyUsed];
}

async function showManageMenu(interaction, character, currentPage = 0) {
  const allMinions = getOrderedMinions(character);

  if (allMinions.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.YELLOW)
      .setTitle("Manage Minions")
      .setDescription(
        "You have no minions to manage.\n\nCraft some minions using `/craft` to get started!"
      );

    const backButton = new ButtonBuilder()
      .setCustomId("back_to_minion_view")
      .setLabel("Back")
      .setStyle(ButtonStyle.Secondary);
    const backRow = new ActionRowBuilder().addComponents(backButton);

    await interaction.editReply({ embeds: [embed], components: [backRow] });

    // Set up collector for back button
    const message = await interaction.fetchReply();
    const filter = (i) =>
      i.user.id === interaction.user.id && i.customId === "back_to_minion_view";
    const collector = message.createMessageComponentCollector({ filter });

    collector.on("collect", async (i) => {
      if (!i.replied && !i.deferred) {
        await i.deferUpdate();
      }

      const createRobustReply = async (content) => {
        try {
          if (i.deferred || i.replied) {
            return await i.editReply(content);
          } else {
            return await i.update(content);
          }
        } catch {
          return await i.followUp({ ...content, ephemeral: true });
        }
      };

      const mockInteraction = {
        ...i,
        options: { getString: () => null },
        reply: createRobustReply,
        editReply: createRobustReply,
        fetchReply: i.fetchReply ? i.fetchReply.bind(i) : async () => i.message,
      };
      await handleView(mockInteraction, character);
      collector.stop();
    });

    return;
  }

  const ITEMS_PER_PAGE = 25;
  const totalPages = Math.ceil(allMinions.length / ITEMS_PER_PAGE);
  const startIdx = currentPage * ITEMS_PER_PAGE;
  const endIdx = startIdx + ITEMS_PER_PAGE;
  const pageMinions = allMinions.slice(startIdx, endIdx);

  const options = pageMinions.map((m) => {
    const info = getMinionDisplayInfo(m);
    return {
      label: `[${m.location.toUpperCase()}] ${info.name} (ID: ${m.id.substring(0, 8)})`,
      value: `${m.location}_${m.id}`,
      emoji: info.emoji,
    };
  });

  const select = new StringSelectMenuBuilder()
    .setCustomId("select_minions_toggle")
    .setPlaceholder("Select minions to toggle")
    .addOptions(options)
    .setMinValues(1)
    .setMaxValues(options.length);

  const selectRow = new ActionRowBuilder().addComponents(select);

  const components = [selectRow];

  // Add pagination buttons if needed
  if (totalPages > 1) {
    const paginationRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("manage_minions_prev")
        .setLabel("Previous")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === 0),
      new ButtonBuilder()
        .setCustomId("manage_minions_next")
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(currentPage === totalPages - 1)
    );
    components.push(paginationRow);
  }

  const backButton = new ButtonBuilder()
    .setCustomId("back_to_minion_view")
    .setLabel("Back")
    .setStyle(ButtonStyle.Secondary);

  const backRow = new ActionRowBuilder().addComponents(backButton);
  components.push(backRow);

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.SKY_BLUE)
    .setTitle("Manage Minions")
    .setDescription(
      "Select minions to place (from storage) or remove (from island). The selection will automatically process the changes."
    );

  if (totalPages > 1) {
    embed.setFooter({
      text: `Page ${currentPage + 1} of ${totalPages} • Showing ${pageMinions.length} of ${allMinions.length} minions`,
    });
  }

  await interaction.editReply({ embeds: [embed], components });

  const message = await interaction.fetchReply();

  const filter = (i) =>
    i.user.id === interaction.user.id &&
    (i.customId === "select_minions_toggle" ||
      i.customId === "back_to_minion_view" ||
      i.customId === "manage_minions_prev" ||
      i.customId === "manage_minions_next");

  const collector = message.createMessageComponentCollector({ filter });

  let currentPageState = currentPage;

  collector.on("collect", async (i) => {
    // Check if interaction is already acknowledged before deferring
    if (!i.replied && !i.deferred) {
      await i.deferUpdate();
    }

    if (i.customId === "manage_minions_prev") {
      const newPage = Math.max(0, currentPageState - 1);
      if (newPage !== currentPageState) {
        currentPageState = newPage;
        await showManageMenu(i, character, newPage);
      }
      return;
    }

    if (i.customId === "manage_minions_next") {
      const allMinions = getOrderedMinions(character);
      const totalPages = Math.ceil(allMinions.length / 25);
      const newPage = Math.min(totalPages - 1, currentPageState + 1);
      if (newPage !== currentPageState) {
        currentPageState = newPage;
        await showManageMenu(i, character, newPage);
      }
      return;
    }

    if (i.customId === "back_to_minion_view") {
      // Create robust mock
      const createRobustReply = async (content) => {
        try {
          if (i.deferred || i.replied) {
            return await i.editReply(content);
          } else {
            return await i.update(content);
          }
        } catch {
          return await i.followUp({ ...content, ephemeral: true });
        }
      };

      const mockInteraction = {
        ...i,
        options: { getString: () => null },
        reply: createRobustReply,
        editReply: createRobustReply,
        fetchReply: i.fetchReply ? i.fetchReply.bind(i) : async () => i.message,
      };
      await handleView(mockInteraction, character);
      collector.stop();
      return;
    }

    // Process selection using atomic operations to prevent duplication
    const selected = i.values;

    const { withMinionLock } = require("../utils/minionMutex");
    const {
      atomicMinionPlace,
      atomicMinionRemove,
    } = require("../utils/atomicMinionOperations");

    await withMinionLock(interaction.user.id, "manage_batch", async () => {
      const toPlaceIds = [];
      const toRemoveIds = [];
      selected.forEach((val) => {
        const [loc, id] = val.split("_");
        if (loc === "storage") {
          toPlaceIds.push(id);
        } else if (loc === "placed") {
          toRemoveIds.push(id);
        }
      });

      // Check slot limits before processing
      const currentCharacter = await getPlayerData(interaction.user.id);
      if (!currentCharacter) {
        await i.followUp({
          content: "Error: Could not load character data.",
          ephemeral: true,
        });
        return;
      }

      const currentPlacedCount =
        currentCharacter.island?.placedMinions?.length || 0;
      const netChange = toPlaceIds.length - toRemoveIds.length;
      const newPlacedCount = currentPlacedCount + netChange;
      const maxSlots = calculateMaxMinionSlots(currentCharacter);

      if (newPlacedCount > maxSlots) {
        await i.followUp({
          content: `Cannot process: Would exceed slot limit (${newPlacedCount} > ${maxSlots}).`,
          ephemeral: true,
        });
        return;
      }

      // Track items collected for display
      const allItemsCollected = [];
      let totalCoinsCollected = 0;

      // Process removals first using atomic operations
      for (const id of toRemoveIds) {
        const result = await atomicMinionRemove(interaction.user.id, id);
        if (result.success) {
          allItemsCollected.push(...(result.itemsReturned || []));
          totalCoinsCollected += result.coinsReturned || 0;
        } else {
          console.warn(
            `[Manage Menu] Failed to remove minion ${id}:`,
            result.error
          );
        }
      }

      // Process placements using atomic operations
      for (const id of toPlaceIds) {
        const result = await atomicMinionPlace(interaction.user.id, id);
        if (!result.success) {
          console.warn(
            `[Manage Menu] Failed to place minion ${id}:`,
            result.error
          );
        }
      }

      // Get final character data for the response
      const freshCharacter = await getPlayerData(interaction.user.id);

      // Refresh view with updated character data
      const createRobustReply = async (content) => {
        try {
          if (i.deferred || i.replied) {
            return await i.editReply(content);
          } else {
            return await i.update(content);
          }
        } catch {
          return await i.followUp({ ...content, ephemeral: true });
        }
      };

      const mockInteraction = {
        ...i,
        options: { getString: () => null },
        reply: createRobustReply,
        editReply: createRobustReply,
        fetchReply: i.fetchReply ? i.fetchReply.bind(i) : async () => i.message,
      };
      await handleView(mockInteraction, freshCharacter);

      // Display summary if anything was collected
      if (allItemsCollected.length > 0 || totalCoinsCollected > 0) {
        const allItemsView = configManager.getAllItems();
        const collectedSummary = {};

        allItemsCollected.forEach(({ itemKey, amount }) => {
          if (!collectedSummary[itemKey]) collectedSummary[itemKey] = 0;
          collectedSummary[itemKey] += amount;
        });
        if (totalCoinsCollected > 0) {
          collectedSummary["COINS"] = totalCoinsCollected;
        }

        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.LIGHT_GREEN)
          .setTitle("Auto-Collected Items");

        let list = "";
        for (const [key, amt] of Object.entries(collectedSummary)) {
          const item = allItemsView[key] || {
            name: key.replace(/_/g, " ").toUpperCase(),
            emoji: "",
          };
          const total =
            key === "COINS"
              ? freshCharacter.coins || 0
              : freshCharacter.inventory?.items?.[key] || 0;
          list += `- ${item.emoji} **${item.name}**: ${formatNumber(amt)} (${formatNumber(total)})\n`;
        }

        embed.setDescription(`${list}`);
        await i.followUp({ embeds: [embed] });
      }

      collector.stop();
    }); // End of withMinionLock
  });
}
