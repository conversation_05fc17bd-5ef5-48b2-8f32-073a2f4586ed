// Adds XP formatting utilities
function formatXP(value) {
  if (typeof value !== "number") {
    return value;
  }

  // Round to avoid floating-point precision issues
  const roundedValue = Math.round(value * 100) / 100;

  if (roundedValue >= 1_000_000_000) {
    return `${(roundedValue / 1_000_000_000).toFixed(1).replace(/\.0$/, "")}B`;
  }
  if (roundedValue >= 1_000_000) {
    return `${(roundedValue / 1_000_000).toFixed(1).replace(/\.0$/, "")}M`;
  }
  if (roundedValue >= 1_000) {
    return `${(roundedValue / 1_000).toFixed(1).replace(/\.0$/, "")}k`;
  }

  // For smaller values, show as integer to avoid decimal display issues
  return String(Math.round(roundedValue));
}

// Formats a price to show one decimal place, removing .0 for whole numbers
function formatPrice(value) {
  if (typeof value !== "number" || isNaN(value)) {
    return "0";
  }

  // round to 1 decimal place to avoid floating point precision issues
  const rounded = Math.round(value * 10) / 10;

  // if it's a whole number, show without decimal
  if (rounded % 1 === 0) {
    return rounded.toLocaleString();
  }

  // otherwise show 1 decimal place
  return rounded.toLocaleString(undefined, {
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  });
}

// Parses price input allowing decimal values with one decimal place
function parsePrice(priceStr) {
  const cleanedStr = String(priceStr).replace(/,/g, "").trim();
  const num = parseFloat(cleanedStr);

  if (isNaN(num) || num <= 0) {
    return NaN;
  }

  // round to 1 decimal place to avoid floating point precision issues
  return Math.round(num * 10) / 10;
}

// Parses shorthand numerical strings like "10k", "2m" or "all" (case-insensitive)
// into a positive integer. Returns NaN for invalid inputs.
function parseShorthandAmount(amountStr, balance = Infinity) {
  const cleanedStr = String(amountStr).replace(/,/g, "").trim().toLowerCase();
  if (cleanedStr === "all") return balance;

  let multiplier = 1;
  let numStr = cleanedStr;

  if (cleanedStr.endsWith("k")) {
    multiplier = 1_000;
    numStr = cleanedStr.slice(0, -1);
  } else if (cleanedStr.endsWith("m")) {
    multiplier = 1_000_000;
    numStr = cleanedStr.slice(0, -1);
  }

  const num = parseFloat(numStr);
  return !isNaN(num) && num > 0 ? Math.floor(num * multiplier) : NaN;
}

module.exports = {
  formatXP,
  parseShorthandAmount,
  formatPrice,
  parsePrice,
};
