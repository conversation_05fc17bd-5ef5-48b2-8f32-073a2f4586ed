// Garden Visitor Milestones System

const { getPlayerData, savePlayerData } = require("./playerDataManager");
const { EMBED_COLORS: _EMBED_COLORS } = require("../gameConfig");
const { addSkillExp } = require("./skillExpManager");

// Milestone requirements and rewards (based on Hypixel)
const OFFERS_ACCEPTED_MILESTONES = [
  { offers: 1, totalOffers: 1, farmingXp: 500, garden_xp: 30 },
  { offers: 4, totalOffers: 5, farmingXp: 1000, garden_xp: 60 },
  { offers: 5, totalOffers: 10, farmingXp: 2000, garden_xp: 90 },
  { offers: 10, totalOffers: 20, farmingXp: 3000, garden_xp: 120 },
  { offers: 10, totalOffers: 30, farmingXp: 5000, garden_xp: 150 },
  { offers: 20, totalOffers: 50, farmingXp: 7500, garden_xp: 180 },
  { offers: 25, totalOffers: 75, farmingXp: 15000, garden_xp: 210 },
  { offers: 75, totalOffers: 150, farmingXp: 20000, garden_xp: 240 },
  { offers: 100, totalOffers: 250, farmingXp: 25000, garden_xp: 270 },
  { offers: 150, totalOffers: 400, farmingXp: 30000, garden_xp: 300 },
  { offers: 250, totalOffers: 650, farmingXp: 30000, garden_xp: 330 },
  { offers: 350, totalOffers: 1000, farmingXp: 30000, garden_xp: 360 },
  { offers: 500, totalOffers: 1500, farmingXp: 30000, garden_xp: 390 },
  { offers: 500, totalOffers: 2000, farmingXp: 30000, garden_xp: 420 },
  { offers: 500, totalOffers: 2500, farmingXp: 30000, garden_xp: 450 },
];

const UNIQUE_VISITORS_MILESTONES = [
  { visitors: 1, totalVisitors: 1, farmingXp: 500, garden_xp: 30 },
  { visitors: 4, totalVisitors: 5, farmingXp: 1000, garden_xp: 60 },
  { visitors: 5, totalVisitors: 10, farmingXp: 2000, garden_xp: 90 },
  { visitors: 10, totalVisitors: 20, farmingXp: 3000, garden_xp: 120 },
  { visitors: 10, totalVisitors: 30, farmingXp: 4000, garden_xp: 150 },
  { visitors: 10, totalVisitors: 40, farmingXp: 5000, garden_xp: 180 },
  { visitors: 10, totalVisitors: 50, farmingXp: 6000, garden_xp: 210 },
  { visitors: 10, totalVisitors: 60, farmingXp: 7000, garden_xp: 240 },
  { visitors: 10, totalVisitors: 70, farmingXp: 8000, garden_xp: 270 },
  { visitors: 10, totalVisitors: 80, farmingXp: 9000, garden_xp: 300 },
];

/**
 * Check and award milestone rewards for offers accepted
 * @param {string} userId - Player user ID
 * @param {number} newOffersAccepted - New total offers accepted
 * @param {number} previousOffersAccepted - Previous total offers accepted
 * @param {Object} interaction - Discord interaction for notifications
 * @returns {Array} Array of milestone rewards earned
 */
async function checkOffersAcceptedMilestones(
  userId,
  newOffersAccepted,
  previousOffersAccepted,
  interaction = null
) {
  try {
    const character = await getPlayerData(userId);
    if (!character) return [];

    const milestonesEarned = [];
    let needsSave = false;

    for (const milestone of OFFERS_ACCEPTED_MILESTONES) {
      // Check if this milestone was just completed
      if (
        newOffersAccepted >= milestone.totalOffers &&
        previousOffersAccepted < milestone.totalOffers
      ) {
        milestonesEarned.push({
          type: "offers_accepted",
          milestone: milestone.totalOffers,
          farmingXp: milestone.farmingXp,
          garden_xp: milestone.garden_xp,
        });

        // Award the Garden XP directly to character object
        character.garden_xp = (character.garden_xp || 0) + milestone.garden_xp;
        needsSave = true;

        // Award farming XP with automatic notifications using centralized system
        if (milestone.farmingXp > 0 && interaction) {
          await addSkillExp(
            character,
            "farming",
            milestone.farmingXp,
            interaction
          );
        }

        console.log(
          `[Visitor Milestones] Player ${userId} earned offers accepted milestone ${milestone.totalOffers}`
        );
      }
    }

    // Save once at the end if needed
    if (needsSave) {
      await savePlayerData(userId, character, ["garden_xp"]);
    }

    return milestonesEarned;
  } catch (error) {
    console.error(
      "[Visitor Milestones] Error checking offers accepted milestones:",
      error
    );
    return [];
  }
}

/**
 * Check and award milestone rewards for unique visitors served
 * @param {string} userId - Player user ID
 * @param {Array} uniqueVisitorsServed - Array of unique visitor keys served
 * @param {number} previousUniqueCount - Previous count of unique visitors
 * @param {Object} interaction - Discord interaction for notifications
 * @returns {Array} Array of milestone rewards earned
 */
async function checkUniqueVisitorsMilestones(
  userId,
  uniqueVisitorsServed,
  previousUniqueCount,
  interaction = null
) {
  try {
    const character = await getPlayerData(userId);
    if (!character) return [];

    const currentUniqueCount = uniqueVisitorsServed.length;
    const milestonesEarned = [];
    let needsSave = false;

    for (const milestone of UNIQUE_VISITORS_MILESTONES) {
      // Check if this milestone was just completed
      if (
        currentUniqueCount >= milestone.totalVisitors &&
        previousUniqueCount < milestone.totalVisitors
      ) {
        milestonesEarned.push({
          type: "unique_visitors",
          milestone: milestone.totalVisitors,
          farmingXp: milestone.farmingXp,
          garden_xp: milestone.garden_xp,
        });

        // Award the Garden XP directly to character object
        character.garden_xp = (character.garden_xp || 0) + milestone.garden_xp;
        needsSave = true;

        // Award farming XP with automatic notifications using centralized system
        if (milestone.farmingXp > 0 && interaction) {
          await addSkillExp(
            character,
            "farming",
            milestone.farmingXp,
            interaction
          );
        }

        console.log(
          `[Visitor Milestones] Player ${userId} earned unique visitors milestone ${milestone.totalVisitors}`
        );
      }
    }

    // Save once at the end if needed
    if (needsSave) {
      await savePlayerData(userId, character, ["garden_xp"]);
    }

    return milestonesEarned;
  } catch (error) {
    console.error(
      "[Visitor Milestones] Error checking unique visitors milestones:",
      error
    );
    return [];
  }
}

/**
 * Get milestone progress for display
 * @param {Object} character - Player character data
 * @returns {Object} Milestone progress information
 */
function getMilestoneProgress(character) {
  const offersAccepted = character.visitor_offers_accepted || 0;

  let uniqueServed = 0;
  if (character.visitor_unique_served) {
    if (Array.isArray(character.visitor_unique_served)) {
      // Already parsed by playerDataManager
      uniqueServed = character.visitor_unique_served.length;
    } else {
      // Still a JSON string, need to parse
      try {
        const parsedUnique = JSON.parse(character.visitor_unique_served);
        uniqueServed = Array.isArray(parsedUnique) ? parsedUnique.length : 0;
      } catch (error) {
        console.error(
          "[Visitor Milestones] Error parsing unique served in getMilestoneProgress:",
          error
        );
        uniqueServed = 0;
      }
    }
  }

  // Find current and next milestones
  let currentOffersMilestone = 0;
  let nextOffersMilestone = null;

  for (const milestone of OFFERS_ACCEPTED_MILESTONES) {
    if (offersAccepted >= milestone.totalOffers) {
      currentOffersMilestone = milestone.totalOffers;
    } else if (!nextOffersMilestone) {
      nextOffersMilestone = milestone;
      break;
    }
  }

  let currentUniquesMilestone = 0;
  let nextUniquesMilestone = null;

  for (const milestone of UNIQUE_VISITORS_MILESTONES) {
    if (uniqueServed >= milestone.totalVisitors) {
      currentUniquesMilestone = milestone.totalVisitors;
    } else if (!nextUniquesMilestone) {
      nextUniquesMilestone = milestone;
      break;
    }
  }

  return {
    offersAccepted: {
      current: offersAccepted,
      currentMilestone: currentOffersMilestone,
      nextMilestone: nextOffersMilestone,
    },
    uniqueVisitors: {
      current: uniqueServed,
      currentMilestone: currentUniquesMilestone,
      nextMilestone: nextUniquesMilestone,
    },
  };
}

module.exports = {
  OFFERS_ACCEPTED_MILESTONES,
  UNIQUE_VISITORS_MILESTONES,
  checkOffersAcceptedMilestones,
  checkUniqueVisitorsMilestones,
  getMilestoneProgress,
};
