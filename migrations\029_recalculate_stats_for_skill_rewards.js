const { dbAll } = require("../utils/dbUtils");
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager");

module.exports = {
  version: 29,
  description:
    "Recalculate all player stats to include new skill-based stat rewards (health, treasure chance, etc.)",
  async up() {
    console.log(
      "[Migration 29] Starting stat recalculation for all players...",
    );
    const players = await dbAll("SELECT discord_id FROM players", []);
    let updated = 0;
    for (const player of players) {
      try {
        const character = await getPlayerData(player.discord_id);
        if (!character) {
          console.warn(
            `[Migration 29] Could not load player data for ${player.discord_id}`,
          );
          continue;
        }
        await recalculateAndSaveStats(player.discord_id, character);
        updated++;
        if (updated % 50 === 0) {
          console.log(
            `[Migration 29] Recalculated stats for ${updated} players so far...`,
          );
        }
      } catch (err) {
        console.error(
          `[Migration 29] Error updating player ${player.discord_id}:`,
          err,
        );
      }
    }
    console.log(
      `[Migration 29] Stat recalculation complete. Updated ${updated} players.`,
    );
  },
  async down() {
    // No rollback needed for stat recalculation
    console.log("[Migration 29] No rollback for stat recalculation.");
  },
};
