/**
 * Migration utility to upgrade existing stop buttons to unified format
 * Run this after deploying the unified stop button system
 */

const { getActiveActions } = require("./actionPersistence");
const {
  ensureUnifiedStopButton,
  migrateToUnifiedStopButton,
} = require("./unifiedStopButtons");

/**
 * Migrates all active actions to use unified stop buttons
 * @param {import('discord.js').Client} client - Discord client
 * @returns {Promise<{migrated: number, failed: number, skipped: number}>} Migration results
 */
async function migrateActiveStopButtons(client) {
  console.log(
    "[StopButtonMigration] Starting migration of active actions to unified stop buttons..."
  );

  const results = {
    migrated: 0,
    failed: 0,
    skipped: 0,
  };

  try {
    // Get all active actions from database
    const activeActions = await getActiveActions();
    console.log(
      `[StopButtonMigration] Found ${activeActions.length} active actions to check`
    );

    for (const action of activeActions) {
      try {
        const {
          id: actionId,
          user_id: userId,
          action_type: actionType,
          channel_id: channelId,
          message_id: messageId,
        } = action;

        if (!channelId || !messageId) {
          console.log(
            `[StopButtonMigration] Skipping action ${actionId} - missing channel or message ID`
          );
          results.skipped++;
          continue;
        }

        // Try to fetch the channel and message
        const channel = await client.channels
          .fetch(channelId)
          .catch(() => null);
        if (!channel) {
          console.log(
            `[StopButtonMigration] Skipping action ${actionId} - channel ${channelId} not found`
          );
          results.skipped++;
          continue;
        }

        const message = await channel.messages
          .fetch(messageId)
          .catch(() => null);
        if (!message) {
          console.log(
            `[StopButtonMigration] Skipping action ${actionId} - message ${messageId} not found`
          );
          results.skipped++;
          continue;
        }

        // Check if message already has unified stop button
        const hasUnifiedButton = message.components?.some((row) =>
          row.components.some(
            (component) =>
              component.customId &&
              component.customId.startsWith(`unified_stop:${actionId}:`)
          )
        );

        if (hasUnifiedButton) {
          console.log(
            `[StopButtonMigration] Skipping action ${actionId} - already has unified stop button`
          );
          results.skipped++;
          continue;
        }

        // Check if message has old-format stop buttons that need migration
        const hasOldButton = message.components?.some((row) =>
          row.components.some(
            (component) =>
              component.customId &&
              (component.customId.startsWith(`stop_action:${actionId}:`) ||
                component.customId.startsWith(
                  `stop_resumed_action:${actionId}:`
                ))
          )
        );

        if (hasOldButton) {
          // Migrate old button to unified format
          const migrated = await migrateToUnifiedStopButton(
            message,
            actionId,
            userId,
            `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
          );
          if (migrated) {
            console.log(
              `[StopButtonMigration] Successfully migrated action ${actionId} (${actionType})`
            );
            results.migrated++;
          } else {
            console.warn(
              `[StopButtonMigration] Failed to migrate action ${actionId} (${actionType})`
            );
            results.failed++;
          }
        } else {
          // No stop button exists, add unified one
          const added = await ensureUnifiedStopButton(
            message,
            actionId,
            userId,
            `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
          );
          if (added) {
            console.log(
              `[StopButtonMigration] Added unified stop button to action ${actionId} (${actionType})`
            );
            results.migrated++;
          } else {
            console.warn(
              `[StopButtonMigration] Failed to add unified stop button to action ${actionId} (${actionType})`
            );
            results.failed++;
          }
        }

        // Small delay to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (actionError) {
        console.error(
          `[StopButtonMigration] Error processing action ${action.id}:`,
          actionError
        );
        results.failed++;
      }
    }

    console.log(
      `[StopButtonMigration] Migration complete: ${results.migrated} migrated, ${results.failed} failed, ${results.skipped} skipped`
    );
    return results;
  } catch (error) {
    console.error("[StopButtonMigration] Error during migration:", error);
    throw error;
  }
}

/**
 * Migrates a specific action's stop button to unified format
 * @param {import('discord.js').Client} client - Discord client
 * @param {number} actionId - Action ID to migrate
 * @returns {Promise<boolean>} True if migration succeeded
 */
async function migrateSpecificAction(client, actionId) {
  try {
    const { getActionById } = require("./actionPersistence");
    const action = await getActionById(actionId);

    if (!action) {
      console.log(`[StopButtonMigration] Action ${actionId} not found`);
      return false;
    }

    const {
      user_id: userId,
      action_type: actionType,
      channel_id: channelId,
      message_id: messageId,
    } = action;

    if (!channelId || !messageId) {
      console.log(
        `[StopButtonMigration] Action ${actionId} missing channel or message ID`
      );
      return false;
    }

    const channel = await client.channels.fetch(channelId).catch(() => null);
    if (!channel) {
      console.log(
        `[StopButtonMigration] Channel ${channelId} not found for action ${actionId}`
      );
      return false;
    }

    const message = await channel.messages.fetch(messageId).catch(() => null);
    if (!message) {
      console.log(
        `[StopButtonMigration] Message ${messageId} not found for action ${actionId}`
      );
      return false;
    }

    // Migrate to unified stop button
    const migrated = await migrateToUnifiedStopButton(
      message,
      actionId,
      userId,
      `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
    );

    if (migrated) {
      console.log(
        `[StopButtonMigration] Successfully migrated action ${actionId}`
      );
      return true;
    } else {
      // Try adding unified button if migration didn't work
      const added = await ensureUnifiedStopButton(
        message,
        actionId,
        userId,
        `Stop ${actionType.charAt(0).toUpperCase() + actionType.slice(1)}`
      );
      if (added) {
        console.log(
          `[StopButtonMigration] Added unified stop button to action ${actionId}`
        );
        return true;
      }
    }

    console.warn(`[StopButtonMigration] Failed to migrate action ${actionId}`);
    return false;
  } catch (error) {
    console.error(
      `[StopButtonMigration] Error migrating action ${actionId}:`,
      error
    );
    return false;
  }
}

module.exports = {
  migrateActiveStopButtons,
  migrateSpecificAction,
};
