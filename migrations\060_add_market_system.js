const { dbRun, dbAll } = require("../utils/dbUtils");

/**
 * Migration 060: Add advanced market system with order book
 * Creates tables for sell orders, buy orders, and order matching
 */
async function up() {
  console.log("[Migration 060] Adding advanced market system tables...");

  try {
    // sell orders table (items listed for sale)
    await dbRun(`
      CREATE TABLE IF NOT EXISTS market_sell_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        seller_id TEXT NOT NULL,
        item_key TEXT NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        price_per_unit INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (seller_id) REFERENCES players(discord_id),
        CHECK (quantity > 0),
        CHECK (price_per_unit > 0)
      )
    `);

    // buy orders table (players wanting to buy items)
    await dbRun(`
      CREATE TABLE IF NOT EXISTS market_buy_orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        buyer_id TEXT NOT NULL,
        item_key TEXT NOT NULL,
        quantity INTEGER NOT NULL DEFAULT 0,
        price_per_unit INTEGER NOT NULL DEFAULT 0,
        total_coins_locked INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL DEFAULT (datetime('now')),
        FOREIGN KEY (buyer_id) REFERENCES players(discord_id),
        CHECK (quantity > 0),
        CHECK (price_per_unit > 0),
        CHECK (total_coins_locked > 0)
      )
    `);

    // create indexes for efficient queries
    const indexes = [
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_item_key ON market_sell_orders(item_key)",
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_seller ON market_sell_orders(seller_id)",
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_price ON market_sell_orders(price_per_unit)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_item_key ON market_buy_orders(item_key)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_buyer ON market_buy_orders(buyer_id)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_price ON market_buy_orders(price_per_unit)",
      // composite indexes for order matching
      "CREATE INDEX IF NOT EXISTS idx_sell_orders_item_price ON market_sell_orders(item_key, price_per_unit)",
      "CREATE INDEX IF NOT EXISTS idx_buy_orders_item_price ON market_buy_orders(item_key, price_per_unit DESC)",
    ];

    for (const indexSql of indexes) {
      await dbRun(indexSql);
    }

    // migrate existing market_listings to sell orders if they exist
    try {
      const existingListings = await dbAll("SELECT * FROM market_listings");
      if (existingListings && existingListings.length > 0) {
        console.log(
          `[Migration 060] Migrating ${existingListings.length} existing listings...`,
        );

        for (const listing of existingListings) {
          await dbRun(
            `
            INSERT INTO market_sell_orders (seller_id, item_key, quantity, price_per_unit, created_at)
            VALUES (?, ?, ?, ?, ?)
          `,
            [
              listing.seller_id,
              listing.item_key,
              listing.quantity,
              listing.price_per_unit,
              listing.created_at,
            ],
          );
        }

        // drop old table
        await dbRun("DROP TABLE market_listings");
        console.log("[Migration 060] Successfully migrated existing listings");
      }
    } catch {
      // table doesn't exist yet, that's fine
      console.log(
        "[Migration 060] No existing market_listings table to migrate",
      );
    }

    console.log(
      "[Migration 060] Advanced market system tables created successfully",
    );
  } catch (error) {
    console.error("[Migration 060] Error creating market tables:", error);
    throw error;
  }
}

module.exports = { up };
