{"name": "Fishing Minion", "emoji": "<:minion_fishing:1370514354772119723>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "fishing", "resourceItemKey": "RAW_FISH", "recipes": [{"ingredients": [{"itemKey": "RAW_FISH", "amount": 64}]}], "craftingRequirements": {"collections": {"RAW_FISH": 2}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 78, "maxStorage": 640}, {"tier": 2, "generationIntervalSeconds": 75, "maxStorage": 640, "upgradeCost": [{"itemKey": "RAW_FISH", "amount": 128}]}, {"tier": 3, "generationIntervalSeconds": 72, "maxStorage": 640, "upgradeCost": [{"itemKey": "RAW_FISH", "amount": 256}]}, {"tier": 4, "generationIntervalSeconds": 72, "maxStorage": 704, "upgradeCost": [{"itemKey": "RAW_FISH", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 68, "maxStorage": 704, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 68, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 24}]}, {"tier": 7, "generationIntervalSeconds": 62, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 64}]}, {"tier": 8, "generationIntervalSeconds": 62, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 128}]}, {"tier": 9, "generationIntervalSeconds": 53, "maxStorage": 832, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 256}]}, {"tier": 10, "generationIntervalSeconds": 53, "maxStorage": 896, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 512}]}, {"tier": 11, "generationIntervalSeconds": 35, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_RAW_FISH", "amount": 1024}]}], "drops": [{"itemKey": "RAW_FISH", "chance": 0.5}, {"itemKey": "RAW_SALMON", "chance": 0.25}, {"itemKey": "PUFFERFISH", "chance": 0.12}, {"itemKey": "CLOWNFISH", "chance": 0.04}, {"itemKey": "PRISMARINE_CRYSTALS", "chance": 0.03}, {"itemKey": "PRISMARINE_SHARD", "chance": 0.03}, {"itemKey": "SPONGE", "chance": 0.03}]}