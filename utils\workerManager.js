// Load Balancer and Worker Management for Main Bot
// Handles delegation of action commands to worker bots

// express not needed here; routes are attached in bot.js

class WorkerManager {
  constructor() {
    this.workers = new Map(); // workerId -> worker info
    this.userAssignments = new Map(); // userId -> workerId
    this.activeActions = new Map(); // userId -> action info
    this.roundRobinIndex = 0;
  }

  // Register a new worker bot
  registerWorker(workerId, workerInfo) {
    console.log(`[WorkerManager] Registering worker: ${workerId}`);

    // Create a shared axios client per worker with keep-alive
    const http = require("http");
    const https = require("https");
    const axios = require("axios");
    const axiosClient = axios.create({
      baseURL: `http://127.0.0.1:${workerInfo.port}`,
      timeout: 10000,
      httpAgent: new http.Agent({
        keepAlive: true,
        maxSockets: 32,
        maxFreeSockets: 16,
        timeout: 60000,
        keepAliveMsecs: 10000,
      }),
      httpsAgent: new https.Agent({
        keepAlive: true,
        maxSockets: 32,
        maxFreeSockets: 16,
      }),
    });

    this.workers.set(workerId, {
      workerId: workerId, // Add the workerId as a property
      ...workerInfo,
      status: "ready",
      lastHeartbeat: Date.now(),
      activeUsers: new Set(),
      totalActionsHandled: 0,
      url: `http://127.0.0.1:${workerInfo.port}`,
      apiUrl: `http://127.0.0.1:${workerInfo.port}`, // ensure apiUrl is set for main bot resumption
      axiosClient,
    });

    console.log(
      `[WorkerManager] Worker ${workerId} registered. Total workers: ${this.workers.size}`
    );
  }

  // Remove a worker bot
  unregisterWorker(workerId) {
    console.log(`[WorkerManager] Unregistering worker: ${workerId}`);

    const worker = this.workers.get(workerId);
    if (worker) {
      // Reassign any active users from this worker
      for (const [userId, assignedWorkerId] of this.userAssignments.entries()) {
        if (assignedWorkerId === workerId) {
          this.userAssignments.delete(userId);
          this.activeActions.delete(userId);
          console.log(
            `[WorkerManager] Reassigned user ${userId} due to worker ${workerId} shutdown`
          );
        }
      }

      this.workers.delete(workerId);
    }

    console.log(
      `[WorkerManager] Worker ${workerId} unregistered. Total workers: ${this.workers.size}`
    );
  }

  // Update worker heartbeat
  updateHeartbeat(workerId, data) {
    const worker = this.workers.get(workerId);
    if (worker) {
      worker.lastHeartbeat = Date.now();
      worker.memoryUsage = data.memoryUsage;
      worker.uptime = data.uptime;
    }
  }

  // Check if any workers are unhealthy (no heartbeat for 60 seconds)
  cleanupUnhealthyWorkers() {
    const now = Date.now();
    const timeoutMs = 120000; // 120 seconds (allow 2 missed heartbeats)

    for (const [workerId, worker] of this.workers.entries()) {
      if (now - worker.lastHeartbeat > timeoutMs) {
        console.warn(
          `[WorkerManager] Worker ${workerId} appears unhealthy, removing`
        );
        this.unregisterWorker(workerId);
      }
    }
  }

  // Get available worker for a new action
  getAvailableWorker(commandName) {
    this.cleanupUnhealthyWorkers();

    // map brew to alchemy for worker capabilities
    const mappedCommand = commandName === "brew" ? "alchemy" : commandName;
    // count active actions assigned to each worker
    const availableWorkers = Array.from(this.workers.values()).filter(
      (worker) => {
        const activeActionCount = Array.from(
          this.activeActions.values()
        ).filter((a) => a.workerId === worker.workerId).length;
        return (
          worker.status === "ready" &&
          worker.capabilities.includes(mappedCommand) &&
          activeActionCount < 10 // Max 10 concurrent actions per worker
        );
      }
    );

    if (availableWorkers.length === 0) {
      return null;
    }

    // random selection from eligible workers
    const selectedWorker =
      availableWorkers[Math.floor(Math.random() * availableWorkers.length)];
    return selectedWorker;
  }

  // Assign user to worker
  async assignUserToWorker(userId, workerId, commandName) {
    const worker = this.workers.get(workerId);
    if (!worker) {
      throw new Error(`Worker ${workerId} not found`);
    }

    // If user is already assigned, check if their activity is still active
    if (this.userAssignments.has(userId)) {
      const { isUserBusy } = require("./activityManager.js");
      const busy = await isUserBusy(userId);
      if (!busy) {
        this.releaseUser(userId);
        // continue to assignment
      } else {
        throw new Error(`User ${userId} is already assigned to a worker`);
      }
    }

    // Assign user
    this.userAssignments.set(userId, workerId);
    worker.activeUsers.add(userId);

    this.activeActions.set(userId, {
      workerId,
      commandName,
      startTime: Date.now(),
    });

    console.log(
      `[WorkerManager] Assigned user ${userId} to worker ${workerId} for ${commandName}`
    );
  }

  // Release user from worker
  releaseUser(userId) {
    const workerId = this.userAssignments.get(userId);
    if (workerId) {
      const worker = this.workers.get(workerId);
      if (worker) {
        worker.activeUsers.delete(userId);
        worker.totalActionsHandled++;
      }

      this.userAssignments.delete(userId);
      this.activeActions.delete(userId);

      console.log(
        `[WorkerManager] Released user ${userId} from worker ${workerId}`
      );
    }
  }

  // Check if user is busy
  isUserBusy(userId) {
    return this.userAssignments.has(userId);
  }

  // Get worker stats
  getWorkerStats() {
    const stats = {
      totalWorkers: this.workers.size,
      healthyWorkers: 0,
      totalActiveUsers: 0,
      workers: {},
    };

    for (const [workerId, worker] of this.workers.entries()) {
      const isHealthy = Date.now() - worker.lastHeartbeat < 60000;
      if (isHealthy) stats.healthyWorkers++;

      stats.totalActiveUsers += worker.activeUsers.size;

      stats.workers[workerId] = {
        status: worker.status,
        activeUsers: worker.activeUsers.size,
        totalActionsHandled: worker.totalActionsHandled,
        lastHeartbeat: worker.lastHeartbeat,
        healthy: isHealthy,
        capabilities: worker.capabilities,
      };
    }

    return stats;
  }

  // Delegate action to worker
  async delegateAction(
    interaction,
    commandName,
    validatedAmount,
    validatedResourceKey,
    wasMax = false
  ) {
    const userId = interaction.user.id;

    // Find available worker
    const worker = this.getAvailableWorker(commandName);
    if (!worker) {
      const { EmbedBuilder } = require("discord.js");
      const { EMBED_COLORS } = require("../gameConfig");
      const embed = new EmbedBuilder()
        .setDescription(
          "⚠️ All workers are currently busy. Please try again in a moment."
        )
        .setColor(EMBED_COLORS.YELLOW);

      try {
        if (!interaction.deferred && !interaction.replied) {
          await interaction.reply({ embeds: [embed], ephemeral: true });
        } else if (interaction.deferred) {
          await interaction.editReply({ embeds: [embed] });
        }
      } catch (replyError) {
        console.error(
          `[WorkerManager] Failed to send no-worker-available message:`,
          replyError
        );
      }
      return false;
    }

    try {
      // Assign user to worker
      await this.assignUserToWorker(userId, worker.workerId, commandName);

      // Defer the original interaction so Discord knows we received it
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferReply();
      }

      // Extract action parameters from the slash command options safely
      // Use validated values from bot.js if provided
      let amount = validatedAmount || 1;
      let targetItem = validatedResourceKey || null;
      let duration = amount; // For skills that use duration, fallback to amount

      // Map short command names to full skill names, including brew -> alchemy
      const skillNameMap = {
        farm: "farming",
        mine: "mining",
        fish: "fishing",
        combat: "combat",
        alchemy: "alchemy",
        forage: "foraging",
        brew: "alchemy", // ensure /brew is delegated as alchemy
      };
      const fullSkillName = skillNameMap[commandName] || commandName;

      // Create action in database first to get actionId
      const { startAction } = require("./actionPersistence");
      const parameters = {}; // fill with any needed parameters for your action
      const actionId = await startAction(
        interaction.user.id,
        fullSkillName,
        targetItem,
        parameters,
        amount || duration,
        interaction.channelId,
        null, // messageId will be set later when worker creates the message
        worker.workerId
      );

      // Prepare simplified interaction data for worker
      const interactionData = {
        user: {
          id: interaction.user.id,
          username: interaction.user.username,
          displayName:
            interaction.user.displayName || interaction.user.username,
        },
        guildId: interaction.guildId,
        channelId: interaction.channelId,
        commandName: fullSkillName,
        workerId: worker.workerId,
        actionId: actionId, // include actionId for double-handling protection
      };

      // Send action to worker using a shared keep-alive HTTP client
      const axiosClient = worker.axiosClient;

      try {
        const response = await axiosClient.post(`/api/execute-action`, {
          actionType: fullSkillName,
          interaction: interactionData,
          duration: duration,
          amount: amount,
          targetItem: targetItem,
          wasMax: wasMax,
        });

        console.log(
          `[WorkerManager] Successfully delegated ${commandName} for user ${userId} to worker ${worker.workerId}`
        );

        // Send response to original interaction since worker will create its own message
        try {
          if (interaction.deferred) {
            await interaction.deleteReply();
          } else if (!interaction.replied) {
            await interaction.deferReply();
            await interaction.deleteReply();
          }
        } catch (deleteError) {
          console.log(
            `[WorkerManager] Could not delete deferred reply (this is normal): ${deleteError.message}`
          );
        }

        return true;
      } catch (err) {
        // If we get a 409 conflict, the worker already has this action
        if (err.response && err.response.status === 409) {
          console.log(
            `[WorkerManager] Worker ${worker.workerId} already processing action for user ${userId} - delegation successful`
          );

          // Clean up the deferred interaction since worker handles the response
          try {
            if (interaction.deferred) {
              await interaction.deleteReply();
            }
          } catch (deleteError) {
            console.log(
              `[WorkerManager] Could not delete deferred reply after 409: ${deleteError.message}`
            );
          }

          return true;
        }

        // For any other error (connection failed, etc), throw it
        console.error(
          `[WorkerManager] Failed to delegate ${commandName} for user ${userId}:`,
          err.message
        );
        throw err;
      }
    } catch (error) {
      console.error(
        `[WorkerManager] Failed to delegate action to worker ${worker.workerId}:`,
        error
      );

      // Release user assignment
      this.releaseUser(userId);

      // Send error to user using reliable editReply pattern
      try {
        if (interaction.deferred) {
          await interaction.editReply({
            content: `❌ Failed to process your request. Please try again.`,
          });
        } else if (!interaction.replied) {
          await interaction.reply({
            content: `❌ Failed to process your request. Please try again.`,
            ephemeral: true,
          });
        }
      } catch (interactionError) {
        console.error(
          `[WorkerManager] Failed to send error response to user:`,
          interactionError
        );
        // If all interaction methods fail, we can't notify the user through Discord
        // The error is already logged above
      }

      return false;
    }
  }
}

// Create global worker manager instance
const workerManager = new WorkerManager();

// Express routes for worker communication
function setupWorkerRoutes(app) {
  // Worker registration endpoint
  app.post("/register-worker", (req, res) => {
    try {
      const { workerId, port, capabilities, status } = req.body;

      if (!workerId || !port || !capabilities) {
        return res.status(400).json({ error: "Missing required fields" });
      }

      workerManager.registerWorker(workerId, { port, capabilities, status });

      res.json({ success: true, message: "Worker registered successfully" });
    } catch (error) {
      console.error("[WorkerRoutes] Error registering worker:", error);
      res.status(500).json({ error: "Failed to register worker" });
    }
  });

  // Worker notification endpoint
  app.post("/worker-notification", (req, res) => {
    try {
      const { eventType, workerId, data } = req.body;

      switch (eventType) {
        case "heartbeat":
          workerManager.updateHeartbeat(workerId, data);
          break;

        case "action-complete":
          workerManager.releaseUser(data.userId);
          console.log(
            `[WorkerRoutes] User ${data.userId} completed ${data.commandName} on worker ${workerId}`
          );
          break;

        case "action-failed":
          workerManager.releaseUser(data.userId);
          console.warn(
            `[WorkerRoutes] User ${data.userId} failed ${data.commandName} on worker ${workerId}: ${data.error}`
          );
          break;

        case "worker-shutdown":
          workerManager.unregisterWorker(workerId);
          console.log(
            `[WorkerRoutes] Worker ${workerId} shutting down: ${data.reason}`
          );
          break;

        case "resumption":
          // Handle resumption status notifications from workers
          console.log(
            `[WorkerRoutes] Worker ${workerId} resumption ${req.body.status}: ${data.timestamp}${data.actionsResumed !== undefined ? ` (${data.actionsResumed} actions)` : ""}${data.error ? ` - Error: ${data.error}` : ""}`
          );
          break;

        default:
          console.warn(`[WorkerRoutes] Unknown event type: ${eventType}`);
      }

      res.json({ success: true });
    } catch (error) {
      console.error(
        "[WorkerRoutes] Error processing worker notification:",
        error
      );
      res.status(500).json({ error: "Failed to process notification" });
    }
  });

  // Worker stats endpoint (for admin monitoring)
  app.get("/worker-stats", (req, res) => {
    try {
      const stats = workerManager.getWorkerStats();
      res.json(stats);
    } catch (error) {
      console.error("[WorkerRoutes] Error getting worker stats:", error);
      res.status(500).json({ error: "Failed to get worker stats" });
    }
  });

  // Force release user endpoint (for /unstuck command)
  app.post("/release-user", (req, res) => {
    try {
      const { userId } = req.body;

      if (!userId) {
        return res.status(400).json({ error: "Missing userId" });
      }

      workerManager.releaseUser(userId);
      res.json({ success: true, message: "User released successfully" });
    } catch (error) {
      console.error("[WorkerRoutes] Error releasing user:", error);
      res.status(500).json({ error: "Failed to release user" });
    }
  });
}

// Periodic cleanup of unhealthy workers
setInterval(() => {
  workerManager.cleanupUnhealthyWorkers();
}, 30000); // Every 30 seconds

module.exports = {
  workerManager,
  setupWorkerRoutes,
};
