{"name": "Potato Minion", "emoji": "<:minion_potato:1375843129562894399>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "resourceItemKey": "POTATO", "recipes": [{"ingredients": [{"itemKey": "POTATO", "amount": 128}]}], "craftingRequirements": {"collections": {"POTATO": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 20, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 20, "maxStorage": 192, "upgradeCost": [{"itemKey": "POTATO", "amount": 256}]}, {"tier": 3, "generationIntervalSeconds": 18, "maxStorage": 192, "upgradeCost": [{"itemKey": "POTATO", "amount": 512}]}, {"tier": 4, "generationIntervalSeconds": 18, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_POTATO", "amount": 8}]}, {"tier": 5, "generationIntervalSeconds": 16, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_POTATO", "amount": 24}]}, {"tier": 6, "generationIntervalSeconds": 16, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_POTATO", "amount": 64}]}, {"tier": 7, "generationIntervalSeconds": 14, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_POTATO", "amount": 128}]}, {"tier": 8, "generationIntervalSeconds": 14, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_POTATO", "amount": 256}]}, {"tier": 9, "generationIntervalSeconds": 12, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_POTATO", "amount": 512}]}, {"tier": 10, "generationIntervalSeconds": 12, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_BAKED_POTATO", "amount": 8}]}, {"tier": 11, "generationIntervalSeconds": 10, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_BAKED_POTATO", "amount": 16}]}], "drops": [{"itemKey": "POTATO", "chance": 1, "min": 2, "max": 4}]}