// Garden Visitor Notifications - Notify players about visitor events

const { EmbedBuilder } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");
const { GARDEN_VISITORS } = require("./gardenVisitors");

/**
 * Create notification embed for new visitor arrival
 * @param {Object} visitor - Visitor data
 * @param {string} playerName - Player's display name
 * @returns {Object} Discord embed
 */
function createVisitorArrivalEmbed(visitor, playerName) {
  const visitorData = GARDEN_VISITORS[visitor.key];

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PASTEL_GREEN)
    .setTitle(`${visitorData.emoji} New Garden Visitor!`)
    .setDescription(
      `**${visitorData.name}** (${visitor.rarity}) has arrived in ${playerName}'s garden!\n\n` +
        `*"${visitorData.dialogue}"*\n\n` +
        `Use \`/visitors\` to see what they're looking for.`
    )
    .setFooter({ text: "Use /visitors to see what they're looking for!" });

  return embed;
}

/**
 * Create notification embed for milestone achievement
 * @param {Object} milestone - Milestone data
 * @param {string} playerName - Player's display name
 * @returns {Object} Discord embed
 */
function createMilestoneEmbed(milestone, playerName) {
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GOLD)
    .setTitle("🎉 Garden Visitor Milestone!")
    .setDescription(
      `**${playerName}** achieved a visitor milestone!\n\n` +
        `**${milestone.type === "offers_accepted" ? "Offers Accepted" : "Unique Visitors"}:** ${milestone.milestone}\n\n` +
        `**Rewards:**\n` +
        `<:garden:1394656922623410237> Garden XP: +${milestone.garden_xp}\n` +
        `<:skill_farming:1367753361511682128> Farming XP: +${milestone.farmingXp}`
    );

  return embed;
}

/**
 * Create notification embed for rare visitor spawn
 * @param {Object} visitor - Visitor data
 * @param {string} playerName - Player's display name
 * @returns {Object} Discord embed
 */
function createRareVisitorEmbed(visitor, playerName) {
  const visitorData = GARDEN_VISITORS[visitor.key];

  // Only notify for LEGENDARY, MYTHIC, or SPECIAL visitors
  if (!["LEGENDARY", "MYTHIC", "SPECIAL"].includes(visitor.rarity)) {
    return null;
  }

  const embed = new EmbedBuilder()
    .setColor(
      visitor.rarity === "LEGENDARY"
        ? EMBED_COLORS.GOLD
        : visitor.rarity === "MYTHIC"
          ? EMBED_COLORS.PURPLE
          : EMBED_COLORS.RED
    )
    .setTitle(`✨ Rare Garden Visitor Alert!`)
    .setDescription(
      `**${visitorData.name}** (${visitor.rarity}) has arrived in ${playerName}'s garden!\n\n` +
        `This is a rare visitor - don't miss out on the valuable rewards!`
    )
    .setFooter({
      text: "Rare visitors provide significantly more XP and copper!",
    });

  return embed;
}

module.exports = {
  createVisitorArrivalEmbed,
  createMilestoneEmbed,
  createRareVisitorEmbed,
};
