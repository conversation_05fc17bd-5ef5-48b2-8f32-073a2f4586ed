{"name": "js-rpg-bot", "version": "0.0.1", "main": "index.js", "scripts": {"start": "node bot.js", "web": "node web-server.js", "dev": "concurrently \"npm run start\" \"npm run web\"", "lint": "eslint .", "format": "prettier --write .", "format:check": "prettier --check .", "fix:visitors": "node scripts/normalize-visitor-data.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@sentry/node": "^9.31.0", "@sentry/profiling-node": "^9.31.0", "axios": "^1.11.0", "cors": "^2.8.5", "discord.js": "^14.21.0", "dotenv": "^16.5.0", "express": "^4.18.2", "git": "^0.1.5", "js-rpg-bot": "file:", "mcp-knowledge-graph": "^1.0.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "sqlite3": "^5.1.7", "stripe": "^18.3.0", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@eslint/json": "^0.12.0", "concurrently": "^7.6.0", "eslint": "^9.30.1", "globals": "^16.3.0", "prettier": "^3.6.1"}}