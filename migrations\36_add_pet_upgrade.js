// migrations/013_add_pet_upgrade.js
// Add pet_upgrade_json column to players table

/**
 * Applies the migration to add pet_upgrade_json column to players table
 * @param {object} db - The database connection
 * @param {object} utils - Utility functions for player data
 */
async function up(db) {
  console.log(
    "Running migration: Add pet_upgrade_json column to players table",
  );

  // Check if the column already exists
  const tableInfo = await new Promise((resolve, reject) => {
    db.all("PRAGMA table_info(players)", (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });

  const columnExists = tableInfo.some((col) => col.name === "pet_upgrade_json");

  if (!columnExists) {
    // Add the column
    await new Promise((resolve, reject) => {
      db.run("ALTER TABLE players ADD COLUMN pet_upgrade_json TEXT", (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
    console.log("Added pet_upgrade_json column to players table");
  } else {
    console.log("pet_upgrade_json column already exists in players table");
  }
}

/**
 * Reverts the migration (removes the pet_upgrade_json column)
 * @param {object} db - The database connection
 * @param {object} utils - Utility functions for player data
 */
async function down() {
  console.log(
    "This migration cannot be reverted directly in SQLite (cannot drop columns)",
  );
  console.log(
    "To revert, you would need to create a new table without the column and copy data",
  );
}

module.exports = { up, down };
