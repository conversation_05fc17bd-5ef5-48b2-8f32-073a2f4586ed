// Constants for DisBlock Time (Renamed)
const DB_SECONDS_PER_MINUTE = 60;
const DB_MINUTES_PER_HOUR = 60;
const DB_HOURS_PER_DAY = 24;
const DB_DAYS_PER_MONTH = 31;
const DB_MONTHS_PER_YEAR = 12;

const DB_SECONDS_PER_HOUR = DB_SECONDS_PER_MINUTE * DB_MINUTES_PER_HOUR; // 3600
const DB_SECONDS_PER_DAY = DB_SECONDS_PER_HOUR * DB_HOURS_PER_DAY; // 86400
const DB_SECONDS_PER_MONTH = DB_SECONDS_PER_DAY * DB_DAYS_PER_MONTH; // 2678400
const DB_SECONDS_PER_YEAR = DB_SECONDS_PER_MONTH * DB_MONTHS_PER_YEAR; // 32140800

const DISBLOCK_SECONDS_PER_REAL_SECOND = 72; // Renamed
const BAKER_WINDOW_REAL_SECONDS = 24 * 60 * 60; // 24 real-life hours

/**
 * Converts total elapsed DisBlock seconds since the epoch into a structured date/time object.
 * NOTE: This function doesn't need the full state, only the seconds.
 * @param {number} totalSeconds - Total elapsed DisBlock seconds.
 * @returns {{ year: number, month: number, day: number, hour: number, minute: number, second: number }}
 */
function disBlockSecondsToDateTime(totalSeconds) {
  // Renamed function
  if (typeof totalSeconds !== "number" || totalSeconds < 0) {
    totalSeconds = 0;
  }

  // Use renamed constants
  const year = Math.floor(totalSeconds / DB_SECONDS_PER_YEAR) + 1;
  const secondsInCurrentYear = totalSeconds % DB_SECONDS_PER_YEAR;

  const month = Math.floor(secondsInCurrentYear / DB_SECONDS_PER_MONTH) + 1; // Months 1-12
  const secondsInCurrentMonth = secondsInCurrentYear % DB_SECONDS_PER_MONTH;

  const day = Math.floor(secondsInCurrentMonth / DB_SECONDS_PER_DAY) + 1; // Days 1-31
  const secondsInCurrentDay = secondsInCurrentMonth % DB_SECONDS_PER_DAY;

  const hour = Math.floor(secondsInCurrentDay / DB_SECONDS_PER_HOUR);
  const secondsInCurrentHour = secondsInCurrentDay % DB_SECONDS_PER_HOUR;

  const minute = Math.floor(secondsInCurrentHour / DB_SECONDS_PER_MINUTE);
  const second = Math.floor(secondsInCurrentHour % DB_SECONDS_PER_MINUTE);

  return { year, month, day, hour, minute, second };
}

/**
 * Gets the current DisBlock date and time as a structured object.
 * @param {{disBlockEpochUTC: number, lastSavedRealTimestampUTC: number, disBlockSecondsElapsed: number}} currentTimeState - The current time state object from bot.js (with renamed fields).
 * @returns {{ year: number, month: number, day: number, hour: number, minute: number, second: number }}
 */
function getDisBlockDateTime(currentTimeState) {
  // Renamed function
  // Use the passed state (with renamed field)
  const totalSeconds = currentTimeState?.disBlockSecondsElapsed || 0;
  return disBlockSecondsToDateTime(totalSeconds); // Call renamed function
}

/**
 * Determines the DisBlock season string (e.g., "Early Spring", "Summer", "Late Winter") based on the month number.
 * NOTE: This function doesn't directly need the time state, only the month number.
 * @param {number} month - DisBlock month number (1-12).
 * @returns {string} The season string.
 */
function getSeason(month) {
  if (month < 1 || month > 12) return "Invalid Month";

  const monthInSeason = ((month - 1) % 3) + 1; // 1, 2, or 3
  let seasonName = "";
  let prefix = "";

  if (month >= 1 && month <= 3) seasonName = "Spring";
  else if (month >= 4 && month <= 6) seasonName = "Summer";
  else if (month >= 7 && month <= 9) seasonName = "Autumn";
  else seasonName = "Winter";

  if (monthInSeason === 1) prefix = "Early ";
  else if (monthInSeason === 3) prefix = "Late ";
  // MonthInSeason === 2 has no prefix

  return `${prefix}${seasonName}`;
}

/**
 * Calculates the real-world UTC timestamp (in milliseconds) for the start of the *next* DisBlock year.
 * @param {{disBlockEpochUTC: number, lastSavedRealTimestampUTC: number, disBlockSecondsElapsed: number}} currentTimeState - The current time state object from bot.js (with renamed fields).
 * @returns {number} The UTC timestamp in milliseconds.
 */
function calculateNextDisBlockYearRolloverUTC(currentTimeState) {
  // Renamed function
  // Use the passed state (with renamed fields)
  const disBlockEpochUTC =
    currentTimeState?.disBlockEpochUTC ||
    new Date("2025-05-12T00:00:00Z").getTime();
  const disBlockSecondsElapsed = currentTimeState?.disBlockSecondsElapsed || 0;

  // Use renamed constants
  const currentYearDecimal = disBlockSecondsElapsed / DB_SECONDS_PER_YEAR;
  const currentYearNumber = Math.floor(currentYearDecimal); // Year index (0-indexed)

  const nextYearStartDBSeconds = (currentYearNumber + 1) * DB_SECONDS_PER_YEAR; // DB Seconds at the exact start of the *next* year

  // Calculate real seconds from epoch to the start of the next DB year
  const realSecondsFromEpochToNextRollover =
    nextYearStartDBSeconds / DISBLOCK_SECONDS_PER_REAL_SECOND;

  // Calculate the exact real-world UTC timestamp in milliseconds
  const nextRolloverUTCms =
    disBlockEpochUTC + Math.floor(realSecondsFromEpochToNextRollover * 1000);

  return nextRolloverUTCms;
}

/**
 * Checks if the current real-world time is within the Baker's spawn window (1 hour before next DisBlock year rollover).
 * @param {{disBlockEpochUTC: number, lastSavedRealTimestampUTC: number, disBlockSecondsElapsed: number}} currentTimeState - The current time state object from bot.js (with renamed fields).
 * @returns {boolean}
 */
function isBakerSpawnWindow(currentTimeState) {
  // Function name is okay
  const nowUTCms = Date.now();
  const nextRolloverUTCms =
    calculateNextDisBlockYearRolloverUTC(currentTimeState); // Call renamed function
  const windowStartUTCms = nextRolloverUTCms - BAKER_WINDOW_REAL_SECONDS * 1000;

  return nowUTCms >= windowStartUTCms && nowUTCms < nextRolloverUTCms;
}

/**
 * Gets the DisBlock year number that is currently ending or has just ended.
 * Useful for assigning the correct year to the New Year Cake.
 * @param {{disBlockEpochUTC: number, lastSavedRealTimestampUTC: number, disBlockSecondsElapsed: number}} currentTimeState - The current time state object from bot.js (with renamed fields).
 * @returns {number} The ending year number (0 for the first year, 1 for the second, etc.).
 */
function getEndingYear(currentTimeState) {
  // Function name is okay
  // Use the passed state (with renamed field)
  const disBlockSecondsElapsed = currentTimeState?.disBlockSecondsElapsed || 0;
  // Use renamed constant
  const endingYear = Math.floor(disBlockSecondsElapsed / DB_SECONDS_PER_YEAR);
  return endingYear; // Year index (0 for the first year, 1 for the second, etc.)
}

module.exports = {
  disBlockSecondsToDateTime, // Renamed
  getDisBlockDateTime, // Renamed
  getSeason,
  calculateNextDisBlockYearRolloverUTC, // Renamed
  isBakerSpawnWindow,
  getEndingYear,
  // Export constants if needed elsewhere (use new names)
  DB_DAYS_PER_MONTH,
  DB_MONTHS_PER_YEAR,
  DB_SECONDS_PER_YEAR,
  DISBLOCK_SECONDS_PER_REAL_SECOND, // Use new name
  BAKER_WINDOW_REAL_SECONDS, // Export this constant
};
