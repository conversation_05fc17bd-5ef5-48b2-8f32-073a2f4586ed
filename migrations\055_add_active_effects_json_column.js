// Migration 055: Add active_effects_json column for potion effects
// This column stores the active potion effects for each player

async function up(db, _playerDataUtils) {
  console.log("Running migration 055: Add active_effects_json column...");

  try {
    // Check if column already exists
    const columns = await new Promise((resolve, reject) => {
      db.all("PRAGMA table_info(players)", (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    const hasActiveEffectsColumn = columns.some(
      (col) => col.name === "active_effects_json",
    );

    if (!hasActiveEffectsColumn) {
      console.log("Adding active_effects_json column to players table...");
      await new Promise((resolve, reject) => {
        db.run(
          "ALTER TABLE players ADD COLUMN active_effects_json TEXT",
          (err) => {
            if (err) reject(err);
            else resolve();
          },
        );
      });
      console.log("Successfully added active_effects_json column");
    } else {
      console.log("active_effects_json column already exists, skipping...");
    }

    console.log("Migration 055 completed successfully");
  } catch (error) {
    console.error("Migration 055 failed:", error);
    throw error;
  }
}

module.exports = { up };
