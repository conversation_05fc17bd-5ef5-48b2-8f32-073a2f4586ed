const { PermissionFlagsBits } = require("discord.js");
const config = require("../config.json");
const { dbAll } = require("./dbUtils");
const { getPersonalChannelConfig } = require("./personalChannelConfig");
const { updateSetupChannelPermissions } = require("./setupChannelManager");
const { validateCategoryPermissions } = require("./categoryValidator");
const { sanitizeChannelName } = require("./channelManager");

/**
 * Validate and fix all player channels on bot startup
 * @param {Client} client - Discord client
 */
async function validateAllPlayerChannels(client) {
  try {
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(
        "[ChannelValidator] Starting automatic channel validation..."
      );

    // first validate the category permissions
    if (!QUIET_BOOT)
      console.log(
        "[ChannelValidator] Validating category permissions first..."
      );
    const categoryValidated = await validateCategoryPermissions(client);
    if (!categoryValidated) {
      console.warn(
        "[ChannelValidator] Category validation failed, continuing with individual channels..."
      );
    }

    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      console.error("[ChannelValidator] Guild not found:", config.guildId);
      return;
    }

    // get all players with personal channels
    const players = await dbAll(
      "SELECT discord_id, name, personal_channel_id FROM players WHERE personal_channel_id IS NOT NULL"
    );

    if (players.length === 0) {
      console.log(
        "[ChannelValidator] No players with personal channels found."
      );
      return;
    }

    if (!QUIET_BOOT)
      console.log(
        `[ChannelValidator] Found ${players.length} players with personal channels, validating...`
      );

    let validatedCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    for (const player of players) {
      try {
        const { discord_id, name, personal_channel_id } = player;

        // fetch the channel
        const channel = await guild.channels
          .fetch(personal_channel_id)
          .catch(() => null);
        if (!channel) {
          console.warn(
            `[ChannelValidator] ⚠️ Channel ${personal_channel_id} not found for player ${name} (${discord_id})`
          );
          errorCount++;
          continue;
        }

        let needsUpdate = false;
        const updates = [];

        // get the expected channel configuration
        const channelConfig = getPersonalChannelConfig(name, discord_id);

        // check and fix channel name
        const expectedChannelName = sanitizeChannelName(name);

        if (channel.name !== expectedChannelName) {
          try {
            await channel.setName(expectedChannelName);
            updates.push("name");
            needsUpdate = true;
          } catch (nameError) {
            console.warn(
              `[ChannelValidator] Failed to update name for ${name}:`,
              nameError.message
            );
          }
        }

        // check and fix channel topic
        if (channel.topic !== channelConfig.topic) {
          try {
            await channel.setTopic(channelConfig.topic);
            updates.push("topic");
            needsUpdate = true;
          } catch (topicError) {
            console.warn(
              `[ChannelValidator] Failed to update topic for ${name}:`,
              topicError.message
            );
          }
        }

        // check and fix permissions
        const currentOverwrites = channel.permissionOverwrites.cache;
        let permissionsNeedUpdate = false;

        // but we also need to check if category permissions are properly inherited
        const categoryId = config.channelCreation.playerChannelsCategoryId;
        const category = await guild.channels
          .fetch(categoryId)
          .catch(() => null);

        if (category) {
          // check if we have the basic category permissions
          for (const [permissionId, _categoryOverwrite] of category
            .permissionOverwrites.cache) {
            const currentOverwrite = currentOverwrites.get(permissionId);
            if (!currentOverwrite) {
              console.log(
                `[ChannelValidator] ${name}: Missing category permission for ${permissionId}`
              );
              permissionsNeedUpdate = true;
              break;
            }

            // check if @everyone is properly denied UseApplicationCommands
            if (permissionId === config.guildId) {
              // @everyone
              if (
                !currentOverwrite.deny.has(
                  PermissionFlagsBits.UseApplicationCommands
                )
              ) {
                console.log(
                  `[ChannelValidator] ${name}: @everyone not denied UseApplicationCommands`
                );
                permissionsNeedUpdate = true;
              }
            }
          }
        }

        // check if owner has slash command permission and manage messages permission
        const ownerOverwrite = currentOverwrites.get(discord_id);
        if (
          !ownerOverwrite ||
          !ownerOverwrite.allow.has(
            PermissionFlagsBits.UseApplicationCommands
          ) ||
          !ownerOverwrite.allow.has(PermissionFlagsBits.ManageMessages)
        ) {
          console.log(
            `[ChannelValidator] ${name}: Owner missing UseApplicationCommands or ManageMessages permission`
          );
          permissionsNeedUpdate = true;
        }

        if (permissionsNeedUpdate) {
          try {
            // validate each permission before applying
            const validPermissions = [];
            for (const perm of channelConfig.permissions) {
              if (perm.id === discord_id) {
                // user permission - try to fetch user
                try {
                  await client.users.fetch(discord_id);
                  validPermissions.push(perm);
                } catch {
                  console.warn(
                    `[ChannelValidator] ${name}: User ${discord_id} not found, skipping`
                  );
                }
              } else {
                // role permission - check if role exists
                const role = guild.roles.cache.get(perm.id);
                if (role) {
                  validPermissions.push(perm);
                } else {
                  console.warn(
                    `[ChannelValidator] ${name}: Role ${perm.id} not found, skipping`
                  );
                }
              }
            }

            if (validPermissions.length > 0) {
              // for proper category sync, we need to combine category permissions with our overrides
              try {
                // get the current category permissions
                const categoryId =
                  config.channelCreation.playerChannelsCategoryId;
                const category = await guild.channels.fetch(categoryId);

                if (category) {
                  // start with category permissions as base
                  const combinedPermissions = [];

                  // copy all category permission overwrites
                  for (const [id, overwrite] of category.permissionOverwrites
                    .cache) {
                    combinedPermissions.push({
                      id: id,
                      allow: Array.from(overwrite.allow),
                      deny: Array.from(overwrite.deny),
                    });
                  }

                  // then add/override with our channel-specific permissions
                  for (const perm of validPermissions) {
                    const existingIndex = combinedPermissions.findIndex(
                      (p) => p.id === perm.id
                    );
                    if (existingIndex >= 0) {
                      // merge with existing permission
                      const existing = combinedPermissions[existingIndex];
                      combinedPermissions[existingIndex] = {
                        id: perm.id,
                        allow: [
                          ...new Set([
                            ...existing.allow,
                            ...(perm.allow || []),
                          ]),
                        ],
                        deny: [
                          ...new Set([...existing.deny, ...(perm.deny || [])]),
                        ],
                      };
                    } else {
                      // add new permission
                      combinedPermissions.push(perm);
                    }
                  }

                  await channel.permissionOverwrites.set(combinedPermissions);
                  updates.push("permissions (category + overrides)");
                  needsUpdate = true;
                } else {
                  console.warn(
                    `[ChannelValidator] ${name}: Category not found, using direct permissions`
                  );
                  await channel.permissionOverwrites.set(validPermissions);
                  updates.push("permissions");
                  needsUpdate = true;
                }
              } catch (syncError) {
                console.warn(
                  `[ChannelValidator] ${name}: Failed to combine with category permissions (${syncError.message}), falling back to direct permission set`
                );
                // fallback to the old method
                await channel.permissionOverwrites.set(validPermissions);
                updates.push("permissions");
                needsUpdate = true;
              }
            } else {
              console.warn(
                `[ChannelValidator] ${name}: No valid permissions to apply`
              );
            }
          } catch (permError) {
            console.warn(
              `[ChannelValidator] Failed to update permissions for ${name}:`,
              permError.message
            );
          }
        }

        if (needsUpdate) {
          console.log(
            `[ChannelValidator] ✅ Updated ${name}'s channel: ${updates.join(", ")}`
          );
          updatedCount++;
        }

        validatedCount++;
      } catch (error) {
        console.error(
          `[ChannelValidator] ❌ Error validating channel for ${player.name}:`,
          error.message
        );
        errorCount++;
      }
    }

    if (!QUIET_BOOT)
      console.log(
        `[ChannelValidator] Validation complete: ${validatedCount} validated, ${updatedCount} updated, ${errorCount} errors`
      );

    // also update setup channel permissions to hide it from players with channels
    if (!QUIET_BOOT)
      console.log("[ChannelValidator] Updating setup channel permissions...");
    await updateSetupChannelPermissions(client);
  } catch (error) {
    console.error(
      "[ChannelValidator] Failed to validate player channels:",
      error
    );
  }
}

/**
 * Helper function to check if a single channel needs validation
 * @param {Client} client - Discord client
 * @param {string} discordId - Player's Discord ID
 * @param {string} channelId - Channel ID to validate
 * @param {string} playerName - Player's character name
 * @returns {Promise<Object>} - Validation result
 */
async function validateSingleChannel(client, discordId, channelId, playerName) {
  try {
    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      throw new Error("Guild not found");
    }

    const channel = await guild.channels.fetch(channelId);
    if (!channel) {
      throw new Error("Channel not found");
    }

    const issues = [];
    const channelConfig = getPersonalChannelConfig(playerName, discordId);

    // check topic
    if (channel.topic !== channelConfig.topic) {
      issues.push(`Topic should be: "${channelConfig.topic}"`);
    }

    // check owner permissions
    const ownerOverwrite = channel.permissionOverwrites.cache.get(discordId);
    if (
      !ownerOverwrite ||
      !ownerOverwrite.allow.has(PermissionFlagsBits.UseApplicationCommands)
    ) {
      issues.push("Owner missing slash command permissions");
    }

    return {
      valid: issues.length === 0,
      issues: issues,
      channelName: channel.name,
      currentTopic: channel.topic,
    };
  } catch (error) {
    return {
      valid: false,
      issues: [error.message],
      channelName: "Unknown",
      currentTopic: "Unknown",
    };
  }
}

module.exports = {
  validateAllPlayerChannels,
  validateSingleChannel,
};
