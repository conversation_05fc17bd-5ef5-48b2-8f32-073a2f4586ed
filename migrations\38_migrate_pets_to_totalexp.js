// Migration script to convert pets from level+exp format to totalExp format
// This can be run manually or automatically during pet access

const { dbAll, dbRun } = require("../utils/dbUtils");
const { safeJsonParse } = require("../utils/playerDataManager");

/**
 * Migrates a single pet from old format to new format
 * @param {object} pet - Pet object to migrate
 * @returns {object} - Migrated pet object
 */
function migratePetToNewFormat(pet) {
  // If already in new format, return as-is
  if (
    pet.totalExp !== undefined &&
    pet.level === undefined &&
    pet.exp === undefined &&
    pet.xp === undefined
  ) {
    return pet;
  }

  // Calculate total XP from old format
  let totalExp = 0;

  if (pet.totalExp !== undefined) {
    // Already has totalExp, just clean up old fields
    totalExp = pet.totalExp;
  } else if (pet.exp !== undefined) {
    // Has exp field (current XP within level)
    totalExp = pet.exp;
  } else if (pet.xp !== undefined) {
    // Has old xp field
    totalExp = pet.xp;
  } else if (pet.level !== undefined) {
    // Only has level, estimate XP (this shouldn't happen but just in case)
    console.warn(
      `[PetMigration] Pet ${pet.id} only has level ${pet.level}, estimating XP`,
    );
    // This is a rough estimate - in practice this case shouldn't occur
    totalExp = 0;
  }

  // Create new format pet
  const migratedPet = {
    id: pet.id,
    petKey: pet.petKey,
    rarity: pet.rarity,
    totalExp: Math.max(0, totalExp),
  };

  console.log(
    `[PetMigration] Migrated pet ${pet.id}: ${pet.petKey} ${pet.rarity} - totalExp: ${totalExp}`,
  );
  return migratedPet;
}

/**
 * Migrates all pets for a single player
 * @param {string} discordId - Player's Discord ID
 * @returns {Promise<boolean>} - Success status
 */
async function migratePlayerPets(discordId) {
  try {
    // Get player data
    const row = await dbAll(
      "SELECT pets_json FROM players WHERE discord_id = ?",
      [discordId],
    );
    if (!row || row.length === 0) {
      return true; // No player found, nothing to migrate
    }

    const petsJson = row[0].pets_json;
    if (!petsJson) {
      return true; // No pets, nothing to migrate
    }

    const pets = safeJsonParse(petsJson, []);
    if (!Array.isArray(pets) || pets.length === 0) {
      return true; // No pets to migrate
    }

    // Check if any pets need migration
    const needsMigration = pets.some(
      (pet) =>
        pet.level !== undefined ||
        pet.exp !== undefined ||
        pet.xp !== undefined,
    );

    if (!needsMigration) {
      console.log(`[PetMigration] Player ${discordId} pets already migrated`);
      return true;
    }

    // Migrate all pets
    const migratedPets = pets.map(migratePetToNewFormat);

    // Save back to database
    await dbRun("UPDATE players SET pets_json = ? WHERE discord_id = ?", [
      JSON.stringify(migratedPets),
      discordId,
    ]);

    console.log(
      `[PetMigration] Successfully migrated ${migratedPets.length} pets for player ${discordId}`,
    );
    return true;
  } catch (error) {
    console.error(
      `[PetMigration] Error migrating pets for player ${discordId}:`,
      error,
    );
    return false;
  }
}

/**
 * Migrates all players' pets in the database
 * @returns {Promise<{success: number, failed: number, total: number}>}
 */
async function migrateAllPlayerPets() {
  try {
    console.log("[PetMigration] Starting migration of all player pets...");

    // Get all players with pets
    const players = await dbAll(
      'SELECT discord_id FROM players WHERE pets_json IS NOT NULL AND pets_json != "[]"',
    );

    console.log(
      `[PetMigration] Found ${players.length} players with pets to check`,
    );

    let success = 0;
    let failed = 0;

    for (const player of players) {
      const result = await migratePlayerPets(player.discord_id);
      if (result) {
        success++;
      } else {
        failed++;
      }

      // Small delay to avoid overwhelming the database
      await new Promise((resolve) => {
        setTimeout(resolve, 10);
      });
    }

    console.log(
      `[PetMigration] Migration complete: ${success} successful, ${failed} failed, ${players.length} total`,
    );

    return {
      success,
      failed,
      total: players.length,
    };
  } catch (error) {
    console.error("[PetMigration] Error during bulk migration:", error);
    throw error;
  }
}

/**
 * Database migration function - required by the migration system
 * This will be called automatically when the bot starts
 */
async function up() {
  console.log(
    "[Migration 38] Starting pet data migration to totalExp format...",
  );

  try {
    const result = await migrateAllPlayerPets();
    console.log(
      `[Migration 38] Pet migration completed: ${result.success}/${result.total} players migrated successfully`,
    );

    if (result.failed > 0) {
      console.warn(
        `[Migration 38] ${result.failed} players failed to migrate - they will be migrated automatically when accessed`,
      );
    }

    return true;
  } catch (error) {
    console.error("[Migration 38] Pet migration failed:", error);
    // Don't throw - allow bot to start, pets will migrate automatically when accessed
    console.log(
      "[Migration 38] Continuing with automatic migration on pet access...",
    );
    return true;
  }
}

module.exports = {
  up,
  migratePetToNewFormat,
  migratePlayerPets,
  migrateAllPlayerPets,
};
