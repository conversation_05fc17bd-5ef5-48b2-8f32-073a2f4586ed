const { getDefaultStats } = require("./playerDefaults");
const {
  calculateEquipmentStats,
  calculatePetStats,
  calculateAllStats,
} = require("./statCalculations");
const { applyEnderArmorBonus } = require("./enderArmorSetBonus");
const { dbGet, dbAll, dbRunQueued } = require("./dbUtils");
const { getLevelFromExp } = require("./expFunctions");
const { getAllSlayerLevels } = require("./slayerLevelUtils");
const {
  MAGICAL_POWER_VALUES,
  ACCESSORY_POWERS,
  calculateAccessoryStat,
} = require("./accessoryPowers");

const QUIET_BOOT = process.env.QUIET_BOOT === "true";

// Dynamic column discovery and field mapping
let cachedColumns = null;
let cachedFieldMappings = null;
let cacheInitialized = false;

/**
 * Initialize metadata cache once at startup
 */
async function initializeMetadataCache() {
  if (cacheInitialized) return;

  try {
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log("[PlayerDataManager] Initializing metadata cache...");
    await getPlayerTableColumns();
    await getFieldMappings();
    cacheInitialized = true;
    if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
      console.log(
        "[PlayerDataManager] Metadata cache initialized successfully"
      );
  } catch (error) {
    console.warn(
      "[PlayerDataManager] Failed to initialize metadata cache:",
      error
    );
    // Don't mark as initialized so it can retry later
  }
}

/**
 * Clear cache and force refresh of field mappings
 */
function clearFieldMappingsCache() {
  if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
    console.log("[PlayerDataManager] Clearing field mappings cache...");
  cachedFieldMappings = null;
  cachedColumns = null;
  cacheInitialized = false;
}

/**
 * Dynamically discover database columns to avoid manual maintenance
 */
async function getPlayerTableColumns() {
  if (cachedColumns) return cachedColumns;

  try {
    const columns = await dbAll("PRAGMA table_info(players)");
    cachedColumns = columns.map((col) => col.name);
    if (process.env.IS_MAIN_BOT === "true") {
      if (!QUIET_BOOT) {
        console.log(
          `[PlayerDataManager] Discovered ${cachedColumns.length} columns in players table:`
        );
        console.log(`[PlayerDataManager] Columns: ${cachedColumns.join(", ")}`);
      }
    }
    return cachedColumns;
  } catch (error) {
    console.error(
      "[PlayerDataManager] Failed to discover table columns:",
      error
    );
    // Fallback to basic columns if introspection fails
    return [
      "discord_id",
      "name",
      "current_region",
      "current_health",
      "coins",
      "bank",
      "rank",
    ];
  }
}

/**
 * Get field mappings (cached version)
 */
async function getFieldMappings() {
  if (cachedFieldMappings) return cachedFieldMappings;

  const columns = await getPlayerTableColumns();
  return generateFieldMappings(columns);
}

/**
 * Generate field mappings using simple conventions
 */
function generateFieldMappings(columns) {
  if (cachedFieldMappings) return cachedFieldMappings;

  const mappings = {};

  // RULE 1: Every database column maps to itself (direct access)
  for (const column of columns) {
    mappings[column] = column;
  }

  // RULE 2: Auto-detect JSON field shortcuts (field without _json suffix maps to _json column)
  // This automatically creates mappings for any *_json columns in the database
  for (const column of columns) {
    if (column.endsWith("_json")) {
      // Convert "skills_json" -> "skills", "minion_storage_json" -> "minionStorage", etc.
      let shortName = column.replace("_json", "");

      // Convert snake_case to camelCase for multi-word fields
      if (shortName.includes("_")) {
        shortName = shortName.replace(/_([a-z])/g, (match, letter) =>
          letter.toUpperCase()
        );
      }

      // Add the mapping so "skills" maps to "skills_json", "minionStorage" maps to "minion_storage_json", etc.
      mappings[shortName] = column;
    }
  }

  // RULE 3: Composite fields (special cases that combine multiple columns)
  mappings["inventory"] = null; // Special case: handled separately

  cachedFieldMappings = mappings;
  return mappings;
}

/**
 * Initialize field mappings cache (called on module load)
 */
async function initializeFieldMappings() {
  if (!cachedFieldMappings) {
    try {
      await getFieldMappings();
      if (process.env.IS_MAIN_BOT === "true" && !QUIET_BOOT)
        console.log(
          "[PlayerDataManager] Field mappings initialized successfully"
        );
    } catch (error) {
      console.warn(
        "[PlayerDataManager] Failed to initialize field mappings:",
        error
      );
      // Don't throw - let the system work with fallback mappings
    }
  }
}

/**
 * Get the database column name(s) for a field
 */
function getColumnForField(field, fieldMappings) {
  const mapping = fieldMappings[field];
  if (!mapping) {
    // Only log warnings for fields that should have database mappings
    // Special fields like "inventory", "equipment", etc. are handled separately
    if (!["inventory", "equipment", "accessories"].includes(field)) {
      console.warn(`[PlayerDataManager] No mapping found for field: ${field}`);
    }
    return null;
  }
  return mapping;
}

/**
 * Check if a column stores JSON data
 */
function isJsonColumn(columnName) {
  return (
    columnName.endsWith("_json") ||
    columnName === "cakeBag" ||
    columnName === "disblockMilestones" ||
    columnName === "visitor_unique_served" || // JSON array stored in TEXT column
    columnName === "visitor_completion_counts"
  ); // JSON object stored in TEXT column
}

/**
 * Get default value for a field
 */
function getDefaultValueForField(field, columnName) {
  const defaults = {
    // Array defaults - JSON columns only
    pets_json: [],
    minion_storage_json: [],
    crafted_minions_json: [],
    cakeBag: [],

    // Object defaults - JSON columns only
    skills_json: {},
    stats_json: {},
    collections_json: {},
    island_json: {},
    settings_json: {},
    mob_kills_json: {},
    slayer_xp_json: {},
    active_effects_json: {},
    shop_limits_json: {},
    garden_milestones_json: {},
    garden_crop_upgrades_json: {},
    // Extra JSON-in-TEXT columns
    visitor_completion_counts: {},

    // Special object defaults
    disblockMilestones: { skills: {}, collections: {}, minions: [] },
    // Store as actual JSON array (not string) to avoid double-encoding
    visitor_unique_served: [],

    // Null defaults
    pet_upgrade_json: null,
    active_pet_id: null,
    visiting_island_owner_id: null,
    last_identity_change: null,
    active_slayer_quest: null,
    personal_channel_id: null,
    booster_cookie_expiry: null,
    last_coin_generation: null,
    last_bits_collection: null,

    // Number defaults
    disblock_xp: 0,
    lastClaimedYear: 0,
    current_health: 100,
    coins: 0,
    bank: 0,
    garden_xp: 0,
    garden_visitor_timer: 0,
    copper: 0,
    visitor_offers_accepted: 0,
    gems: 0,
    bits: 0,
    bits_available: 0,
    bits_multiplier: 1.0,
    base_bits_from_cookies: 0,
    accessory_power: 0,

    // String defaults
    current_region: "the_hub",
    rank: "MEMBER",
    name: "Unknown Player",
  };

  return defaults[field] ?? defaults[columnName] ?? null;
}

// Helper to safely parse JSON, returning a default value on error or null/undefined input
function safeJsonParse(jsonString, defaultValue) {
  if (jsonString == null) {
    return defaultValue; // Handle null or undefined input
  }

  // If already an object/array, return as-is
  if (typeof jsonString === "object") {
    return jsonString;
  }

  // Ensure string
  const str = String(jsonString);

  // First attempt
  try {
    let parsed = JSON.parse(str);
    // If the parsed result is itself a JSON-looking string, parse one more time
    if (typeof parsed === "string") {
      const inner = parsed.trim();
      if (
        (inner.startsWith("{") && inner.endsWith("}")) ||
        (inner.startsWith("[") && inner.endsWith("]"))
      ) {
        try {
          parsed = JSON.parse(inner);
        } catch {
          // ignore second-pass error, fall through with first parsed value
        }
      }
    }
    return parsed;
  } catch (e1) {
    // Heuristic repair: common corruption is backslash-escaped quotes without outer quotes
    // e.g., [\"A\",\"B\"] -> ["A","B"]
    const looksEscapedArray = /\[\\".+\\"\]/.test(str);
    const looksEscapedObject = /\{.*\\".*\}/.test(str);
    if (looksEscapedArray || looksEscapedObject) {
      try {
        const cleaned = str.replace(/\\"/g, '"');
        return JSON.parse(cleaned);
      } catch (e2) {
        console.error(
          "Failed to parse JSON string (after clean attempt):",
          str,
          e2
        );
        return defaultValue;
      }
    }
    console.error("Failed to parse JSON string:", str, e1); // Keep important error log
    return defaultValue;
  }
}

// *** ADDED: Function to get all player IDs ***
async function getAllPlayerIds() {
  try {
    const rows = await dbAll("SELECT discord_id FROM players", []);
    return rows.map((row) => row.discord_id);
  } catch (error) {
    console.error("[getAllPlayerIds] Error fetching player IDs:", error);
    return []; // Return empty array on error
  }
}

/**
 * Fetches player data for a given Discord ID. Optionally, only fetches specified fields.
 * @param {string} discordId - The Discord user ID.
 * @param {string[]|null} [fields] - Optional array of field names to fetch. If omitted, fetches all fields.
 * @returns {Promise<Object|null>} The player data object or null if not found.
 */
async function getPlayerData(discordId, fields = null) {
  // Initialize cache on first use if not already done
  if (!cacheInitialized) {
    await initializeMetadataCache();
  }

  let retry = false;
  for (let attempt = 1; attempt <= 2; attempt++) {
    try {
      // --- 1. Build SELECT statement based on fields ---
      const allColumns = await getPlayerTableColumns();
      const fieldMappings = await getFieldMappings();

      let selectColumns = allColumns;
      if (Array.isArray(fields) && fields.length > 0) {
        const requiredColumns = new Set(["discord_id"]); // Always include discord_id

        // Add the requested fields using dynamic mapping
        fields.forEach((field) => {
          const columnMapping = getColumnForField(field, fieldMappings);
          if (columnMapping) {
            // Single column mapping
            requiredColumns.add(columnMapping);
          }
        });

        // Always include Garden columns for now (until Garden system is stable)
        requiredColumns.add("garden_milestones_json");
        requiredColumns.add("garden_xp");
        requiredColumns.add("garden_visitor_timer");
        requiredColumns.add("copper");
        requiredColumns.add("visitor_offers_accepted");
        requiredColumns.add("visitor_unique_served");
        requiredColumns.add("garden_crop_upgrades_json");

        selectColumns = Array.from(requiredColumns);
      }
      const playerSql = `SELECT ${selectColumns.join(
        ", "
      )} FROM players WHERE discord_id = ?`;

      const playerRow = await dbGet(playerSql, [discordId]);

      if (!playerRow) {
        console.error(
          `[getPlayerData] No player row found for discordId: ${discordId}`
        );
        if (!retry) {
          retry = true;
          continue;
        }
        return null; // Player not found
      }

      // --- 2. Inventory/Equipment/Accessories: Only fetch if requested or if fields is null (full fetch) ---
      let inventoryItemRows = [],
        equipmentRows = [],
        accessoryRows = [];
      const fetchInventory =
        !fields ||
        fields.includes("inventory") ||
        fields.includes("equipment") ||
        fields.includes("accessories");
      if (fetchInventory) {
        const itemsSql =
          "SELECT item_name, amount FROM player_inventory_items WHERE discord_id = ?";
        const equipSql =
          "SELECT equipment_id, item_key, is_equipped, data_json FROM player_equipment WHERE discord_id = ?";
        const accessorySql =
          "SELECT accessory_id, item_key, is_equipped, data_json FROM player_accessories WHERE discord_id = ?";
        // Use sequential queries instead of Promise.all to reduce libuv pressure
        inventoryItemRows = await dbAll(itemsSql, [discordId]);
        equipmentRows = await dbAll(equipSql, [discordId]);
        accessoryRows = await dbAll(accessorySql, [discordId]);
      }

      // --- 3. Build the result object based on requested fields ---
      const result = { discordId: playerRow.discord_id };
      const want = (key) => !fields || fields.includes(key);

      // Auto-process all simple (non-JSON) fields from database columns
      const availableFieldMappings = await getFieldMappings();
      for (const [fieldName, columnName] of Object.entries(
        availableFieldMappings
      )) {
        // Skip special cases that are handled separately
        if (
          fieldName === "inventory" ||
          columnName === null ||
          Array.isArray(columnName)
        ) {
          continue;
        }

        // Skip JSON fields - they're handled separately below
        if (columnName.endsWith("_json") && fieldName !== columnName) {
          continue;
        }

        // Process the field if wanted and exists in database row
        if (want(fieldName) && playerRow[columnName] !== undefined) {
          result[fieldName] = playerRow[columnName];
        }
      }

      // Auto-process JSON fields using field mappings
      for (const [fieldName, columnName] of Object.entries(fieldMappings)) {
        // Skip special cases that are handled separately
        if (
          fieldName === "inventory" ||
          columnName === null ||
          Array.isArray(columnName)
        ) {
          continue;
        }

        // Process JSON fields (columns ending with _json or special JSON columns)
        if (
          isJsonColumn(columnName) &&
          want(fieldName) &&
          playerRow[columnName] !== undefined
        ) {
          const defaultValue = getDefaultValueForField(fieldName, columnName);
          let parsedValue = safeJsonParse(playerRow[columnName], defaultValue);

          // Normalize specific fields with historical double-encoding issues
          if (fieldName === "visitor_unique_served") {
            // If parsedValue is a string that looks like JSON, try to parse again
            if (typeof parsedValue === "string") {
              const inner = parsedValue.trim();
              if (
                (inner.startsWith("[") && inner.endsWith("]")) ||
                (inner.startsWith("{") && inner.endsWith("}"))
              ) {
                try {
                  const reparsed = JSON.parse(inner);
                  if (Array.isArray(reparsed)) {
                    parsedValue = reparsed;
                    // Persist normalization back to DB to prevent repeated fixes
                    try {
                      await dbRunQueued(
                        "UPDATE players SET visitor_unique_served = ? WHERE discord_id = ?",
                        [JSON.stringify(parsedValue), discordId]
                      );
                    } catch (normErr) {
                      console.warn(
                        "[getPlayerData] Failed to normalize visitor_unique_served for",
                        discordId,
                        normErr?.message || normErr
                      );
                    }
                  }
                } catch {
                  /* ignore */
                }
              }
            }
            // Heuristic: values like [\"A\"]
            if (
              !Array.isArray(parsedValue) &&
              typeof playerRow[columnName] === "string" &&
              /\\"/.test(playerRow[columnName])
            ) {
              try {
                const cleaned = playerRow[columnName].replace(/\\"/g, '"');
                const reparsed = JSON.parse(cleaned);
                if (Array.isArray(reparsed)) {
                  parsedValue = reparsed;
                  try {
                    await dbRunQueued(
                      "UPDATE players SET visitor_unique_served = ? WHERE discord_id = ?",
                      [JSON.stringify(parsedValue), discordId]
                    );
                  } catch (normErr) {
                    console.warn(
                      "[getPlayerData] Failed to normalize visitor_unique_served (clean path) for",
                      discordId,
                      normErr?.message || normErr
                    );
                  }
                }
              } catch {
                /* ignore */
              }
            }
          }

          // Special handling for pets - ensure pet data consistency
          if (fieldName === "pets" && Array.isArray(parsedValue)) {
            parsedValue = parsedValue.filter(
              (p) => p && typeof p === "object" && p.id
            );
            parsedValue.forEach((pet) => {
              if (pet && typeof pet.xp === "number") {
                if (
                  typeof pet.exp !== "number" ||
                  isNaN(pet.exp) ||
                  pet.exp === 0
                ) {
                  pet.exp = pet.xp;
                }
                delete pet.xp;
              }
            });
          }

          // Special handling for pet_upgrade_json - convert empty objects to null
          if (
            fieldName === "pet_upgrade_json" ||
            fieldName === "pet_upgrade_json"
          ) {
            if (
              !parsedValue ||
              (typeof parsedValue === "object" &&
                Object.keys(parsedValue).length === 0)
            ) {
              parsedValue = null;
            }
          }

          result[fieldName] = parsedValue;
        }
      }

      // Inventory/equipment
      if (fetchInventory) {
        const inventoryItems = {};
        for (const row of inventoryItemRows) {
          inventoryItems[row.item_name] = row.amount;
        }
        const equipment = equipmentRows.map((row) => {
          let dataJson = {};
          if (row.data_json) {
            try {
              dataJson =
                typeof row.data_json === "string"
                  ? JSON.parse(row.data_json)
                  : row.data_json;
            } catch (e) {
              console.error(
                `Error parsing data_json for equipment_id ${row.equipment_id}:`,
                e
              );
              dataJson = {};
            }
          }
          return {
            id: row.equipment_id,
            itemKey: row.item_key,
            isEquipped: Boolean(row.is_equipped),
            data_json: dataJson,
          };
        });

        // Process accessories
        const accessories = accessoryRows.map((row) => {
          let dataJson = {};
          if (row.data_json) {
            try {
              dataJson =
                typeof row.data_json === "string"
                  ? JSON.parse(row.data_json)
                  : row.data_json;
            } catch (e) {
              console.error(
                `Error parsing data_json for accessory_id ${row.accessory_id}:`,
                e
              );
              dataJson = {};
            }
          }
          return {
            id: row.accessory_id,
            itemKey: row.item_key,
            isEquipped: Boolean(row.is_equipped),
            data_json: dataJson,
          };
        });

        result.inventory = { items: inventoryItems, equipment, accessories };
        result.equipment = { equipped: {} };
        result.accessories = { equipped: [], accessories };

        // Populate equipped equipment
        const allItems = require("../utils/configManager").getAllItems();
        equipment.forEach((eq) => {
          if (eq.isEquipped) {
            const itemDef = allItems[eq.itemKey];
            if (itemDef && itemDef.type) {
              const equipmentTypeKey =
                typeof itemDef.type === "string"
                  ? itemDef.type
                  : itemDef.type.name;
              if (equipmentTypeKey) {
                let slotKey = equipmentTypeKey.toUpperCase();

                // Special handling for ARMOR - use subtype for slot
                if (equipmentTypeKey === "ARMOR" && itemDef.subtype) {
                  slotKey = itemDef.subtype.toUpperCase();
                }
                // Special handling for AXE and FARMING_AXE as they can be both a weapon and a tool
                else if (
                  itemDef.subtype === "AXE" ||
                  itemDef.subtype === "FARMING_AXE"
                ) {
                  if (itemDef.type === "TOOL") {
                    slotKey = "AXE"; // Tool axes go in AXE slot
                  } else {
                    slotKey = "WEAPON"; // Weapon axes go in WEAPON slot
                  }
                }

                result.equipment.equipped[slotKey] = eq.itemKey;

                // Special handling for TOOL type - also populate equipment.tool
                if (equipmentTypeKey === "TOOL") {
                  result.equipment.tool = {
                    id: eq.id,
                    itemKey: eq.itemKey,
                    data_json: eq.data_json || "{}",
                  };
                }
              }
            }
          }
        });

        // Populate equipped accessories
        accessories.forEach((acc) => {
          if (acc.isEquipped) {
            result.accessories.equipped.push(acc.id);
          }
        });
      }

      // Derived stats (totalStats) and health clamping only if full fetch
      if (!fields) {
        result.totalStats = calculateAllStats(result);
        const maxHealth = calculateAllStats(result).HEALTH;
        if (typeof result.current_health === "number") {
          result.current_health = Math.min(result.current_health, maxHealth);
        } else {
          result.current_health = maxHealth;
        }
      }

      // Defensive: If active_pet_id is set but not valid, clear it and save the fix
      if (want("pets") && want("active_pet_id")) {
        const validPetIds = new Set(result.pets?.map((p) => p.id));
        if (result.active_pet_id && !validPetIds.has(result.active_pet_id)) {
          console.log(
            `[getPlayerData] active_pet_id does not match any valid pet for ${discordId}. Clearing active_pet_id.`
          );
          result.active_pet_id = null;

          // persist the fix to prevent repeated warnings
          try {
            await dbRunQueued(
              "UPDATE players SET active_pet_id = NULL WHERE discord_id = ?",
              [discordId]
            );
          } catch (dbError) {
            console.error(
              `[getPlayerData] Failed to clear invalid active_pet_id for ${discordId}:`,
              dbError
            );
          }
        }
      }

      return result;
    } catch (err) {
      console.error(
        `[getPlayerData] Error getting player data for ID: ${discordId} (Attempt ${attempt})`,
        err
      );
      if (!retry) {
        retry = true;
        continue;
      }
      return null;
    }
  }
  console.error(
    `[getPlayerData] All attempts failed for discordId: ${discordId}`
  );
  return null;
}

/**
 * Saves player data for a given Discord ID. Optionally, only updates specified fields.
 * @param {string} discordId - The Discord user ID.
 * @param {Object} character - The player data object.
 * @param {string[]|null} [fields] - Optional array of field names to update. If omitted, updates all fields.
 * @returns {Promise<void>}
 */
async function savePlayerData(discordId, character, fields = null) {
  // Ensure field mappings are initialized before any operations
  if (!cachedFieldMappings) {
    console.log(
      `[savePlayerData] Field mappings not cached, initializing for ${discordId}`
    );
    try {
      await initializeMetadataCache();
    } catch (initError) {
      console.warn(
        `[savePlayerData] Failed to initialize metadata cache for ${discordId}:`,
        initError
      );
      // Continue with fallback mappings
    }
  }

  // Clamp currentHealth to max health before saving (only for full saves with complete character data)
  // Skip health clamping for targeted updates to avoid requiring full character object
  if (
    !fields &&
    character &&
    character.stats &&
    character.stats.HEALTH &&
    character.inventory &&
    character.inventory.equipment
  ) {
    try {
      const maxHealth =
        require("./statCalculations").calculateAllStats(character).HEALTH;
      if (typeof character.current_health === "number") {
        character.current_health = Math.min(
          character.current_health,
          maxHealth
        );
      } else {
        character.current_health = maxHealth;
      }
    } catch (statCalcError) {
      console.warn(
        `[savePlayerData] Could not calculate max health for ${discordId}, skipping health clamping:`,
        statCalcError.message
      );
    }
  }

  // synchronize mapped fields before saving (fixes field mapping asymmetry)
  if (!fields) {
    // for full saves, ensure that mapped fields are synchronized
    const fieldMappings = await getFieldMappings();
    for (const [shortFieldName, dbColumnName] of Object.entries(
      fieldMappings
    )) {
      // sync short field to db column for JSON fields
      if (
        shortFieldName !== dbColumnName &&
        dbColumnName && // ensure dbColumnName is not null
        Object.prototype.hasOwnProperty.call(character, shortFieldName) &&
        isJsonColumn(dbColumnName)
      ) {
        // always sync the short field to the db column field before saving
        character[dbColumnName] = character[shortFieldName];
      }
    }
  }

  // Use async/await pattern instead of Promise constructor
  try {
    // Get database columns dynamically
    const dbColumns = await getPlayerTableColumns();
    const fieldMappings = await getFieldMappings();

    const playerExists = await dbGet(
      "SELECT 1 FROM players WHERE discord_id = ?",
      [discordId]
    );

    if (!playerExists) {
      // --- NEW PLAYER LOGIC (DYNAMIC INSERT) ---
      if (fields) {
        throw new Error("Partial insert not supported for new players.");
      }

      console.log(`[Save Data] Creating new player record for ${discordId}`);

      // Build dynamic INSERT statement
      const insertColumns = [];
      const insertParams = [];
      const placeholders = [];

      // Always include discord_id first
      insertColumns.push("discord_id");
      insertParams.push(discordId);
      placeholders.push("?");

      // Process all other columns dynamically
      for (const column of dbColumns) {
        if (column === "discord_id") continue; // Already handled

        // Get the value from character data using field mappings
        let value = null;

        // Check if character has this column directly
        if (Object.prototype.hasOwnProperty.call(character, column)) {
          value = character[column];
        } else {
          // Check field mappings for alternative names (e.g., 'skills' -> 'skills_json')
          for (const [fieldName, mappedColumn] of Object.entries(
            fieldMappings
          )) {
            if (
              mappedColumn === column &&
              Object.prototype.hasOwnProperty.call(character, fieldName)
            ) {
              value = character[fieldName];
              break;
            }
          }
        }

        // Apply default values and JSON serialization
        if (value === null || value === undefined) {
          value = getDefaultValueForField(column, column);
        }

        // Serialize JSON fields
        if (isJsonColumn(column) && value !== null) {
          value = JSON.stringify(value);
        }

        insertColumns.push(column);
        insertParams.push(value);
        placeholders.push("?");
      }

      const insertSql = `INSERT INTO players (${insertColumns.join(", ")}) VALUES (${placeholders.join(", ")})`;

      await dbRunQueued(insertSql, insertParams);
      console.log(
        `[Save Data] Successfully created new player ${character.name} (${discordId})`
      );

      // Handle inventory items and equipment
      const itemPromises = [];
      if (character.inventory && character.inventory.items) {
        for (const [itemKey, amount] of Object.entries(
          character.inventory.items
        )) {
          if (amount > 0) {
            itemPromises.push(
              dbRunQueued(
                "INSERT INTO player_inventory_items (discord_id, item_name, amount) VALUES (?, ?, ?)",
                [discordId, itemKey, amount]
              )
            );
          }
        }
      }
      if (
        character.inventory &&
        character.inventory.equipment &&
        Array.isArray(character.inventory.equipment)
      ) {
        for (const item of character.inventory.equipment) {
          if (item && item.id && item.itemKey) {
            const dataJson = item.data_json
              ? JSON.stringify(item.data_json)
              : "{}";
            itemPromises.push(
              dbRunQueued(
                "INSERT INTO player_equipment (equipment_id, discord_id, item_key, is_equipped, data_json) VALUES (?, ?, ?, ?, ?)",
                [
                  item.id,
                  discordId,
                  item.itemKey,
                  item.isEquipped ? 1 : 0,
                  dataJson,
                ]
              )
            );
          }
        }
      }
      await Promise.all(itemPromises);
    } else {
      // --- EXISTING PLAYER LOGIC (DYNAMIC UPDATE) ---

      if (fields && Array.isArray(fields)) {
        // --- TARGETED UPDATE ---
        const updateFields = [];
        const params = [];
        const fieldsNotFound = [];

        for (const field of fields) {
          const columnMapping = fieldMappings[field];
          if (
            columnMapping &&
            Object.prototype.hasOwnProperty.call(character, field)
          ) {
            const dbField = columnMapping;
            updateFields.push(`${dbField} = ?`);

            if (isJsonColumn(dbField)) {
              const defaultValue = getDefaultValueForField(field, dbField);
              const jsonData = JSON.stringify(character[field] || defaultValue);
              params.push(jsonData);
            } else {
              params.push(character[field]);
            }
          } else {
            fieldsNotFound.push(field);
          }
        }

        if (updateFields.length === 0) {
          if (fieldsNotFound.length > 0) {
            console.warn(
              `[savePlayerData] No valid fields to update for ${discordId}. Missing mappings for: ${fieldsNotFound.join(", ")}`
            );
            console.warn(
              `[savePlayerData] Available character properties: ${Object.keys(character).join(", ")}`
            );
          } else {
            console.warn(
              `[savePlayerData] No valid fields to update for ${discordId} - no matching character properties found`
            );
          }
          return;
        }

        params.push(discordId);
        const sql = `UPDATE players SET ${updateFields.join(", ")} WHERE discord_id = ?`;
        await dbRunQueued(sql, params);
      } else {
        // --- SAFE MERGE UPDATE (EXISTING PLAYER): only update columns explicitly provided ---
        // Build UPDATE from keys present on `character` (either direct column or mapped short field)

        // Optional: clamp health if the caller provided current_health or full stats context
        if (
          (Object.prototype.hasOwnProperty.call(character, "current_health") ||
            !character.current_health === undefined) &&
          character &&
          character.stats &&
          character.inventory &&
          character.inventory.equipment
        ) {
          try {
            const maxHealth =
              require("./statCalculations").calculateAllStats(character).HEALTH;
            if (typeof character.current_health === "number") {
              character.current_health = Math.min(
                character.current_health,
                maxHealth
              );
            }
          } catch (statCalcError) {
            // Best-effort; skip clamping if stats incomplete
          }
        }

        const updateFields = [];
        const params = [];

        // Helper to queue an update for a db column with a JS value
        const queueUpdate = (column, value) => {
          if (column === "discord_id") return;
          // Guard: prevent accidental rename to 'Unknown Player' unless explicitly allowed
          if (
            column === "name" &&
            value === "Unknown Player" &&
            character._allowUnknownName !== true
          ) {
            console.warn(
              `[savePlayerData] Blocked renaming existing player ${discordId} to 'Unknown Player'`
            );
            return;
          }
          if (isJsonColumn(column) && value !== null && value !== undefined) {
            updateFields.push(`${column} = ?`);
            params.push(JSON.stringify(value));
          } else if (value !== undefined) {
            updateFields.push(`${column} = ?`);
            params.push(value);
          }
        };

        // 1) Direct columns provided on the object
        for (const [prop, val] of Object.entries(character)) {
          // Skip internal flags
          if (prop === "_allowUnknownName") continue;
          // Map property to db column if possible
          const mapped =
            fieldMappings[prop] || (dbColumns.includes(prop) ? prop : null);
          if (!mapped) continue;
          queueUpdate(mapped, val);
        }

        if (updateFields.length === 0) {
          // Nothing to update; avoid writing defaults
          console.warn(
            `[savePlayerData] No explicit fields provided to update for existing player ${discordId}; skipping.`
          );
          return;
        }

        params.push(discordId);
        const sql = `UPDATE players SET ${updateFields.join(", ")} WHERE discord_id = ?`;
        await dbRunQueued(sql, params);
      }
    }
  } catch (error) {
    console.error(
      `[Save Data] General error in savePlayerData for ${discordId}:`,
      error
    );
    throw error;
  }
}

// ++ NEW: Calculate max minion slots based on unique crafts ++
const { BASE_MINION_SLOTS, MINION_SLOT_UNLOCKS } = require("../gameConfig");

function calculateMaxMinionSlots(character) {
  const uniqueCraftsCount = character.craftedMinions?.length || 0;
  let bonusSlots = 0;

  // Find the highest bonus tier reached
  for (const tier of MINION_SLOT_UNLOCKS) {
    if (uniqueCraftsCount >= tier.crafts) {
      bonusSlots = tier.bonus;
    } else {
      // Since the array is sorted, we can stop early
      break;
    }
  }

  return BASE_MINION_SLOTS + bonusSlots;
}

// *** ADDED: Function to get player skill level ***
function getPlayerSkillLevel(characterData, skillKey) {
  const keyLower = skillKey.toLowerCase();
  const exp = characterData?.skills?.[keyLower]?.exp || 0;
  return getLevelFromExp(exp).level;
}

/**
 * Recalculates slayer stat bonuses based on current slayer levels
 * @param {Object} character - The character data
 */
function recalculateSlayerStatBonuses(character) {
  // Get slayer levels
  const slayerXpData = character.slayerXp || {};
  const slayerLevels = getAllSlayerLevels(slayerXpData);

  // Reset all slayer bonuses to 0
  for (const statName in character.stats) {
    if (character.stats[statName].fromSlayers !== undefined) {
      character.stats[statName].fromSlayers = 0;
    }
  }

  // Apply slayer bonuses for all slayer types
  const { getStatRewardsForLevel } = require("./slayerRewards");

  // Apply zombie slayer bonuses
  const zombieLevel = slayerLevels.zombie.level;
  for (let level = 1; level <= zombieLevel; level++) {
    const rewards = getStatRewardsForLevel("zombie", level);
    for (const reward of rewards) {
      if (character.stats[reward.stat]) {
        if (character.stats[reward.stat].fromSlayers === undefined) {
          character.stats[reward.stat].fromSlayers = 0;
        }
        character.stats[reward.stat].fromSlayers += reward.value;
      }
    }
  }

  // Apply spider slayer bonuses
  const spiderLevel = slayerLevels.spider.level;
  for (let level = 1; level <= spiderLevel; level++) {
    const rewards = getStatRewardsForLevel("spider", level);
    for (const reward of rewards) {
      if (character.stats[reward.stat]) {
        if (character.stats[reward.stat].fromSlayers === undefined) {
          character.stats[reward.stat].fromSlayers = 0;
        }
        character.stats[reward.stat].fromSlayers += reward.value;
      }
    }
  }
}

// *** ADDED: Function to recalculate and save player stats ***
async function recalculateAndSaveStats(discordId, character) {
  if (!character || !character.stats || !character.inventory?.equipment) {
    console.error(
      `[recalculateAndSaveStats] Invalid character data provided for ${discordId}`
    );
    // Optionally, try to fetch fresh data if character is incomplete
    try {
      character = await getPlayerData(discordId);
      if (!character) throw new Error("Failed to fetch fresh data.");
    } catch (fetchError) {
      console.error(
        `[recalculateAndSaveStats] Failed to fetch fresh character data for ${discordId}:`,
        fetchError
      );
      return; // Cannot proceed
    }
  }

  // Ensure pets data is available for Pet Score calculation
  if (!character.pets) {
    try {
      const petData = await getPlayerData(discordId, ["pets"]);
      if (petData && petData.pets) {
        character.pets = petData.pets;
      } else {
        character.pets = [];
      }
    } catch (petFetchError) {
      console.error(
        `[recalculateAndSaveStats] Failed to fetch pets for ${discordId}:`,
        petFetchError
      );
      character.pets = [];
    }
  }

  const newEquipmentStats = calculateEquipmentStats(
    character.inventory.equipment,
    character
  );
  const newPetStats = calculatePetStats(character);

  // Ensure all base stats exist in the character object
  const defaultStats = getDefaultStats();
  for (const statName in defaultStats) {
    if (!character.stats[statName]) {
      character.stats[statName] = { ...defaultStats[statName] };
      console.log(
        `[recalculateAndSaveStats] Initialized missing stat ${statName} for ${discordId}`
      );
    }
  }

  // Update fromEquipment, fromPet, and fromLevels for all stats
  const skillStats =
    require("./statCalculations").calculateSkillStats(character);

  // Ensure all stats present in skillStats are initialized in character.stats
  for (const statName in skillStats) {
    if (!character.stats[statName]) {
      character.stats[statName] = {
        base: 0,
        fromLevels: 0,
        fromSlayers: 0,
        fromEquipment: 0,
        fromPet: 0,
        fromSetBonus: 0,
        fromAccessories: 0,
        fromMagicalPower: 0,
        fromPetScore: 0,
      };
    }
  }

  // Initialize DAMAGE_MULT if it doesn't exist
  if (!character.stats.DAMAGE_MULT) {
    character.stats.DAMAGE_MULT = {
      base: 0,
      fromLevels: 0,
      fromSlayers: 0,
      fromEquipment: 0,
      fromPet: 0,
      fromSetBonus: 0,
      fromAccessories: 0,
      fromMagicalPower: 0,
      fromPetScore: 0,
    };
  }

  // Update fromEquipment, fromPet, and fromLevels for all stats
  for (const statName in character.stats) {
    const newEquipValue = newEquipmentStats[statName] || 0;
    const newPetValue = newPetStats[statName] || 0;
    const newLevelValue = skillStats[statName] || 0;

    // Always set to 0 if no bonus, to prevent stale values
    character.stats[statName].fromEquipment = newEquipValue;
    character.stats[statName].fromPet = newPetValue;
    character.stats[statName].fromLevels = newLevelValue;

    // Initialize fromSlayers if it doesn't exist (for existing players)
    if (character.stats[statName].fromSlayers === undefined) {
      character.stats[statName].fromSlayers = 0;
    }

    // Initialize fromSetBonus if it doesn't exist (for existing players)
    if (character.stats[statName].fromSetBonus === undefined) {
      character.stats[statName].fromSetBonus = 0;
    }

    // Initialize fromAccessories if it doesn't exist (for existing players)
    if (character.stats[statName].fromAccessories === undefined) {
      character.stats[statName].fromAccessories = 0;
    }

    // Initialize fromMagicalPower if it doesn't exist (for existing players)
    if (character.stats[statName].fromMagicalPower === undefined) {
      character.stats[statName].fromMagicalPower = 0;
    }

    // Initialize fromPetScore if it doesn't exist (for existing players)
    if (character.stats[statName].fromPetScore === undefined) {
      character.stats[statName].fromPetScore = 0;
    }
  }

  // Recalculate slayer stat bonuses for all stats
  recalculateSlayerStatBonuses(character);

  // Apply set bonuses using centralized system
  const setBonuses = require("./setBonuses").getActiveSetBonuses(character);
  const { applySetBonusesToStats } = require("./setBonuses");
  applySetBonusesToStats(character, setBonuses);

  // --- Process Accessory Effects and Base Stats ---
  try {
    const { getPlayerAccessories } = require("./accessoryManager");
    const playerAccessories = await getPlayerAccessories(discordId);
    const equippedAccessories = playerAccessories.filter(
      (acc) => acc.isEquipped
    );

    // Reset all fromAccessories values to 0 first to prevent accumulation
    for (const statName in character.stats) {
      character.stats[statName].fromAccessories = 0;
    }

    // Load item definitions
    const allItems = require("./configManager").getAllItems();

    for (const accessory of equippedAccessories) {
      const itemData = allItems[accessory.itemKey];

      if (itemData && itemData.type === "ACCESSORY") {
        // Process baseStats from equipped accessories
        if (itemData.baseStats) {
          for (const [statName, statValue] of Object.entries(
            itemData.baseStats
          )) {
            if (character.stats[statName]) {
              character.stats[statName].fromAccessories += statValue;
            }
          }
        }

        // Process special effects from equipped accessories
        if (itemData.effects) {
          // Village Affinity Talisman - Hub Health Bonus
          if (
            itemData.effects.hubHealthBonus &&
            character.current_region === "the_hub"
          ) {
            const healthBonus = itemData.effects.hubHealthBonus;
            character.stats.HEALTH.fromAccessories += healthBonus;
          }
        }
      }
    }
  } catch (accessoryError) {
    console.error(
      `[recalculateAndSaveStats] Error processing accessory effects for ${discordId}:`,
      accessoryError
    );
  }
  // --- End Accessory Effects and Base Stats ---

  // --- Process Magical Power Bonuses ---
  try {
    // Reset all fromMagicalPower values to 0 first to prevent accumulation
    for (const statName in character.stats) {
      character.stats[statName].fromMagicalPower = 0;
    }

    // Calculate total magical power from EQUIPPED accessories only
    const { getPlayerAccessories } = require("./accessoryManager");
    const allPlayerAccessories = await getPlayerAccessories(discordId);
    const allItems = require("./configManager").getAllItems();

    let totalMagicalPower = 0;
    const seenAccessories = new Set(); // Track unique accessories to prevent duplicates

    for (const accessory of allPlayerAccessories) {
      // Only count equipped accessories
      if (accessory.isEquipped) {
        const itemData = allItems[accessory.itemKey];
        if (itemData && itemData.type === "ACCESSORY" && itemData.rarity) {
          // Only count each unique accessory type once (prevent duplicates)
          if (!seenAccessories.has(accessory.itemKey)) {
            seenAccessories.add(accessory.itemKey);
            const rarityName =
              typeof itemData.rarity === "string"
                ? itemData.rarity
                : itemData.rarity;
            totalMagicalPower += MAGICAL_POWER_VALUES[rarityName] || 0;
          }
        }
      }
    }

    // Get player's selected accessory power
    const accessoryPower = character.accessory_power;

    if (accessoryPower && totalMagicalPower > 0) {
      // Using shared MAGICAL_POWER_VALUES & ACCESSORY_POWERS from utils/accessoryPowers.js
      const power = ACCESSORY_POWERS[accessoryPower];
      if (power && power.stats) {
        // Apply bonuses for each stat in the selected power
        for (const [statName, statData] of Object.entries(power.stats)) {
          const statBonus = calculateAccessoryStat(
            statData.basePower,
            statData.multiplier,
            totalMagicalPower
          );

          // Initialize stat if it doesn't exist
          if (!character.stats[statName]) {
            character.stats[statName] = {
              base: 0,
              fromLevels: 0,
              fromSlayers: 0,
              fromEquipment: 0,
              fromPet: 0,
              fromSetBonus: 0,
              fromAccessories: 0,
              fromMagicalPower: 0,
              fromPetScore: 0,
            };
          }

          character.stats[statName].fromMagicalPower = statBonus;
        }
      }
    }
  } catch (magicalPowerError) {
    console.error(
      `[recalculateAndSaveStats] Error processing magical power for ${discordId}:`,
      magicalPowerError
    );
  }
  // --- End Magical Power Bonuses ---

  // --- Pet Score-based Magic Find Bonus ---
  try {
    // Reset all fromPetScore values to 0 first to prevent accumulation
    for (const statName in character.stats) {
      character.stats[statName].fromPetScore = 0;
    }

    const {
      calculatePetScore,
      calculateMagicFindFromPetScore,
    } = require("./disblockXpSystem");

    // Calculate current Pet Score
    const petScore = calculatePetScore(character);

    // Calculate Magic Find bonus from Pet Score
    const magicFindBonus = calculateMagicFindFromPetScore(petScore);

    // Initialize MAGIC_FIND stat if it doesn't exist
    if (!character.stats.MAGIC_FIND) {
      character.stats.MAGIC_FIND = {
        base: 0,
        fromLevels: 0,
        fromSlayers: 0,
        fromEquipment: 0,
        fromPet: 0,
        fromSetBonus: 0,
        fromAccessories: 0,
        fromMagicalPower: 0,
        fromPetScore: 0,
      };
    }

    // Apply Pet Score-based Magic Find bonus
    character.stats.MAGIC_FIND.fromPetScore = magicFindBonus;
  } catch (petScoreError) {
    console.error(
      `[recalculateAndSaveStats] Error processing pet score magic find for ${discordId}:`,
      petScoreError
    );
  }
  // --- End Pet Score-based Magic Find ---

  // --- Ensure set bonuses and all derived stats are recalculated ---
  character.totalStats = calculateAllStats(character);

  // Apply Ender Armor set bonus (doubles all stats from Ender Armor pieces when in The End)
  const originalTotalStats = { ...character.totalStats };
  character.totalStats = applyEnderArmorBonus(character, character.totalStats);

  // Update character.stats.fromSetBonus to reflect the Ender Armor bonus for proper display in /stats
  // Only apply if character has Ender Armor pieces and is in The End
  const hasEnderArmor = character?.inventory?.equipment?.some(
    (eq) => eq.isEquipped && eq.itemKey.startsWith("ENDER_ARMOR_")
  );
  const inTheEnd = character.current_region === "the_end";

  if (hasEnderArmor && inTheEnd) {
    for (const statName in character.totalStats) {
      if (character.totalStats[statName] > originalTotalStats[statName]) {
        const enderBonus =
          character.totalStats[statName] - originalTotalStats[statName];
        if (character.stats[statName]) {
          character.stats[statName].fromSetBonus =
            (character.stats[statName].fromSetBonus || 0) + enderBonus;
        }
      }
    }
  }

  // --- Explicitly set fromLevels for fortune stats to prevent correction loop ---
  if (character.skills.farming && character.stats.FARMING_FORTUNE) {
    const farmingLevel = getLevelFromExp(
      character.skills.farming.exp || 0
    ).level;
    // Fortune rewards start at level 1, so level 0 = 0 fortune, level 1 = 4 fortune, etc.
    character.stats.FARMING_FORTUNE.fromLevels =
      farmingLevel >= 1 ? farmingLevel * 4 : 0;
  }
  if (character.skills.mining && character.stats.MINING_FORTUNE) {
    const miningLevel = getLevelFromExp(character.skills.mining.exp || 0).level;
    // Fortune rewards start at level 1, so level 0 = 0 fortune, level 1 = 4 fortune, etc.
    character.stats.MINING_FORTUNE.fromLevels =
      miningLevel >= 1 ? miningLevel * 4 : 0;
  }
  if (character.skills.foraging && character.stats.FORAGING_FORTUNE) {
    const foragingLevel = getLevelFromExp(
      character.skills.foraging.exp || 0
    ).level;
    // Fortune rewards start at level 1, so level 0 = 0 fortune, level 1 = 4 fortune, etc.
    character.stats.FORAGING_FORTUNE.fromLevels =
      foragingLevel >= 1 ? foragingLevel * 4 : 0;
  }

  // Clamp currentHealth to new max health after stat recalculation
  const maxHealth = character.totalStats.HEALTH;
  if (typeof character.current_health === "number") {
    character.current_health = Math.min(character.current_health, maxHealth);
  } else {
    character.current_health = maxHealth;
  }

  // Persist recalculated stats to the database (use targeted update to avoid overwriting concurrent changes)
  const updateSql = `
        UPDATE players SET
            stats_json = ?,
            current_health = ?,
            active_pet_id = ?
        WHERE discord_id = ?
    `;
  await dbRunQueued(updateSql, [
    JSON.stringify(character.stats || {}),
    character.current_health || 100,
    character.active_pet_id || null,
    discordId,
  ]);
}

// ++ NEW: Atomic purchase pet function ++
async function purchasePet(discordId, petInstance, cost) {
  try {
    const result = await dbGet(
      "SELECT coins, pets_json FROM players WHERE discord_id = ? AND coins >= ?",
      [discordId, cost]
    );
    if (!result) {
      throw new Error("Insufficient coins");
    }

    // Parse existing pets, add new one
    let pets = safeJsonParse(result.pets_json, []);
    if (!Array.isArray(pets)) pets = [];
    pets.push(petInstance);

    // Update both coins and pets atomically
    await dbRunQueued(
      "UPDATE players SET coins = coins - ?, pets_json = ? WHERE discord_id = ?",
      [cost, JSON.stringify(pets), discordId]
    );

    return true;
  } catch (error) {
    console.error(`[purchasePet] Error for ${discordId}:`, error);
    throw error;
  }
}

/**
 * Atomically updates a player's coins and bank balance with enhanced security
 * @param {string} discordId - The Discord ID of the player
 * @param {number} coinsDelta - Amount to change in purse (positive or negative)
 * @param {number} bankDelta - Amount to change in bank (positive or negative)
 * @returns {Promise<{coins: number, bank: number}|null>} New balances or null if update failed
 */
async function updateCurrencyAtomically(discordId, coinsDelta, bankDelta) {
  try {
    // 1. Basic validation
    if (!discordId || typeof discordId !== "string") {
      throw new Error("Invalid discordId provided");
    }
    if (typeof coinsDelta !== "number" || !Number.isFinite(coinsDelta)) {
      throw new Error("coinsDelta must be a finite number");
    }
    if (typeof bankDelta !== "number" || !Number.isFinite(bankDelta)) {
      throw new Error("bankDelta must be a finite number");
    }

    const MAX_TRANSACTION = 1_000_000_000; // 1 B coins safety cap
    if (
      Math.abs(coinsDelta) > MAX_TRANSACTION ||
      Math.abs(bankDelta) > MAX_TRANSACTION
    ) {
      console.error(
        `[updateCurrencyAtomically] Suspicious large transaction blocked for ${discordId}: coins=${coinsDelta}, bank=${bankDelta}`
      );
      return null;
    }

    // 2. Single-statement, guarded update: add deltas only if resulting balances stay non-negative
    const updateResult = await dbRunQueued(
      `UPDATE players
         SET coins = coins + ?,
             bank  = bank  + ?
       WHERE discord_id = ?
         AND coins + ? >= 0
         AND bank  + ? >= 0`,
      [coinsDelta, bankDelta, discordId, coinsDelta, bankDelta]
    );

    if (updateResult.changes === 0) {
      // Either player not found or insufficient funds
      console.warn(
        `[updateCurrencyAtomically] Transaction aborted for ${discordId}: insufficient funds or row locked.`
      );
      return null;
    }

    // 3. Retrieve and return the new balances (small extra read)
    const updated = await dbGet(
      "SELECT coins, bank FROM players WHERE discord_id = ?",
      [discordId]
    );

    return updated || null;
  } catch (error) {
    console.error(`[updateCurrencyAtomically] Error for ${discordId}:`, error);
    return null;
  }
}

/**
 * Atomically updates a player's gems balance (premium currency)
 * @param {string} discordId - The Discord ID of the player
 * @param {number} gemsDelta - Amount to change in gems (positive or negative)
 * @returns {Promise<{gems: number}|null>} New gems balance or null if update failed
 */
async function updateGemsAtomically(discordId, gemsDelta) {
  try {
    // 1. Basic validation
    if (!discordId || typeof discordId !== "string") {
      throw new Error("Invalid discordId provided");
    }
    if (typeof gemsDelta !== "number" || !Number.isFinite(gemsDelta)) {
      throw new Error("gemsDelta must be a finite number");
    }

    const MAX_GEMS_TRANSACTION = 100_000; // reasonable cap for gems
    if (Math.abs(gemsDelta) > MAX_GEMS_TRANSACTION) {
      console.error(
        `[updateGemsAtomically] Suspicious large gems transaction blocked for ${discordId}: gems=${gemsDelta}`
      );
      return null;
    }

    // 2. Check if player exists, if not create minimal record for gem purchases
    const playerExists = await dbGet(
      "SELECT 1 FROM players WHERE discord_id = ?",
      [discordId]
    );

    if (!playerExists && gemsDelta > 0) {
      console.log(
        `[updateGemsAtomically] Player ${discordId} doesn't exist, creating minimal player record for gem purchase`
      );

      // Create minimal player record for gem purchase
      try {
        await dbRunQueued(
          `INSERT INTO players (discord_id, name, gems, coins, health, max_health, damage, defense, intelligence, strength, crit_chance, crit_damage, health_regen, vitality, dodge_chance, fishing_speed, 
           mining_speed, mining_fortune, foraging_speed, foraging_fortune, combat_xp, mining_xp, foraging_xp, fishing_xp, farming_xp, alchemy_xp, enchanting_xp, 
           combat_level, mining_level, foraging_level, fishing_level, farming_level, alchemy_level, enchanting_level, current_region, x, y, last_activity) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            discordId,
            "New Player", // default name
            gemsDelta, // start with purchased gems
            0, // coins
            100,
            100, // health, max_health
            5,
            0,
            100,
            0, // damage, defense, intelligence, strength
            30,
            50,
            100,
            100,
            0, // crit_chance, crit_damage, health_regen, vitality, dodge_chance
            0,
            0,
            0,
            0,
            0, // fishing_speed, mining_speed, mining_fortune, foraging_speed, foraging_fortune
            0,
            0,
            0,
            0,
            0,
            0,
            0, // combat_xp, mining_xp, foraging_xp, fishing_xp, farming_xp, alchemy_xp, enchanting_xp
            1,
            1,
            1,
            1,
            1,
            1,
            1, // combat_level, mining_level, foraging_level, fishing_level, farming_level, alchemy_level, enchanting_level
            "Emerald_Grove",
            0,
            0, // current_region, x, y
            Date.now(), // last_activity
          ]
        );

        // Return the newly created player's gem data
        return { gems: gemsDelta };
      } catch (insertError) {
        console.error(
          `[updateGemsAtomically] Failed to create player record for ${discordId}:`,
          insertError
        );
        return null;
      }
    }

    // 3. Single-statement, guarded update: add delta only if resulting balance stays non-negative
    const updateResult = await dbRunQueued(
      `UPDATE players
         SET gems = gems + ?
       WHERE discord_id = ?
         AND gems + ? >= 0`,
      [gemsDelta, discordId, gemsDelta]
    );

    if (updateResult.changes === 0) {
      // Either player not found or insufficient gems
      console.warn(
        `[updateGemsAtomically] Transaction aborted for ${discordId}: insufficient gems or player not found.`
      );
      return null;
    }

    // 4. Retrieve and return the new balance
    const updated = await dbGet(
      "SELECT gems FROM players WHERE discord_id = ?",
      [discordId]
    );

    return updated || null;
  } catch (error) {
    console.error(`[updateGemsAtomically] Error for ${discordId}:`, error);
    return null;
  }
}

/**
 * Atomically updates a player's bits balance (earned from booster cookies)
 * this mirrors updateGemsAtomically but for the non-premium bits currency.
 * @param {string} discordId - player discord id
 * @param {number} bitsDelta - positive to add bits, negative to spend bits
 * @returns {Promise<{bits: number}|null>} new bits balance or null if failed
 */
async function updateBitsAtomically(discordId, bitsDelta) {
  try {
    if (!discordId || typeof discordId !== "string") {
      throw new Error("Invalid discordId provided");
    }
    if (typeof bitsDelta !== "number" || !Number.isFinite(bitsDelta)) {
      throw new Error("bitsDelta must be a finite number");
    }

    // apply a sane cap to prevent accidental huge writes (can adjust later)
    const MAX_BITS_TRANSACTION = 5_000_000; // 5m per atomic op
    if (Math.abs(bitsDelta) > MAX_BITS_TRANSACTION) {
      console.error(
        `[updateBitsAtomically] suspicious large bits transaction blocked for ${discordId}: bits=${bitsDelta}`
      );
      return null;
    }

    // guarded update: ensure non-negative result
    const updateResult = await dbRunQueued(
      `UPDATE players
         SET bits = COALESCE(bits,0) + ?
       WHERE discord_id = ?
         AND COALESCE(bits,0) + ? >= 0`,
      [bitsDelta, discordId, bitsDelta]
    );

    if (updateResult.changes === 0) {
      console.warn(
        `[updateBitsAtomically] transaction aborted for ${discordId}: insufficient bits or player not found.`
      );
      return null;
    }

    const updated = await dbGet(
      "SELECT bits FROM players WHERE discord_id = ?",
      [discordId]
    );
    return updated || null;
  } catch (error) {
    console.error(`[updateBitsAtomically] Error for ${discordId}:`, error);
    return null;
  }
}

/**
 * Atomically updates only skill-related player data to avoid overwriting concurrent changes
 * NOTE: This function deliberately excludes coins and bank to prevent race conditions with
 * updateInventoryAtomically. Coins should only be updated through updateInventoryAtomically.
 * @param {string} discordId - The Discord ID of the player
 * @param {object} characterData - Character data with updated skill information
 */
async function updatePlayerSkillDataAtomically(discordId, characterData) {
  try {
    // Removed verbose collection logging for routine saves

    const updateSql = `
            UPDATE players SET
                skills_json = ?,
                stats_json = ?,
                collections_json = ?,
                pets_json = ?,
                active_pet_id = ?,
                current_health = ?,
                mob_kills_json = ?
            WHERE discord_id = ?
        `;

    const updateResult = await dbRunQueued(updateSql, [
      JSON.stringify(characterData.skills || {}),
      JSON.stringify(characterData.stats || {}),
      JSON.stringify(characterData.collections || {}),
      JSON.stringify(characterData.pets || []),
      characterData.active_pet_id || null,
      characterData.current_health || 100,
      JSON.stringify(characterData.mobKills || {}),
      discordId,
    ]);

    if (updateResult.changes === 0) {
      console.warn(
        `[updatePlayerSkillDataAtomically] No rows updated for ${discordId}`
      );
    }
    // Removed verbose success logging for routine database updates
  } catch (error) {
    console.error(
      `[updatePlayerSkillDataAtomically] Error for ${discordId}:`,
      error
    );
    throw error;
  }
}

/**
 * Atomically updates a single setting in the player's settings_json field
 * This prevents race conditions when multiple operations try to update settings simultaneously
 * @param {string} discordId - The Discord ID of the player
 * @param {string} settingKey - The key of the setting to update
 * @param {any} settingValue - The value to set for the setting
 * @returns {Promise<void>}
 */
async function updatePlayerSetting(discordId, settingKey, settingValue) {
  return new Promise((resolve, reject) => {
    // Use a direct SQL update with JSON functions to atomically merge the setting
    // Convert the value to proper JSON representation to ensure correct storage
    const jsonValue = JSON.stringify(settingValue);

    const sql = `
      UPDATE players 
      SET settings_json = CASE 
        WHEN settings_json IS NULL OR settings_json = '' THEN 
          json_object(?, json(?))
        ELSE 
          json_set(settings_json, '$.' || ?, json(?))
      END
      WHERE discord_id = ?
    `;

    const params = [settingKey, jsonValue, settingKey, jsonValue, discordId];

    dbRunQueued(sql, params)
      .then(() => {
        console.log(
          `[updatePlayerSetting] Successfully updated ${settingKey} for ${discordId}`
        );
        resolve();
      })
      .catch((error) => {
        console.error(
          `[updatePlayerSetting] Error updating ${settingKey} for ${discordId}:`,
          error
        );
        reject(error);
      });
  });
}

/**
 * Atomically updates specific JSON fields for a player
 * Only updates the fields that are provided, leaves others unchanged
 * @param {string} discordId - The Discord ID of the player
 * @param {Object} updates - Object containing the fields to update
 * @param {Object} [updates.collections] - Collections data to update
 * @param {Array} [updates.pets] - Pets data to update
 * @param {Object} [updates.minionStorage] - Minion storage data to update
 * @param {Object} [updates.skills] - Skills data to update
 * @param {Object} [updates.stats] - Stats data to update
 * @param {Object} [updates.mobKills] - Mob kills data to update
 * @returns {Promise<void>}
 */
async function updatePlayerJsonFields(discordId, updates) {
  try {
    // build dynamic SQL based on which fields are provided
    const setParts = [];
    const params = [];

    if (updates.collections !== undefined) {
      setParts.push("collections_json = ?");
      params.push(JSON.stringify(updates.collections));
    }

    if (updates.pets !== undefined) {
      setParts.push("pets_json = ?");
      params.push(JSON.stringify(updates.pets));
    }

    if (updates.minionStorage !== undefined) {
      setParts.push("minion_storage_json = ?");
      params.push(JSON.stringify(updates.minionStorage));
    }

    if (updates.skills !== undefined) {
      setParts.push("skills_json = ?");
      params.push(JSON.stringify(updates.skills));
    }

    if (updates.stats !== undefined) {
      setParts.push("stats_json = ?");
      params.push(JSON.stringify(updates.stats));
    }

    if (updates.mobKills !== undefined) {
      setParts.push("mob_kills_json = ?");
      params.push(JSON.stringify(updates.mobKills));
    }

    // Support Crop Upgrades JSON
    if (updates.garden_crop_upgrades !== undefined) {
      setParts.push("garden_crop_upgrades_json = ?");
      params.push(JSON.stringify(updates.garden_crop_upgrades));
    }

    if (updates.active_pet_id !== undefined) {
      setParts.push("active_pet_id = ?");
      params.push(updates.active_pet_id);
    }

    if (updates.current_health !== undefined) {
      setParts.push("current_health = ?");
      params.push(updates.current_health);
    }

    if (setParts.length === 0) {
      console.warn(
        `[updatePlayerJsonFields] No valid fields provided for ${discordId}`
      );
      return;
    }

    // add discord_id to params for WHERE clause
    params.push(discordId);

    const updateSql = `UPDATE players SET ${setParts.join(", ")} WHERE discord_id = ?`;

    const updateResult = await dbRunQueued(updateSql, params);

    if (updateResult.changes === 0) {
      console.warn(`[updatePlayerJsonFields] No rows updated for ${discordId}`);
    }
  } catch (error) {
    console.error(`[updatePlayerJsonFields] Error for ${discordId}:`, error);
    throw error;
  }
}

/**
 * Clear metadata cache (useful after schema migrations)
 */
function clearMetadataCache() {
  cachedColumns = null;
  cachedFieldMappings = null;
  cacheInitialized = false;
  console.log("[PlayerDataManager] Metadata cache cleared");
}

module.exports = {
  getPlayerData,
  savePlayerData,
  safeJsonParse,
  calculateMaxMinionSlots,
  getPlayerSkillLevel,
  getAllPlayerIds,
  recalculateAndSaveStats,
  purchasePet, // Add new export
  updateCurrencyAtomically,
  updateGemsAtomically, // Add gems currency function
  updateBitsAtomically, // Add bits currency function
  updatePlayerSkillDataAtomically,
  updatePlayerJsonFields, // Add new targeted update function
  updatePlayerSetting,
  initializeFieldMappings, // Add initialization function
  initializeMetadataCache, // Add cache initialization function
  clearMetadataCache, // Add cache clearing function
  clearFieldMappingsCache, // Add field mappings cache clearing function
  getFieldMappings, // Add for debugging
  getPlayerTableColumns, // Add for testing and debugging
  getDefaultValueForField, // Add for testing and debugging
  isJsonColumn, // Add for testing and debugging
};

// Field mappings will be initialized explicitly during bot startup
