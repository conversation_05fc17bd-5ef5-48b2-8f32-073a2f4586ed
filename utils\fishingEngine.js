// Fishing Engine - Handles all fishing-specific logic including sea creature encounters
const configManager = require("./configManager");

const { calculateTotalStat } = require("./statCalculations");
const { getLevelFromExp } = require("./expFunctions");
const { calculateInstanceStats } = require("./mobUtils");
const {
  runTurnBasedFight,
  calculateRewardsFromMob,
} = require("./combatEngine");

// --- Sea Creature Helpers ---
function getAvailableWaterSeaCreatures(fishingLevel) {
  return Object.values(configManager.getAllMobs()).filter(
    (mob) =>
      mob.seaCreature &&
      mob.waterFishable &&
      fishingLevel >= (mob.fishingLevelReq || 1)
  );
}

function rollSeaCreature(fishingLevel) {
  const available = getAvailableWaterSeaCreatures(fishingLevel);
  if (!available.length) return null;
  const totalWeight = available.reduce(
    (sum, mob) => sum + (mob.weight || 1),
    0
  );
  let roll = Math.random() * totalWeight;
  for (const mob of available) {
    if (roll < (mob.weight || 1)) return mob;
    roll -= mob.weight || 1;
  }
  return null;
}

async function getSeaCreatureChance(character, userId = null) {
  let baseChance = calculateTotalStat(character.stats.SEA_CREATURE_CHANCE || 0);

  // Check for Angler enchantment on equipped fishing rod
  if (userId) {
    try {
      const { getEquippedFishingRod } = require("./equipmentUtils");
      const fishingRod = await getEquippedFishingRod(userId);
      if (fishingRod && fishingRod.data_json) {
        const rodData = JSON.parse(fishingRod.data_json);
        if (rodData.enchantments && rodData.enchantments.ANGLER) {
          const anglerLevel = rodData.enchantments.ANGLER;
          baseChance += anglerLevel; // +1% per level
        }
      }
    } catch (error) {
      console.error("[FISHING] Error checking Angler enchantment:", error);
    }
  }

  return baseChance;
}

function getTreasureChance(character) {
  return calculateTotalStat(character.stats.TREASURE_CHANCE || 0);
}

// --- Treasure System ---
function weightedRandom(pool) {
  const totalWeight = pool.reduce((sum, item) => sum + item.weight, 0);
  let roll = Math.random() * totalWeight;
  for (const item of pool) {
    if (roll < item.weight) {
      const amount =
        typeof item.amount === "function" ? item.amount() : item.amount;
      if (item.rarity) {
        return {
          itemKey: item.itemKey,
          amount,
          exp: item.exp,
          rarity: item.rarity,
          isCurrency: item.isCurrency,
          isPet: item.isPet,
        };
      }
      return {
        itemKey: item.itemKey,
        amount,
        exp: item.exp,
        isCurrency: item.isCurrency,
        isPet: item.isPet,
      };
    }
    roll -= item.weight;
  }
  return { itemKey: pool[0].itemKey, amount: 1, exp: pool[0].exp };
}

function rollTreasure() {
  const treasurePools = {
    goodCatch: [
      {
        itemKey: "COINS",
        amount: () => Math.floor(Math.random() * 25001) + 25000,
        exp: 10,
        weight: 100,
        isCurrency: true,
      },
      { itemKey: "PRISMARINE_CRYSTALS", amount: 8, exp: 12, weight: 50 },
      { itemKey: "PRISMARINE_SHARD", amount: 8, exp: 12, weight: 50 },
      { itemKey: "PUFFERFISH", amount: 12, exp: 20, weight: 80 },
      { itemKey: "SPONGE", amount: 16, exp: 20, weight: 100 },
      { itemKey: "CLAY_BALL", amount: 32, exp: 20, weight: 100 },
      { itemKey: "CLOWNFISH", amount: 12, exp: 20, weight: 100 },
      { itemKey: "RAW_SALMON", amount: 16, exp: 20, weight: 150 },
      { itemKey: "RAW_FISH", amount: 16, exp: 20, weight: 220 },
    ],
    greatCatch: [
      {
        itemKey: "COINS",
        amount: () => Math.floor(Math.random() * 150001) + 100000,
        exp: 50,
        weight: 100,
        isCurrency: true,
      },
      {
        itemKey: "SQUID_PET",
        rarity: "RARE",
        amount: 1,
        exp: 100,
        weight: 25,
        isPet: true,
      },
      {
        itemKey: "SQUID_PET",
        rarity: "UNCOMMON",
        amount: 1,
        exp: 80,
        weight: 50,
        isPet: true,
      },
      {
        itemKey: "SQUID_PET",
        rarity: "COMMON",
        amount: 1,
        exp: 60,
        weight: 75,
        isPet: true,
      },
      { itemKey: "ENCHANTED_SPONGE", amount: 1, exp: 50, weight: 100 },
      { itemKey: "ENCHANTED_CLAY", amount: 8, exp: 50, weight: 100 },
      { itemKey: "ENCHANTED_PUFFERFISH", amount: 3, exp: 50, weight: 100 },
      { itemKey: "ENCHANTED_CLOWNFISH", amount: 3, exp: 50, weight: 100 },
      { itemKey: "ENCHANTED_RAW_SALMON", amount: 4, exp: 75, weight: 150 },
      { itemKey: "ENCHANTED_RAW_FISH", amount: 4, exp: 75, weight: 220 },
    ],
    outstandingCatch: [
      {
        itemKey: "COINS",
        amount: () => Math.floor(Math.random() * 500001) + 500000,
        exp: 200,
        weight: 100,
        isCurrency: true,
      },
      {
        itemKey: "SQUID_PET",
        rarity: "LEGENDARY",
        amount: 1,
        exp: 300,
        weight: 100,
        isPet: true,
      },
      {
        itemKey: "SQUID_PET",
        rarity: "EPIC",
        amount: 1,
        exp: 250,
        weight: 200,
        isPet: true,
      },
      { itemKey: "ENCHANTED_SPONGE", amount: 8, exp: 200, weight: 100 },
      { itemKey: "ENCHANTED_CLAY", amount: 64, exp: 200, weight: 100 },
      { itemKey: "ENCHANTED_PUFFERFISH", amount: 24, exp: 200, weight: 100 },
      { itemKey: "ENCHANTED_CLOWNFISH", amount: 24, exp: 200, weight: 100 },
      { itemKey: "ENCHANTED_RAW_SALMON", amount: 32, exp: 250, weight: 150 },
      { itemKey: "ENCHANTED_RAW_FISH", amount: 32, exp: 250, weight: 220 },
    ],
  };

  const poolRoll = Math.random();
  let selectedPool;
  if (poolRoll < 0.89) {
    selectedPool = treasurePools.goodCatch;
  } else if (poolRoll < 0.99) {
    selectedPool = treasurePools.greatCatch;
  } else {
    selectedPool = treasurePools.outstandingCatch;
  }

  return weightedRandom(selectedPool);
}

// --- Main Fishing Encounter Logic ---
/**
 * Main fishing encounter logic for the fishing system.
 * Handles sea creature, treasure, and normal fish outcomes.
 */
async function runFishingEncounter(
  character,
  interaction,
  preCalculatedStats = null
) {
  const fishingExp = character.skills?.fishing?.exp || 0;
  const fishingLevel = getLevelFromExp(fishingExp).level;
  const userId = interaction?.user?.id || interaction?.userId;

  // Use pre-calculated stats if available, otherwise calculate (fallback)
  const seaCreatureChance =
    preCalculatedStats?.seaCreatureChance ??
    (await getSeaCreatureChance(character, userId));
  const treasureChance =
    preCalculatedStats?.treasureChance ?? getTreasureChance(character);
  // fishingWisdom removed - now handled centrally in addSkillExp function

  // 1. Roll for sea creature
  const seaCreatureRoll = Math.random();
  if (seaCreatureRoll < seaCreatureChance / 100) {
    if (!interaction) {
      // If we can't show the battle, skip the sea creature encounter entirely
      // Proceed to treasure/normal fish logic below
    } else {
      const seaCreature = rollSeaCreature(fishingLevel);
      if (seaCreature) {
        // Fight sea creature using Combat Engine directly
        const instanceLevel = seaCreature.level;
        const instanceStats = calculateInstanceStats(seaCreature.baseStats);

        // Note: Frail enchantment effect is now handled in the combat engine

        const mobInstanceData = {
          mobKey: seaCreature.key,
          name: seaCreature.name,
          level: instanceLevel,
          stats: instanceStats,
          baseExp: seaCreature.baseExp,
          baseCoins: seaCreature.baseCoins,
          loot: seaCreature.loot,
          color: seaCreature.color,
          emoji: seaCreature.emoji,
        };

        // Run the turn-based fight using shared CombatEngine with pre-calculated stats
        const fightResult = await runTurnBasedFight({
          interaction,
          mobInstanceData,
          character,
          currentFightNum: 1,
          totalFights: 1,
          restoreState: null,
          actionId: interaction?.actionId,
          isSeaCreatureSubCombat: true,
          preCalculatedStats: preCalculatedStats?.allStats,
          messageId: interaction?.messageId,
          channel: interaction?.channel,
        });

        // Check if player fled (stopped the action) or was defeated
        if (!fightResult.victory) {
          if (fightResult.fled) {
            // Player stopped the action - treat as cancelled encounter, not defeat
            return {
              items: [],
              exp: 0,
              coins: 0,
              defeated: false, // Not defeated, just cancelled
              fled: true, // Mark as fled to prevent penalties
              finalCharacterState: fightResult.finalCharacterState,
              seaCreatureKey: seaCreature.key,
              mobName: seaCreature.name,
              mobEmoji: seaCreature.emoji,
            };
          } else {
            // Player was actually defeated in combat - use unified death system
            const {
              processPlayerDeath,
              createSkillDeathResult,
            } = require("./deathSystem");

            // First process the actual death penalty
            await processPlayerDeath(
              interaction?.user?.id || interaction?.userId,
              fightResult.finalCharacterState,
              {
                name: seaCreature.name,
                emoji: seaCreature.emoji,
              },
              "sea_creature"
            );

            // Then return the formatted result for the skill system
            return createSkillDeathResult(
              seaCreature,
              fightResult.finalCharacterState
            );
          }
        } else {
          // Sea creature defeated - calculate rewards for the fishing system to apply
          const rewards = await calculateRewardsFromMob(
            mobInstanceData,
            fightResult.finalCharacterState
          );
          const baseExp = seaCreature.baseExp?.amount || 0;

          // Fishing wisdom multiplier now applied centrally in addSkillExp function
          const exp = baseExp;

          return {
            droppedItems: rewards.droppedItems || [], // Unified drops array
            coins: rewards.coins,
            exp,
            seaCreatureKey: seaCreature.key,
            mobName: seaCreature.name, // For "Defeated:" section
            mobEmoji: seaCreature.emoji, // For "Defeated:" section
            defeated: false, // Player won this encounter
            finalCharacterState: fightResult.finalCharacterState, // Pass back the character state after combat
          };
        }
      }
    }
  }

  // 2. Roll for treasure
  const treasureRoll = Math.random();
  if (treasureRoll < treasureChance / 100) {
    const treasure = rollTreasure();
    if (treasure) {
      // Fishing wisdom multiplier now applied centrally in addSkillExp function
      const treasureExp = treasure.exp || 0;

      // Check if it's a currency reward (coins)
      if (treasure.isCurrency && treasure.itemKey === "COINS") {
        return {
          items: [],
          coins: treasure.amount, // Return as coins for purse, not as items
          exp: treasureExp,
        };
      }
      // Check if it's a pet item
      else if (treasure.isPet && treasure.rarity) {
        const { createPetInstance } = require("./petUtils");
        const petInstance = createPetInstance(
          treasure.itemKey,
          treasure.rarity
        );

        if (petInstance) {
          return {
            items: [],
            exp: treasureExp,
            petToAdd: petInstance, // Return the pet instance to be added by the handler
          };
        } else {
          console.error(
            `[Fishing] Failed to create pet instance for ${treasure.itemKey} with rarity ${treasure.rarity}`
          );
          // Fallback to returning as a regular item if pet creation fails
          return {
            items: [{ itemKey: treasure.itemKey, amount: treasure.amount }],
            exp: treasureExp,
          };
        }
      }
      // Regular item
      else {
        return {
          items: [{ itemKey: treasure.itemKey, amount: treasure.amount }],
          exp: treasureExp,
        };
      }
    }
  }

  // 3. Normal fish
  const normalFishPool = [
    { itemKey: "PUFFERFISH", amount: 1, exp: 10, weight: 80 },
    { itemKey: "CLOWNFISH", amount: 1, exp: 10, weight: 100 },
    { itemKey: "RAW_SALMON", amount: 1, exp: 10, weight: 150 },
    { itemKey: "RAW_FISH", amount: 1, exp: 10, weight: 220 },
  ];
  const normalFish = weightedRandom(normalFishPool);
  // Fishing wisdom multiplier now applied centrally in addSkillExp function
  const totalExp = normalFish.exp || 0;
  const finalResult = {
    items: [{ itemKey: normalFish.itemKey, amount: normalFish.amount }],
    exp: totalExp,
    coins: 0, // Explicitly return 0 coins for normal fishing
  };
  return finalResult;
}

module.exports = {
  getAvailableWaterSeaCreatures,
  rollSeaCreature,
  getSeaCreatureChance,
  getTreasureChance,
  weightedRandom,
  rollTreasure,
  runFishingEncounter,
};
