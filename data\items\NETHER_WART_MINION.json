{"name": "<PERSON><PERSON> Wart Minion", "emoji": "<:minion_netherwart:1375851950637191178>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "resourceItemKey": "NETHER_WART", "recipes": [{"ingredients": [{"itemKey": "NETHER_WART", "amount": 128}]}], "craftingRequirements": {"collections": {"NETHER_WART": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 50, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 50, "maxStorage": 192, "upgradeCost": [{"itemKey": "NETHER_WART", "amount": 256}]}, {"tier": 3, "generationIntervalSeconds": 45, "maxStorage": 192, "upgradeCost": [{"itemKey": "NETHER_WART", "amount": 512}]}, {"tier": 4, "generationIntervalSeconds": 45, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_NETHER_WART", "amount": 8}]}, {"tier": 5, "generationIntervalSeconds": 40, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_NETHER_WART", "amount": 24}]}, {"tier": 6, "generationIntervalSeconds": 40, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_NETHER_WART", "amount": 64}]}, {"tier": 7, "generationIntervalSeconds": 35, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_NETHER_WART", "amount": 128}]}, {"tier": 8, "generationIntervalSeconds": 35, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_NETHER_WART", "amount": 256}]}, {"tier": 9, "generationIntervalSeconds": 30, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_NETHER_WART", "amount": 512}]}, {"tier": 10, "generationIntervalSeconds": 30, "maxStorage": 960, "upgradeCost": [{"itemKey": "MUTANT_NETHER_WART", "amount": 8}]}, {"tier": 11, "generationIntervalSeconds": 25, "maxStorage": 960, "upgradeCost": [{"itemKey": "MUTANT_NETHER_WART", "amount": 16}]}], "drops": [{"itemKey": "NETHER_WART", "chance": 1, "min": 2, "max": 3}]}