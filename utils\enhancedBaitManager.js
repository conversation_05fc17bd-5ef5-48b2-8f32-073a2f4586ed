const { getInventory, updateInventoryAtomically } = require("./inventory");
const { getPlayerData } = require("./playerDataManager");

// List of bait items in order of preference (highest to lowest priority)
const BAIT_PRIORITIES = [
  { key: "FISH_BAIT", fishingSpeed: 45 },
  { key: "MINNOW_BAIT", fishingSpeed: 25 },
];

/**
 * Enhanced bait manager that supports multiple bait types and auto-switching
 */
class EnhancedBaitManager {
  constructor() {
    this.baitCache = new Map(); // userId -> { baits: {baitKey: amount}, activeBait: string, lastUpdated: timestamp }
    this.pendingConsumption = new Map(); // userId -> { baitKey: amount }
    this.reservedBait = new Map(); // userId -> { baitKey, fishingSpeed, casterSaved, reservedAt }
    this.flushInterval = 30000; // Flush every 30 seconds
    this.maxPendingConsumption = 10; // Flush after 10 bait consumed (total across all types)
    this.timers = new Map(); // userId -> timer
  }

  /**
   * Gets the player's preferred bait based on settings and availability (real-time)
   * @param {string} userId - The user ID
   * @returns {Promise<{baitKey: string|null, fishingSpeed: number}>}
   */
  async getPreferredBait(userId) {
    try {
      // Get player's active bait setting
      const playerData = await getPlayerData(userId);
      const activeBaitSetting = playerData?.settings?.activeBait;
      console.log(
        `[Enhanced BaitManager] User ${userId} activeBaitSetting: ${activeBaitSetting}`
      );

      // If bait is explicitly disabled, return no bait
      if (!activeBaitSetting || activeBaitSetting === "NONE") {
        return { baitKey: null, fishingSpeed: 0 };
      }

      // Get fresh inventory data
      const inventory = await getInventory(userId);

      // Check if player has their selected bait available
      const baitConfig = BAIT_PRIORITIES.find(
        (b) => b.key === activeBaitSetting
      );
      const availableAmount = inventory.items[activeBaitSetting] || 0;

      if (baitConfig && availableAmount > 0) {
        return {
          baitKey: activeBaitSetting,
          fishingSpeed: baitConfig.fishingSpeed,
        };
      }

      // If selected bait is not available, auto-reset setting to NONE
      console.log(
        `[Enhanced BaitManager] User ${userId} selected bait ${activeBaitSetting} is unavailable (${availableAmount}), auto-resetting to NONE`
      );
      await this.resetBaitSetting(userId, activeBaitSetting);

      // Return no bait
      return { baitKey: null, fishingSpeed: 0 };
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error getting preferred bait for user ${userId}:`,
        error
      );
      return { baitKey: null, fishingSpeed: 0 };
    }
  }

  /**
   * Reserves bait for a fishing cycle (checks availability but doesn't consume yet)
   * @param {string} userId - The user ID
   * @param {string} specificBaitKey - Optional specific bait to reserve
   * @returns {Promise<{success: boolean, baitKey: string|null, fishingSpeed: number, reservedFor: string}>}
   */
  async reserveBaitForCycle(userId, specificBaitKey = null) {
    try {
      let targetBait;

      if (specificBaitKey) {
        // Use specific bait if requested
        const baitConfig = BAIT_PRIORITIES.find(
          (b) => b.key === specificBaitKey
        );
        if (!baitConfig) {
          return {
            success: false,
            baitKey: null,
            fishingSpeed: 0,
            reservedFor: userId,
          };
        }
        targetBait = {
          baitKey: specificBaitKey,
          fishingSpeed: baitConfig.fishingSpeed,
        };
      } else {
        // Get preferred bait based on settings at reservation time
        targetBait = await this.getPreferredBait(userId);
      }

      if (!targetBait.baitKey) {
        return {
          success: false,
          baitKey: null,
          fishingSpeed: 0,
          reservedFor: userId,
        };
      }

      // Get fresh inventory data to check current bait availability
      const inventory = await getInventory(userId);
      const availableAmount = inventory.items[targetBait.baitKey] || 0;

      // Check if we have enough bait available
      if (availableAmount <= 0) {
        console.log(
          `[Enhanced BaitManager] User ${userId} has no ${targetBait.baitKey} available (${availableAmount})`
        );

        // If user ran out of their selected bait (or never had it), automatically reset setting to NONE
        if (!specificBaitKey) {
          // Only auto-reset when using preferred bait (not specific requests)
          console.log(
            `[Enhanced BaitManager] Auto-resetting setting for user ${userId} - bait unavailable: ${targetBait.baitKey}`
          );
          await this.resetBaitSetting(userId, targetBait.baitKey);
        }

        return {
          success: false,
          baitKey: null,
          fishingSpeed: 0,
          reservedFor: userId,
        };
      }

      // Check for Caster enchantment at reservation time
      const casterSaved = await this.checkCasterEnchantment(userId);

      // Store reservation for later consumption (at cycle end)
      this.reservedBait.set(userId, {
        baitKey: targetBait.baitKey,
        fishingSpeed: targetBait.fishingSpeed,
        casterSaved,
        reservedAt: Date.now(),
      });

      console.log(
        `[Enhanced BaitManager] Reserved ${targetBait.baitKey} for user ${userId} (caster: ${casterSaved})`
      );

      return {
        success: true,
        baitKey: targetBait.baitKey,
        fishingSpeed: targetBait.fishingSpeed,
        reservedFor: userId,
      };
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error reserving bait for user ${userId}:`,
        error
      );
      return {
        success: false,
        baitKey: null,
        fishingSpeed: 0,
        reservedFor: userId,
      };
    }
  }

  /**
   * Consumes the bait that was previously reserved for this cycle
   * @param {string} userId - The user ID
   * @returns {Promise<{success: boolean, baitKey: string|null, fishingSpeed: number, casterSaved: boolean}>}
   */
  async consumeReservedBait(userId) {
    try {
      const reservation = this.reservedBait.get(userId);

      if (!reservation) {
        console.log(
          `[Enhanced BaitManager] No bait reservation found for user ${userId}`
        );
        return {
          success: false,
          baitKey: null,
          fishingSpeed: 0,
          casterSaved: false,
        };
      }

      // Remove reservation first
      this.reservedBait.delete(userId);

      // Only consume bait if Caster didn't save it
      if (!reservation.casterSaved) {
        try {
          // Check if bait is still available (user might have used/sold it during the cycle)
          const inventory = await getInventory(userId);
          const currentAmount = inventory.items[reservation.baitKey] || 0;

          if (currentAmount <= 0) {
            console.log(
              `[Enhanced BaitManager] User ${userId} no longer has ${reservation.baitKey} - cannot consume reserved bait`
            );
            return {
              success: false,
              baitKey: null,
              fishingSpeed: 0,
              casterSaved: false,
            };
          }

          // Immediately consume bait using atomic inventory update
          await updateInventoryAtomically(
            userId,
            0, // No coin change
            [{ itemKey: reservation.baitKey, amount: -1 }], // Remove 1 bait
            [], // No equipment to add
            [], // No equipment to remove
            0, // No bank coins
            [], // No equipment data updates
            null, // No island data
            null, // No collections data
            false // Not using existing transaction
          );

          // Invalidate cache since inventory changed
          this.invalidateCache(userId);

          console.log(
            `[Enhanced BaitManager] Consumed 1x ${reservation.baitKey} for user ${userId} (was reserved, now consumed)`
          );

          // Check if user just ran out of bait after consumption
          const updatedInventory = await getInventory(userId);
          const remainingAmount =
            updatedInventory.items[reservation.baitKey] || 0;
          if (remainingAmount <= 0) {
            console.log(
              `[Enhanced BaitManager] User ${userId} ran out of ${reservation.baitKey} after consumption - resetting setting to NONE`
            );
            await this.resetBaitSetting(userId, reservation.baitKey);
          }
        } catch (consumeError) {
          console.error(
            `[Enhanced BaitManager] Failed to consume reserved bait ${reservation.baitKey} for user ${userId}:`,
            consumeError
          );
          return {
            success: false,
            baitKey: null,
            fishingSpeed: 0,
            casterSaved: false,
          };
        }
      } else {
        console.log(
          `[Enhanced BaitManager] Caster saved ${reservation.baitKey} for user ${userId} (was reserved, consumption skipped)`
        );
      }

      return {
        success: true,
        baitKey: reservation.baitKey,
        fishingSpeed: reservation.fishingSpeed,
        casterSaved: reservation.casterSaved,
      };
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error consuming reserved bait for user ${userId}:`,
        error
      );
      return {
        success: false,
        baitKey: null,
        fishingSpeed: 0,
        casterSaved: false,
      };
    }
  }

  /**
   * Legacy method for backwards compatibility - now reserves and immediately consumes
   * @param {string} userId - The user ID
   * @param {string} specificBaitKey - Optional specific bait to reserve
   * @returns {Promise<{success: boolean, baitKey: string|null, fishingSpeed: number, casterSaved: boolean}>}
   */
  /**
   * Legacy method for backwards compatibility - now reserves and immediately consumes
   * @param {string} userId - The user ID
   * @param {string} specificBaitKey - Optional specific bait to reserve
   * @returns {Promise<{success: boolean, baitKey: string|null, fishingSpeed: number, casterSaved: boolean}>}
   */
  async reserveBait(userId, specificBaitKey = null) {
    // For backwards compatibility, reserve and immediately consume
    const reservation = await this.reserveBaitForCycle(userId, specificBaitKey);
    if (!reservation.success) {
      return {
        success: false,
        baitKey: null,
        fishingSpeed: 0,
        casterSaved: false,
      };
    }

    const consumption = await this.consumeReservedBait(userId);
    return consumption;
  }

  /**
   * Checks for Caster enchantment and determines if bait should be saved
   * @param {string} userId - The user ID
   * @returns {Promise<boolean>} Whether bait was saved by Caster
   */
  async checkCasterEnchantment(userId) {
    try {
      const { getEquippedFishingRod } = require("./equipmentUtils");
      const fishingRod = await getEquippedFishingRod(userId);

      if (fishingRod && fishingRod.data_json) {
        const rodData = JSON.parse(fishingRod.data_json);
        if (rodData.enchantments && rodData.enchantments.CASTER) {
          const casterLevel = rodData.enchantments.CASTER;
          const CASTER_ENCHANTMENT = {
            baitSaveChance: {
              1: 5,
              2: 10,
              3: 15,
              4: 20,
              5: 25,
            },
          };
          const baitSaveChance =
            CASTER_ENCHANTMENT.baitSaveChance[casterLevel] || 0;
          if (Math.random() * 100 < baitSaveChance) {
            console.log(
              `[Enhanced BaitManager] Caster ${casterLevel} saved bait (${baitSaveChance}% chance)`
            );
            return true;
          }
        }
      }
      return false;
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error checking Caster enchantment for user ${userId}:`,
        error
      );
      return false;
    }
  }

  /**
   * Automatically resets bait setting to NONE when user runs out of their selected bait
   * @param {string} userId - The user ID
   * @param {string} baitKey - The bait type that ran out
   */
  async resetBaitSetting(userId, baitKey) {
    try {
      const { updatePlayerSetting } = require("./playerDataManager");
      await updatePlayerSetting(userId, "activeBait", null);
      console.log(
        `[Enhanced BaitManager] Auto-reset activeBait setting to NONE for user ${userId} (ran out of ${baitKey})`
      );

      // Invalidate cache to reflect the setting change
      this.invalidateCache(userId);
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error resetting bait setting for user ${userId}:`,
        error
      );
    }
  }

  /**
   * Updates cache if needed
   * @param {string} userId - The user ID
   */
  async updateCacheIfNeeded(userId) {
    let cachedData = this.baitCache.get(userId);

    if (!cachedData || this.isCacheStale(cachedData)) {
      const inventory = await getInventory(userId);
      const baits = {};

      // Get amounts for all bait types
      for (const baitConfig of BAIT_PRIORITIES) {
        baits[baitConfig.key] = inventory.items[baitConfig.key] || 0;
      }

      cachedData = {
        baits,
        lastUpdated: Date.now(),
      };
      this.baitCache.set(userId, cachedData);
    }
  }

  /**
   * Flushes pending bait consumption to database
   * @param {string} userId - The user ID
   */
  async flushConsumption(userId) {
    const pendingConsumption = this.pendingConsumption.get(userId);
    if (!pendingConsumption || Object.keys(pendingConsumption).length === 0)
      return;

    try {
      // Clear timer
      if (this.timers.has(userId)) {
        clearTimeout(this.timers.get(userId));
        this.timers.delete(userId);
      }

      // Prepare items to update
      const itemsToUpdate = [];
      for (const [baitKey, amount] of Object.entries(pendingConsumption)) {
        if (amount > 0) {
          itemsToUpdate.push({ itemKey: baitKey, amount: -amount });
        }
      }

      if (itemsToUpdate.length > 0) {
        // Update database
        await updateInventoryAtomically(
          userId,
          0, // coinsToAdd
          itemsToUpdate,
          [], // equipmentToAdd
          [], // equipmentIdsToRemove
          0, // bankCoinsToAdd
          [], // equipmentDataUpdates
          null, // islandJsonString
          null, // collectionsJsonString
          true // useExistingTransaction
        );

        // Update cache
        const cachedData = this.baitCache.get(userId);
        if (cachedData) {
          for (const [baitKey, amount] of Object.entries(pendingConsumption)) {
            cachedData.baits[baitKey] = Math.max(
              0,
              (cachedData.baits[baitKey] || 0) - amount
            );
          }
          cachedData.lastUpdated = Date.now();
        }
      }

      // Clear pending consumption
      this.pendingConsumption.delete(userId);
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error flushing consumption for user ${userId}:`,
        error
      );
      // On error, reset cache to force fresh fetch next time
      this.baitCache.delete(userId);
      this.pendingConsumption.delete(userId);
    }
  }

  /**
   * Sets a timer to flush consumption later
   * @param {string} userId - The user ID
   */
  setFlushTimer(userId) {
    if (this.timers.has(userId)) {
      clearTimeout(this.timers.get(userId));
    }

    const timer = setTimeout(() => {
      this.flushConsumption(userId);
    }, this.flushInterval);

    this.timers.set(userId, timer);
  }

  /**
   * Checks if cache is stale
   * @param {object} cachedData - The cached data
   * @returns {boolean} - True if stale
   */
  isCacheStale(cachedData) {
    const cacheMaxAge = 60000; // 1 minute
    return Date.now() - cachedData.lastUpdated > cacheMaxAge;
  }

  /**
   * Gets available bait amounts for a user (real-time from database)
   * @param {string} userId - The user ID
   * @returns {Promise<object>} - Object with bait keys and available amounts
   */
  async getAvailableBaits(userId) {
    try {
      const inventory = await getInventory(userId);
      const availableBaits = {};

      for (const baitConfig of BAIT_PRIORITIES) {
        availableBaits[baitConfig.key] = inventory.items[baitConfig.key] || 0;
      }

      return availableBaits;
    } catch (error) {
      console.error(
        `[Enhanced BaitManager] Error getting available baits for user ${userId}:`,
        error
      );
      return {};
    }
  }

  /**
   * Forces immediate flush for a specific user
   * @param {string} userId - The user ID
   */
  async forceFlush(userId) {
    await this.flushConsumption(userId);
  }

  /**
   * Flushes all pending consumptions
   */
  async flushAllConsumptions() {
    const userIds = Array.from(this.pendingConsumption.keys());
    const promises = userIds.map((userId) => this.flushConsumption(userId));
    await Promise.all(promises);
  }

  /**
   * Gets the currently reserved bait for a user (if any)
   * @param {string} userId - The user ID
   * @returns {object|null} - Reserved bait info or null
   */
  getReservedBait(userId) {
    return this.reservedBait.get(userId) || null;
  }

  /**
   * Clears any pending reservation for a user (e.g., when action is stopped)
   * @param {string} userId - The user ID
   */
  clearReservation(userId) {
    const wasReserved = this.reservedBait.has(userId);
    this.reservedBait.delete(userId);
    if (wasReserved) {
      console.log(
        `[Enhanced BaitManager] Cleared bait reservation for user ${userId}`
      );
    }
  }

  /**
   * Invalidates cache for a specific user (call when user inventory changes externally)
   * @param {string} userId - The user ID
   */
  invalidateCache(userId) {
    console.log(`[Enhanced BaitManager] Invalidating cache for user ${userId}`);
    this.baitCache.delete(userId);
  }

  /**
   * Clears all cache and timers
   */
  cleanup() {
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();
    this.baitCache.clear();
    this.pendingConsumption.clear();
    this.reservedBait.clear();
  }
}

// Create singleton instance
const enhancedBaitManager = new EnhancedBaitManager();

module.exports = {
  EnhancedBaitManager,
  enhancedBaitManager,
  BAIT_PRIORITIES,
};
