// Migration script to add collections column (Version 2)

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve({ lastID: this.lastID, changes: this.changes });
    });
  });
}

module.exports = {
  /**
   * @param {import('sqlite3').Database} db
   */
  async up(db) {
    console.log("Applying migration 002_add_collections...");
    // Add the new JSON column to store collection progress
    // Use IF NOT EXISTS just in case, though the runner prevents re-running
    await dbRunAsync(
      db,
      "ALTER TABLE players ADD COLUMN collections_json TEXT",
      [],
    );
    // Initialize existing rows with an empty JSON object '{}' if the column is added newly
    // NOTE: SQLite's ALTER TABLE ADD COLUMN automatically adds NULL for existing rows.
    // We will handle the default empty object in the data manager (getPlayerData).
    console.log("Column 'collections_json' added to 'players' table.");
  },
};
