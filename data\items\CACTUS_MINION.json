{"name": "Cactus <PERSON>", "emoji": "<:minion_cactus:1400315943393165322>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "resourceItemKey": "CACTUS", "recipes": [{"ingredients": [{"itemKey": "CACTUS", "amount": 128}]}], "craftingRequirements": {"collections": {"CACTUS": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 27, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 27, "maxStorage": 192, "upgradeCost": [{"itemKey": "CACTUS", "amount": 256}]}, {"tier": 3, "generationIntervalSeconds": 25, "maxStorage": 192, "upgradeCost": [{"itemKey": "CACTUS", "amount": 512}]}, {"tier": 4, "generationIntervalSeconds": 25, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS_GREEN", "amount": 8}]}, {"tier": 5, "generationIntervalSeconds": 23, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS_GREEN", "amount": 24}]}, {"tier": 6, "generationIntervalSeconds": 23, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS_GREEN", "amount": 64}]}, {"tier": 7, "generationIntervalSeconds": 21, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS_GREEN", "amount": 128}]}, {"tier": 8, "generationIntervalSeconds": 21, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS_GREEN", "amount": 256}]}, {"tier": 9, "generationIntervalSeconds": 18, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS_GREEN", "amount": 512}]}, {"tier": 10, "generationIntervalSeconds": 18, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS", "amount": 8}]}, {"tier": 11, "generationIntervalSeconds": 15, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_CACTUS", "amount": 16}]}], "drops": [{"itemKey": "CACTUS", "chance": 1}]}