const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
} = require("discord.js");

const { formatNumber } = require("../utils/displayUtils");
const { getPlayerData } = require("../utils/playerDataManager");
const { updateInventoryAtomically } = require("../utils/inventory");
const { STATS } = require("../gameConfig");
const { STAT_ABBREVIATIONS } = require("./statAbbreviations");
const {
  getAvailableReforges,
  getReforgeStats,
  canReforge,
} = require("../data/reforges");
const { dbRunQueued } = require("../utils/dbUtils");
const { getItemColor } = require("./rarityUtils");

async function handleReroll(interaction, selectedNPC, allItems, selectedValue) {
  // Parse the selectedValue which is in format "source_identifier"
  const [source, ...idParts] = selectedValue.split("_");
  const identifier = idParts.join("_"); // Rejoin in case the ID has underscores

  const playerData = await getPlayerData(interaction.user.id);

  let item, itemData;

  // Find the item data based on source
  if (source === "storage") {
    // For storage items, look in playerData.inventory.items
    itemData = playerData.inventory?.items?.[identifier];
    if (itemData && itemData.amount > 0) {
      item = allItems[identifier];
    }
  } else if (source === "equipment") {
    // For equipped items, find in equipment array
    const equippedItem = playerData.inventory?.equipment?.find(
      (eq) => eq.id === identifier
    );
    if (equippedItem && equippedItem.itemKey) {
      item = allItems[equippedItem.itemKey];
      itemData = equippedItem;
    }
  }

  if (!item || !itemData) {
    return interaction.reply({ content: "Item not found!", ephemeral: true });
  }

  // Check if item can be reforged using the proper function
  if (!canReforge(item)) {
    return interaction.reply({
      content: "This item cannot be reforged!",
      ephemeral: true,
    });
  }

  // Calculate reforge cost using the dedicated function
  const { getReforgeCost } = require("../data/reforges");
  const cost = getReforgeCost(item.rarity);
  //console.log(`[handleReroll] Reforging item with rarity: ${item.rarity}, Cost: ${cost}`);

  // Check if player has enough coins
  if ((playerData.coins || 0) < cost) {
    return interaction.reply({
      content: `You don't have enough coins to reforge this item! You need ${formatNumber(cost)} coins.`,
      ephemeral: true,
    });
  }

  // Acknowledge the interaction first to prevent timeout
  try {
    await interaction.deferUpdate();
  } catch (error) {
    if (error.code === 10062) {
      // Unknown interaction error
      return; // Exit early as we can't continue with an expired interaction
    } else if (error.code === 40060) {
      // Interaction already acknowledged
      //console.log("[handleReroll] Interaction has already been acknowledged");
      // Continue execution as the interaction is already acknowledged
    } else {
      console.error("[handleReroll] Error deferring update:", error);
      return; // Exit early as we can't continue
    }
  }

  try {
    // Get available reforges for this item type
    const availableReforges = getAvailableReforges(item.type, item.subtype);

    if (availableReforges.length === 0) {
      return interaction.followUp({
        content: "No reforges available for this item type!",
        ephemeral: true,
      });
    }

    // Select a random reforge (including current one)
    const newReforgeKey =
      availableReforges[Math.floor(Math.random() * availableReforges.length)];

    // Get the reforge stats using the proper function
    const { ITEM_RARITY } = require("../gameConfig");
    const rarityKey = ITEM_RARITY[item.rarity]?.name || "Common";
    const reforgeStats = getReforgeStats(
      newReforgeKey,
      item.type,
      item.subtype,
      rarityKey
    );

    if (!reforgeStats) {
      return interaction.followUp({
        content: "Error getting reforge stats!",
        ephemeral: true,
      });
    }

    // Create new reforge object (new dynamic format - only store the key)
    const newReforge = {
      key: newReforgeKey,
    };

    // Update the item's data_json with the new reforge
    let dataJsonToModify = {};

    if (source === "storage") {
      // For storage items, update the data_json in inventory items
      if (
        typeof itemData.data_json === "string" &&
        itemData.data_json.trim() !== ""
      ) {
        try {
          dataJsonToModify = JSON.parse(itemData.data_json);
        } catch {
          dataJsonToModify = {};
        }
      } else if (
        typeof itemData.data_json === "object" &&
        itemData.data_json !== null
      ) {
        dataJsonToModify = { ...itemData.data_json };
      }

      dataJsonToModify.reforge = newReforge;

      // Update using updateInventoryAtomically

      // Create a special update for the item's data_json
      // We need to update the item in the database directly since updateInventoryAtomically
      // doesn't have a parameter for updating item data_json
      await dbRunQueued(
        "UPDATE player_inventory_items SET data_json = ? WHERE discord_id = ? AND item_name = ?",
        [JSON.stringify(dataJsonToModify), interaction.user.id, identifier]
      );

      // Update the coins
      await updateInventoryAtomically(
        interaction.user.id,
        -cost, // coinsToAdd
        [], // itemsToChange
        [], // equipmentToAdd
        [], // equipmentIdsToRemove
        0, // bankCoinsToAdd
        [], // equipmentDataUpdates
        null, // islandJsonString
        null // collectionsJsonString
      );
    } else if (source === "equipment") {
      // For equipped items, update the equipment data_json
      // Get current data from database for equipped items
      const db = require("../utils/database");
      const currentItemData = await new Promise((resolve, reject) => {
        db.get(
          "SELECT data_json FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
          [identifier, interaction.user.id],
          (err, row) => (err ? reject(err) : resolve(row))
        );
      });

      // Parse existing data_json or create empty object if none exists
      let dataJsonToModify = {};
      try {
        if (currentItemData?.data_json) {
          if (typeof currentItemData.data_json === "string") {
            dataJsonToModify = JSON.parse(currentItemData.data_json);
          } else {
            dataJsonToModify = { ...currentItemData.data_json };
          }
        }
      } catch (error) {
        console.error("[handleReroll] Error parsing data_json:", error);
        dataJsonToModify = {};
      }

      // Add the new reforge to the existing data
      dataJsonToModify.reforge = newReforge;

      const updates = {
        coinsToAdd: -cost,
        equipmentDataUpdates: [
          {
            equipmentId: identifier,
            dataJson: JSON.stringify(dataJsonToModify),
          },
        ],
      };

      await updateInventoryAtomically(
        interaction.user.id,
        -cost, // coinsToAdd
        [], // itemsToChange
        [], // equipmentToAdd
        [], // equipmentIdsToRemove
        0, // bankCoinsToAdd
        updates.equipmentDataUpdates, // equipmentDataUpdates
        null, // islandJsonString
        null // collectionsJsonString
      );
    } else {
      console.error(
        `[handleReroll] Unrecognized item source: "${source}" for item ID: ${identifier}. Reforge cannot be saved.`
      );
      await interaction.followUp({
        content:
          "An internal error occurred: Unrecognized item source. Your reforge could not be saved. Please report this.",
        ephemeral: true,
      });
      return;
    }

    // Player data and item have been updated and saved.
    // newReforge is the chosen reforge.
    // playerData.coins is the updated coin balance.
    // cost is the reforge cost.
    // identifier is the item's ID.

    // Get dynamic reforge stats for display
    const {
      calculateDynamicReforgeStats,
      getDynamicReforgeName,
    } = require("./dynamicReforgeStats");
    const dynamicReforgeStats = calculateDynamicReforgeStats(newReforge, item);
    const dynamicReforgeName = getDynamicReforgeName(newReforge, item);

    // Recalculate combined stats for the display
    const combinedStats = { ...item.baseStats }; // Start with base stats
    if (dynamicReforgeStats && Object.keys(dynamicReforgeStats).length > 0) {
      for (const [stat, value] of Object.entries(dynamicReforgeStats)) {
        combinedStats[stat] = (combinedStats[stat] || 0) + value;
      }
    }
    // Also include any existing enchantments or other modifiers if they are stored separately
    // and affect the display. For now, assuming baseStats + reforge is the total.

    // Format TOTAL stats (item base + new reforge)
    const totalStatsArray = Object.entries(combinedStats) // Use combinedStats
      .map(([stat, value]) => {
        const statConfig = STATS[stat];
        if (statConfig) {
          const displayValue = statConfig.isPercentage
            ? `${value > 0 ? "+" : ""}${value}%`
            : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
          const statName =
            STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
          return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
        } else {
          return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
        }
      });
    let totalStatsText = "No stats";
    if (totalStatsArray.length > 0) {
      totalStatsText = totalStatsArray.reduce((acc, statText, index) => {
        if (index % 3 === 0 && index > 0) acc += "\n";
        return acc + (index % 3 === 0 ? "" : " ") + statText;
      }, "");
    }

    // Format NEW REFORGE specific stats
    let newReforgeOnlyStatsText = "No stats";
    if (dynamicReforgeStats && Object.keys(dynamicReforgeStats).length > 0) {
      const newReforgeOnlyStatsArray = Object.entries(dynamicReforgeStats).map(
        ([stat, value]) => {
          const statConfig = STATS[stat];
          if (statConfig) {
            const displayValue = statConfig.isPercentage
              ? `${value > 0 ? "+" : ""}${value}%`
              : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
            const statName =
              STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
            return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
          } else {
            return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
          }
        }
      );
      if (newReforgeOnlyStatsArray.length > 0) {
        newReforgeOnlyStatsText = newReforgeOnlyStatsArray.reduce(
          (acc, statText, index) => {
            if (index % 3 === 0 && index > 0) acc += "\n";
            return acc + (index % 3 === 0 ? "" : " ") + statText;
          },
          ""
        );
      }
    }

    // Construct the new description
    let newDescription = `**Stats:**\n${totalStatsText}\n\n`;
    newDescription += `**Current Reforge:** ${dynamicReforgeName}\n`;
    // Display only the new reforge's specific stats under its name
    newDescription += `**Reforge Stats:**\n${newReforgeOnlyStatsText}\n\n`;
    newDescription += `**Your coins:** <:purse_coins:1367849116033482772> ${formatNumber(playerData.coins)} Coins\n\n`;
    newDescription += `**Reforge Cost:** <:purse_coins:1367849116033482772> ${formatNumber(cost)} Coins`;

    const successEmbed = new EmbedBuilder()
      .setTitle(
        `<:npc_blacksmith:1378078835752439879> Blacksmith: ${item.emoji} ${item.name}`
      ) // Original title
      .setDescription(newDescription)
      .setColor(getItemColor(item)) // Use helper function for item color
      .setFooter({ text: `Item ID: ${identifier}` });

    // Rebuild buttons to update disabled state of reroll button
    const updatedRerollButton = new ButtonBuilder()
      .setCustomId(interaction.message.components[0].components[0].customId) // Keep original customId
      .setLabel("Reroll")
      .setStyle(ButtonStyle.Primary)
      .setDisabled(playerData.coins < cost); // Update disabled state

    const updatedChangeItemButton = new ButtonBuilder()
      .setCustomId(interaction.message.components[0].components[1].customId) // Keep original customId
      .setLabel("Change Item")
      .setStyle(ButtonStyle.Secondary);

    const updatedCloseButton = new ButtonBuilder()
      .setCustomId(interaction.message.components[0].components[2].customId) // Keep original customId
      .setLabel("Close")
      .setStyle(ButtonStyle.Danger);

    const updatedActionRow = new ActionRowBuilder().addComponents(
      updatedRerollButton,
      updatedChangeItemButton,
      updatedCloseButton
    );

    try {
      await interaction.editReply({
        embeds: [successEmbed],
        components: [updatedActionRow],
      });
    } catch (error) {
      if (error.code === 10062) {
        // Unknown interaction error
      } else {
        console.error(
          "[handleReroll] Error updating with success embed:",
          error
        );
      }
    }
  } catch (error) {
    console.error("Error updating player data:", error);
    try {
      await interaction.followUp({
        content:
          "An error occurred while updating your item. Please try again.",
        ephemeral: true,
      });
    } catch (e) {
      console.error("Failed to send error message:", e);
    }
  }
}

module.exports = handleReroll;
