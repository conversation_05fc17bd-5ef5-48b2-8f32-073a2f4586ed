// Centralized Skill XP Manager
// This handles ALL skill XP gains and automatically sends level up notifications

// No MessageFlags usage required

const {
  addSkillExpOnly,
  getTamingExpFromPetExp,
  addPetExp,
} = require("./expGain");
const { checkAndNotifyDisblockXP } = require("./disblockXpSystem");

/**
 * Add skill XP with automatic notifications and pet/taming XP
 * This is the ONE function to use for all skill XP gains in the bot
 *
 * @param {string|Object} userIdOrCharacter - Discord user ID OR character object
 * @param {string} skillName - The skill name (e.g., 'farming', 'mining', etc.)
 * @param {number} skillExpGained - The amount of skill XP to add
 * @param {Object} [interaction=null] - Discord interaction for sending notifications (optional)
 * @param {boolean} [deferNotifications=false] - If true, returns embeds but doesn't send them (for skill actions)
 * @param {number} [preCalculatedWisdom=null] - Pre-calculated wisdom value to use instead of recalculating (for consistency)
 * @returns {Promise<Object>} - Returns result with notification status and final XP
 */
async function addSkillExp(
  userIdOr<PERSON>haracter,
  skillName,
  skillExpGained,
  interaction = null,
  deferNotifications = false,
  preCalculatedWisdom = null
) {
  if (!userIdOrCharacter || !skillName || skillExpGained === 0) {
    return {
      exp: 0,
      levelUpEmbeds: [],
      totalLevelUpCoinReward: 0,
      petExpGained: 0,
      notificationsSent: false,
    };
  }

  // figure out if we got a userId or character object
  let character;
  let userId;

  if (typeof userIdOrCharacter === "string") {
    // we got a userId, need to load character
    userId = userIdOrCharacter;
    const { getPlayerData } = require("./playerDataManager");
    character = await getPlayerData(userId);
    if (!character) {
      console.error(
        `[Skill Notifications] Character not found for user ${userId}`
      );
      return {
        exp: 0,
        levelUpEmbeds: [],
        totalLevelUpCoinReward: 0,
        petExpGained: 0,
        notificationsSent: false,
      };
    }
  } else {
    // we got a character object, extract userId from it
    character = userIdOrCharacter;
    userId = character.discordId || character.discord_id;
    if (!userId) {
      console.error(
        `[Skill Notifications] No userId found in character object`
      );
      return {
        exp: 0,
        levelUpEmbeds: [],
        totalLevelUpCoinReward: 0,
        petExpGained: 0,
        notificationsSent: false,
      };
    }
  }

  // Apply wisdom multiplier to skill XP before adding it
  // For skill actions, wisdom is now applied per cycle, so preCalculatedWisdom should be null
  // This is only for non-action XP gains (like admin commands, quest rewards, etc.)
  let wisdom;
  if (preCalculatedWisdom !== null) {
    // Use pre-calculated wisdom for non-action uses
    wisdom = preCalculatedWisdom;
    //console.log(`[addSkillExp] Using pre-calculated ${skillName} wisdom: ${wisdom}%`);
  } else {
    // Calculate wisdom from character stats for non-action uses
    const { calculateTotalStat } = require("./statCalculations");
    const wisdomStatKey = `${skillName.toUpperCase()}_WISDOM`;
    wisdom = calculateTotalStat(character.stats?.[wisdomStatKey] || {});
    //console.log(`[addSkillExp] Calculated ${skillName} wisdom from character stats: ${wisdom}%`);
  }

  const wisdomMultiplier = 1 + wisdom / 100;
  const finalSkillExpGained = skillExpGained * wisdomMultiplier; // Allow decimal XP - no Math.floor

  // Log wisdom application if there's a bonus
  if (wisdom > 0) {
    //console.log(`[addSkillExp] Applied ${skillName} wisdom: +${wisdom}% (${skillExpGained} -> ${finalSkillExpGained} XP)`);
  }

  // add the primary skill XP
  const skillResult = await addSkillExpOnly(
    character,
    skillName,
    finalSkillExpGained
  );

  let allLevelUpEmbeds = [];
  let totalCoinReward = skillResult.totalLevelUpCoinReward || 0;

  // add skill level up embeds if any
  if (skillResult.levelUpEmbeds) {
    allLevelUpEmbeds = allLevelUpEmbeds.concat(skillResult.levelUpEmbeds);
  }

  // calculate pet XP based on the FINAL skill XP gained (after wisdom multiplier)
  // Use the same calculation method as farming/mining for consistency
  const { calculateActionDerivedExp } = require("./skillActionUtils");
  const petExpGained = calculateActionDerivedExp(
    finalSkillExpGained,
    skillName,
    character
  );

  let petResult = null;
  let tamingResult = null;

  if (petExpGained > 0 && character.active_pet_id) {
    // add pet XP
    petResult = await addPetExp(
      character,
      character.active_pet_id,
      petExpGained
    );

    if (petResult.levelUpEmbeds) {
      allLevelUpEmbeds = allLevelUpEmbeds.concat(petResult.levelUpEmbeds);
    }

    // calculate and add taming XP based on pet XP - use addSkillExpOnly here to avoid infinite recursion
    const tamingExpGained = getTamingExpFromPetExp(petExpGained);

    if (tamingExpGained > 0) {
      tamingResult = await addSkillExpOnly(
        character,
        "taming",
        tamingExpGained
      );

      // Store the actual XP gained instead of total XP
      tamingResult.expGained = tamingExpGained;

      if (tamingResult.levelUpEmbeds) {
        allLevelUpEmbeds = allLevelUpEmbeds.concat(tamingResult.levelUpEmbeds);
      }

      if (tamingResult.totalLevelUpCoinReward) {
        totalCoinReward += tamingResult.totalLevelUpCoinReward;
      }
    }
  }

  // send notifications immediately if we have an interaction and level ups occurred (unless deferred)
  let notificationsSent = false;
  if (
    !deferNotifications &&
    interaction &&
    interaction.channel &&
    allLevelUpEmbeds.length > 0
  ) {
    try {
      // Send each level up notification as a separate message with delays for better readability
      for (let i = 0; i < allLevelUpEmbeds.length; i++) {
        const item = allLevelUpEmbeds[i];

        // Send embed-only notifications
        if (item && item.embed) {
          await interaction.channel.send({ embeds: [item.embed] });
        } else {
          // Fallback: assume it's a raw EmbedBuilder
          await interaction.channel.send({ embeds: [item] });
        }

        // Add 1000ms delay between notifications (except after the last one)
        if (i < allLevelUpEmbeds.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
      notificationsSent = true;
      //console.log(`[Skill Notifications] Sent ${allLevelUpEmbeds.length} individual level up notification message(s) for ${skillName} skill with delays`);
    } catch (error) {
      console.error(
        `[Skill Notifications] Error sending level up notifications for ${skillName}:`,
        error
      );
    }
  }

  // save character data if we loaded it ourselves (always save when XP was changed)
  if (typeof userIdOrCharacter === "string" && finalSkillExpGained !== 0) {
    try {
      // Use atomic skill data save instead of full savePlayerData to avoid interruption issues
      const {
        updatePlayerSkillDataAtomically,
      } = require("./playerDataManager");

      //console.log(`[Skill Notifications] About to save character data for ${userId} - ${skillName} XP gained: ${finalSkillExpGained}`);
      //console.log(`[Skill Notifications] Character ${skillName} XP before save: ${character.skills?.[skillName]?.exp || 0}`);
      //console.log(`[Skill Notifications] Using atomic skill data save instead of full savePlayerData`);

      // Save using atomic skill data save - much faster and more reliable
      await updatePlayerSkillDataAtomically(userId, character);
      //console.log(`[Skill Notifications] Saved ${finalSkillExpGained} ${skillName} XP for user ${userId}${petExpGained > 0 ? ` and ${petExpGained} pet XP` : ''}`);

      // apply coin rewards if any (only for positive XP gains)
      if (totalCoinReward > 0 && finalSkillExpGained > 0) {
        try {
          const { updateInventoryAtomically } = require("./inventory");
          await updateInventoryAtomically(userId, totalCoinReward, []);
        } catch (error) {
          console.error(
            `[Skill Notifications] Error applying coin rewards:`,
            error
          );
        }
      }
    } catch (saveError) {
      console.error(
        `[Skill Notifications] Error saving character data:`,
        saveError
      );
      // Re-throw the error so the caller knows about the failure
      throw saveError;
    }
  } else {
    //console.log(`[Skill Notifications] Skipping save - userIdOrCharacter type: ${typeof userIdOrCharacter}, finalSkillExpGained: ${finalSkillExpGained}`);
  }

  // update disblock XP system - always check if XP was gained, regardless of notifications
  if (finalSkillExpGained > 0) {
    try {
      // If we have an interaction, use it; otherwise we'll skip DisBlock XP here and let the caller handle it
      if (interaction) {
        await checkAndNotifyDisblockXP(userId, interaction, character);
      }
    } catch (error) {
      console.error(`[Skill Notifications] Error updating Disblock XP:`, error);
    }
  }

  return {
    exp: skillResult.exp, // return the final skill XP amount
    levelUpEmbeds: allLevelUpEmbeds.length > 0 ? allLevelUpEmbeds : [], // return all level up embeds
    totalLevelUpCoinReward: totalCoinReward,
    petExpGained,
    tamingExpGained: tamingResult ? tamingResult.expGained : 0,
    notificationsSent,
  };
}

module.exports = {
  addSkillExp,
};
