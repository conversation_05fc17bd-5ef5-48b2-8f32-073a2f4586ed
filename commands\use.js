const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const { getPlayerData, savePlayerData } = require("../utils/playerDataManager");
const configManager = require("../utils/configManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const { EMBED_COLORS, BITS_EMOJI } = require("../gameConfig");
const { calculateAllStats } = require("../utils/statCalculations");
const { wrapText } = require("../utils/textUtils");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("use")
    .setDescription("Use/consume an item from your inventory")
    .addStringOption((option) =>
      option
        .setName("item")
        .setDescription("The item to use")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addIntegerOption((option) =>
      option
        .setName("quantity")
        .setDescription("How many to use (default: 1)")
        .setMinValue(1)
        .setRequired(false)
    ),

  async execute(interaction) {
    try {
      const userId = interaction.user.id;

      await interaction.deferReply();

      const character = await getPlayerData(userId);
      if (!character) {
        return interaction.editReply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
        });
      }

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.editReply({
          content: "You don't have permission to use this command.",
        });
      }

      const itemInput = interaction.options.getString("item");
      const quantity = interaction.options.getInteger("quantity") || 1;
      const allItems = configManager.getAllItems();

      // Find the item (exact match or search)
      let itemKey = null;
      let itemData = null;

      // Try exact match first
      if (allItems[itemInput.toUpperCase()]) {
        itemKey = itemInput.toUpperCase();
        itemData = allItems[itemKey];
      } else {
        // Search for partial matches
        const searchResults = Object.entries(allItems)
          .filter(
            ([_key, item]) =>
              item.name &&
              item.name.toLowerCase().includes(itemInput.toLowerCase()) &&
              item.consumable // Only show consumable items
          )
          .slice(0, 10);

        if (searchResults.length === 0) {
          return interaction.editReply({
            content: `No consumable items found matching "${itemInput}".`,
          });
        }

        if (searchResults.length === 1) {
          [itemKey, itemData] = searchResults[0];
        } else {
          // Multiple matches - show options
          const matchList = searchResults
            .map(([_key, item]) => `${item.emoji || "❓"} **${item.name}**`)
            .join("\n");

          return interaction.editReply({
            content: `Multiple items found matching "${itemInput}":\n\n${matchList}\n\nPlease be more specific.`,
          });
        }
      }

      // Check if item is consumable
      if (!itemData.consumable) {
        return interaction.editReply({
          content: `${itemData.emoji || "❓"} **${itemData.name}** is not a consumable item.`,
        });
      }

      // Check if player has the item
      const playerInventory = character.inventory?.items || {};
      const itemCount = playerInventory[itemKey] || 0;

      if (itemCount <= 0) {
        return interaction.editReply({
          content: `You don't have any ${itemData.emoji || "❓"} **${itemData.name}** in your inventory.`,
        });
      }

      // Check if player has enough of the item
      if (itemCount < quantity) {
        return interaction.editReply({
          content: `You only have ${itemCount} ${itemData.emoji || "❓"} **${itemData.name}** but want to use ${quantity}.`,
        });
      }

      // Handle potion consumption
      if (itemData.consumable.type === "POTION_EFFECT") {
        return await this.handlePotionConsumption(
          interaction,
          character,
          itemKey,
          itemData,
          quantity
        );
      }

      // Handle God Potion consumption
      if (itemData.consumable.type === "GOD_POTION") {
        return await this.handleGodPotionConsumption(
          interaction,
          character,
          itemKey,
          itemData,
          quantity
        );
      }

      // Handle booster cookie consumption
      if (itemData.consumable.type === "BOOSTER_COOKIE") {
        return await this.handleBoosterCookieConsumption(
          interaction,
          character,
          itemKey,
          itemData,
          quantity
        );
      }

      // Handle other consumable types (future expansion)
      if (itemData.consumable.type === "KAT_SKIP") {
        return await this.handleKatSkipConsumption(
          interaction,
          character,
          itemKey,
          itemData,
          quantity
        );
      }

      return interaction.editReply({
        content: `${itemData.emoji || "❓"} **${itemData.name}** consumption is not yet implemented.`,
      });
    } catch (error) {
      console.error("[Use Command Error]", error);
      return interaction.editReply({
        content:
          "An error occurred while trying to use the item. Please try again.",
      });
    }
  },

  async handleKatSkipConsumption(
    interaction,
    character,
    itemKey,
    itemData,
    quantity = 1
  ) {
    const userId = interaction.user.id;
    try {
      const {
        checkPetUpgradeStatus,
        completePetUpgrade,
      } = require("../utils/petUpgradeUtils");
      const status = await checkPetUpgradeStatus(userId);
      if (!status.upgrading) {
        return interaction.editReply({
          content: `You don't have a pet being upgraded at Kat. These can only be used while a pet upgrade is in progress.`,
        });
      }

      // Compute total ms to skip, but cap to remaining
      const hoursPerUse = itemData.consumable.hoursToSkip || 24;
      const totalSkipMsRequested = hoursPerUse * quantity * 60 * 60 * 1000;
      const now = Date.now();
      const remainingMs = Math.max(0, status.pet_upgrade_json.endTime - now);
      if (remainingMs <= 0) {
        // already complete, advise to pick up
        return interaction.editReply({
          content: `Your pet upgrade is already complete. Pick it up from Kat instead of using this.`,
        });
      }

      // Determine actual items to use
      const singleSkipMs = hoursPerUse * 60 * 60 * 1000;
      const maxUsable = Math.ceil(remainingMs / singleSkipMs);
      const actualQty = Math.min(quantity, maxUsable);
      const skipMs = Math.min(totalSkipMsRequested, remainingMs);

      // Apply skip by pulling, editing, and saving minimal fields
      const {
        getPlayerData,
        savePlayerData,
      } = require("../utils/playerDataManager");
      const fresh = await getPlayerData(userId);
      if (!fresh?.petUpgrade) {
        return interaction.editReply({
          content: `Your upgrade data couldn't be found. Try again in a moment.`,
        });
      }

      // Re-evaluate remaining using fresh data for accuracy
      const remainingNow = Math.max(0, fresh.petUpgrade.endTime - Date.now());
      const maxUsableNow = Math.ceil(remainingNow / singleSkipMs);
      const qtyToConsume = Math.min(actualQty, maxUsableNow);
      const msToSkip = Math.min(qtyToConsume * singleSkipMs, remainingNow);

      fresh.petUpgrade.endTime = fresh.petUpgrade.endTime - msToSkip;
      await savePlayerData(userId, fresh, ["petUpgrade"]);

      // Consume items atomically
      const { updateInventoryAtomically } = require("../utils/inventory");
      await updateInventoryAtomically(
        userId,
        0,
        [{ itemKey, amount: -qtyToConsume }],
        [],
        [],
        0
      );

      // If now complete, auto-complete upgrade
      let completed = false;
      if (Date.now() >= fresh.petUpgrade.endTime) {
        const result = await completePetUpgrade(userId);
        completed = result?.success === true;
      }

      const hoursSkipped = Math.round(msToSkip / (60 * 60 * 1000));
      const { formatTimeRemaining } = require("../utils/petUpgradeUtils");

      if (completed) {
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.SUCCESS)
          .setTitle(`${itemData.emoji || "🌸"} ${itemData.name} Used`)
          .setDescription(
            `You used ${qtyToConsume} ${itemData.emoji || ""} **${itemData.name}**${qtyToConsume > 1 ? "s" : ""} and your pet upgrade has finished!`
          );
        return interaction.editReply({ embeds: [embed] });
      } else {
        const remainingAfter = Math.max(
          0,
          fresh.petUpgrade.endTime - Date.now()
        );
        const completionTs = Math.floor((Date.now() + remainingAfter) / 1000);
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setTitle(`${itemData.emoji || "🌸"} ${itemData.name} Used`)
          .setDescription(
            `You used ${qtyToConsume} ${itemData.emoji || ""} **${itemData.name}**${qtyToConsume > 1 ? "s" : ""} and skipped ~${hoursSkipped} hour${hoursSkipped !== 1 ? "s" : ""} of upgrade time.`
          )
          .addFields({
            name: "New Ready Time",
            value: `<t:${completionTs}:R>`,
            inline: true,
          });

        if (qtyToConsume < quantity) {
          embed.addFields({
            name: "Notice",
            value: `Could only use ${qtyToConsume} due to limited remaining time.`,
            inline: false,
          });
        }
        return interaction.editReply({ embeds: [embed] });
      }
    } catch (err) {
      console.error("[handleKatSkipConsumption] Error:", err);
      return interaction.editReply({
        content: "Failed to use the item. Please try again.",
      });
    }
  },

  async handlePotionConsumption(
    interaction,
    character,
    itemKey,
    itemData,
    quantity = 1
  ) {
    const userId = interaction.user.id;
    const potionConfig = itemData.consumable;
    const {
      formatDurationShort,
      cleanupExpiredEffects,
    } = require("../utils/potionEffects");

    // Clean up expired effects first
    cleanupExpiredEffects(character);

    // Block if God Potion is active
    const { isGodPotionActive } = require("../utils/potionEffects");
    if (isGodPotionActive(character)) {
      return interaction.editReply({
        content: `You already have an active God Potion. All potion effects are maxed, so you can't consume normal potions now.`,
      });
    }

    // Calculate potion duration with Alchemy bonus
    const allStats = calculateAllStats(character);
    const potionDurationBonus = allStats.POTION_DURATION || 0;
    const baseDuration = potionConfig.duration * 1000; // Convert to milliseconds
    const singlePotionDuration = Math.floor(
      baseDuration * (1 + potionDurationBonus / 100)
    );

    // Calculate how many potions we can actually consume without exceeding 72 hours
    const maxDuration = 72 * 60 * 60 * 1000; // 72 hours in milliseconds
    const currentTime = Date.now();

    // Get current remaining time for this family
    let currentRemainingTime = 0;
    if (
      character.activeEffects &&
      character.activeEffects[potionConfig.family]
    ) {
      const currentEffect = character.activeEffects[potionConfig.family];
      if (currentEffect.expiresAt > currentTime) {
        currentRemainingTime = currentEffect.expiresAt - currentTime;
      }
    }

    // Check if there's a stronger potion active
    if (
      character.activeEffects &&
      character.activeEffects[potionConfig.family]
    ) {
      const currentEffect = character.activeEffects[potionConfig.family];
      if (
        currentEffect.tier > potionConfig.tier &&
        currentEffect.expiresAt > currentTime
      ) {
        const currentPotionName = currentEffect.name || "Unknown Potion";
        return interaction.editReply({
          content: `You have a stronger ${potionConfig.family} effect active: **${currentPotionName}**. Cannot use a weaker potion.`,
        });
      }
    }

    // Calculate maximum potions we can use
    const availableTimeSlot = maxDuration - currentRemainingTime;
    const maxPotionsCanUse = Math.floor(
      availableTimeSlot / singlePotionDuration
    );

    if (maxPotionsCanUse <= 0) {
      const remainingHours = Math.floor(
        currentRemainingTime / (60 * 60 * 1000)
      );
      const remainingMinutes = Math.floor(
        (currentRemainingTime % (60 * 60 * 1000)) / (60 * 1000)
      );
      return interaction.editReply({
        content: `Would exceed the 72-hour limit for ${potionConfig.family} effects. Current remaining time: ${remainingHours}h ${remainingMinutes}m`,
      });
    }

    // Determine actual quantity to use
    const actualQuantity = Math.min(quantity, maxPotionsCanUse);
    const totalDurationToAdd = actualQuantity * singlePotionDuration;

    // Apply the potion effects
    if (!character.activeEffects) {
      character.activeEffects = {};
    }

    // Check if we're upgrading to a higher tier - if so, reset duration to prevent exploit
    let newExpiresAt;
    const currentEffect = character.activeEffects[potionConfig.family];
    const isUpgrading =
      currentEffect &&
      currentEffect.tier < potionConfig.tier &&
      currentEffect.expiresAt > currentTime;

    // Capture previous potion info before overwriting (for display purposes)
    let previousPotionName = null;
    let previousPotionEmoji = null;
    if (isUpgrading) {
      previousPotionName = currentEffect.name || "Unknown Potion";
      // Find the previous potion's emoji by searching through all items
      const allItems = configManager.getAllItems();
      const previousPotionItem = Object.values(allItems).find(
        (item) => item.name === previousPotionName
      );
      previousPotionEmoji = previousPotionItem?.emoji || "🧪";
    }

    if (isUpgrading) {
      // Reset duration when upgrading to prevent duration stacking exploit
      newExpiresAt = currentTime + totalDurationToAdd;
    } else {
      // Extend duration only for same tier or when no effect is active
      newExpiresAt =
        currentRemainingTime > 0
          ? character.activeEffects[potionConfig.family].expiresAt +
            totalDurationToAdd
          : currentTime + totalDurationToAdd;
    }

    character.activeEffects[potionConfig.family] = {
      name: itemData.name,
      tier: potionConfig.tier,
      effects: potionConfig.effects,
      expiresAt: newExpiresAt,
      appliedAt: currentTime,
    };

    // Save only active effects to avoid overwriting unrelated fields
    await savePlayerData(userId, { activeEffects: character.activeEffects }, [
      "activeEffects",
    ]);

    // Consume the potions from inventory using the atomic inventory system
    const { updateInventoryAtomically } = require("../utils/inventory");
    await updateInventoryAtomically(
      userId,
      0, // coinsToAdd
      [{ itemKey: itemKey, amount: -actualQuantity }], // itemsToChange - remove potions
      [], // equipmentToAdd
      [], // equipmentIdsToRemove
      0 // bankCoinsToAdd
    );

    // Create success embed
    const totalNewDuration = newExpiresAt - currentTime;
    const durationText = formatDurationShort(totalNewDuration);

    const effectsText = Object.entries(potionConfig.effects)
      .map(([stat, value]) => {
        const statConfig = require("../gameConfig").STATS[stat];
        const statName = statConfig?.name || stat;
        const statEmoji = statConfig?.emoji || "📈";
        return `${statEmoji} +${value} ${statName}`;
      })
      .join("\n");

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(
        `${itemData.emoji || "🧪"} Potion${actualQuantity > 1 ? "s" : ""} Consumed!`
      )
      .setDescription(
        `You consumed ${actualQuantity} **${itemData.name}**${actualQuantity > 1 ? "s" : ""}`
      )
      .addFields(
        {
          name: "Effects Applied",
          value: effectsText,
          inline: true,
        },
        {
          name: "Total Duration",
          value: durationText,
          inline: true,
        }
      );

    if (potionDurationBonus > 0) {
      embed.setFooter({
        text: `+${potionDurationBonus}% potion duration`,
      });
    }

    // Add warning if we couldn't use all requested potions
    if (actualQuantity < quantity) {
      embed.addFields({
        name: "**Notice**",
        value: `Could only use ${actualQuantity} out of ${quantity} potions due to the 72-hour limit for ${potionConfig.family} effects.`,
        inline: false,
      });
    }

    // Add notice if we upgraded from a lower tier
    if (isUpgrading) {
      const overrideMessage = `${previousPotionEmoji} **${previousPotionName}**'s effect was overridden by ${itemData.emoji || "🧪"} **${itemData.name}**'s effect`;
      embed.addFields({
        name: "**Effect Override**",
        value: wrapText(overrideMessage),
        inline: false,
      });
    }

    return interaction.editReply({ embeds: [embed] });
  },

  async handleGodPotionConsumption(
    interaction,
    character,
    itemKey,
    itemData,
    quantity = 1
  ) {
    const userId = interaction.user.id;
    const {
      formatDurationShort,
      applyGodPotion,
      isGodPotionActive,
      getGodPotionRemaining,
    } = require("../utils/potionEffects");
    const { updateInventoryAtomically } = require("../utils/inventory");

    // Clean up expired effects
    require("../utils/potionEffects").cleanupExpiredEffects(character);

    // Calculate duration with Potion Duration bonus
    const allStats = calculateAllStats(character);
    const potionDurationBonus = allStats.POTION_DURATION || 0;
    const baseSeconds = itemData.consumable.baseDurationSeconds || 43200;
    const singleDuration = Math.floor(
      baseSeconds * 1000 * (1 + potionDurationBonus / 100)
    );

    // Determine how many can be used with 72h cap
    const now = Date.now();
    const maxDuration = 72 * 60 * 60 * 1000;
    let currentRemaining = 0;
    if (isGodPotionActive(character)) {
      currentRemaining = getGodPotionRemaining(character);
    }
    const available = maxDuration - currentRemaining;
    const maxUsable = Math.floor(available / singleDuration);
    if (maxUsable <= 0) {
      const remainingHours = Math.floor(currentRemaining / (60 * 60 * 1000));
      const remainingMinutes = Math.floor(
        (currentRemaining % (60 * 60 * 1000)) / (60 * 1000)
      );
      return interaction.editReply({
        content: `Would exceed the 72-hour limit for God Potion. Current remaining time: ${remainingHours}h ${remainingMinutes}m`,
      });
    }

    const actualQty = Math.min(quantity, maxUsable);
    const totalDuration = actualQty * singleDuration;

    // Apply God Potion (also clears other potion effects, but keeps booster cookie intact)
    applyGodPotion(character, totalDuration);
    // Persist only active effects (God Potion) to the database
    await savePlayerData(userId, { activeEffects: character.activeEffects }, [
      "activeEffects",
    ]);

    // Consume items
    await updateInventoryAtomically(
      userId,
      0,
      [{ itemKey, amount: -actualQty }],
      [],
      [],
      0
    );

    // Build response
    const remainingAfter =
      require("../utils/potionEffects").getGodPotionRemaining(character);
    const durationText = formatDurationShort(remainingAfter);
    const configManager = require("../utils/configManager");
    const aggregated =
      require("../utils/potionEffects").computeGodPotionAggregatedEffects(
        configManager
      );

    const embed = new (require("discord.js").EmbedBuilder)()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(
        `${itemData.emoji || "🧪"} God Potion${actualQty > 1 ? "s" : ""} Consumed!`
      )
      .setDescription(
        `You consumed ${actualQty} **${itemData.name}**${actualQty > 1 ? "s" : ""}`
      )
      .addFields(
        {
          name: "Effects Applied",
          value:
            Object.entries(aggregated.effects)
              .map(([stat, value]) => {
                const statConfig = require("../gameConfig").STATS[stat];
                const statName = statConfig?.name || stat;
                const statEmoji = statConfig?.emoji || "📈";
                return `${statEmoji} \`+${value} ${statName}\``;
              })
              .join(" ") || "`No effects`",
          inline: true,
        },
        { name: "Total Duration", value: durationText, inline: true }
      );
    if (potionDurationBonus > 0) {
      embed.setFooter({ text: `+${potionDurationBonus}% potion duration` });
    }
    if (actualQty < quantity) {
      embed.addFields({
        name: "Notice",
        value: `Could only use ${actualQty} out of ${quantity} due to the 72-hour cap.`,
        inline: false,
      });
    }

    return interaction.editReply({ embeds: [embed] });
  },

  async handleBoosterCookieConsumption(
    interaction,
    character,
    itemKey,
    itemData,
    quantity = 1
  ) {
    const userId = interaction.user.id;

    // Trigger bits collection before consuming new cookies
    const { collectPendingBits } = require("../utils/bitsManager");
    const bitsResult = await collectPendingBits(userId, character);

    // Update character with collected bits
    character = bitsResult.character;

    // Activate booster cookie effect
    const { activateBoosterCookie } = require("../utils/boosterCookieManager");
    const cookieResult = await activateBoosterCookie(
      userId,
      character,
      quantity
    );

    if (!cookieResult.success) {
      return interaction.editReply({
        content: "Failed to activate booster cookie effect. Please try again.",
      });
    }

    // Consume the cookies from inventory
    const { updateInventoryAtomically } = require("../utils/inventory");
    await updateInventoryAtomically(
      userId,
      0, // coinsToAdd
      [{ itemKey: itemKey, amount: -quantity }], // itemsToChange - remove cookies
      [], // equipmentToAdd
      [], // equipmentIdsToRemove
      0 // bankCoinsToAdd
    );

    // Create success embed
    // Create Discord timestamp for when the cookie expires
    const expiryTimestamp = Math.floor(cookieResult.newExpiryTime / 1000);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(
        `${itemData.emoji || "🍪"} Booster Cookie${quantity > 1 ? "s" : ""} Consumed!`
      )
      .setDescription(
        `You consumed ${quantity} **${itemData.name}**${quantity > 1 ? "s" : ""}\n\n**Total Duration**: Expires <t:${expiryTimestamp}:R>\n**${BITS_EMOJI} Available Bits**: ${cookieResult.totalBitsAvailable.toLocaleString()}/${cookieResult.totalBaseBits.toLocaleString()}`
      );

    return interaction.editReply({ embeds: [embed] });
  },

  async autocomplete(interaction) {
    const focusedValue = interaction.options.getFocused().toLowerCase();
    const userId = interaction.user.id;
    const character = await getPlayerData(userId);

    if (!character || !character.inventory?.items) {
      return interaction.respond([]);
    }

    const allItems = configManager.getAllItems();
    const playerInventory = character.inventory.items;

    // Get consumable items from player's inventory
    const consumableItems = Object.entries(playerInventory)
      .filter(([itemKey, count]) => {
        const itemData = allItems[itemKey];
        return count > 0 && itemData && itemData.consumable;
      })
      .map(([itemKey, count]) => {
        const itemData = allItems[itemKey];
        return {
          name: `${itemData.name} (${count})`,
          value: itemKey,
          item: itemData,
        };
      })
      .filter(
        (item) =>
          item.name.toLowerCase().includes(focusedValue) ||
          item.value.toLowerCase().includes(focusedValue)
      )
      .slice(0, 25);

    await interaction.respond(
      consumableItems.map((item) => ({
        name: item.name,
        value: item.value,
      }))
    );
  },
};
