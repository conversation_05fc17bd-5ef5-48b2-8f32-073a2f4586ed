/**
 * Data-driven configuration for skill level up rewards
 * Central location for defining rewards for all skills
 */

const { STATS, CURRENCY } = require("../gameConfig");

/**
 * Core reward system for all skills
 * Each skill has:
 * - rewards: Array of reward definitions that apply to specific levels
 * - coinRewards: Optional array of coin rewards per level
 */
const skillRewards = {
  fishing: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +2 Health for levels 1-14
        levels: Array.from({ length: 14 }, (_, i) => i + 1),
        stat: "HEALTH",
        value: 2,
      },
      {
        // Add +3 Health for levels 15-19
        levels: Array.from({ length: 5 }, (_, i) => i + 15),
        stat: "HEALTH",
        value: 3,
      },
      {
        // Add +4 Health for levels 20-25
        levels: Array.from({ length: 6 }, (_, i) => i + 20),
        stat: "HEALTH",
        value: 4,
      },
      {
        // Add +5 Health for levels 26-50
        levels: Array.from({ length: 25 }, (_, i) => i + 26),
        stat: "HEALTH",
        value: 5,
      },
      {
        // Add +0.1 Treasure Chance for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "TREASURE_CHANCE",
        value: 0.1,
        displayValue: (value) => value.toFixed(1), // Format to 1 decimal place
      },
    ],
  },

  mining: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +1 Defense for levels 1-14
        levels: Array.from({ length: 14 }, (_, i) => i + 1),
        stat: "DEFENSE",
        value: 1,
      },
      {
        // Add +2 Defense for levels 15-50
        levels: Array.from({ length: 36 }, (_, i) => i + 15),
        stat: "DEFENSE",
        value: 2,
      },
      {
        // Add +4 Mining Fortune for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "MINING_FORTUNE",
        value: 4,
      },
    ],
  },

  farming: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +2 Health for levels 1-14
        levels: Array.from({ length: 14 }, (_, i) => i + 1),
        stat: "HEALTH",
        value: 2,
      },
      {
        // Add +3 Health for levels 15-19
        levels: Array.from({ length: 5 }, (_, i) => i + 15),
        stat: "HEALTH",
        value: 3,
      },
      {
        // Add +4 Health for levels 20-25
        levels: Array.from({ length: 6 }, (_, i) => i + 20),
        stat: "HEALTH",
        value: 4,
      },
      {
        // Add +5 Health for levels 26-50
        levels: Array.from({ length: 25 }, (_, i) => i + 26),
        stat: "HEALTH",
        value: 5,
      },
      {
        // Add +4 Farming Fortune for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "FARMING_FORTUNE",
        value: 4,
      },
    ],
  },

  foraging: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +1 Strength for levels 1-14
        levels: Array.from({ length: 14 }, (_, i) => i + 1),
        stat: "STRENGTH",
        value: 1,
      },
      {
        // Add +2 Strength for levels 15-50
        levels: Array.from({ length: 36 }, (_, i) => i + 15),
        stat: "STRENGTH",
        value: 2,
      },
      {
        // Add +4 Foraging Fortune for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "FORAGING_FORTUNE",
        value: 4,
      },
    ],
  },

  combat: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +0.5 Crit Chance for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "CRIT_CHANCE",
        value: 0.5,
      },
      {
        // Add +4% Damage for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "DAMAGE_MULT",
        value: 4,
      },
      {
        // Add +0.2 Dodge Chance per level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "DODGE_CHANCE",
        value: 0.2,
      },
    ],
  },

  crafting: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +1 Health for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "HEALTH",
        value: 1,
      },
      {
        // Add +1 Wardrobe Slot at levels 5, 10, and 15
        levels: [5, 10, 15],
        type: "WARDROBE_SLOT",
        value: 1,
      },
    ],
  },

  enchanting: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +1 Intelligence for levels 1-14
        levels: Array.from({ length: 14 }, (_, i) => i + 1),
        stat: "INTELLIGENCE",
        value: 1,
      },
      {
        // Add +2 Intelligence for levels 15-50
        levels: Array.from({ length: 36 }, (_, i) => i + 15),
        stat: "INTELLIGENCE",
        value: 2,
      },
    ],
  },

  taming: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +1% Pet EXP gain per level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "PET_EXP",
        value: 1,
      },
    ],
  },

  alchemy: {
    coinRewards: [
      0, 100, 250, 500, 750, 1000, 2000, 3000, 4000, 5000, 7500, 10000, 15000,
      20000, 25000, 30000, 40000, 50000, 65000, 80000, 100000, 125000, 150000,
      175000, 200000, 225000, 250000, 275000, 300000, 325000, 350000, 375000,
      400000, 425000, 450000, 475000, 500000, 550000, 600000, 650000, 700000,
      750000, 800000, 850000, 900000, 1000000, 1000000, 1000000, 1000000,
      1000000, 1000000,
    ],
    rewards: [
      {
        // Add +1 Intelligence for levels 1-14
        levels: Array.from({ length: 14 }, (_, i) => i + 1),
        stat: "INTELLIGENCE",
        value: 1,
      },
      {
        // Add +2 Intelligence for levels 15-50
        levels: Array.from({ length: 36 }, (_, i) => i + 15),
        stat: "INTELLIGENCE",
        value: 2,
      },
      {
        // Add +1% Potion Duration for every level 1-50
        levels: Array.from({ length: 50 }, (_, i) => i + 1),
        stat: "POTION_DURATION",
        value: 1,
        displayValue: (value) => `${value}%`, // Format as percentage
      },
    ],
  },
};

/**
 * Gets the skill rewards for a specific level
 * @param {string} skill - The skill name
 * @param {number} level - The level to get rewards for
 * @returns {object} Object with reward info
 */
function getSkillRewardsForLevel(skill, level) {
  if (!skillRewards[skill]) {
    console.warn(`[SkillRewards] No rewards defined for skill: ${skill}`);
    return { statRewards: [], specialRewards: [], coinReward: 0 };
  }

  const skillConfig = skillRewards[skill];
  const statRewards = [];
  const specialRewards = [];

  // Check each reward definition and see if this level is included
  if (skillConfig.rewards) {
    for (const rewardDef of skillConfig.rewards) {
      if (rewardDef.levels.includes(level)) {
        const displayValue = rewardDef.displayValue
          ? rewardDef.displayValue(rewardDef.value)
          : rewardDef.value.toString();

        if (rewardDef.stat) {
          // This is a stat reward
          statRewards.push({
            stat: rewardDef.stat,
            value: rewardDef.value,
            displayValue: displayValue,
          });
        } else if (rewardDef.type) {
          // This is a special reward (like wardrobe slot)
          specialRewards.push({
            type: rewardDef.type,
            value: rewardDef.value,
            displayValue: displayValue,
          });
        }
      }
    }
  }

  // Get coin reward for this level
  const coinReward =
    skillConfig.coinRewards && skillConfig.coinRewards[level]
      ? skillConfig.coinRewards[level]
      : 0;

  return {
    statRewards,
    specialRewards,
    coinReward,
  };
}

/**
 * Formats a list of rewards for display in an embed
 * @param {Array} statRewards - Array of stat reward objects
 * @param {Array} specialRewards - Array of special reward objects
 * @param {number} coinReward - Coin reward amount
 * @param {string} skillName - The skill name (for calculating skill action rewards)
 * @param {number} level - The level reached (for calculating skill action rewards)
 * @returns {string} Formatted rewards text
 *
 * Note: The skillName and level parameters are used to automatically calculate
 * and display "+X Skill Actions" rewards based on the skill action limit system.
 * If these are not provided, no skill action rewards will be shown.
 */
function formatRewardsText(
  statRewards,
  specialRewards,
  coinReward,
  skillName = null,
  level = null
) {
  const rewardTexts = [];

  // Add coin reward first if present
  if (coinReward > 0) {
    rewardTexts.push(
      `${CURRENCY.emoji} \`${coinReward.toLocaleString()} Coins\``
    );
  }

  // Add stat rewards
  for (const reward of statRewards) {
    if (!STATS[reward.stat]) {
      console.warn(`[SkillRewards] Unknown stat in reward: ${reward.stat}`);
      continue;
    }

    const statConfig = STATS[reward.stat];
    const prefix = reward.value > 0 ? "+" : "";

    rewardTexts.push(
      `${statConfig.emoji} \`${prefix}${reward.displayValue} ${statConfig.name}\``
    );
  }

  // Add special rewards
  for (const reward of specialRewards) {
    if (reward.type === "WARDROBE_SLOT") {
      const prefix = reward.value > 0 ? "+" : "";
      rewardTexts.push(
        `<:wardrobe:1387376984304128033> **${prefix}${reward.displayValue} Wardrobe Slot**`
      );
    } else {
      console.warn(
        `[SkillRewards] Unknown special reward type: ${reward.type}`
      );
    }
  }

  // Add skill action reward last if skill name and level are provided
  // Exclude certain skills as they don't use action systems
  const EXCLUDED_SKILLS_FOR_ACTION_REWARD = [
    "crafting",
    "taming",
    "enchanting",
  ];
  if (
    skillName &&
    level &&
    !EXCLUDED_SKILLS_FOR_ACTION_REWARD.includes(skillName)
  ) {
    const { getSkillActionRewardText } = require("../utils/skillActionRewards");
    const actionRewardText = getSkillActionRewardText(skillName, level);
    if (actionRewardText) {
      rewardTexts.push(actionRewardText);
    }
  }

  return rewardTexts.length > 0
    ? rewardTexts.join("\n")
    : "No rewards for this level.";
}

module.exports = {
  skillRewards,
  getSkillRewardsForLevel,
  formatRewardsText,
};
