const { MessageFlags } = require("discord.js");
const { handleSkillActionExecution } = require("../utils/skillActionUtils");

async function handleGenericSkillAction(
  interaction,
  character,
  resourceKey,
  amount,
  isAgain,
  skillConfig,
  wasMax = false
) {
  try {
    // Check if skill has custom handler (for Combat unification)
    if (skillConfig.customHandler) {
      await skillConfig.customHandler(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        skillConfig,
        wasMax
      );
    } else {
      // Use standard skill execution
      await handleSkillActionExecution(
        interaction,
        character,
        resourceKey,
        amount,
        isAgain,
        skillConfig,
        wasMax
      );
    }
  } catch (error) {
    // Generic error handling
    console.error("[GenericSkillHandler] Error during skill action:", error);
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "A critical error occurred. Please check logs.",
          flags: [MessageFlags.Ephemeral],
        });
      } else {
        // Use channel.send() for errors during long-running actions to avoid interaction timeout
        const channel = interaction.channel;
        if (channel) {
          await channel.send({
            content: `<@${interaction.user.id}> A critical error occurred during your action. Please check with support.`,
          });
        }
      }
    } catch (e) {
      // Log but do not rethrow
      console.error(
        "[GenericSkillHandler] Failed to send error message to user:",
        e
      );
    }
  }
}

module.exports = { handleGenericSkillAction };
