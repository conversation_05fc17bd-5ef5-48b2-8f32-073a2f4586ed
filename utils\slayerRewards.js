/**
 * Centralized Slayer Rewards System
 * Single source of truth for all slayer level rewards
 */

// Import centralized stat definitions
const { getStatEmoji } = require("./statUtils");

const configManager = require("./configManager");

function getItemsData() {
  return configManager.getAllItems();
}

/**
 * Centralized slayer stat rewards definition
 */
const SLAYER_STAT_REWARDS = {
  zombie: {
    1: [{ stat: "HEALTH", value: 2 }],
    2: [{ stat: "HEALTH", value: 2 }],
    3: [{ stat: "HEALTH", value: 3 }],
    4: [{ stat: "HEALTH", value: 3 }],
    5: [{ stat: "HEALTH", value: 4 }],
    6: [{ stat: "HEALTH", value: 4 }],
    7: [{ stat: "HEALTH", value: 5 }],
    8: [
      { stat: "HEALTH", value: 5 },
      { stat: "HEALTH_REGEN", value: 50 },
    ],
    9: [{ stat: "HEALTH", value: 6 }],
  },
  spider: {
    1: [{ stat: "CRIT_DAMAGE", value: 1 }],
    2: [{ stat: "CRIT_DAMAGE", value: 1 }],
    3: [{ stat: "CRIT_DAMAGE", value: 1 }],
    4: [{ stat: "CRIT_DAMAGE", value: 1 }],
    5: [{ stat: "CRIT_DAMAGE", value: 2 }],
    6: [{ stat: "CRIT_DAMAGE", value: 2 }],
    7: [{ stat: "CRIT_DAMAGE", value: 2 }],
    8: [
      { stat: "CRIT_DAMAGE", value: 3 },
      { stat: "ALCHEMY_WISDOM", value: 10 },
    ],
    9: [{ stat: "CRIT_DAMAGE", value: 3 }],
  },
};

/**
 * Centralized slayer item rewards definition
 */
const SLAYER_ITEM_REWARDS = {
  zombie: {
    5: { itemKey: "BEHEADED_HORROR", amount: 1 },
    6: { itemKey: "REVENANT_CATALYST", amount: 1 },
  },
  spider: {
    // Spider slayer item rewards - Spider Catalyst dropped from Broodfather III
  },
};

/**
 * Get stat rewards for a specific slayer type and level
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} level - The slayer level
 * @returns {Array} Array of stat reward objects
 */
function getStatRewardsForLevel(slayerType, level) {
  return SLAYER_STAT_REWARDS[slayerType]?.[level] || [];
}

/**
 * Get item rewards for a specific slayer type and level
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} level - The slayer level
 * @returns {Array} Array of item reward objects with names and emojis
 */
function getItemRewardsForLevel(slayerType, level) {
  const rewards = SLAYER_ITEM_REWARDS[slayerType]?.[level];
  if (!rewards) return [];

  const itemsData = getItemsData();
  return [
    {
      itemKey: rewards.itemKey,
      amount: rewards.amount,
      name:
        itemsData[rewards.itemKey]?.name || rewards.itemKey.replace("_", " "),
      emoji: itemsData[rewards.itemKey]?.emoji || "❓",
    },
  ];
}

/**
 * Get all stat rewards earned up to a specific level
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} currentLevel - The current slayer level
 * @returns {object} Object with totals for each stat type
 */
function getTotalStatRewards(slayerType, currentLevel) {
  const totals = {};

  for (let level = 1; level <= currentLevel; level++) {
    const rewards = getStatRewardsForLevel(slayerType, level);
    for (const reward of rewards) {
      totals[reward.stat] = (totals[reward.stat] || 0) + reward.value;
    }
  }

  return totals;
}

/**
 * Get all item rewards earned up to a specific level
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} currentLevel - The current slayer level
 * @returns {Array} Array of all item rewards earned
 */
function getAllItemRewards(slayerType, currentLevel) {
  const allRewards = [];

  for (let level = 1; level <= currentLevel; level++) {
    const rewards = getItemRewardsForLevel(slayerType, level);
    allRewards.push(...rewards);
  }

  return allRewards;
}

/**
 * Get crafting recipe rewards for a specific slayer type and level
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} level - The slayer level
 * @returns {Array} Array of crafting recipe objects with names and emojis
 */
function getCraftingRewardsForLevel(slayerType, level) {
  const itemsData = getItemsData();
  const recipes = [];

  // Search through all items to find ones that require this slayer level
  for (const [itemKey, itemData] of Object.entries(itemsData)) {
    if (itemData.craftingRequirements?.slayers?.[slayerType] === level) {
      recipes.push({
        itemKey,
        name: itemData.name,
        emoji: itemData.emoji || "❓",
        rarity: itemData.rarity?.name || "Common",
      });
    }
  }

  return recipes;
}

/**
 * Get all crafting recipe rewards unlocked up to a specific level
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} currentLevel - The current slayer level
 * @returns {Array} Array of all crafting recipe rewards unlocked
 */
function getAllCraftingRewards(slayerType, currentLevel) {
  const allRewards = [];

  for (let level = 1; level <= currentLevel; level++) {
    const rewards = getCraftingRewardsForLevel(slayerType, level);
    allRewards.push(...rewards);
  }

  return allRewards;
}

/**
 * Get the maximum level that has crafting rewards for a slayer type
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @returns {number} Maximum level with crafting rewards
 */
function getMaxCraftingRewardLevel(slayerType) {
  const itemsData = getItemsData();
  let maxLevel = 0;

  // Search through all items to find the highest required slayer level
  for (const [, itemData] of Object.entries(itemsData)) {
    if (itemData.craftingRequirements?.slayers?.[slayerType]) {
      maxLevel = Math.max(
        maxLevel,
        itemData.craftingRequirements.slayers[slayerType]
      );
    }
  }

  return maxLevel;
}

/**
 * Get the maximum level that has rewards for a slayer type (including crafting rewards)
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @returns {number} Maximum level with rewards
 */
function getMaxRewardLevel(slayerType) {
  const statLevels = Object.keys(SLAYER_STAT_REWARDS[slayerType] || {}).map(
    Number
  );
  const itemLevels = Object.keys(SLAYER_ITEM_REWARDS[slayerType] || {}).map(
    Number
  );
  const craftingMaxLevel = getMaxCraftingRewardLevel(slayerType);
  const allLevels = [...statLevels, ...itemLevels, craftingMaxLevel];
  return allLevels.length > 0 ? Math.max(...allLevels) : 0;
}

/**
 * Check if a slayer type has any rewards defined
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @returns {boolean} True if rewards are defined
 */
function hasRewards(slayerType) {
  return (
    Object.keys(SLAYER_STAT_REWARDS[slayerType] || {}).length > 0 ||
    Object.keys(SLAYER_ITEM_REWARDS[slayerType] || {}).length > 0
  );
}

/**
 * Get formatted rewards text for display purposes
 * @param {string} slayerType - The slayer type (zombie/spider)
 * @param {number} currentLevel - The current slayer level
 * @returns {object} Object with currentRewards and nextRewards text arrays
 */
function getFormattedRewardsText(slayerType, currentLevel) {
  const result = {
    currentRewards: [],
    nextRewards: [],
  };

  if (!hasRewards(slayerType)) {
    result.currentRewards.push(
      `*${slayerType.charAt(0).toUpperCase() + slayerType.slice(1)} Slayer rewards coming soon!*`
    );
    return result;
  }

  // Current rewards
  if (currentLevel > 0) {
    const totalStats = getTotalStatRewards(slayerType, currentLevel);
    const earnedItems = getAllItemRewards(slayerType, currentLevel);
    const earnedCrafting = getAllCraftingRewards(slayerType, currentLevel);

    // Format stat rewards using centralized emojis
    for (const [stat, total] of Object.entries(totalStats)) {
      if (total > 0) {
        const emoji = getStatEmoji(stat);
        const statName = stat
          .replace("_", " ")
          .toLowerCase()
          .replace(/\b\w/g, (l) => l.toUpperCase());
        result.currentRewards.push(`${emoji} **+${total}** ${statName}`);
      }
    }

    // Format item rewards
    for (const item of earnedItems) {
      result.currentRewards.push(`${item.emoji} ${item.name}`);
    }

    // Format crafting recipe rewards
    for (const recipe of earnedCrafting) {
      result.currentRewards.push(`${recipe.emoji} ${recipe.name} Recipe`);
    }
  }

  // Next rewards
  const nextLevel = currentLevel + 1;
  const maxLevel = getMaxRewardLevel(slayerType);

  if (nextLevel <= maxLevel) {
    const nextStatRewards = getStatRewardsForLevel(slayerType, nextLevel);
    const nextItemRewards = getItemRewardsForLevel(slayerType, nextLevel);
    const nextCraftingRewards = getCraftingRewardsForLevel(
      slayerType,
      nextLevel
    );

    // Format stat rewards using centralized emojis
    for (const reward of nextStatRewards) {
      const emoji = getStatEmoji(reward.stat);
      const statName = reward.stat
        .replace("_", " ")
        .toLowerCase()
        .replace(/\b\w/g, (l) => l.toUpperCase());
      result.nextRewards.push(`${emoji} **+${reward.value}** ${statName}`);
    }

    // Format item rewards
    for (const item of nextItemRewards) {
      result.nextRewards.push(`${item.emoji} ${item.name}`);
    }

    // Format crafting recipe rewards
    for (const recipe of nextCraftingRewards) {
      result.nextRewards.push(`${recipe.emoji} ${recipe.name} Recipe`);
    }
  }

  return result;
}

module.exports = {
  SLAYER_STAT_REWARDS,
  SLAYER_ITEM_REWARDS,
  getStatRewardsForLevel,
  getItemRewardsForLevel,
  getTotalStatRewards,
  getAllItemRewards,
  getMaxRewardLevel,
  hasRewards,
  getFormattedRewardsText,
  getStatEmoji,
  getMaxCraftingRewardLevel,
  getCraftingRewardsForLevel,
  getAllCraftingRewards,
};
