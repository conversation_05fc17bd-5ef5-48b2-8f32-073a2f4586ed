/**
 * Damage Calculation Context System
 *
 * This system handles the collection and calculation of all damage multipliers
 * according to the Hypixel Skyblock damage formula (with static multiplier):
 *
 * DamageDealt = ((5 + BaseDamage) * (1 + Strength/100) * AdditiveMultiplier * MultiplicativeMultiplier * StaticMultiplier + BonusModifiers) * (1 + CritDamage/100)
 *
 * Where:
 * - AdditiveMultiplier = 1 + (sum of all additive percentages) / 100
 * - MultiplicativeMultiplier = product of all multiplicative multipliers
 * - StaticMultiplier = configurable constant to compensate for turn-based combat
 * - BonusModifiers = sum of all flat bonus damage (Giant Killer, Execute, Smite, etc.)
 */

const configManager = require("./configManager");
const { parseDataJsonCached } = require("./dataJsonCache");
const { ENCHANTMENTS } = require("../commands/enchant");

/**
 * Creates a damage calculation context for a character
 * @param {Object} character - The character object
 * @param {Object} mobInstanceData - The mob being fought (optional, for conditional bonuses)
 * @returns {Object} Damage calculation context
 */
function createDamageCalculationContext(character, mobInstanceData = null) {
  const context = {
    additiveMultipliers: [],
    multiplicativeMultipliers: [],
    bonusModifiers: [],

    // Calculated values
    totalAdditiveMultiplier: 1,
    totalMultiplicativeMultiplier: 1,
    totalBonusModifiers: 0,

    // Debug info
    sources: {
      additive: [],
      multiplicative: [],
      bonus: [],
    },
  };

  // Collect damage multipliers from various sources
  collectSkillMultipliers(context, character);
  collectEquipmentMultipliers(context, character, mobInstanceData);
  collectSetBonusMultipliers(context, character);
  collectPetMultipliers(context, character);

  // Calculate final values
  calculateFinalMultipliers(context);

  return context;
}

/**
 * Collect damage multipliers from skill levels
 */
function collectSkillMultipliers(context, character) {
  // Combat skill levels provide additive damage multiplier via DAMAGE_MULT stat
  const damageMultFromLevels = character.stats?.DAMAGE_MULT?.fromLevels || 0;

  if (damageMultFromLevels > 0) {
    context.additiveMultipliers.push(damageMultFromLevels);
    context.sources.additive.push({
      source: "Combat Skill Levels",
      value: damageMultFromLevels,
      description: `+${damageMultFromLevels}% from skill levels`,
    });
  }
}

/**
 * Collect damage multipliers from equipment (weapons, armor, enchantments)
 */
function collectEquipmentMultipliers(context, character, mobInstanceData) {
  if (!character.inventory?.equipment) return;

  character.inventory.equipment.forEach((eq) => {
    if (!eq.isEquipped) return;

    const itemDetails = configManager.getAllItems()[eq.itemKey];
    if (!itemDetails) return;

    // Process enchantments
    if (eq.data_json) {
      const data = parseDataJsonCached(eq.data_json, eq.id || eq.equipment_id);

      if (data?.enchantments) {
        processEnchantmentMultipliers(
          context,
          data.enchantments,
          itemDetails,
          mobInstanceData,
          character
        );
      }
    }

    // Process weapon abilities
    if (itemDetails.abilities) {
      processWeaponAbilities(context, itemDetails.abilities, mobInstanceData);
    }

    // Process single ability (for items like Spider Sword that use "ability" instead of "abilities")
    if (itemDetails.ability) {
      processWeaponAbilities(
        context,
        { singleAbility: itemDetails.ability },
        mobInstanceData
      );
    }
  });
}

/**
 * Process enchantment-based damage multipliers
 */
function processEnchantmentMultipliers(
  context,
  enchantments,
  itemDetails,
  mobInstanceData,
  character
) {
  // Sharpness - Additive damage multiplier
  if (enchantments.SHARPNESS) {
    const level = enchantments.SHARPNESS;
    const damageBonus = level === 5 ? 30 : level * 5;

    context.additiveMultipliers.push(damageBonus);
    context.sources.additive.push({
      source: "Sharpness Enchantment",
      value: damageBonus,
      description: `+${damageBonus}% from Sharpness ${level}`,
    });
  }

  // Smite - Conditional bonus modifier (only vs Undead)
  if (enchantments.SMITE && mobInstanceData?.mobType === "Undead") {
    const level = enchantments.SMITE;
    const damageBonus = ENCHANTMENTS.SMITE.undeadDamageBonus?.[level] || 0;

    if (damageBonus > 0) {
      // Smite is a percentage of base damage, calculated as bonus modifier
      // This will be calculated later when we have the base damage
      context.conditionalBonuses = context.conditionalBonuses || [];
      context.conditionalBonuses.push({
        type: "SMITE",
        value: damageBonus,
        description: `+${damageBonus}% vs Undead from Smite ${level}`,
      });
    }
  }

  // Bane of Arthropods - Conditional bonus modifier (only vs Arthropods)
  if (
    enchantments.BANE_OF_ARTHROPODS &&
    mobInstanceData?.mobType === "Arthropod"
  ) {
    const level = enchantments.BANE_OF_ARTHROPODS;
    const damageBonus =
      ENCHANTMENTS.BANE_OF_ARTHROPODS.arthropodDamageBonus?.[level] || 0;

    if (damageBonus > 0) {
      // Bane of Arthropods is a percentage of base damage, calculated as bonus modifier
      // This will be calculated later when we have the base damage
      context.conditionalBonuses = context.conditionalBonuses || [];
      context.conditionalBonuses.push({
        type: "BANE_OF_ARTHROPODS",
        value: damageBonus,
        description: `+${damageBonus}% vs Arthropods from Bane of Arthropods ${level}`,
      });
    }
  }

  // Giant Killer - Conditional bonus modifier
  if (enchantments.GIANT_KILLER && mobInstanceData) {
    const level = enchantments.GIANT_KILLER;
    const mobMaxHealth = mobInstanceData.stats?.health || 0;
    const characterMaxHealth =
      character.totalStats?.HEALTH || character.stats?.HEALTH?.base || 100;

    if (mobMaxHealth > characterMaxHealth) {
      const extraHealth = mobMaxHealth - characterMaxHealth;
      const extraHealthPercent = (extraHealth / characterMaxHealth) * 100;

      const damageIncreasePerExtraHealthPercent =
        ENCHANTMENTS.GIANT_KILLER.damageIncreasePerExtraHealthPercent?.[
          level
        ] || 0;
      const maxDamageBonus =
        ENCHANTMENTS.GIANT_KILLER.maxDamageBonus?.[level] || 0;

      // Calculate bonus with proper Hypixel cap per level
      const uncappedBonusPercent =
        extraHealthPercent * damageIncreasePerExtraHealthPercent;
      const bonusPercent = Math.min(uncappedBonusPercent, maxDamageBonus);

      if (bonusPercent > 0) {
        context.conditionalBonuses = context.conditionalBonuses || [];
        context.conditionalBonuses.push({
          type: "GIANT_KILLER",
          value: bonusPercent,
          description: `+${bonusPercent.toFixed(1)}% vs high HP mobs from Giant Killer ${level} (${extraHealthPercent.toFixed(0)}% extra HP, capped at ${maxDamageBonus}%)`,
        });
      }
    }
  }

  // Execute - Conditional bonus modifier (based on mob's missing health)
  if (enchantments.EXECUTE && mobInstanceData) {
    const level = enchantments.EXECUTE;
    // Execute bonus will be calculated dynamically during combat based on mob's current health
    // We'll store the enchantment info for later use
    context.executeEnchantment = {
      level: level,
      damageIncreasePerMissingPercent:
        ENCHANTMENTS.EXECUTE.missingHealthDamagePercent?.[level] || 0,
    };
  }
}

/**
 * Process weapon ability multipliers
 */
function processWeaponAbilities(context, abilities, mobInstanceData) {
  // Handle both single ability and multiple abilities
  const abilitiesToProcess = abilities.singleAbility
    ? { singleAbility: abilities.singleAbility }
    : abilities;

  for (const [, ability] of Object.entries(abilitiesToProcess)) {
    // Check if current mob is valid for this ability
    let isTargetMob = false;

    if (ability.targetMobType && mobInstanceData?.mobType) {
      isTargetMob =
        mobInstanceData.mobType.toLowerCase() ===
        ability.targetMobType.toLowerCase();
    }

    if (ability.targetMobs && mobInstanceData?.key) {
      isTargetMob = ability.targetMobs.some((targetMob) =>
        mobInstanceData.key.toUpperCase().includes(targetMob.toUpperCase())
      );
    }

    if (isTargetMob) {
      if (ability.type === "MOB_DAMAGE_BONUS") {
        const damageMultiplier = ability.damageMultiplier || 1;
        const additiveBonus = (damageMultiplier - 1) * 100;

        context.additiveMultipliers.push(additiveBonus);
        context.sources.additive.push({
          source: ability.name || "Weapon Ability",
          value: additiveBonus,
          description: `+${additiveBonus}% vs ${ability.targetMobType || "specific mobs"}`,
        });
      }
    }
  }
}

/**
 * Collect damage multipliers from set bonuses
 */
function collectSetBonusMultipliers(context, character) {
  // Set bonuses are already applied to character stats
  // Any damage multipliers from sets would be in character.stats.DAMAGE_MULT.fromSetBonus
  const damageMultFromSetBonus =
    character.stats?.DAMAGE_MULT?.fromSetBonus || 0;

  if (damageMultFromSetBonus > 0) {
    context.additiveMultipliers.push(damageMultFromSetBonus);
    context.sources.additive.push({
      source: "Set Bonuses",
      value: damageMultFromSetBonus,
      description: `+${damageMultFromSetBonus}% from armor set bonuses`,
    });
  }
}

/**
 * Collect damage multipliers from pets
 */
function collectPetMultipliers(context, character) {
  // Pet damage multipliers would be in character.stats.DAMAGE_MULT.fromPet
  const damageMultFromPet = character.stats?.DAMAGE_MULT?.fromPet || 0;

  if (damageMultFromPet > 0) {
    context.additiveMultipliers.push(damageMultFromPet);
    context.sources.additive.push({
      source: "Pet Bonuses",
      value: damageMultFromPet,
      description: `+${damageMultFromPet}% from active pet`,
    });
  }
}

/**
 * Calculate final multiplier values
 */
function calculateFinalMultipliers(context) {
  // Calculate total additive multiplier
  const totalAdditivePercent = context.additiveMultipliers.reduce(
    (sum, mult) => sum + mult,
    0
  );
  context.totalAdditiveMultiplier = 1 + totalAdditivePercent / 100;

  // Calculate total multiplicative multiplier
  context.totalMultiplicativeMultiplier =
    context.multiplicativeMultipliers.reduce(
      (product, mult) => product * mult,
      1
    );

  // Bonus modifiers will be calculated during actual damage calculation
  // since they depend on the base damage value
}

/**
 * Calculate conditional bonus modifiers that depend on base damage
 * @param {Object} context - Damage calculation context
 * @param {number} baseDamage - Base damage before bonuses
 * @param {number} strength - Character strength
 * @param {Object} mobInstanceData - Current mob data (for Execute enchantment)
 * @returns {number} Total bonus modifier damage
 */
function calculateConditionalBonuses(
  context,
  baseDamage,
  strength,
  mobInstanceData = null
) {
  let totalBonusModifiers = 0;

  if (!context.conditionalBonuses) return totalBonusModifiers;

  // Calculate pre-crit damage for percentage-based bonuses (including static multiplier)
  const { STATIC_DAMAGE_MULTIPLIER } = require("../gameConfig");
  const preCritDamage =
    (5 + baseDamage) *
    (1 + strength / 100) *
    context.totalAdditiveMultiplier *
    context.totalMultiplicativeMultiplier *
    STATIC_DAMAGE_MULTIPLIER;

  for (const bonus of context.conditionalBonuses) {
    let bonusAmount = 0;

    switch (bonus.type) {
      case "SMITE":
      case "BANE_OF_ARTHROPODS":
      case "GIANT_KILLER":
        bonusAmount = preCritDamage * (bonus.value / 100);
        break;
    }

    totalBonusModifiers += bonusAmount;

    // Update sources for debugging
    context.sources.bonus.push({
      source: bonus.type,
      value: bonusAmount,
      description: bonus.description,
    });
  }

  // Handle Execute enchantment (dynamic based on mob health)
  if (context.executeEnchantment && mobInstanceData) {
    const mobMaxHealth = mobInstanceData.stats?.health || 1;
    const mobCurrentHealth = mobInstanceData.current_health || mobMaxHealth;
    const missingHealthPercent = 1 - mobCurrentHealth / mobMaxHealth;

    const executeBonus =
      missingHealthPercent *
      context.executeEnchantment.damageIncreasePerMissingPercent *
      100;
    const executeBonusAmount = preCritDamage * (executeBonus / 100);

    totalBonusModifiers += executeBonusAmount;

    context.sources.bonus.push({
      source: "Execute Enchantment",
      value: executeBonusAmount,
      description: `+${executeBonus.toFixed(1)}% from Execute (${(missingHealthPercent * 100).toFixed(1)}% missing health)`,
    });
  }

  context.totalBonusModifiers = totalBonusModifiers;
  return totalBonusModifiers;
}

/**
 * Get a summary of all damage multipliers for display purposes
 * @param {Object} context - Damage calculation context
 * @returns {Object} Summary of multipliers
 */
function getDamageMultiplierSummary(context) {
  return {
    additiveMultiplier: context.totalAdditiveMultiplier,
    multiplicativeMultiplier: context.totalMultiplicativeMultiplier,
    bonusModifiers: context.totalBonusModifiers,
    sources: context.sources,
    breakdown: {
      additive: context.additiveMultipliers,
      multiplicative: context.multiplicativeMultipliers,
      totalAdditivePercent:
        ((context.totalAdditiveMultiplier - 1) * 100).toFixed(1) + "%",
    },
  };
}

module.exports = {
  createDamageCalculationContext,
  calculateConditionalBonuses,
  getDamageMultiplierSummary,
};
