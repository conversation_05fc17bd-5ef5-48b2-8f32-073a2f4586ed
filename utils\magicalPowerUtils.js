// Calculates a player's total Magical Power based on equipped accessories only.
// Shared utility so multiple systems (Maxwell menu, XP system, etc.) can stay DRY.

const configManager = require("./configManager");
const { dbAll } = require("./dbUtils");
const { MAGICAL_POWER_VALUES } = require("./accessoryPowers");

/**
 * Calculate Magical Power from a player's EQUIPPED accessories.
 * Counts each accessory type once (duplicates ignored).
 *
 * @param {string} userId Discord user ID
 * @returns {Promise<number>} total Magical Power
 */
async function calculateMagicalPower(userId) {
  try {
    // Fetch only equipped accessories for the user
    const accessories = await dbAll(
      `SELECT item_key FROM player_accessories WHERE discord_id = ? AND is_equipped = 1`,
      [userId]
    );

    let totalMP = 0;
    const seen = new Set();

    for (const acc of accessories) {
      const itemData = configManager.getItem(acc.item_key);
      if (!itemData || !itemData.rarity) continue;

      // Prevent counting duplicates of the same accessory item key
      if (seen.has(acc.item_key)) continue;
      seen.add(acc.item_key);

      const rarityKey =
        typeof itemData.rarity === "string" ? itemData.rarity : itemData.rarity;
      totalMP += MAGICAL_POWER_VALUES[rarityKey] || 0;
    }

    return totalMP;
  } catch (err) {
    console.error("[calculateMagicalPower] Error:", err);
    return 0;
  }
}

module.exports = { calculateMagicalPower };
