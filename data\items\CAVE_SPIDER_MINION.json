{"name": "Cave Spider Minion", "emoji": "<:minion_cave_spider:1386242082464858194> ", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "combat", "drops": [{"itemKey": "SPIDER_EYE", "chance": 1}, {"itemKey": "STRING", "chance": 0.5}], "recipes": [{"ingredients": [{"itemKey": "SPIDER_EYE", "amount": 80}]}], "craftingRequirements": {"collections": {"SPIDER_EYE": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 26, "maxStorage": 192}, {"tier": 2, "generationIntervalSeconds": 26, "maxStorage": 320, "upgradeCost": [{"itemKey": "SPIDER_EYE", "amount": 160}]}, {"tier": 3, "generationIntervalSeconds": 24, "maxStorage": 320, "upgradeCost": [{"itemKey": "SPIDER_EYE", "amount": 320}]}, {"tier": 4, "generationIntervalSeconds": 24, "maxStorage": 448, "upgradeCost": [{"itemKey": "SPIDER_EYE", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 22, "maxStorage": 448, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 22, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 16}]}, {"tier": 7, "generationIntervalSeconds": 20, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 32}]}, {"tier": 8, "generationIntervalSeconds": 20, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 64}]}, {"tier": 9, "generationIntervalSeconds": 17, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 128}]}, {"tier": 10, "generationIntervalSeconds": 17, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 256}]}, {"tier": 11, "generationIntervalSeconds": 13, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_SPIDER_EYE", "amount": 512}]}]}