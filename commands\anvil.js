const { EMBED_COLORS } = require("../gameConfig.js");
const {
  <PERSON>lash<PERSON>ommandBuilder,
  EmbedBuilder,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");
const { getInventory } = require("../utils/inventory");
const configManager = require("../utils/configManager");
const db = require("../utils/database");
const { dbRunQueued } = require("../utils/dbUtils");
const { REFORGE_DATA } = require("../data/reforges.js");
const { isBoosterCookieActive } = require("../utils/boosterCookieManager");

const HOT_POTATO_STATS = {
  ARMOR: {
    HEALTH: 4,
    DEFENSE: 2,
  },
  WEAPON: {
    DAMAGE: 2,
    STRENGTH: 2,
  },
};

function canApplyHotPotatoBooks(itemData) {
  if (!itemData || !itemData.type) return false;

  if (itemData.type === "ARMOR") return true;
  if (itemData.type === "WEAPON") return true;

  return false;
}

function getItemCategory(itemData) {
  if (itemData.type === "ARMOR") return "ARMOR";
  if (itemData.type === "WEAPON") return "WEAPON";
  return null;
}

function calculateStatBonuses(itemData, bookCount) {
  const category = getItemCategory(itemData);
  if (!category) return {};

  const bonusPerBook = HOT_POTATO_STATS[category];
  const totalBonuses = {};

  for (const [stat, value] of Object.entries(bonusPerBook)) {
    totalBonuses[stat] = value * bookCount;
  }

  return totalBonuses;
}

// Format stat bonuses for display
function formatStatBonuses(bonuses) {
  const formatted = [];
  for (const [stat, value] of Object.entries(bonuses)) {
    if (stat === "HEALTH")
      formatted.push(`+${value} <:health:1269719825429565532>  Health`);
    else if (stat === "DEFENSE")
      formatted.push(`+${value} <:defense:1269719821399097456>  Defense`);
    else if (stat === "DAMAGE")
      formatted.push(`+${value} <:damage:1270616867484209253>  Damage`);
    else if (stat === "STRENGTH")
      formatted.push(`+${value} <:strength:1269719830148157481> Strength`);
  }
  return formatted.join(", ");
}

// Format stat bonuses for next enhancement display
function formatStatBonusesForNextEnhancement(bonusDifference) {
  const formatted = [];
  for (const [stat, value] of Object.entries(bonusDifference)) {
    if (value > 0) {
      if (stat === "HEALTH")
        formatted.push(`<:health:1269719825429565532> **+${value} Health**`);
      else if (stat === "DEFENSE")
        formatted.push(`<:defense:1269719821399097456> **+${value} Defense**`);
      else if (stat === "DAMAGE")
        formatted.push(`<:damage:1270616867484209253> **+${value} Damage**`);
      else if (stat === "STRENGTH")
        formatted.push(
          `<:strength:1269719830148157481> **+${value} Strength**`
        );
    }
  }
  return formatted.length > 0 ? formatted.join("\n") : "No bonuses";
}

// Check if player has Hot Potato Books in inventory
async function getHotPotatoBookCount(userId) {
  return new Promise((resolve, reject) => {
    db.get(
      "SELECT amount FROM player_inventory_items WHERE discord_id = ? AND item_name = ?",
      [userId, "HOT_POTATO_BOOK"],
      (err, row) => {
        if (err) return reject(err);
        resolve(row ? row.amount : 0);
      }
    );
  });
}

// Utility to safely parse the `data_json` column once and reuse it across the command
function parseItemData(dataJson) {
  if (!dataJson) return {};
  try {
    return typeof dataJson === "string" ? JSON.parse(dataJson) : dataJson;
  } catch (err) {
    console.error("[parseItemData] Failed to parse data_json:", err);
    return {};
  }
}

// Apply Hot Potato Book to item
async function applyHotPotatoBook(userId, itemId) {
  try {
    // First, perform validation queries outside the transaction
    const itemRow = await new Promise((resolve, reject) => {
      db.get(
        "SELECT data_json, item_key FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
        [itemId, userId],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });

    if (!itemRow) {
      throw new Error("Item not found or you don't own this item.");
    }

    const currentData = parseItemData(itemRow.data_json);
    const currentBooks = currentData.hotPotatoBooks || 0;

    // Check if already at max
    if (currentBooks >= 10) {
      throw new Error(
        "This item already has the maximum number of Hot Potato Books (10)."
      );
    }

    // Check if player has Hot Potato Books
    const availableBooks = await getHotPotatoBookCount(userId);
    if (availableBooks < 1) {
      throw new Error("You need at least 1 Hot Potato Book to apply.");
    }

    // Prepare new data
    const newData = { ...currentData };
    newData.hotPotatoBooks = currentBooks + 1;
    const newDataString = JSON.stringify(newData);

    // Execute all database operations in the same tick so they get batched together
    const [updateResult, inventoryResult] = await Promise.all([
      dbRunQueued(
        "UPDATE player_equipment SET data_json = ? WHERE equipment_id = ? AND discord_id = ?",
        [newDataString, itemId, userId]
      ),
      dbRunQueued(
        "UPDATE player_inventory_items SET amount = amount - 1 WHERE discord_id = ? AND item_name = ? AND amount >= 1",
        [userId, "HOT_POTATO_BOOK"]
      ),
      dbRunQueued(
        "DELETE FROM player_inventory_items WHERE discord_id = ? AND item_name = ? AND amount <= 0",
        [userId, "HOT_POTATO_BOOK"]
      ),
    ]);

    // Verify the operations succeeded
    if (updateResult.changes === 0) {
      throw new Error("Failed to update item data.");
    }
    if (inventoryResult.changes === 0) {
      throw new Error("Failed to consume Hot Potato Book from inventory.");
    }

    // Get item data for stat calculation
    const allItems = configManager.getAllItems();
    const itemData = allItems[itemRow.item_key];
    const newBonuses = calculateStatBonuses(itemData, newData.hotPotatoBooks);

    return {
      success: true,
      message: `Successfully applied Hot Potato Book #${newData.hotPotatoBooks} to your item!`,
      newBookCount: newData.hotPotatoBooks,
      statBonuses: formatStatBonuses(newBonuses),
      remainingBooks: availableBooks - 1,
    };
  } catch (error) {
    console.error("Error in applyHotPotatoBook:", error);
    throw error;
  }
}

// Create the main anvil UI
async function createAnvilUI(userId, selectedItem, itemData) {
  const currentData = parseItemData(selectedItem.data_json);

  const currentBooks = currentData.hotPotatoBooks || 0;
  const availableBooks = await getHotPotatoBookCount(userId);

  const nextBonuses = calculateStatBonuses(itemData, currentBooks + 1);
  const currentBonuses = calculateStatBonuses(itemData, currentBooks);

  const bonusDifference = {};
  for (const [stat, value] of Object.entries(nextBonuses)) {
    bonusDifference[stat] = value - (currentBonuses[stat] || 0);
  }

  let displayName = itemData.name;
  if (currentData.reforge && currentData.reforge.key) {
    // Look up reforge name from reforge data
    const reforgeKey = currentData.reforge.key;
    const reforgeInfo = Object.values(REFORGE_DATA).find((category) =>
      Object.keys(category).includes(reforgeKey)
    );
    if (
      reforgeInfo &&
      reforgeInfo[reforgeKey] &&
      reforgeInfo[reforgeKey].name
    ) {
      displayName = `${reforgeInfo[reforgeKey].name} ${itemData.name}`;
    }
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BROWN)
    .setTitle(
      `<:anvil:1376166748755066901> Anvil: ${itemData.emoji || "❓"} ${
        displayName
      }`
    );

  let description = `<:hot_potato_book:1374100906483646567> **Hot Potato Books Applied:** \`${currentBooks}/10\`\n`;
  description += `<:hot_potato_book:1374100906483646567> **Books Available:** \`${availableBooks}\`\n`;

  if (currentBooks > 0) {
    description += `\n**Bonuses Applied:**\n`;
    description += formatStatBonusesForNextEnhancement(currentBonuses);
  }

  description += `\n`;

  const actionRow = new ActionRowBuilder();

  if (currentBooks >= 10) {
    description += `This item has reached the maximum number of Hot Potato Books.`;
    embed.setDescription(description);
    embed.setFooter({
      text: `This item is fully enhanced with Hot Potato Books`,
    });
  } else if (availableBooks === 0) {
    description += `\nYou need <:hot_potato_book:1374100906483646567> **Hot Potato Books** to apply them to this item.`;
    embed.setDescription(description);
    embed.setFooter({
      text: `You can craft Hot Potato Books using Sugar Cane and Enchanted Baked Potato`,
    });
  } else {
    if (currentBooks > 0) {
      description += `\nApplying a <:hot_potato_book:1374100906483646567> **Hot Potato Book** will add:\n`;
      description += formatStatBonusesForNextEnhancement(bonusDifference);
    } else {
      description += `Applying a <:hot_potato_book:1374100906483646567> Hot Potato Book will give this item:\n`;
      description += formatStatBonusesForNextEnhancement(bonusDifference);
    }
    embed.setDescription(description);

    const remainingBooks = 10 - currentBooks;
    embed.setFooter({
      text: `You can apply ${remainingBooks} more Hot Potato Book${
        remainingBooks === 1 ? "" : "s"
      } to this item`,
    });

    actionRow.addComponents(
      new ButtonBuilder()
        .setCustomId(`anvil_apply:${selectedItem.id}`)
        .setLabel(`Apply Hot Potato Book #${currentBooks + 1}`)
        .setStyle(ButtonStyle.Primary)
        .setEmoji("<:hot_potato_book:1374100906483646567>")
    );
  }

  actionRow.addComponents(
    new ButtonBuilder()
      .setCustomId("anvil_close")
      .setLabel("Close Anvil")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("❌")
  );

  return {
    embed,
    components: [actionRow],
  };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("anvil")
    .setDescription("Apply Hot Potato Books to your equipment")
    .addStringOption((option) =>
      option
        .setName("item_id")
        .setDescription("The item to enhance")
        .setRequired(true)
        .setAutocomplete(true)
    ),

  async execute(interaction) {
    await interaction.deferReply();

    const userId = interaction.user.id;
    const selectedItemId = interaction.options.getString("item_id");

    try {
      const character = await getPlayerData(userId);
      const inventory = await getInventory(userId);

      if (!character) {
        return interaction.editReply({
          content: "You must have a character to use this command.",
        });
      }

      // check if player is in The Hub or has active booster cookie
      const hasActiveBoosterCookie = isBoosterCookieActive(character);
      const isInHub = character.current_region === "the_hub";

      if (!isInHub && !hasActiveBoosterCookie) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("Anvil Not Available")
          .setDescription(
            "The anvil is only accessible in **The Hub**.\n\n💡 **Tip:** You can access the anvil from anywhere with an active Booster Cookie!"
          );

        const response = await interaction.editReply({ embeds: [errorEmbed] });
        setTimeout(() => {
          response.delete().catch(() => {});
        }, 10000);
        return;
      }

      const selectedItem = inventory.equipment.find(
        (item) => item.id === selectedItemId
      );
      if (!selectedItem) {
        return interaction.editReply({
          content: "That item does not exist in your inventory.",
        });
      }

      const allItems = configManager.getAllItems();
      const itemData = allItems[selectedItem.itemKey];

      if (!itemData) {
        return interaction.editReply({
          content: "Could not find data for that item.",
        });
      }

      if (!canApplyHotPotatoBooks(itemData)) {
        return interaction.editReply({
          content: "Hot Potato Books can only be applied to weapons and armor.",
        });
      }

      const { embed, components } = await createAnvilUI(
        userId,
        selectedItem,
        itemData
      );

      const response = await interaction.editReply({
        embeds: [embed],
        components: components,
      });

      const collector = response.createMessageComponentCollector({
        filter: (i) =>
          i.user.id === interaction.user.id &&
          (i.customId.startsWith("anvil_apply:") ||
            i.customId === "anvil_close"),
        time: 300000, // 5 minute timeout
      });

      collector.on("collect", async (i) => {
        const customId = i.customId;

        if (customId === "anvil_close") {
          const closeEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BROWN)
            .setTitle("<:anvil:1376166748755066901> Anvil Closed")
            .setDescription("Anvil session closed.")
            .setFooter({ text: "You can use /anvil from The Hub" });

          await i.update({
            embeds: [closeEmbed],
            components: [],
          });
          collector.stop();
          return;
        }

        if (customId.startsWith("anvil_apply:")) {
          const itemId = customId.split(":")[1];

          try {
            const result = await applyHotPotatoBook(userId, itemId);

            if (result.success) {
              const updatedInventory = await getInventory(userId);
              const updatedItem = updatedInventory.equipment.find(
                (item) => item.id === itemId
              );

              if (updatedItem) {
                const { embed, components } = await createAnvilUI(
                  userId,
                  updatedItem,
                  itemData
                );

                await i.update({
                  embeds: [embed],
                  components: components,
                });
              }
            }
          } catch (error) {
            await i.reply({ content: error.message, ephemeral: true });
          }
          return;
        }
      });

      collector.on("end", async (_, reason) => {
        if (reason === "time") {
          try {
            await interaction.editReply({
              content: "Anvil session timed out after 5 minutes of inactivity.",
              embeds: [],
              components: [],
            });
          } catch (error) {
            console.error("Error updating anvil session timeout:", error);
          }
        }
      });
    } catch (error) {
      console.error("Error in /anvil command:", error);
      return interaction.editReply({
        content: "An error occurred while processing your command.",
      });
    }
  },

  async autocomplete(interaction) {
    const focusedValue = interaction.options.getFocused();

    try {
      const userId = interaction.user.id;
      const inventory = await getInventory(userId);

      if (
        !inventory ||
        !inventory.equipment ||
        inventory.equipment.length === 0
      ) {
        return interaction.respond([]);
      }

      const allItems = configManager.getAllItems();

      const enhanceableItems = inventory.equipment.filter((item) => {
        const itemData = allItems[item.itemKey];
        if (!itemData) return false;

        return canApplyHotPotatoBooks(itemData);
      });

      let options = enhanceableItems.map((item) => {
        const itemData = allItems[item.itemKey];
        const currentData = item.data_json; // data_json is already parsed in getInventory

        let displayName = itemData.name;
        if (currentData && currentData.reforge && currentData.reforge.key) {
          // Look up reforge name from reforge data
          const reforgeKey = currentData.reforge.key;
          const reforgeInfo = Object.values(REFORGE_DATA).find((category) =>
            Object.keys(category).includes(reforgeKey)
          );
          if (
            reforgeInfo &&
            reforgeInfo[reforgeKey] &&
            reforgeInfo[reforgeKey].name
          ) {
            displayName = `${reforgeInfo[reforgeKey].name} ${itemData.name}`;
          }
        }

        return {
          name: `${displayName} (${item.id.substring(0, 4)})`,
          value: item.id,
        };
      });

      if (focusedValue) {
        const lowercaseFocused = focusedValue.toLowerCase();
        options = options.filter(
          (option) =>
            option.name.toLowerCase().includes(lowercaseFocused) ||
            option.value.toLowerCase().includes(lowercaseFocused)
        );
      }

      options = options.slice(0, 25);

      return interaction.respond(options);
    } catch (error) {
      console.error("Error in /anvil autocomplete:", error);
      return interaction.respond([]);
    }
  },
};
