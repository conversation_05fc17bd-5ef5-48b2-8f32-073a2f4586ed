{"name": "Spruce Minion", "emoji": "<:minion_spruce:1375851418937725090>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "foraging", "resourceItemKey": "SPRUCE_LOG", "recipes": [{"ingredients": [{"itemKey": "SPRUCE_LOG", "amount": 64}]}], "craftingRequirements": {"collections": {"SPRUCE_LOG": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 50, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 50, "maxStorage": 192, "upgradeCost": [{"itemKey": "SPRUCE_LOG", "amount": 128}]}, {"tier": 3, "generationIntervalSeconds": 45, "maxStorage": 192, "upgradeCost": [{"itemKey": "SPRUCE_LOG", "amount": 256}]}, {"tier": 4, "generationIntervalSeconds": 45, "maxStorage": 384, "upgradeCost": [{"itemKey": "SPRUCE_LOG", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 40, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 40, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 16}]}, {"tier": 7, "generationIntervalSeconds": 35, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 32}]}, {"tier": 8, "generationIntervalSeconds": 35, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 64}]}, {"tier": 9, "generationIntervalSeconds": 30, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 128}]}, {"tier": 10, "generationIntervalSeconds": 30, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 256}]}, {"tier": 11, "generationIntervalSeconds": 25, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_SPRUCE_LOG", "amount": 512}]}], "drops": [{"itemKey": "SPRUCE_LOG", "chance": 1}]}