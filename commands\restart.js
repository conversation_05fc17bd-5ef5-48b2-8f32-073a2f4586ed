const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MessageFlags,
} = require("discord.js");
const fs = require("fs");
const path = require("path");
const { dbAll } = require("../utils/dbUtils");
const {
  getActiveChannelsForAnnouncements,
} = require("../utils/activityTracker");

const RESTART_REASONS = ["Game Update", "Scheduled Restart", "Hotfix"];

module.exports = {
  data: new SlashCommandBuilder()
    .setName("restart")
    .setDescription(
      "Announce a server restart and restart the server (admin only)"
    )
    .addStringOption((option) =>
      option
        .setName("reason")
        .setDescription("Reason for the restart")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("message_id")
        .setDescription("Message ID to fetch and display changes (optional)")
        .setRequired(false)
    ),
  async autocomplete(interaction) {
    const focused = interaction.options.getFocused();
    const filtered = RESTART_REASONS.filter((r) =>
      r.toLowerCase().includes(focused.toLowerCase())
    );
    await interaction.respond(filtered.map((r) => ({ name: r, value: r })));
  },
  async execute(interaction) {
    if (!interaction.memberPermissions.has("Administrator")) {
      return interaction.reply({
        content: "You do not have permission to use this command.",
        flags: [MessageFlags.Ephemeral],
      });
    }
    const reason = interaction.options.getString("reason");
    const messageId = interaction.options.getString("message_id");
    let description = "";
    let color = 0xed4245;
    switch (reason) {
      case "Game Update":
        description = "**Server is about to restart for a Game Update**";
        if (messageId) {
          try {
            const messageLink = `https://discord.com/channels/${interaction.guildId}/${interaction.channelId}/${messageId}`;
            description += `\n**Changes:** [**CLICK HERE**](${messageLink})`;
          } catch (error) {
            console.error("[RESTART] Failed to fetch message:", error);
            description += "\n**Changes:** Unable to fetch message content";
          }
        }
        color = 0xed4245; // Red
        break;
      case "Scheduled Restart":
        description =
          "**Server is about to restart for Scheduled Maintenance**";
        color = 0xffa500; // Orange
        break;
      case "Hotfix":
        description = "**Server is about to restart for a Hotfix**";
        if (messageId) {
          try {
            const messageLink = `https://discord.com/channels/${interaction.guildId}/${interaction.channelId}/${messageId}`;
            description += `\n**Changes:** [**CLICK HERE**](${messageLink})`;
          } catch (error) {
            console.error("[RESTART] Failed to fetch message:", error);
            description += "\n**Changes:** Unable to fetch message content";
          }
        }
        color = 0xff5555; // Lighter red
        break;
      default:
        description = "**Server is about to restart**";
    }
    const embed = new EmbedBuilder()
      .setTitle("Game Announcement")
      .setDescription(description)
      .setFooter({
        text: "Your actions may be interrupted temporarily during downtime.",
      })
      .setColor(color);
    // Get personal channels that have had recent slash command activity (30 minutes)
    const activeChannelIds = getActiveChannelsForAnnouncements(30 * 60 * 1000);

    // Filter to only include personal player channels
    const players = await dbAll(
      "SELECT personal_channel_id FROM players WHERE personal_channel_id IS NOT NULL"
    );
    const personalChannelIds = new Set(
      players.map((player) => player.personal_channel_id).filter(Boolean)
    );

    // Only notify personal channels that have had recent activity
    const uniqueChannelIds = activeChannelIds.filter((channelId) =>
      personalChannelIds.has(channelId)
    );

    const notifyFile = path.join(__dirname, "../notifiedChannels.json");
    try {
      fs.writeFileSync(notifyFile, JSON.stringify(uniqueChannelIds));
    } catch (e) {
      console.error("[RESTART] Failed to write notifiedChannels.json:", e);
    }

    for (const channelId of uniqueChannelIds) {
      try {
        const channel = await interaction.client.channels.fetch(channelId);
        if (channel && channel.isTextBased()) {
          await channel.send({ embeds: [embed] });
        }
      } catch (e) {
        console.error(
          `[RESTART] Failed to send embed to channel ${channelId}:`,
          e
        );
      }
    }
    await interaction.reply({
      content: `Restart announcement sent to ${uniqueChannelIds.length} recently active personal player channels. Restarting in 5 seconds...`,
      flags: [MessageFlags.Ephemeral],
    });
    setTimeout(async () => {
      try {
        // Perform graceful cleanup (flush batches, drain DB queue) before exiting
        const {
          gracefulErrorHandler,
        } = require("../utils/gracefulErrorHandler");
        await gracefulErrorHandler.gracefulShutdown("RESTART");
      } catch (e) {
        console.warn(
          "[RESTART] Graceful shutdown encountered an error, forcing exit:",
          e?.message || e
        );
      } finally {
        // Use special exit code to signal full system restart (main + workers)
        process.exit(999);
      }
    }, 5000);
  },
};
