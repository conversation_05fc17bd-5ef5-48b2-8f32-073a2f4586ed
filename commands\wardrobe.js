const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
} = require("discord.js");
const { dbRunQueued, dbGet, dbAll } = require("../utils/dbUtils");
const { getPlayerData } = require("../utils/playerDataManager");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager");
const { WARDROBE, EMBED_COLORS } = require("../gameConfig");
const configManager = require("../utils/configManager");
const {
  equipItemAtomically,
  unequipItemAtomically,
} = require("../utils/inventory");
const { formatDisblockLevel } = require("../utils/disblockXpSystem");
const { getPetLevel } = require("../utils/petUtils");
const { calculateWardrobeSlots } = require("../utils/wardrobeUtils");

const WARDROBE_SLOTS = {
  HELMET: "helmet_item_id",
  CHESTPLATE: "chestplate_item_id",
  LEGGINGS: "leggings_item_id",
  BOOTS: "boots_item_id",
  WEAPON: "weapon_item_id",
  AXE: "axe_item_id",
  PICKAXE: "pickaxe_item_id",
  SHOVEL: "shovel_item_id",
  HOE: "hoe_item_id",
  ROD: "fishing_rod_item_id",
  NECKLACE: "necklace_item_id",
  CLOAK: "cloak_item_id",
  BELT: "belt_item_id",
  GLOVES: "gloves_item_id",
  PET: "pet_id",
};

const SLOT_LABELS = {
  WEAPON: "Weapon",
  HELMET: "Helmet",
  CHESTPLATE: "Chestplate",
  LEGGINGS: "Leggings",
  BOOTS: "Boots",
  AXE: "Axe",
  PICKAXE: "Pickaxe",
  SHOVEL: "Shovel",
  HOE: "Hoe",
  ROD: "Fishing Rod",
  NECKLACE: "Necklace",
  CLOAK: "Cloak",
  BELT: "Belt",
  GLOVES: "Gloves/Bracelet",
  PET: "Pet",
};

const PRESETS_PER_PAGE = 2;

module.exports = {
  data: new SlashCommandBuilder()
    .setName("wardrobe")
    .setDescription("Manage your gear presets for quick equipment swapping")
    .addIntegerOption((option) =>
      option
        .setName("slot")
        .setDescription("Quick equip a specific preset slot")
        .setRequired(false)
        .setAutocomplete(true)
    ),

  async execute(interaction) {
    try {
      const userId = interaction.user.id;

      // Check if the user is performing another action
      const activeType = await getCurrentActivity(userId);
      if (activeType) {
        return warnUserBusy(interaction, activeType, "wardrobe");
      }

      const playerData = await getPlayerData(userId);

      if (!playerData) {
        return interaction.reply({
          content: "❌ Player data not found. Please start playing first!",
        });
      }

      const slotOption = interaction.options.getInteger("slot");

      if (slotOption) {
        await equipPreset(interaction, userId, slotOption, true);
      } else {
        await showMainWardrobeMenu(interaction, userId);
      }
    } catch (error) {
      return interaction.reply({
        content: error.message,
        ephemeral: true,
      });
    }
  },

  async handleButton(interaction) {
    const customId = interaction.customId;
    const userId = interaction.user.id;

    const activeActionType = await getCurrentActivity(userId);
    if (activeActionType) {
      return warnUserBusy(interaction, activeActionType, "wardrobe");
    }

    if (customId === "wardrobe_configure") {
      await showConfigureSelectMenu(interaction, userId, true);
    } else if (customId === "wardrobe_stop_configure") {
      await showMainWardrobeMenu(interaction, userId, true);
    } else if (customId === "wardrobe_back") {
      await showConfigureSelectMenu(interaction, userId, true);
    } else if (customId.startsWith("wardrobe_rename_")) {
      const presetNumber = parseInt(customId.split("_")[2]);
      await showRenameModal(interaction, presetNumber);
    } else if (customId.startsWith("wardrobe_paste_")) {
      const presetNumber = parseInt(customId.split("_")[2]);
      await handlePasteCurrentSetup(interaction, userId, presetNumber);
    } else if (customId.startsWith("wardrobe_prevpage_")) {
      const currentPage = parseInt(customId.split("_")[2]);
      const newPage = Math.max(0, currentPage - 1);
      try {
        await showMainWardrobeMenu(interaction, userId, true, newPage);
      } catch (error) {
        console.error("Error in prevpage handler:", error);
        await interaction.reply({
          content: "❌ An error occurred while navigating.",
          ephemeral: true,
        });
      }
    } else if (customId.startsWith("wardrobe_nextpage_")) {
      const currentPage = parseInt(customId.split("_")[2]);
      const newPage = currentPage + 1;
      try {
        await showMainWardrobeMenu(interaction, userId, true, newPage);
      } catch (error) {
        console.error("Error in nextpage handler:", error);
        await interaction.reply({
          content: "❌ An error occurred while navigating.",
          ephemeral: true,
        });
      }
    } else if (customId.startsWith("wardrobe_configure_prevpage_")) {
      const currentPage = parseInt(customId.split("_")[3]);
      const newPage = Math.max(0, currentPage - 1);
      try {
        await showConfigureSelectMenu(interaction, userId, true, newPage);
      } catch (error) {
        console.error("Error in configure prevpage handler:", error);
        await interaction.reply({
          content: "❌ An error occurred while navigating.",
          ephemeral: true,
        });
      }
    } else if (customId.startsWith("wardrobe_configure_nextpage_")) {
      const currentPage = parseInt(customId.split("_")[3]);
      const newPage = currentPage + 1;
      try {
        await showConfigureSelectMenu(interaction, userId, true, newPage);
      } catch (error) {
        console.error("Error in configure nextpage handler:", error);
        await interaction.reply({
          content: "❌ An error occurred while navigating.",
          ephemeral: true,
        });
      }
    } else if (customId.startsWith("wardrobe_prev_")) {
      const parts = customId.split("_");
      const presetNumber = parseInt(parts[2]);
      const currentPage = parseInt(parts[3]);
      const newPage = Math.max(0, currentPage - 1);
      await showItemSelectionMenu(
        interaction,
        userId,
        presetNumber,
        true,
        newPage
      );
    } else if (customId.startsWith("wardrobe_next_")) {
      const parts = customId.split("_");
      const presetNumber = parseInt(parts[2]);
      const currentPage = parseInt(parts[3]);
      const newPage = currentPage + 1;
      await showItemSelectionMenu(
        interaction,
        userId,
        presetNumber,
        true,
        newPage
      );
    }
  },

  async autocomplete(interaction) {
    const focusedOption = interaction.options.getFocused(true);

    if (focusedOption.name === "slot") {
      try {
        const userId = interaction.user.id;
        const playerData = await getPlayerData(userId);
        const maxSlots = calculateWardrobeSlots(playerData);

        const presets = await dbAll(
          "SELECT slot_number, preset_name FROM wardrobe_presets WHERE user_id = ? ORDER BY slot_number",
          [userId]
        );

        const choices = presets.map((preset) => {
          const name =
            preset.preset_name || `Preset Slot ${preset.slot_number}`;
          return {
            name: name,
            value: preset.slot_number,
          };
        });

        if (choices.length === 0) {
          choices.push(
            ...Array.from({ length: maxSlots }, (_, i) => ({
              name: `Preset Slot ${i + 1}`,
              value: i + 1,
            }))
          );
        }

        await interaction.respond(choices);
      } catch (error) {
        console.error("Error in wardrobe autocomplete:", error);
        await interaction.respond(
          Array.from({ length: WARDROBE.MAX_SLOTS }, (_, i) => ({
            name: `Preset Slot ${i + 1}`,
            value: i + 1,
          }))
        );
      }
    }
  },

  async handleModal(interaction) {
    const customId = interaction.customId;
    const userId = interaction.user.id;

    const activeActionType = await getCurrentActivity(userId);
    if (activeActionType) {
      const busyEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("❌ Cannot Access Wardrobe")
        .setDescription(
          `You cannot access your wardrobe while performing an action (${activeActionType}). Please finish your current action first.`
        );
      return interaction.reply({ embeds: [busyEmbed], ephemeral: true });
    }

    if (customId.startsWith("wardrobe_rename_modal_")) {
      const presetNumber = parseInt(customId.split("_")[3]);
      await handleRenameModal(interaction, presetNumber);
    }
  },

  async handleSelectMenu(interaction) {
    const customId = interaction.customId;
    const userId = interaction.user.id;
    const selectedValues = interaction.values;

    const activeActionType = await getCurrentActivity(userId);
    if (activeActionType) {
      const busyEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("❌ Cannot Access Wardrobe")
        .setDescription(
          `You cannot access your wardrobe while performing an action (${activeActionType}). Please finish your current action first.`
        );
      return interaction.reply({ embeds: [busyEmbed], ephemeral: true });
    }

    if (customId === "wardrobe_equip_preset") {
      const presetNumber = parseInt(selectedValues[0].split("_")[1]);
      await equipPreset(interaction, userId, presetNumber);
    } else if (customId === "wardrobe_configure_preset") {
      const presetNumber = parseInt(selectedValues[0].split("_")[1]);
      await showItemSelectionMenu(interaction, userId, presetNumber);
    } else if (customId.startsWith("wardrobe_items_")) {
      const presetNumber = parseInt(customId.split("_")[2]);
      await handleItemSelection(
        interaction,
        userId,
        presetNumber,
        selectedValues
      );
    }
  },
};

async function showMainWardrobeMenu(
  interaction,
  userId,
  isUpdate = false,
  page = 0
) {
  try {
    console.log(
      `showMainWardrobeMenu called with isUpdate=${isUpdate}, page=${page}`
    );
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);
    console.log(`maxSlots: ${maxSlots}`);
    const presets = await dbAll(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? ORDER BY slot_number",
      [userId]
    );

    const totalPages = Math.ceil(maxSlots / PRESETS_PER_PAGE);
    page = Math.min(Math.max(0, page), totalPages - 1);
    console.log(`totalPages: ${totalPages}, adjusted page: ${page}`);

    const startPreset = page * PRESETS_PER_PAGE + 1;
    const endPreset = Math.min(maxSlots, startPreset + PRESETS_PER_PAGE - 1);
    console.log(`startPreset: ${startPreset}, endPreset: ${endPreset}`);

    const disblockLevel = Math.floor(
      formatDisblockLevel(playerData.disblock_xp || 0)
    );
    const username = playerData.name;

    const embed = new EmbedBuilder()
      .setTitle(
        `<:wardrobe:1387376984304128033> [${disblockLevel}] ${username}'s Wardrobe`
      )
      .setDescription("Manage your wardrobe to quickly swap equipped items.")
      .setColor(EMBED_COLORS.MID_BLUE);

    for (let i = startPreset; i <= endPreset; i++) {
      const preset = presets.find((p) => p.slot_number === i);
      const presetName = preset?.preset_name || `Preset Slot ${i}`;

      if (preset) {
        const presetSummary = await getPresetSummary(preset, playerData);
        embed.addFields({
          name: `<:armorstand:1387406319950106644> ${presetName}`,
          value: presetSummary || "Empty preset",
          inline: true,
        });
      } else {
        embed.addFields({
          name: `<:armorstand:1387406319950106644> ${presetName}`,
          value: "*Empty*",
          inline: true,
        });
      }

      if (i % 2 === 0) {
        embed.addFields({
          name: "\u200b",
          value: "\u200b",
          inline: true,
        });
      }
    }

    if ((endPreset - startPreset + 1) % 2 === 1) {
      embed.addFields({
        name: "\u200b",
        value: "\u200b",
        inline: true,
      });
    }

    if (totalPages > 1) {
      embed.setFooter({ text: `📄 Page ${page + 1} of ${totalPages}` });
    }

    const equipOptions = [];
    for (let i = 1; i <= maxSlots; i++) {
      const preset = presets.find((p) => p.slot_number === i);
      const presetName = preset?.preset_name || `Preset Slot ${i}`;
      const isEmpty = !preset || (await isPresetEmpty(preset));

      equipOptions.push({
        label: presetName,
        description: isEmpty ? "Empty preset" : "Equip this preset",
        value: `preset_${i}`,
      });
    }

    const equipSelectMenu = new StringSelectMenuBuilder()
      .setCustomId("wardrobe_equip_preset")
      .setPlaceholder("Select preset to equip")
      .addOptions(equipOptions);

    const configureButton = new ButtonBuilder()
      .setCustomId("wardrobe_configure")
      .setLabel("Configure")
      .setStyle(ButtonStyle.Primary)
      .setEmoji("⚙️");

    const row1 = new ActionRowBuilder().addComponents(equipSelectMenu);
    const row2 = new ActionRowBuilder().addComponents(configureButton);

    const components = [row1, row2];

    if (totalPages > 1) {
      const prevButton = new ButtonBuilder()
        .setCustomId(`wardrobe_prevpage_${page}`)
        .setLabel("Prev")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === 0);

      const nextButton = new ButtonBuilder()
        .setCustomId(`wardrobe_nextpage_${page}`)
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page >= totalPages - 1);

      const navRow = new ActionRowBuilder().addComponents(
        prevButton,
        nextButton
      );
      components.push(navRow);
    }

    const response = {
      embeds: [embed],
      components,
    };

    console.log(`About to update interaction, isUpdate: ${isUpdate}`);
    if (isUpdate) {
      if (interaction.deferred) {
        console.log("Using editReply");
        await interaction.editReply(response);
      } else {
        console.log("Using update");
        await interaction.update(response);
      }
    } else {
      console.log("Using reply");
      await interaction.reply(response);
    }
  } catch (error) {
    console.error("Error showing main wardrobe menu:", error);
    throw error;
  }
}

async function showConfigureSelectMenu(
  interaction,
  userId,
  isUpdate = false,
  page = 0
) {
  try {
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);
    const presets = await dbAll(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? ORDER BY slot_number",
      [userId]
    );

    const totalPages = Math.ceil(maxSlots / PRESETS_PER_PAGE);
    page = Math.min(Math.max(0, page), totalPages - 1);

    const startPreset = page * PRESETS_PER_PAGE + 1;
    const endPreset = Math.min(maxSlots, startPreset + PRESETS_PER_PAGE - 1);

    const embed = new EmbedBuilder()
      .setTitle("<:wardrobe:1387376984304128033> Wardrobe - Configure")
      .setDescription("Select a slot to configure.")
      .setColor(EMBED_COLORS.ORANGE);

    for (let i = startPreset; i <= endPreset; i++) {
      const preset = presets.find((p) => p.slot_number === i);
      const presetName = preset?.preset_name || `Preset Slot ${i}`;

      if (preset) {
        const presetSummary = await getPresetSummary(preset, playerData);
        embed.addFields({
          name: `<:armorstand:1387406319950106644> ${presetName}`,
          value: presetSummary || "Empty preset",
          inline: true,
        });
      } else {
        embed.addFields({
          name: `<:armorstand:1387406319950106644> ${presetName}`,
          value: "*Empty*",
          inline: true,
        });
      }

      if (i % 2 === 0) {
        embed.addFields({
          name: "\u200b",
          value: "\u200b",
          inline: true,
        });
      }
    }

    if ((endPreset - startPreset + 1) % 2 === 1) {
      embed.addFields({
        name: "\u200b",
        value: "\u200b",
        inline: true,
      });
    }

    if (totalPages > 1) {
      embed.setFooter({ text: `📄 Page ${page + 1} of ${totalPages}` });
    }

    const configureOptions = [];
    for (let i = 1; i <= maxSlots; i++) {
      const preset = presets.find((p) => p.slot_number === i);
      const presetName = preset?.preset_name || `Preset Slot ${i}`;
      configureOptions.push({
        label: presetName,
        description: `Configure items for ${presetName}`,
        value: `preset_${i}`,
        emoji: "⚙️",
      });
    }

    const configureSelectMenu = new StringSelectMenuBuilder()
      .setCustomId("wardrobe_configure_preset")
      .setPlaceholder("Select preset to configure")
      .addOptions(configureOptions);

    const stopConfigureButton = new ButtonBuilder()
      .setCustomId("wardrobe_stop_configure")
      .setLabel("Stop Configure")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("❌");

    const row1 = new ActionRowBuilder().addComponents(configureSelectMenu);
    const row2 = new ActionRowBuilder().addComponents(stopConfigureButton);

    const components = [row1, row2];

    if (totalPages > 1) {
      const prevButton = new ButtonBuilder()
        .setCustomId(`wardrobe_configure_prevpage_${page}`)
        .setLabel("Prev")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === 0);

      const nextButton = new ButtonBuilder()
        .setCustomId(`wardrobe_configure_nextpage_${page}`)
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page >= totalPages - 1);

      const navRow = new ActionRowBuilder().addComponents(
        prevButton,
        nextButton
      );
      components.push(navRow);
    }

    const response = {
      embeds: [embed],
      components,
    };

    if (isUpdate) {
      await interaction.update(response);
    } else {
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply(response);
      } else {
        await interaction.reply(response);
      }
    }
  } catch (error) {
    console.error("Error showing configure select menu:", error);
    throw error;
  }
}

async function showItemSelectionMenu(
  interaction,
  userId,
  presetNumber,
  isUpdate = false,
  page = 0
) {
  try {
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    if (presetNumber < 1 || presetNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      if (isUpdate) {
        return interaction.update({ embeds: [errorEmbed] });
      } else {
        return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      }
    }
    const allItems = configManager.getAllItems();
    // let equippedCount = 0; // Currently unused
    // let unequippedCount = 0; // Currently unused
    // let errors = []; // Currently unused

    const preset = await dbGet(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
      [userId, presetNumber]
    );

    const presetName = preset?.preset_name || `Preset Slot ${presetNumber}`;

    const availableItemsBySlot = getAvailableItemsBySlot(playerData, allItems);

    const allItemsForPagination = [];
    for (const [slotKey, slotLabel] of Object.entries(SLOT_LABELS)) {
      const slotItems = availableItemsBySlot[slotKey] || [];

      for (const item of slotItems) {
        const isCurrentlyInPreset =
          preset && preset[WARDROBE_SLOTS[slotKey]] === item.id;
        allItemsForPagination.push({
          label: `${isCurrentlyInPreset ? "✅ " : ""}${item.name}`,
          description: slotLabel,
          value: `${slotKey}_${item.id || item.itemKey}`,
          emoji: item.emoji || "📦",
          isCurrentlyInPreset: isCurrentlyInPreset,
        });
      }
    }

    allItemsForPagination.sort((a, b) => {
      if (a.isCurrentlyInPreset && !b.isCurrentlyInPreset) return -1;
      if (!a.isCurrentlyInPreset && b.isCurrentlyInPreset) return 1;
      return 0;
    });

    const itemsPerPage = 24;
    const totalPages = Math.ceil(allItemsForPagination.length / itemsPerPage);
    const startIndex = page * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedItems = allItemsForPagination.slice(startIndex, endIndex);

    const description = "Select items to include in this preset.";

    const embed = new EmbedBuilder()
      .setTitle(
        `<:wardrobe:1387376984304128033> Wardrobe - Configuring ${presetName}`
      )
      .setDescription(description)
      .setColor(EMBED_COLORS.ORANGE);

    if (totalPages > 1) {
      embed.setFooter({ text: `📄 Page ${page + 1} of ${totalPages}` });
    }

    if (preset) {
      const presetSummary = await getPresetSummary(preset, playerData);
      embed.addFields({
        name: "Currently in Wardrobe",
        value: presetSummary || "*No items configured*",
        inline: false,
      });
    }

    const options = [];

    options.push({
      label: "🗑️ Remove All Items",
      description: "Clear all items from this preset",
      value: "remove_all",
      emoji: "🗑️",
    });

    options.push(...paginatedItems);

    if (options.length === 1) {
      options.push({
        label: "No items available",
        description:
          "You need equipment in your inventory to configure presets",
        value: "no_items",
        emoji: "❌",
      });
    }

    const itemSelectMenu = new StringSelectMenuBuilder()
      .setCustomId(`wardrobe_items_${presetNumber}`)
      .setPlaceholder("Pick items to include in this preset")
      .setMinValues(1)
      .setMaxValues(Math.min(options.length, 25))
      .addOptions(options.slice(0, 25));

    const row1 = new ActionRowBuilder().addComponents(itemSelectMenu);

    const backButton = new ButtonBuilder()
      .setCustomId("wardrobe_back")
      .setLabel("<- Back")
      .setStyle(ButtonStyle.Secondary);

    const renameButton = new ButtonBuilder()
      .setCustomId(`wardrobe_rename_${presetNumber}`)
      .setLabel("Rename")
      .setStyle(ButtonStyle.Primary)
      .setEmoji("<:rename_button:1387409515661496351>");

    const pasteButton = new ButtonBuilder()
      .setCustomId(`wardrobe_paste_${presetNumber}`)
      .setLabel("Paste")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("<:armorstand:1387406319950106644>");

    const buttonRow = [backButton, renameButton, pasteButton];

    if (totalPages > 1) {
      const prevButton = new ButtonBuilder()
        .setCustomId(`wardrobe_prev_${presetNumber}_${page}`)
        .setLabel("Prev")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === 0);

      const nextButton = new ButtonBuilder()
        .setCustomId(`wardrobe_next_${presetNumber}_${page}`)
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page >= totalPages - 1);

      buttonRow.push(prevButton, nextButton);
    }

    const row2 = new ActionRowBuilder().addComponents(...buttonRow);

    const response = {
      embeds: [embed],
      components: [row1, row2],
    };

    if (isUpdate) {
      if (interaction.deferred) {
        await interaction.editReply(response);
      } else {
        await interaction.update(response);
      }
    } else {
      await interaction.update(response);
    }
  } catch (error) {
    console.error("Error showing item selection menu:", error);
    throw error;
  }
}

async function handleItemSelection(
  interaction,
  userId,
  presetNumber,
  selectedValues
) {
  try {
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    if (presetNumber < 1 || presetNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      return interaction.update({ embeds: [errorEmbed] });
    }
    // const allItems = configManager.getAllItems(); // Currently unused
    // let equippedCount = 0; // Currently unused
    // let unequippedCount = 0; // Currently unused
    // let errors = []; // Currently unused

    for (const selectedValue of selectedValues) {
      if (selectedValue === "remove_all") {
        await dbRunQueued(
          "DELETE FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
          [userId, presetNumber]
        );
        continue;
      } else if (
        selectedValue === "no_items" ||
        selectedValue.startsWith("header_")
      ) {
        continue;
      }

      const [slotKey, itemId] = selectedValue.split("_", 2);
      const dbColumn = WARDROBE_SLOTS[slotKey];

      if (!dbColumn) {
        continue;
      }

      let inventoryItem;
      if (slotKey === "PET") {
        inventoryItem = playerData.pets?.find((pet) => pet.id === itemId);
      } else {
        inventoryItem = playerData.inventory.equipment?.find(
          (item) => item.id === itemId
        );
      }
      if (!inventoryItem) {
        continue;
      }

      let preset = await dbGet(
        "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
        [userId, presetNumber]
      );

      if (!preset) {
        await dbRunQueued(
          "INSERT OR REPLACE INTO wardrobe_presets (user_id, slot_number, helmet_item_id, chestplate_item_id, leggings_item_id, boots_item_id, weapon_item_id, fishing_rod_item_id, pickaxe_item_id, axe_item_id, shovel_item_id, hoe_item_id, necklace_item_id, cloak_item_id, belt_item_id, gloves_item_id, pet_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
          [
            userId,
            presetNumber,
            null, // helmet_id
            null, // chestplate_id
            null, // leggings_id
            null, // boots_id
            null, // weapon_id
            null, // fishing_rod_id
            null, // pickaxe_id
            null, // axe_id
            null, // shovel_id
            null, // hoe_id
            null, // necklace_id
            null, // cloak_id
            null, // belt_id
            null, // gloves_id
            null, // pet_id
          ]
        );
        preset = await dbGet(
          "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
          [userId, presetNumber]
        );
      }

      const currentItem = preset[dbColumn];
      if (currentItem === itemId) {
        await dbRunQueued(
          `UPDATE wardrobe_presets SET ${dbColumn} = NULL WHERE user_id = ? AND slot_number = ?`,
          [userId, presetNumber]
        );
      } else {
        await dbRunQueued(
          `UPDATE wardrobe_presets SET ${dbColumn} = ? WHERE user_id = ? AND slot_number = ?`,
          [itemId, userId, presetNumber]
        );
      }
    }

    await showItemSelectionMenu(interaction, userId, presetNumber, true);
  } catch (error) {
    console.error("Error handling item selection:", error);
    try {
      await interaction.update({
        content: "❌ An error occurred while updating your preset.",
        embeds: [],
        components: [],
      });
    } catch (updateError) {
      console.error("Error updating interaction after error:", updateError);
    }
  }
}

async function equipPreset(
  interaction,
  userId,
  presetNumber,
  isFromCommand = false
) {
  try {
    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    if (presetNumber < 1 || presetNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      if (isFromCommand) {
        return interaction.reply({ embeds: [errorEmbed], ephemeral: true });
      } else {
        return interaction.update({ embeds: [errorEmbed] });
      }
    }

    if (isFromCommand) {
      await interaction.deferReply();
    } else {
      await interaction.deferUpdate();
    }

    const preset = await dbGet(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
      [userId, presetNumber]
    );

    if (!preset || (await isPresetEmpty(preset))) {
      const presetName = preset?.preset_name || `Preset Slot ${presetNumber}`;
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ **${presetName}** is empty!\n\nNothing was equipped.`
        )
        .setColor(EMBED_COLORS.ERROR);

      const response = await interaction.followUp({
        embeds: [errorEmbed],
      });

      setTimeout(async () => {
        try {
          await response.delete();
        } catch (error) {
          console.log("Could not delete empty preset message:", error.message);
        }
      }, 7000);

      return response;
    }
    const allItems = configManager.getAllItems();
    const errors = [];

    const currentlyEquipped = playerData.inventory.equipment.filter(
      (item) => item.isEquipped
    );
    const presetItemIds = Object.values(WARDROBE_SLOTS)
      .map((dbColumn) => preset[dbColumn])
      .filter((id) => id);

    for (const equippedItem of currentlyEquipped) {
      if (!presetItemIds.includes(equippedItem.id)) {
        try {
          await unequipItemAtomically(userId, equippedItem.id);
        } catch (error) {
          console.error(`Error unequipping item ${equippedItem.id}:`, error);
        }
      }
    }

    if (
      playerData.active_pet_id &&
      playerData.active_pet_id !== preset.pet_id
    ) {
      try {
        await dbRunQueued(
          "UPDATE players SET active_pet_id = NULL WHERE discord_id = ?",
          [userId]
        );
      } catch (error) {
        console.error(
          `Error unequipping pet ${playerData.active_pet_id}:`,
          error
        );
      }
    }

    for (const [slotKey, dbColumn] of Object.entries(WARDROBE_SLOTS)) {
      const itemId = preset[dbColumn];
      if (itemId) {
        try {
          if (slotKey === "PET") {
            if (playerData.pets && Array.isArray(playerData.pets)) {
              const pet = playerData.pets.find((p) => p.id === itemId);
              if (pet) {
                await dbRunQueued(
                  "UPDATE players SET active_pet_id = ? WHERE discord_id = ?",
                  [itemId, userId]
                );
              } else {
                errors.push(`Pet ${itemId.slice(0, 4)}: Pet not found`);
              }
            } else {
              errors.push(`Pet ${itemId.slice(0, 4)}: No pets available`);
            }
          } else {
            const inventoryItem = playerData.inventory.equipment.find(
              (item) => item.id === itemId
            );

            if (inventoryItem) {
              const result = await equipItemAtomically(
                userId,
                inventoryItem.id
              );
              if (!result.success) {
                const itemName =
                  allItems[inventoryItem.itemKey]?.name ||
                  inventoryItem.itemKey;
                errors.push(
                  `${itemName} ${itemId.slice(0, 4)}: ${result.message}`
                );
              }
            } else {
              errors.push(
                `Item ${itemId.slice(0, 4)}: Item not found in inventory`
              );
            }
          }
        } catch (error) {
          console.error(`Error equipping ${slotKey} ${itemId}:`, error);
          errors.push(`${slotKey} ${itemId.slice(0, 4)}: Equipment failed`);
        }
      }
    }

    const presetName = preset?.preset_name || `Preset ${presetNumber}`;
    const successEmbed = new EmbedBuilder()
      //.setTitle("<:wardrobe:1387376984304128033> Wardrobe")
      .setDescription(`Successfully equipped Wardrobe **${presetName}**`)
      .setColor(EMBED_COLORS.GREEN);

    if (errors.length > 0) {
      successEmbed.addFields({
        name: "⚠️ Errors",
        value: errors.join("\n"),
        inline: false,
      });
      successEmbed.setColor(EMBED_COLORS.GOLD);
    }

    let response;
    if (isFromCommand) {
      response = await interaction.followUp({
        embeds: [successEmbed],
      });
    } else {
      response = await interaction.channel.send({
        embeds: [successEmbed],
      });
    }

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log("Could not delete preset equip message:", error.message);
      }
    }, 7000);
  } catch (error) {
    console.error("Error equipping preset:", error);
    await interaction.followUp({
      content: "❌ An error occurred while equipping the preset.",
    });
  }
}

async function getPresetSummary(preset, playerData = null) {
  const allItems = configManager.getAllItems();
  const equippedItems = [];

  if (!playerData && preset.user_id) {
    playerData = await getPlayerData(preset.user_id);
  }

  for (const [slotKey, dbColumn] of Object.entries(WARDROBE_SLOTS)) {
    const itemId = preset[dbColumn];

    if (slotKey === "PET") {
      if (itemId && playerData?.pets && Array.isArray(playerData.pets)) {
        const pet = playerData.pets.find((p) => p.id === itemId);
        if (pet) {
          const petData = allItems[pet.petKey];
          const petLevel = getPetLevel(pet);
          const petName = petData?.name || pet.petKey;
          const petEmoji = petData?.emoji || "🐾";
          const rarityName = pet.rarity ? pet.rarity.toUpperCase() : "COMMON";
          const petId = pet.id ? pet.id.slice(0, 4) : "????";

          const uuidSuffix = petId ? ` \`${petId}\`` : "";
          equippedItems.push(
            `${petEmoji} **${rarityName} ${petName} Lvl ${petLevel}**${uuidSuffix}`
          );
        }
      }
    } else {
      if (itemId && playerData?.inventory?.equipment) {
        const inventoryItem = playerData.inventory.equipment.find(
          (item) => item.id === itemId
        );
        if (inventoryItem && allItems[inventoryItem.itemKey]) {
          const itemDef = allItems[inventoryItem.itemKey];

          let displayName = itemDef.name;
          try {
            if (inventoryItem.data_json) {
              let dataJson;
              if (typeof inventoryItem.data_json === "string") {
                dataJson = JSON.parse(inventoryItem.data_json);
              } else {
                dataJson = inventoryItem.data_json;
              }
              if (dataJson.reforge) {
                const {
                  getDynamicReforgeName,
                } = require("../utils/dynamicReforgeStats");
                const dynamicReforgeName = getDynamicReforgeName(
                  dataJson.reforge,
                  itemDef
                );
                displayName = `${dynamicReforgeName} ${itemDef.name}`;
              }
            }
          } catch (error) {
            console.error("Error parsing reforge data in wardrobe:", error);
          }

          const uuidSuffix = itemId ? ` \`${itemId.slice(0, 4)}\`` : "";
          equippedItems.push(
            `${itemDef.emoji || "📦"} **${displayName}**${uuidSuffix}`
          );
        }
      }
    }
  }

  if (equippedItems.length === 0) {
    return "*No items configured*";
  }

  let result = equippedItems.join("\n");

  if (result.length > 1000) {
    const truncatedItems = [];
    let currentLength = 0;
    const maxLength = 950;

    for (const item of equippedItems) {
      if (currentLength + item.length + 1 > maxLength) {
        const remaining = equippedItems.length - truncatedItems.length;
        truncatedItems.push(`*...and ${remaining} more items*`);
        break;
      }
      truncatedItems.push(item);
      currentLength += item.length + 1;
    }

    result = truncatedItems.join("\n");
  }

  return result;
}

async function isPresetEmpty(preset) {
  for (const dbColumn of Object.values(WARDROBE_SLOTS)) {
    if (preset[dbColumn]) {
      return false;
    }
  }
  return true;
}

function getAvailableItemsBySlot(playerData, allItems) {
  const itemsBySlot = {};

  for (const slotKey of Object.keys(WARDROBE_SLOTS)) {
    itemsBySlot[slotKey] = [];
  }

  const allEquipment = playerData.inventory.equipment || [];

  for (const item of allEquipment) {
    const itemDef = allItems[item.itemKey];
    if (!itemDef) continue;

    const slotKey = getItemSlot(itemDef);
    if (slotKey && itemsBySlot[slotKey]) {
      itemsBySlot[slotKey].push({
        id: item.id,
        itemKey: item.itemKey,
        name: itemDef.name,
        emoji: itemDef.emoji,
        isEquipped: item.isEquipped,
      });
    }
  }

  if (playerData.pets && Array.isArray(playerData.pets)) {
    for (const pet of playerData.pets) {
      const petData = allItems[pet.petKey];
      const petLevel = getPetLevel(pet);
      const petName = petData?.name || pet.petKey;
      const petEmoji = petData?.emoji || "🐾";
      const rarityName = pet.rarity ? pet.rarity.toUpperCase() : "COMMON";
      const petId = pet.id ? pet.id.slice(0, 4) : "????";

      const formattedName = `**${rarityName} ${petName} Lvl ${petLevel}** \`${petId}\``;

      itemsBySlot["PET"].push({
        id: pet.id,
        itemKey: pet.petKey || pet.key || pet.name,
        name: formattedName,
        emoji: petEmoji,
        isEquipped: playerData.active_pet_id === pet.id,
      });
    }
  }

  for (const slotKey of Object.keys(itemsBySlot)) {
    itemsBySlot[slotKey].sort((a, b) => {
      if (a.isEquipped && !b.isEquipped) return -1;
      if (!a.isEquipped && b.isEquipped) return 1;
      return a.name.localeCompare(b.name);
    });
  }

  return itemsBySlot;
}

function getItemSlot(itemDef) {
  // farming axes should always go in the AXE slot, regardless of type
  if (itemDef.subtype === "FARMING_AXE") {
    return "AXE";
  }
  if (itemDef.subtype === "AXE") {
    return itemDef.type === "TOOL" ? "AXE" : "WEAPON";
  }

  if (itemDef.type === "EQUIPMENT") {
    if (itemDef.subtype === "GLOVES" || itemDef.subtype === "BRACELET") {
      return "GLOVES";
    }
    return itemDef.subtype;
  }

  if (["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"].includes(itemDef.subtype)) {
    return itemDef.subtype;
  }

  if (itemDef.type === "TOOL") {
    if (["PICKAXE", "SHOVEL", "HOE"].includes(itemDef.subtype)) {
      return itemDef.subtype;
    }
    if (itemDef.subtype === "ROD") {
      return "ROD";
    }
  }

  if (itemDef.type === "WEAPON") {
    return "WEAPON";
  }

  return null;
}

/* function getSlotEmoji(slotKey) {
  const emojis = {
    HELMET: "⛑️",
    CHESTPLATE: "🛡️",
    LEGGINGS: "👖",
    BOOTS: "👢",
    WEAPON: "⚔️",
    AXE: "🪓",
    PICKAXE: "⛏️",
    SHOVEL: "🥄",
    HOE: "🔨",
    ROD: "🎣",
    NECKLACE: "📿",
    CLOAK: "🧥",
    BELT: "🔗",
    GLOVES: "🧤",
    PET: "🐾",
  };
  return emojis[slotKey] || "<:armorstand:1387406319950106644>";
} */

async function showRenameModal(interaction, presetNumber) {
  const userId = interaction.user.id;
  const playerData = await getPlayerData(userId);
  const maxSlots = calculateWardrobeSlots(playerData);

  if (presetNumber < 1 || presetNumber > maxSlots) {
    const errorEmbed = new EmbedBuilder()
      .setDescription(
        `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
      )
      .setColor(EMBED_COLORS.ERROR);

    return interaction.update({ embeds: [errorEmbed] });
  }

  const modal = new ModalBuilder()
    .setCustomId(`wardrobe_rename_modal_${presetNumber}`)
    .setTitle(`Rename Wardrobe Preset ${presetNumber}`);

  const nameInput = new TextInputBuilder()
    .setCustomId("preset_name_input")
    .setLabel("Preset Name")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(
      "Enter preset name (letters and numbers only, max 16 chars)"
    )
    .setRequired(true)
    .setMaxLength(16);

  const actionRow = new ActionRowBuilder().addComponents(nameInput);
  modal.addComponents(actionRow);

  await interaction.showModal(modal);
}

async function handleRenameModal(interaction, presetNumber) {
  const userId = interaction.user.id;
  const playerData = await getPlayerData(userId);
  const maxSlots = calculateWardrobeSlots(playerData);

  if (presetNumber < 1 || presetNumber > maxSlots) {
    const errorEmbed = new EmbedBuilder()
      .setDescription(
        `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
      )
      .setColor(EMBED_COLORS.ERROR);

    const response = await interaction.channel.send({
      embeds: [errorEmbed],
    });

    await interaction.deferUpdate();

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log(
          "Could not delete validation error message:",
          error.message
        );
      }
    }, 7000);
    return;
  }

  const presetName = interaction.fields.getTextInputValue("preset_name_input");

  const nameRegex = /^[a-zA-Z0-9]+$/;
  if (!nameRegex.test(presetName)) {
    const errorEmbed = new EmbedBuilder()
      .setDescription(
        "❌ Preset name can only contain letters and numbers, no spaces or special characters."
      )
      .setColor(EMBED_COLORS.ERROR);

    const response = await interaction.channel.send({
      embeds: [errorEmbed],
    });

    await interaction.deferUpdate();

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log("Could not delete rename error message:", error.message);
      }
    }, 7000);
    return;
  }

  try {
    const existingPreset = await dbGet(
      "SELECT slot_number FROM wardrobe_presets WHERE user_id = ? AND preset_name = ?",
      [userId, presetName]
    );

    if (existingPreset) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(`❌ You already have a preset named "${presetName}".`)
        .setColor(EMBED_COLORS.ERROR);

      const response = await interaction.channel.send({
        embeds: [errorEmbed],
      });

      await interaction.deferUpdate();

      setTimeout(async () => {
        try {
          await response.delete();
        } catch (error) {
          console.log("Could not delete rename error message:", error.message);
        }
      }, 7000);
      return;
    }

    const slotNumber = presetNumber;

    await dbRunQueued(
      "UPDATE wardrobe_presets SET preset_name = ? WHERE user_id = ? AND slot_number = ?",
      [presetName, userId, slotNumber]
    );

    const preset = await dbGet(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
      [userId, slotNumber]
    );

    if (!preset) {
      await dbRunQueued(
        "INSERT INTO wardrobe_presets (user_id, slot_number, preset_name) VALUES (?, ?, ?)",
        [userId, slotNumber, presetName]
      );
    }

    const successEmbed = new EmbedBuilder()
      .setDescription(`✅ Preset renamed to "${presetName}".`)
      .setColor(EMBED_COLORS.GREEN);

    const response = await interaction.channel.send({
      embeds: [successEmbed],
    });

    await showItemSelectionMenu(interaction, userId, presetNumber, true);

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log("Could not delete rename success message:", error.message);
      }
    }, 7000);
  } catch (error) {
    console.error("Error renaming preset:", error);
    const errorEmbed = new EmbedBuilder()
      .setDescription("❌ An error occurred while renaming the preset.")
      .setColor(EMBED_COLORS.ERROR);

    const response = await interaction.channel.send({
      embeds: [errorEmbed],
    });

    await interaction.deferUpdate();

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log("Could not delete rename error message:", error.message);
      }
    }, 7000);
  }
}

async function handlePasteCurrentSetup(interaction, userId, presetNumber) {
  try {
    // Defer the interaction update first
    await interaction.deferUpdate();

    const playerData = await getPlayerData(userId);
    const maxSlots = calculateWardrobeSlots(playerData);

    if (presetNumber < 1 || presetNumber > maxSlots) {
      const errorEmbed = new EmbedBuilder()
        .setDescription(
          `❌ Invalid preset slot! Please choose a slot between 1 and ${maxSlots}.`
        )
        .setColor(EMBED_COLORS.ERROR);

      const response = await interaction.channel.send({
        embeds: [errorEmbed],
      });

      setTimeout(async () => {
        try {
          await response.delete();
        } catch (error) {
          console.log("Could not delete paste error message:", error.message);
        }
      }, 7000);
      return;
    }

    const allItems = configManager.getAllItems();
    const equippedItems = playerData.inventory.equipment.filter(
      (item) => item.isEquipped
    );

    // Create or get existing preset
    const preset = await dbGet(
      "SELECT * FROM wardrobe_presets WHERE user_id = ? AND slot_number = ?",
      [userId, presetNumber]
    );

    if (!preset) {
      // Create new preset
      await dbRunQueued(
        "INSERT OR REPLACE INTO wardrobe_presets (user_id, slot_number, helmet_item_id, chestplate_item_id, leggings_item_id, boots_item_id, weapon_item_id, fishing_rod_item_id, pickaxe_item_id, axe_item_id, shovel_item_id, hoe_item_id, necklace_item_id, cloak_item_id, belt_item_id, gloves_item_id, pet_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        [
          userId,
          presetNumber,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
          null,
        ]
      );
    } else {
      // Clear existing preset
      await dbRunQueued(
        "UPDATE wardrobe_presets SET helmet_item_id = NULL, chestplate_item_id = NULL, leggings_item_id = NULL, boots_item_id = NULL, weapon_item_id = NULL, fishing_rod_item_id = NULL, pickaxe_item_id = NULL, axe_item_id = NULL, shovel_item_id = NULL, hoe_item_id = NULL, necklace_item_id = NULL, cloak_item_id = NULL, belt_item_id = NULL, gloves_item_id = NULL, pet_id = NULL WHERE user_id = ? AND slot_number = ?",
        [userId, presetNumber]
      );
    }

    // Add equipped items to preset
    const itemsAdded = [];
    for (const item of equippedItems) {
      const itemDef = allItems[item.itemKey];
      if (!itemDef) continue;

      const slotKey = getItemSlot(itemDef);
      if (slotKey && WARDROBE_SLOTS[slotKey]) {
        const dbColumn = WARDROBE_SLOTS[slotKey];
        await dbRunQueued(
          `UPDATE wardrobe_presets SET ${dbColumn} = ? WHERE user_id = ? AND slot_number = ?`,
          [item.id, userId, presetNumber]
        );
        itemsAdded.push(itemDef.name);
      }
    }

    // Add active pet if exists
    if (playerData.active_pet_id) {
      await dbRunQueued(
        "UPDATE wardrobe_presets SET pet_id = ? WHERE user_id = ? AND slot_number = ?",
        [playerData.active_pet_id, userId, presetNumber]
      );

      const activePet = playerData.pets?.find(
        (p) => p.id === playerData.active_pet_id
      );
      if (activePet) {
        const petData = allItems[activePet.petKey];
        const petName = petData?.name || activePet.petKey;
        itemsAdded.push(petName);
      }
    }

    // Show success message
    const preset_name = preset?.preset_name || `Preset Slot ${presetNumber}`;
    const successEmbed = new EmbedBuilder()
      .setDescription(
        `✅ Pasted current setup to **${preset_name}**.\n\n**Items added:**\n${itemsAdded.length > 0 ? itemsAdded.join(", ") : "No items were currently equipped"}`
      )
      .setColor(EMBED_COLORS.GREEN);

    const response = await interaction.channel.send({
      embeds: [successEmbed],
    });

    // Refresh the item selection menu using editReply since we deferred
    await showItemSelectionMenu(interaction, userId, presetNumber, true);

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log("Could not delete paste success message:", error.message);
      }
    }, 7000);
  } catch (error) {
    console.error("Error pasting current setup:", error);
    const errorEmbed = new EmbedBuilder()
      .setDescription("❌ An error occurred while pasting your current setup.")
      .setColor(EMBED_COLORS.ERROR);

    const response = await interaction.channel.send({
      embeds: [errorEmbed],
    });

    setTimeout(async () => {
      try {
        await response.delete();
      } catch (error) {
        console.log("Could not delete paste error message:", error.message);
      }
    }, 7000);
  }
}
