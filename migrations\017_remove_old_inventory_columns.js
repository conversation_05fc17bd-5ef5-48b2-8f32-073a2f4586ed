const { dbRun } = require("../utils/dbUtils");

module.exports = {
  async up() {
    // dbRun uses the shared connection
    console.log(
      "[Migration 017] Dropping old inventory JSON columns from players table...",
    );

    try {
      // SQLite doesn't support dropping multiple columns in one statement reliably across versions
      await dbRun("ALTER TABLE players DROP COLUMN inventory_items_json;", []);
      console.log("[Migration 017] Dropped inventory_items_json column.");

      await dbRun("ALTER TABLE players DROP COLUMN equipment_json;", []);
      console.log("[Migration 017] Dropped equipment_json column.");

      console.log(
        "[Migration 017] Successfully removed old inventory JSON columns.",
      );
      return true; // Indicate success
    } catch (error) {
      // Handle specific errors if needed, e.g., column doesn't exist (shouldn't happen if previous migrations ran)
      if (error.message.includes("no such column")) {
        console.warn(
          `[Migration 017] Warning: Column already dropped or never existed: ${error.message}`,
        );
        return true; // Consider it a success if columns are already gone
      }
      console.error("[Migration 017] Error dropping columns:", error);
      return false; // Indicate failure
    }
  },
};
