// scripts/migrations/003_create_active_actions_table.js

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve(this);
    });
  });
}

module.exports = {
  /**
   * Applies the migration.
   * @param {Database} db The database connection.
   */
  async up(db) {
    console.log("Applying migration 003: Create active_actions table...");
    await dbRunAsync(
      db,
      `
            CREATE TABLE IF NOT EXISTS active_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                action_type TEXT NOT NULL,              -- e.g., 'farming', 'mining', 'fishing', 'foraging', 'multi_combat'
                parameters_json TEXT NOT NULL,         -- JSON storing action specifics, e.g., {"cropItemKey": "WHEAT"} or {"mobKey": "ZOMBIE"}
                total_amount INTEGER NOT NULL,           -- Initial requested cycles
                completed_cycles INTEGER NOT NULL DEFAULT 0, -- Cycles completed before potential shutdown
                start_timestamp INTEGER NOT NULL,        -- Unix ms when the action originally started
                notify_on_complete INTEGER NOT NULL DEFAULT 0, -- 0 for false, 1 for true
                channel_id TEXT NOT NULL,              -- Channel where the action was initiated
                last_update_timestamp INTEGER NOT NULL   -- Unix ms when this record was last touched
            );
        `,
    );
    // Add an index for faster lookups on startup
    await dbRunAsync(
      db,
      "CREATE INDEX IF NOT EXISTS idx_active_actions_user_id ON active_actions(user_id);",
    );
    console.log("Migration 003 applied successfully.");
  },

  /**
   * Reverts the migration.
   * @param {Database} db The database connection.
   */
  async down(db) {
    console.log("Reverting migration 003: Drop active_actions table...");
    await dbRunAsync(db, "DROP TABLE IF EXISTS active_actions;");
    console.log("Migration 003 reverted successfully.");
  },
};
