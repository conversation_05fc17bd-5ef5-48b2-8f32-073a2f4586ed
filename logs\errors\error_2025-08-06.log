2025-08-06T14:08:00.046Z [uncaughtException] errorHandler.handleError is not a function
TypeError: errorHandler.handleError is not a function
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\tests\test-resilience.js:29:31)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:08:00.047Z [uncaughtException] errorHandler.handleError is not a function
TypeError: errorHandler.handleError is not a function
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\tests\test-resilience.js:29:31)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:14:07.460Z [uncaughtException] Cannot access 'WORKER_BOT_ID' before initialization
ReferenceError: Cannot access 'WORKER_BOT_ID' before initialization
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:4:26)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:14:07.464Z [uncaughtException] Cannot access 'WORKER_BOT_ID' before initialization
ReferenceError: Cannot access 'WORKER_BOT_ID' before initialization
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:4:26)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:14:07.467Z [uncaughtException] Cannot access 'WORKER_BOT_ID' before initialization
ReferenceError: Cannot access 'WORKER_BOT_ID' before initialization
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:4:26)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:15:09.637Z [uncaughtException] Cannot access 'WORKER_BOT_ID' before initialization
ReferenceError: Cannot access 'WORKER_BOT_ID' before initialization
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:4:26)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:15:09.641Z [uncaughtException] Cannot access 'WORKER_BOT_ID' before initialization
ReferenceError: Cannot access 'WORKER_BOT_ID' before initialization
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:4:26)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:15:09.644Z [uncaughtException] Cannot access 'WORKER_BOT_ID' before initialization
ReferenceError: Cannot access 'WORKER_BOT_ID' before initialization
    at Object.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:4:26)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32)
    at Function._load (node:internal/modules/cjs/loader:1108:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)
    at node:internal/main/run_main_module:36:49
---
2025-08-06T14:23:53.899Z [uncaughtException] Unknown interaction
DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ButtonInteraction.deferUpdate (C:\Users\<USER>\Desktop\disblock\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:312:22)
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:640:7)
---
2025-08-06T14:23:53.898Z [uncaughtException] Unknown interaction
DiscordAPIError[10062]: Unknown interaction
    at handleErrors (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:748:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async BurstHandler.runRequest (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:852:23)
    at async _REST.request (C:\Users\<USER>\Desktop\disblock\node_modules\@discordjs\rest\dist\index.js:1293:22)
    at async ButtonInteraction.deferUpdate (C:\Users\<USER>\Desktop\disblock\node_modules\discord.js\src\structures\interfaces\InteractionResponses.js:312:22)
    at async Client.<anonymous> (C:\Users\<USER>\Desktop\disblock\worker-bot.js:640:7)
---
