{"name": "Cobblestone Minion", "emoji": "<:minion_cobblestone:1367698365449506826>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "mining", "drops": [{"itemKey": "COBBLESTONE", "chance": 1}], "recipes": [{"ingredients": [{"itemKey": "COBBLESTONE", "amount": 80}]}], "craftingRequirements": {"collections": {"COBBLESTONE": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 14, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 14, "maxStorage": 192, "upgradeCost": [{"itemKey": "COBBLESTONE", "amount": 160}]}, {"tier": 3, "generationIntervalSeconds": 12, "maxStorage": 192, "upgradeCost": [{"itemKey": "COBBLESTONE", "amount": 320}]}, {"tier": 4, "generationIntervalSeconds": 12, "maxStorage": 384, "upgradeCost": [{"itemKey": "COBBLESTONE", "amount": 512}]}, {"tier": 5, "generationIntervalSeconds": 10, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 8}]}, {"tier": 6, "generationIntervalSeconds": 10, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 16}]}, {"tier": 7, "generationIntervalSeconds": 9, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 32}]}, {"tier": 8, "generationIntervalSeconds": 9, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 64}]}, {"tier": 9, "generationIntervalSeconds": 8, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 128}]}, {"tier": 10, "generationIntervalSeconds": 8, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 256}]}, {"tier": 11, "generationIntervalSeconds": 7, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_COBBLESTONE", "amount": 512}]}]}