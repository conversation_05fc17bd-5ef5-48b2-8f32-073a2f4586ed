const { dbRunQueued } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");
const { withMinionLock } = require("./minionMutex");

/**
 * Atomically moves a minion from storage to placed minions
 * @param {string} userId - Discord user ID
 * @param {string} minionId - ID of minion to place
 * @returns {Promise<{success: boolean, error?: string, character?: Object}>}
 */
async function atomicMinionPlace(userId, minionId) {
  return await withMinionLock(userId, "place", async () => {
    try {
      // get current character data
      const character = await getPlayerData(userId);
      if (!character) {
        return { success: false, error: "Character not found" };
      }

      const storageIndex = character.minionStorage.findIndex(
        (m) => m.id === minionId
      );
      if (storageIndex === -1) {
        return { success: false, error: "Minion not found in storage" };
      }

      const minionToPlace = character.minionStorage[storageIndex];

      // initialize placement data
      minionToPlace.lastCollectionTimestamp = Date.now();
      minionToPlace.resourcesStored = {};

      // prepare updated data structures
      const updatedStorage = [...character.minionStorage];
      updatedStorage.splice(storageIndex, 1);

      if (!character.island) character.island = {};
      if (!character.island.placedMinions) character.island.placedMinions = [];

      const updatedPlacedMinions = [
        ...character.island.placedMinions,
        minionToPlace,
      ];
      const updatedIsland = {
        ...character.island,
        placedMinions: updatedPlacedMinions,
      };

      // atomic database update - both minion storage and island in one transaction
      await dbRunQueued(
        `UPDATE players SET 
        minion_storage_json = ?, 
        island_json = ? 
      WHERE discord_id = ?`,
        [JSON.stringify(updatedStorage), JSON.stringify(updatedIsland), userId]
      );

      // update character object for return
      character.minionStorage = updatedStorage;
      character.island = updatedIsland;

      return { success: true, character };
    } catch (error) {
      console.error(
        `[AtomicMinionPlace] Error for ${userId}, minion ${minionId}:`,
        error
      );
      return { success: false, error: error.message };
    }
  }); // End of withMinionLock
}

/**
 * Atomically moves a minion from placed back to storage with resource collection
 * @param {string} userId - Discord user ID
 * @param {string} minionId - ID of minion to remove
 * @returns {Promise<{success: boolean, error?: string, character?: Object, itemsReturned?: Array}>}
 */
async function atomicMinionRemove(userId, minionId) {
  return await withMinionLock(userId, "remove", async () => {
    try {
      const character = await getPlayerData(userId);
      if (!character) {
        return { success: false, error: "Character not found" };
      }

      const placedMinions = character.island?.placedMinions || [];
      const minionIndex = placedMinions.findIndex((m) => m.id === minionId);

      if (minionIndex === -1) {
        return { success: false, error: "Minion not found on island" };
      }

      const minionToRemove = placedMinions[minionIndex];

      // collect stored resources and upgrades
      const storedResourcesToReturn = [];
      let coinsToReturn = 0;

      if (minionToRemove.resourcesStored) {
        for (const [resourceKey, amount] of Object.entries(
          minionToRemove.resourcesStored
        )) {
          if (amount > 0) {
            if (resourceKey === "COINS") {
              coinsToReturn += amount;
            } else {
              storedResourcesToReturn.push({ itemKey: resourceKey, amount });
            }
          }
        }
      }

      // collect upgrade items
      const upgradeItemsToReturn = [];
      if (Array.isArray(minionToRemove.upgrades)) {
        minionToRemove.upgrades.forEach((upgradeKey) => {
          if (upgradeKey) {
            upgradeItemsToReturn.push({ itemKey: upgradeKey, amount: 1 });
          }
        });
      }

      // prepare minion for storage (clean state)
      const minionForStorage = {
        id: minionToRemove.id,
        itemKey: minionToRemove.itemKey,
        tier: minionToRemove.tier,
        resourcesStored: {},
        lastCollectionTimestamp: null,
        upgrades: [],
      };

      // prepare updated data structures
      const updatedPlacedMinions = [...placedMinions];
      updatedPlacedMinions.splice(minionIndex, 1);

      const updatedStorage = [
        ...(character.minionStorage || []),
        minionForStorage,
      ];
      const updatedIsland = {
        ...(character.island || {}),
        placedMinions: updatedPlacedMinions,
      };

      const allItemsToReturn = [
        ...storedResourcesToReturn,
        ...upgradeItemsToReturn,
      ];

      // use dbRunQueued for all operations - the writeQueue will batch them atomically
      // Update inventory with returned items
      if (allItemsToReturn.length > 0 || coinsToReturn > 0) {
        // Update coins if needed
        if (coinsToReturn > 0) {
          await dbRunQueued(
            "UPDATE players SET coins = coins + ? WHERE discord_id = ?",
            [coinsToReturn, userId]
          );
        }

        // Update inventory items using the correct table name and column
        for (const item of allItemsToReturn) {
          await dbRunQueued(
            `INSERT INTO player_inventory_items (discord_id, item_name, amount) 
           VALUES (?, ?, ?)
           ON CONFLICT(discord_id, item_name) 
           DO UPDATE SET amount = amount + ?`,
            [userId, item.itemKey, item.amount, item.amount]
          );
        }
      }

      // Update both island and minion storage in single query
      await dbRunQueued(
        `UPDATE players SET 
        island_json = ?, 
        minion_storage_json = ? 
      WHERE discord_id = ?`,
        [JSON.stringify(updatedIsland), JSON.stringify(updatedStorage), userId]
      );

      // update character object for return
      character.island = updatedIsland;
      character.minionStorage = updatedStorage;

      return {
        success: true,
        character,
        itemsReturned: allItemsToReturn,
        coinsReturned: coinsToReturn,
      };
    } catch (error) {
      console.error(
        `[AtomicMinionRemove] Error for ${userId}, minion ${minionId}:`,
        error
      );
      return { success: false, error: error.message };
    }
  }); // End of withMinionLock
}

module.exports = {
  atomicMinionPlace,
  atomicMinionRemove,
};
