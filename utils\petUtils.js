const { v4: uuidv4 } = require("uuid");
const { calculateLevelFromTotalXp } = require("../data/petLeveling");

/**
 * Creates a standardized pet instance object.
 *
 * @param {string} itemKey - The base item key for the pet (e.g., 'SILVERFISH_PET').
 * @param {string} rarity - The determined rarity for this instance (e.g., 'COMMON', 'RARE').
 * @param {number} [totalExp=0] - The total experience points (defaults to 0). Level is calculated from this.
 * @returns {object | null} A pet instance object with a unique petId, or null if itemKey is missing.
 */
function createPetInstance(itemKey, rarity, totalExp = 0) {
  if (!itemKey) {
    console.error("[createPetInstance] Missing itemKey.");
    return null;
  }
  if (!rarity) {
    console.error(`[createPetInstance] Missing rarity for pet ${itemKey}.`);
    // Decide if default rarity is acceptable or if it should fail
    // For now, let's make it fail to ensure rarity is always passed from crafting/drops
    return null;
  }

  return {
    id: uuidv4(), // CORRECTED: Changed petId to id for consistency
    petKey: itemKey,
    rarity: rarity,
    totalExp: Math.max(0, totalExp), // Store only total XP, level is calculated dynamically
    createdAt: Date.now(), // Timestamp when the pet was created
    // Add any other default instance-specific properties here if needed
  };
}

/**
 * Migrates a pet from old format to new format in-place
 * @param {object} pet - Pet object to migrate
 * @returns {boolean} - Whether migration was performed
 */
function migratePetInPlace(pet) {
  // If already in new format, no migration needed
  if (
    pet.totalExp !== undefined &&
    pet.level === undefined &&
    pet.exp === undefined &&
    pet.xp === undefined
  ) {
    return false;
  }

  // Calculate total XP from old format
  let totalExp = 0;

  if (pet.totalExp !== undefined) {
    totalExp = pet.totalExp;
  } else if (pet.exp !== undefined) {
    totalExp = pet.exp;
  } else if (pet.xp !== undefined) {
    totalExp = pet.xp;
  }

  // Update pet to new format
  pet.totalExp = Math.max(0, totalExp);

  // Remove old fields
  if (pet.level !== undefined) delete pet.level;
  if (pet.exp !== undefined) delete pet.exp;
  if (pet.xp !== undefined) delete pet.xp;

  // Pet migration completed silently
  return true;
}

/**
 * Get the current level of a pet based on its total XP
 * @param {object} pet - Pet object with rarity and totalExp
 * @returns {number} - Current level of the pet
 */
function getPetLevel(pet) {
  // Auto-migrate legacy pets
  migratePetInPlace(pet);

  // Now use totalExp (guaranteed to exist after migration)
  return calculateLevelFromTotalXp(pet.rarity, pet.totalExp).level;
}

/**
 * Get the current XP within the current level for a pet
 * @param {object} pet - Pet object with rarity and totalExp
 * @returns {number} - XP within current level
 */
function getPetExpInLevel(pet) {
  // Auto-migrate legacy pets
  migratePetInPlace(pet);

  // Now use totalExp (guaranteed to exist after migration)
  return calculateLevelFromTotalXp(pet.rarity, pet.totalExp).expInLevel;
}

/**
 * Get the total XP for a pet, handling legacy formats
 * @param {object} pet - Pet object
 * @returns {number} - Total XP
 */
function getPetTotalExp(pet) {
  // Auto-migrate legacy pets
  migratePetInPlace(pet);

  // Now use totalExp (guaranteed to exist after migration)
  return pet.totalExp;
}

module.exports = {
  createPetInstance,
  getPetLevel,
  getPetExpInLevel,
  getPetTotalExp,
  migratePetInPlace,
};
