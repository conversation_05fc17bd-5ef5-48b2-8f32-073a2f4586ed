/**
 * Utility functions for searching and matching items by name or key
 * Used by both /craft and /recipe commands for consistent behavior
 */

const configManager = require("./configManager");

/**
 * Normalizes a string by removing spaces and underscores for flexible matching
 * @param {string} str - The string to normalize
 * @returns {string} - The normalized string
 */
function normalizeString(str) {
  return str.toLowerCase().replace(/[_\s]/g, "");
}

/**
 * Finds an item by key or name with flexible matching
 * @param {string} input - The user input (item name or key)
 * @param {Object} allItems - The items object from configManager
 * @returns {string|null} - The item key if found, null otherwise
 */
function findItemByKeyOrName(input, allItems = null) {
  if (!allItems) {
    allItems = configManager.getAllItems();
  }

  const upperInput = input.toUpperCase();

  // Try direct key lookup first
  if (allItems[upperInput]) {
    return upperInput;
  }

  // If not found directly, try flexible matching
  const originalInput = input;

  const foundByName = Object.entries(allItems).find(([key, data]) => {
    if (!data || !data.name) return false;

    // Normalize both the input and item name/key for comparison
    const normalizedInput = normalizeString(originalInput);
    const normalizedName = normalizeString(data.name);
    const normalizedKey = normalizeString(key);

    return (
      normalizedName === normalizedInput ||
      normalizedKey === normalizedInput ||
      data.name.toLowerCase() === originalInput.toLowerCase() ||
      key.toLowerCase() === originalInput.toLowerCase()
    );
  });

  return foundByName ? foundByName[0] : null;
}

/**
 * Checks if an item matches the search query using flexible matching
 * Used for autocomplete filtering
 * @param {string} itemKey - The item key
 * @param {Object} itemData - The item data object
 * @param {string} searchQuery - The search query
 * @returns {boolean} - True if the item matches the search
 */
function itemMatchesSearch(itemKey, itemData, searchQuery) {
  if (!itemData || !itemData.name) return false;

  const itemNameLower = itemData.name.toLowerCase();
  const itemKeyLower = itemKey.toLowerCase();
  const searchLower = searchQuery.toLowerCase();

  // Create normalized versions for more flexible matching
  const normalizedSearchQuery = normalizeString(searchQuery);
  const normalizedItemName = normalizeString(itemData.name);
  const normalizedItemKey = normalizeString(itemKey);

  return (
    itemNameLower.includes(searchLower) ||
    itemKeyLower.includes(searchLower) ||
    normalizedItemName.includes(normalizedSearchQuery) ||
    normalizedItemKey.includes(normalizedSearchQuery)
  );
}

module.exports = {
  normalizeString,
  findItemByKeyOrName,
  itemMatchesSearch,
};
