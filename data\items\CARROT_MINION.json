{"name": "Carrot Minion", "emoji": "<:minion_carrot:1367967311356493824>", "type": "MINION", "isMinion": true, "rarity": "COMMON", "unique": true, "sellable": false, "category": "farming", "drops": [{"itemKey": "CARROT", "chance": 1}], "recipes": [{"ingredients": [{"itemKey": "CARROT", "amount": 128}]}], "craftingRequirements": {"collections": {"CARROT": 1}}, "tiers": [null, {"tier": 1, "generationIntervalSeconds": 20, "maxStorage": 64}, {"tier": 2, "generationIntervalSeconds": 20, "maxStorage": 192, "upgradeCost": [{"itemKey": "CARROT", "amount": 256}]}, {"tier": 3, "generationIntervalSeconds": 18, "maxStorage": 192, "upgradeCost": [{"itemKey": "CARROT", "amount": 512}]}, {"tier": 4, "generationIntervalSeconds": 18, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 8}]}, {"tier": 5, "generationIntervalSeconds": 16, "maxStorage": 384, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 24}]}, {"tier": 6, "generationIntervalSeconds": 16, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 64}]}, {"tier": 7, "generationIntervalSeconds": 14, "maxStorage": 576, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 128}]}, {"tier": 8, "generationIntervalSeconds": 14, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 256}]}, {"tier": 9, "generationIntervalSeconds": 12, "maxStorage": 768, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 512}]}, {"tier": 10, "generationIntervalSeconds": 12, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 1024}]}, {"tier": 11, "generationIntervalSeconds": 10, "maxStorage": 960, "upgradeCost": [{"itemKey": "ENCHANTED_CARROT", "amount": 2048}]}]}