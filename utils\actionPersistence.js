const { dbRunQueued, dbGet, dbAll } = require("./dbUtils"); // Import shared helpers

// OPTIMIZATION: Cache for JSON parsing to reduce redundant operations
const jsonCache = new Map();
const MAX_CACHE_SIZE = 1000;

function getCachedJson(key, jsonString) {
  if (jsonCache.has(key)) {
    return jsonCache.get(key);
  }

  try {
    const parsed = JSON.parse(jsonString);

    // Implement simple LRU cache
    if (jsonCache.size >= MAX_CACHE_SIZE) {
      const firstKey = jsonCache.keys().next().value;
      jsonCache.delete(firstKey);
    }

    jsonCache.set(key, parsed);
    return parsed;
  } catch (e) {
    console.error(`Failed to parse JSON for key ${key}:`, e);
    return {};
  }
}

/**
 * Starts tracking a multi-step action in the database.
 * NOTE: Commands must check for existing actions before calling this function.
 * @param {string} userId Discord User ID.
 * @param {string} actionType Type of action (e.g., 'farming', 'mining').
 * @param {string|null} resourceKey The primary resource target (e.g., crop key, mob key), or null if none (e.g., fishing).
 * @param {object} parameters Action-specific parameters (e.g., { cropItemKey: 'WHEAT' }).
 * @param {number} totalAmount Total cycles requested.
 * @param {string} channelId Channel ID where the action was initiated.
 * @param {string} messageId Message ID associated with the action display.
 * @returns {Promise<number>} The database ID of the newly created action record.
 */
async function startAction(
  userId,
  actionType,
  resourceKey,
  parameters,
  totalAmount,
  channelId,
  messageId,
  workerId // must always be set for new actions; legacy actions without workerId should be cleaned up
) {
  const parametersJson = JSON.stringify(parameters);
  const startTime = Date.now();
  // remove any existing active action for this user to avoid unique constraint
  await dbRunQueued("DELETE FROM active_actions WHERE user_id = ?", [userId]);

  // No cleanup needed - commands already check for active actions before calling startAction

  const sql = `
        INSERT INTO active_actions (
            user_id, action_type, resource_key, parameters_json, total_amount,
            completed_cycles, start_timestamp, channel_id,
            message_id, last_update_timestamp, worker_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

  const params = [
    userId,
    actionType,
    resourceKey,
    parametersJson,
    totalAmount,
    0,
    startTime,
    channelId,
    messageId,
    startTime,
    workerId,
  ];

  const result = await dbRunQueued(sql, params);

  return result.lastID; // Return the ID of the new row
}

/**
 * Updates the progress of an ongoing action using cumulative progress tracking.
 * @param {number} actionId The database ID of the action record.
 * @param {number} completedCycles The new number of completed cycles.
 * @param {object | null} currentCycleResult The detailed (lean) result of the cycle that just completed.
 * @returns {Promise<void>}
 */
async function updateActionProgress(
  actionId,
  completedCycles,
  currentCycleResult = null
) {
  const now = Date.now();
  let loadedParams = {}; // To store what's loaded from DB

  try {
    loadedParams = await getActionParameters(actionId);
  } catch (error) {
    console.error(
      `[ActionPersistence][updateActionProgress] Error fetching parameters for actionId ${actionId}:`,
      error
    );
    loadedParams = {}; // Default to empty on error
  }

  // Initialize or get existing cumulative progress
  let cumulativeProgress = loadedParams.cumulativeProgress || {
    totalExp: 0,
    totalCoins: 0,
    totalItems: {},
    totalCombatWisdomBonus: 0,
    seaCreaturesDefeated: {},
    petsGained: [],
    completedCycles: 0,
    playerDefeated: false,
    defeatedBy: null,
    defeatedMobs: {},
    slayerXpByType: {},
  };

  // Handle migration from old cycle_results format
  if (
    !loadedParams.cumulativeProgress &&
    loadedParams.cycle_results &&
    Array.isArray(loadedParams.cycle_results)
  ) {
    console.log(
      `[ActionPersistence][${actionId}] Migrating from old cycle_results format (${loadedParams.cycle_results.length} entries)`
    );
    cumulativeProgress = migrateCycleResultsToCumulative(
      loadedParams.cycle_results
    );
    // Remove old format to save space
    delete loadedParams.cycle_results;
  }

  // Idempotency: only apply the delta if we're moving forward in completed cycles
  const prevCompleted =
    typeof cumulativeProgress.completedCycles === "number"
      ? cumulativeProgress.completedCycles
      : 0;
  const isNewCycleAdvance =
    typeof completedCycles === "number" && completedCycles > prevCompleted;

  // Update cumulative progress with new cycle result (only on forward progress)
  if (currentCycleResult !== null && isNewCycleAdvance) {
    cumulativeProgress.totalExp += currentCycleResult.exp || 0;
    cumulativeProgress.totalCoins += currentCycleResult.coins || 0;
    cumulativeProgress.totalCombatWisdomBonus +=
      currentCycleResult.combatWisdomBonus || 0;
    cumulativeProgress.completedCycles = completedCycles;

    // Add items to cumulative totals from unified droppedItems
    if (
      currentCycleResult.droppedItems &&
      Array.isArray(currentCycleResult.droppedItems)
    ) {
      currentCycleResult.droppedItems.forEach((item) => {
        if (item && item.itemKey && item.amount > 0) {
          if (item.itemType === "PET") {
            // Handle pet drops - add to petsGained for display
            for (let i = 0; i < item.amount; i++) {
              cumulativeProgress.petsGained.push({ itemKey: item.itemKey });
            }
          } else if (item.itemType !== "ACCESSORY") {
            // Only track non-accessory, non-pet items in cumulative totals
            cumulativeProgress.totalItems[item.itemKey] =
              (cumulativeProgress.totalItems[item.itemKey] || 0) + item.amount;
          }
        }
      });
    }

    // Handle fishing-specific data
    if (
      currentCycleResult.seaCreatureKey &&
      !currentCycleResult.defeated &&
      !currentCycleResult.fled
    ) {
      cumulativeProgress.seaCreaturesDefeated[
        currentCycleResult.seaCreatureKey
      ] =
        (cumulativeProgress.seaCreaturesDefeated[
          currentCycleResult.seaCreatureKey
        ] || 0) + 1;
    }

    // Handle pet gains
    if (currentCycleResult.petToAdd) {
      cumulativeProgress.petsGained.push(currentCycleResult.petToAdd);
    }

    // Handle combat defeats
    if (currentCycleResult.defeated) {
      cumulativeProgress.playerDefeated = true;
      cumulativeProgress.defeatedBy = currentCycleResult.defeatedBy;
    }

    // Handle defeated mobs tracking for combat
    if (currentCycleResult.defeatedMobData) {
      // Handle both optimized and detailed formats
      if (typeof currentCycleResult.defeatedMobData === "object") {
        // Check if it's optimized format (keys are mob keys, values are numbers)
        const isOptimizedFormat = Object.values(
          currentCycleResult.defeatedMobData
        ).every((v) => typeof v === "number");

        if (isOptimizedFormat) {
          // Optimized format: merge counts
          for (const [mobKey, count] of Object.entries(
            currentCycleResult.defeatedMobData
          )) {
            if (!cumulativeProgress.defeatedMobs[mobKey]) {
              cumulativeProgress.defeatedMobs[mobKey] = 0;
            }
            cumulativeProgress.defeatedMobs[mobKey] += count;
          }
        } else {
          // Detailed format (legacy): use original logic
          const mobKey = `${currentCycleResult.defeatedMobData.name}|${currentCycleResult.defeatedMobData.emoji || ""}`;
          if (!cumulativeProgress.defeatedMobs[mobKey]) {
            cumulativeProgress.defeatedMobs[mobKey] = {
              name: currentCycleResult.defeatedMobData.name,
              emoji: currentCycleResult.defeatedMobData.emoji || "",
              count: 0,
            };
          }
          cumulativeProgress.defeatedMobs[mobKey].count++;
        }
      }
    }

    // Handle slayer XP tracking for combat by type
    if (currentCycleResult.slayer_xp_jsonByType) {
      for (const [slayerType, xpAmount] of Object.entries(
        currentCycleResult.slayer_xp_jsonByType
      )) {
        if (!cumulativeProgress.slayer_xp_jsonByType[slayerType]) {
          cumulativeProgress.slayer_xp_jsonByType[slayerType] = 0;
        }
        cumulativeProgress.slayer_xp_jsonByType[slayerType] += xpAmount;
      }
    }

    // Legacy support for old totalSlayerXp format (convert to zombie type for compatibility)
    if (
      currentCycleResult.slayerXp &&
      !currentCycleResult.slayer_xp_jsonByType
    ) {
      if (!cumulativeProgress.slayer_xp_jsonByType.zombie) {
        cumulativeProgress.slayer_xp_jsonByType.zombie = 0;
      }
      cumulativeProgress.slayer_xp_jsonByType.zombie +=
        currentCycleResult.slayerXp;
    }
  }

  // Always ensure completedCycles in cumulativeProgress is at least the max seen
  if (!isNewCycleAdvance) {
    cumulativeProgress.completedCycles = Math.max(
      prevCompleted,
      completedCycles || 0
    );
  }

  // Preserve other top-level keys and set cumulative progress
  const parametersToActuallySave =
    typeof loadedParams === "object" && loadedParams !== null
      ? { ...loadedParams }
      : {};
  parametersToActuallySave.cumulativeProgress = cumulativeProgress;

  let sql =
    "UPDATE active_actions SET completed_cycles = ?, last_update_timestamp = ?";
  const queryParams = [completedCycles, now];

  // Only update parameters_json if it's non-empty or if it was intentionally cleared
  // This condition ensures we don't write an empty {cycle_results:[]} if it was already null/empty and no new cycles were added.
  // However, if it HAD content and now cycleResultsArray is empty (e.g. data corruption cleared it), we should write the empty array.
  // A simpler rule: always write parameters_json if currentCycleResult was processed, or if the array isn't empty.
  // This also handles cleaning up bloated records by overwriting them with a clean structure.
  // Always include parameters_json in the update if we intend to manage it.
  sql += ", parameters_json = ?";
  queryParams.push(JSON.stringify(parametersToActuallySave));

  sql += " WHERE id = ?";
  queryParams.push(actionId);

  await dbRunQueued(sql, queryParams);
}

/**
 * Removes an action record from the database upon completion or cancellation.
 * @param {number} actionId The database ID of the action record.
 * @returns {Promise<void>}
 */
async function completeAction(actionId) {
  // Get the userId before deleting
  const action = await dbGet(
    "SELECT user_id FROM active_actions WHERE id = ?",
    [actionId]
  );
  await dbRunQueued("DELETE FROM active_actions WHERE id = ?", [actionId]);
  // Release user assignment if present
  if (action && action.user_id) {
    try {
      const { workerManager } = require("./workerManager.js");
      workerManager.releaseUser(action.user_id);
    } catch (err) {
      console.warn(
        `[ActionPersistence] Could not release user ${action.user_id}:`,
        err
      );
    }
  }
}

/**
 * Retrieves a specific action by its database ID.
 * @param {number} actionId The database ID of the action record.
 * @returns {Promise<object|null>} The action record or null if not found.
 */
async function getActionById(actionId) {
  // Select all relevant columns, including worker_id
  return await dbGet(
    "SELECT id, user_id, action_type, resource_key, parameters_json, total_amount, completed_cycles, channel_id, message_id, start_timestamp, last_update_timestamp, worker_id FROM active_actions WHERE id = ?",
    [actionId]
  );
}

/**
 * Retrieves all pending actions from the database. Used on bot startup.
 * @returns {Promise<Array<object>>} An array of pending action records.
 */
async function getAllPendingActions() {
  try {
    // Ensure all necessary columns are selected, including new ones
    // const actions = await dbAllAsync(
    const actions = await dbAll(
      "SELECT id, user_id, action_type, resource_key, parameters_json, total_amount, completed_cycles, channel_id, message_id, start_timestamp, worker_id FROM active_actions"
    );

    return actions;
  } catch (err) {
    console.error("Error getting all pending actions:", err);
    return [];
  }
}

/**
 * Fetch and parse the parameters_json for a given action.
 * @param {number} actionId
 * @returns {Promise<object>} Parsed parameters object, or {} if not found.
 */
async function getActionParameters(actionId) {
  try {
    const sql = "SELECT parameters_json FROM active_actions WHERE id = ?";
    const row = await dbGet(sql, [actionId]);
    if (!row || !row.parameters_json) return {};

    // OPTIMIZATION: Use cached JSON parsing
    const cacheKey = `action_params_${actionId}_${row.parameters_json.slice(0, 50)}`;
    const params = getCachedJson(cacheKey, row.parameters_json);

    // Initialize cumulative progress if it doesn't exist
    if (!params.cumulativeProgress) {
      params.cumulativeProgress = {
        totalExp: 0,
        totalCoins: 0,
        totalItems: {},
        totalCombatWisdomBonus: 0,
        seaCreaturesDefeated: {},
        petsGained: [],
        completedCycles: 0,
        playerDefeated: false,
        defeatedBy: null,
        defeatedMobs: {},
        slayerXpByType: {},
      };
    }

    // Migrate old totalSlayerXp format to slayerXpByType
    if (
      params.cumulativeProgress.totalSlayerXp &&
      !params.cumulativeProgress.slayer_xp_jsonByType
    ) {
      params.cumulativeProgress.slayer_xp_jsonByType = {};
    }
    if (
      params.cumulativeProgress.totalSlayerXp > 0 &&
      (!params.cumulativeProgress.slayer_xp_jsonByType.zombie ||
        params.cumulativeProgress.slayer_xp_jsonByType.zombie === 0)
    ) {
      if (!params.cumulativeProgress.slayer_xp_jsonByType.zombie) {
        params.cumulativeProgress.slayer_xp_jsonByType.zombie = 0;
      }
      params.cumulativeProgress.slayer_xp_jsonByType.zombie +=
        params.cumulativeProgress.totalSlayerXp;
      delete params.cumulativeProgress.totalSlayerXp; // Remove old format
    }

    return params;
  } catch (e) {
    console.error(
      `[ActionPersist] Failed to parse parameters_json for action ${actionId}. Error:`,
      e
    );
    return {
      cumulativeProgress: {
        totalExp: 0,
        totalCoins: 0,
        totalItems: {},
        totalCombatWisdomBonus: 0,
        seaCreaturesDefeated: {},
        petsGained: [],
        completedCycles: 0,
        playerDefeated: false,
        defeatedBy: null,
        defeatedMobs: {},
        slayerXpByType: {},
      },
    }; // Return a default structure on error
  }
}

/**
 * Migrates old cycle_results array format to new cumulative progress format.
 * @param {Array} cycleResults Array of individual cycle result objects.
 * @returns {object} Cumulative progress object.
 */
function migrateCycleResultsToCumulative(cycleResults) {
  const cumulative = {
    totalExp: 0,
    totalCoins: 0,
    totalItems: {},
    totalCombatWisdomBonus: 0,
    seaCreaturesDefeated: {},
    petsGained: [],
    completedCycles: 0,
    playerDefeated: false,
    defeatedBy: null,
    defeatedMobs: {},
    slayerXpByType: {},
  };

  if (!Array.isArray(cycleResults)) {
    return cumulative;
  }

  for (const savedCycle of cycleResults) {
    if (!savedCycle) continue;

    const count = savedCycle._count || 1;
    cumulative.totalExp += (savedCycle.exp || 0) * count;
    cumulative.totalCoins += (savedCycle.coins || 0) * count;
    cumulative.totalCombatWisdomBonus +=
      (savedCycle.combatWisdomBonus || 0) * count;

    // Process items
    if (Array.isArray(savedCycle.items)) {
      savedCycle.items.forEach((itemStack) => {
        if (itemStack && itemStack.itemKey && itemStack.amount > 0) {
          cumulative.totalItems[itemStack.itemKey] =
            (cumulative.totalItems[itemStack.itemKey] || 0) +
            itemStack.amount * count;
        }
      });
    }

    // Process sea creatures
    if (savedCycle.seaCreatureKey && !savedCycle.defeated && !savedCycle.fled) {
      cumulative.seaCreaturesDefeated[savedCycle.seaCreatureKey] =
        (cumulative.seaCreaturesDefeated[savedCycle.seaCreatureKey] || 0) +
        count;
    }

    // Process pets
    if (savedCycle.petToAdd) {
      for (let i = 0; i < count; i++) {
        cumulative.petsGained.push(savedCycle.petToAdd);
      }
    }

    // Process defeats
    if (savedCycle.defeated) {
      cumulative.playerDefeated = true;
      cumulative.defeatedBy = savedCycle.defeatedBy;
    }

    // Process defeated mobs for combat
    if (savedCycle.defeatedMobData) {
      const mobKey = `${savedCycle.defeatedMobData.name}|${savedCycle.defeatedMobData.emoji || ""}`;
      if (!cumulative.defeatedMobs[mobKey]) {
        cumulative.defeatedMobs[mobKey] = {
          name: savedCycle.defeatedMobData.name,
          emoji: savedCycle.defeatedMobData.emoji || "",
          count: 0,
        };
      }
      cumulative.defeatedMobs[mobKey].count += count;
    }

    // Process slayer XP for combat by type
    if (savedCycle.slayer_xp_jsonByType) {
      for (const [slayerType, xpAmount] of Object.entries(
        savedCycle.slayer_xp_jsonByType
      )) {
        if (!cumulative.slayer_xp_jsonByType[slayerType]) {
          cumulative.slayer_xp_jsonByType[slayerType] = 0;
        }
        cumulative.slayer_xp_jsonByType[slayerType] += xpAmount * count;
      }
    }

    // Legacy support for old totalSlayerXp format (convert to zombie type for compatibility)
    if (savedCycle.slayerXp && !savedCycle.slayer_xp_jsonByType) {
      if (!cumulative.slayer_xp_jsonByType.zombie) {
        cumulative.slayer_xp_jsonByType.zombie = 0;
      }
      cumulative.slayer_xp_jsonByType.zombie += savedCycle.slayerXp * count;
    }
  }

  return cumulative;
}

/**
 * Update the parameters_json for a given action.
 * @param {number} actionId
 * @param {object} newParams
 * @returns {Promise<void>}
 */
async function updateActionParameters(actionId, newParams) {
  const sql = "UPDATE active_actions SET parameters_json = ? WHERE id = ?";
  await dbRunQueued(sql, [JSON.stringify(newParams), actionId]);
}

/**
 * Returns the first active action for a user, or null if none.
 * @param {string} userId Discord User ID.
 * @returns {Promise<object|null>} The active action record or null if not found.
 */
async function getActiveActionForUser(userId) {
  return await dbGet(
    "SELECT id, user_id, action_type, resource_key, parameters_json, total_amount, completed_cycles, channel_id, message_id, start_timestamp, last_update_timestamp FROM active_actions WHERE user_id = ? LIMIT 1",
    [userId]
  );
}

/**
 * Persists temporary state for an action that is expected to resume.
 * This is different from `updateActionProgress` which updates cumulative results.
 * This stores state needed for the *next* cycle or subsequent resumption.
 * @param {string} userId
 * @param {number} actionId
 * @param {object} pendingState The state to persist (e.g., next sea creature, current cycle index)
 */
async function persistPendingSkillAction(userId, actionId, pendingState) {
  // We store pending state within the parameters_json for now
  const currentParams = await getActionParameters(actionId);
  currentParams.pendingSkillAction = pendingState;
  await updateActionParameters(actionId, currentParams);
}

/**
 * Clears any pending state for a resumed action.
 * @param {number} actionId
 */
async function clearPendingSkillAction(actionId) {
  const currentParams = await getActionParameters(actionId);
  if (currentParams.pendingSkillAction) {
    delete currentParams.pendingSkillAction;
    await updateActionParameters(actionId, currentParams);
  }
}

/**
 * Updates the message ID associated with an active action in the database.
 * This is used when a message needs to be regenerated or replaced.
 * @param {number} actionId The database ID of the action record.
 * @param {string} newMessageId The new Discord message ID.
 * @returns {Promise<void>}
 */
async function updateActionMessageId(actionId, newMessageId) {
  const sql = "UPDATE active_actions SET message_id = ? WHERE id = ?";
  const params = [newMessageId, actionId];
  await dbRunQueued(sql, params);
  console.log(
    `[ActionPersistence] Updated message_id for action ${actionId} to ${newMessageId}`
  );
}

/**
 * Updates the message creation timestamp in the action parameters.
 * This is used to track message age for cycling purposes.
 * @param {number} actionId The database ID of the action record.
 * @param {number} timestamp The message creation timestamp.
 * @returns {Promise<void>}
 */
async function updateActionMessageTimestamp(actionId, timestamp) {
  try {
    const currentParams = await getActionParameters(actionId);
    currentParams.messageCreationTime = timestamp;
    await updateActionParameters(actionId, currentParams);
  } catch (error) {
    console.error(
      `[ActionPersistence] Error updating message timestamp for action ${actionId}:`,
      error
    );
  }
}

module.exports = {
  startAction,
  updateActionProgress,
  completeAction,
  getActionById,
  getAllPendingActions,
  getActionParameters,
  updateActionParameters,
  getActiveActionForUser,
  migrateCycleResultsToCumulative, // Export for testing
  persistPendingSkillAction,
  clearPendingSkillAction,
  updateActionMessageId,
  updateActionMessageTimestamp,
};
