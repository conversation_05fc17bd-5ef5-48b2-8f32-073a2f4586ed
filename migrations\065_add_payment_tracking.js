// Migration: 065_add_payment_tracking.js
// Purpose: Add payment tracking table for persistent duplicate prevention

const { dbRun } = require("../utils/dbUtils");

async function up() {
  console.log("[Migration 065] Adding payment tracking table...");

  try {
    await dbRun(`
      CREATE TABLE IF NOT EXISTS processed_payments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT UNIQUE NOT NULL,
        discord_id TEXT NOT NULL,
        gems_amount INTEGER NOT NULL,
        processed_at INTEGER NOT NULL,
        is_gift BOOLEAN DEFAULT FALSE,
        recipient_discord_id TEXT,
        package_id TEXT NOT NULL
      )
    `);

    await dbRun(`
      CREATE INDEX IF NOT EXISTS idx_processed_payments_processed_at ON processed_payments(processed_at)
    `);

    await dbRun(`
      CREATE INDEX IF NOT EXISTS idx_processed_payments_discord_id ON processed_payments(discord_id)
    `);

    console.log("[Migration 065] Successfully added payment tracking table");
  } catch (error) {
    console.error("[Migration 065] Error:", error);
    throw error;
  }
}

async function down() {
  console.log("[Migration 065] Removing payment tracking table...");

  try {
    await dbRun("DROP TABLE IF EXISTS processed_payments");
    console.log("[Migration 059] Successfully removed payment tracking table");
  } catch (error) {
    console.error("[Migration 059] Error in down migration:", error);
    throw error;
  }
}

module.exports = { up, down };
