const { updateActionProgress } = require("./actionPersistence");

/**
 * Batches database operations for action progress updates to improve performance
 * by reducing frequent database writes during long-running actions.
 */
class ActionProgressBatcher {
  constructor(options = {}) {
    this.batchSize = options.batchSize || 5; // Number of cycles to batch before writing
    this.maxBatchTime = options.maxBatchTime || 30000; // Maximum time to wait before flushing (30 seconds)
    this.batches = new Map(); // actionId -> batch data
    this.timers = new Map(); // actionId -> timer
    this.enabled = options.enabled !== false; // Allow disabling batching

    // For critical operations that need immediate persistence
    this.criticalOperations = new Set([
      "pet_gained",
      "player_defeated",
      "action_completed",
    ]);
  }

  /**
   * Adds a progress update to the batch or processes it immediately if critical
   * @param {number} actionId - The action ID
   * @param {number} completedCycles - Number of completed cycles
   * @param {object} cycleResult - Result of the current cycle
   * @param {boolean} isCritical - Whether this update needs immediate persistence
   */
  async addProgressUpdate(
    actionId,
    completedCycles,
    cycleResult,
    isCritical = false
  ) {
    if (!this.enabled || isCritical || this.hasCriticalData(cycleResult)) {
      return await updateActionProgress(actionId, completedCycles, cycleResult);
    }

    if (!this.batches.has(actionId)) {
      this.batches.set(actionId, { updates: [], startTime: Date.now() });
    }

    const batch = this.batches.get(actionId);
    batch.updates.push({ completedCycles, cycleResult, timestamp: Date.now() });

    if (
      batch.updates.length >= this.batchSize ||
      Date.now() - batch.startTime >= this.maxBatchTime
    ) {
      await this.flushBatch(actionId);
    } else {
      this.resetBatchTimer(actionId);
    }
  }

  /**
   * Checks if cycle result contains critical data that needs immediate persistence
   * @param {object} cycleResult - The cycle result to check
   * @returns {boolean} - True if critical data is present
   */
  hasCriticalData(cycleResult) {
    if (!cycleResult) return false;

    return !!(
      cycleResult.petToAdd || // Pet gained
      cycleResult.defeated || // Player defeated
      cycleResult.playerDefeated || // Player defeated (alternative field)
      cycleResult.fled || // Player fled
      cycleResult.seaCreatureKey // Sea creature encountered (fishing)
    );
  }

  /**
   * Flushes a batch of updates for a specific action
   * @param {number} actionId - The action ID to flush
   */
  async flushBatch(actionId) {
    const batch = this.batches.get(actionId);
    if (!batch || batch.updates.length === 0) return;

    try {
      // Clear timer
      if (this.timers.has(actionId)) {
        clearTimeout(this.timers.get(actionId));
        this.timers.delete(actionId);
      }

      // Process the most recent update with cumulative data
      const lastUpdate = batch.updates[batch.updates.length - 1];
      const cumulativeResult = this.mergeCycleResults(batch.updates);

      await updateActionProgress(
        actionId,
        lastUpdate.completedCycles,
        cumulativeResult
      );

      // Clear the batch
      this.batches.delete(actionId);
    } catch (error) {
      console.error(
        `[ActionProgressBatcher] Error flushing batch for action ${actionId}:`,
        error
      );
      // Clear the batch even on error to prevent memory leaks
      this.batches.delete(actionId);
    }
  }

  /**
   * Merges multiple cycle results into a cumulative result
   * @param {Array} updates - Array of update objects
   * @returns {object} - Merged cycle result
   */
  mergeCycleResults(updates) {
    const merged = {
      exp: 0,
      coins: 0,
      items: [],
      combatWisdomBonus: 0,
      timestamp: Date.now(),
    };

    const itemCounts = {};

    for (const update of updates) {
      const result = update.cycleResult;
      if (!result) continue;

      merged.exp += result.exp || 0;
      merged.coins += result.coins || 0;
      merged.combatWisdomBonus += result.combatWisdomBonus || 0;

      // Merge items (handle both old items array and new droppedItems array)
      const itemsArray = result.droppedItems || result.items;
      if (itemsArray && Array.isArray(itemsArray)) {
        itemsArray.forEach((item) => {
          if (item && item.itemKey && item.amount > 0) {
            itemCounts[item.itemKey] =
              (itemCounts[item.itemKey] || 0) + item.amount;
          }
        });
      }

      // Keep track of resource key from most recent update
      if (result.resourceKey) {
        merged.resourceKey = result.resourceKey;
      }
    }

    // Convert item counts back to both formats for compatibility
    const itemsArray = Object.entries(itemCounts).map(([itemKey, amount]) => ({
      itemKey,
      amount,
    }));
    merged.items = itemsArray; // Legacy format
    merged.droppedItems = itemsArray; // New format

    return merged;
  }

  /**
   * Sets or resets the batch timer for an action
   * @param {number} actionId - The action ID
   */
  resetBatchTimer(actionId) {
    if (this.timers.has(actionId)) {
      clearTimeout(this.timers.get(actionId));
    }

    const timer = setTimeout(() => {
      this.flushBatch(actionId);
    }, this.maxBatchTime);

    this.timers.set(actionId, timer);
  }

  /**
   * Flushes all pending batches (call on action completion or bot shutdown)
   */
  async flushAllBatches() {
    const actionIds = Array.from(this.batches.keys());
    const promises = actionIds.map((actionId) => this.flushBatch(actionId));
    await Promise.all(promises);
  }

  /**
   * Forces immediate flush of a specific action's batch
   * @param {number} actionId - The action ID to flush
   */
  async forceFlush(actionId) {
    await this.flushBatch(actionId);
  }

  /**
   * Clears all batches and timers (cleanup)
   */
  cleanup() {
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();
    this.batches.clear();
  }
}

// Create singleton instance
const batcher = new ActionProgressBatcher({
  // Batch a handful of cycles to cut write volume while keeping loss window tiny
  batchSize: 8, // flush every 8 cycles
  maxBatchTime: 3000, // or every 3 seconds, whichever comes first
  enabled: true,
});

module.exports = {
  ActionProgressBatcher,
  batcher,
};
