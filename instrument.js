// Import with `import * as Sentry from "@sentry/node"` if you are using ESM
const Sentry = require("@sentry/node");
// optional profiling import (tree-shaken if unused)
require("@sentry/profiling-node");

// allow disabling in local dev
const config = (() => {
  try {
    return require("./config.json");
  } catch {
    return {};
  }
})();
const SENTRY_DSN =
  process.env.SENTRY_DSN ||
  "https://<EMAIL>/4509558236381264"; // fallback to current hard-coded dsn
const CONFIG_ENV = config.sentryEnvironment;

if (SENTRY_DSN && SENTRY_DSN !== "disabled") {
  Sentry.init({
    dsn: SENTRY_DSN,
    sendDefaultPii: true, // ok for internal bot; remove if you don't want IPs etc
    tracesSampleRate: Number(process.env.SENTRY_TRACES_SAMPLE_RATE || 0.1), // lower default to reduce noise
    profilesSampleRate: Number(process.env.SENTRY_PROFILES_SAMPLE_RATE || 0.0), // off by default unless explicitly enabled
    environment:
      CONFIG_ENV ||
      process.env.SENTRY_ENV ||
      process.env.NODE_ENV ||
      "development",
    release: process.env.SENTRY_RELEASE || "1.0.0",
    maxBreadcrumbs: 50,
    // scrub & trim
    beforeSend(event) {
      try {
        // Remove file paths from stack traces
        if (event.exception?.values) {
          for (const exception of event.exception.values) {
            const frames = exception.stacktrace?.frames;
            if (frames) {
              for (const frame of frames) {
                if (frame.filename)
                  frame.filename = frame.filename.replace(/.*[\\/]/, "");
              }
            }
          }
        }
        if (event.extra) {
          for (const key of Object.keys(event.extra)) {
            if (typeof event.extra[key] === "string") {
              event.extra[key] = event.extra[key]
                .replace(
                  /[A-Za-z0-9_-]{23,28}\.[A-Za-z0-9_-]{6,7}\.[A-Za-z0-9_-]{27,}/g,
                  "[DISCORD_TOKEN]"
                )
                .replace(/[A-Za-z]:[^\s]+/g, "[FILE_PATH]");
            }
          }
        }
      } catch {
        // swallow scrub errors
      }
      return event;
    },
    // ignore totally noisy / expected errors
    ignoreErrors: ["Unknown interaction", /ECONNRESET/i, /ETIMEDOUT/i],
  });

  // helpful breadcrumb for boot
  Sentry.addBreadcrumb({
    category: "lifecycle",
    level: "info",
    message: "Sentry initialized",
  });

  // Flush on various exits so queued events actually ship
  const flush = async (reason) => {
    try {
      Sentry.addBreadcrumb({
        category: "lifecycle",
        level: "info",
        message: `process exiting: ${reason}`,
      });
      await Sentry.flush(2000); // give it up to 2s
    } catch {
      /* silent */
    }
  };
  process.on("beforeExit", () => flush("beforeExit"));
  process.on("exit", () => flush("exit"));
  process.on("SIGINT", () => flush("SIGINT"));
  process.on("SIGTERM", () => flush("SIGTERM"));
}

module.exports = Sentry; // allow requiring the configured instance if needed
