// Utilities not needed - using custom promisified helpers instead

function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) {
        console.error(
          `Database run error for query [${query}] with params [${params}]:`,
          err.message,
        );
        reject(err);
      } else {
        resolve({ lastID: this.lastID, changes: this.changes });
      }
    });
  });
}

function columnExists(db, tableName, columnName) {
  return new Promise((resolve, reject) => {
    db.all(`PRAGMA table_info(${tableName})`, (err, columns) => {
      if (err) {
        console.error(
          `Error fetching table info for ${tableName}:`,
          err.message,
        );
        return reject(err);
      }
      resolve(columns.some((col) => col.name === columnName));
    });
  });
}

module.exports = {
  async up(db) {
    console.log(
      "Applying migration: Adding mob_kills_json column to players table...",
    );
    const column = { name: "mob_kills_json", type: "TEXT" };
    try {
      const exists = await columnExists(db, "players", column.name);
      if (!exists) {
        console.log(`  -> Adding column '${column.name}'...`);
        await dbRunAsync(
          db,
          `ALTER TABLE players ADD COLUMN ${column.name} ${column.type}`,
        );
        console.log(`     Column '${column.name}' added.`);
      } else {
        console.log(`  -> Column '${column.name}' already exists. Skipping.`);
      }
      console.log("mob_kills_json column migration applied successfully.");
    } catch (err) {
      console.error("Migration failed:", err);
      throw err;
    }
  },
};
