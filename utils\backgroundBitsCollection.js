/**
 * Background Bits Collection System
 * Integrates with existing playerDataManager to automatically collect bits on player interactions
 */

const { collectPendingBits } = require("./bitsManager");

/**
 * Called whenever a player's data is loaded/accessed
 * Automatically collects any pending bits in the background
 * @param {string} userId - The player's Discord ID
 * @param {Object} character - The player's character data
 * @returns {Promise<Object>} Updated character data if bits were collected
 */
async function processBitsCollection(userId, character) {
  try {
    const bitsResult = await collectPendingBits(userId, character);

    if (bitsResult.bitsCollected > 0) {
      console.log(
        `[BitsCollection] Auto-collected ${bitsResult.bitsCollected} bits for user ${userId} (${bitsResult.collectionsProcessed} collections)`
      );
      return bitsResult.character; // Return updated character
    }

    return character; // Return original character if no bits collected
  } catch (error) {
    console.error(
      `[BitsCollection] Error auto-collecting bits for user ${userId}:`,
      error
    );
    return character; // Return original character on error
  }
}

module.exports = {
  processBitsCollection,
};
