// Migration 049: Remove unused database tables
// These tables are no longer referenced in the codebase and contain no data

module.exports = {
  async up(db) {
    console.log("[Migration 049] Removing unused database tables...");

    const tablesToRemove = [
      "player_last_actions",
      "player_skills",
      "player_stats",
    ];

    for (const tableName of tablesToRemove) {
      try {
        // Check if table exists before attempting to drop
        const tableExists = await new Promise((resolve, reject) => {
          db.get(
            "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
            [tableName],
            (err, row) => {
              if (err) reject(err);
              else resolve(!!row);
            },
          );
        });

        if (tableExists) {
          // Check row count before dropping
          const rowCount = await new Promise((resolve, reject) => {
            db.get(
              `SELECT COUNT(*) as count FROM ${tableName}`,
              (err, result) => {
                if (err) reject(err);
                else resolve(result.count);
              },
            );
          });

          console.log(
            `[Migration 049] Dropping table ${tableName} (${rowCount} rows)`,
          );

          await new Promise((resolve, reject) => {
            db.run(`DROP TABLE ${tableName}`, (err) => {
              if (err) reject(err);
              else resolve();
            });
          });

          console.log(
            `[Migration 049] Successfully dropped table ${tableName}`,
          );
        } else {
          console.log(
            `[Migration 049] Table ${tableName} does not exist, skipping`,
          );
        }
      } catch (error) {
        console.error(
          `[Migration 049] Error processing table ${tableName}:`,
          error,
        );
        throw error; // Re-throw to fail the migration
      }
    }

    console.log("[Migration 049] Unused table cleanup completed successfully");
  },

  async down(db) {
    console.log("[Migration 049] Rolling back unused table removal...");

    // Recreate the tables in their original format
    // Note: This will create empty tables as they had no data when removed

    // Recreate player_last_actions table
    await new Promise((resolve, reject) => {
      db.run(
        `
                CREATE TABLE IF NOT EXISTS player_last_actions (
                    discord_id TEXT NOT NULL,
                    action_name TEXT NOT NULL,
                    timestamp INTEGER NOT NULL,
                    FOREIGN KEY (discord_id) REFERENCES players(discord_id) ON DELETE CASCADE
                )
            `,
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });

    // Recreate player_skills table
    await new Promise((resolve, reject) => {
      db.run(
        `
                CREATE TABLE IF NOT EXISTS player_skills (
                    discord_id TEXT NOT NULL,
                    skill_name TEXT NOT NULL,
                    exp REAL DEFAULT 0,
                    level INTEGER DEFAULT 1,
                    PRIMARY KEY (discord_id, skill_name),
                    FOREIGN KEY (discord_id) REFERENCES players(discord_id) ON DELETE CASCADE
                )
            `,
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });

    // Recreate player_stats table
    await new Promise((resolve, reject) => {
      db.run(
        `
                CREATE TABLE IF NOT EXISTS player_stats (
                    discord_id TEXT NOT NULL,
                    stat_name TEXT NOT NULL,
                    base REAL DEFAULT 0,
                    from_levels REAL DEFAULT 0,
                    from_equipment REAL DEFAULT 0,
                    PRIMARY KEY (discord_id, stat_name),
                    FOREIGN KEY (discord_id) REFERENCES players(discord_id) ON DELETE CASCADE
                )
            `,
        (err) => {
          if (err) reject(err);
          else resolve();
        },
      );
    });

    console.log("[Migration 049] Rollback completed - empty tables recreated");
  },
};
