const db = require("./database");
const { getPlayerData } = require("./playerDataManager");
const { calculateTotalStat, calculateAllStats } = require("./statCalculations");
const { dbRunQueued } = require("./dbUtils");
const configManager = require("./configManager");

function dbAll(query, params) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

function calculateHealthRegen(character) {
  if (
    !character ||
    !character.stats ||
    !character.stats.HEALTH ||
    !character.stats.HEALTH_REGEN
  ) {
    console.error("Invalid character data for health regeneration calculation");
    return 0;
  }
  const maxHealth = calculateAllStats(character).HEALTH;
  let healthRegen = calculateTotalStat(character.stats.HEALTH_REGEN);

  // Check for health regen doubler effect from equipped items
  const hasHealthRegenDoubler = checkForHealthRegenDoubler(character);
  if (hasHealthRegenDoubler) {
    healthRegen *= 2; // Double the health regen
  }

  // Hypixel Skyblock formula
  const baseHealthGained = (1 + maxHealth / 100) * (healthRegen / 100);

  // Apply vitality modifier to health regeneration
  const { applyVitalityHealing } = require("./healingUtils");
  const finalHealthGained = applyVitalityHealing(character, baseHealthGained);

  return finalHealthGained;
}

function checkForHealthRegenDoubler(character) {
  if (!character) {
    return false;
  }

  const allItems = configManager.getAllItems();
  let equippedItems = [];

  // New system: character.equipment.equipped
  if (character.equipment && character.equipment.equipped) {
    const newSystemItems = Object.values(character.equipment.equipped)
      .filter((item) => item)
      .map((item) => item.itemKey);
    equippedItems = equippedItems.concat(newSystemItems);
  }

  // Old system: character.inventory.equipment (fallback)
  if (character.inventory && character.inventory.equipment) {
    const oldSystemItems = character.inventory.equipment
      .filter((item) => item.isEquipped)
      .map((item) => item.itemKey);
    equippedItems = equippedItems.concat(oldSystemItems);
  }

  // Check for health regen doubler effect
  for (const itemKey of equippedItems) {
    const itemData = allItems[itemKey];
    if (itemData && itemData.effects && itemData.effects.healthRegenDoubler) {
      return true;
    }
  }

  return false;
}

async function applyHealthRegenToAllPlayers() {
  try {
    // Select players who are not at full health
    // We need max health, which requires stats. We can join or do multiple queries.
    // Simpler approach: Get all players and filter/update. Less efficient for many players.
    // More efficient: Select IDs, get data individually. Still many queries.
    // Ideal: Complex SQL query or iterate and update directly.

    // Fetch discord_id and current_health for all players
    const playersToUpdate = await dbAll(
      "SELECT discord_id, current_health FROM players WHERE current_health IS NOT NULL AND current_health > 0"
    );

    for (const playerInfo of playersToUpdate) {
      // Need full player data to calculate max health
      const character = await getPlayerData(playerInfo.discord_id);
      if (!character) continue; // Skip if full data retrieval fails

      const maxHealth = calculateAllStats(character).HEALTH;
      let currentHealth = character.current_health;

      if (currentHealth <= 0) continue; // Don't regen dead players

      if (currentHealth < maxHealth) {
        const regenAmount = calculateHealthRegen(character);
        const newHealth = Math.min(maxHealth, currentHealth + regenAmount);
        if (newHealth > currentHealth) {
          await dbRunQueued(
            "UPDATE players SET current_health = ? WHERE discord_id = ?",
            [newHealth, character.discordId]
          );
        }
      }
    }
  } catch (error) {
    console.error("Error applying health regen:", error);
  }
}

module.exports = { calculateHealthRegen, applyHealthRegenToAllPlayers };
