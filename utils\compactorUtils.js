/**
 * Compacts items in a minion's storage based on equipped compactor upgrades
 * @param {Object} minion - The minion object with resourcesStored and upgrades
 * @param {Object} allItems - All items from configManager
 * @returns {Object} - { compacted: boolean, changes: Array<{from, to, amount}> }
 */
function processCompacting(minion, allItems) {
  if (!minion.upgrades || !Array.isArray(minion.upgrades)) {
    return { compacted: false, changes: [] };
  }

  // Check what compactor upgrades are equipped - only one type allowed
  let hasCompactor = false;
  let hasSuperCompactor = false;

  for (const upgradeKey of minion.upgrades) {
    if (!upgradeKey) continue;
    const upgrade = allItems[upgradeKey];
    if (upgrade?.upgradeEffect?.compactToBlocks) {
      hasCompactor = true;
    }
    if (upgrade?.upgradeEffect?.superCompactor) {
      hasSuperCompactor = true;
    }
  }

  // Super Compactor 3000 takes priority if both are somehow equipped
  if (hasSuperCompactor) {
    hasCompactor = false;
  }

  if (!hasCompactor && !hasSuperCompactor) {
    return { compacted: false, changes: [] };
  }

  const changes = [];
  const resourcesStored = minion.resourcesStored || {};

  // Apply the appropriate compacting logic
  if (hasSuperCompactor) {
    const compactingResults = performSuperCompacting(resourcesStored, allItems);
    changes.push(...compactingResults);
  } else if (hasCompactor) {
    // Regular compactor logic (convert to specific blocks only)
    const compactingResults = performRegularCompacting(resourcesStored);
    changes.push(...compactingResults);
  }

  // Apply changes to the minion's storage
  for (const change of changes) {
    // Remove source items
    if (resourcesStored[change.from]) {
      resourcesStored[change.from] -= change.sourceAmount;
      if (resourcesStored[change.from] <= 0) {
        delete resourcesStored[change.from];
      }
    }

    // Add compacted items
    resourcesStored[change.to] =
      (resourcesStored[change.to] || 0) + change.amount;
  }

  return { compacted: changes.length > 0, changes };
}

/**
 * Performs super compacting (items -> enchanted items)
 * @param {Object} resourcesStored - Current minion storage
 * @param {Object} allItems - All items from configManager
 * @returns {Array} - Array of compacting changes
 */
function performSuperCompacting(resourcesStored, allItems) {
  const changes = [];
  const workingStorage = { ...resourcesStored }; // Create a working copy

  // Keep applying compacting until no more changes can be made
  let madeChanges = true;
  while (madeChanges) {
    madeChanges = false;

    // Find all enchanted items and their recipes (exclude minions, equipment, accessories, and blocks)
    const enchantedItems = Object.entries(allItems).filter(([key, item]) => {
      return (
        item.recipes &&
        Array.isArray(item.recipes) &&
        item.recipes.length > 0 &&
        item.recipes[0].ingredients &&
        item.recipes[0].ingredients.length === 1 && // Only single-ingredient recipes
        (key.startsWith("ENCHANTED_") || item.name.startsWith("Enchanted ")) && // Only enchanted items
        item.type !== "MINION" && // Exclude minions
        item.type !== "ARMOR" && // Exclude armor
        item.type !== "WEAPON" && // Exclude weapons
        item.type !== "ACCESSORY" && // Exclude accessories (talismans, rings, artifacts)
        !item.isMinion && // Extra check for minions
        !key.startsWith("BLOCK_OF_")
      ); // Exclude block items (handled by regular compactor)
    });

    for (const [enchantedKey, enchantedItem] of enchantedItems) {
      const recipe = enchantedItem.recipes[0];
      const ingredient = recipe.ingredients[0];
      const sourceItemKey = ingredient.itemKey;
      const requiredAmount = ingredient.amount;

      // Check if we have enough of the source item in working storage
      const availableAmount = workingStorage[sourceItemKey] || 0;

      if (availableAmount >= requiredAmount) {
        const timesToCraft = Math.floor(availableAmount / requiredAmount);
        if (timesToCraft > 0) {
          changes.push({
            from: sourceItemKey,
            to: enchantedKey,
            sourceAmount: timesToCraft * requiredAmount,
            amount: timesToCraft,
            type: "super_compact",
          });

          // Update working storage
          workingStorage[sourceItemKey] -= timesToCraft * requiredAmount;
          if (workingStorage[sourceItemKey] <= 0) {
            delete workingStorage[sourceItemKey];
          }
          workingStorage[enchantedKey] =
            (workingStorage[enchantedKey] || 0) + timesToCraft;

          madeChanges = true;
        }
      }
    }
  }

  return changes;
}

/**
 * Performs regular compacting (items -> blocks)
 * @param {Object} resourcesStored - Current minion storage
 * @returns {Array} - Array of compacting changes
 */
function performRegularCompacting(resourcesStored) {
  const changes = [];

  // Define specific block conversions
  const blockConversions = {
    COAL: { block: "BLOCK_OF_COAL", ratio: 9 },
    IRON_INGOT: { block: "BLOCK_OF_IRON", ratio: 9 },
    GOLD_INGOT: { block: "BLOCK_OF_GOLD", ratio: 9 },
    DIAMOND: { block: "BLOCK_OF_DIAMOND", ratio: 9 },
    EMERALD: { block: "BLOCK_OF_EMERALD", ratio: 9 },
    REDSTONE: { block: "BLOCK_OF_REDSTONE", ratio: 9 },
    LAPIS_LAZULI: { block: "BLOCK_OF_LAPIS", ratio: 9 },
    CLAY_BALL: { block: "BLOCK_OF_CLAY", ratio: 4 },
    GLOWSTONE_DUST: { block: "GLOWSTONE", ratio: 4 },
  };

  for (const [itemKey, conversion] of Object.entries(blockConversions)) {
    const availableAmount = resourcesStored[itemKey] || 0;
    if (availableAmount >= conversion.ratio) {
      const blocksToMake = Math.floor(availableAmount / conversion.ratio);
      if (blocksToMake > 0) {
        changes.push({
          from: itemKey,
          to: conversion.block,
          sourceAmount: blocksToMake * conversion.ratio,
          amount: blocksToMake,
          type: "regular_compact",
        });
      }
    }
  }

  return changes;
}

/**
 * Gets a summary of what compacting would do without actually doing it
 * @param {Object} minion - The minion object
 * @param {Object} allItems - All items from configManager
 * @returns {Array} - Array of potential compacting operations
 */
function getCompactingPreview(minion, allItems) {
  if (!minion.upgrades || !Array.isArray(minion.upgrades)) {
    return [];
  }

  // Check what compactor upgrades are equipped
  let hasCompactor = false;
  let hasSuperCompactor = false;

  for (const upgradeKey of minion.upgrades) {
    if (!upgradeKey) continue;
    const upgrade = allItems[upgradeKey];
    if (upgrade?.upgradeEffect?.compactToBlocks) {
      hasCompactor = true;
    }
    if (upgrade?.upgradeEffect?.superCompactor) {
      hasSuperCompactor = true;
    }
  }

  if (!hasCompactor && !hasSuperCompactor) {
    return [];
  }

  const resourcesStored = minion.resourcesStored || {};
  let preview = [];

  if (hasSuperCompactor) {
    preview = performSuperCompacting(resourcesStored, allItems);
  } else if (hasCompactor) {
    preview = performRegularCompacting(resourcesStored);
  }

  return preview.map((change) => ({
    from: change.from,
    to: change.to,
    sourceAmount: change.sourceAmount,
    amount: change.amount,
    type: change.type,
    fromName: allItems[change.from]?.name || change.from,
    toName: allItems[change.to]?.name || change.to,
  }));
}

/**
 * Checks if equipping a new upgrade would create a compactor conflict
 * @param {Array} currentUpgrades - Current minion upgrades array
 * @param {string} newUpgradeKey - The upgrade being equipped
 * @param {number} slotIndex - The slot index where the upgrade is being equipped
 * @param {Object} allItems - All items from configManager
 * @returns {Object} - { hasConflict: boolean, conflictType: string, message: string }
 */
function checkCompactorConflict(
  currentUpgrades,
  newUpgradeKey,
  slotIndex,
  allItems
) {
  const newUpgrade = allItems[newUpgradeKey];
  if (!newUpgrade || newUpgrade.type !== "MINION_UPGRADE") {
    return { hasConflict: false };
  }

  const isNewCompactor = newUpgrade.upgradeEffect?.compactToBlocks;
  const isNewSuperCompactor = newUpgrade.upgradeEffect?.superCompactor;

  // If the new upgrade isn't a compactor, no conflict
  if (!isNewCompactor && !isNewSuperCompactor) {
    return { hasConflict: false };
  }

  // Check existing upgrades for compactor conflicts
  for (let i = 0; i < currentUpgrades.length; i++) {
    if (i === slotIndex) continue; // Skip the slot we're equipping to

    const existingUpgradeKey = currentUpgrades[i];
    if (!existingUpgradeKey) continue;

    const existingUpgrade = allItems[existingUpgradeKey];
    if (!existingUpgrade) continue;

    const hasExistingCompactor = existingUpgrade.upgradeEffect?.compactToBlocks;
    const hasExistingSuperCompactor =
      existingUpgrade.upgradeEffect?.superCompactor;

    // Check for conflicts
    if (
      (isNewCompactor || isNewSuperCompactor) &&
      (hasExistingCompactor || hasExistingSuperCompactor)
    ) {
      const existingName = existingUpgrade.name;
      const newName = newUpgrade.name;

      return {
        hasConflict: true,
        conflictType: "compactor",
        message: `Cannot equip **${newName}** because **${existingName}** is already equipped. Only one compactor can be equipped per minion.`,
        existingUpgrade: existingUpgradeKey,
        existingSlot: i,
      };
    }
  }

  return { hasConflict: false };
}

module.exports = {
  processCompacting,
  getCompactingPreview,
  checkCompactorConflict,
};
