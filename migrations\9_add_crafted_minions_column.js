// Migration to add crafted_minions_json column to track unique minion crafts

const MIGRATION_VERSION = 9;

async function up(db) {
  console.log(
    `[Migration ${MIGRATION_VERSION}] Applying migration: Add crafted_minions_json column`,
  );

  await new Promise((resolve, reject) => {
    // Add the column with default '[]' (empty JSON array)
    db.run(
      "ALTER TABLE players ADD COLUMN crafted_minions_json TEXT DEFAULT '[]'",
      function (err) {
        if (err) {
          if (err.message.includes("duplicate column name")) {
            console.log(
              `[Migration ${MIGRATION_VERSION}] Column crafted_minions_json already exists, skipping.`,
            );
            resolve();
          } else {
            console.error(
              `[Migration ${MIGRATION_VERSION}] Error adding crafted_minions_json column:`,
              err,
            );
            reject(err);
          }
        } else {
          console.log(
            `[Migration ${MIGRATION_VERSION}] Successfully added crafted_minions_json column.`,
          );
          resolve();
        }
      },
    );
  });
}

module.exports = { up /*, down */ };
