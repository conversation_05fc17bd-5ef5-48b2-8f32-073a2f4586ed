/**
 * Instant Stop Utilities
 * Provides immediate action cancellation using AbortController pattern
 */

// Import robust timer utilities
const { safeSetTimeout, safeClearTimeout } = require("./robustTimer");

// Map to store AbortControllers for active actions
const activeAbortControllers = new Map();

/**
 * Creates an AbortController for an action and stores it
 * @param {number} actionId - The action ID
 * @returns {AbortController} The abort controller for this action
 */
function createAbortController(actionId) {
  const controller = new AbortController();
  activeAbortControllers.set(actionId, controller);
  return controller;
}

/**
 * Aborts an action immediately
 * @param {number} actionId - The action ID to abort
 * @returns {boolean} True if action was aborted, false if not found
 */
function abortAction(actionId) {
  const controller = activeAbortControllers.get(actionId);
  if (controller && !controller.signal.aborted) {
    controller.abort();
    activeAbortControllers.delete(actionId);
    console.log(`[InstantStop] Action ${actionId} aborted immediately`);
    return true;
  }
  return false;
}

/**
 * Gets the AbortSignal for an action
 * @param {number} actionId - The action ID
 * @returns {AbortSignal|null} The abort signal or null if not found
 */
function getAbortSignal(actionId) {
  const controller = activeAbortControllers.get(actionId);
  return controller ? controller.signal : null;
}

/**
 * Cleans up AbortController for completed action
 * @param {number} actionId - The action ID
 */
function cleanupAbortController(actionId) {
  activeAbortControllers.delete(actionId);
}

/**
 * Creates a cancellable delay using robust timer system
 * @param {number} ms - Milliseconds to wait
 * @param {AbortSignal} signal - Abort signal to check
 * @returns {Promise} Promise that resolves after delay or rejects if cancelled
 */
function cancellableDelay(ms, signal) {
  return new Promise((resolve, reject) => {
    if (signal && signal.aborted) {
      reject(new DOMException("Action was cancelled", "AbortError"));
      return;
    }

    // Ensure minimum delay of 1ms to prevent libuv timing issues
    // Never use setTimeout(0) as it can cause libuv assertion failures
    const safeDelay = Math.max(1, Math.floor(ms));

    // For very short delays, use setImmediate to avoid libuv race conditions
    if (safeDelay <= 1) {
      setImmediate(() => {
        if (signal && signal.aborted) {
          reject(new DOMException("Action was cancelled", "AbortError"));
        } else {
          resolve();
        }
      });
      return;
    }

    let timerId;
    let abortHandler;
    let isResolved = false;

    const cleanup = () => {
      if (timerId) {
        safeClearTimeout(timerId);
        timerId = null;
      }
      if (abortHandler && signal) {
        try {
          signal.removeEventListener("abort", abortHandler);
        } catch {
          // Ignore cleanup errors
        }
        abortHandler = null;
      }
    };

    const safeResolve = () => {
      if (!isResolved) {
        isResolved = true;
        cleanup();
        resolve();
      }
    };

    const safeReject = (error) => {
      if (!isResolved) {
        isResolved = true;
        cleanup();
        reject(error);
      }
    };

    try {
      // Use robust timer system instead of plain setTimeout
      timerId = safeSetTimeout(safeResolve, safeDelay);

      // If robust timer failed, fall back to setImmediate
      if (!timerId) {
        console.warn(
          "[cancellableDelay] Robust timer failed, using setImmediate fallback"
        );
        setImmediate(() => {
          if (signal && signal.aborted) {
            safeReject(new DOMException("Action was cancelled", "AbortError"));
          } else {
            safeResolve();
          }
        });
        return;
      }

      if (signal) {
        abortHandler = () => {
          safeReject(new DOMException("Action was cancelled", "AbortError"));
        };
        signal.addEventListener("abort", abortHandler);
      }
    } catch (error) {
      // If everything fails, use setImmediate as final fallback
      console.warn(
        "[cancellableDelay] All timer methods failed, using setImmediate fallback:",
        error.message
      );
      setImmediate(() => {
        if (signal && signal.aborted) {
          safeReject(new DOMException("Action was cancelled", "AbortError"));
        } else {
          safeResolve();
        }
      });
    }
  });
}

/**
 * Enhanced cancellable delay with time jump detection
 * @param {number} ms - Milliseconds to wait
 * @param {AbortSignal} signal - Abort signal to check
 * @param {Object} options - Additional options
 * @param {boolean} options.detectTimeJumps - Whether to detect time jumps
 * @returns {Promise} Promise that resolves after delay or rejects if cancelled
 */
function enhancedCancellableDelay(ms, signal, options = {}) {
  return new Promise((resolve, reject) => {
    if (signal && signal.aborted) {
      reject(new DOMException("Action was cancelled", "AbortError"));
      return;
    }

    const safeDelay = Math.max(1, Math.floor(ms));
    const startTime = Date.now();
    const startMonotonicTime = process.hrtime.bigint();

    let timerId;
    let abortHandler;
    let isResolved = false;

    const cleanup = () => {
      if (timerId) {
        safeClearTimeout(timerId);
        timerId = null;
      }
      if (abortHandler && signal) {
        try {
          signal.removeEventListener("abort", abortHandler);
        } catch {
          // Ignore cleanup errors
        }
        abortHandler = null;
      }
    };

    const safeResolve = () => {
      if (!isResolved) {
        isResolved = true;
        cleanup();

        // Check for time jumps if enabled
        if (options.detectTimeJumps) {
          const endTime = Date.now();
          const endMonotonicTime = process.hrtime.bigint();
          const realElapsed = endTime - startTime;
          const monotonicElapsed =
            Number(endMonotonicTime - startMonotonicTime) / 1000000;

          const timeDifference = Math.abs(realElapsed - monotonicElapsed);
          if (timeDifference > 1000) {
            // 1 second threshold
            console.warn(
              `[enhancedCancellableDelay] Time jump detected during delay: real=${realElapsed}ms, monotonic=${monotonicElapsed}ms, diff=${timeDifference}ms`
            );
          }
        }

        resolve();
      }
    };

    const safeReject = (error) => {
      if (!isResolved) {
        isResolved = true;
        cleanup();
        reject(error);
      }
    };

    // Use robust timer system
    timerId = safeSetTimeout(safeResolve, safeDelay);

    if (!timerId) {
      // Fallback to setImmediate if robust timer fails
      setImmediate(safeResolve);
      return;
    }

    if (signal) {
      abortHandler = () => {
        safeReject(new DOMException("Action was cancelled", "AbortError"));
      };
      signal.addEventListener("abort", abortHandler);
    }
  });
}

/**
 * Throws if the signal is aborted
 * @param {AbortSignal} signal - Abort signal to check
 */
function throwIfAborted(signal) {
  if (signal && signal.aborted) {
    throw new DOMException("Action was cancelled", "AbortError");
  }
}

/**
 * Custom error class for when actions are cancelled
 */
class ActionCancelledError extends Error {
  constructor(message = "Action was cancelled by user") {
    super(message);
    this.name = "ActionCancelledError";
  }
}

module.exports = {
  createAbortController,
  abortAction,
  getAbortSignal,
  cleanupAbortController,
  cancellableDelay,
  enhancedCancellableDelay,
  throwIfAborted,
  ActionCancelledError,
};
