// Migration 028: Remove 'currentLevelExp' and 'requiredExpForNextLevel' from all player skill records
// Purpose: Remove obsolete fields from all player skill records in the database.

async function up(db, playerDataUtils) {
  const { getAllPlayerIds, getPlayerData, savePlayerData } = playerDataUtils;

  console.log(
    "Running migration 028: Remove 'currentLevelExp' and 'requiredExpForNextLevel' from all player skill records...",
  );
  let processedCount = 0;
  let migratedCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log("No player IDs found. Migration step 028 complete.");
    return;
  }
  console.log(`Found ${playerIds.length} players to check for migration 028.`);

  for (const userId of playerIds) {
    processedCount++;
    try {
      const characterData = await getPlayerData(userId);
      if (!characterData || !characterData.skills) continue;
      let changed = false;
      for (const skillKey of Object.keys(characterData.skills)) {
        const skill = characterData.skills_json[skillKey];
        if ("currentLevelExp" in skill) {
          delete skill.currentLevelExp;
          changed = true;
        }
        if ("requiredExpForNextLevel" in skill) {
          delete skill.requiredExpForNextLevel;
          changed = true;
        }
      }
      if (changed) {
        await savePlayerData(userId, characterData);
        migratedCount++;
      }
    } catch (error) {
      console.error(
        `  - Error processing player ${userId} for migration 028:`,
        error,
      );
      errorCount++;
    }
  }

  console.log(
    `Migration 028 complete. Processed: ${processedCount}, Migrated: ${migratedCount}, Errors: ${errorCount}`,
  );
}

module.exports = { up };
