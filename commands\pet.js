const {
  <PERSON><PERSON><PERSON><PERSON>mandBuilder,
  <PERSON>bed<PERSON><PERSON>er,
  StringSelectMenuBuilder,
  ActionRowBuilder,
  MessageFlags,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
} = require("discord.js");
const {
  getPlayerData,
  savePlayerData,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager");
const configManager = require("../utils/configManager.js");
const { checkRankPermission } = require("../utils/permissionUtils.js");
const {
  getExpRequiredForLevel,
  MAX_PET_LEVEL,
} = require("../data/petLeveling");
const {
  getPetLevel,
  getPetExpInLevel,
  getPetTotalExp,
} = require("../utils/petUtils");
const { ITEM_RARITY, STATS, EMBED_COLORS } = require("../gameConfig.js");
const { STAT_ABBREVIATIONS } = require("../utils/statAbbreviations.js");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager.js");
const { calculatePetScore } = require("../utils/disblockXpSystem.js");
const { createProgressBar } = require("../utils/displayUtils");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("pet")
    .setDescription("Manage your pets")
    .addSubcommand((subcommand) =>
      subcommand.setName("list").setDescription("List your owned pets")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("equip")
        .setDescription("Equip a pet")
        .addStringOption((option) =>
          option
            .setName("pet")
            .setDescription("The pet you want to equip")
            .setRequired(true)
            .setAutocomplete(true)
        )
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("unequip")
        .setDescription("Unequip your currently active pet")
    )
    .addSubcommand((subcommand) =>
      subcommand
        .setName("release")
        .setDescription("Release (delete) a pet you own")
    ),

  async execute(interaction) {
    try {
      await interaction.deferReply();

      const subcommand = interaction.options.getSubcommand();
      let neededFields;
      if (subcommand === "list") {
        neededFields = ["pets", "active_pet_id", "rank", "name"];
      } else if (subcommand === "equip" || subcommand === "unequip") {
        neededFields = ["pets", "active_pet_id", "rank"];
      } else if (subcommand === "release") {
        neededFields = ["pets", "active_pet_id", "rank"];
      } else {
        neededFields = null; // fallback to all fields if unknown
      }
      const character = await getPlayerData(interaction.user.id, neededFields);
      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.editReply({
          content: "You don't have permission (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }
      if (!character) {
        return interaction.editReply({
          content: "You need a character to manage pets!",
          flags: [MessageFlags.Ephemeral],
        });
      }

      character.pets = character.pets || [];

      const allItems = configManager.getAllItems();

      if (subcommand === "list") {
        if (character.pets.length === 0) {
          return interaction.editReply({
            content: "You don't own any pets yet.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const rarityOrder = {
          MYTHIC: 6,
          LEGENDARY: 5,
          EPIC: 4,
          RARE: 3,
          UNCOMMON: 2,
          COMMON: 1,
        };

        const sortedPets = [...character.pets].sort((a, b) => {
          const rarityA = rarityOrder[a.rarity] || 0;
          const rarityB = rarityOrder[b.rarity] || 0;

          if (rarityA !== rarityB) {
            return rarityB - rarityA;
          }

          const levelA = getPetLevel(a);
          const levelB = getPetLevel(b);
          return levelB - levelA;
        });

        const equippedPetId = character.active_pet_id;
        const petsPerPage = 7;
        let currentPage = 0;
        const totalPages = Math.ceil(sortedPets.length / petsPerPage);

        function createPetListEmbed(page) {
          const embed = new EmbedBuilder()
            .setColor(EMBED_COLORS.DARK_BROWN)
            .setTitle(`${character.name}'s Pets`);

          const startIndex = page * petsPerPage;
          const endIndex = Math.min(
            startIndex + petsPerPage,
            sortedPets.length
          );
          const petsOnPage = sortedPets.slice(startIndex, endIndex);

          if (petsOnPage.length === 0) {
            embed.setDescription("No pets on this page.");
          } else {
            petsOnPage.forEach((pet) => {
              const petInfo = allItems[pet.petKey];
              const rarityInfo = ITEM_RARITY[pet.rarity];
              if (!petInfo || !rarityInfo) return;

              const currentLevel = getPetLevel(pet);
              const currentExpInLevel = getPetExpInLevel(pet);
              const requiredForNext = getExpRequiredForLevel(
                pet.rarity,
                currentLevel
              );

              // Handle max level pets - show full progress bar with just total XP
              let progressBar;
              if (currentLevel >= MAX_PET_LEVEL) {
                const totalExp = getPetTotalExp(pet);
                // For max level pets, show full bar without XP text, then add custom total XP display
                progressBar =
                  createProgressBar(totalExp, totalExp, {
                    size: 10,
                    showXpText: false, // Don't show redundant XP text
                    localeString: true,
                  }) + `\n\`${totalExp.toLocaleString()} Total XP\``;
              } else {
                progressBar = createProgressBar(
                  currentExpInLevel,
                  requiredForNext,
                  {
                    size: 10,
                    showXpText: true,
                    localeString: true,
                  }
                );
              }
              let statsString = "";
              const rarityPerks = petInfo.rarities?.[pet.rarity];
              if (
                rarityPerks &&
                typeof rarityPerks.calculateBonus === "function"
              ) {
                const calculatedBonuses = rarityPerks.calculateBonus(
                  getPetLevel(pet)
                );
                const statsParts = Object.entries(calculatedBonuses)
                  .map(([statKey, value]) => {
                    const statConfig = STATS[statKey];
                    if (!statConfig) return null;
                    const formattedValue =
                      typeof value === "number"
                        ? value.toLocaleString(undefined, {
                            maximumFractionDigits: 2,
                          })
                        : value;
                    const statName =
                      STAT_ABBREVIATIONS[statConfig.name] ||
                      statConfig.name ||
                      statKey.toUpperCase();
                    return `${statConfig.emoji || ""} \`${statName} +${formattedValue}\``;
                  })
                  .filter((part) => part !== null);
                if (statsParts.length > 0)
                  statsString = `\n${statsParts.join(" ")}`;
              }

              const isEquipped = pet.id === equippedPetId;
              const equipIcon = isEquipped ? "✅ " : "";

              embed.addFields({
                name: `${equipIcon}${petInfo.emoji || "❓"} [Lvl ${getPetLevel(pet)}] ${rarityInfo.name} ${petInfo.name} \`${pet.id.slice(0, 4)}\``,
                value: `${progressBar}${statsString}`,
                inline: false,
              });
            });
          }

          const currentPetScore = calculatePetScore(character);
          if (totalPages > 1) {
            embed.setFooter({
              text: `Page ${page + 1} of ${totalPages} • ${sortedPets.length} total pets • Current Pet Score: ${currentPetScore}`,
            });
          } else {
            embed.setFooter({
              text: `${sortedPets.length} total pets • Current Pet Score: ${currentPetScore}`,
            });
          }

          return embed;
        }

        function createNavigationButtons(page) {
          const row = new ActionRowBuilder();

          const prevButton = new ButtonBuilder()
            .setCustomId(`pet_list_prev:${interaction.user.id}`)
            .setLabel("◀ Previous")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(page === 0);

          const nextButton = new ButtonBuilder()
            .setCustomId(`pet_list_next:${interaction.user.id}`)
            .setLabel("Next ▶")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(page === totalPages - 1);

          if (totalPages > 1) {
            row.addComponents(prevButton, nextButton);
            return [row];
          }

          return [];
        }

        const initialEmbed = createPetListEmbed(currentPage);
        const components = createNavigationButtons(currentPage);

        const message = await interaction.editReply({
          embeds: [initialEmbed],
          components,
        });

        if (totalPages > 1) {
          const filter = (i) => {
            return (
              i.customId.startsWith("pet_list_") &&
              i.user.id === interaction.user.id
            );
          };

          const collector = message.createMessageComponentCollector({
            filter,
            componentType: ComponentType.Button,
            time: 300000,
          });

          collector.on("collect", async (i) => {
            try {
              if (i.customId.includes("prev")) {
                currentPage = Math.max(0, currentPage - 1);
              } else if (i.customId.includes("next")) {
                currentPage = Math.min(totalPages - 1, currentPage + 1);
              }

              const updatedEmbed = createPetListEmbed(currentPage);
              const updatedComponents = createNavigationButtons(currentPage);

              await i.update({
                embeds: [updatedEmbed],
                components: updatedComponents,
              });
            } catch (error) {
              console.error("Error handling pet list pagination:", error);
              if (!i.replied && !i.deferred) {
                await i.reply({
                  content: "An error occurred while navigating pages.",
                  ephemeral: true,
                });
              }
            }
          });

          collector.on("end", async () => {
            try {
              const disabledComponents = createNavigationButtons(
                currentPage
              ).map((row) => {
                const newRow = new ActionRowBuilder();
                row.components.forEach((component) => {
                  newRow.addComponents(
                    ButtonBuilder.from(component).setDisabled(true)
                  );
                });
                return newRow;
              });

              await message.edit({ components: disabledComponents });
            } catch (error) {
              console.error("Error disabling pet list buttons:", error);
            }
          });
        }

        return;
      } else if (subcommand === "equip") {
        const currentActivity = await getCurrentActivity(interaction.user.id);
        if (currentActivity) {
          return warnUserBusy(interaction, currentActivity, "pet");
        }

        const fullCharacter = await getPlayerData(interaction.user.id);
        if (!fullCharacter) {
          return interaction.editReply({
            content:
              "Could not load your character data. Please try again later.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        const petIdToEquip = interaction.options.getString("pet");
        const petToEquip = fullCharacter.pets.find(
          (p) => p.id === petIdToEquip
        );

        if (!petToEquip) {
          return interaction.editReply({
            content: "Could not find the selected pet.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        if (petToEquip.id === fullCharacter.active_pet_id) {
          return interaction.editReply({
            content: "This pet is already equipped.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const oldActivePetId = fullCharacter.active_pet_id;
        const previouslyEquippedPet = oldActivePetId
          ? fullCharacter.pets.find((p) => p.id === oldActivePetId)
          : null;

        fullCharacter.active_pet_id = petIdToEquip;
        try {
          await recalculateAndSaveStats(interaction.user.id, fullCharacter);
        } catch (recalcError) {
          console.error(
            `[Pet Equip] Failed to recalculate/save player data for ${interaction.user.id}:`,
            recalcError
          );

          fullCharacter.active_pet_id = oldActivePetId;

          return interaction.editReply({
            content:
              "[PET-EQP-REC] Failed to update stats after equipping the pet. Please report this error code.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const petInfoEquip = allItems[petToEquip.petKey];
        const rarityInfoEquip = ITEM_RARITY[petToEquip.rarity];

        // create description based on whether we're replacing a pet or just equipping
        let description;
        if (previouslyEquippedPet) {
          const previousPetInfo = allItems[previouslyEquippedPet.petKey];
          const previousRarityInfo = ITEM_RARITY[previouslyEquippedPet.rarity];
          description = `You equipped **${petInfoEquip?.emoji || "❓"} [Lvl ${getPetLevel(petToEquip)}] ${rarityInfoEquip?.name || "Unknown Rarity"} ${petInfoEquip?.name || "Pet"}** and unequipped **${previousPetInfo?.emoji || "❓"} [Lvl ${getPetLevel(previouslyEquippedPet)}] ${previousRarityInfo?.name || "Unknown Rarity"} ${previousPetInfo?.name || "Pet"}**.`;
        } else {
          description = `You equipped **${petInfoEquip?.emoji || "❓"} [Lvl ${getPetLevel(petToEquip)}] ${rarityInfoEquip?.name || "Unknown Rarity"} ${petInfoEquip?.name || "Pet"}**.`;
        }

        const equipEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setDescription(description);

        await interaction.editReply({ embeds: [equipEmbed] });
      } else if (subcommand === "unequip") {
        const currentActivity = await getCurrentActivity(interaction.user.id);
        if (currentActivity) {
          return warnUserBusy(interaction, currentActivity, "pet");
        }

        const fullCharacter = await getPlayerData(interaction.user.id);
        if (!fullCharacter) {
          return interaction.editReply({
            content:
              "Could not load your character data. Please try again later.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        if (!fullCharacter.active_pet_id) {
          return interaction.editReply({
            content: "You don't have a pet equipped.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const previouslyActivePetId = fullCharacter.active_pet_id;
        const unequippedPet = fullCharacter.pets.find(
          (p) => p.id === previouslyActivePetId
        );
        fullCharacter.active_pet_id = null;
        try {
          await recalculateAndSaveStats(interaction.user.id, fullCharacter);
        } catch (recalcError) {
          console.error(
            `[Pet Unequip] Failed to recalculate/save player data for ${interaction.user.id}:`,
            recalcError
          );

          fullCharacter.active_pet_id = previouslyActivePetId;

          return interaction.editReply({
            content:
              "[PET-UNEQP-REC] Failed to update stats after unequipping the pet. Please report this error code.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        const petInfoUnequip = unequippedPet
          ? allItems[unequippedPet.petKey]
          : null;
        const rarityInfoUnequip = unequippedPet
          ? ITEM_RARITY[unequippedPet.rarity]
          : null;
        const unequipEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setDescription(
            unequippedPet && petInfoUnequip
              ? `You unequipped **${petInfoUnequip.emoji || "❓"} [Lvl ${getPetLevel(unequippedPet)}] ${rarityInfoUnequip?.name || "Unknown Rarity"} ${petInfoUnequip.name}**.`
              : "You unequipped your pet."
          );

        await interaction.editReply({ embeds: [unequipEmbed] });
      } else if (subcommand === "release") {
        if (character.pets.length === 0) {
          return interaction.editReply({
            content: "You don't own any pets to release.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        const petOptions = character.pets.map((pet) => {
          const petInfo = allItems[pet.petKey];
          const emoji = petInfo?.emoji || "❓";
          const rarityInfo = ITEM_RARITY[pet.rarity];
          const idShort = pet.id.slice(0, 4);
          return {
            label: `(${idShort}) [Lvl ${getPetLevel(pet)}] ${rarityInfo?.name || pet.rarity} ${petInfo?.name || pet.petKey}`,
            value: pet.id,
            emoji:
              typeof emoji === "string" && emoji.length > 0 ? emoji : undefined,
          };
        });
        const selectMenu = new StringSelectMenuBuilder()
          .setCustomId(`pet_release_select:${interaction.user.id}`)
          .setPlaceholder("Select a pet to release...")
          .addOptions(petOptions);
        const row = new ActionRowBuilder().addComponents(selectMenu);
        const embed = new EmbedBuilder()
          .setColor(EMBED_COLORS.DARK_RED)
          .setTitle("Release a Pet")
          .setDescription(
            "Select the pet you wish to **release** (delete) from your collection. This action is **permanent**!"
          );
        return interaction.editReply({ embeds: [embed], components: [row] });
      }
    } catch (error) {
      console.error("[Pet Command Error]", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "[PET-EXE-ERR] An unexpected error occurred while managing pets. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(console.error);
      } else {
        await interaction
          .followUp({
            content:
              "[PET-EXE-ERR] An unexpected error occurred while managing pets. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(console.error);
      }
    }
  },

  async autocomplete(interaction) {
    if (interaction.commandName !== "pet") return;
    const subcommand = interaction.options.getSubcommand(false); // Don't throw if not found
    const focusedOption = interaction.options.getFocused(true); // Get { name, value }

    if (subcommand === "equip" && focusedOption.name === "pet") {
      let character;
      try {
        character = await getPlayerData(interaction.user.id);
      } catch (error) {
        console.error(
          `[Pet Autocomplete] Error fetching player data for ${interaction.user.id}:`,
          error
        );
        await interaction.respond([]).catch(() => {});
        return;
      }

      if (!character || !character.pets || character.pets.length === 0) {
        await interaction.respond([]).catch(() => {});
        return;
      }

      const allItems = configManager.getAllItems();
      const focusedValue = focusedOption.value.toLowerCase();
      const activePetId = character.active_pet_id;

      const choices = character.pets
        .map((pet) => {
          const petInfo = allItems[pet.petKey];
          const rarityInfo = ITEM_RARITY[pet.rarity];
          if (!petInfo || !rarityInfo) return null;
          const idShort = pet.id.slice(0, 4);
          const name = `(${idShort}) [Lvl ${getPetLevel(pet)}] ${rarityInfo.name} ${petInfo.name}`;
          return { name, value: pet.id, petInfo };
        })
        .filter((choice) => choice && choice.value !== activePetId)
        .filter((choice) =>
          choice.petInfo.name.toLowerCase().includes(focusedValue)
        )
        .slice(0, 25)
        .map((choice) => ({ name: choice.name, value: choice.value }));

      await interaction.respond(choices).catch((err) => {
        console.error(
          "[Pet Autocomplete] Error responding to interaction:",
          err
        );
      });
    }
  },

  handlePetReleaseComponent: async function (interaction) {
    if (
      interaction.isStringSelectMenu() &&
      interaction.customId.startsWith("pet_release_select:")
    ) {
      const userId = interaction.customId.split(":")[1];
      if (interaction.user.id !== userId) {
        return interaction.reply({
          content: "This menu is not for you.",
          flags: [MessageFlags.Ephemeral],
        });
      }
      const selectedPetId = interaction.values[0];
      const character = await getPlayerData(userId);
      const allItems = configManager.getAllItems();
      const pet = character.pets.find((p) => p.id === selectedPetId);
      if (!pet) {
        return interaction.update({
          content: "Could not find the selected pet.",
          embeds: [],
          components: [],
        });
      }
      const petInfo = allItems[pet.petKey];
      const rarity = pet.rarity.charAt(0) + pet.rarity.slice(1).toLowerCase();
      const confirmRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId(`pet_release_confirm:${userId}:${pet.id}`)
          .setLabel("Release")
          .setStyle(ButtonStyle.Danger),
        new ButtonBuilder()
          .setCustomId(`pet_release_cancel:${userId}`)
          .setLabel("Cancel")
          .setStyle(ButtonStyle.Secondary)
      );
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.DARK_RED)
        .setTitle("Confirm Pet Release")
        .setDescription(
          "Are you sure you want to **release** this pet? This cannot be undone!"
        )
        .addFields({
          name: `${petInfo?.emoji || "❓"} [Lvl ${getPetLevel(pet)}] ${rarity} ${petInfo?.name || pet.petKey}`,
          value: `ID: \`${pet.id}\``,
        });
      await interaction.update({ embeds: [embed], components: [confirmRow] });
    } else if (
      interaction.isButton() &&
      interaction.customId.startsWith("pet_release_confirm:")
    ) {
      const [, userId, petId] = interaction.customId.split(":");
      if (interaction.user.id !== userId) {
        return interaction.reply({
          content: "This button is not for you.",
          flags: [MessageFlags.Ephemeral],
        });
      }
      const character = await getPlayerData(userId);
      const petIndex = character.pets.findIndex((p) => p.id === petId);
      if (petIndex === -1) {
        return interaction.update({
          content: "Could not find the selected pet.",
          embeds: [],
          components: [],
        });
      }
      const wasEquipped = character.active_pet_id === petId;
      if (wasEquipped) {
        console.log(
          `[Pet Release] User ${userId} is releasing their active pet (${petId}). Clearing active_pet_id.`
        );
        character.active_pet_id = null;
      }
      character.pets.splice(petIndex, 1);

      await savePlayerData(userId, character, ["pets", "active_pet_id"]);

      if (wasEquipped) {
        await recalculateAndSaveStats(userId, character);
      }
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GREEN)
        .setTitle("Pet Released")
        .setDescription(
          "Your pet has been released and is no longer in your collection."
        );
      await interaction.update({ embeds: [embed], components: [] });
    } else if (
      interaction.isButton() &&
      interaction.customId.startsWith("pet_release_cancel:")
    ) {
      const [, userId] = interaction.customId.split(":");
      if (interaction.user.id !== userId) {
        return interaction.reply({
          content: "This button is not for you.",
          flags: [MessageFlags.Ephemeral],
        });
      }
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GREY)
        .setTitle("Pet Release Cancelled")
        .setDescription("No pets were released.");
      await interaction.update({ embeds: [embed], components: [] });
    }
  },
};
