// No imports needed - using db parameter directly

async function up(db) {
  console.log(
    "[Migration 047] Adding preset_name column to wardrobe_presets table...",
  );

  try {
    // First, check if the column exists
    const tableInfo = await db.all("PRAGMA table_info(wardrobe_presets)");
    const hasPresetName =
      Array.isArray(tableInfo) &&
      tableInfo.some((column) => column.name === "preset_name");

    if (hasPresetName) {
      console.log(
        "[Migration 047] preset_name column already exists, skipping...",
      );
      return;
    }

    console.log("[Migration 047] Adding preset_name column...");
    await db.run(`
      ALTER TABLE wardrobe_presets 
      ADD COLUMN preset_name TEXT
    `);

    console.log("[Migration 047] Successfully added preset_name column");
  } catch (error) {
    console.error("[Migration 047] Error adding preset_name column:", error);
    throw error;
  }
}

async function down(db) {
  console.log(
    "[Migration 047] Removing preset_name column from wardrobe_presets table...",
  );

  try {
    // SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
    // First, check if the column exists
    const tableInfo = await db.all("PRAGMA table_info(wardrobe_presets)");
    console.log("[Migration 047] Table info:", tableInfo);
    const hasPresetName =
      Array.isArray(tableInfo) &&
      tableInfo.some((column) => column.name === "preset_name");

    if (!hasPresetName) {
      console.log(
        "[Migration 047] preset_name column does not exist, skipping...",
      );
      return;
    }

    console.log(
      "[Migration 047] Recreating table without preset_name column...",
    );
    // This is a simplified approach - in production you might want to preserve data
    await db.run("DROP TABLE IF EXISTS wardrobe_presets_backup");
    await db.run(
      "ALTER TABLE wardrobe_presets RENAME TO wardrobe_presets_backup",
    );

    // Recreate table without preset_name column (same as migration 046)
    await db.run(`
      CREATE TABLE wardrobe_presets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        slot_number INTEGER NOT NULL,
        helmet_item_id TEXT,
        chestplate_item_id TEXT,
        leggings_item_id TEXT,
        boots_item_id TEXT,
        weapon_item_id TEXT,
        axe_item_id TEXT,
        pickaxe_item_id TEXT,
        shovel_item_id TEXT,
        hoe_item_id TEXT,
        fishing_rod_item_id TEXT,
        necklace_item_id TEXT,
        cloak_item_id TEXT,
        belt_item_id TEXT,
        gloves_item_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, slot_number)
      )
    `);

    // Copy data back (excluding preset_name)
    await db.run(`
      INSERT INTO wardrobe_presets (
        id, user_id, slot_number, helmet_item_id, chestplate_item_id, leggings_item_id,
        boots_item_id, weapon_item_id, axe_item_id, pickaxe_item_id, shovel_item_id,
        hoe_item_id, fishing_rod_item_id, necklace_item_id, cloak_item_id, belt_item_id,
        gloves_item_id, created_at, updated_at
      )
      SELECT 
        id, user_id, slot_number, helmet_item_id, chestplate_item_id, leggings_item_id,
        boots_item_id, weapon_item_id, axe_item_id, pickaxe_item_id, shovel_item_id,
        hoe_item_id, fishing_rod_item_id, necklace_item_id, cloak_item_id, belt_item_id,
        gloves_item_id, created_at, updated_at
      FROM wardrobe_presets_backup
    `);

    // Clean up backup table
    await db.run("DROP TABLE wardrobe_presets_backup");

    console.log("[Migration 047] Successfully removed preset_name column");
  } catch (error) {
    console.error("[Migration 047] Error removing preset_name column:", error);
    throw error;
  }
}

module.exports = { up, down };
