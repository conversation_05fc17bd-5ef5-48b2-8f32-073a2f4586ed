// utility functions for text formatting and manipulation

/**
 * Wraps text to approximately 50 characters per line without cutting words
 * @param {string} text - The text to wrap
 * @param {number} maxLength - Maximum characters per line (default: 50)
 * @returns {string} - Text with line breaks inserted
 */
function wrapText(text, maxLength = 50) {
  if (!text || typeof text !== "string") return text;

  const words = text.split(" ");
  const lines = [];
  let currentLine = "";

  for (const word of words) {
    // if adding this word would exceed the limit, start a new line
    if (
      currentLine.length + word.length + 1 > maxLength &&
      currentLine.length > 0
    ) {
      lines.push(currentLine);
      currentLine = word;
    } else {
      // add word to current line
      currentLine = currentLine.length > 0 ? `${currentLine} ${word}` : word;
    }
  }

  // don't forget the last line
  if (currentLine.length > 0) {
    lines.push(currentLine);
  }

  return lines.join("\n");
}

module.exports = {
  wrapText,
};
