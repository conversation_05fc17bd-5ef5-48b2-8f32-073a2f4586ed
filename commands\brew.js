const { SlashCommandBuilder } = require("discord.js");
const configManager = require("../utils/configManager");
const { startOrResumeAction } = require("../utils/actionHandlers");
const {
  getAbortSignal,
  cancellableDelay,
} = require("../utils/instantStopUtils");

const { alchemySkillConfig } = require("../data/skillConfigs");

// conditional imports for worker bot compatibility
const isWorkerBot = process.env.IS_WORKER_BOT === "true";
const activityModule = isWorkerBot
  ? require("../utils/workerDatabaseProxy")
  : require("../utils/activityManager");

function calculateBrewingTime(potionKey) {
  const allItems = configManager.getAllItems();
  const potionData = allItems[potionKey];
  return potionData?.brewingTime || 20000;
}

async function brewingAnimation(
  message,
  embed,
  brewingTime,
  stopRequestMap,
  actionId,
  auxConsumed,
  resourceEmoji
) {
  const cauldronEmoji = "🧪";
  const displayEmoji = resourceEmoji || "⚗️";
  const steps = 5;
  const trackLength = 15;

  // keep original dynamic timing based on brewing time as akin designed
  const stepDuration = brewingTime / steps;
  for (let i = 0; i <= steps; i++) {
    const position = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(position)}${cauldronEmoji}${" ".repeat(trackLength - position)}`;
    embed.setDescription(`\`${movingPart}\` ${displayEmoji}`);
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      // always clean up brewing actions if stopped during animation
      const {
        clearPendingSkillAction,
        completeAction,
      } = require("../utils/skillActionUtils");
      const { clearActivity } = activityModule;
      try {
        await clearPendingSkillAction(actionId);
        await completeAction(actionId);
        await clearActivity(message?.author?.id || message?.user?.id);
      } catch (cleanupErr) {
        console.error(
          `[BrewingAnimation] Failed to clean up stopped brewing action ${actionId}:`,
          cleanupErr
        );
      }
      return;
    }
    try {
      await message.edit({ embeds: [embed] });
    } catch {
      /* empty */
    }
    if (i < steps) {
      await cancellableDelay(stepDuration, signal);
    }
  }
}

async function handleBrewAction(
  interaction,
  potionKey,
  amount,
  isAgain,
  originalTotalAmountFromResumption,
  character
) {
  const isResumption = interaction.isResumption || false;
  let actualAmount = amount;
  if (isResumption) {
    actualAmount = originalTotalAmountFromResumption;
  }
  const normalizedPotionKey =
    typeof potionKey === "string" ? potionKey.toUpperCase() : "";
  return startOrResumeAction(interaction, "alchemy", {
    character,
    resourceKey: normalizedPotionKey,
    amount: actualAmount,
    isResumption,
    actionId: interaction.actionId,
    pendingLoot: interaction.pendingLoot,
    startingCycle: interaction.startingCycle || 0,
  });
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("brew")
    .setDescription("Brew potions")
    .addStringOption((option) =>
      option
        .setName("potion")
        .setDescription("Type of potion to brew")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'Number of repetitions (e.g., 5, or "max"). Defaults to your max allowed amount.'
        )
        .setRequired(false)
    ),
  async execute(interaction) {
    // Extract and validate parameters before delegation
    const {
      extractAndValidateSkillCommandParams,
    } = require("../utils/commandUtils");
    const params = await extractAndValidateSkillCommandParams(
      interaction,
      alchemySkillConfig
    );

    if (!params) {
      // extractAndValidateSkillCommandParams already sent error response
      return;
    }

    // Delegate to worker bot
    const { workerManager } = require("../utils/workerManager");
    await workerManager.delegateAction(
      interaction,
      "brew",
      params.amount,
      params.resourceKey,
      params.wasMax
    );
  },
  handleBrewAction,
  async autocomplete(interaction) {
    const { skillResourceAutocomplete } = require("../utils/commandUtils");
    return skillResourceAutocomplete(interaction, "Alchemy", "potion");
  },
  calculateBrewingTime,
  brewingAnimation,
};
