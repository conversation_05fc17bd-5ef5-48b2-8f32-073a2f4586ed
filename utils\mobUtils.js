/**
 * Calculates the stats for a specific mob instance based on its level.
 * @param {object} baseStats - The base stats object (e.g., { health, damage }) from mob data.
 * @returns {object} An object containing the calculated stats for the instance (e.g., { health, damage }).
 */
function calculateInstanceStats(baseStats) {
  const calculatedStats = { ...baseStats };

  if (calculatedStats.health !== undefined) {
    calculatedStats.health = Math.max(1, Math.round(calculatedStats.health));
  }
  if (calculatedStats.damage !== undefined) {
    calculatedStats.damage = Math.max(0, calculatedStats.damage);
  }
  if (calculatedStats.defense !== undefined) {
    calculatedStats.defense = Math.max(0, calculatedStats.defense);
  }
  return calculatedStats;
}

/**
 * Calculates the coin drop range for a specific mob instance based on its level.
 * @param {{min: number, max: number}} baseCoins - The base coin range from mob data.
 * @returns {{min: number, max: number}} The calculated min/max coin range for the instance.
 */
function calculateInstanceCoinRange(baseCoins) {
  // No scaling needed, just return baseCoins
  return {
    min: Math.max(0, baseCoins.min),
    max: Math.max(0, baseCoins.max),
  };
}

function getInstanceCoins(mobData) {
  return typeof mobData.coins === "number" ? mobData.coins : 0;
}

module.exports = {
  calculateInstanceStats,
  calculateInstanceCoinRange,
  getInstanceCoins,
};
