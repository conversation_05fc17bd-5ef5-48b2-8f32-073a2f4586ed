// utils/petUpgradeUtils.js
const { v4: uuidv4 } = require("uuid");
const { PET_RARITIES } = require("../gameConfig");
const { getPlayerData, savePlayerData } = require("./playerDataManager");
// const { dbRun, dbRunQueued } = require("./dbUtils"); // Currently unused

// Pet upgrade costs in coins (based on Hypixel Skyblock with appropriate scaling for DisBlock economy)
const PET_UPGRADE_COSTS = {
  COMMON_TO_UNCOMMON: 20000,
  UNCOMMON_TO_RARE: 125000,
  RARE_TO_EPIC: 500000,
  EPIC_TO_LEGENDARY: 1500000,
};

// Pet upgrade durations in real-world seconds
const PET_UPGRADE_DURATIONS = {
  COMMON_TO_UNCOMMON: 3600, // 1 hour
  UNCOMMON_TO_RARE: 12600, // 3.5 hours
  RARE_TO_EPIC: 57600, // 16 hours
  EPIC_TO_LEGENDARY: 144000, // 40 hours
};

/**
 * Gets the upgrade key for a pet rarity transition
 * @param {string} currentRarity - The current rarity of the pet
 * @returns {string|null} - The upgrade key or null if no upgrade is possible
 */
function getUpgradeKey(currentRarity) {
  const currentIndex = PET_RARITIES.indexOf(currentRarity);
  if (currentIndex === -1 || currentIndex >= PET_RARITIES.length - 1) {
    return null; // Invalid rarity or already at max rarity
  }

  const nextRarity = PET_RARITIES[currentIndex + 1];
  return `${currentRarity}_TO_${nextRarity}`;
}

/**
 * Gets the cost to upgrade a pet from its current rarity to the next rarity
 * @param {string} currentRarity - The current rarity of the pet
 * @returns {number|null} - The cost in coins or null if no upgrade is possible
 */
function getUpgradeCost(currentRarity) {
  const upgradeKey = getUpgradeKey(currentRarity);
  return upgradeKey ? PET_UPGRADE_COSTS[upgradeKey] : null;
}

/**
 * Gets the duration to upgrade a pet from its current rarity to the next rarity
 * @param {string} currentRarity - The current rarity of the pet
 * @returns {number|null} - The duration in seconds or null if no upgrade is possible
 */
function getUpgradeDuration(currentRarity) {
  const upgradeKey = getUpgradeKey(currentRarity);
  return upgradeKey ? PET_UPGRADE_DURATIONS[upgradeKey] : null;
}

/**
 * Gets the next rarity for a pet
 * @param {string} currentRarity - The current rarity of the pet
 * @returns {string|null} - The next rarity or null if no upgrade is possible
 */
function getNextRarity(currentRarity) {
  const currentIndex = PET_RARITIES.indexOf(currentRarity);
  if (currentIndex === -1 || currentIndex >= PET_RARITIES.length - 1) {
    return null; // Invalid rarity or already at max rarity
  }

  return PET_RARITIES[currentIndex + 1];
}

/**
 * Starts a pet upgrade process
 * @param {string} userId - The Discord ID of the player
 * @param {string} petId - The ID of the pet to upgrade
 * @returns {Promise<{success: boolean, error: string|null, pet_upgrade_json: object|null}>} - The result of the upgrade attempt
 */
async function startPetUpgrade(userId, petId) {
  try {
    console.log(
      `[startPetUpgrade] Starting pet upgrade for ${userId}, petId: ${petId}`
    );

    // Batching system handles transactions automatically
    console.log(`[startPetUpgrade] Starting atomic operations for ${userId}`);

    try {
      // Get player data
      const character = await getPlayerData(userId);

      // Check if player already has a pet being upgraded
      if (character.petUpgrade) {
        console.log(
          `[startPetUpgrade] Player ${userId} already has a pet being upgraded:`,
          JSON.stringify(character.petUpgrade)
        );
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "already_upgrading",
          pet_upgrade_json: character.petUpgrade,
        };
      }

      // Find the pet in the player's inventory
      const petIndex = character.pets.findIndex((pet) => pet.id === petId);
      if (petIndex === -1) {
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "pet_not_found",
          pet_upgrade_json: null,
        };
      }

      const pet = character.pets[petIndex];

      // Check if the pet can be upgraded
      const nextRarity = getNextRarity(pet.rarity);
      if (!nextRarity) {
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "max_rarity",
          pet_upgrade_json: null,
        };
      }

      // Get the upgrade cost
      const upgradeCost = getUpgradeCost(pet.rarity);
      if (!upgradeCost) {
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "invalid_upgrade",
          pet_upgrade_json: null,
        };
      }

      // Check if player has enough coins
      if (character.coins < upgradeCost) {
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "insufficient_coins",
          pet_upgrade_json: null,
        };
      }

      // Get the upgrade duration
      const upgradeDuration = getUpgradeDuration(pet.rarity);

      // Create the pet upgrade object
      const now = Date.now();
      const petUpgrade = {
        id: uuidv4(),
        petId: pet.id,
        petKey: pet.petKey,
        originalRarity: pet.rarity,
        targetRarity: nextRarity,
        startTime: now,
        endTime: now + upgradeDuration * 1000,
        cost: upgradeCost,
        originalTotalExp: require("./petUtils").getPetTotalExp(pet), // Store the original pet's total XP
      };

      // Remove the pet from the player's inventory
      character.pets.splice(petIndex, 1);

      // If this pet was the active pet, clear the active_pet_id
      if (character.active_pet_id && character.active_pet_id === pet.id) {
        character.active_pet_id = null;
      }

      // Deduct the coins
      character.coins -= upgradeCost;

      // Add the pet upgrade to the player's data
      character.petUpgrade = petUpgrade;

      // Save the player data with specific fields to ensure persistence
      await savePlayerData(userId, character, [
        "pets",
        "petUpgrade",
        "active_pet_id",
        "coins",
      ]);

      // Batching system handles commits automatically
      console.log(
        `[startPetUpgrade] Operations completed successfully for ${userId}`
      );

      return {
        success: true,
        error: null,
        petUpgrade,
      };
    } catch (transactionError) {
      // Rollback transaction on any error
      console.error(
        `[startPetUpgrade] Error during transaction for ${userId}, rolling back:`,
        transactionError
      );
      // Batching system handles rollbacks automatically
      throw transactionError;
    }
  } catch (error) {
    console.error(`[startPetUpgrade] Error for ${userId}:`, error);
    return {
      success: false,
      error: "internal_error",
      pet_upgrade_json: null,
    };
  }
}

/**
 * Completes a pet upgrade process
 * @param {string} userId - The Discord ID of the player
 * @returns {Promise<{success: boolean, error: string|null, pet: object|null}>} - The result of the completion attempt
 */
async function completePetUpgrade(userId) {
  try {
    // Batching system handles transactions automatically

    try {
      // Get player data
      const character = await getPlayerData(userId);

      // Check if player has a pet being upgraded
      if (!character.petUpgrade) {
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "no_upgrade_in_progress",
          pet: null,
        };
      }

      // Check if the upgrade is complete
      const now = Date.now();
      if (now < character.petUpgrade.endTime) {
        // Batching system handles rollbacks automatically
        return {
          success: false,
          error: "upgrade_not_complete",
          pet: null,
          timeRemaining: character.petUpgrade.endTime - now,
        };
      }

      // Create the upgraded pet with the same total XP
      const { petKey, targetRarity, petId /*, originalRarity*/ } =
        character.petUpgrade;

      // Get the original pet's total XP
      const originalTotalExp =
        character.petUpgrade.originalTotalExp ||
        character.petUpgrade.originalXp ||
        0;

      const upgradedPet = {
        id: petId, // Keep the same ID
        petKey,
        rarity: targetRarity,
        totalExp: originalTotalExp, // Store total XP, level will be calculated dynamically
      };

      // Add the pet back to the player's inventory
      character.pets.push(upgradedPet);

      // Remove the pet upgrade from the player's data
      const completedUpgrade = { ...character.petUpgrade };
      character.petUpgrade = null;

      // Save the player data with specific fields to ensure persistence
      await savePlayerData(userId, character, ["pets", "petUpgrade", "coins"]);

      // Reset the pet upgrade notification flag in the player_last_active_channel table
      const { dbRunQueued } = require("./dbUtils");
      await dbRunQueued(
        "UPDATE player_last_active_channel SET pet_upgrade_notified = NULL WHERE discord_id = ?",
        [userId]
      );

      // Batching system handles commits automatically

      return {
        success: true,
        error: null,
        pet: upgradedPet,
        completedUpgrade,
      };
    } catch (transactionError) {
      // Rollback transaction on any error
      console.error(
        `[completePetUpgrade] Error during transaction for ${userId}, rolling back:`,
        transactionError
      );
      // Batching system handles rollbacks automatically
      console.log(
        `[completePetUpgrade] Transaction will be rolled back automatically for ${userId}`
      );
      throw transactionError;
    }
  } catch (error) {
    console.error(`[completePetUpgrade] Error for ${userId}:`, error);
    return {
      success: false,
      error: "internal_error",
      pet: null,
    };
  }
}

/**
 * Checks the status of a pet upgrade
 * @param {string} userId - The Discord ID of the player
 * @returns {Promise<{upgrading: boolean, pet_upgrade_json: object|null, isComplete: boolean, timeRemaining: number|null}>} - The status of the pet upgrade
 */
async function checkPetUpgradeStatus(userId) {
  try {
    // Get player data directly from the database to ensure we get the pet_upgrade_json field
    const { dbGet } = require("./dbUtils");

    // Get the raw pet_upgrade_json from the database
    const row = await dbGet(
      "SELECT pet_upgrade_json FROM players WHERE discord_id = ?",
      [userId]
    );

    if (!row) {
      // Silent handling - no player data found
      return {
        upgrading: false,
        pet_upgrade_json: null,
        isComplete: false,
        timeRemaining: null,
      };
    }

    // Parse the pet_upgrade_json (correct column name)
    const { safeJsonParse } = require("./playerDataManager");
    const petUpgrade = safeJsonParse(row.pet_upgrade_json, null);

    // Silent data parsing

    // Check if player has a pet being upgraded
    // Handle both null and empty object cases (empty object can occur due to database storage issues)
    if (
      !petUpgrade ||
      (typeof petUpgrade === "object" && Object.keys(petUpgrade).length === 0)
    ) {
      // Silent handling - no pet upgrade found
      return {
        upgrading: false,
        pet_upgrade_json: null,
        isComplete: false,
        timeRemaining: null,
      };
    }

    // Check if the upgrade is complete
    const now = Date.now();
    const isComplete = now >= petUpgrade.endTime;

    // Silent upgrade status check

    return {
      upgrading: true,
      pet_upgrade_json: petUpgrade,
      // Back-compat alias to reduce breakage in older callers
      petUpgrade,
      isComplete,
      timeRemaining: isComplete ? 0 : petUpgrade.endTime - now,
    };
  } catch {
    // Silent error handling
    return {
      upgrading: false,
      pet_upgrade_json: null,
      isComplete: false,
      timeRemaining: null,
    };
  }
}

/**
 * Formats time remaining in a human-readable format
 * @param {number} timeRemainingMs - Time remaining in milliseconds
 * @returns {string} - Formatted time string
 */
function formatTimeRemaining(timeRemainingMs) {
  const seconds = Math.floor(timeRemainingMs / 1000);

  if (seconds < 60) {
    return `${seconds} second${seconds !== 1 ? "s" : ""}`;
  }

  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? "s" : ""}`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? "s" : ""}`;
  }

  return `${hours} hour${
    hours !== 1 ? "s" : ""
  } and ${remainingMinutes} minute${remainingMinutes !== 1 ? "s" : ""}`;
}

module.exports = {
  PET_UPGRADE_COSTS,
  PET_UPGRADE_DURATIONS,
  getUpgradeKey,
  getUpgradeCost,
  getUpgradeDuration,
  getNextRarity,
  startPetUpgrade,
  completePetUpgrade,
  checkPetUpgradeStatus,
  formatTimeRemaining,
};
