// migration 068_add_worker_id_to_active_actions.js

module.exports = {
  async up(db) {
    console.log(
      "Applying migration 068: Add worker_id to active_actions table...",
    );
    await db.run(`ALTER TABLE active_actions ADD COLUMN worker_id TEXT`);
    console.log("Migration 068 applied: worker_id column added.");
  },

  async down(db) {
    // SQLite does not support DROP COLUMN directly; manual migration required if rollback needed
    console.log(
      "Reverting migration 068: Remove worker_id from active_actions table...",
    );
    // To rollback, you would need to recreate the table without worker_id and copy data
    // This is left as a manual step for safety
    console.log(
      "Migration 068 reverted (manual step required for full rollback).",
    );
  },
};
