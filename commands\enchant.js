const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  StringSelectMenuOptionBuilder,
} = require("discord.js");
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("../utils/playerDataManager");
const {
  getInventory,
  updateInventoryAtomically,
} = require("../utils/inventory");
const configManager = require("../utils/configManager");
const { skillEmojis, EMBED_COLORS } = require("../gameConfig");
const { addSkillExp } = require("../utils/skillExpManager");
const db = require("../utils/database");
const { dbRunQueued } = require("../utils/dbUtils");
const { calculateAllStats } = require("../utils/statCalculations");
const { isBoosterCookieActive } = require("../utils/boosterCookieManager");

// helper function to safely update interactions with proper error handling
async function _safeInteractionUpdate(interaction, options) {
  try {
    if (interaction.deferred || interaction.replied) {
      return await interaction.editReply(options);
    } else {
      return await interaction.update(options);
    }
  } catch (error) {
    console.error("[Enchant] Failed to update interaction:", error);

    // check if this is an expired interaction (unknown interaction error)
    if (error.code === 10062 || error.rawError?.code === 10062) {
      console.warn(
        "[Enchant] Interaction has expired (15+ minutes). Cannot update or send followup."
      );
      // for expired interactions, we can't do anything - just log and return
      return null;
    }

    // for other errors, try sending as followup only if interaction was replied/deferred
    if (interaction.deferred || interaction.replied) {
      try {
        return await interaction.followUp({
          ...options,
          ephemeral: true,
        });
      } catch (followupError) {
        console.error(
          "[Enchant] Failed to send followup message:",
          followupError
        );
        throw followupError;
      }
    } else {
      // if interaction wasn't replied/deferred and update failed, we can't send followup
      console.error(
        "[Enchant] Cannot send followup because interaction was never replied to or deferred"
      );
      throw error;
    }
  }
}

// helper function to safely reply to interactions with proper error handling
async function _safeInteractionReply(interaction, options) {
  try {
    if (interaction.replied || interaction.deferred) {
      return await interaction.followUp(options);
    } else {
      return await interaction.reply(options);
    }
  } catch (error) {
    console.error("[Enchant] Failed to reply to interaction:", error);
    // if reply fails, try followup if not already replied
    try {
      if (!interaction.replied) {
        return await interaction.followUp({
          ...options,
          ephemeral: true,
        });
      }
    } catch (followupError) {
      console.error("[Enchant] Failed to send followup reply:", followupError);
      throw followupError;
    }
  }
}

// Define enchantments
const ENCHANTMENTS = {
  PROTECTION: {
    name: "Protection",
    description: "Increases Defense by 4 per level.",
    maxLevel: 5,
    applicableTypes: ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"],
    statBonus: (level) => ({ DEFENSE: level * 4 }),
    cost: (level) => 2500 * level, // 2500, 5000, 7500, 10000, 12500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:defense:1269719821399097456>",
  },
  GROWTH: {
    name: "Growth",
    description: "Increases Health by 15 per level.",
    maxLevel: 5,
    applicableTypes: ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"],
    statBonus: (level) => ({ HEALTH: level * 15 }),
    cost: (level) => 2500 * level, // 2500, 5000, 7500, 10000, 12500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:health:1269719825429565532>",
  },
  SHARPNESS: {
    name: "Sharpness",
    description: "Increases Damage by 5% per level (25% at level 5).",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: (level) => ({ DAMAGE_MULT: level === 5 ? 30 : level * 5 }), // 5%, 10%, 15%, 20%, 30%
    cost: (level) => 3000 * level, // 3000, 6000, 9000, 12000, 15000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 3000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:damage:1270616867484209253>",
  },
  LIFE_STEAL: {
    name: "Life Steal",
    description: "Heals for 0.5% of your max health per level on hit.",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: () => ({}),
    cost: (level) => 2500 * level, // 2500, 5000, 7500, 10000, 12500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:health:1269719825429565532>",
    onHit: (level, character) => {
      const maxHealth = calculateAllStats(character).HEALTH;
      const healAmount = maxHealth * (0.005 * level); // 0.5% per level
      return Math.floor(healAmount);
    },
  },
  CRITICAL: {
    name: "Critical",
    description: "Increases Crit Damage by 10 per level.",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: (level) => ({ CRIT_DAMAGE: level * 10 }),
    cost: (level) => 2000 * level, // 2000, 4000, 6000, 8000, 10000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:critdamage:1269719819884953610>",
  },
  LOOTING: {
    name: "Looting",
    description:
      "Increases the chance of a monster dropping an item by 15% per level.",
    maxLevel: 3,
    applicableTypes: ["WEAPON"], // Typically swords in Hypixel, assuming WEAPON covers this
    // Looting doesn't add a direct stat bonus, its effect is applied during mob drops
    statBonus: () => ({}),
    cost: (level) => 1800 * level, // 1800, 3600, 5400 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 1800 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>", // Using a generic enchanted book emoji for now
    statEmoji: "❓", // No specific stat emoji
  },
  THORNS: {
    name: "Thorns",
    description:
      "Grants a 50% chance to rebound 3% per level of damage dealt back at the attacker.",
    maxLevel: 3,
    applicableTypes: ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS"], // Applicable to all armor pieces
    statBonus: () => ({}), // No direct stat bonus, effect is on damage taken
    cost: (level) => 3000 * level, // 3000, 6000, 9000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 3000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>", // Using a generic enchanted book emoji
    statEmoji: "✨", // No specific stat emoji
  },
  EXECUTE: {
    name: "Execute",
    description: "Increases damage dealt based on target's missing health.",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: () => ({}), // Effect is conditional in combat
    // Percentage of damage increase PER PERCENT of missing health
    missingHealthDamagePercent: {
      1: 0.2,
      2: 0.4,
      3: 0.6,
      4: 0.8,
      5: 1.0,
    },
    cost: (level) => 4000 * level, // 4000, 8000, 12000, 16000, 20000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 4000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "⚔️",
  },
  GIANT_KILLER: {
    name: "Giant Killer",
    description:
      "Increases damage dealt against enemies with more max health than you.",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: () => ({}), // Effect is conditional in combat
    // Damage increase percentage PER PERCENT of extra health (matches Hypixel exactly)
    damageIncreasePerExtraHealthPercent: {
      1: 0.1, // 0.1% per % extra health
      2: 0.2, // 0.2% per % extra health
      3: 0.3, // 0.3% per % extra health
      4: 0.4, // 0.4% per % extra health
      5: 0.6, // 0.6% per % extra health
    },
    // Maximum damage bonus caps per level (matches Hypixel)
    maxDamageBonus: {
      1: 5, // up to 5% max
      2: 10, // up to 10% max
      3: 15, // up to 15% max
      4: 20, // up to 20% max
      5: 30, // up to 30% max
    },
    cost: (level) => 4500 * level, // 4500, 9000, 13500, 18000, 22500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 4500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>", // Using a generic enchanted book emoji
    statEmoji: "⚔️", // Use a sword emoji
  },
  SCAVENGER: {
    name: "Scavenger",
    description: "Enemies drop extra coins based on their level.",
    maxLevel: 3,
    applicableTypes: ["WEAPON"],

    coinsPerMobLevel: {
      1: 0.3,
      2: 0.6,
      3: 0.9,
    },
    cost: (level) => 1000 * level, // 1000, 2000, 3000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 1000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:purse_coins:1367849116033482772>",
  },
  HARVESTING: {
    name: "Harvesting",
    description:
      "Grants Farming Fortune, which increases your chance for multiple crops.",
    maxLevel: 5,
    applicableTypes: ["HOE"],
    statBonus: (level) => ({ FARMING_FORTUNE: level * 12.5 }),
    cost: (level) => {
      return level * 1800; // 1800, 3600, 5400, 7200, 9000 coins
    },
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += i * 1800;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:foraging_fortune:1270616477824712714>",
  },
  EFFICIENCY: {
    name: "Efficiency",
    description:
      "Increases Fortune and grants Sweep at level 5 based on tool type.",
    maxLevel: 5,
    applicableTypes: ["PICKAXE", "SHOVEL", "AXE", "HOE"],
    statBonus: (level, itemSubtype, _itemData = null) => {
      const fortuneValues = [5, 10, 15, 20, 25];
      const fortune = fortuneValues[level - 1] || 0;

      const stats = {};

      if (itemSubtype === "PICKAXE") {
        stats.MINING_FORTUNE = fortune;
        if (level === 5) stats.MINING_SWEEP = 1;
      } else if (itemSubtype === "SHOVEL") {
        stats.MINING_FORTUNE = fortune;
        if (level === 5) stats.MINING_SWEEP = 1;
      } else if (itemSubtype === "FARMING_AXE") {
        // Farming axe - give farming fortune and sweep
        stats.FARMING_FORTUNE = fortune;
        if (level === 5) stats.FARMING_SWEEP = 1;
      } else if (itemSubtype === "AXE") {
        // Foraging axe - give foraging fortune and sweep
        stats.FORAGING_FORTUNE = fortune;
        if (level === 5) stats.FORAGING_SWEEP = 1;
      } else if (itemSubtype === "HOE") {
        stats.FARMING_FORTUNE = fortune;
        if (level === 5) stats.FARMING_SWEEP = 1;
      }

      return stats;
    },
    cost: (level) => {
      return level * 2000; // 2000, 4000, 6000, 8000, 10000 coins
    },
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += i * 2000;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
  },
  VAMPIRISM: {
    name: "Vampirism",
    description:
      "Heals for 1% of your missing health per level whenever you kill a mob.",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: () => ({}), // No direct stat bonus, handled in combat
    cost: (level) => 3000 * level, // 3000, 6000, 9000, 12000, 15000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 3000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    onKill: (level, character) => {
      const currentHealth = character.current_health || character.health;
      const maxHealth = character.maxHealth || character.health;
      const missingHealth = maxHealth - currentHealth;
      const healAmount = missingHealth * (0.01 * level);
      return Math.floor(healAmount);
    },
  },
  FORTUNE: {
    name: "Fortune",
    description:
      "Increases Mining Fortune, which increases your chance for multiple drops.",
    maxLevel: 3,
    applicableTypes: ["PICKAXE"],
    statBonus: (level) => ({ MINING_FORTUNE: level * 10 }),
    cost: (level) => 1500 * level, // 1500, 3000, 4500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 1500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
  },
  SMITE: {
    name: "Smite",
    description:
      "Increases damage dealt to Undead mobs by 10% per level (60% at level 5).",
    maxLevel: 6,
    naturalMaxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: () => ({}), // Effect is conditional in combat
    undeadDamageBonus: {
      1: 10,
      2: 20,
      3: 30,
      4: 40,
      5: 60,
      6: 80,
    },
    cost: (level) => 2500 * level, // 2500, 5000, 7500, 10000, 12500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "⚔️",
  },
  SUNDER: {
    name: "Sunder",
    description: "Grants Farming Fortune on Farming Axes.",
    maxLevel: 6,
    naturalMaxLevel: 0,
    applicableTypes: ["FARMING_AXE"],
    statBonus: (level) => ({ FARMING_FORTUNE: level * 12.5 }),
    cost: (level) => level * 1800,
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += i * 1800;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "<:foraging_fortune:1270616477824712714>",
  },
  BANE_OF_ARTHROPODS: {
    name: "Bane of Arthropods",
    description:
      "Increases damage dealt to Arthropod mobs by 10% per level (60% at level 5).",
    maxLevel: 5,
    applicableTypes: ["WEAPON"],
    statBonus: () => ({}), // Effect is conditional in combat
    arthropodDamageBonus: {
      1: 10,
      2: 20,
      3: 30,
      4: 40,
      5: 60,
    },
    cost: (level) => 2500 * level, // 2500, 5000, 7500, 10000, 12500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "🕷️",
  },
  ANGLER: {
    name: "Angler",
    description: "Increases Sea Creature Chance by 1% per level.",
    maxLevel: 5,
    applicableTypes: ["ROD"],
    statBonus: (level) => ({ SEA_CREATURE_CHANCE: level }),
    cost: (level) => 1500 * level, // 1500, 3000, 4500, 6000, 7500 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 1500 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "🎣",
  },
  CASTER: {
    name: "Caster",
    description: "Gives 5% chance per level to not consume bait.",
    maxLevel: 5,
    applicableTypes: ["ROD"],
    statBonus: () => ({}), // Effect is handled in fishing logic
    baitSaveChance: {
      1: 5,
      2: 10,
      3: 15,
      4: 20,
      5: 25,
    },
    cost: (level) => 2000 * level, // 2000, 4000, 6000, 8000, 10000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 2000 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "🎣",
  },
  FRAIL: {
    name: "Frail",
    description:
      "Sea creatures start combat with 2.5% reduced health per level.",
    maxLevel: 5,
    applicableTypes: ["ROD"],
    statBonus: () => ({}), // Effect is handled in combat logic
    healthReduction: {
      1: 2.5,
      2: 5.0,
      3: 7.5,
      4: 10.0,
      5: 12.5,
    },
    cost: (level) => 1800 * level, // 1800, 3600, 5400, 7200, 9000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 1800 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "💀",
  },
  PISCARY: {
    name: "Piscary",
    description: "Increases Fishing Speed by 2.5 per level.",
    maxLevel: 5,
    applicableTypes: ["ROD"],
    statBonus: (level) => ({ FISHING_SPEED: level * 2.5 }),
    cost: (level) => 1200 * level, // 1200, 2400, 3600, 4800, 6000 coins
    upgradeCost: (fromLevel, toLevel) => {
      if (fromLevel >= toLevel) return 0;
      let totalCost = 0;
      for (let i = fromLevel + 1; i <= toLevel; i++) {
        totalCost += 1200 * i;
      }
      return totalCost;
    },
    emoji: "<:enchanted_book:1376912043185148014>",
    statEmoji: "⚡",
  },
};

function isWeapon(itemType, itemData = null) {
  const isWeaponType =
    itemType === "WEAPON" ||
    (typeof itemType === "object" && itemType.name === "WEAPON");

  if (isWeaponType && itemData) {
    if (
      itemData.type === "TOOL" &&
      (itemData.subtype === "AXE" || itemData.subtype === "FARMING_AXE")
    ) {
      return false;
    }
  }

  return isWeaponType;
}

function isArmor(itemType) {
  const armorTypes = ["HELMET", "CHESTPLATE", "LEGGINGS", "BOOTS", "ARMOR"];
  if (typeof itemType === "string") {
    const result = armorTypes.includes(itemType);
    return result;
  } else if (typeof itemType === "object" && itemType.subtypes) {
    const subtypes = Object.values(itemType.subtypes);
    const result = subtypes.some((subtype) => armorTypes.includes(subtype));
    return result;
  }
  return false;
}

async function getApplicableEnchantments(item, itemId = null) {
  if (!item) return [];

  let itemType = item.type || (item.subtype ? "ARMOR" : null);
  let itemSubtype = item.subtype;

  if (typeof itemType === "object" && itemType.name) {
    itemSubtype = itemType.subtypes
      ? Object.values(itemType.subtypes)[0]
      : null;
    itemType = itemType.name;
  }

  const applicable = [];

  // Get existing enchantments if itemId is provided
  let existingEnchantments = {};
  if (itemId) {
    try {
      existingEnchantments = await getItemEnchantments(itemId);
    } catch (error) {
      console.error("Error getting existing enchantments:", error);
    }
  }

  const isWeaponResult = isWeapon(itemType, item);

  if (isWeaponResult) {
    // Check for mutual exclusivity between Sharpness, Smite, and Bane of Arthropods
    if (
      !existingEnchantments.SMITE &&
      !existingEnchantments.BANE_OF_ARTHROPODS
    ) {
      applicable.push("SHARPNESS");
    }
    if (
      !existingEnchantments.SHARPNESS &&
      !existingEnchantments.BANE_OF_ARTHROPODS
    ) {
      applicable.push("SMITE");
    }
    if (!existingEnchantments.SHARPNESS && !existingEnchantments.SMITE) {
      applicable.push("BANE_OF_ARTHROPODS");
    }

    // Other weapon enchantments (no conflicts)
    applicable.push("LIFE_STEAL");
    applicable.push("CRITICAL");
    applicable.push("LOOTING");
    applicable.push("EXECUTE");
    applicable.push("GIANT_KILLER");
    applicable.push("SCAVENGER");
    applicable.push("VAMPIRISM");
  }

  const isArmorTypeResult = isArmor(itemType);
  const isArmorSubtypeResult = isArmor(itemSubtype);

  if (isArmorTypeResult || isArmorSubtypeResult) {
    applicable.push("PROTECTION");
    applicable.push("GROWTH");
    applicable.push("THORNS");
  }

  if (
    itemSubtype === "HOE" ||
    itemSubtype === "FARMING_AXE" ||
    (itemType === "TOOL" &&
      (itemSubtype === "HOE" || itemSubtype === "FARMING_AXE"))
  ) {
    applicable.push("HARVESTING");
  }

  // Sunder only applies to Farming Axes
  if (
    itemSubtype === "FARMING_AXE" ||
    (itemType === "TOOL" && itemSubtype === "FARMING_AXE")
  ) {
    applicable.push("SUNDER");
  }

  if (
    itemType === "TOOL" &&
    ["PICKAXE", "SHOVEL", "AXE", "FARMING_AXE", "HOE"].includes(itemSubtype)
  ) {
    applicable.push("EFFICIENCY");
  }

  if (
    itemSubtype === "PICKAXE" ||
    (itemType === "TOOL" && itemSubtype === "PICKAXE")
  ) {
    applicable.push("FORTUNE");
  }

  // Fishing rod enchantments
  if (itemSubtype === "ROD" || (itemType === "TOOL" && itemSubtype === "ROD")) {
    applicable.push("ANGLER");
    applicable.push("CASTER");
    applicable.push("FRAIL");
    applicable.push("PISCARY");
  }
  return applicable;
}

// Get current enchantments on an item
async function getItemEnchantments(equipmentId) {
  try {
    const row = await new Promise((resolve, reject) => {
      db.get(
        "SELECT json_extract(data_json, '$.enchantments') as enchantments FROM player_equipment WHERE equipment_id = ?",
        [equipmentId],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });

    if (!row || !row.enchantments) return {};
    return JSON.parse(row.enchantments) || {};
  } catch (error) {
    console.error("Error getting item enchantments:", error);
    return {};
  }
}

// Format stats display
function formatStatBonus(
  enchantKey,
  level,
  itemSubtype = null,
  _itemData = null
) {
  const enchant = ENCHANTMENTS[enchantKey];
  if (!enchant) return "No effect";

  // Special case for Life Steal
  if (enchantKey === "LIFE_STEAL") {
    const healPercent = (level * 0.5).toFixed(1);
    return `Heals for ${healPercent}% of max health on hit`;
  }

  // Special case for Efficiency
  if (enchantKey === "EFFICIENCY") {
    const fortuneValues = [15, 25, 35, 45, 55];
    const fortune = fortuneValues[level - 1] || 0;

    if (itemSubtype === "PICKAXE" || itemSubtype === "SHOVEL") {
      let result = `+${fortune} Mining Fortune`;
      if (level === 5) result += ", +1 Mining Sweep";
      return result;
    } else if (itemSubtype === "FARMING_AXE") {
      // Farming axe
      let result = `+${fortune} Farming Fortune`;
      if (level === 5) result += ", +1 Farming Sweep";
      return result;
    } else if (itemSubtype === "AXE") {
      // Foraging axe
      let result = `+${fortune} Foraging Fortune`;
      if (level === 5) result += ", +1 Foraging Sweep";
      return result;
    } else if (itemSubtype === "HOE") {
      let result = `+${fortune} Farming Fortune`;
      if (level === 5) result += ", +1 Farming Sweep";
      return result;
    } else {
      return `+${fortune} Fortune (tool-specific)`;
    }
  }

  // Special case for Harvesting
  if (enchantKey === "HARVESTING") {
    const fortune = level * 12.5;
    return `+${fortune} Farming Fortune`;
  }

  // Special case for Sunder
  if (enchantKey === "SUNDER") {
    const fortune = level * 12.5;
    return `+${fortune} Farming Fortune`;
  }

  // Special case for Protection
  if (enchantKey === "PROTECTION") {
    const defense = level * 4;
    return `+${defense} Defense`;
  }

  // Special case for Growth
  if (enchantKey === "GROWTH") {
    const health = level * 15;
    return `+${health} Health`;
  }

  // Special case for Sharpness
  if (enchantKey === "SHARPNESS") {
    const damagePercent = level === 5 ? 30 : level * 5;
    return `+${damagePercent}% Damage`;
  }

  // Special case for Critical
  if (enchantKey === "CRITICAL") {
    const critDamage = level * 10;
    return `+${critDamage} Crit Damage`;
  }

  // Special case for Looting
  if (enchantKey === "LOOTING") {
    const bonusPercent = level * 15;
    return `+${bonusPercent}% more items from mobs`;
  }

  // Special case for Thorns
  if (enchantKey === "THORNS") {
    const reflectPercent = level * 3;
    return `+${reflectPercent}% damage reflected on hit (50% chance)`;
  }

  // Special case for Execute
  if (enchantKey === "EXECUTE") {
    const damageIncreasePerMissingPercent =
      ENCHANTMENTS.EXECUTE.missingHealthDamagePercent?.[level] || 0;
    const displayPercent = damageIncreasePerMissingPercent.toFixed(1);
    return `+${displayPercent}% damage for each % missing health`;
  }

  // Special case for Giant Killer
  if (enchantKey === "GIANT_KILLER") {
    const damageIncreasePerExtraHealthPercent =
      ENCHANTMENTS.GIANT_KILLER.damageIncreasePerExtraHealthPercent?.[level] ||
      0;
    const maxDamageBonus =
      ENCHANTMENTS.GIANT_KILLER.maxDamageBonus?.[level] || 0;
    const displayPercent = damageIncreasePerExtraHealthPercent.toFixed(1);
    return `+${displayPercent}% damage for each % extra health mob has over you, up to ${maxDamageBonus}% max`;
  }

  // Special case for Scavenger
  if (enchantKey === "SCAVENGER") {
    const coinsPerMobLevel =
      ENCHANTMENTS.SCAVENGER.coinsPerMobLevel?.[level] || 0;
    return `Enemies drop +${coinsPerMobLevel.toFixed(1)} Coins per mob level`;
  }

  // Special case for Vampirism
  if (enchantKey === "VAMPIRISM") {
    const healPercent = level * 1;
    return `Heals for ${healPercent}% of missing health on kill`;
  }

  // Special case for Smite
  if (enchantKey === "SMITE") {
    const damagePercent = ENCHANTMENTS.SMITE.undeadDamageBonus?.[level] || 0;
    return `+${damagePercent}% damage to Undead mobs`;
  }

  // Special case for Bane of Arthropods
  if (enchantKey === "BANE_OF_ARTHROPODS") {
    const damagePercent =
      ENCHANTMENTS.BANE_OF_ARTHROPODS.arthropodDamageBonus?.[level] || 0;
    return `+${damagePercent}% damage to Arthropod mobs`;
  }

  // Special case for Angler
  if (enchantKey === "ANGLER") {
    const seaCreatureChance = level;
    return `+${seaCreatureChance}% Sea Creature Chance`;
  }

  // Special case for Caster
  if (enchantKey === "CASTER") {
    const baitSaveChance = ENCHANTMENTS.CASTER.baitSaveChance?.[level] || 0;
    return `${baitSaveChance}% chance to not consume bait`;
  }

  // Special case for Frail
  if (enchantKey === "FRAIL") {
    const healthReduction = ENCHANTMENTS.FRAIL.healthReduction?.[level] || 0;
    return `Sea creatures start with ${healthReduction}% reduced health`;
  }

  // Special case for Piscary
  if (enchantKey === "PISCARY") {
    const fishingSpeed = level * 2.5;
    return `+${fishingSpeed} Fishing Speed`;
  }

  // Fallback for any enchantments with statBonus function
  if (enchant.statBonus) {
    const statBonus = enchant.statBonus(level, itemSubtype);
    return Object.entries(statBonus)
      .map(([stat, value]) => {
        if (stat === "DAMAGE_MULT") {
          return `+${value}% Damage`;
        } else if (stat === "DEFENSE") {
          return `+${value} Defense`;
        } else if (stat === "HEALTH") {
          return `+${value} Health`;
        } else if (stat === "CRIT_DAMAGE") {
          return `+${value} Crit Damage`;
        } else if (stat === "SEA_CREATURE_CHANCE") {
          return `+${value}% Sea Creature Chance`;
        } else if (stat === "FISHING_SPEED") {
          return `+${value} Fishing Speed`;
        } else {
          return `+${value} ${stat.replace("_", " ")}`;
        }
      })
      .join(", ");
  }

  // Final fallback
  return enchant.description || "No effect";
}

// Format stat bonus with emoji for success messages
function formatStatBonusWithEmoji(
  enchantKey,
  level,
  itemSubtype = null,
  _itemData = null
) {
  const enchant = ENCHANTMENTS[enchantKey];
  if (!enchant) return "No effect";

  // Special case for Life Steal
  if (enchantKey === "LIFE_STEAL") {
    const healPercent = (level * 0.5).toFixed(1);
    return `<:enchanted_book:1376912043185148014> **Life Steal ${level}:** Heals for ${healPercent}% of max health on hit`;
  }

  // Special case for Efficiency
  if (enchantKey === "EFFICIENCY") {
    const fortuneValues = [15, 25, 35, 45, 55];
    const fortune = fortuneValues[level - 1] || 0;

    if (itemSubtype === "PICKAXE" || itemSubtype === "SHOVEL") {
      let result = `<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:foraging_fortune:1270616477824712714> +${fortune} Mining Fortune`;
      if (level === 5)
        result += `\n<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:mining_sweep:1385551769420959805> +1 Mining Sweep`;
      return result;
    } else if (itemSubtype === "FARMING_AXE") {
      // Farming axe
      let result = `<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:foraging_fortune:1270616477824712714> +${fortune} Farming Fortune`;
      if (level === 5)
        result += `\n<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:farming_sweep:1385551740438450237> +1 Farming Sweep`;
      return result;
    } else if (itemSubtype === "AXE") {
      // Foraging axe
      let result = `<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:foraging_fortune:1270616477824712714> +${fortune} Foraging Fortune`;
      if (level === 5)
        result += `\n<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:foraging_sweep:1385549884186431578> +1 Foraging Sweep`;
      return result;
    } else if (itemSubtype === "HOE") {
      let result = `<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:foraging_fortune:1270616477824712714> +${fortune} Farming Fortune`;
      if (level === 5)
        result += `\n<:enchanted_book:1376912043185148014> **Efficiency ${level}:** <:farming_sweep:1385551740438450237> +1 Farming Sweep`;
      return result;
    } else {
      return `<:enchanted_book:1376912043185148014> **Efficiency ${level}:** ⚡ +${fortune} Fortune (tool-specific)`;
    }
  }

  // Special case for Harvesting
  if (enchantKey === "HARVESTING") {
    const fortune = level * 12.5;
    return `<:enchanted_book:1376912043185148014> **Harvesting ${level}:** <:foraging_fortune:1270616477824712714> +${fortune} Farming Fortune`;
  }

  // Special case for Sunder
  if (enchantKey === "SUNDER") {
    const fortune = level * 12.5;
    return `<:enchanted_book:1376912043185148014> **Sunder ${level}:** <:foraging_fortune:1270616477824712714> +${fortune} Farming Fortune`;
  }

  // Special case for Protection
  if (enchantKey === "PROTECTION") {
    const defense = level * 4;
    return `<:enchanted_book:1376912043185148014> **Protection ${level}:** <:defense:1269719821399097456> +${defense} Defense`;
  }

  // Special case for Growth
  if (enchantKey === "GROWTH") {
    const health = level * 15;
    return `<:enchanted_book:1376912043185148014> **Growth ${level}:** <:health:1269719825429565532> +${health} Health`;
  }

  // Special case for Sharpness
  if (enchantKey === "SHARPNESS") {
    const damagePercent = level === 5 ? 30 : level * 5;
    return `<:enchanted_book:1376912043185148014> **Sharpness ${level}:** <:damage:1270616867484209253> +${damagePercent}% Damage`;
  }

  // Special case for Critical
  if (enchantKey === "CRITICAL") {
    const critDamage = level * 10;
    return `<:enchanted_book:1376912043185148014> **Critical ${level}:** <:critdamage:1269719819884953610> +${critDamage} Crit Damage`;
  }

  // Special case for Looting
  if (enchantKey === "LOOTING") {
    const bonusPercent = level * 15;
    return `<:enchanted_book:1376912043185148014> **Looting ${level}:** +${bonusPercent}% more items from mobs`;
  }

  // Special case for Thorns
  if (enchantKey === "THORNS") {
    const reflectPercent = level * 3;
    return `<:enchanted_book:1376912043185148014> **Thorns ${level}:** +${reflectPercent}% damage reflected on hit (50% chance)`;
  }

  // Special case for Execute
  if (enchantKey === "EXECUTE") {
    const damageIncreasePerMissingPercent =
      ENCHANTMENTS.EXECUTE.missingHealthDamagePercent?.[level] || 0;
    const displayPercent = damageIncreasePerMissingPercent.toFixed(1);
    return `<:enchanted_book:1376912043185148014> **Execute ${level}:** <:damage:1270616867484209253> +${displayPercent}% damage for each % missing health`;
  }

  // Special case for Giant Killer
  if (enchantKey === "GIANT_KILLER") {
    const damageIncreasePerExtraHealthPercent =
      ENCHANTMENTS.GIANT_KILLER.damageIncreasePerExtraHealthPercent?.[level] ||
      0;
    const maxDamageBonus =
      ENCHANTMENTS.GIANT_KILLER.maxDamageBonus?.[level] || 0;
    const displayPercent = damageIncreasePerExtraHealthPercent.toFixed(1);
    return `<:enchanted_book:1376912043185148014> **Giant Killer ${level}:** <:damage:1270616867484209253> +${displayPercent}% damage for each % extra health mob has over you, up to ${maxDamageBonus}% max`;
  }

  // Special case for Scavenger
  if (enchantKey === "SCAVENGER") {
    const coinsPerMobLevel =
      ENCHANTMENTS.SCAVENGER.coinsPerMobLevel?.[level] || 0;
    return `<:enchanted_book:1376912043185148014> **Scavenger ${level}:** Enemies drop <:purse_coins:1367849116033482772> +${coinsPerMobLevel.toFixed(1)} Coins per mob level`;
  }

  // Special case for Vampirism
  if (enchantKey === "VAMPIRISM") {
    const healPercent = level * 1;
    return `<:enchanted_book:1376912043185148014> **Vampirism ${level}:** 🩸 Heals for ${healPercent}% of missing health on kill`;
  }

  // Special case for Smite
  if (enchantKey === "SMITE") {
    const damagePercent = ENCHANTMENTS.SMITE.undeadDamageBonus?.[level] || 0;
    return `<:enchanted_book:1376912043185148014> **Smite ${level}:** ⚔️ +${damagePercent}% damage to Undead mobs`;
  }

  // Special case for Bane of Arthropods
  if (enchantKey === "BANE_OF_ARTHROPODS") {
    const damagePercent =
      ENCHANTMENTS.BANE_OF_ARTHROPODS.arthropodDamageBonus?.[level] || 0;
    return `<:enchanted_book:1376912043185148014> **Bane of Arthropods ${level}:** 🕷️ +${damagePercent}% damage to Arthropod mobs`;
  }

  // Special case for Angler
  if (enchantKey === "ANGLER") {
    const seaCreatureChance = level;
    return `<:enchanted_book:1376912043185148014> **Angler ${level}:** <:sea_creature_chance:1369190745730449479> +${seaCreatureChance} Sea Creature Chance`;
  }

  // Special case for Caster
  if (enchantKey === "CASTER") {
    const baitSaveChance = ENCHANTMENTS.CASTER.baitSaveChance?.[level] || 0;
    return `<:enchanted_book:1376912043185148014> **Caster ${level}:** ${baitSaveChance}% chance to not consume bait`;
  }

  // Special case for Frail
  if (enchantKey === "FRAIL") {
    const healthReduction = ENCHANTMENTS.FRAIL.healthReduction?.[level] || 0;
    return `<:enchanted_book:1376912043185148014> **Frail ${level}:** Sea creatures start with ${healthReduction}% reduced health`;
  }

  // Special case for Piscary
  if (enchantKey === "PISCARY") {
    const fishingSpeed = level * 2.5;
    return `<:enchanted_book:1376912043185148014> **Piscary ${level}:** <:fishing_speed:1270616476428271658> +${fishingSpeed} Fishing Speed`;
  }

  // Fallback for any enchantments with statBonus function
  if (enchant.statBonus) {
    const statBonus = enchant.statBonus(level, itemSubtype);
    const statLines = Object.entries(statBonus)
      .map(([stat, value]) => {
        if (stat === "DAMAGE_MULT") {
          return `<:damage:1270616867484209253> +${value}% Damage`;
        } else if (stat === "DEFENSE") {
          return `<:defense:1269719821399097456> +${value} Defense`;
        } else if (stat === "HEALTH") {
          return `<:health:1269719825429565532> +${value} Health`;
        } else if (stat === "CRIT_DAMAGE") {
          return `<:critdamage:1269719819884953610> +${value} Crit Damage`;
        } else if (stat === "SEA_CREATURE_CHANCE") {
          return `🎣 +${value}% Sea Creature Chance`;
        } else if (stat === "FISHING_SPEED") {
          return `⚡ +${value} Fishing Speed`;
        } else {
          return `+${value} ${stat.replace("_", " ")}`;
        }
      })
      .join(", ");

    return `<:enchanted_book:1376912043185148014> **${enchant.name} ${level}:** ${statLines}`;
  }

  // Final fallback
  return `<:enchanted_book:1376912043185148014> **${enchant.name} ${level}:** ${enchant.description || "No effect"}`;
}

// Map enchant keys to their book item keys
function getBookItemKeyForEnchantLevel(enchKey, level) {
  if (enchKey === "SUNDER") return `SUNDER_${level}_BOOK`;
  if (enchKey === "SMITE" && level === 6) return `SMITE_6_BOOK`;
  return null;
}

// Determine the maximum selectable level for an enchantment given inventory books
function getAllowedMaxLevelForUser(
  enchant,
  enchKey,
  currentLevel,
  inventoryItems
) {
  const naturalMax =
    typeof enchant.naturalMaxLevel === "number"
      ? enchant.naturalMaxLevel
      : enchant.maxLevel;
  let allowedMax = Math.min(enchant.maxLevel, naturalMax);

  // Check for book-gated levels above natural cap
  for (let lvl = naturalMax + 1; lvl <= enchant.maxLevel; lvl++) {
    const bookKey = getBookItemKeyForEnchantLevel(enchKey, lvl);
    if (bookKey && (inventoryItems?.[bookKey] || 0) > 0) {
      allowedMax = Math.max(allowedMax, lvl);
    }
  }

  // Handle book-only enchants (naturalMaxLevel = 0)
  if (naturalMax === 0) {
    for (let lvl = enchant.maxLevel; lvl >= 1; lvl--) {
      const bookKey = getBookItemKeyForEnchantLevel(enchKey, lvl);
      if (bookKey && (inventoryItems?.[bookKey] || 0) > 0) {
        allowedMax = Math.max(allowedMax, lvl);
        break;
      }
    }
  }

  return allowedMax;
}

// Remove an enchantment
async function _removeEnchantment(userId, itemId, enchKey, interaction = null) {
  try {
    // First get the current enchantments to verify and calculate XP
    const itemData = await new Promise((resolve, reject) => {
      db.get(
        "SELECT data_json FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
        [itemId, userId],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });

    if (!itemData) {
      throw new Error("Item not found or you don't own this item.");
    }

    const itemJson = itemData.data_json ? JSON.parse(itemData.data_json) : {};
    const enchantments = itemJson.enchantments || {};

    if (!(enchKey in enchantments)) {
      throw new Error("This enchantment is not on this item.");
    }

    const enchant = ENCHANTMENTS[enchKey];
    const enchantLevel = Number(enchantments[enchKey]);

    // Remove the enchantment
    delete enchantments[enchKey];
    itemJson.enchantments =
      Object.keys(enchantments).length > 0 ? enchantments : undefined;

    // Update the item using batching system
    await dbRunQueued(
      "UPDATE player_equipment SET data_json = ? WHERE equipment_id = ? AND discord_id = ?",
      [JSON.stringify(itemJson), itemId, userId]
    );

    // Calculate XP for removing the enchantment (about 1/4 of what you'd get for applying it)
    const baseXp = 5 * 5;
    const tierBonus = enchantLevel * 5 * 5;
    const expGained = Number(baseXp + tierBonus);

    // Add enchanting experience directly through addSkillExp
    const expResult = await addSkillExp(
      userId,
      "enchanting",
      expGained,
      interaction
    );

    // Recalculate player stats
    await recalculateAndSaveStats(userId, await getPlayerData(userId));

    // Return success message
    const enchantName = enchant?.name || enchKey;
    return {
      success: true,
      message: `${enchantName} has been removed from this item.`,
      expGained,
      levelUpEmbeds: expResult?.levelUpEmbeds || null,
    };
  } catch (error) {
    console.error("Error in removeEnchantment:", error);
    return { success: false, message: "An unexpected error occurred." };
  }
}

// Apply enchantment to item with specified level
async function _applyEnchantment(
  userId,
  itemId,
  enchKey,
  targetLevel,
  currentLevel = 0,
  interaction = null
) {
  try {
    // Convert to numbers to ensure proper calculations
    targetLevel = Number(targetLevel);
    currentLevel = Number(currentLevel || 0);

    // Check if player exists
    const playerExists = await new Promise((resolve) => {
      db.get("SELECT 1 FROM players WHERE discord_id = ?", [userId], (err) => {
        resolve(!err);
      });
    });
    if (!playerExists) {
      return { success: false, message: "Player data not found." };
    }

    // Get enchantment details
    const enchant = ENCHANTMENTS[enchKey];
    if (!enchant) {
      return { success: false, message: "Invalid enchantment." };
    }

    // Validate target level
    if (targetLevel <= currentLevel || targetLevel > (enchant.maxLevel || 1)) {
      return {
        success: false,
        message: "Invalid target level for this enchant.",
      };
    }

    // Calculate cost
    const cost = enchant.upgradeCost(currentLevel, targetLevel);

    // Verify item ownership and get current data
    const equipQuery = `
            SELECT data_json, item_key
            FROM player_equipment
            WHERE equipment_id = ? AND discord_id = ?`;

    const equipRow = await new Promise((resolve, reject) => {
      db.get(equipQuery, [itemId, userId], (err, row) => {
        if (err) return reject(err);
        resolve(row);
      });
    });

    if (!equipRow) {
      return {
        success: false,
        message: "Item not found or you don't own this item.",
      };
    }

    // Get item data to determine subtype
    const itemKey = equipRow.item_key;
    const allItems = configManager.getAllItems();
    const itemData = allItems[itemKey];
    const itemSubtype = itemData?.subtype || null;

    // Get and update enchantments
    const currentData = equipRow.data_json
      ? JSON.parse(equipRow.data_json)
      : {};
    const existingEnchantments = currentData.enchantments || {};
    const newEnchantments = { ...existingEnchantments, [enchKey]: targetLevel };

    // Prepare updated data
    currentData.enchantments = newEnchantments;
    const newFullDataJsonString = JSON.stringify(currentData);

    // Book gating: require book for levels above natural cap or book-only enchants
    const naturalMax =
      typeof enchant.naturalMaxLevel === "number"
        ? enchant.naturalMaxLevel
        : enchant.maxLevel;
    const requiresBook = targetLevel > naturalMax || naturalMax === 0;
    let itemsToChange = [];
    if (requiresBook) {
      const bookKey = getBookItemKeyForEnchantLevel(enchKey, targetLevel);
      if (!bookKey) {
        return {
          success: false,
          message: "Required Enchanted Book not found for this level.",
        };
      }
      // Verify player owns the book
      const inv = await getInventory(userId);
      const qty = (inv.items && inv.items[bookKey]) || 0;
      if (qty <= 0) {
        return {
          success: false,
          message: `You need a ${bookKey.replace(/_/g, " ")} to apply this enchant level.`,
        };
      }
      itemsToChange.push({ itemKey: bookKey, amount: -1 });
    }

    // Apply atomically: deduct coins, consume book if needed, and update equipment JSON
    try {
      await updateInventoryAtomically(
        userId,
        -cost,
        itemsToChange,
        [],
        [],
        0,
        [{ equipmentId: itemId, dataJson: newFullDataJsonString }],
        null,
        null,
        false,
        [],
        []
      );
    } catch (atomicErr) {
      const msg = atomicErr?.message || "Transaction failed.";
      // Surface insufficient funds/items messages when available
      return { success: false, message: msg };
    }

    // Enchantment and coin deduction succeeded. Now handle XP and stats.

    // Calculate enchanting experience based on level
    const baseXp = 25 * 5;
    const tierBonus = targetLevel * 25 * 5;
    const levelDiffMultiplier = (targetLevel - currentLevel) * 2 * 5;
    const expGained = Number(baseXp + tierBonus + levelDiffMultiplier);
    const expResult = await addSkillExp(
      userId,
      "enchanting",
      expGained,
      interaction
    );

    // Recalculate and save stats
    await recalculateAndSaveStats(userId, await getPlayerData(userId));

    // Get the latest coin balance directly from database
    const newBalance = await getPlayerCoins(userId);

    return {
      success: true,
      message: `Successfully applied ${enchant.name} ${targetLevel} to your item!`,
      cost,
      newBalance,
      expGained,
      statBonus: formatStatBonus(enchKey, targetLevel, itemSubtype, itemData),
      statBonusWithEmoji: formatStatBonusWithEmoji(
        enchKey,
        targetLevel,
        itemSubtype,
        itemData
      ),
      levelUpEmbeds: expResult?.levelUpEmbeds || null,
    };
  } catch (error) {
    console.error("Error applying enchantment:", error);
    // Provide more specific error messages if possible
    if (error.message && error.message.includes("Insufficient purse coins")) {
      return { success: false, message: error.message };
    }
    return {
      success: false,
      message:
        "An error occurred while applying the enchantment. Please try again.",
    };
  }
}

// Helper function to get player coins directly from database
async function getPlayerCoins(userId) {
  try {
    const row = await new Promise((resolve, reject) => {
      db.get(
        "SELECT coins FROM players WHERE discord_id = ?",
        [userId],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });
    return row?.coins || 0;
  } catch (error) {
    console.error("Error fetching player coins:", error);
    return 0;
  }
}

// Create the main enchanting UI
async function createEnchantingUI(userId, selectedItem) {
  // Get all items and player data in parallel
  const [allItems, currentEnchantments, playerCoins, inventory] =
    await Promise.all([
      configManager.getAllItems(),
      getItemEnchantments(selectedItem.id),
      getPlayerCoins(userId),
      getInventory(userId),
    ]);

  const itemData = allItems[selectedItem.itemKey];
  if (!itemData) {
    return { error: "Item data not found." };
  }

  const applicableEnchantmentKeys = await getApplicableEnchantments(
    itemData,
    selectedItem.id
  );

  // Create an enchantment summary field
  let enchantmentSummary = "";
  if (Object.keys(currentEnchantments).length > 0) {
    enchantmentSummary = Object.entries(currentEnchantments)
      .map(([key, level]) => {
        const enchant = ENCHANTMENTS[key];
        if (!enchant) return `${key}: Level ${level}`;

        return `${formatStatBonusWithEmoji(key, level, itemData.subtype, itemData)}`;
      })
      .join("\n");
  } else {
    enchantmentSummary = "*No enchantments*";
  }

  // Create embed
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PURPLE)
    .setTitle(
      `${skillEmojis.enchanting} Enchanting Table: ${
        itemData.emoji || "❓"
      } ${itemData.name}`
    )
    .addFields(
      {
        name: "Your Coins",
        value: `<:purse_coins:1367849116033482772> **${playerCoins.toLocaleString()}**`,
        inline: true,
      },
      {
        name: "Current Enchantments",
        value: enchantmentSummary,
        inline: false,
      }
    )
    .setFooter({ text: "Select an option below to continue" })
    .setDescription(
      "Enchanted Books in your inventory unlock higher tiers for certain enchants."
    );

  // Create components for interaction
  const rows = [];

  // Add buttons for each applicable enchantment (max 5 per row)
  let currentEnchantRow = new ActionRowBuilder();
  let buttonCount = 0;

  applicableEnchantmentKeys.forEach((key) => {
    const enchant = ENCHANTMENTS[key];
    const currentLevel = currentEnchantments[key] || 0;
    const allowedMax = getAllowedMaxLevelForUser(
      enchant,
      key,
      currentLevel,
      inventory.items || {}
    );
    const hasUpgrade = currentLevel < allowedMax;

    // Only add button if upgrade exists (natural or book-gated)
    if (hasUpgrade) {
      // If we've reached 5 buttons, start a new row
      if (buttonCount === 5) {
        rows.push(currentEnchantRow);
        currentEnchantRow = new ActionRowBuilder();
        buttonCount = 0;
      }

      currentEnchantRow.addComponents(
        new ButtonBuilder()
          .setCustomId(`ench_add:${selectedItem.id}:${key}:${currentLevel}`)
          .setLabel(`${enchant.name} ${currentLevel > 0 ? currentLevel : ""}`)
          .setEmoji(enchant.emoji || "✨")
          .setStyle(ButtonStyle.Primary)
      );
      buttonCount++;
    }
  });

  // Add the last row if it has any buttons
  if (currentEnchantRow.components.length > 0) {
    rows.push(currentEnchantRow);
  }

  // Add actions row (remove enchantments, etc)
  const actionsRow = new ActionRowBuilder();

  // Only add remove button if there are enchantments to remove
  if (Object.keys(currentEnchantments).length > 0) {
    actionsRow.addComponents(
      new ButtonBuilder()
        .setCustomId(`ench_remove_menu:${selectedItem.id}`)
        .setLabel("Remove Enchantment")
        .setEmoji("<:enchanted_book:1376912043185148014>")
        .setStyle(ButtonStyle.Danger)
    );
  }

  // Add close button
  actionsRow.addComponents(
    new ButtonBuilder()
      .setCustomId("ench_close")
      .setLabel("Close")
      //.setEmoji("🚪") // no longer used for consistency
      .setStyle(ButtonStyle.Secondary)
  );

  // Always add the actions row since it will always have at least the close button
  rows.push(actionsRow);

  return { embed, components: rows };
}

// Create level selection UI
async function _createLevelSelectionUI(
  userId,
  selectedItem,
  enchKey,
  currentLevel
) {
  const allItems = configManager.getAllItems();
  const itemData = allItems[selectedItem.itemKey];
  const enchant = ENCHANTMENTS[enchKey];
  const inventory = await getInventory(userId);

  if (!enchant) {
    return { error: "Invalid enchantment" };
  }

  // Create select menu options for available levels
  const selectOptions = [];
  const naturalMax =
    typeof enchant.naturalMaxLevel === "number"
      ? enchant.naturalMaxLevel
      : enchant.maxLevel;
  const allowedMax = getAllowedMaxLevelForUser(
    enchant,
    enchKey,
    currentLevel,
    inventory.items || {}
  );
  for (let level = currentLevel + 1; level <= allowedMax; level++) {
    const cost = enchant.upgradeCost(currentLevel, level);
    const requiresBook = level > naturalMax || naturalMax === 0;
    const label = requiresBook
      ? `${enchant.name} ${level} (${cost.toLocaleString()} coins) • Requires Book`
      : `${enchant.name} ${level} (${cost.toLocaleString()} coins)`;
    const desc = `Upgrade to level ${level}`;
    selectOptions.push(
      new StringSelectMenuOptionBuilder()
        .setLabel(label)
        .setDescription(desc)
        .setValue(`${selectedItem.id}:${enchKey}:${currentLevel}:${level}`)
        .setEmoji("<:enchanted_book:1376912043185148014>")
    );
  }

  const rows = [];

  // Add select menu if there are options
  if (selectOptions.length > 0) {
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("ench_level_select")
      .setPlaceholder("Choose a level to apply")
      .addOptions(selectOptions);

    rows.push(new ActionRowBuilder().addComponents(selectMenu));
  }

  // Add back button
  const backRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`ench_back:${selectedItem.id}`)
      .setLabel("Go Back")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("⬅️")
  );
  rows.push(backRow);

  // Create embed
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PURPLE)
    .setTitle(
      `${skillEmojis.enchanting} Enchanting Table: ${
        itemData.emoji || "❓"
      } ${itemData.name}`
    )
    .setDescription(
      `Select a level to apply **${enchant.emoji || "✨"} ${
        enchant.name
      }**\n\n**Current Level:** ${
        currentLevel > 0 ? currentLevel : "None"
      }\n**Effect:** ${enchant.description}`
    );

  // Show message if no upgrades available
  if (selectOptions.length === 0) {
    embed.addFields({
      name: "Available Upgrades",
      value:
        "No upgrades available. You may need an Enchanted Book for higher tiers.",
    });
  }

  return { embed, components: rows };
}

// Create remove enchantment UI
async function _createRemoveEnchantmentUI(userId, itemId) {
  try {
    // Get item data directly from database
    const [itemRow, allItems] = await Promise.all([
      new Promise((resolve, reject) => {
        db.get(
          "SELECT data_json, item_key FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
          [itemId, userId],
          (err, row) => (err ? reject(err) : resolve(row))
        );
      }),
      configManager.getAllItems(),
    ]);

    if (!itemRow) {
      return { error: "Item not found or you don't own this item." };
    }

    const itemData = allItems[itemRow.item_key];
    if (!itemData) {
      return { error: "Item data not found." };
    }

    // Parse current enchantments
    const currentData = JSON.parse(itemRow.data_json || "{}");
    const currentEnchantments = currentData.enchantments || {};

    if (Object.keys(currentEnchantments).length === 0) {
      return { error: "This item has no enchantments to remove." };
    }

    // Create buttons for each enchantment, splitting into rows of max 5
    const rows = [];
    let currentEnchantRow = new ActionRowBuilder();
    let buttonCount = 0;

    // Add buttons for each enchantment
    let validEnchantmentCount = 0;
    Object.entries(currentEnchantments).forEach(([key, level]) => {
      const enchant = ENCHANTMENTS[key];
      if (!enchant) return;

      // Start a new row if the current one is full
      if (buttonCount === 5) {
        if (currentEnchantRow.components.length > 0) {
          rows.push(currentEnchantRow);
        }
        currentEnchantRow = new ActionRowBuilder();
        buttonCount = 0;
      }

      currentEnchantRow.addComponents(
        new ButtonBuilder()
          .setCustomId(`ench_remove:${itemId}:${key}`)
          .setLabel(`${enchant.name} ${level}`)
          .setEmoji(enchant.emoji || "✨")
          .setStyle(ButtonStyle.Danger)
      );
      buttonCount++;
      validEnchantmentCount++;
    });

    // If no valid enchantments were found, return error
    if (validEnchantmentCount === 0) {
      return { error: "This item has no valid enchantments to remove." };
    }

    // Push the last row if it contains any buttons
    if (currentEnchantRow.components.length > 0) {
      rows.push(currentEnchantRow);
    }

    // Add back button
    const backRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`ench_back:${itemId}`)
        .setLabel("Go Back")
        .setStyle(ButtonStyle.Secondary)
        .setEmoji("⬅️")
    );
    rows.push(backRow);

    // Create embed
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle(
        `${skillEmojis.enchanting} Enchanting Table: ${
          itemData.emoji || "❓"
        } ${itemData.name}`
      )
      .setDescription(`Select an enchantment to remove`)
      .setFooter({ text: "This action cannot be undone" });

    return { embed, components: rows };
  } catch (error) {
    console.error("Error in createRemoveEnchantmentUI:", error);
    return { error: "An error occurred while loading enchantment data." };
  }
}

module.exports = {
  ENCHANTMENTS, // Export the enchantments object for use in other modules
  formatStatBonusWithEmoji, // Export the formatting function for use in other modules
  data: new SlashCommandBuilder()
    .setName("enchant")
    .setDescription("Enchant an item")
    .addStringOption((option) =>
      option
        .setName("item_id")
        .setDescription("The item to enchant")
        .setRequired(true)
        .setAutocomplete(true)
    ),

  async execute(interaction) {
    await interaction.deferReply();

    const userId = interaction.user.id;
    const selectedItemId = interaction.options.getString("item_id");

    try {
      // Get player data and inventory
      const character = await getPlayerData(userId);
      const inventory = await getInventory(userId);

      if (!character) {
        return interaction.editReply({
          content: "You must have a character to use this command.",
        });
      }

      // check if player is in The Hub or has active booster cookie
      const hasActiveBoosterCookie = isBoosterCookieActive(character);
      const isInHub = character.current_region === "the_hub";

      if (!isInHub && !hasActiveBoosterCookie) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("Enchanting Not Available")
          .setDescription(
            "Enchanting services are only available in **The Hub**.\n\n💡 **Tip:** You can access enchanting from anywhere with an active Booster Cookie!"
          );

        const response = await interaction.editReply({ embeds: [errorEmbed] });
        setTimeout(() => {
          response.delete().catch(() => {});
        }, 10000);
        return;
      }

      // Find the selected item
      const selectedItem = inventory.equipment.find(
        (item) => item.id === selectedItemId
      );
      if (!selectedItem) {
        return interaction.editReply({
          content: "That item does not exist in your inventory.",
        });
      }

      // Get the item data
      const allItems = configManager.getAllItems();
      const itemData = allItems[selectedItem.itemKey];

      if (!itemData) {
        return interaction.editReply({
          content: "Could not find data for that item.",
        });
      }

      // Get applicable enchantments
      const applicableEnchantmentKeys = await getApplicableEnchantments(
        itemData,
        selectedItem.id
      );
      if (applicableEnchantmentKeys.length === 0) {
        return interaction.editReply({
          content: "This item cannot be enchanted.",
        });
      }

      // Create the main enchanting UI
      const { embed, components } = await createEnchantingUI(
        userId,
        selectedItem,
        character
      );

      // Send the embed with the components
      // Note: Interactions are now handled by persistent handlers in bot.js
      // This eliminates the 15-minute interaction timeout limitation
      await interaction.editReply({
        embeds: [embed],
        components: components,
      });
    } catch (error) {
      console.error("Error in /enchant command:", error);
      return interaction.editReply({
        content: "An error occurred while processing your command.",
      });
    }
  },

  async autocomplete(interaction) {
    const focusedValue = interaction.options.getFocused();

    try {
      const userId = interaction.user.id;
      const inventory = await getInventory(userId);

      if (
        !inventory ||
        !inventory.equipment ||
        inventory.equipment.length === 0
      ) {
        return interaction.respond([]);
      }

      const allItems = configManager.getAllItems();

      // Filter out items that can be enchanted
      const enchantableItems = [];
      for (const item of inventory.equipment) {
        const itemData = allItems[item.itemKey];
        if (!itemData) continue;

        // Check if the item has applicable enchantments
        const applicableEnchants = await getApplicableEnchantments(
          itemData,
          null
        );
        if (applicableEnchants.length > 0) {
          enchantableItems.push(item);
        }
      }

      // Format the options
      let options = enchantableItems.map((item) => {
        const itemData = allItems[item.itemKey];
        return {
          name: `${itemData.name} (${item.id.substring(0, 4)})`,
          value: item.id,
        };
      });

      // Filter options if needed
      if (focusedValue) {
        const lowercaseFocused = focusedValue.toLowerCase();
        options = options.filter(
          (option) =>
            option.name.toLowerCase().includes(lowercaseFocused) ||
            option.value.toLowerCase().includes(lowercaseFocused)
        );
      }

      // Limit to 25 options
      options = options.slice(0, 25);

      return interaction.respond(options);
    } catch (error) {
      console.error("Error in /enchant autocomplete:", error);
      return interaction.respond([]);
    }
  },

  // Persistent button handler - never expires!
  async handleButton(interaction) {
    try {
      await interaction.deferUpdate();

      const customId = interaction.customId;

      // Close button - show closing message
      if (customId === "ench_close") {
        const closeEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.PURPLE)
          .setTitle(`${skillEmojis.enchanting} Enchanting Table`)
          .setDescription("Enchanting session closed.");

        await interaction.editReply({
          embeds: [closeEmbed],
          components: [],
        });
        return;
      }

      if (customId.startsWith("ench_back:")) {
        const itemId = customId.split(":")[1];
        return await this.showEnchantingMenu(interaction, itemId);
      }

      if (customId.startsWith("ench_add:")) {
        const parts = customId.split(":");
        const itemId = parts[1];
        const enchKey = parts[2];
        const currentLevel = parseInt(parts[3]);
        return await this.showLevelSelection(
          interaction,
          itemId,
          enchKey,
          currentLevel
        );
      }

      if (customId.startsWith("ench_remove_menu:")) {
        const itemId = customId.split(":")[1];
        return await this.showRemoveEnchantmentMenu(interaction, itemId);
      }

      if (customId.startsWith("ench_remove:")) {
        const parts = customId.split(":");
        const itemId = parts[1];
        const enchKey = parts[2];
        return await this.removeEnchantmentFromItem(
          interaction,
          itemId,
          enchKey
        );
      }

      if (customId.startsWith("ench_level:")) {
        const parts = customId.split(":");
        const itemId = parts[1];
        const level = parseInt(parts[2]);
        return await this.showLevelEnchantOptions(interaction, itemId, level);
      }

      if (customId.startsWith("ench_apply:")) {
        const parts = customId.split(":");
        const itemId = parts[1];
        const level = parseInt(parts[2]);
        const stat = parts[3];
        return await this.applyEnchantment(interaction, itemId, level, stat, 1);
      }

      if (customId.startsWith("ench_max:")) {
        const parts = customId.split(":");
        const itemId = parts[1];
        const level = parseInt(parts[2]);
        const stat = parts[3];
        return await this.applyEnchantment(
          interaction,
          itemId,
          level,
          stat,
          "max"
        );
      }

      if (customId.startsWith("ench_custom:")) {
        const parts = customId.split(":");
        const itemId = parts[1];
        const level = parseInt(parts[2]);
        const stat = parts[3];
        // Show modal for custom amount
        return await this.showCustomAmountModal(
          interaction,
          itemId,
          level,
          stat
        );
      }
    } catch (error) {
      console.error("[Enchant] Error in persistent button handler:", error);
      try {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription(
            "❌ An error occurred while processing your enchantment request."
          );

        if (interaction.deferred) {
          await interaction.editReply({ embeds: [errorEmbed], components: [] });
        } else {
          await interaction.reply({
            embeds: [errorEmbed],
            components: [],
            ephemeral: true,
          });
        }
      } catch (replyError) {
        console.error("[Enchant] Failed to send error message:", replyError);
      }
    }
  },

  // Persistent select menu handler - never expires!
  async handleSelectMenu(interaction) {
    try {
      if (interaction.customId === "ench_level_select") {
        await interaction.deferUpdate();

        const selectedValue = interaction.values[0];
        const [itemId, enchKey, currentLevelStr, targetLevelStr] =
          selectedValue.split(":");
        const currentLevel = parseInt(currentLevelStr);
        const targetLevel = parseInt(targetLevelStr);
        const userId = interaction.user.id;

        // Get item data for title
        const inventory = await getInventory(userId);
        const item = inventory.equipment.find((item) => item.id === itemId);

        if (!item) {
          const errorEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setDescription("❌ Item not found in your inventory.");

          await interaction.editReply({ embeds: [errorEmbed], components: [] });
          return;
        }

        const allItems = configManager.getAllItems();
        const itemData = allItems[item?.itemKey];

        // Apply the enchantment
        const result = await _applyEnchantment(
          userId,
          itemId,
          enchKey,
          targetLevel,
          currentLevel,
          interaction
        );

        if (result.success) {
          const successEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.LIGHT_GREEN)
            .setTitle(
              `${skillEmojis.enchanting} Enchanting Table: ${
                itemData?.emoji || "❓"
              } ${itemData?.name || "Unknown Item"}`
            )
            .setDescription(
              `**Effect Added:**\n${
                result.statBonusWithEmoji
              }\n\n**Cost:** <:purse_coins:1367849116033482772> ${result.cost.toLocaleString()} Coins\n**Remaining:** <:purse_coins:1367849116033482772> ${result.newBalance.toLocaleString()} Coins\n\n${
                skillEmojis.enchanting
              } \`EXP: Enchanting +${result.expGained.toFixed(2)}\``
            );

          // Add button to return to enchanting menu
          const returnRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId(`ench_back:${itemId}`)
              .setLabel("Continue Enchanting")
              .setStyle(ButtonStyle.Success)
          );

          await interaction.editReply({
            embeds: [successEmbed],
            components: [returnRow],
          });

          // Note: Level-up notifications are automatically sent by addSkillExp when interaction is provided
        } else {
          // Handle error case
          const errorEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setDescription(`❌ ${result.message}`);

          await interaction.editReply({ embeds: [errorEmbed], components: [] });
        }
        return;
      }
    } catch (error) {
      console.error(
        "[Enchant] Error in persistent select menu handler:",
        error
      );
      try {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription(
            "❌ An error occurred while processing your enchantment selection."
          );

        if (interaction.deferred) {
          await interaction.editReply({ embeds: [errorEmbed], components: [] });
        } else {
          await interaction.reply({
            embeds: [errorEmbed],
            components: [],
            ephemeral: true,
          });
        }
      } catch (replyError) {
        console.error("[Enchant] Failed to send error message:", replyError);
      }
    }
  },

  // Helper functions for persistent handlers
  async showEnchantingMenu(interaction, itemId) {
    try {
      const userId = interaction.user.id;
      const inventory = await getInventory(userId);
      const selectedItem = inventory.equipment.find(
        (item) => item.id === itemId
      );

      if (!selectedItem) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription("❌ Item not found in your inventory.");

        await interaction.editReply({ embeds: [errorEmbed], components: [] });
        return;
      }

      // Create updated UI
      const { embed, components } = await createEnchantingUI(
        userId,
        selectedItem
      );

      await interaction.editReply({
        embeds: [embed],
        components: components,
      });
    } catch (error) {
      console.error("[Enchant] Error showing enchanting menu:", error);
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription("❌ Failed to load enchanting menu.");

      await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
  },

  async showLevelSelection(interaction, itemId, enchKey, currentLevel) {
    try {
      const userId = interaction.user.id;
      const inventory = await getInventory(userId);
      const selectedItem = inventory.equipment.find(
        (item) => item.id === itemId
      );

      if (!selectedItem) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription("❌ Item not found in your inventory.");

        await interaction.editReply({ embeds: [errorEmbed], components: [] });
        return;
      }

      // Create level selection UI
      const result = await _createLevelSelectionUI(
        userId,
        selectedItem,
        enchKey,
        currentLevel
      );

      if (result.error) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription(`❌ ${result.error}`);

        await interaction.editReply({ embeds: [errorEmbed], components: [] });
        return;
      }

      await interaction.editReply({
        embeds: [result.embed],
        components: result.components,
      });
    } catch (error) {
      console.error("[Enchant] Error showing level selection:", error);
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription("❌ Failed to load level selection.");

      await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
  },

  async showRemoveEnchantmentMenu(interaction, itemId) {
    try {
      const userId = interaction.user.id;

      // Create remove enchantment UI
      const result = await _createRemoveEnchantmentUI(userId, itemId);

      if (result.error) {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription(`❌ ${result.error}`);

        await interaction.editReply({ embeds: [errorEmbed], components: [] });
        return;
      }

      await interaction.editReply({
        embeds: [result.embed],
        components: result.components,
      });
    } catch (error) {
      console.error("[Enchant] Error showing remove enchantment menu:", error);
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription("❌ Failed to load remove enchantment menu.");

      await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
  },

  async removeEnchantmentFromItem(interaction, itemId, enchKey) {
    try {
      const userId = interaction.user.id;

      // Remove the enchantment
      const result = await _removeEnchantment(
        userId,
        itemId,
        enchKey,
        interaction
      );

      if (result.success) {
        const successEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.LIGHT_GREEN)
          .setTitle("✅ Enchantment Removed")
          .setDescription(`${result.message}`)
          .addFields({
            name: "Enchanting XP Gained",
            value: `${skillEmojis.enchanting} \`EXP: Enchanting +${result.expGained.toFixed(2)}\``,
            inline: true,
          });

        // Add button to return to enchanting menu
        const returnRow = new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId(`ench_back:${itemId}`)
            .setLabel("Continue Enchanting")
            .setStyle(ButtonStyle.Success)
            .setEmoji("⚡")
        );

        await interaction.editReply({
          embeds: [successEmbed],
          components: [returnRow],
        });

        // Note: Level-up notifications are automatically sent by addSkillExp when interaction is provided
      } else {
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setDescription(`❌ ${result.message}`);

        await interaction.editReply({ embeds: [errorEmbed], components: [] });
      }
    } catch (error) {
      console.error("[Enchant] Error removing enchantment:", error);
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription("❌ Failed to remove enchantment.");

      await interaction.editReply({ embeds: [errorEmbed], components: [] });
    }
  },

  async showLevelEnchantOptions(interaction, itemId, level) {
    // TODO: Implement - this would show enchantment options for a specific level
    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.YELLOW)
      .setDescription(
        `🔧 Level ${level} enchanting options - implementation needed!`
      );

    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  },

  async applyEnchantment(interaction, itemId, level, stat, amount) {
    // TODO: Implement - this would apply the actual enchantment
    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.YELLOW)
      .setDescription(
        `🔧 Apply ${stat} level ${level} (${amount}x) - implementation needed!`
      );

    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  },

  async showCustomAmountModal(interaction, _itemId, _level, _stat) {
    // TODO: Implement - this would show a modal for custom enchantment amount
    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.YELLOW)
      .setDescription("🔧 Custom amount modal - implementation needed!");

    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  },
};
