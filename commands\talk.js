const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  ComponentType,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  MessageFlags,
} = require("discord.js");
const {
  CURRENCY,
  NPC_TYPES,
  ITEM_RARITY,
  STATS,
  EMBED_COLORS,
  skillEmojis,
  GEMS_EMOJI,
  BITS_EMOJI,
} = require("../gameConfig");
const { canReforge, getReforgeCost } = require("../data/reforges");
const NPCS = require("../data/npcs.js");
const { getNPCGreeting } = require("../data/npcs.js");
const configManager = require("../utils/configManager");
const {
  getPlayerData,
  savePlayerData,
  recalculateAndSaveStats,
  purchasePet,
  updatePlayerSetting,
  updateCurrencyAtomically,
} = require("../utils/playerDataManager");
const { updateInventoryAtomically } = require("../utils/inventory");
const { showBankingMenu } = require("./bank");
const { checkRankPermission } = require("../utils/permissionUtils");
const { v4: uuidv4 } = require("uuid");
const { createPetInstance, getPetLevel } = require("../utils/petUtils");
const { isBakerSpawnWindow, getEndingYear } = require("../utils/timeUtils");
const { wrapText } = require("../utils/textUtils");
const { formatNumber } = require("../utils/displayUtils");
const handleReroll = require("../utils/handleReroll");
const { handleMaxwellInteraction } = require("./maxwell");
const { parseShorthandAmount, formatXP } = require("../utils/formatUtils");
const { getDynamicReforgeName } = require("../utils/dynamicReforgeStats");
const { getItemColor } = require("../utils/rarityUtils");

const { STAT_ABBREVIATIONS } = require("../utils/statAbbreviations");
const {
  getCurrentActivity,
  warnUserBusy,
} = require("../utils/activityManager");
const bot = require("../bot.js"); // Added for global access to timeState
const {
  getUpgradeCost,
  getUpgradeDuration,
  getNextRarity,
  startPetUpgrade,
  completePetUpgrade,
  checkPetUpgradeStatus,
  formatTimeRemaining,
} = require("../utils/petUpgradeUtils");

const BEE_PET_RARITY_PRICES = {
  COMMON: 5000,
  UNCOMMON: 15000,
  RARE: 50000,
  EPIC: 200000,
  LEGENDARY: 650000,
};
const BEE_PET_RARITIES = ["COMMON", "UNCOMMON", "RARE", "EPIC", "LEGENDARY"];

const SELL_ITEM_MENU_ID = "sell_item_menu";
const SELL_PREV_PAGE_ID = "sell_prev_page";
const SELL_NEXT_PAGE_ID = "sell_next_page";

const shopMessageIds = new Map();
const sellMenuMessageIds = new Map();
const buyMenuMessageIds = new Map();

/**
 * Generates shop display embed with optional details
 * @param {object} npc - The NPC object
 * @param {object} allItems - All items data
 * @param {number} playerCoins - Player's coin amount
 * @param {boolean} showDetails - Whether to show item details
 * @param {object} shopLimits - Optional shop limits object with itemKey -> remainingLimit mapping
 * @returns {object} - Object containing embed and hasDetails flag
 */
async function generateShopDisplay(
  npc,
  allItems,
  playerCoins,
  showDetails = false,
  shopLimits = null
) {
  const inventoryItems =
    npc.shopInventory
      ?.sort((a, b) => (b.price || 0) - (a.price || 0))
      ?.map((item) => {
        const itemData = allItems[item.itemKey];
        if (!itemData) {
          console.warn(
            `[generateShopDisplay] Item data not found for key: ${item.itemKey} in NPC ${npc.key}'s inventory.`
          );
          return null;
        }
        const name = itemData.name || `Unknown (${item.itemKey})`;
        const price = item.price?.toLocaleString() || "?";
        const itemEmoji = itemData.emoji || "❓";
        return { name, price, itemEmoji, itemData, itemKey: item.itemKey };
      })
      .filter((item) => item !== null) || [];

  const maxNameLen = Math.max(
    ...inventoryItems.map((item) => item.name.length),
    0
  );

  // Check if any items have details to show
  const hasDetails = inventoryItems.some((item) => {
    return (
      (item.itemData.baseStats &&
        Object.keys(item.itemData.baseStats).length > 0) ||
      (item.itemData.effects &&
        Object.keys(item.itemData.effects).length > 0) ||
      (item.itemData.description && item.itemData.description.trim() !== "")
    );
  });

  // Generate item display strings
  const itemDisplays = inventoryItems.map((item) => {
    let statsDisplay = "";

    if (showDetails) {
      // Check if item has baseStats or effects to show instead of description
      if (
        item.itemData.baseStats &&
        Object.keys(item.itemData.baseStats).length > 0
      ) {
        const { STATS } = require("../gameConfig");
        statsDisplay = Object.entries(item.itemData.baseStats)
          .map(([statKey, value]) => {
            const statConfig = STATS[statKey];
            const statEmoji = statConfig?.emoji || "❓";
            const statName = statConfig?.name || statKey;
            return `\n${statEmoji} **\`+${value} ${statName}\`**`;
          })
          .join("");
      } else if (
        item.itemData.effects &&
        Object.keys(item.itemData.effects).length > 0
      ) {
        statsDisplay = Object.entries(item.itemData.effects)
          .map(([effectKey, value]) => {
            if (effectKey === "zombieDamageReduction") {
              const percentage = Math.round(value * 100);
              return `\n<:defense:1269719821399097456> **\`${percentage}% Damage Reduction vs Zombies\`**`;
            } else if (effectKey === "skeletonDamageReduction") {
              const percentage = Math.round(value * 100);
              return `\n<:defense:1269719821399097456> **\`${percentage}% Damage Reduction vs Skeletons\`**`;
            } else if (effectKey === "hubHealthBonus") {
              return `\n<:health:1269719825429565532> **\`+${value} Health in The Hub\`**`;
            } else if (effectKey === "scavengerBonus") {
              return `\n<:purse_coins:1367849116033482772> **\`+${value} Coins per Enemy Level on kill\`**`;
            } else if (effectKey === "intimidation") {
              // Hide intimidation talisman effects
              return "";
            }
            // Add more effect types here as needed
            return `\n⚡ **\`${effectKey}: ${value}\`**`;
          })
          .filter((effect) => effect !== "") // Remove empty effects
          .join("");
      } else {
        const description = item.itemData.description
          ? `\n${item.itemData.description}`
          : "";
        statsDisplay = description;
      }
    }

    // Pad name and price for alignment
    const paddedName = item.name.padEnd(maxNameLen, " ");
    const paddedPrice = item.price.padStart(7, " ");

    // Add shop limits display if available
    let limitDisplay = "";
    if (shopLimits && item.itemKey in shopLimits) {
      const remaining = shopLimits[item.itemKey];
      if (remaining === 0) {
        limitDisplay = " `(Limit reached)`";
      } else {
        limitDisplay = ` \`(${remaining} left)\``;
      }
    }

    return `${item.itemEmoji} \`${paddedName} ${paddedPrice}\` ${CURRENCY.purseEmoji}${limitDisplay}${statsDisplay}`;
  });

  // get dynamic greeting with top player substitution
  const dynamicGreeting = await getNPCGreeting(npc.key);

  // Create the base embed
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${npc.emoji || "❓"} ${npc.name}'s Shop`)
    .setDescription(dynamicGreeting || "How can I help you?");

  // Split items into multiple fields if needed (max 1024 chars per field)
  const MAX_FIELD_LENGTH = 1000; // Leave buffer for Discord's 1024 limit
  let currentFieldItems = [];
  let currentFieldLength = 0;
  let fieldCount = 0;

  // Helper function to add a field to the embed
  const addField = (items) => {
    if (items.length === 0) return;

    fieldCount++;
    embed.addFields({
      name: fieldCount === 1 ? "Items for Sale" : "\u200B",
      value: items.join("\n"),
      inline: false,
    });
  };

  // Process each item display
  for (const itemDisplay of itemDisplays) {
    const itemLength = itemDisplay.length + 1; // +1 for newline

    // If adding this item would exceed the limit and we have items in the current field,
    // add the current field and start a new one
    if (
      currentFieldLength + itemLength > MAX_FIELD_LENGTH &&
      currentFieldItems.length > 0
    ) {
      addField(currentFieldItems, fieldCount === 0);
      currentFieldItems = [];
      currentFieldLength = 0;
    }

    // Add item to current field
    currentFieldItems.push(itemDisplay);
    currentFieldLength += itemLength;

    // If we've reached the maximum number of items per field (Discord limit is 25 fields per embed)
    if (fieldCount >= 20) {
      // Leave room for other fields like coins
      break;
    }
  }

  // Add any remaining items
  if (currentFieldItems.length > 0 && fieldCount < 20) {
    addField(currentFieldItems, fieldCount === 0);
  }

  // If we had to truncate items, add a note
  if (fieldCount >= 20) {
    const remainingItems =
      itemDisplays.length - (currentFieldItems.length + (fieldCount - 1) * 20);
    if (remainingItems > 0) {
      embed.addFields({
        name: "\u200B",
        value: `*...and ${remainingItems} more items*`,
        inline: false,
      });
    }
  }

  // Add coins field
  embed.addFields({
    name: "Your Coins",
    value: `${CURRENCY.purseEmoji} ${playerCoins.toLocaleString()}`,
    inline: false,
  });

  return { embed, hasDetails };
}

// --- Helper: Clean up menu message ---
/**
 * Safely edits a menu message, ignoring errors if the message is gone.
 * @param {Message} message - The Discord message to edit.
 * @param {string|undefined} content - The new content for the message.
 * @param {Array} components - The new components for the message.
 */
async function safeEditMenuMessage(
  message,
  content = undefined,
  components = []
) {
  try {
    await message.edit({ content, components });
  } catch {
    // Silently ignore if message is gone
  }
}

/**
 * Handles the buy quantity modal submission for all merchants, including Bee Pet logic.
 * @param {ModalSubmitInteraction} interaction
 * @param {object} npc
 * @param {object} allItems
 * @param {string} [beePetRarity]
 */
async function handleBuyQuantitySubmit(
  interaction,
  npc,
  allItems,
  beePetRarity
) {
  try {
    await interaction.deferReply(); // Remove ephemeral flag to match sell flow

    const fullId = interaction.customId;
    // Extract itemKey by removing 'buy_quantity_' and trailing _UUID
    let itemKey = fullId.replace("buy_quantity_", "").replace(/_[^_]+$/, "");
    // For Bee Pet purchases, always use 'BEE_PET' as the itemKey
    if (itemKey.startsWith("BEE_PET")) {
      itemKey = "BEE_PET";
    }

    const item = npc.shopInventory?.find((i) => i.itemKey === itemKey);
    const liveItemDetails = allItems[itemKey];

    if (!item || !liveItemDetails) {
      console.error(
        `[Talk Buy Submit] Failed to find Item/Details for key ${itemKey} from modal ${interaction.customId}`
      );
      return interaction.editReply({
        content: "Error retrieving item data for purchase.",
      });
    }
    const quantityInputStr = interaction.fields.getTextInputValue("quantity");
    const userId = interaction.user.id;
    // Only fetch currencies field since we only need coins for purchase check
    const character = await getPlayerData(userId);

    if (!character) {
      return interaction.editReply({
        content: "Could not load your character data to complete the purchase.",
      });
    }

    const playerCoins = character.coins || 0;
    let priceOverride = null;
    if (itemKey === "BEE_PET" && beePetRarity) {
      priceOverride = BEE_PET_RARITY_PRICES[beePetRarity];
    }
    const itemPrice = priceOverride || item.price;

    let quantityToBuy;

    if (quantityInputStr.toLowerCase() === "max") {
      if (itemPrice <= 0) {
        quantityToBuy = Infinity;
      } else {
        quantityToBuy = Math.floor(playerCoins / itemPrice);
      }
      if (quantityToBuy === Infinity) {
        quantityToBuy = 1000;
      }

      // check shop limits for "max" purchases to avoid exceeding daily limits
      const { getTimeState } = require("../bot.js");
      const timeState = getTimeState();
      const { getNPCShopLimits } = require("../utils/shopLimits");
      const shopLimits = await getNPCShopLimits(userId, npc, timeState);
      const remainingLimit = shopLimits[itemKey];

      if (remainingLimit !== undefined && remainingLimit !== -1) {
        // apply shop limit constraint to max purchase
        quantityToBuy = Math.min(quantityToBuy, remainingLimit);
      }
    } else {
      quantityToBuy = parseInt(quantityInputStr, 10);
    }

    if (isNaN(quantityToBuy) || quantityToBuy <= 0) {
      return interaction.editReply({
        content: 'Invalid quantity. Please enter a positive number or "max".',
      });
    }

    const totalCost = quantityToBuy * itemPrice;

    if (totalCost > playerCoins) {
      return interaction.editReply({
        content: `You don't have enough coins. You need ${totalCost} ${CURRENCY.name}, but you only have ${playerCoins}.`,
      });
    }

    // Check shop purchase limits
    const { getTimeState } = require("../bot.js");
    const timeState = getTimeState();
    const {
      checkPurchaseLimit,
      recordPurchase,
    } = require("../utils/shopLimits");

    const limitCheck = await checkPurchaseLimit(
      userId,
      npc.key,
      itemKey,
      quantityToBuy,
      npc,
      timeState
    );
    if (!limitCheck.canPurchase) {
      return interaction.editReply({
        content: limitCheck.message,
      });
    }

    let itemsToChange = [];
    const equipmentToAdd = [];
    const accessoriesToAdd = [];

    if (itemKey === "BEE_PET" && beePetRarity) {
      for (let i = 0; i < quantityToBuy; i++) {
        const newPet = createPetInstance(itemKey, beePetRarity);
        if (!newPet) {
          console.error(
            `[Talk Buy Submit - BEE_PET] createPetInstance returned null for ${itemKey}, rarity ${beePetRarity}.`
          );
          return interaction.editReply({
            content: "Failed to create pet instance. Please try again.",
          });
        }

        try {
          // Use the new atomic purchasePet function
          await purchasePet(
            userId,
            newPet,
            BEE_PET_RARITY_PRICES[beePetRarity]
          );
        } catch (error) {
          if (error.message === "Insufficient coins") {
            return interaction.editReply({
              content: `You don't have enough coins. You need ${BEE_PET_RARITY_PRICES[beePetRarity]} ${CURRENCY.name}.`,
            });
          }
          console.error(
            `[Talk Buy Submit - BEE_PET] Purchase failed for user ${userId}:`,
            error
          );
          return interaction.editReply({
            content: "Failed to purchase pet. Please try again.",
          });
        }
      }

      try {
        // Get fresh character data and recalculate stats
        const freshCharacter = await getPlayerData(userId);
        if (!freshCharacter) {
          return interaction.editReply({
            content:
              "Pet purchase successful, but there was an issue updating your stats. Please contact support.",
          });
        }

        await recalculateAndSaveStats(userId, freshCharacter);

        // Record the purchase in shop limits
        await recordPurchase(
          userId,
          npc.key,
          itemKey,
          quantityToBuy,
          timeState
        );

        // Create success embed like the sell flow
        const successEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setTitle("✅ Pet Purchased!")
          .setDescription(
            `Successfully purchased ${quantityToBuy} ${
              liveItemDetails.emoji || ""
            } **${
              beePetRarity.charAt(0) + beePetRarity.slice(1).toLowerCase()
            } Bee Pet** for ${totalCost.toLocaleString()} ${CURRENCY.name}.`
          )
          .setFooter({
            text: "Coins have been deducted from your purse.",
          });

        // Update the message to show success
        const replyMessage = await interaction.editReply({
          embeds: [successEmbed],
          components: [],
        });

        // Set a timeout to delete the message after 15 seconds
        setTimeout(async () => {
          try {
            await replyMessage.delete();
          } catch (error) {
            console.error(
              "[Talk Buy Submit - BEE_PET] Error deleting success message:",
              error
            );
            // Ignore errors if the message was already deleted
          }
        }, 15000); // 15 seconds
      } catch (error) {
        console.error(
          `[Talk Buy Submit - BEE_PET] Stats recalculation failed for ${userId}:`,
          error
        );
        return interaction.editReply({
          content:
            "Pet purchase successful, but there was an issue updating your stats. Please try again or contact support.",
        });
      }
      return;
    } else if (
      liveItemDetails.unique === true &&
      ["ARMOR", "WEAPON", "TOOL", "EQUIPMENT"].includes(liveItemDetails.type) &&
      liveItemDetails.subtype
    ) {
      for (let i = 0; i < quantityToBuy; i++) {
        equipmentToAdd.push({ itemKey });
      }
    } else if (liveItemDetails.type === "ACCESSORY") {
      for (let i = 0; i < quantityToBuy; i++) {
        accessoriesToAdd.push({ itemKey });
      }
    } else {
      itemsToChange = [{ itemKey, amount: quantityToBuy }];
    }

    // Enhanced security validation for merchant purchases
    if (totalCost <= 0) {
      throw new Error("Invalid purchase cost calculated");
    }
    if (quantityToBuy <= 0 || !Number.isInteger(quantityToBuy)) {
      throw new Error("Invalid quantity for purchase");
    }

    // Prevent extremely large purchases that could indicate bugs
    const MAX_PURCHASE_QUANTITY = 1000000000;
    const MAX_PURCHASE_COST = 1000000000; // 1 billion coins
    if (quantityToBuy > MAX_PURCHASE_QUANTITY) {
      throw new Error(
        `Purchase quantity ${quantityToBuy} exceeds maximum allowed (${MAX_PURCHASE_QUANTITY})`
      );
    }
    if (totalCost > MAX_PURCHASE_COST) {
      throw new Error(
        `Purchase cost ${totalCost} exceeds maximum allowed (${MAX_PURCHASE_COST})`
      );
    }

    // Get current player data to validate they have sufficient funds
    const currentPlayerData = await getPlayerData(userId);
    if (!currentPlayerData) {
      throw new Error("Unable to retrieve player data");
    }

    const currentCoins = currentPlayerData.coins || 0;
    if (currentCoins < totalCost) {
      throw new Error(
        `Insufficient funds: have ${currentCoins} coins, need ${totalCost} coins`
      );
    }

    // Log large purchases for audit purposes
    if (totalCost > 100000 || quantityToBuy > 1000) {
      // Large purchase detected - no logging to console
    }

    const cost = -totalCost;

    try {
      await updateInventoryAtomically(
        userId,
        cost,
        itemsToChange,
        equipmentToAdd,
        [], // equipmentIdsToRemove
        0, // bankCoinsToAdd
        [], // equipmentDataUpdates
        null, // islandJsonString
        null, // collectionsJsonString
        false, // useExistingTransaction
        accessoriesToAdd
      );

      // Record the purchase in shop limits
      await recordPurchase(userId, npc.key, itemKey, quantityToBuy, timeState);

      // Create success embed (matching sell flow style)
      const itemDisplayName = beePetRarity
        ? `${liveItemDetails.name} (${
            beePetRarity.charAt(0) + beePetRarity.slice(1).toLowerCase()
          })`
        : liveItemDetails.name;

      const successEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GREEN)
        .setTitle("Item Purchased")
        .setDescription(
          `Successfully purchased ${quantityToBuy.toLocaleString()} ${
            liveItemDetails.emoji || ""
          } **${itemDisplayName}** for ${totalCost.toLocaleString()} ${
            CURRENCY.name
          }.`
        )
        .setFooter({
          text: "Coins have been deducted from your purse.",
        });

      // Update the message to show success
      const replyMessage = await interaction.editReply({
        embeds: [successEmbed],
        components: [],
      });

      // Set a timeout to delete the message after 15 seconds
      setTimeout(async () => {
        try {
          await replyMessage.delete();
        } catch (error) {
          console.error(
            "[HandleBuyQuantitySubmit] Error deleting success message:",
            error
          );
          // Ignore errors if the message was already deleted
        }
      }, 15000); // 15 seconds

      // Refresh both the shop menu and sell menu with updated data (matching sell flow)
      await refreshShopAndSellMenus(interaction, userId, npc, allItems);
    } catch (updateError) {
      console.error(
        `[Talk Buy Submit] Failed inventory update for ${itemKey} x${quantityToBuy} for user ${userId}:`,
        updateError
      );
      return interaction.editReply({
        content: `Inventory update failed: ${
          updateError.message || "Unknown error"
        }`,
      });
    }
  } catch (error) {
    console.error(
      `Error in handleBuyQuantitySubmit (within talk.js) for ${interaction.customId}:`,
      error
    );
    try {
      if (interaction.deferred || interaction.replied) {
        await interaction
          .editReply({
            content: "An error occurred while processing your purchase.",
          })
          .catch(() => {});
      } else {
        await interaction
          .reply({
            content: "An error occurred while processing your purchase.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    } catch (e) {
      console.error("Error handling error in handleBuyQuantitySubmit:", e);
    }
  }
}

/**
 * Executes the buy flow for a merchant, including Bee Pet special case.
 * @param {ButtonInteraction} buttonInteraction
 * @param {object} npc
 * @param {object} allItems
 */
async function executeBuyFlow(buttonInteraction, npc, allItems) {
  const userId = buttonInteraction.user.id;
  try {
    // Use allItems passed in, don't call configManager.getAllItems() again
    const liveItems = allItems;
    const npcShopItems = npc.shopInventory || [];
    if (npcShopItems.length === 0) {
      await buttonInteraction.reply({
        content: "This merchant isn't selling anything right now.",
        ephemeral: true,
      });
      return;
    }

    // Check if we need to show a modal (single item case)
    const needsModal = npcShopItems.length === 1;

    if (!needsModal) {
      await buttonInteraction.deferUpdate();
    }

    // --- Generalized Dropdown for All Merchants ---
    // Special case for Bea: Bee Pet rarity dropdown
    if (npc.key === "BEA") {
      const beePet = liveItems["BEE_PET"];
      if (!beePet) {
        if (needsModal) {
          await buttonInteraction.reply({
            content: "Bee Pet data missing!",
            ephemeral: true,
          });
        } else {
          await buttonInteraction.followUp({
            content: "Bee Pet data missing!",
          });
        }
        return;
      }

      // Create a select menu for bee pet rarities
      const menuId = `buy_bee_pet_rarity_menu_${uuidv4()}`;
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(menuId)
        .setPlaceholder("Select Bee Pet rarity to buy")
        .addOptions(
          BEE_PET_RARITIES.map((rarity) => ({
            label: `Bee Pet (${
              rarity.charAt(0) + rarity.slice(1).toLowerCase()
            })`,
            description: `Price: ${BEE_PET_RARITY_PRICES[
              rarity
            ].toLocaleString()} ${CURRENCY.name}`,
            value: rarity,
            emoji: beePet.emoji || "❓",
          }))
        );

      const menuMessage = needsModal
        ? await buttonInteraction
            .reply({
              content: "Select the Bee Pet rarity you want to buy:",
              components: [new ActionRowBuilder().addComponents(selectMenu)],
              ephemeral: false,
            })
            .then(() => buttonInteraction.fetchReply())
        : await buttonInteraction.followUp({
            content: "Select the Bee Pet rarity you want to buy:",
            components: [new ActionRowBuilder().addComponents(selectMenu)],
          });

      // Store the buy menu message ID for this user
      buyMenuMessageIds.set(userId, menuMessage.id);

      // Use a collector instead of awaitMessageComponent to match Fish Merchant pattern
      const collectorFilter = (i) => {
        return i.customId === menuId && i.user.id === userId;
      };
      const collector = menuMessage.createMessageComponentCollector({
        filter: collectorFilter,
        componentType: ComponentType.StringSelect,
        time: 60000,
      });

      collector.on("collect", async (menuInteraction) => {
        try {
          // Get the selected rarity
          const selectedRarity = menuInteraction.values[0];

          // Defer the reply to give us time to process
          await menuInteraction.deferReply();

          // Get player data to check coins
          const character = await getPlayerData(userId);
          if (!character) {
            return menuInteraction.editReply({
              content:
                "Could not load your character data to complete the purchase.",
            });
          }

          const playerCoins = character.coins || 0;
          const petPrice = BEE_PET_RARITY_PRICES[selectedRarity];

          // Check if player has enough coins
          if (petPrice > playerCoins) {
            return menuInteraction.editReply({
              content: `You don't have enough coins. You need ${petPrice.toLocaleString()} ${CURRENCY.name}, but you only have ${playerCoins.toLocaleString()}.`,
            });
          }

          // Create the pet instance
          const newPet = createPetInstance("BEE_PET", selectedRarity);
          if (!newPet) {
            console.error(
              `[ExecuteBuyFlow-Bea] createPetInstance returned null for BEE_PET, rarity ${selectedRarity}.`
            );
            return menuInteraction.editReply({
              content: "Failed to create pet instance. Please try again.",
            });
          }

          try {
            // Use the atomic purchasePet function
            await purchasePet(userId, newPet, petPrice);

            // Get fresh character data and recalculate stats
            const freshCharacter = await getPlayerData(userId);
            if (freshCharacter) {
              await recalculateAndSaveStats(userId, freshCharacter);
            }

            // Create success embed
            const successEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.GREEN)
              .setTitle("✅ Pet Purchased!")
              .setDescription(
                `Successfully purchased ${beePet.emoji || "🐝"} **${selectedRarity.charAt(0) + selectedRarity.slice(1).toLowerCase()} Bee Pet** for ${petPrice.toLocaleString()} ${CURRENCY.name}.`
              )
              .setFooter({
                text: "Coins have been deducted from your purse.",
              });

            // Update the message to show success
            const replyMessage = await menuInteraction.editReply({
              embeds: [successEmbed],
              components: [],
            });

            // Set a timeout to delete the message after 15 seconds
            setTimeout(async () => {
              try {
                await replyMessage.delete();
              } catch (error) {
                console.error(
                  "[ExecuteBuyFlow-Bea] Error deleting success message:",
                  error
                );
              }
            }, 15000);
          } catch (error) {
            if (error.message === "Insufficient coins") {
              return menuInteraction.editReply({
                content: `You don't have enough coins. You need ${petPrice.toLocaleString()} ${CURRENCY.name}.`,
              });
            }
            console.error(
              `[ExecuteBuyFlow-Bea] Purchase failed for user ${userId}:`,
              error
            );
            return menuInteraction.editReply({
              content: "Failed to purchase pet. Please try again.",
            });
          }

          // Refresh both the shop menu and buy menu with updated data
          await refreshShopAndSellMenus(
            buttonInteraction,
            userId,
            npc,
            allItems
          );
        } catch (error) {
          console.error(
            "[ExecuteBuyFlow-Bea] Error in bee pet menu collector:",
            error
          );
          try {
            if (!menuInteraction.replied && !menuInteraction.deferred) {
              await menuInteraction.reply({
                content:
                  "An error occurred during the Bee Pet purchase process.",
              }); // Remove ephemeral flag
            } else {
              await menuInteraction.followUp({
                content:
                  "An error occurred during the Bee Pet purchase process.",
              }); // Remove ephemeral flag
            }
          } catch (e) {
            console.error(
              "[ExecuteBuyFlow-Bea] Failed to send error message:",
              e
            );
          }
        }
      });

      collector.on("end", (collected, reason) => {
        if (reason === "time") {
          safeEditMenuMessage(
            menuMessage,
            "Bee Pet rarity selection timed out."
          );
        }
      });

      return;
    }
    // --- Generalized for all other merchants ---
    // If only one item, skip dropdown
    if (npcShopItems.length === 1) {
      const itemInfo = npcShopItems[0];
      const itemDetails = liveItems[itemInfo.itemKey];
      if (!itemDetails) {
        await buttonInteraction.reply({
          content: "Item data missing!",
          ephemeral: true,
        });
        return;
      }
      const modalCustomId = `buy_quantity_${itemInfo.itemKey}_${uuidv4()}`;
      const modal = new ModalBuilder()
        .setCustomId(modalCustomId)
        .setTitle(`Buy ${itemDetails.name}`)
        .addComponents(
          new ActionRowBuilder().addComponents(
            new TextInputBuilder()
              .setCustomId("quantity")
              .setLabel(
                `How many? (${itemInfo.price.toLocaleString()} coins each)`
              )
              .setStyle(TextInputStyle.Short)
              .setRequired(true)
              .setPlaceholder('Enter quantity or "max"')
          )
        );
      await buttonInteraction.showModal(modal);
      const modalFilter = (mi) =>
        mi.customId === modalCustomId && mi.user.id === userId;
      const modalInteraction = await buttonInteraction
        .awaitModalSubmit({ filter: modalFilter, time: 60000 })
        .catch(() => {
          buttonInteraction
            .editReply({
              content: "Buy quantity input timed out.",
              components: [],
            })
            .catch(() => {});
          return null;
        });
      if (!modalInteraction) return;
      await handleBuyQuantitySubmit(modalInteraction, npc, allItems);
      return;
    }
    // Otherwise, show dropdown for all items
    const menuId = `buy_item_menu_${uuidv4()}`;
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(menuId)
      .setPlaceholder("Select an item to buy")
      .addOptions(
        npcShopItems.map((itemInfo) => {
          const itemDetails = liveItems[itemInfo.itemKey];
          let description = `Price: ${
            itemInfo.price?.toLocaleString() || "?"
          } coins`;

          // Add stats to description if item has baseStats
          if (
            itemDetails?.baseStats &&
            Object.keys(itemDetails.baseStats).length > 0
          ) {
            const statsText = Object.entries(itemDetails.baseStats)
              .map(([stat, value]) => {
                const formattedStat = stat
                  .replace(/_/g, " ")
                  .toLowerCase()
                  .replace(/\b\w/g, (l) => l.toUpperCase());
                return `+${value} ${formattedStat}`;
              })
              .join(", ");
            description += ` | ${statsText}`;
          }

          return {
            label: itemDetails?.name || itemInfo.itemKey,
            description: description,
            value: itemInfo.itemKey,
            emoji: itemDetails?.emoji || "❓",
          };
        })
      );

    const menuMessage = await buttonInteraction.followUp({
      content: "Select the item you want to buy:",
      components: [new ActionRowBuilder().addComponents(selectMenu)],
      // Remove ephemeral flag to match sell flow
    });

    // Store the buy menu message ID for this user
    buyMenuMessageIds.set(userId, menuMessage.id);

    // Use a collector instead of awaitMessageComponent to avoid conflicts with main bot handler
    const collectorFilter = (i) =>
      i.customId === menuId && i.user.id === userId;
    const collector = menuMessage.createMessageComponentCollector({
      filter: collectorFilter,
      componentType: ComponentType.StringSelect,
      time: 60000,
    });

    collector.on("collect", async (menuInteraction) => {
      try {
        const selectedItemKey = menuInteraction.values[0];
        const selectedItemInfo = npcShopItems.find(
          (i) => i.itemKey === selectedItemKey
        );
        const selectedItemDetails = liveItems[selectedItemKey];

        if (!selectedItemInfo || !selectedItemDetails) {
          return menuInteraction.reply({
            content: "Item data missing!",
            ephemeral: true,
          });
        }

        const modalCustomId = `buy_quantity_${selectedItemKey}_${uuidv4()}`;
        const modal = new ModalBuilder()
          .setCustomId(modalCustomId)
          .setTitle(`Buy ${selectedItemDetails.name}`)
          .addComponents(
            new ActionRowBuilder().addComponents(
              new TextInputBuilder()
                .setCustomId("quantity")
                .setLabel(
                  `How many? (${selectedItemInfo.price.toLocaleString()} coins each)`
                )
                .setStyle(TextInputStyle.Short)
                .setRequired(true)
                .setPlaceholder('Enter quantity or "max"')
            )
          );

        await menuInteraction.showModal(modal);

        const modalFilter = (mi) =>
          mi.customId === modalCustomId && mi.user.id === userId;
        const modalInteraction = await menuInteraction
          .awaitModalSubmit({ filter: modalFilter, time: 60000 })
          .catch(() => {
            safeEditMenuMessage(
              menuMessage,
              "Buy quantity input timed out. Item selection has expired."
            );
            return null;
          });

        if (!modalInteraction) return;

        await handleBuyQuantitySubmit(modalInteraction, npc, allItems);

        // Don't clear the menu - it will be refreshed by refreshShopAndSellMenus
        // The collector continues running so users can make more purchases
      } catch (error) {
        console.error("[ExecuteBuyFlow] Error in buy menu collector:", error);
        try {
          if (!menuInteraction.replied && !menuInteraction.deferred) {
            await menuInteraction.reply({
              content: "An error occurred during the buy process.",
              ephemeral: true,
            });
          } else {
            await menuInteraction.followUp({
              content: "An error occurred during the buy process.",
              ephemeral: true,
            });
          }
        } catch (e) {
          console.error("[ExecuteBuyFlow] Failed to send error message:", e);
        }
      }
    });

    collector.on("end", (collected, reason) => {
      if (reason === "time") {
        safeEditMenuMessage(menuMessage, "Item selection timed out.");
      }
    });

    return;
  } catch (error) {
    console.error(
      `[ExecuteBuyFlow] Error during buy flow for user ${userId}:`,
      error
    );
    try {
      if (!buttonInteraction.replied && !buttonInteraction.deferred) {
        await buttonInteraction
          .reply({
            content: "An error occurred during the buy process.",
            ephemeral: true,
          })
          .catch(() => {});
      } else {
        await buttonInteraction
          .followUp({
            content: "An error occurred during the buy process.",
            ephemeral: true,
          })
          .catch(() => {});
      }
    } catch (e) {
      console.error("[ExecuteBuyFlow] Failed to send error follow-up:", e);
    }
  }
}

/**
 * Executes the sell flow for a merchant.
 * @param {ButtonInteraction} buttonInteraction
 * @param {object} npc
 * @param {object} allItems
 */
async function executeSellFlow(buttonInteraction, npc, allItems) {
  const userId = buttonInteraction.user.id;
  let currentPage = 0; // Start at page 0
  let menuMessage = null; // To store the message with the menu and buttons    let currentMenuId = null;

  try {
    // Only fetch inventory as that's all we need for selling
    const character = await getPlayerData(userId);
    if (!character) {
      return buttonInteraction.followUp({
        content: "Could not load your character data to initiate sell.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (
      !character.inventory ||
      !character.inventory.items ||
      Object.keys(character.inventory.items).length === 0
    ) {
      return buttonInteraction.followUp({
        content: "Your inventory is empty.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    const sellableItems = Object.entries(character.inventory.items)
      .map(([itemKey, quantity]) => {
        const itemDetails = allItems[itemKey];
        // Create a composite object for easier access
        return {
          itemKey,
          quantity,
          itemDetails,
          itemName: itemDetails?.name || itemKey,
          sellPrice: itemDetails?.sellPrice || 0,
          emoji: itemDetails?.emoji || "❓",
        };
      })
      .filter(
        ({ itemDetails, quantity }) =>
          itemDetails &&
          itemDetails.sellable &&
          itemDetails.sellPrice > 0 &&
          quantity > 0
      )
      // Sort alphabetically by item name for consistency across pages
      .sort((a, b) => a.itemName.localeCompare(b.itemName));

    if (sellableItems.length === 0) {
      return buttonInteraction.followUp({
        content: "You have no sellable items.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    const itemsPerPage = 25;
    const totalPages = Math.ceil(sellableItems.length / itemsPerPage);

    // Function to generate components for the current page
    const generateComponents = (pageIndex) => {
      const startIndex = pageIndex * itemsPerPage;
      const endIndex = Math.min(
        startIndex + itemsPerPage,
        sellableItems.length
      );
      const currentItems = sellableItems.slice(startIndex, endIndex);

      const options = currentItems.map(
        ({ itemKey, itemName, quantity, sellPrice, emoji }) => ({
          label: `${itemName} (x${quantity})`,
          description: `Sell Price: ${sellPrice} ${CURRENCY.name} each`,
          value: itemKey,
          emoji: emoji,
        })
      );

      // Use fixed IDs without UUID to make collector consistent
      const menuId = SELL_ITEM_MENU_ID;
      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(menuId)
        .setPlaceholder(
          `Select item to sell (Page ${pageIndex + 1}/${totalPages})`
        )
        .addOptions(options);

      const prevButtonId = SELL_PREV_PAGE_ID;
      const nextButtonId = SELL_NEXT_PAGE_ID;

      const rowButtons = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId(prevButtonId)
          .setLabel("Previous")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("⬅️")
          .setDisabled(pageIndex === 0), // Disable on first page
        new ButtonBuilder()
          .setCustomId(nextButtonId)
          .setLabel("Next")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("➡️")
          .setDisabled(pageIndex >= totalPages - 1) // Disable on last page
      );

      // Set current IDs to be used by the collector filter function

      return {
        components: [
          new ActionRowBuilder().addComponents(selectMenu),
          rowButtons,
        ],
        menuId,
        prevButtonId,
        nextButtonId,
      };
    };

    // Initial component generation
    let currentComponentsInfo = generateComponents(currentPage);

    // Create embed for the sell menu
    const sellEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle(`${npc.emoji || "💰"} Sell Items to ${npc.name}`)
      .setDescription("Select an item from your inventory to sell.")
      .setFooter({
        text: `Page ${currentPage + 1}/${totalPages} • ${
          sellableItems.length
        } items available`,
      });

    // Send the initial message
    menuMessage = await buttonInteraction.followUp({
      embeds: [sellEmbed],
      components: currentComponentsInfo.components,
    });

    // Store the sell menu message ID for this user
    sellMenuMessageIds.set(userId, menuMessage.id);

    // Create a dynamic filter function that checks against the current component IDs
    const collectorFilter = (i) => {
      return (
        (i.customId === SELL_ITEM_MENU_ID ||
          i.customId === SELL_PREV_PAGE_ID ||
          i.customId === SELL_NEXT_PAGE_ID) &&
        i.user.id === userId
      );
    };

    // Use longer timeout for pagination
    const collector = menuMessage.createMessageComponentCollector({
      filter: collectorFilter,
      time: 300000,
    });

    collector.on("collect", async (i) => {
      try {
        // --- Handle Pagination ---
        if (i.customId === SELL_PREV_PAGE_ID) {
          await i.deferUpdate(); // Acknowledge the button press
          currentPage--;
          currentComponentsInfo = generateComponents(currentPage); // Regenerate components for the new page

          // Update the embed for the new page
          const updatedEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle(`${npc.emoji || "💰"} Sell Items to ${npc.name}`)
            .setDescription("Select an item from your inventory to sell.")
            .setFooter({
              text: `Page ${currentPage + 1}/${totalPages} • ${
                sellableItems.length
              } items available`,
            });

          try {
            await i.editReply({
              embeds: [updatedEmbed],
              components: currentComponentsInfo.components,
            });
          } catch (editError) {
            console.error(
              `[ExecuteSellFlow] Error updating page to ${currentPage + 1}:`,
              editError
            );
            // If we can't update the message, we'll try to follow up with a new message
            await i
              .followUp({
                content: "Error updating page. Please try again.",
                flags: [MessageFlags.Ephemeral],
              })
              .catch(() => {});
          }
          return; // Wait for next interaction
        }

        if (i.customId === SELL_NEXT_PAGE_ID) {
          await i.deferUpdate(); // Acknowledge the button press
          currentPage++;
          currentComponentsInfo = generateComponents(currentPage); // Regenerate components for the new page

          // Update the embed for the new page
          const updatedEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle(`${npc.emoji || "💰"} Sell Items to ${npc.name}`)
            .setDescription("Select an item from your inventory to sell.")
            .setFooter({
              text: `Page ${currentPage + 1}/${totalPages} • ${
                sellableItems.length
              } items available`,
            });

          try {
            await i.editReply({
              embeds: [updatedEmbed],
              components: currentComponentsInfo.components,
            });
          } catch (editError) {
            console.error(
              `[ExecuteSellFlow] Error updating page to ${currentPage + 1}:`,
              editError
            );
            // If we can't update the message, we'll try to follow up with a new message
            await i
              .followUp({
                content: "Error updating page. Please try again.",
                flags: [MessageFlags.Ephemeral],
              })
              .catch(() => {});
          }
          return; // Wait for next interaction
        }

        // --- Handle Item Selection ---
        if (i.customId === SELL_ITEM_MENU_ID && i.isStringSelectMenu()) {
          const menuInteraction = i; // This is our item selection interaction
          const selectedItemKey = menuInteraction.values[0];
          // Fetch fresh character data to get current quantity before showing modal
          const freshCharacterData = await getPlayerData(userId);
          const currentQuantity =
            freshCharacterData?.inventory?.items?.[selectedItemKey] || 0;
          const itemDetails = allItems[selectedItemKey];

          if (
            !itemDetails ||
            !itemDetails.sellable ||
            itemDetails.sellPrice <= 0
          ) {
            console.error(
              `[ExecuteSellFlow] Failed to find sellable item data for ${selectedItemKey}.`
            );
            await menuInteraction.update({
              content:
                "Error finding item details or item is no longer sellable.",
              components: [],
            });
            return;
          }

          const itemName = itemDetails.name || selectedItemKey;

          if (currentQuantity <= 0) {
            await menuInteraction.update({
              content: `You no longer have any ${
                itemDetails.emoji || ""
              } \`${itemName}\` to sell.`,
              components: [],
            });
            return;
          }

          // Create and show the modal directly (bank style)
          const modalCustomId = `sell_quantity_${selectedItemKey}_${uuidv4()}`;
          const modal = new ModalBuilder()
            .setCustomId(modalCustomId)
            .setTitle(`Sell ${itemName}`);

          const quantityInput = new TextInputBuilder()
            .setCustomId("sell_amount")
            .setLabel(
              `Amount (Max: ${currentQuantity}, Price: ${itemDetails.sellPrice} each)`
            )
            .setStyle(TextInputStyle.Short)
            .setPlaceholder("Enter quantity (e.g. 10, 1k, all)")
            .setRequired(true);

          modal.addComponents(
            new ActionRowBuilder().addComponents(quantityInput)
          );

          // Just show the modal and let the global handler in bot.js handle the submission
          await menuInteraction.showModal(modal);

          // No need to await for the modal submission here
          // The global handleSellQuantityModal function in bot.js will handle it

          // Log that we've delegated this to the global handler
          console.log(
            `[ExecuteSellFlow] Showing sell modal for item ${selectedItemKey}, letting global handler process submission`
          );

          // Don't refresh shop/sell menus here - the global handler will do that
        }
      } catch (error) {
        console.error(
          "[ExecuteSellFlow] Error in collector interaction:",
          error
        );
        try {
          await i
            .followUp({
              content: "An error occurred. Please try again.",
              flags: [MessageFlags.Ephemeral],
            })
            .catch((followUpError) => {
              console.error(
                "[Talk Command] Error in follow-up message:",
                followUpError
              );
            });
        } catch (e) {
          console.error("[Talk Command] Error in error handling:", e);
        }
      }
    });

    collector.on("end", () => {
      if (menuMessage) {
        safeEditMenuMessage(menuMessage, undefined, []);
      }
    });
  } catch (error) {
    console.error(
      `[ExecuteSellFlow] Top-level error during sell flow for user ${userId}:`,
      error
    );
    // Attempt to inform the user via the initial button interaction
    try {
      // Check state of initial button interaction
      if (buttonInteraction.replied || buttonInteraction.deferred) {
        await buttonInteraction
          .followUp({
            content: "An error occurred starting the sell process.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    } catch (e) {
      console.error("[ExecuteSellFlow] Failed to send error follow-up:", e);
    }
  }
}

/**
 * Refreshes the shop menu, sell menu, and buy menu with updated data
 * @param {ButtonInteraction} interaction - The interaction
 * @param {string} userId - The user's Discord ID
 * @param {object} npc - The NPC object
 * @param {object} allItems - All items from config
 * @param {number} currentSellPage - The current page in the sell menu (defaults to 0)
 */
async function refreshShopAndSellMenus(
  interaction,
  userId,
  npc,
  allItems,
  currentSellPage = 0
) {
  try {
    const channel = interaction.channel;

    // Get fresh character data to ensure we have the latest inventory and coins
    const freshCharacter = await getPlayerData(userId);
    if (!freshCharacter) {
      console.error(
        "[RefreshShopAndSellMenus] Failed to get fresh character data for refresh"
      );
      return;
    }

    const playerCoins = freshCharacter.coins || 0;

    // Get shop limits for this NPC
    const timeState = bot.getTimeState ? bot.getTimeState() : null;
    let shopLimits = null;
    if (timeState) {
      const { getNPCShopLimits } = require("../utils/shopLimits");
      shopLimits = await getNPCShopLimits(userId, npc, timeState);
    }

    // STEP 1: Update the original shop message
    const shopMessageId = shopMessageIds.get(userId);
    if (shopMessageId) {
      try {
        const shopMessage = await channel.messages.fetch(shopMessageId);

        // Recreate the shop embed with updated coins
        let embed;
        let buyingOptions;

        if (npc.key === "BEA") {
          // Special handling for Bea's Bee Pet shop
          const beePet = allItems["BEE_PET"];
          if (beePet) {
            const names = BEE_PET_RARITIES.map(
              (rarity) =>
                `Bee Pet (${rarity.charAt(0) + rarity.slice(1).toLowerCase()})`
            );
            const maxNameLen = Math.max(...names.map((n) => n.length));
            buyingOptions = BEE_PET_RARITIES.map((rarity) => {
              const name = `Bee Pet (${rarity.charAt(0) + rarity.slice(1).toLowerCase()})`;
              const price = BEE_PET_RARITY_PRICES[rarity].toLocaleString();
              const paddedName = name.padEnd(maxNameLen, " ");
              const paddedPrice = price.padStart(7, " ");

              // Add shop limits for bee pets
              let limitDisplay = "";
              if (shopLimits && `BEE_PET_${rarity}` in shopLimits) {
                const remaining = shopLimits[`BEE_PET_${rarity}`];
                if (remaining === 0) {
                  limitDisplay = " `(Limit reached)`";
                } else {
                  limitDisplay = ` \`(${remaining} left)\``;
                }
              }

              return `${
                beePet.emoji || "❓"
              } \`${paddedName} ${paddedPrice}\` ${CURRENCY.purseEmoji}${limitDisplay}`;
            }).join("\n");
          } else {
            buyingOptions = "Bee Pet data missing!";
          }
          const dynamicGreeting = await getNPCGreeting(npc.key);
          embed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle(`${npc.emoji || "❓"} ${npc.name}'s Shop`)
            .setDescription(
              dynamicGreeting +
                "\n\n*Select Bee Pet to choose rarity and price.*"
            )
            .addFields({
              name: "Items for Sale",
              value: buyingOptions,
              inline: false,
            });
        } else {
          // Use standard generator for all other merchants (no details by default)
          const shopData = await generateShopDisplay(
            npc,
            allItems,
            playerCoins,
            false,
            shopLimits
          );
          embed = shopData.embed;
        }

        // Add coins field only if not already present (generateShopDisplay may include it)
        if (!embed.data.fields?.some((f) => f.name === "Your Coins")) {
          embed.addFields({
            name: "Your Coins",
            value: `${CURRENCY.purseEmoji} ${playerCoins.toLocaleString()}`,
            inline: false,
          });
        }

        // Update the shop message with fresh data
        await shopMessage.edit({
          embeds: [embed],
          components: shopMessage.components, // Keep the same components
        });
      } catch (error) {
        console.error(
          `[RefreshShopAndSellMenus] Error updating shop message with ID ${shopMessageId}:`,
          error
        );
        // Continue even if updating the shop message fails
      }
    }

    // STEP 2: Update the sell menu message
    const sellMenuMessageId = sellMenuMessageIds.get(userId);
    if (sellMenuMessageId) {
      try {
        const sellMenuMessage = await channel.messages.fetch(sellMenuMessageId);

        // Get updated sellable items
        const updatedSellableItems = Object.entries(
          freshCharacter?.inventory?.items || {}
        )
          .map(([itemKey, quantity]) => {
            const itemDetails = allItems[itemKey];
            return {
              itemKey,
              quantity,
              itemDetails,
              itemName: itemDetails?.name || itemKey,
              sellPrice: itemDetails?.sellPrice || 0,
              emoji: itemDetails?.emoji || "❓",
            };
          })
          .filter(
            ({ itemDetails, quantity }) =>
              itemDetails &&
              itemDetails.sellable &&
              itemDetails.sellPrice > 0 &&
              quantity > 0
          )
          .sort((a, b) => a.itemName.localeCompare(b.itemName));

        // Check if there are any items left to sell
        if (updatedSellableItems.length === 0) {
          // Create an empty sell menu embed
          const emptySellEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle(`${npc.emoji || "💰"} Sell Items to ${npc.name}`)
            .setDescription("You have no sellable items in your inventory.");

          // Update the sell menu message with no components
          await sellMenuMessage.edit({
            embeds: [emptySellEmbed],
            components: [],
          });
          return; // Exit early since there's nothing else to do
        }

        // Recreate the sell menu with updated items
        const itemsPerPage = 25;
        const totalPages = Math.ceil(
          updatedSellableItems.length / itemsPerPage
        );
        // Use the passed currentSellPage instead of resetting to 0
        let currentPage = currentSellPage;

        // Make sure currentPage is valid for the new total pages
        if (currentPage >= totalPages) {
          currentPage = Math.max(0, totalPages - 1);
        }

        // Generate components for the current page
        const generateComponents = (pageIndex) => {
          const startIndex = pageIndex * itemsPerPage;
          const endIndex = Math.min(
            startIndex + itemsPerPage,
            updatedSellableItems.length
          );
          const currentItems = updatedSellableItems.slice(startIndex, endIndex);

          const options = currentItems.map(
            ({ itemKey, itemName, quantity, sellPrice, emoji }) => ({
              label: `${itemName} (x${quantity})`,
              description: `Sell Price: ${sellPrice} ${CURRENCY.name} each`,
              value: itemKey,
              emoji: emoji,
            })
          );

          const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(SELL_ITEM_MENU_ID)
            .setPlaceholder(
              `Select item to sell (Page ${pageIndex + 1}/${totalPages})`
            )
            .addOptions(options);

          const rowButtons = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId(SELL_PREV_PAGE_ID)
              .setLabel("Previous")
              .setStyle(ButtonStyle.Secondary)
              .setEmoji("⬅️")
              .setDisabled(pageIndex === 0),
            new ButtonBuilder()
              .setCustomId(SELL_NEXT_PAGE_ID)
              .setLabel("Next")
              .setStyle(ButtonStyle.Secondary)
              .setEmoji("➡️")
              .setDisabled(pageIndex >= totalPages - 1)
          );

          return [new ActionRowBuilder().addComponents(selectMenu), rowButtons];
        };

        // Create updated embed
        const updatedSellEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.BLUE)
          .setTitle(`${npc.emoji || "💰"} Sell Items to ${npc.name}`)
          .setDescription("Select an item from your inventory to sell.")
          .setFooter({
            text: `Page ${currentPage + 1}/${totalPages} • ${
              updatedSellableItems.length
            } items available • Updated inventory`,
          });

        // Update the sell menu message with refreshed data
        const components = generateComponents(currentPage);
        await sellMenuMessage.edit({
          embeds: [updatedSellEmbed],
          components,
        });
      } catch (error) {
        console.error(
          `[RefreshShopAndSellMenus] Error updating sell menu message with ID ${sellMenuMessageId}:`,
          error
        );
        // Continue even if updating the sell menu fails
      }
    }

    // STEP 3: Update the buy menu message (if it exists)
    const buyMenuMessageId = buyMenuMessageIds.get(userId);
    if (buyMenuMessageId) {
      try {
        const buyMenuMessage = await channel.messages.fetch(buyMenuMessageId);

        // Keep the buy menu content simple and clean
        await buyMenuMessage.edit({
          content: "Select the item you want to buy:",
          components: buyMenuMessage.components, // Keep the same components
        });
      } catch (error) {
        console.error(
          `[RefreshShopAndSellMenus] Error updating buy menu message with ID ${buyMenuMessageId}:`,
          error
        );
        // Continue even if updating the buy menu fails
      }
    }
  } catch (error) {
    console.error(
      "[RefreshShopAndSellMenus] Error in refresh function:",
      error
    );
  }
}

/**
 * Handles the Kat NPC's pet upgrade interaction.
 * @param {CommandInteraction} interaction
 * @param {object} character
 * @param {object} katNPC
 * @param {object} allItems
 */
async function handleKatPetUpgradeInteraction(
  interaction,
  character,
  katNPC,
  allItems
) {
  try {
    const userId = interaction.user.id;

    // Check if player already has a pet being upgraded - use direct database check
    const upgradeStatus = await checkPetUpgradeStatus(userId);

    // Get the latest character data with pets and currencies fields
    const fullCharacter = await getPlayerData(userId);

    // Silent upgrade status check

    // Create the embed for Kat's message
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PINK) // Pink color for Kat
      .setTitle(`${katNPC.emoji || "🐱"} ${katNPC.name}`);

    // Handle different scenarios based on upgrade status
    if (upgradeStatus.upgrading) {
      // Get pet information for the pet being upgraded
      const petInfo =
        allItems[
          upgradeStatus.pet_upgrade_json?.petKey ||
            upgradeStatus.petUpgrade?.petKey
        ];
      const originalRarityInfo =
        ITEM_RARITY[
          upgradeStatus.pet_upgrade_json?.originalRarity ||
            upgradeStatus.petUpgrade?.originalRarity
        ];
      const targetRarityInfo =
        ITEM_RARITY[
          upgradeStatus.pet_upgrade_json?.targetRarity ||
            upgradeStatus.petUpgrade?.targetRarity
        ];

      if (!petInfo || !originalRarityInfo || !targetRarityInfo) {
        console.error(
          "[Kat NPC] Missing pet info for upgrade:",
          upgradeStatus.pet_upgrade_json
        );
        return interaction.reply({
          content:
            "An error occurred while retrieving pet information. Please try again later.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Determine if the pet is ready for pickup
      if (upgradeStatus.isComplete) {
        // Pet is ready for pickup
        const dialogueKey = "pet_ready";
        const npcDialogue =
          katNPC.dialogue?.[dialogueKey]?.[0] ||
          "Good news! Your pet upgrade is complete and ready for pickup!";

        embed.setDescription(`${npcDialogue}`).addFields({
          name: "Pet Ready for Pickup",
          value: `${petInfo.emoji || "🐾"} **${originalRarityInfo.name} ${
            petInfo.name
          }** has been upgraded to **${targetRarityInfo.name} ${
            petInfo.name
          }**!`,
          inline: false,
        });
      } else {
        // Pet is still being upgraded - simplified dialogue
        const dialogueKey = "pet_not_ready";
        const npcDialogue =
          katNPC.dialogue?.[dialogueKey]?.[0] ||
          "Your pet is still being upgraded. Please check back {timeRemaining}.";

        // Calculate time remaining and create Discord timestamp
        const timeRemainingFormatted = formatTimeRemaining(
          upgradeStatus.timeRemaining
        );
        const completionTimestamp = Math.floor(
          (Date.now() + upgradeStatus.timeRemaining) / 1000
        );
        const discordTimestamp = `<t:${completionTimestamp}:R>`; // Relative time format

        // Format the dialogue with time remaining
        const formattedDialogue = npcDialogue.replace(
          "{timeRemaining}",
          `in ${timeRemainingFormatted}`
        );

        embed.setDescription(`${formattedDialogue}`).addFields({
          name: "Pet Being Upgraded",
          value: `${petInfo.emoji || "🐾"} **${originalRarityInfo.name} ${
            petInfo.name
          }** → **${targetRarityInfo.name} ${
            petInfo.name
          }**\n⏳ Ready ${discordTimestamp}`,
          inline: false,
        });
      }
    } else if (!fullCharacter.pets || fullCharacter.pets.length === 0) {
      // Player has no pets
      const dialogueKey = "no_pets";
      const npcDialogue =
        katNPC.dialogue?.[dialogueKey]?.[0] ||
        "You don't seem to have any pets that I can upgrade. Come back when you have a pet!";
      embed.setDescription(npcDialogue);
    } else {
      // Default case - player has pets but no upgrade in progress
      const dialogueKey = "default";
      const npcDialogue =
        katNPC.dialogue?.[dialogueKey]?.[0] ||
        "Hi there! I'm Kat, the Pet Sitter. I can upgrade your pets to a higher rarity for a fee.";
      embed.setDescription(npcDialogue);
    }

    // Pet upgrade info is now added directly in the main dialogue section above

    // Create components based on the upgrade status
    let row;

    if (upgradeStatus.upgrading) {
      // If a pet is being upgraded
      row = new ActionRowBuilder();

      if (upgradeStatus.isComplete) {
        // Pet is ready for pickup
        const pickupButtonId = `pickup_pet_${uuidv4()}`;
        row.addComponents(
          new ButtonBuilder()
            .setCustomId(pickupButtonId)
            .setLabel("Pick Up Pet")
            .setStyle(ButtonStyle.Success)
            .setEmoji("🐾")
        );
      } else {
        // Pet is still being upgraded - just show a disabled "Upgrading in Progress..." button
        row.addComponents(
          new ButtonBuilder()
            .setCustomId("upgrading_in_progress")
            .setLabel("Upgrade in progress...")
            .setStyle(ButtonStyle.Secondary)
            //  .setEmoji('⏳')
            .setDisabled(true)
        );
      }
    } else {
      // No pet is being upgraded
      if (fullCharacter.pets && fullCharacter.pets.length > 0) {
        // Filter pets that can be upgraded (not at max rarity)
        const upgradablePets = fullCharacter.pets.filter((pet) => {
          const nextRarity = getNextRarity(pet.rarity);
          return nextRarity !== null;
        });

        if (upgradablePets.length > 0) {
          // Double-check that the player doesn't have a pet being upgraded
          // This is a safety check in case the upgrade status changed between checks
          const latestUpgradeStatus = await checkPetUpgradeStatus(userId);
          if (latestUpgradeStatus.upgrading) {
            // If a pet is being upgraded, show a disabled button instead
            row = new ActionRowBuilder();
            row.addComponents(
              new ButtonBuilder()
                .setCustomId("already_upgrading_pet")
                .setLabel("Already Upgrading a Pet")
                .setStyle(ButtonStyle.Secondary)
                .setEmoji("⏳")
                .setDisabled(true)
            );
          } else {
            // Create a paginated select menu for pet selection (25 options max per select)
            const selectSessionId = uuidv4();
            const selectMenuId = `select_pet_upgrade_${selectSessionId}`;
            const prevBtnId = `kat_upgrade_prev_${selectSessionId}`;
            const nextBtnId = `kat_upgrade_next_${selectSessionId}`;

            const ITEMS_PER_PAGE = 25;
            let currentPage = 0;
            const totalPages = Math.max(
              1,
              Math.ceil(upgradablePets.length / ITEMS_PER_PAGE)
            );

            const buildOptionsForPage = (pageIndex) => {
              const start = pageIndex * ITEMS_PER_PAGE;
              const end = Math.min(
                start + ITEMS_PER_PAGE,
                upgradablePets.length
              );
              return upgradablePets.slice(start, end).map((pet) => {
                const petInfo = allItems[pet.petKey];
                const rarityInfo = ITEM_RARITY[pet.rarity];
                const nextRarity = getNextRarity(pet.rarity);
                const nextRarityInfo = ITEM_RARITY[nextRarity];
                const upgradeCost = getUpgradeCost(pet.rarity);
                return {
                  label: `${rarityInfo.name} ${petInfo.name} (Lvl ${getPetLevel(pet)})`,
                  description: `Upgrade to ${nextRarityInfo.name} for ${upgradeCost.toLocaleString()} coins`,
                  value: pet.id,
                  emoji: petInfo.emoji || "🐾",
                };
              });
            };

            const buildSelectRow = (pageIndex) => {
              const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(selectMenuId)
                .setPlaceholder(
                  `Select a pet to upgrade (Page ${pageIndex + 1}/${totalPages})`
                )
                .addOptions(buildOptionsForPage(pageIndex));
              return new ActionRowBuilder().addComponents(selectMenu);
            };

            const buildNavRow = (pageIndex) => {
              const prevBtn = new ButtonBuilder()
                .setCustomId(prevBtnId)
                .setLabel("Previous")
                .setStyle(ButtonStyle.Secondary)
                .setEmoji("⬅️")
                .setDisabled(pageIndex === 0);
              const nextBtn = new ButtonBuilder()
                .setCustomId(nextBtnId)
                .setLabel("Next")
                .setStyle(ButtonStyle.Secondary)
                .setEmoji("➡️")
                .setDisabled(pageIndex >= totalPages - 1);
              return new ActionRowBuilder().addComponents(prevBtn, nextBtn);
            };

            // Build initial rows
            const rows = [buildSelectRow(currentPage)];
            if (totalPages > 1) {
              rows.push(buildNavRow(currentPage));
            }

            // We'll replace 'row' with our rows array later during reply
            row = rows; // temporarily assign to be used below

            // After reply is sent, the collector below will handle prev/next using these IDs
          }
        } else {
          // No upgradable pets
          row = new ActionRowBuilder();
          row.addComponents(
            new ButtonBuilder()
              .setCustomId("no_upgradable_pets_button")
              .setLabel("No Upgradable Pets")
              .setStyle(ButtonStyle.Secondary)
              .setEmoji("❌")
              .setDisabled(true)
          );
        }
      } else {
        // Player has no pets
        row = new ActionRowBuilder();
        row.addComponents(
          new ButtonBuilder()
            .setCustomId("no_pets_button")
            .setLabel("No Pets Available")
            .setStyle(ButtonStyle.Secondary)
            .setEmoji("❌")
            .setDisabled(true)
        );
      }
    }

    // Send the message with the components (support array for paginated select)
    const componentsToSend = Array.isArray(row) ? row : [row];
    await interaction.reply({ embeds: [embed], components: componentsToSend });
    const reply = await interaction.fetchReply();

    // Set up collector for the buttons and select menu
    const collector = reply.createMessageComponentCollector({
      filter: (i) => i.user.id === userId,
      time: 300000, // 5 minute timeout
    });

    // Add collector end event to disable buttons after timeout
    collector.on("end", async (collected) => {
      if (collected.size === 0) {
        try {
          // Get the current components
          const currentComponents = reply.components;
          if (currentComponents && currentComponents.length > 0) {
            // Create new disabled components
            const disabledComponents = currentComponents.map((row) => {
              const newRow = ActionRowBuilder.from(row);
              newRow.components.forEach((component) => {
                component.setDisabled(true);
              });
              return newRow;
            });

            // Update the message with disabled components
            await interaction.editReply({ components: disabledComponents });
          }
        } catch (error) {
          console.error(
            "[Kat NPC] Error disabling buttons after timeout:",
            error
          );
        }
      }
    });

    collector.on("collect", async (i) => {
      const componentId = i.customId;

      if (componentId.startsWith("pickup_pet_")) {
        await handlePetPickup(i);
      } else if (componentId.startsWith("select_pet_upgrade_")) {
        // This is now handled by the global interaction handler in bot.js
      } else if (
        componentId.startsWith("kat_upgrade_prev_") ||
        componentId.startsWith("kat_upgrade_next_")
      ) {
        // Handle pagination for the select menu
        try {
          const idParts = componentId.split("_");
          const sessionId = idParts[idParts.length - 1];
          // Reconstruct IDs consistent with the ones we created above
          const selectMenuIdPrefix = `select_pet_upgrade_${sessionId}`;
          const isPrev = componentId.startsWith("kat_upgrade_prev_");

          // Find current components state from message
          // We didn't store currentPage globally, recompute from placeholder text
          const currentSelectRow = reply.components[0];
          const currentSelect = currentSelectRow?.components?.[0];
          let pageFromPlaceholder = 1;
          try {
            const placeholder =
              currentSelect?.data?.placeholder ||
              currentSelect?.placeholder ||
              "";
            const m = placeholder.match(/Page (\d+)\/(\d+)/);
            if (m) pageFromPlaceholder = parseInt(m[1], 10);
          } catch {}
          let currentPage = Math.max(1, pageFromPlaceholder) - 1;

          // Recreate the same upgradablePets scope by refetching player data quickly (safe & cheap)
          const latestCharacter = await getPlayerData(i.user.id);
          const latestUpgradable = (latestCharacter?.pets || []).filter(
            (pet) => getNextRarity(pet.rarity) !== null
          );
          const ITEMS_PER_PAGE = 25;
          const totalPages = Math.max(
            1,
            Math.ceil(latestUpgradable.length / ITEMS_PER_PAGE)
          );
          currentPage = isPrev
            ? Math.max(0, currentPage - 1)
            : Math.min(totalPages - 1, currentPage + 1);

          const buildOptionsForPage = (pageIndex) => {
            const start = pageIndex * ITEMS_PER_PAGE;
            const end = Math.min(
              start + ITEMS_PER_PAGE,
              latestUpgradable.length
            );
            return latestUpgradable.slice(start, end).map((pet) => {
              const petInfo = allItems[pet.petKey];
              const rarityInfo = ITEM_RARITY[pet.rarity];
              const nextRarity = getNextRarity(pet.rarity);
              const nextRarityInfo = ITEM_RARITY[nextRarity];
              const upgradeCost = getUpgradeCost(pet.rarity);
              return {
                label: `${rarityInfo.name} ${petInfo.name} (Lvl ${getPetLevel(pet)})`,
                description: `Upgrade to ${nextRarityInfo.name} for ${upgradeCost.toLocaleString()} coins`,
                value: pet.id,
                emoji: petInfo.emoji || "🐾",
              };
            });
          };

          const selectRow = new ActionRowBuilder().addComponents(
            new StringSelectMenuBuilder()
              .setCustomId(selectMenuIdPrefix)
              .setPlaceholder(
                `Select a pet to upgrade (Page ${currentPage + 1}/${totalPages})`
              )
              .addOptions(buildOptionsForPage(currentPage))
          );

          const navRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
              .setCustomId(`kat_upgrade_prev_${sessionId}`)
              .setLabel("Previous")
              .setStyle(ButtonStyle.Secondary)
              .setEmoji("⬅️")
              .setDisabled(currentPage === 0),
            new ButtonBuilder()
              .setCustomId(`kat_upgrade_next_${sessionId}`)
              .setLabel("Next")
              .setStyle(ButtonStyle.Secondary)
              .setEmoji("➡️")
              .setDisabled(currentPage >= totalPages - 1)
          );

          await i.update({
            components: totalPages > 1 ? [selectRow, navRow] : [selectRow],
          });
        } catch (e) {
          console.error("[Kat NPC] Pagination update failed:", e);
          try {
            await i.deferUpdate().catch(() => {});
          } catch {}
        }
      }
    });
  } catch (error) {
    console.error("[Kat NPC] Error in handleKatPetUpgradeInteraction:", error);
    return interaction.reply({
      content:
        "An error occurred while talking to Kat. Please try again later.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * Handles the pet pickup process
 * @param {ButtonInteraction} i - The button interaction
 */
async function handlePetPickup(i) {
  try {
    await i.deferUpdate();

    const userId = i.user.id;
    const result = await completePetUpgrade(userId);

    if (!result.success) {
      let errorMessage = "An error occurred while picking up your pet.";
      if (result.error === "no_upgrade_in_progress") {
        errorMessage = "You don't have any pets being upgraded.";
      } else if (result.error === "upgrade_not_complete") {
        const timeRemainingFormatted = formatTimeRemaining(
          result.timeRemaining
        );
        errorMessage = `Your pet upgrade is not complete yet. Please check back in ${timeRemainingFormatted}.`;
      }

      return i.followUp({
        content: errorMessage,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get the pet and item information
    const allItems = configManager.getAllItems();
    const petInfo = allItems[result.pet.petKey];
    const rarityInfo = ITEM_RARITY[result.pet.rarity];

    if (!petInfo || !rarityInfo) {
      return i.followUp({
        content: "An error occurred while retrieving your pet information.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Create success embed with more detailed level information
    const successEmbed = new EmbedBuilder()
      .setColor(rarityInfo.color || EMBED_COLORS.PINK)
      .setTitle("🎉 Pet Upgrade Complete!")
      .setDescription(
        `Your pet has been upgraded to **${rarityInfo.name} ${petInfo.name}**!`
      )
      .addFields({
        name: "Pet Details",
        value: `${petInfo.emoji || "🐾"} **${rarityInfo.name} ${
          petInfo.name
        }** (Level ${getPetLevel(result.pet)})`,
        inline: false,
      });

    // Add pet stats if available
    if (petInfo.stats) {
      const statLines = [];
      for (const [statKey, statFormula] of Object.entries(petInfo.stats)) {
        if (statFormula && typeof statFormula === "object") {
          const base = statFormula.base || 0;
          const perLevel = statFormula.perLevel || 0;
          const effectiveLevel = Math.min(
            getPetLevel(result.pet),
            statFormula.cap || 100
          );
          const calculatedBonus = base + perLevel * effectiveLevel;

          if (calculatedBonus > 0) {
            statLines.push(`+${calculatedBonus} ${statKey}`);
          }
        }
      }

      if (statLines.length > 0) {
        successEmbed.addFields({
          name: "Pet Bonuses",
          value: statLines.join("\n"),
          inline: false,
        });
      }
    }

    // Update the message with the success embed and remove buttons
    return i.editReply({ embeds: [successEmbed], components: [] });
  } catch (error) {
    console.error("[Kat NPC] Error in handlePetPickup:", error);
    return i.followUp({
      content: "An error occurred while picking up your pet.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * Handles starting the pet upgrade process
 * @param {ButtonInteraction} i - The button interaction
 * @param {string} petId - The ID of the pet to upgrade
 * @param {object} katNPC - The Kat NPC object
 * @param {object} allItems - All items in the game
 */
async function handlePetUpgradeStart(i, petId, katNPC, allItems) {
  try {
    // Immediately acknowledge the interaction to prevent timeout
    await i.deferUpdate();

    const userId = i.user.id;

    // Check if player already has a pet being upgraded before starting - use direct database check
    const preCheckStatus = await checkPetUpgradeStatus(userId);

    if (preCheckStatus.upgrading) {
      await i.followUp({
        content: `You already have a pet being upgraded! You can only upgrade one pet at a time.`,
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Start the pet upgrade
    const result = await startPetUpgrade(userId, petId);

    if (!result.success) {
      let errorMessage = "An error occurred while starting the pet upgrade.";
      if (result.error === "already_upgrading") {
        errorMessage = "You already have a pet being upgraded.";
      } else if (result.error === "pet_not_found") {
        errorMessage = "The selected pet could not be found.";
      } else if (result.error === "max_rarity") {
        errorMessage = "This pet is already at maximum rarity.";
      } else if (result.error === "insufficient_coins") {
        errorMessage = "You don't have enough coins to upgrade this pet.";
      }

      return i.followUp({
        content: errorMessage,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Get pet information
    const petInfo = allItems[result.petUpgrade.petKey];
    const originalRarityInfo = ITEM_RARITY[result.petUpgrade.originalRarity];
    const targetRarityInfo = ITEM_RARITY[result.petUpgrade.targetRarity];

    if (!petInfo || !originalRarityInfo || !targetRarityInfo) {
      return i.followUp({
        content: "An error occurred while retrieving pet information.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Calculate time remaining
    const timeRemaining = result.petUpgrade.endTime - Date.now();
    const timeRemainingFormatted = formatTimeRemaining(timeRemaining);

    // Create success embed
    const successEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PINK)
      .setTitle("Pet Upgrade Started")
      .setDescription(`${katNPC.emoji || "🐱"} Kat is now upgrading your pet!`)
      .addFields(
        {
          name: "Pet Being Upgraded",
          value: `${petInfo.emoji || "🐾"} ${originalRarityInfo.name} ${
            petInfo.name
          } → ${targetRarityInfo.name} ${petInfo.name}`,
          inline: false,
        },
        {
          name: "Cost",
          value: `${
            CURRENCY.emoji
          } ${result.petUpgrade.cost.toLocaleString()} coins`,
          inline: true,
        },
        {
          name: "Time Remaining",
          value: `⏳ ${timeRemainingFormatted} (<t:${Math.floor(
            result.petUpgrade.endTime / 1000
          )}:R>)`,
          inline: true,
        },
        {
          name: "Pickup",
          value:
            "Come back to Kat when the upgrade is complete to pick up your pet.",
          inline: false,
        }
      );

    // Update the message with the success embed
    return i.editReply({ content: "", embeds: [successEmbed], components: [] });
  } catch (error) {
    console.error("[Kat NPC] Error in handlePetUpgradeStart:", error);
    return i.followUp({
      content: "An error occurred while starting the pet upgrade.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * Handles the Baker NPC's cake interaction.
 * @param {CommandInteraction} interaction
 * @param {object} character
 * @param {object} bakerNPC
 * @param {object} timeState
 */
async function handleBakerCakeInteraction(
  interaction,
  character,
  bakerNPC,
  timeState
) {
  try {
    const userId = interaction.user.id;

    // Get latest data about claimed cake status
    const cakeData = await getPlayerData(userId);
    const endingYear = getEndingYear(timeState) + 1; // Add 1 to display as Year 1, 2, etc. instead of 0-indexed
    const hasClaimedCake = cakeData.lastClaimedYear >= endingYear;

    // Get the New Year Cake item data
    const allItems = configManager.getAllItems();
    const cakeItemKey = "NEW_YEAR_CAKE_BASE";
    const cakeItem = allItems[cakeItemKey];
    const cakeEmoji = "<:new_year_cake:1372914056641511484>"; // Use the specified emoji

    if (!cakeItem) {
      console.error(`[Baker] Cake item not found with key: ${cakeItemKey}`);
      return interaction.reply({
        content:
          "Sorry, there was an error finding the cake. Please report this to an admin.",
      });
    }

    // Create appropriate message based on state
    let dialogueKey = "default";
    if (!isBakerSpawnWindow(timeState)) {
      dialogueKey = "not_spawn_window";
    } else if (hasClaimedCake) {
      dialogueKey = "already_claimed";
    }

    // Get the dialogue from the Baker's dialogue object, replacing {year} with the actual year
    const npcDialogue =
      bakerNPC.dialogue?.[dialogueKey]?.[0] || "Happy New Year!";
    const formattedDialogue = npcDialogue.replace(/{year}/g, endingYear);

    // Create the embed for the Baker's message
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GOLD) // Gold color for festive appearance
      .setTitle(`${bakerNPC.emoji || cakeEmoji} ${bakerNPC.name}`)
      .setDescription(formattedDialogue);

    // Create the "Collect Cake" button
    const collectCakeId = `collect_cake_${uuidv4()}`;
    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(collectCakeId)
        .setLabel("Collect Cake")
        .setStyle(ButtonStyle.Success)
        .setDisabled(hasClaimedCake || !isBakerSpawnWindow(timeState))
        .setEmoji("🎂")
    );

    // Send the message with the button (non-ephemeral)
    await interaction.reply({ embeds: [embed], components: [row] });
    const reply = await interaction.fetchReply();

    // Set up collector for the button
    const collector = reply.createMessageComponentCollector({
      filter: (i) => i.customId === collectCakeId && i.user.id === userId,
      time: 60000, // 1 minute timeout
      max: 1, // Only allow one collection
    });

    collector.on("collect", async (i) => {
      await i.deferUpdate(); // Double-check that the player hasn't claimed this year's cake
      // Fetch all fields needed by savePlayerData since it doesn't respect the fields parameter for existing players
      const updatedCharacter = await getPlayerData(userId);
      if (updatedCharacter.lastClaimedYear >= endingYear) {
        return i.followUp({
          content: "You've already collected your New Year Cake for this year!",
        });
      }

      // Add the cake to the player's cake bag
      if (!updatedCharacter.cakeBag) {
        updatedCharacter.cakeBag = [];
      }

      // Create a new cake entry with the current year and timestamp
      const newCake = {
        year: endingYear,
        obtainedTimestampUTC: Math.floor(Date.now() / 1000),
      };
      updatedCharacter.cakeBag.push(newCake);
      updatedCharacter.lastClaimedYear = endingYear;

      // Save only the updated cake bag and lastClaimedYear fields
      await savePlayerData(userId, updatedCharacter, [
        "cakeBag",
        "lastClaimedYear",
      ]);

      // Send success message (non-ephemeral)
      const successEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GOLD)
        .setTitle("🎉 New Year Cake Collected!")
        .setDescription(
          `You have collected a **New Year Cake (Year ${endingYear})** from the Baker! View your collection with /cakebag.`
        );

      await i.followUp({ embeds: [successEmbed] });

      // Update the original message to disable the button
      const updatedRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId(collectCakeId)
          .setLabel("Cake Collected")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(true)
          .setEmoji("✅")
      );

      await reply.edit({ components: [updatedRow] });
    });
  } catch (error) {
    console.error("[Baker Cake Interaction] Error:", error);
    return interaction.reply({
      content:
        "An error occurred while interacting with the Baker. Please try again later.",
    });
  }
}

async function autocomplete(interaction) {
  const focusedValue = interaction.options.getFocused();
  const userId = interaction.user.id;
  let character;

  try {
    character = await getPlayerData(userId);
  } catch (error) {
    console.error(
      `[Talk Autocomplete] Error fetching player data for ${userId}:`,
      error
    );
    await interaction
      .respond([])
      .catch((e) =>
        console.error(
          "Error responding to autocomplete (player data fetch):",
          e
        )
      );
    return;
  }

  if (!character) {
    await interaction
      .respond([])
      .catch((e) =>
        console.error("Error responding to autocomplete (no character):", e)
      );
    return;
  }

  // Use current_region from database
  if (!character.current_region) {
    // Let's provide some fallback options rather than nothing
    const fallbackChoices = Object.values(NPCS)
      .map((npc) => ({ name: npc.name, value: npc.name }))
      .filter((choice) =>
        choice.name.toLowerCase().startsWith(focusedValue.toLowerCase())
      )
      .slice(0, 25);

    await interaction
      .respond(fallbackChoices)
      .catch((e) => console.error("Error responding with fallbacks:", e));
    return;
  }

  const currentRegionKey = character.current_region;

  // Get timeState from the globally required bot module
  const timeState = bot.getTimeState ? bot.getTimeState() : null;

  const choices = [];
  for (const npcKey in NPCS) {
    const npc = NPCS[npcKey];
    let isAvailable =
      npc.foundInRegions && npc.foundInRegions.includes(currentRegionKey);

    // Log the check for each NPC

    // Special handling for Baker's appearance conditions
    if (isAvailable && npc.key === "BAKER") {
      const bakerAvailable = timeState && isBakerSpawnWindow(timeState);
      if (!bakerAvailable) {
        isAvailable = false;
      }
    }

    if (isAvailable) {
      choices.push({ name: npc.name, value: npc.name });
    }
  }

  const filtered = choices.filter((choice) =>
    choice.name.toLowerCase().startsWith(focusedValue.toLowerCase())
  );

  await interaction
    .respond(filtered.slice(0, 25))
    .catch((e) =>
      console.error("Error responding to autocomplete (final respond):", e)
    );
}

/**
 * Handles the pet upgrade selection process
 * @param {StringSelectMenuInteraction} interaction - The select menu interaction
 * @returns {Promise<boolean>} - Whether the interaction was handled successfully
 */
async function handlePetUpgradeSelection(interaction) {
  try {
    // Interaction should already be deferred by the global handler

    const userId = interaction.user.id;
    const selectedPetId = interaction.values[0];

    // First, check if the player already has a pet being upgraded - use direct database check
    const upgradeStatus = await checkPetUpgradeStatus(userId);

    // Silent upgrade status check

    if (upgradeStatus.upgrading) {
      // Player already has a pet being upgraded, show an error message
      const petInfo =
        configManager.getAllItems()[upgradeStatus.pet_upgrade_json.petKey];
      const petName = petInfo ? petInfo.name : "pet";
      const rarityInfo =
        ITEM_RARITY[upgradeStatus.pet_upgrade_json.originalRarity];
      const rarityName = rarityInfo ? rarityInfo.name : "";

      await interaction.followUp({
        content: `You already have a ${rarityName} ${petName} being upgraded! You can only upgrade one pet at a time.`,
        flags: [MessageFlags.Ephemeral],
      });
      return false;
    }

    // Get the player's pets and currency
    const character = await getPlayerData(userId);

    // Find the selected pet
    const selectedPet = character.pets.find((pet) => pet.id === selectedPetId);
    if (!selectedPet) {
      await interaction.followUp({
        content: "The selected pet could not be found.",
        flags: [MessageFlags.Ephemeral],
      });
      return false;
    }

    // Get all items
    const allItems = configManager.getAllItems();

    // Get pet information
    const petInfo = allItems[selectedPet.petKey];
    const rarityInfo = ITEM_RARITY[selectedPet.rarity];
    const nextRarity = getNextRarity(selectedPet.rarity);
    const nextRarityInfo = ITEM_RARITY[nextRarity];

    if (!petInfo || !rarityInfo || !nextRarity || !nextRarityInfo) {
      await interaction.followUp({
        content: "An error occurred while retrieving pet information.",
        flags: [MessageFlags.Ephemeral],
      });
      return false;
    }

    // Get upgrade cost and duration
    const upgradeCost = getUpgradeCost(selectedPet.rarity);
    const upgradeDuration = getUpgradeDuration(selectedPet.rarity);

    if (!upgradeCost || !upgradeDuration) {
      await interaction.followUp({
        content:
          "An error occurred while calculating upgrade cost and duration.",
        flags: [MessageFlags.Ephemeral],
      });
      return false;
    }

    // Check if player has enough coins
    if (character.coins < upgradeCost) {
      await interaction.followUp({
        content: `You don't have enough coins to upgrade this pet. You need ${upgradeCost.toLocaleString()} coins, but you only have ${character.coins.toLocaleString()} coins.`,
        flags: [MessageFlags.Ephemeral],
      });
      return false;
    }

    // Double-check that the player doesn't have a pet being upgraded - use direct database check
    const doubleCheckStatus = await checkPetUpgradeStatus(userId);

    if (doubleCheckStatus.upgrading) {
      const petInfo =
        configManager.getAllItems()[doubleCheckStatus.pet_upgrade_json.petKey];
      const petName = petInfo ? petInfo.name : "pet";
      const rarityInfo =
        ITEM_RARITY[doubleCheckStatus.pet_upgrade_json.originalRarity];
      const rarityName = rarityInfo ? rarityInfo.name : "";

      await interaction.followUp({
        content: `You already have a ${rarityName} ${petName} being upgraded! You can only upgrade one pet at a time.`,
        flags: [MessageFlags.Ephemeral],
      });
      return false;
    }

    // Get the Kat NPC
    const katNPC = NPCS.KAT;

    // Create confirmation embed
    const confirmEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.PINK)
      .setTitle("Pet Upgrade Confirmation")
      .setDescription(
        `${katNPC.emoji || "🐱"} Are you sure you want to upgrade your pet?`
      )
      .addFields(
        {
          name: "Pet",
          value: `${petInfo.emoji || "🐾"} ${rarityInfo.name} ${
            petInfo.name
          } (Level ${getPetLevel(selectedPet)})`,
          inline: false,
        },
        {
          name: "Upgrade To",
          value: `${petInfo.emoji || "🐾"} ${nextRarityInfo.name} ${
            petInfo.name
          }`,
          inline: false,
        },
        {
          name: "Cost",
          value: `${CURRENCY.emoji} ${upgradeCost.toLocaleString()} coins`,
          inline: true,
        },
        {
          name: "Duration",
          value: `⏳ ${formatTimeRemaining(upgradeDuration * 1000)}`,
          inline: true,
        },
        {
          name: "Note",
          value:
            "⚠️ Your pet's level may change based on the new rarity requirements.",
          inline: false,
        }
      );

    // Create confirm and cancel buttons
    const confirmButtonId = `confirm_pet_upgrade_${uuidv4()}`;
    const cancelButtonId = `cancel_pet_upgrade_${uuidv4()}`;

    const row = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(confirmButtonId)
        .setLabel("Confirm Upgrade")
        .setStyle(ButtonStyle.Success)
        .setEmoji("✅"),
      new ButtonBuilder()
        .setCustomId(cancelButtonId)
        .setLabel("Cancel")
        .setStyle(ButtonStyle.Secondary)
        .setEmoji("❌")
    );

    // Update the message with the confirmation (edit the active conversation, not ephemeral)
    await interaction.editReply({ embeds: [confirmEmbed], components: [row] });
    const updatedMessage = await interaction.fetchReply();

    // Set up collector for the buttons
    const collector = updatedMessage.createMessageComponentCollector({
      filter: (buttonInteraction) =>
        (buttonInteraction.customId === confirmButtonId ||
          buttonInteraction.customId === cancelButtonId) &&
        buttonInteraction.user.id === userId,
      time: 60000, // 1 minute timeout
      max: 1,
    });

    collector.on("collect", async (buttonInteraction) => {
      if (buttonInteraction.customId === confirmButtonId) {
        await handlePetUpgradeStart(
          buttonInteraction,
          selectedPetId,
          katNPC,
          allItems
        );
      } else {
        await buttonInteraction.deferUpdate();
        await buttonInteraction.editReply({
          content: "Pet upgrade cancelled.",
          embeds: [],
          components: [],
        });
      }
    });

    collector.on("end", async (collected) => {
      if (collected.size === 0) {
        // Timeout - disable the components instead of removing them
        try {
          // Get the current components
          const currentComponents = updatedMessage.components;
          if (currentComponents && currentComponents.length > 0) {
            // Create new disabled components
            const disabledComponents = currentComponents.map((row) => {
              const newRow = ActionRowBuilder.from(row);
              newRow.components.forEach((component) => {
                component.setDisabled(true);
              });
              return newRow;
            });

            // Update the message with disabled components and add timeout message
            await updatedMessage.edit({
              content: "Pet upgrade confirmation timed out.",
              embeds: updatedMessage.embeds,
              components: disabledComponents,
            });
          }
        } catch (error) {
          console.error(
            "[Kat NPC] Error disabling buttons after timeout:",
            error
          );
          // Fallback to original behavior if the above fails
          try {
            await updatedMessage.edit({
              content: "Pet upgrade confirmation timed out.",
              embeds: [],
              components: [],
            });
          } catch {
            // Ignore errors if the message was deleted
          }
        }
      }
    });

    return true;
  } catch (error) {
    console.error("[Kat NPC] Error in handlePetUpgradeSelection:", error);
    try {
      await interaction.followUp({
        content: "An error occurred while processing your pet selection.",
        flags: [MessageFlags.Ephemeral],
      });
    } catch (followUpError) {
      console.error("[Kat NPC] Error sending followUp:", followUpError);
    }
    return false;
  }
}

/**
 * Handles interaction with the Blacksmith NPC for reforging equipment
 */
async function handleBlacksmithInteraction(
  interaction,
  character,
  selectedNPC,
  allItems
) {
  // Inventory utilities are available but not used in this function

  // Get player's reforgeable items from inventory and equipment
  let playerData = await getPlayerData(interaction.user.id);
  const storageItems = playerData.inventory?.items || {};
  const equippedItems = playerData.inventory?.equipment || [];

  const reforgableItems = [];

  // Add items from storage (/storage command items)
  for (const [itemKey, itemData] of Object.entries(storageItems)) {
    if (itemData.amount > 0) {
      const item = allItems[itemKey];
      if (item && canReforge(item)) {
        // Read reforge from data_json
        let currentReforge = "None";
        if (itemData.data_json) {
          const dataJson =
            typeof itemData.data_json === "string"
              ? JSON.parse(itemData.data_json)
              : itemData.data_json;
          if (dataJson.reforge) {
            currentReforge =
              getDynamicReforgeName(dataJson.reforge, item) || "None";
          }
        }
        // Display name would be used for UI formatting if needed
        reforgableItems.push({
          label: `${item.name} (${currentReforge})`,
          value: `storage_${itemKey}`,
          description: `From Storage - Current: ${currentReforge}`,
          emoji: item.emoji,
        });
      }
    }
  }

  // Add equipped items (/inventory command equipped items)
  for (const equippedItem of equippedItems) {
    if (equippedItem && equippedItem.isEquipped && equippedItem.itemKey) {
      const item = allItems[equippedItem.itemKey];
      if (item && canReforge(item)) {
        // Read reforge from data_json that's already loaded
        let currentReforge = "None";
        if (equippedItem.data_json && equippedItem.data_json.reforge) {
          currentReforge =
            getDynamicReforgeName(equippedItem.data_json.reforge, item) ||
            "None";
        }

        // Display name would be used for UI formatting if needed
        reforgableItems.push({
          label: `${item.name} (${currentReforge})`,
          value: `equipment_${equippedItem.id}`,
          description: `Equipped - Current: ${currentReforge}`,
          emoji: item.emoji,
        });
      }
    }
  }

  if (reforgableItems.length === 0) {
    const embed = new EmbedBuilder()
      .setTitle(`<:npc_blacksmith:1378078835752439879> ${selectedNPC.name}`)
      .setDescription(
        "You don't have any equipment that can be reforged! Come back when you have weapons, armor, or tools."
      )
      .setColor(EMBED_COLORS.BROWN);

    return interaction.reply({ embeds: [embed] });
  }

  // Create greeting embed with item selection
  const embed = new EmbedBuilder()
    .setTitle(`<:npc_blacksmith:1378078835752439879> ${selectedNPC.name}`)
    .setDescription("What do you want to reforge today?")
    .setColor(EMBED_COLORS.BROWN)
    .addFields({
      name: "Your Coins",
      value: `${CURRENCY.purseEmoji} ${formatNumber(playerData.coins || 0)}`,
      inline: true,
    });

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId(`blacksmith_item_select_${uuidv4()}`)
    .setPlaceholder("Choose an item to reforge")
    .addOptions(reforgableItems.slice(0, 25)); // Discord limit

  const row = new ActionRowBuilder().addComponents(selectMenu);

  // Add a Refresh button so players can update their item list without rerunning the command
  const refreshButton = new ButtonBuilder()
    .setCustomId(`blacksmith_refresh_${uuidv4()}`)
    .setLabel("Refresh")
    .setStyle(ButtonStyle.Secondary)
    .setEmoji("🔄");

  const refreshRow = new ActionRowBuilder().addComponents(refreshButton);

  await interaction.reply({ embeds: [embed], components: [row, refreshRow] });
  const reply = await interaction.fetchReply();

  // Create collector for item selection
  // Store collectors in a global map if not already created
  if (!global.blacksmithCollectors) {
    global.blacksmithCollectors = new Map();
  }

  // Remove old collector if it exists for this user
  const oldCollector = global.blacksmithCollectors.get(interaction.user.id);
  if (oldCollector) {
    oldCollector.stop();
  }

  const collector = reply.createMessageComponentCollector({
    filter: (i) =>
      (i.customId.startsWith("blacksmith_item_select_") ||
        i.customId.startsWith("blacksmith_refresh_")) &&
      i.user.id === interaction.user.id,
    time: 300000,
  });

  // Store the new collector in the global map
  global.blacksmithCollectors.set(interaction.user.id, collector);

  // Add end event handler to clean up
  collector.on("end", (collected, reason) => {
    // Only remove components if the collector ended due to timeout, not due to a button click
    if (reason === "time") {
      try {
        // Check if interaction.message exists before trying to edit it
        if (interaction.message) {
          interaction.message.edit({ components: [] }).catch(() => {});
        }
      } catch (error) {
        console.error(
          "[Talk Command] Error removing components on collector end:",
          error
        );
      }
    }

    // Remove the collector from the global map
    if (
      global.blacksmithCollectors &&
      global.blacksmithCollectors.get(interaction.user.id) === collector
    ) {
      global.blacksmithCollectors.delete(interaction.user.id);
    }
  });

  collector.on("collect", async (selectInteraction) => {
    try {
      if (selectInteraction.customId.startsWith("blacksmith_refresh_")) {
        // Re-fetch data and regenerate the selection menu
        playerData = await getPlayerData(selectInteraction.user.id);

        const storageItems = playerData.inventory?.items || {};
        const equippedItems = playerData.inventory?.equipment || [];

        const updatedItems = [];

        for (const [itemKey, itemData] of Object.entries(storageItems)) {
          if (itemData.amount > 0) {
            const item = allItems[itemKey];
            if (item && canReforge(item)) {
              let currentReforge = "None";
              if (itemData.data_json) {
                const dataJson =
                  typeof itemData.data_json === "string"
                    ? JSON.parse(itemData.data_json)
                    : itemData.data_json;
                if (dataJson.reforge) {
                  currentReforge =
                    getDynamicReforgeName(dataJson.reforge, item) || "None";
                }
              }
              updatedItems.push({
                label: `${item.name} (${currentReforge})`,
                value: `storage_${itemKey}`,
                description: `From Storage - Current: ${currentReforge}`,
                emoji: item.emoji,
              });
            }
          }
        }

        for (const equippedItem of equippedItems) {
          if (equippedItem && equippedItem.isEquipped && equippedItem.itemKey) {
            const item = allItems[equippedItem.itemKey];
            if (item && canReforge(item)) {
              let currentReforge = "None";
              if (equippedItem.data_json && equippedItem.data_json.reforge) {
                currentReforge =
                  getDynamicReforgeName(equippedItem.data_json.reforge, item) ||
                  "None";
              }
              updatedItems.push({
                label: `${item.name} (${currentReforge})`,
                value: `equipment_${equippedItem.id}`,
                description: `Equipped - Current: ${currentReforge}`,
                emoji: item.emoji,
              });
            }
          }
        }

        if (updatedItems.length === 0) {
          return selectInteraction.reply({
            content:
              "❌ You still don't have any equipment that can be reforged!",
            ephemeral: true,
          });
        }

        const refreshedEmbed = new EmbedBuilder()
          .setTitle(embed.data.title)
          .setDescription(embed.data.description)
          .setColor(EMBED_COLORS.BROWN)
          .addFields({
            name: "Your Coins",
            value: `${CURRENCY.purseEmoji} ${formatNumber(playerData.coins || 0)}`,
            inline: true,
          });

        const refreshedSelect = new StringSelectMenuBuilder()
          .setCustomId(`blacksmith_item_select_${uuidv4()}`)
          .setPlaceholder("Choose an item to reforge")
          .addOptions(updatedItems.slice(0, 25));

        const refreshedRow = new ActionRowBuilder().addComponents(
          refreshedSelect
        );

        const refreshedButton = new ButtonBuilder()
          .setCustomId(`blacksmith_refresh_${uuidv4()}`)
          .setLabel("Refresh")
          .setStyle(ButtonStyle.Secondary)
          .setEmoji("🔄");

        const refreshedButtonRow = new ActionRowBuilder().addComponents(
          refreshedButton
        );

        await selectInteraction.update({
          embeds: [refreshedEmbed],
          components: [refreshedRow, refreshedButtonRow],
        });
      } else {
        await handleItemSelection(
          selectInteraction,
          selectedNPC,
          allItems,
          playerData
        );
      }
    } catch (error) {
      if (error.code === 10062) {
        // Unknown interaction error
      } else {
        console.error("[Talk Command] Error handling item selection:", error);
        try {
          await selectInteraction.reply({
            content: "An error occurred. Please try again.",
            ephemeral: true,
          });
        } catch (replyError) {
          console.error(
            "[Talk Command] Error sending error reply:",
            replyError
          );
        }
      }
    }
  });

  collector.on("end", (collected, reason) => {
    // Only remove components if the collector ended due to timeout, not due to a button click
    if (reason === "time") {
      // Clean up the message when collector expires
      interaction.editReply({ components: [] }).catch(() => {});
    }

    // Remove the collector from the global map
    if (
      global.blacksmithCollectors &&
      global.blacksmithCollectors.get(interaction.user.id) === collector
    ) {
      global.blacksmithCollectors.delete(interaction.user.id);
    }
  });
}

/**
 * Handles the item selection and shows the reforge interface
 */
async function handleItemSelection(
  interaction,
  selectedNPC,
  allItems,
  playerData
) {
  const selectedValue = interaction.values[0];
  const [source, identifier] = selectedValue.split("_", 2);

  let item, itemData, currentReforge;

  if (source === "storage") {
    item = allItems[identifier];
    itemData = playerData.inventory.items[identifier];
    // Read reforge from data_json
    if (itemData.data_json) {
      const dataJson =
        typeof itemData.data_json === "string"
          ? JSON.parse(itemData.data_json)
          : itemData.data_json;
      currentReforge = dataJson.reforge;
    }
  } else if (source === "equipment") {
    const equippedItem = playerData.inventory.equipment.find(
      (eq) => eq.id === identifier
    );
    item = allItems[equippedItem?.itemKey];
    // Read reforge from database data_json
    const db = require("../utils/database");

    const currentItem = await new Promise((resolve, reject) => {
      db.get(
        "SELECT data_json FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
        [identifier, interaction.user.id],
        (err, row) => (err ? reject(err) : resolve(row))
      );
    });

    if (currentItem?.data_json) {
      const dataJson = JSON.parse(currentItem.data_json);
      currentReforge = dataJson.reforge;
    }
    itemData = {}; // We don't need itemData for equipped items anymore
  }

  if (!item || !canReforge(item)) {
    return interaction.update({
      content: "❌ This item cannot be reforged!",
      embeds: [],
      components: [],
    });
  }

  const rarityKey = typeof item.rarity === "string" ? item.rarity : item.rarity;
  const cost = getReforgeCost(rarityKey);

  // Calculate total stats like in inspect command (using dynamic reforge calculation)
  const baseStats = item.baseStats || {};
  let reforgeStats = {};
  if (currentReforge) {
    const {
      calculateDynamicReforgeStats,
    } = require("../utils/dynamicReforgeStats");
    reforgeStats = calculateDynamicReforgeStats(currentReforge, item);
  }
  const totalStats = {};

  // Combine base stats and reforge stats
  for (const [stat, value] of Object.entries(baseStats)) {
    totalStats[stat] = value + (reforgeStats[stat] || 0);
  }
  for (const [stat, value] of Object.entries(reforgeStats)) {
    if (!totalStats[stat]) totalStats[stat] = value;
  }

  const statsArray = Object.entries(totalStats).map(([stat, value]) => {
    const statConfig = STATS[stat];
    if (statConfig) {
      const displayValue = statConfig.isPercentage
        ? `${value > 0 ? "+" : ""}${value}%`
        : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
      const statName =
        STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
      return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
    } else {
      return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
    }
  });

  // Format stats with 3 per line
  const statsText =
    statsArray.length > 0
      ? statsArray.reduce((acc, stat, index) => {
          if (index % 3 === 0 && index > 0) acc += "\n";
          return acc + (index % 3 === 0 ? "" : " ") + stat;
        }, "")
      : "No stats";

  // Format reforge stats for Current Reforge section (using dynamic calculation)
  const reforgeStatsArray =
    Object.keys(reforgeStats).length > 0
      ? Object.entries(reforgeStats).map(([stat, value]) => {
          const statConfig = STATS[stat];
          if (statConfig) {
            const displayValue = statConfig.isPercentage
              ? `${value > 0 ? "+" : ""}${value}%`
              : `${value > 0 ? "+" : ""}${value.toLocaleString()}`;
            const statName =
              STAT_ABBREVIATIONS[statConfig.name] || statConfig.name || stat;
            return `${statConfig.emoji || ""} \`${statName}: ${displayValue}\``;
          } else {
            return `❓ \`${stat}: ${value > 0 ? "+" : ""}${value.toLocaleString()}\``;
          }
        })
      : [];

  const reforgeStatsText =
    reforgeStatsArray.length > 0
      ? "**Stats:**\n" +
        reforgeStatsArray.reduce((acc, stat, index) => {
          if (index % 3 === 0 && index > 0) acc += "\n";
          return acc + (index % 3 === 0 ? "" : " ") + stat;
        }, "")
      : "No stats";

  // Current reforge text would be used for detailed UI display if needed
  const playerCoins = playerData.coins || 0;

  const embed = new EmbedBuilder()
    .setTitle(
      `<:npc_blacksmith:1378078835752439879> Blacksmith: ${item.emoji} ${item.name}`
    )
    .setDescription(
      `**Stats:**\n${statsText}\n\n**Current Reforge:** ${currentReforge ? getDynamicReforgeName(currentReforge, item) || "None" : "None"}\n${currentReforge ? reforgeStatsText : ""}\n\n**Your coins:** <:purse_coins:1367849116033482772> ${formatNumber(playerCoins)} Coins\n\n**Reforge Cost:** <:purse_coins:1367849116033482772> ${formatNumber(cost)} Coins`
    )
    .setColor(getItemColor(item))
    .setFooter({ text: `Item ID: ${identifier}` });

  const rerollButton = new ButtonBuilder()
    .setCustomId(
      `blacksmith_reroll_${selectedValue}_${uuidv4().substring(0, 8)}`
    )
    .setLabel("Reroll")
    .setStyle(ButtonStyle.Primary)
    .setDisabled((playerData.coins || 0) < cost);

  const changeItemButton = new ButtonBuilder()
    .setCustomId(`blacksmith_change_${uuidv4().substring(0, 8)}`)
    .setLabel("Change Item")
    .setStyle(ButtonStyle.Secondary);

  const closeButton = new ButtonBuilder()
    .setCustomId(`blacksmith_close_${uuidv4().substring(0, 8)}`)
    .setLabel("Close")
    .setStyle(ButtonStyle.Danger);

  const row = new ActionRowBuilder().addComponents(
    rerollButton,
    changeItemButton,
    closeButton
  );

  try {
    await interaction.update({ embeds: [embed], components: [row] });
  } catch (error) {
    if (error.code === 10062) {
      // Unknown interaction error

      return; // Exit early as we can't continue with an expired interaction
    } else {
      console.error("[Talk Command] Error updating item selection:", error);
      return; // Exit early as we can't continue
    }
  }

  // Create collector for buttons
  // Store collectors in a global map if not already created
  if (!global.blacksmithCollectors) {
    global.blacksmithCollectors = new Map();
  }

  // Remove old collector if it exists for this user
  const oldCollector = global.blacksmithCollectors.get(interaction.user.id);
  if (oldCollector) {
    oldCollector.stop();
  }

  const collector = interaction.message.createMessageComponentCollector({
    filter: (i) =>
      (i.customId.startsWith("blacksmith_reroll_") ||
        i.customId.startsWith("blacksmith_change_") ||
        i.customId.startsWith("blacksmith_close_")) &&
      i.user.id === interaction.user.id,
    time: 300000,
  });

  // Store the new collector in the global map
  global.blacksmithCollectors.set(interaction.user.id, collector);

  // Add end event handler to clean up
  collector.on("end", (collected, reason) => {
    // Only remove components if the collector ended due to timeout, not due to a button click
    if (reason === "time") {
      try {
        // Check if interaction.message exists before trying to edit it
        if (interaction.message) {
          interaction.message.edit({ components: [] }).catch(() => {});
        }
      } catch (error) {
        console.error(
          "[Talk Command] Error removing components on collector end:",
          error
        );
      }
    }

    // Remove the collector from the global map
    if (
      global.blacksmithCollectors &&
      global.blacksmithCollectors.get(interaction.user.id) === collector
    ) {
      global.blacksmithCollectors.delete(interaction.user.id);
    }
  });

  collector.on("collect", async (buttonInteraction) => {
    if (buttonInteraction.customId.startsWith("blacksmith_reroll_")) {
      // Extract the selectedValue from the button's customId
      const customIdParts = buttonInteraction.customId.split("_");
      // The format is blacksmith_reroll_source_identifier_uuid
      // We need to reconstruct the selectedValue (source_identifier)
      const buttonSelectedValue = customIdParts.slice(2, -1).join("_");
      try {
        await handleReroll(
          buttonInteraction,
          selectedNPC,
          allItems,
          buttonSelectedValue || selectedValue
        );
      } catch (error) {
        if (error.code === 40060) {
          // Interaction has already been acknowledged
        } else {
          console.error("[talk.js] Error in handleReroll:", error);
          try {
            await buttonInteraction.followUp({
              content: "An error occurred. Please try again.",
              ephemeral: true,
            });
          } catch (followUpError) {
            console.error("[talk.js] Error sending followUp:", followUpError);
          }
        }
      }
    } else if (buttonInteraction.customId.startsWith("blacksmith_change_")) {
      // Get player's reforgeable items from inventory and equipment
      const { formatNumber } = require("../utils/displayUtils");
      const { CURRENCY } = require("../gameConfig");
      const playerData = await getPlayerData(buttonInteraction.user.id);
      const storageItems = playerData.inventory?.items || {};
      const equippedItems = playerData.inventory?.equipment || [];

      const reforgableItems = [];

      // Add items from storage (/storage command items)
      for (const [itemKey, itemData] of Object.entries(storageItems)) {
        if (itemData.amount > 0) {
          const item = allItems[itemKey];
          if (item && canReforge(item)) {
            // Read reforge from data_json
            let currentReforge = "None";
            if (itemData.data_json) {
              const dataJson =
                typeof itemData.data_json === "string"
                  ? JSON.parse(itemData.data_json)
                  : itemData.data_json;
              currentReforge = dataJson.reforge
                ? getDynamicReforgeName(dataJson.reforge, item) || "None"
                : "None";
            }
            reforgableItems.push({
              label: `${item.name} (${currentReforge})`,
              value: `storage_${itemKey}`,
              description: `From Storage - Current: ${currentReforge}`,
              emoji: item.emoji,
            });
          }
        }
      }

      // Add equipped items (/inventory command equipped items)
      for (const equippedItem of equippedItems) {
        if (equippedItem && equippedItem.isEquipped && equippedItem.itemKey) {
          const item = allItems[equippedItem.itemKey];
          if (item && canReforge(item)) {
            // Read reforge from data_json in database
            let currentReforge = "None";
            try {
              const db = require("../utils/database");

              const currentItem = await new Promise((resolve, reject) => {
                db.get(
                  "SELECT data_json FROM player_equipment WHERE equipment_id = ? AND discord_id = ?",
                  [equippedItem.id, buttonInteraction.user.id],
                  (err, row) => (err ? reject(err) : resolve(row))
                );
              });

              if (currentItem?.data_json) {
                const dataJson = JSON.parse(currentItem.data_json);
                currentReforge = dataJson.reforge
                  ? getDynamicReforgeName(dataJson.reforge, item) || "None"
                  : "None";
              }
            } catch (error) {
              console.error("Error reading reforge data:", error);
            }

            reforgableItems.push({
              label: `${item.name} (${currentReforge})`,
              value: `equipment_${equippedItem.id}`,
              description: `Equipped - Current: ${currentReforge}`,
              emoji: item.emoji,
            });
          }
        }
      }

      // Create greeting embed with item selection
      const embed = new EmbedBuilder()
        .setTitle(`<:npc_blacksmith:1378078835752439879> ${selectedNPC.name}`)
        .setDescription("What do you want to reforge today?")
        .setColor(EMBED_COLORS.BROWN)
        .addFields({
          name: "Your Coins",
          value: `${CURRENCY.purseEmoji} ${formatNumber(playerData.coins || 0)}`,
          inline: true,
        });

      const selectMenu = new StringSelectMenuBuilder()
        .setCustomId(`blacksmith_item_select_${uuidv4()}`)
        .setPlaceholder("Choose an item to reforge")
        .addOptions(reforgableItems.slice(0, 25)); // Discord limit

      const row = new ActionRowBuilder().addComponents(selectMenu);

      try {
        await buttonInteraction.update({ embeds: [embed], components: [row] });
      } catch (error) {
        if (error.code === 10062) {
          // Unknown interaction error
          return; // Exit early as we can't continue with an expired interaction
        } else {
          console.error("[Talk Command] Error updating change item:", error);
          return; // Exit early as we can't continue
        }
      }

      // Create collector for item selection
      const reply = await buttonInteraction.message;

      // Store collectors in a global map if not already created
      if (!global.blacksmithCollectors) {
        global.blacksmithCollectors = new Map();
      }

      // Remove old collector if it exists for this user
      const oldCollector = global.blacksmithCollectors.get(
        buttonInteraction.user.id
      );
      if (oldCollector) {
        oldCollector.stop();
      }

      // Create new collector for item selection
      const newCollector = reply.createMessageComponentCollector({
        filter: (i) =>
          i.customId.startsWith("blacksmith_item_select_") &&
          i.user.id === buttonInteraction.user.id,
        time: 300000,
      });

      // Store the new collector in the global map
      global.blacksmithCollectors.set(buttonInteraction.user.id, newCollector);

      newCollector.on("collect", async (selectInteraction) => {
        try {
          await handleItemSelection(
            selectInteraction,
            selectedNPC,
            allItems,
            playerData
          );
        } catch (error) {
          if (error.code === 10062) {
            // Unknown interaction error
          } else {
            console.error(
              "[Talk Command] Error handling item selection from new collector:",
              error
            );
            try {
              await selectInteraction.reply({
                content: "An error occurred. Please try again.",
                ephemeral: true,
              });
            } catch (replyError) {
              console.error(
                "[Talk Command] Error sending error reply:",
                replyError
              );
            }
          }
        }
      });

      newCollector.on("end", (collected, reason) => {
        // Only remove components if the collector ended due to timeout, not due to a button click
        if (reason === "time") {
          // Clean up the message when collector expires
          buttonInteraction.editReply({ components: [] }).catch(() => {});
        }

        // Remove the collector from the global map
        if (
          global.blacksmithCollectors &&
          global.blacksmithCollectors.get(buttonInteraction.user.id) ===
            newCollector
        ) {
          global.blacksmithCollectors.delete(buttonInteraction.user.id);
        }
      });
    } else if (buttonInteraction.customId.startsWith("blacksmith_close_")) {
      const closeEmbed = new EmbedBuilder()
        .setTitle("<:npc_blacksmith:1378078835752439879> Blacksmith")
        .setDescription(
          "Thank you for visiting! Come back anytime you need to reforge your equipment."
        )
        .setColor(EMBED_COLORS.BROWN);
      try {
        await buttonInteraction.update({
          content: "",
          embeds: [closeEmbed],
          components: [],
        });
      } catch (error) {
        if (error.code === 10062) {
          // Unknown interaction error
        } else {
          console.error(
            "[Talk Command] Error updating close interaction:",
            error
          );
        }
      }
    }
  });
}

// Reforge embed utilities available if needed

module.exports = {
  data: new SlashCommandBuilder()
    .setName("talk")
    .setDescription("Talk to an NPC")
    .addStringOption((option) =>
      option
        .setName("npc")
        .setDescription("Select an NPC to talk to")
        .setRequired(true)
        .setAutocomplete(true)
    ),
  async autocomplete(interaction) {
    await autocomplete(interaction);
  },
  handlePetUpgradeSelection,
  async execute(interaction) {
    let character;
    const userId = interaction.user.id;

    try {
      character = await getPlayerData(userId);

      if (!character)
        return interaction.reply({
          content:
            "You don't have a character yet! Visit the setup channel to create one.",
          flags: [MessageFlags.Ephemeral],
        });

      if (!checkRankPermission(character, "MEMBER")) {
        return interaction.reply({
          content:
            "You don't have permission to use this command (Rank Error).",
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Check both field names for backwards compatibility
      if (!character.current_region && !character.current_region) {
        return interaction.reply({
          content:
            "Error: Your current region could not be determined. Please try warping to a region first.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const currentRegionKey =
        character.current_region || character.current_region;

      // Check if player is busy with any activity
      const currentActivity = await getCurrentActivity(userId);

      if (currentActivity) {
        return await warnUserBusy(interaction, currentActivity);
      }

      const selectedNPCName = interaction.options.getString("npc");
      const npcKey = Object.keys(NPCS).find(
        (key) => NPCS[key].name === selectedNPCName
      );
      const selectedNPC = npcKey ? NPCS[npcKey] : null;

      if (!selectedNPC) {
        console.warn(
          `[Talk Command] NPC not found or invalid: ${selectedNPCName} for user ${userId}.`
        );
        return interaction.reply({
          content: `NPC "${selectedNPCName}" not found. Please select a valid NPC.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      if (
        !selectedNPC.foundInRegions ||
        !selectedNPC.foundInRegions.includes(currentRegionKey)
      ) {
        console.warn(
          `[Talk Command] NPC ${npcKey} (${selectedNPC.name}) not available in region ${currentRegionKey} for user ${userId}.`
        );
        return interaction.reply({
          content: `${selectedNPC.name} is not available in this region.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      let allItems;
      try {
        allItems = configManager.getAllItems();
        if (!allItems)
          throw new Error("getAllItems returned null or undefined");
      } catch (configError) {
        console.error(
          "[Talk Command] Error loading item data: [TLK-CFG-ITM-ERR]",
          configError
        );
        return interaction.reply({
          content:
            "[TLK-CFG-ITM-ERR] Failed to load item definitions. Please report this error code to an Admin.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      if (selectedNPC.type === NPC_TYPES.BANKER) {
        return await showBankingMenu(
          interaction,
          {
            current_region: character.current_region,
            rank: character.rank,
            currencies: {
              coins: character.coins || 0,
              bank: character.bank || 0,
            },
          },
          selectedNPC
        );
      }
      // Handle Kat NPC (PET_SITTER type)
      else if (
        selectedNPC.type === NPC_TYPES.PET_SITTER &&
        selectedNPC.key === "KAT"
      ) {
        return await handleKatPetUpgradeInteraction(
          interaction,
          character,
          selectedNPC,
          allItems
        );
      }
      // Handle Blacksmith NPC (REFORGE_MASTER type)
      else if (selectedNPC.type === "REFORGE_MASTER") {
        return await handleBlacksmithInteraction(
          interaction,
          character,
          selectedNPC,
          allItems
        );
      }
      // Special handling for Baker (EVENT_MERCHANT type)
      else if (
        selectedNPC.type === "EVENT_MERCHANT" &&
        selectedNPC.key === "BAKER"
      ) {
        // Get current time state from bot.js (already required at the top)
        const timeState = bot.getTimeState ? bot.getTimeState() : null;

        if (!timeState) {
          console.error("[Talk-Baker] Failed to get time state from bot.js");
          return interaction.reply({
            content:
              "Error: Couldn't retrieve game time information. Please report this to an admin.",
            flags: [MessageFlags.Ephemeral],
          });
        }

        // Check if Baker is available according to appearance conditions
        if (!isBakerSpawnWindow(timeState)) {
          const embed = new EmbedBuilder()
            .setColor(EMBED_COLORS.GOLD)
            .setTitle(
              `${selectedNPC.emoji || "<:new_year_cake:1372914056641511484>"} ${
                selectedNPC.name
              }`
            )
            .setDescription(
              selectedNPC.dialogue?.not_spawn_window?.[0]?.replace(
                /{year}/g,
                getEndingYear(timeState) + 1
              ) ||
                "The Baker isn't ready yet. Come back closer to the New Year!"
            );
          return interaction.reply({ embeds: [embed] });
        }

        return await handleBakerCakeInteraction(
          interaction,
          character,
          selectedNPC,
          timeState
        );
      } // Handle Maxwell NPC (MAGICAL_POWER_MASTER type)
      else if (selectedNPC.type === "MAGICAL_POWER_MASTER") {
        return await handleMaxwellInteraction(interaction);
      }
      // Handle Shady Akin NPC (IDENTITY_CHANGER type)
      else if (selectedNPC.type === "IDENTITY_CHANGER") {
        return await handleShadyAkinInteraction(
          interaction,
          character,
          selectedNPC
        );
      }
      // Handle Maddox NPC (SLAYER_NPC type)
      else if (
        selectedNPC.type === "SLAYER_NPC" &&
        selectedNPC.key === "MADDOX"
      ) {
        return await handleMaddoxSlayerInteraction(
          interaction,
          character,
          selectedNPC
        );
      }
      // Handle Elizabeth NPC (COMMUNITY_SHOP type)
      else if (
        selectedNPC.type === NPC_TYPES.COMMUNITY_SHOP &&
        selectedNPC.key === "ELIZABETH"
      ) {
        return await handleElizabethCommunityShopInteraction(
          interaction,
          character,
          selectedNPC
        );
      }
      // Generic Merchant Logic (Only runs if type is MERCHANT and not overridden like Bea)
      else if (selectedNPC.type === NPC_TYPES.MERCHANT) {
        // For merchants, we need currency information for display
        const coinData = await getPlayerData(userId);
        const playerCoins = coinData?.coins || 0;

        // Get shop limits for this NPC
        const timeState = bot.getTimeState ? bot.getTimeState() : null;
        let shopLimits = null;
        if (timeState) {
          const { getNPCShopLimits } = require("../utils/shopLimits");
          shopLimits = await getNPCShopLimits(userId, selectedNPC, timeState);
        }

        let buyingOptions;
        let embed;
        let shopData; // Declare shopData variable for scope access
        if (selectedNPC.key === "BEA") {
          // Special: Show all Bee Pet rarities and prices
          const beePet = allItems["BEE_PET"];
          if (beePet) {
            // Find max name length for alignment
            const names = BEE_PET_RARITIES.map(
              (rarity) =>
                `Bee Pet (${rarity.charAt(0) + rarity.slice(1).toLowerCase()})`
            );
            const maxNameLen = Math.max(...names.map((n) => n.length));
            buyingOptions = BEE_PET_RARITIES.map((rarity) => {
              const name = `Bee Pet (${
                rarity.charAt(0) + rarity.slice(1).toLowerCase()
              })`;
              const price = BEE_PET_RARITY_PRICES[rarity].toLocaleString();
              // Pad name and price for alignment
              const paddedName = name.padEnd(maxNameLen, " ");
              const paddedPrice = price.padStart(7, " ");

              // Add shop limits for bee pets
              let limitDisplay = "";
              if (shopLimits && `BEE_PET_${rarity}` in shopLimits) {
                const remaining = shopLimits[`BEE_PET_${rarity}`];
                if (remaining === 0) {
                  limitDisplay = " `(Limit reached)`";
                } else {
                  limitDisplay = ` \`(${remaining} left)\``;
                }
              }

              return `${
                beePet.emoji || "❓"
              } \`${paddedName} ${paddedPrice}\` ${CURRENCY.purseEmoji}${limitDisplay}`;
            }).join("\n");
          } else {
            buyingOptions = "Bee Pet data missing!";
          }
          const dynamicGreeting = await getNPCGreeting(selectedNPC.key);
          embed = new EmbedBuilder()
            .setColor(EMBED_COLORS.BLUE)
            .setTitle(`${selectedNPC.emoji || "❓"} ${selectedNPC.name}'s Shop`)
            .setDescription(
              dynamicGreeting +
                "\n\n*Select Bee Pet to choose rarity and price.*"
            )
            .addFields({
              name: "Items for Sale",
              value: buyingOptions,
              inline: false,
            });
        } else {
          const shopData = await generateShopDisplay(
            selectedNPC,
            allItems,
            playerCoins,
            false,
            shopLimits
          );
          embed = shopData.embed;
        }

        const buyButtonId = `talk_buy_${uuidv4()}`;
        const sellButtonId = `talk_sell_${uuidv4()}`;
        const showDetailsButtonId = `talk_show_details_${uuidv4()}`;

        const buttons = [
          new ButtonBuilder()
            .setCustomId(buyButtonId)
            .setLabel("Buy")
            .setStyle(ButtonStyle.Primary),
          new ButtonBuilder()
            .setCustomId(sellButtonId)
            .setLabel("Sell")
            .setStyle(ButtonStyle.Primary),
        ];

        // Only add Show Details button if there are details to show
        if (selectedNPC.key !== "BEA" && shopData && shopData.hasDetails) {
          buttons.push(
            new ButtonBuilder()
              .setCustomId(showDetailsButtonId)
              .setLabel("Show Details")
              .setStyle(ButtonStyle.Secondary)
          );
        }

        const row = new ActionRowBuilder().addComponents(...buttons);
        await interaction.reply({ embeds: [embed], components: [row] });
        const reply = await interaction.fetchReply();

        // Store the shop message ID for this user
        shopMessageIds.set(userId, reply.id);

        // Store the current details state for this user
        let showingDetails = false;

        // Collector for Buy/Sell/Show Details buttons
        const collector = reply.createMessageComponentCollector({
          filter: (i) =>
            (i.customId === buyButtonId ||
              i.customId === sellButtonId ||
              i.customId === showDetailsButtonId) &&
            i.user.id === userId,
          time: 300000,
        });

        collector.on("collect", async (i) => {
          try {
            if (i.customId === buyButtonId) {
              await executeBuyFlow(i, selectedNPC, allItems);
            } else if (i.customId === sellButtonId) {
              await i.deferUpdate();
              await executeSellFlow(i, selectedNPC, allItems);
            } else if (i.customId === showDetailsButtonId) {
              await i.deferUpdate();

              // Toggle details state
              showingDetails = !showingDetails;

              // Get fresh shop limits
              const timeState = bot.getTimeState ? bot.getTimeState() : null;
              let currentShopLimits = null;
              if (timeState) {
                const { getNPCShopLimits } = require("../utils/shopLimits");
                currentShopLimits = await getNPCShopLimits(
                  userId,
                  selectedNPC,
                  timeState
                );
              }

              // Regenerate shop display with new details state
              const updatedShopData = await generateShopDisplay(
                selectedNPC,
                allItems,
                playerCoins,
                showingDetails,
                currentShopLimits
              );

              // Update button states
              const updatedButtons = [
                new ButtonBuilder()
                  .setCustomId(buyButtonId)
                  .setLabel("Buy")
                  .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                  .setCustomId(sellButtonId)
                  .setLabel("Sell")
                  .setStyle(ButtonStyle.Primary),
              ];

              if (selectedNPC.key !== "BEA" && updatedShopData.hasDetails) {
                updatedButtons.push(
                  new ButtonBuilder()
                    .setCustomId(showDetailsButtonId)
                    .setLabel(showingDetails ? "Hide Details" : "Show Details")
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(false) // Enable after first click
                );
              }

              const updatedRow = new ActionRowBuilder().addComponents(
                ...updatedButtons
              );

              // Update the message
              await i.editReply({
                embeds: [updatedShopData.embed],
                components: [updatedRow],
              });
            }
          } catch (error) {
            console.error(
              `[Talk Collector] Error handling ${i.customId} for user ${userId}:`,
              error
            );
            try {
              if (!i.replied && !i.deferred) {
                await i.reply({
                  content:
                    "An error occurred while processing your request. Please try again.",
                  flags: [MessageFlags.Ephemeral],
                });
              } else {
                await i.followUp({
                  content:
                    "An error occurred while processing your request. Please try again.",
                  flags: [MessageFlags.Ephemeral],
                });
              }
            } catch (replyError) {
              console.error(
                "[Talk Collector] Failed to send error message:",
                replyError
              );
            }
          }
        });
      } else {
        // Handle unknown NPC type
        return interaction.reply({
          content: `I don't know how to interact with ${selectedNPC.name} yet.`,
          flags: [MessageFlags.Ephemeral],
        });
      }
    } catch (error) {
      console.error(
        `[Talk Command] Error during talk command execution for user ${userId}:`,
        error
      );
      return interaction.reply({
        content:
          "An error occurred while processing the command. Please try again later.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  },
};

/**
 * Handle Shady Akin interaction for identity changing
 * @param {object} interaction - Discord interaction
 * @param {object} character - Player character data
 * @param {object} npc - NPC data
 */
async function handleShadyAkinInteraction(interaction, character, npc) {
  const userId = interaction.user.id;
  const IDENTITY_CHANGE_COST = 50000;
  const COOLDOWN_HOURS = 24;
  const COOLDOWN_MS = COOLDOWN_HOURS * 60 * 60 * 1000;

  try {
    // Check if player is on cooldown
    const lastIdentityChange = character.last_identity_change || 0;
    const now = Date.now();
    const timeSinceLastChange = now - lastIdentityChange;

    if (timeSinceLastChange < COOLDOWN_MS) {
      // Player is on cooldown
      const timeRemaining = COOLDOWN_MS - timeSinceLastChange;
      const availableTimestamp = Math.floor((now + timeRemaining) / 1000);

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.PINK_RED)
        .setTitle(`${npc.emoji} ${npc.name}`)
        .setDescription(
          `${npc.dialogue.cooldown.join("\n")}\n\n` +
            `**Available:** <t:${availableTimestamp}:R>`
        )
        .setFooter({ text: "But we were just talking?" });

      return interaction.reply({ embeds: [embed] });
    }

    // Player is not on cooldown, show the identity change offer
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GOLD)
      .setTitle(`${npc.emoji} ${npc.name}`)
      .setDescription(`${npc.dialogue.default.join("\n")}`)
      .setFooter({ text: "This guy looks real suspicious..." });

    const button = new ButtonBuilder()
      .setCustomId(`identity_change_${userId}`)
      .setLabel("Get a new identity")
      .setStyle(ButtonStyle.Primary);

    const row = new ActionRowBuilder().addComponents(button);

    await interaction.reply({ embeds: [embed], components: [row] });

    // Set up button collector
    const reply = await interaction.fetchReply();
    const collector = reply.createMessageComponentCollector({
      filter: (i) =>
        i.customId === `identity_change_${userId}` && i.user.id === userId,
      time: 300000, // 5 minutes
    });

    // Store collector reference for cleanup
    if (!global.shadyAkinCollectors) {
      global.shadyAkinCollectors = new Map();
    }

    // Clean up any existing collector for this user
    if (global.shadyAkinCollectors.has(userId)) {
      const oldCollector = global.shadyAkinCollectors.get(userId);
      oldCollector.stop("new_interaction");
    }

    global.shadyAkinCollectors.set(userId, collector);

    collector.on("collect", async (buttonInteraction) => {
      try {
        // Check cooldown first
        const currentPlayerData = await getPlayerData(userId);
        const lastIdentityChange = currentPlayerData?.last_identity_change || 0;
        const timeSinceLastChange = Date.now() - lastIdentityChange;
        const cooldownTime = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

        if (timeSinceLastChange < cooldownTime) {
          const timeRemaining = cooldownTime - timeSinceLastChange;
          const availableTimestamp = Math.floor(
            (Date.now() + timeRemaining) / 1000
          );

          const cooldownEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.PINK_RED)
            .setTitle(`${npc.emoji} ${npc.name}`)
            .setDescription(
              `*${npc.dialogue.cooldown.join("\n")}*\n\n` +
                `**Available:** <t:${availableTimestamp}:R>`
            )
            .setFooter({ text: "But we were just talking?" });

          return buttonInteraction.reply({
            embeds: [cooldownEmbed],
            ephemeral: true,
          });
        }

        // Check if player has enough coins
        const playerCoins = currentPlayerData?.coins || 0;

        if (playerCoins < IDENTITY_CHANGE_COST) {
          const insufficientEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.PINK_RED)
            .setTitle(`${npc.emoji} ${npc.name}`)
            .setDescription(
              `*You don't have enough coins for this service!*\n\n` +
                `**Required:** ${CURRENCY.emoji} ${IDENTITY_CHANGE_COST.toLocaleString()}\n` +
                `**You have:** ${CURRENCY.emoji} ${playerCoins.toLocaleString()}`
            )
            .setFooter({ text: "Come back when you have more coins..." });

          return buttonInteraction.reply({
            embeds: [insufficientEmbed],
            ephemeral: true,
          });
        }

        // Show username change modal
        const modal = new ModalBuilder()
          .setCustomId(`identity_change_modal_${userId}`)
          .setTitle("New Identity");

        const nameInput = new TextInputBuilder()
          .setCustomId("new_username")
          .setLabel("What's your new identity?")
          .setStyle(TextInputStyle.Short)
          .setRequired(true)
          .setMinLength(1)
          .setMaxLength(24)
          .setPlaceholder("Enter a new name (letters and numbers only)");

        const firstActionRow = new ActionRowBuilder().addComponents(nameInput);
        modal.addComponents(firstActionRow);

        await buttonInteraction.showModal(modal);
      } catch (error) {
        console.error("[Shady Akin] Error handling button interaction:", error);
        await buttonInteraction.reply({
          content: "An error occurred. Please try again.",
          ephemeral: true,
        });
      }
    });
  } catch (error) {
    console.error("[Shady Akin] Error in handleShadyAkinInteraction:", error);
    return interaction.reply({
      content: "An error occurred while talking to Shady Akin.",
      ephemeral: true,
    });
  }
}

/**
 * Validate character name - letters and numbers only
 * @param {string} name - Name to validate
 * @returns {boolean} - Whether the name is valid
 */
function isValidCharacterName(name) {
  return /^[a-zA-Z0-9]{1,24}$/.test(name);
}

/**
 * Handle Shady Akin modal submission for username change
 * @param {object} interaction - Discord modal interaction
 */
async function handleShadyAkinModalSubmit(interaction) {
  if (!interaction.isModalSubmit()) return false;
  if (!interaction.customId.startsWith("identity_change_modal_")) return false;

  const userId = interaction.user.id;
  const modalUserId = interaction.customId.split("_")[3];

  // Ensure the user who submitted is the same user who initiated
  if (userId !== modalUserId) {
    await interaction.reply({
      content: "An error occurred with the identity change.",
      ephemeral: true,
    });
    return true;
  }

  try {
    const newUsername = interaction.fields.getTextInputValue("new_username");
    const IDENTITY_CHANGE_COST = 50000;
    const IDENTITY_CHANGE_COOLDOWN = 24 * 60 * 60 * 1000; // 24 hours

    // Get current player data first for cooldown check
    const currentPlayerData = await getPlayerData(userId);

    // Check cooldown
    const lastIdentityChange = currentPlayerData?.last_identity_change || 0;
    const timeSinceLastChange = Date.now() - lastIdentityChange;

    if (timeSinceLastChange < IDENTITY_CHANGE_COOLDOWN) {
      const timeRemaining = IDENTITY_CHANGE_COOLDOWN - timeSinceLastChange;
      const availableTimestamp = Math.floor(
        (Date.now() + timeRemaining) / 1000
      );

      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription(
          `*You've already changed your identity recently. Come back later.*\n\n**Available:** <t:${availableTimestamp}:R>`
        );
      await interaction.reply({
        embeds: [errorEmbed],
      });
      return true;
    }

    // Validate username
    if (!isValidCharacterName(newUsername)) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription(
          "*Invalid username. Please use only letters and numbers.*"
        );
      await interaction.reply({
        embeds: [errorEmbed],
      });
      return true;
    }

    // Check if name is taken
    const { dbGet } = require("../utils/dbUtils");
    const nameTaken = await dbGet(
      "SELECT 1 FROM players WHERE LOWER(name) = LOWER(?)",
      [newUsername]
    );
    if (nameTaken) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription(
          "*This username is already taken. Please choose a different name.*"
        );
      await interaction.reply({
        embeds: [errorEmbed],
      });
      return true;
    }

    const playerCoins = currentPlayerData?.coins || 0;

    // Final coin check
    if (playerCoins < IDENTITY_CHANGE_COST) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription("*You don't have enough coins for this service!*");
      await interaction.reply({
        embeds: [errorEmbed],
      });
      return true;
    }

    // Perform the identity change
    // Previous username stored for potential logging if needed
    currentPlayerData.name = newUsername;
    currentPlayerData.last_identity_change = Date.now();

    // Deduct coins atomically
    const coinUpdateResult = await updateCurrencyAtomically(
      userId,
      -IDENTITY_CHANGE_COST,
      0
    );
    if (!coinUpdateResult) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setDescription("*Transaction failed! Please try again.*");
      await interaction.reply({
        embeds: [errorEmbed],
      });
      return true;
    }

    // Save only the updated fields (name and timestamp); coins already handled atomically
    await savePlayerData(
      userId,
      {
        name: newUsername,
        last_identity_change: currentPlayerData.last_identity_change,
        _allowUnknownName: true,
      },
      ["name", "last_identity_change"]
    );

    // Update personal channel name if player has one
    if (currentPlayerData.personal_channel_id) {
      console.log(
        `[Shady Akin] Player has personal channel: ${currentPlayerData.personal_channel_id}, attempting to rename to: ${newUsername}`
      );
      const { renamePersonalChannel } = require("../utils/channelManager");
      try {
        const renameSuccess = await renamePersonalChannel(
          interaction.client,
          newUsername,
          userId,
          currentPlayerData.personal_channel_id
        );
        if (renameSuccess) {
          console.log(
            `[Shady Akin] ✅ Successfully renamed channel for ${userId} to match name: ${newUsername}`
          );
        } else {
          console.warn(
            `[Shady Akin] ⚠️ Failed to rename channel for ${userId}`
          );
        }
      } catch (channelError) {
        console.error(
          `[Shady Akin] Error renaming personal channel for ${userId}:`,
          channelError
        );
        // don't fail the name change if channel rename fails
      }
    } else {
      console.log(
        `[Shady Akin] Player ${userId} does not have a personal channel to rename`
      );
    }

    // Update Discord nickname with new identity and current level
    try {
      if (interaction.member && interaction.guild) {
        // Get player's disblock level
        const disblockData = await getPlayerData(userId);
        const { calculateDisblockLevel } = require("../utils/disblockXpSystem");
        const integerLevel = calculateDisblockLevel(
          disblockData?.disblock_xp || 0
        ).level;
        const expectedNickname = `[${integerLevel}] ${newUsername}`;
        const currentNickname = interaction.member.displayName;

        // Only update if the nickname is different from expected
        if (currentNickname !== expectedNickname) {
          // Check if bot has permission to manage nicknames
          if (interaction.guild.members.me.permissions.has("ManageNicknames")) {
            // Check if the member is not the guild owner (can't change owner's nickname)
            if (interaction.member.id !== interaction.guild.ownerId) {
              await interaction.member.setNickname(expectedNickname);
            }
          }
        }
      }
    } catch (nicknameError) {
      console.error(
        `[Shady Akin] Error updating nickname for ${userId}:`,
        nicknameError
      );
    }

    // Update the original embed to show success
    const npc = NPCS.SHADY_AKIN;
    const successEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(`${npc.emoji} ${npc.name}`)
      .setDescription(
        `${npc.dialogue.success.join("\n")}\n\n` +
          `**New identity:** \`${newUsername}\``
      )
      .setFooter({ text: "Ok, that was weird? But seems legit." });

    // Respond to the modal first to prevent timeout
    await interaction.deferReply();

    // Try to edit the original message
    try {
      const originalMessage = await interaction.message;
      if (originalMessage) {
        await originalMessage.edit({
          embeds: [successEmbed],
          components: [],
        });
      }
    } catch (editError) {
      console.error("[Shady Akin] Error updating original message:", editError);
    }

    // Stop the button collector to prevent further interactions
    try {
      if (
        global.shadyAkinCollectors &&
        global.shadyAkinCollectors.has(userId)
      ) {
        const collector = global.shadyAkinCollectors.get(userId);
        collector.stop("identity_changed");
        global.shadyAkinCollectors.delete(userId);
      }
    } catch (collectorError) {
      console.error("[Shady Akin] Error stopping collector:", collectorError);
    }

    // Delete the deferred reply since success is shown in the original message
    try {
      await interaction.deleteReply();
    } catch (deleteError) {
      console.error("[Shady Akin] Error deleting deferred reply:", deleteError);
    }

    return true;
  } catch (error) {
    console.error("[Shady Akin] Error handling modal submission:", error);
    const errorEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setDescription(
        "*An error occurred during the identity change. Please try again.*"
      );

    // Check if we already deferred the reply
    if (interaction.deferred) {
      await interaction.editReply({
        embeds: [errorEmbed],
      });
    } else {
      await interaction.reply({
        embeds: [errorEmbed],
      });
    }
    return true;
  }
}

/**
 * Handles Maddox slayer NPC interaction
 * @param {object} interaction - Discord interaction
 * @param {object} character - Player character data
 * @param {object} npc - NPC data
 */
async function handleMaddoxSlayerInteraction(interaction, character) {
  const userId = interaction.user.id;

  // Import slayer level utilities
  const { getAllSlayerLevels } = require("../utils/slayerLevelUtils");

  // Check if player has an active slayer quest
  const activeQuest = character.active_slayer_quest;
  let questData = null;

  if (activeQuest) {
    try {
      questData = JSON.parse(activeQuest);
    } catch (error) {
      console.error("[Maddox] Error parsing active slayer quest:", error);
    }
  }

  if (questData) {
    // Player has an active quest - show progress

    // Get boss data and use the actual boss name from mob data
    const bossData = configManager.getAllMobs()[questData.type];
    const bossEmoji = bossData?.emoji || "❓";
    const bossName =
      bossData?.name ||
      questData.type
        .replace(/_/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase());
    const questName = `${bossEmoji} ${bossName}`;
    const combatExpGained = questData.combatExpGained || 0;
    const combatExpRequired = questData.combatExpRequired || 150;

    // Get eligible mobs with emojis dynamically from mobs data
    const allMobs = configManager.getAllMobs();
    const questMobData = allMobs[questData.type];
    const targetMobType = questMobData?.slayer?.targetMobType;

    // Use mob type to find all eligible mobs
    const eligibleMobs = Object.values(allMobs).filter(
      (mob) =>
        mob.mobType === targetMobType &&
        mob.spawnRegions &&
        mob.spawnRegions.length > 0
    );
    const eligibleMobsText = eligibleMobs
      .map((mob) => {
        const emoji = mob.emoji || "❓";
        const mobName =
          mob.name ||
          mob.key.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
        return `${emoji} **${mobName}**`;
      })
      .join("\n");

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle("<:npc_maddox:1389642069861208201> Maddox The Slayer")
      .setDescription(
        `Active Slayer Quest:\n` +
          `**${questName}**\n\n` +
          `${skillEmojis.combat} **${formatXP(combatExpGained)} / ${formatXP(combatExpRequired)} Combat EXP**\n\n` +
          `**Eligible Mobs:**\n` +
          eligibleMobsText
      )
      .setFooter({ text: "Use /combat to keep progressing" });

    const abandonButton = new ButtonBuilder()
      .setCustomId(`abandon_quest_${userId}`)
      .setLabel("Abandon")
      .setStyle(ButtonStyle.Danger);

    // Add Auto Quests button - fetch settings properly
    const playerSettings = await getPlayerData(userId);
    const autoQuestsEnabled = playerSettings?.settings?.autoQuests || false;
    const autoQuestsButton = new ButtonBuilder()
      .setCustomId(`toggle_auto_quests_${userId}`)
      .setLabel("Auto Quests")
      .setStyle(autoQuestsEnabled ? ButtonStyle.Success : ButtonStyle.Danger);

    const row = new ActionRowBuilder().addComponents(
      abandonButton,
      autoQuestsButton
    );
    await interaction.reply({ embeds: [embed], components: [row] });

    // Set up button collector for abandon and auto quests
    const reply = await interaction.fetchReply();
    const collector = reply.createMessageComponentCollector({
      filter: (i) =>
        (i.customId === `abandon_quest_${userId}` ||
          i.customId === `toggle_auto_quests_${userId}`) &&
        i.user.id === userId,
      time: 300000,
    });

    collector.on("collect", async (i) => {
      try {
        if (i.customId === `abandon_quest_${userId}`) {
          await handleSlayerQuestAbandon(i);
        } else if (i.customId === `toggle_auto_quests_${userId}`) {
          await handleAutoQuestsToggle(i);
        }
      } catch (error) {
        console.error("[Maddox] Error handling button interaction:", error);
        await i.reply({
          content: "An error occurred. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    });

    collector.on("end", () => {
      // Disable buttons after timeout
      const disabledRow = new ActionRowBuilder().addComponents(
        abandonButton.setDisabled(true),
        autoQuestsButton.setDisabled(true)
      );
      interaction.editReply({ components: [disabledRow] }).catch(() => {});
    });
  } else {
    // Player doesn't have an active quest - show quest selection
    // const playerCoins = character.coins || 0; // Currently unused

    // Get slayer levels for display
    const slayerXpData = character.slayerXp || {};
    const slayerLevels = getAllSlayerLevels(slayerXpData);

    // Import the same progress bar function used in skills
    const { createProgressBar } = require("../utils/displayUtils");

    // formatXP is now imported globally from utils/formatUtils

    // Calculate slayer stats display
    const zombieLevel = slayerLevels.zombie;
    const spiderLevel = slayerLevels.spider;

    // Create progress bars like skills command
    const zombieProgressBar = createProgressBar(
      zombieLevel.currentLevelExp,
      zombieLevel.requiredExpForNextLevel,
      10
    );
    const spiderProgressBar = createProgressBar(
      spiderLevel.currentLevelExp,
      spiderLevel.requiredExpForNextLevel,
      10
    );

    // Create XP text like skills command
    const isZombieMaxLevel = zombieLevel.requiredExpForNextLevel === Infinity;
    const zombieXpText = isZombieMaxLevel
      ? "`MAX LEVEL`"
      : `\`${formatXP(zombieLevel.currentLevelExp)}/${formatXP(zombieLevel.requiredExpForNextLevel)} XP\``;

    const isSpiderMaxLevel = spiderLevel.requiredExpForNextLevel === Infinity;
    const spiderXpText = isSpiderMaxLevel
      ? "`MAX LEVEL`"
      : `\`${formatXP(spiderLevel.currentLevelExp)}/${formatXP(spiderLevel.requiredExpForNextLevel)} XP\``;

    // Create fields like skills command
    const slayerFields = [
      {
        name: `<:revenant_horror:1389646540460658970> Zombie Slayer (Level ${zombieLevel.level})`,
        value: `${zombieProgressBar}\n${zombieXpText}`,
        inline: true,
      },
      {
        name: `<:mob_spider:1370526342927618078> Spider Slayer (Level ${spiderLevel.level})`,
        value: `${spiderProgressBar}\n${spiderXpText}`,
        inline: true,
      },
      {
        name: "\u200b", // Empty field for spacing
        value: "\u200b",
        inline: true,
      },
    ];

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle("<:npc_maddox:1389642069861208201> Maddox The Slayer")
      .setDescription(
        "You can purchase the rights to hunt down a Boss from me.\nWhat boss would you like to Slay?\n\n**Your slayer stats**"
      )
      .addFields(slayerFields)
      .setFooter({ text: "Select a Slayer Quest to begin" });

    // Add Auto Quests button for quest selection menu too - fetch settings properly
    const playerSettings = await getPlayerData(userId);
    const autoQuestsEnabled = playerSettings?.settings?.autoQuests || false;
    const autoQuestsButton = new ButtonBuilder()
      .setCustomId(`toggle_auto_quests_${userId}`)
      .setLabel("Auto Quests")
      .setStyle(autoQuestsEnabled ? ButtonStyle.Success : ButtonStyle.Danger);

    const selectMenuId = `slayer_quest_${uuidv4()}`;
    // Dynamically generate quest options from mobs with slayer field
    const allMobs = configManager.getAllMobs();
    const questOptions = [];
    for (const [mobKey, mobData] of Object.entries(allMobs)) {
      if (mobData.slayer) {
        questOptions.push({
          label:
            mobData.name ||
            mobKey.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
          description: `EXP to Spawn: ${mobData.slayer.combatExpRequired} (${mobData.slayer.questCost.toLocaleString()} coins) | Slayer EXP: ${mobData.slayer.xpReward}`,
          value: mobKey,
        });
      }
    }

    // Build select menu options with dynamic emojis
    const selectMenuOptions = questOptions.map((quest) => {
      // Use the actual boss emoji for the quest
      const bossData = configManager.getAllMobs()[quest.value];
      const emoji = bossData?.emoji || "❓";

      return {
        label: quest.label,
        description: quest.description,
        value: quest.value,
        emoji: emoji,
      };
    });

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(selectMenuId)
      .setPlaceholder("Choose a Slayer Quest...")
      .addOptions(selectMenuOptions);

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    const buttonRow = new ActionRowBuilder().addComponents(autoQuestsButton);
    await interaction.reply({
      embeds: [embed],
      components: [selectRow, buttonRow],
    });

    const reply = await interaction.fetchReply();
    const collector = reply.createMessageComponentCollector({
      filter: (i) =>
        (i.customId === selectMenuId ||
          i.customId === `toggle_auto_quests_${userId}`) &&
        i.user.id === userId,
      time: 300000,
    });

    collector.on("collect", async (i) => {
      try {
        if (i.customId === selectMenuId) {
          await handleSlayerQuestStart(i, i.values[0]);
        } else if (i.customId === `toggle_auto_quests_${userId}`) {
          await handleAutoQuestsToggle(i);
        }
      } catch (error) {
        console.error("[Maddox] Error handling interaction:", error);
        await i.reply({
          content: "An error occurred. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    });

    collector.on("end", () => {
      // Disable components after timeout
      const disabledSelectRow = new ActionRowBuilder().addComponents(
        selectMenu.setDisabled(true)
      );
      const disabledButtonRow = new ActionRowBuilder().addComponents(
        autoQuestsButton.setDisabled(true)
      );
      interaction
        .editReply({ components: [disabledSelectRow, disabledButtonRow] })
        .catch(() => {});
    });
  }
}

/**
 * Handles toggling auto quests setting
 * @param {object} interaction - Discord interaction
 */
async function handleAutoQuestsToggle(interaction) {
  const userId = interaction.user.id;

  try {
    // Get player data
    const playerData = await getPlayerData(userId);

    if (!playerData) {
      await interaction.reply({
        content: "Player data not found.",
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Get current setting value, defaulting to false if not set
    const currentAutoQuestsState = playerData.settings?.autoQuests || false;
    const newAutoQuestsState = !currentAutoQuestsState;

    // Atomically update the setting to prevent race conditions
    await updatePlayerSetting(userId, "autoQuests", newAutoQuestsState);

    // Get the original message to preserve embed and update only components
    const originalMessage = interaction.message;
    const originalEmbeds = originalMessage.embeds;

    // Recreate the components with updated button color
    const updatedComponents = [];

    // Check if this is from active quest view or quest selection view
    const hasAbandonButton = originalMessage.components.some((row) =>
      row.components.some(
        (component) =>
          component.customId && component.customId.includes("abandon_quest")
      )
    );

    if (hasAbandonButton) {
      // Active quest view - recreate abandon + auto quests buttons
      const abandonButton = new ButtonBuilder()
        .setCustomId(`abandon_quest_${userId}`)
        .setLabel("Abandon")
        .setStyle(ButtonStyle.Danger);

      const autoQuestsButton = new ButtonBuilder()
        .setCustomId(`toggle_auto_quests_${userId}`)
        .setLabel("Auto Quests")
        .setStyle(
          newAutoQuestsState ? ButtonStyle.Success : ButtonStyle.Danger
        );

      const row = new ActionRowBuilder().addComponents(
        abandonButton,
        autoQuestsButton
      );
      updatedComponents.push(row);
    } else {
      // Quest selection view - recreate select menu + auto quests button
      const selectMenu = originalMessage.components[0].components[0]; // Get original select menu
      const selectRow = new ActionRowBuilder().addComponents(
        StringSelectMenuBuilder.from(selectMenu)
      );

      const autoQuestsButton = new ButtonBuilder()
        .setCustomId(`toggle_auto_quests_${userId}`)
        .setLabel("Auto Quests")
        .setStyle(
          newAutoQuestsState ? ButtonStyle.Success : ButtonStyle.Danger
        );

      const buttonRow = new ActionRowBuilder().addComponents(autoQuestsButton);
      updatedComponents.push(selectRow, buttonRow);
    }

    // Update only the components, keep original embeds
    await interaction.update({
      embeds: originalEmbeds,
      components: updatedComponents,
    });
  } catch (error) {
    console.error("[Maddox] Error toggling auto quests:", error);
    await interaction.reply({
      content: "An error occurred while updating your auto quest settings.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * Handles abandoning a slayer quest
 * @param {object} interaction - Discord interaction
 */
async function handleSlayerQuestAbandon(interaction) {
  const userId = interaction.user.id;

  // Get player data
  const playerData = await getPlayerData(userId);

  if (!playerData?.active_slayer_quest) {
    await interaction.reply({
      content: "You don't have an active slayer quest to abandon.",
      flags: [MessageFlags.Ephemeral],
    });
    return;
  }

  // Clear the active quest
  playerData.active_slayer_quest = null;

  try {
    await savePlayerData(userId, playerData, ["active_slayer_quest"]);
  } catch (saveError) {
    console.error("[Slayer Quest] Error abandoning quest:", saveError);
    await interaction.reply({
      content:
        "Failed to abandon quest due to a database error. Please try again.",
      flags: [MessageFlags.Ephemeral],
    });
    return;
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.PINK_RED)
    .setTitle(
      "<:npc_maddox:1389642069861208201> Maddox The Disappointed Slayer"
    )
    .setDescription("You have abandoned your slayer quest.")
    .setFooter({ text: "Talk to me again to start a new quest" });

  // add back button to return to maddox main menu
  const backButton = new ButtonBuilder()
    .setCustomId(`back_to_maddox_${userId}`)
    .setLabel("Back")
    .setStyle(ButtonStyle.Secondary);

  const row = new ActionRowBuilder().addComponents(backButton);

  await interaction.update({ embeds: [embed], components: [row] });

  // set up collector for back button
  const reply = await interaction.fetchReply();
  const collector = reply.createMessageComponentCollector({
    filter: (i) =>
      i.customId === `back_to_maddox_${userId}` && i.user.id === userId,
    time: 300000,
  });

  collector.on("collect", async (i) => {
    try {
      // get updated character data and show maddox main menu
      const updatedCharacter = await getPlayerData(userId);
      if (!updatedCharacter) {
        await i.reply({
          content: "Character not found.",
          flags: [MessageFlags.Ephemeral],
        });
        return;
      }

      // create a new interaction context that mimics the original talk interaction
      const fakeInteraction = {
        ...i,
        reply: i.update.bind(i), // use update instead of reply to replace the current message
        fetchReply: i.fetchReply.bind(i),
      };

      await handleMaddoxSlayerInteraction(fakeInteraction, updatedCharacter);
    } catch (error) {
      console.error("[Maddox] Error handling back button:", error);
      await i.reply({
        content: "An error occurred. Please try again.",
        flags: [MessageFlags.Ephemeral],
      });
    }
  });

  collector.on("end", () => {
    // disable back button after timeout
    const disabledRow = new ActionRowBuilder().addComponents(
      backButton.setDisabled(true)
    );
    interaction.editReply({ components: [disabledRow] }).catch(() => {});
  });
}

/**
 * Automatically starts a slayer quest for a player (used by auto quest system)
 * @param {string} userId - Discord user ID
 * @param {string} questType - The slayer quest type to start
 * @param {object} channel - Discord channel to send messages to (optional)
 * @returns {boolean} - Whether the quest was successfully started
 */
async function autoStartSlayerQuest(userId, questType, channel = null) {
  try {
    // Get quest configuration from mob data
    const bossData = configManager.getAllMobs()[questType];
    if (!bossData || !bossData.slayer) {
      console.log(`[Auto Quest] Invalid quest type: ${questType}`);
      return false;
    }

    const cost = bossData.slayer.questCost;
    const combatExpRequired = bossData.slayer.combatExpRequired;
    const targetMobType = bossData.slayer.targetMobType;

    // Get player data
    const playerData = await getPlayerData(userId);
    const playerCoins = playerData?.coins || 0;

    // Check if player already has an active quest
    if (playerData?.active_slayer_quest) {
      console.log(`[Auto Quest] Player ${userId} already has an active quest`);
      return false;
    }

    // Check if player has enough coins
    if (playerCoins < cost) {
      console.log(
        `[Auto Quest] Player ${userId} doesn't have enough coins for ${questType} (${playerCoins}/${cost})`
      );

      // Send message to channel if provided
      if (channel) {
        try {
          const bossEmoji = bossData?.emoji || "❓";
          const bossName =
            bossData?.name ||
            questType
              .replace(/_/g, " ")
              .replace(/\b\w/g, (l) => l.toUpperCase());

          const embed = new EmbedBuilder().setDescription(
            `Not enough coins to start ${bossEmoji} ${bossName} Slayer Quest.`
          );

          const message = await channel.send({ embeds: [embed] });

          // Delete the message after 5 seconds
          setTimeout(() => {
            message.delete().catch(console.error);
          }, 5000);
        } catch (messageError) {
          console.error(
            `[Auto Quest] Error sending insufficient coins message:`,
            messageError
          );
        }
      }

      return false;
    }

    // Set up quest data
    const questData = {
      type: questType,
      startTime: Date.now(),
      combatExpRequired: combatExpRequired,
      combatExpGained: 0,
      targetMobType: targetMobType,
      mob_kills_json: {},
    };

    // Use atomic coin update system for consistency with combat rewards
    console.log(
      `[Auto Quest] Before coin deduction: ${playerData.coins} coins`
    );

    // Import updateInventoryAtomically for consistent coin handling
    const { updateInventoryAtomically } = require("../utils/inventory");

    // Deduct coins atomically (negative amount to subtract)
    await updateInventoryAtomically(userId, -cost);
    console.log(
      `[Auto Quest] After coin deduction: deducted ${cost} coins using atomic update`
    );

    // Set up quest data and save only the quest (coins already updated atomically)
    playerData.active_slayer_quest = JSON.stringify(questData);

    // Save only the quest data (not currencies since it was handled atomically)
    console.log(`[Auto Quest] Saving quest data only`);
    await savePlayerData(userId, playerData, ["active_slayer_quest"]);
    console.log(`[Auto Quest] Player data saved successfully`);

    // Verify the atomic coin update by checking the database
    try {
      const verifyData = await getPlayerData(userId);
      console.log(
        `[Auto Quest] Verification: Player now has ${verifyData?.coins || 0} coins in database after atomic deduction`
      );
    } catch (verifyError) {
      console.error(
        `[Auto Quest] Error verifying coin deduction:`,
        verifyError
      );
    }

    console.log(
      `[Auto Quest] Successfully started ${questType} for player ${userId}`
    );
    return true;
  } catch (error) {
    console.error(`[Auto Quest] Error starting quest for ${userId}:`, error);
    return false;
  }
}

/**
 * Handles starting a slayer quest
 * @param {object} interaction - Discord interaction
 * @param {string} questType - The slayer quest type (e.g., 'revenant_horror_i')
 */
async function handleSlayerQuestStart(interaction, questType) {
  const userId = interaction.user.id;

  // Get quest configuration from mob data
  const bossData = configManager.getAllMobs()[questType];
  if (!bossData || !bossData.slayer) {
    await interaction.reply({
      content: "Invalid slayer quest type.",
      flags: [MessageFlags.Ephemeral],
    });
    return;
  }

  const cost = bossData.slayer.questCost;
  const combatExpRequired = bossData.slayer.combatExpRequired;
  const targetMobType = bossData.slayer.targetMobType;

  // Get player data
  const playerData = await getPlayerData(userId);
  const playerCoins = playerData?.coins || 0;

  // Check if player already has an active quest
  if (playerData?.active_slayer_quest) {
    await interaction.reply({
      content:
        "You already have an active slayer quest! Complete it first before starting a new one.",
      flags: [MessageFlags.Ephemeral],
    });
    return;
  }

  // Check if player has enough coins
  if (playerCoins < cost) {
    await interaction.reply({
      content: `You need ${cost.toLocaleString()} coins to start this quest. You have ${playerCoins.toLocaleString()} coins.`,
      flags: [MessageFlags.Ephemeral],
    });
    return;
  }

  // Set up quest data with kill tracking
  const questData = {
    type: questType,
    startTime: Date.now(),
    combatExpRequired: combatExpRequired,
    combatExpGained: 0, // Keep for backward compatibility, but will be calculated from kills
    targetMobType: targetMobType,
    mob_kills_json: {}, // Track kills per mob type: { "zombie": 5, "crypt_ghoul": 2 }
  };

  // Use atomic coin update system for consistency with auto-slayer and combat rewards
  const { updateInventoryAtomically } = require("../utils/inventory");

  try {
    // Deduct coins atomically (negative amount to subtract)
    await updateInventoryAtomically(userId, -cost);

    // Set up quest data and save it
    playerData.active_slayer_quest = JSON.stringify(questData);
    await savePlayerData(userId, playerData, ["active_slayer_quest"]);
  } catch (saveError) {
    console.error(`[Slayer Quest] Error saving slayer quest data:`, saveError);

    await interaction.reply({
      content:
        "Failed to start slayer quest due to a database error. Please try again.",
      flags: [MessageFlags.Ephemeral],
    });
    return;
  }

  // Create success embed
  // Get boss name and emoji dynamically from mob data
  const questName =
    bossData.name ||
    questType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  const bossEmoji = bossData?.emoji || "❓";

  const allMobs = configManager.getAllMobs();
  // Get eligible mobs with emojis dynamically from mobs data
  // Use mob type to find all eligible mobs
  const eligibleMobs = Object.values(allMobs).filter(
    (mob) =>
      mob.mobType === targetMobType &&
      mob.spawnRegions &&
      mob.spawnRegions.length > 0
  );
  const eligibleMobsText = eligibleMobs
    .map((mob) => {
      const emoji = mob.emoji || "❓";
      const mobName =
        mob.name ||
        mob.key.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
      return `${emoji} **${mobName}**`;
    })
    .join("\n");

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GREEN)
    .setTitle("<:npc_maddox:1389642069861208201> Maddox The Slayer")
    .setDescription(
      `You have started:\n` +
        `${bossEmoji} **${questName}** Quest\n` +
        `<:purse_coins:1367849116033482772> \`-${cost.toLocaleString()} Coins\`\n\n` +
        `**Combat EXP to Spawn:**\n` +
        `${skillEmojis.combat} **${combatExpRequired} Combat EXP**\n\n` +
        `**Eligible Mobs:**\n` +
        eligibleMobsText
    )
    .setFooter({ text: "Use /combat to start slaying!" });

  await interaction.reply({ embeds: [embed] });
}

/**
 * Handles modal submission for selling items
 * @param {object} interaction - The modal submit interaction
 * @returns {Promise<boolean>} - Whether the interaction was handled successfully
 */
async function handleSellQuantityModal(interaction) {
  if (!interaction.isModalSubmit()) return false;
  if (!interaction.customId.startsWith("sell_quantity_")) return false;

  try {
    // Use defer reply immediately to prevent interaction timeouts
    await interaction.deferReply();

    const userId = interaction.user.id;
    const customIdParts = interaction.customId.split("_");
    // The format is sell_quantity_itemKey_uuid
    const itemKey = customIdParts.slice(2, -1).join("_");

    // Get the quantity input
    const quantityInputStr =
      interaction.fields.getTextInputValue("sell_amount");

    // Get current player data for latest inventory quantities
    const allItems = configManager.getAllItems();
    const finalCharacterData = await getPlayerData(userId);
    const finalQuantity = finalCharacterData?.inventory?.items?.[itemKey] || 0;

    if (finalQuantity <= 0) {
      await interaction.editReply({
        content: "You no longer have any of this item to sell.",
      });
      return true;
    }

    // Parse the quantity input
    let quantityToSell = parseShorthandAmount(quantityInputStr, finalQuantity);

    if (isNaN(quantityToSell) || quantityToSell <= 0) {
      await interaction.editReply({
        content:
          'Invalid quantity. Please enter a positive number, shorthand (k/m), or "all".',
      });
      return true;
    }

    if (quantityToSell > finalQuantity) {
      quantityToSell = finalQuantity; // Cap at available quantity
    }

    const itemDetails = allItems[itemKey];
    if (!itemDetails || !itemDetails.sellable || itemDetails.sellPrice <= 0) {
      await interaction.editReply({
        content: "This item cannot be sold or has no value.",
      });
      return true;
    }

    // Process the sale
    const coinsEarned = quantityToSell * itemDetails.sellPrice;
    const itemsToChange = [{ itemKey: itemKey, amount: -quantityToSell }];

    await updateInventoryAtomically(userId, coinsEarned, itemsToChange);

    // Create success embed
    const successEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Item Sold")
      .setDescription(
        `Successfully sold ${quantityToSell.toLocaleString()} ${
          itemDetails.emoji || ""
        } **${itemDetails.name || itemKey}** for ${coinsEarned.toLocaleString()} ${
          CURRENCY.name
        }.`
      )
      .setFooter({
        text: "Your coins have been added to your purse.",
      });

    // Update the message to show success
    await interaction.editReply({
      embeds: [successEmbed],
      components: [],
    });

    // Set a timeout to delete the message after 15 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        console.error(
          "[HandleSellQuantityModal] Error deleting success message:",
          error
        );
        // Ignore errors if the message was already deleted
      }
    }, 15000);

    // Try to find the NPC that triggered this interaction
    let selectedNPC = null;

    // First try to determine the NPC and current page from customId
    console.log(
      `[HandleSellQuantityModal] Processing sell for item: ${itemKey}`
    );

    // Find NPC by checking all shop and sell menus in the channel
    try {
      // Get the last 10 messages in the channel to find shop/sell menus
      const messages = await interaction.channel.messages.fetch({ limit: 10 });

      // Loop through messages to find shop/sell menus
      for (const message of messages.values()) {
        if (!message.embeds || message.embeds.length === 0) continue;

        const embed = message.embeds[0];
        if (!embed.title) continue;

        console.log(
          `[HandleSellQuantityModal] Checking message with title: "${embed.title}"`
        );

        // Extract NPC name from different possible title formats
        let npcName = "";
        if (embed.title.includes("Sell Items to ")) {
          npcName = embed.title.split("Sell Items to ")[1]?.trim();
        } else if (embed.title.includes("'s Shop")) {
          npcName = embed.title.split("'s Shop")[0]?.trim();
          // Remove any prefix like emoji or other text before the NPC name
          npcName = npcName.replace(/^[^\w\s]+\s*/, "").trim();
        } else if (embed.title.includes("Merchant")) {
          // Try to extract merchant name from titles like "Fish Merchant's Shop"
          npcName = embed.title.split("'s")[0]?.trim();
        }

        if (!npcName) continue;
        console.log(
          `[HandleSellQuantityModal] Extracted NPC name: "${npcName}"`
        );

        // Find NPC by name with more flexible matching
        const npcKey = Object.keys(NPCS).find((key) => {
          const npc = NPCS[key];
          if (!npc.name) return false;

          // Check for direct match or partial match in either direction
          return (
            npc.name.toLowerCase() === npcName.toLowerCase() ||
            npc.name.toLowerCase().includes(npcName.toLowerCase()) ||
            npcName.toLowerCase().includes(npc.name.toLowerCase()) ||
            npc.name.replace(/[^\w\s]/g, "").toLowerCase() ===
              npcName.replace(/[^\w\s]/g, "").toLowerCase()
          );
        });

        if (npcKey) {
          selectedNPC = NPCS[npcKey];
          console.log(
            `[HandleSellQuantityModal] Found NPC: ${selectedNPC.name} (key: ${npcKey})`
          );
          break;
        } else {
          console.log(
            `[HandleSellQuantityModal] No NPC found for name: "${npcName}"`
          );
        }
      }
    } catch (error) {
      console.error("[HandleSellQuantityModal] Error finding NPC:", error);
    }

    // If we still couldn't find the NPC through messages, try a more direct approach
    if (!selectedNPC) {
      try {
        // Try to find the original interaction message that triggered the sell flow
        if (interaction.message && interaction.message.reference) {
          const messageContent = await interaction.channel.messages.fetch(
            interaction.message.reference.messageId
          );
          if (
            messageContent &&
            messageContent.embeds &&
            messageContent.embeds.length > 0
          ) {
            const title = messageContent.embeds[0].title || "";
            console.log(
              `[HandleSellQuantityModal] Checking reference message with title: "${title}"`
            );

            // Try to extract NPC name from different title formats
            let npcName = "";
            if (title.includes("Sell Items to ")) {
              npcName = title.split("Sell Items to ")[1]?.trim();
            } else if (title.includes("'s Shop")) {
              npcName = title.split("'s Shop")[0]?.trim();
              // Remove any emoji or special characters from the beginning
              npcName = npcName.replace(/^[^\w\s]+\s*/, "").trim();
            } else if (title.includes("Merchant")) {
              // For titles like "Fish Merchant's Shop"
              npcName = title.split("'s")[0]?.trim();
            } else {
              // Fallback: Try to get NPC name after the first space
              npcName = title.split(" ").slice(1).join(" ").trim();
            }

            if (npcName) {
              console.log(
                `[HandleSellQuantityModal] Extracted NPC name from reference: "${npcName}"`
              );

              // Try to find NPC with flexible matching
              const npcKey = Object.keys(NPCS).find((key) => {
                const npc = NPCS[key];
                if (!npc.name) return false;

                const npcNameLower = npcName.toLowerCase();
                const storedNameLower = npc.name.toLowerCase();

                // Check for direct match, partial match, or match without special characters
                return (
                  storedNameLower === npcNameLower ||
                  storedNameLower.includes(npcNameLower) ||
                  npcNameLower.includes(storedNameLower) ||
                  storedNameLower.replace(/[^\w\s]/g, "") ===
                    npcNameLower.replace(/[^\w\s]/g, "")
                );
              });

              if (npcKey) {
                selectedNPC = NPCS[npcKey];
                console.log(
                  `[HandleSellQuantityModal] Found NPC from reference: ${selectedNPC.name} (key: ${npcKey})`
                );
              }
            }
          }
        }
      } catch (error) {
        console.error(
          "[HandleSellQuantityModal] Error with direct NPC finding:",
          error
        );
      }
    }

    // As a last resort, try to find any merchant NPC if we still don't have one
    if (!selectedNPC) {
      console.log(
        "[HandleSellQuantityModal] Could not determine NPC, trying to find any merchant..."
      );
      // Find the first NPC that's a merchant
      const merchantKey = Object.keys(NPCS).find(
        (key) =>
          NPCS[key].type === "MERCHANT" || NPCS[key].type === "EVENT_MERCHANT"
      );

      if (merchantKey) {
        selectedNPC = NPCS[merchantKey];
        console.log(
          `[HandleSellQuantityModal] Using fallback NPC: ${selectedNPC.name} (key: ${merchantKey})`
        );
      }
    }

    // Determine current page from footer text if available
    let currentPage = 0;
    try {
      const messages = await interaction.channel.messages.fetch({ limit: 10 });
      for (const message of messages.values()) {
        if (message.embeds && message.embeds.length > 0) {
          const embed = message.embeds[0];
          if (
            embed.footer &&
            embed.footer.text &&
            embed.footer.text.includes("Page")
          ) {
            const pageMatch = embed.footer.text.match(/Page (\d+)/);
            if (pageMatch && pageMatch[1]) {
              currentPage = parseInt(pageMatch[1], 10) - 1; // Convert to 0-based index
              console.log(
                `[HandleSellQuantityModal] Found current page: ${currentPage}`
              );
              break;
            }
          }
        }
      }
    } catch (error) {
      console.log(
        "[HandleSellQuantityModal] Error finding page number:",
        error.message
      );
    }

    // Refresh both the shop menu and sell menu with updated data if possible
    if (interaction.channel && selectedNPC) {
      try {
        console.log(
          `[HandleSellQuantityModal] Attempting to refresh menus for NPC: ${selectedNPC.name} (${selectedNPC.key})`
        );

        // Get fresh character data to ensure we have the latest inventory and coins
        const freshCharacter = await getPlayerData(userId);
        if (!freshCharacter) {
          console.error(
            "[HandleSellQuantityModal] Failed to get fresh character data for refresh"
          );
          return true;
        }

        // Update the shop menu if it exists
        // Update the sell menu if it exists
        const sellMenuId = sellMenuMessageIds.get(userId);
        if (sellMenuId) {
          try {
            const sellMessage =
              await interaction.channel.messages.fetch(sellMenuId);
            if (sellMessage) {
              // Instead of calling executeSellFlow with a modified interaction,
              // we'll manually update the sell menu message with a new embed
              const freshCharacter = await getPlayerData(userId);
              if (freshCharacter) {
                const sellableItems = Object.entries(
                  freshCharacter.inventory?.items || {}
                )
                  .map(([itemKey, quantity]) => ({
                    itemKey,
                    quantity,
                    itemDetails: allItems[itemKey],
                    itemName: allItems[itemKey]?.name || itemKey,
                    sellPrice: allItems[itemKey]?.sellPrice || 0,
                    emoji: allItems[itemKey]?.emoji || "❓",
                  }))
                  .filter(
                    (item) =>
                      item.itemDetails?.sellable &&
                      item.quantity > 0 &&
                      item.sellPrice > 0
                  )
                  .sort((a, b) => a.itemName.localeCompare(b.itemName));

                const itemsPerPage = 25;
                const totalPages = Math.ceil(
                  sellableItems.length / itemsPerPage
                );
                // Use the current page from the interaction or default to 0
                const currentPageNum =
                  parseInt(interaction?.customId?.split("_").pop()) || 0;
                const safePage = Math.min(
                  Math.max(0, currentPageNum),
                  Math.max(0, totalPages - 1)
                );
                const startIdx = safePage * itemsPerPage;
                const endIdx = Math.min(
                  startIdx + itemsPerPage,
                  sellableItems.length
                );
                const pageItems = sellableItems.slice(startIdx, endIdx);

                // Store the current page for pagination
                const currentPage = safePage;

                const embed = new EmbedBuilder()
                  .setColor(EMBED_COLORS.BLUE)
                  .setTitle(
                    `${selectedNPC.emoji || "❓"} Sell Items to ${selectedNPC.name}`
                  )
                  .setDescription(
                    `Sell your items to ${selectedNPC.name}. Select an item to sell.`
                  )
                  .setFooter({
                    text: `Page ${currentPage + 1} of ${Math.max(1, totalPages)} • Your items: ${sellableItems.length}`,
                  });

                const menuId = `sell_item_menu_${userId}_${Date.now()}`;
                const selectMenu = new StringSelectMenuBuilder()
                  .setCustomId(menuId)
                  .setPlaceholder("Select an item to sell")
                  .addOptions(
                    pageItems.map((item) => ({
                      label: `${item.itemName} (x${item.quantity})`,
                      description: `Sell for ${item.sellPrice} ${CURRENCY.name} each`,
                      value: item.itemKey,
                      emoji: item.emoji,
                    }))
                  );

                const row = new ActionRowBuilder().addComponents(selectMenu);
                const components = [row];

                // Add pagination buttons if needed
                if (totalPages > 1) {
                  const prevButton = new ButtonBuilder()
                    .setCustomId(`sell_prev_${currentPage}`)
                    .setLabel("◀️")
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage <= 0);

                  const nextButton = new ButtonBuilder()
                    .setCustomId(`sell_next_${currentPage}`)
                    .setLabel("▶️")
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(currentPage >= totalPages - 1);

                  const pageInfo = new ButtonBuilder()
                    .setCustomId("sell_page_info")
                    .setLabel(`Page ${currentPage + 1}/${totalPages}`)
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true);

                  components.push(
                    new ActionRowBuilder().addComponents(
                      prevButton,
                      pageInfo,
                      nextButton
                    )
                  );
                }

                await sellMessage.edit({
                  embeds: [embed],
                  components,
                });

                console.log(
                  `[HandleSellQuantityModal] Successfully updated sell menu for ${selectedNPC.name}`
                );
              }
            }
          } catch (error) {
            console.error(
              "[HandleSellQuantityModal] Error updating sell menu:",
              error
            );
          }
        }

        // Also call the original refresh function for backward compatibility
        await refreshShopAndSellMenus(
          interaction,
          userId,
          selectedNPC,
          allItems,
          currentPage
        );
      } catch (error) {
        console.error(
          "[HandleSellQuantityModal] Error during menu refresh:",
          error
        );
      }
    } else {
      if (!interaction.channel) {
        console.error(
          "[HandleSellQuantityModal] No channel available to refresh menus"
        );
      }
      if (!selectedNPC) {
        console.error(
          "[HandleSellQuantityModal] No NPC found to refresh menus for"
        );
      }
    }

    return true;
  } catch (error) {
    console.error("[HandleSellQuantityModal] Error:", error);
    try {
      if (interaction.deferred) {
        await interaction.editReply({
          content: "An error occurred during the sell process.",
        });
      } else {
        await interaction.reply({
          content: "An error occurred during the sell process.",
          ephemeral: true,
        });
      }
    } catch (followUpError) {
      console.error(
        "[HandleSellQuantityModal] Error sending error message:",
        followUpError
      );
    }
    return true;
  }
}

/**
 * Handles Elizabeth Community Shop interaction
 * @param {object} interaction - Discord interaction
 * @param {object} character - Player character data
 * @param {object} selectedNPC - Elizabeth NPC data
 */
async function handleElizabethCommunityShopInteraction(
  interaction,
  character,
  selectedNPC
) {
  try {
    await showElizabethMainMenu(interaction, character, selectedNPC, false);
  } catch (error) {
    console.error("[handleElizabethCommunityShopInteraction] Error:", error);
    return interaction.reply({
      content: "An error occurred while loading the Community Shop.",
      ephemeral: false,
    });
  }
}

/**
 * Shows Elizabeth's main menu
 */
async function showElizabethMainMenu(
  interaction,
  character,
  selectedNPC,
  isUpdate = false
) {
  // Auto-collect any pending bits when accessing Elizabeth's menu
  const {
    processBitsCollection,
  } = require("../utils/backgroundBitsCollection");
  const updatedCharacter = await processBitsCollection(
    interaction.user.id,
    character
  );

  const currentGems = updatedCharacter.gems || 0;
  const bitsAvailable = updatedCharacter.bits_available || 0;

  const dynamicGreeting = await getNPCGreeting(selectedNPC.key);
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
    .setDescription(wrapText(dynamicGreeting))
    .setFooter({ text: `All payments are secure and processed through Stripe` })
    .addFields([
      {
        name: `Your Gems`,
        value: `${GEMS_EMOJI} ${currentGems.toLocaleString()} gems`,
        inline: true,
      },
      {
        name: `Bits Available`,
        value: `${BITS_EMOJI} ${bitsAvailable.toLocaleString()} bits`,
        inline: true,
      },
    ]);

  const buyGemsButton = new ButtonBuilder()
    .setCustomId(`elizabeth_buy_gems_${interaction.user.id}`)
    .setLabel("Buy Gems")
    .setEmoji(GEMS_EMOJI)
    .setStyle(ButtonStyle.Secondary);

  const buyCookiesButton = new ButtonBuilder()
    .setCustomId(`elizabeth_buy_cookies_${interaction.user.id}`)
    .setLabel("Buy Cookies")
    .setEmoji("<a:booster_cookie:1400058756183887932>")
    .setStyle(ButtonStyle.Secondary);

  // add bits shop button (emoji only) to the right of buy cookies
  const bitsShopButton = new ButtonBuilder()
    .setCustomId(`elizabeth_bits_shop_${interaction.user.id}`)
    .setEmoji(BITS_EMOJI)
    .setStyle(ButtonStyle.Secondary);

  const actionRow = new ActionRowBuilder().addComponents(
    buyGemsButton,
    buyCookiesButton,
    bitsShopButton
  );

  const messageOptions = {
    embeds: [embed],
    components: [actionRow],
  };

  // Offer a one-time Moldy Booster Cookie claim when in The Hub and not yet claimed
  try {
    const hasClaimed =
      updatedCharacter?.settings?.hasClaimedMoldyCookie === true;
    const region = updatedCharacter?.current_region;
    const isInHub = region === "the_hub" || region === "The Hub"; // be tolerant to legacy values
    if (isInHub && !hasClaimed) {
      const claimBtn = new ButtonBuilder()
        .setCustomId(`elizabeth_claim_moldy_cookie_${interaction.user.id}`)
        .setLabel("Claim Free Booster Cookie")
        .setEmoji("<a:booster_cookie:1400058756183887932>")
        .setStyle(ButtonStyle.Success);
      const claimRow = new ActionRowBuilder().addComponents(claimBtn);
      messageOptions.components.push(claimRow);
    }
  } catch {}

  if (isUpdate) {
    await interaction.update(messageOptions);
  } else {
    await interaction.reply({ ...messageOptions, ephemeral: false });
  }
}

/**
 * Shows Elizabeth's Booster Cookie menu
 */
async function showElizabethCookieMenu(interaction, character, selectedNPC) {
  const currentGems = character.gems || 0;
  const cookiePrice = 325;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
    .setDescription(
      wrapText(
        `Booster Cookies grant you the ability to gain Bits when consumed. On top of this, you gain tons of perks!`
      )
    )
    .addFields([
      {
        name: "Items for Sale",
        value: `<a:booster_cookie:1400058756183887932> \`Booster Cookie      ${cookiePrice.toLocaleString()}\` ${GEMS_EMOJI}`,
        inline: false,
      },
      {
        name: "Your Gems",
        value: `${GEMS_EMOJI} ${currentGems.toLocaleString()}`,
        inline: false,
      },
    ]);

  const buy1Button = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_buy_1_${interaction.user.id}`)
    .setLabel("Buy 1")
    .setStyle(ButtonStyle.Secondary)
    .setDisabled(currentGems < cookiePrice);

  const buy6Button = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_buy_6_${interaction.user.id}`)
    .setLabel("Buy 6")
    .setStyle(ButtonStyle.Secondary)
    .setDisabled(currentGems < cookiePrice * 6);

  const buy12Button = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_buy_12_${interaction.user.id}`)
    .setLabel("Buy 12")
    .setStyle(ButtonStyle.Secondary)
    .setDisabled(currentGems < cookiePrice * 12);

  const goBackButton = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_back_${interaction.user.id}`)
    .setLabel("Go Back")
    .setStyle(ButtonStyle.Secondary);

  const perksButton = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_perks_${interaction.user.id}`)
    .setLabel("Perks?")
    .setStyle(ButtonStyle.Secondary);

  const actionRow1 = new ActionRowBuilder().addComponents(
    buy1Button,
    buy6Button,
    buy12Button
  );
  const actionRow2 = new ActionRowBuilder().addComponents(
    goBackButton,
    perksButton
  );

  await interaction.update({
    embeds: [embed],
    components: [actionRow1, actionRow2],
  });
}

/**
 * Shows Elizabeth's cookie purchase confirmation menu
 */
async function showElizabethCookieConfirmation(
  interaction,
  character,
  selectedNPC,
  amount
) {
  const currentGems = character.gems || 0;
  const cookiePrice = 325;
  const totalCost = cookiePrice * amount;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
    .setDescription(
      `Are you sure you want to buy **${amount}** <a:booster_cookie:1400058756183887932> Booster Cookie${amount > 1 ? "s" : ""}?\n\n**Total Cost:** ${GEMS_EMOJI} ${totalCost.toLocaleString()} Gems`
    )
    .addFields([
      {
        name: `${GEMS_EMOJI} Your Gems`,
        value: `${currentGems.toLocaleString()} gems`,
        inline: true,
      },
    ]);

  const confirmButton = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_confirm_${amount}_${interaction.user.id}`)
    .setLabel(`Buy ${amount}`)
    .setStyle(ButtonStyle.Success)
    .setDisabled(currentGems < totalCost);

  const goBackButton = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_menu_${interaction.user.id}`)
    .setLabel("Go Back")
    .setStyle(ButtonStyle.Danger);

  const actionRow = new ActionRowBuilder().addComponents(
    confirmButton,
    goBackButton
  );

  await interaction.update({
    embeds: [embed],
    components: [actionRow],
  });
}

/**
 * Shows Elizabeth's cookie perks menu
 */
async function showElizabethCookiePerks(interaction, character, selectedNPC) {
  const {
    getBoosterCookiePerksDescription,
  } = require("../utils/boosterCookiePerks");

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
    .setDescription(getBoosterCookiePerksDescription());

  const goBackButton = new ButtonBuilder()
    .setCustomId(`elizabeth_cookie_menu_${interaction.user.id}`)
    .setLabel("Go Back")
    .setStyle(ButtonStyle.Secondary);

  const actionRow = new ActionRowBuilder().addComponents(goBackButton);

  await interaction.update({
    embeds: [embed],
    components: [actionRow],
  });
}

/**
 * Shared helper: Elizabeth Bits Shop catalog
 * Keep all item listings and prices in one place to avoid mismatches
 */
function getElizabethBitsCatalog() {
  const allItems = configManager.getAllItems();
  // Define live catalog entries here
  const entries = [
    { key: "BITS_TALISMAN", cost: 15000 },
    { key: "KAT_BOUQUET", cost: 2500 },
    { key: "GOD_POTION", cost: 1500 },
    { key: "KAT_FLOWER", cost: 500 },
    // Add more items as desired, e.g.
    // { key: 'ENCHANTED_DIAMOND_BLOCK', cost: 10 },
  ];
  // Attach resolved item data and filter out unknown items
  return entries
    .map((e) => ({ ...e, item: allItems[e.key] }))
    .filter((e) => !!e.item);
}

/**
 * Shows Elizabeth's Bits Shop menu
 */
async function showElizabethBitsShop(interaction, character, selectedNPC) {
  // ensure latest bits collected first
  try {
    const {
      processBitsCollection,
    } = require("../utils/backgroundBitsCollection");
    character = await processBitsCollection(interaction.user.id, character);
  } catch (e) {
    console.error(
      "[showElizabethBitsShop] error processing background bits collection:",
      e
    );
  }

  const currentBits = character.bits || 0;

  // Build catalog from live item config (shared helper)
  const catalog = getElizabethBitsCatalog();

  const displayItems = catalog.map((e) => ({
    name: e.item.name || e.key,
    price: (e.cost ?? 0).toLocaleString(),
    emoji: e.item.emoji || "",
  }));

  const maxNameLen = displayItems.reduce(
    (m, i) => Math.max(m, i.name.length),
    0
  );
  const maxPriceLen = displayItems.reduce(
    (m, i) => Math.max(m, i.price.length),
    0
  );
  const MIN_NAME_WIDTH = 16;
  const MIN_PRICE_WIDTH = 7;
  const nameWidth = Math.max(maxNameLen, MIN_NAME_WIDTH);
  const priceWidth = Math.max(maxPriceLen, MIN_PRICE_WIDTH);

  const itemLines = displayItems
    .map(
      (i) =>
        `${i.emoji} \`${i.name.padEnd(nameWidth, " ")} ${i.price.padStart(priceWidth, " ")}\` ${BITS_EMOJI}`
    )
    .join("\n");

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}: Bits Shop`)
    .setDescription(
      wrapText(
        `Spend your hard-earned ${BITS_EMOJI} Bits on exclusive items. More items coming soon!`
      )
    )
    .addFields([
      {
        name: "Items for Sale",
        value: itemLines || "No items available",
        inline: false,
      },
      {
        name: `${BITS_EMOJI} Your Bits`,
        value: `${currentBits.toLocaleString()} bits`,
        inline: false,
      },
    ]);

  // select menu for items
  const select = new StringSelectMenuBuilder()
    .setCustomId(`elizabeth_bits_select_${interaction.user.id}`)
    .setPlaceholder("Choose an item to purchase...")
    .addOptions(
      catalog.map((e) => ({
        label: e.item.name,
        description: `${e.cost} Bits`,
        value: e.key,
        emoji: e.item.emoji || undefined,
      }))
    );
  const row1 = new ActionRowBuilder().addComponents(select);

  const backButton = new ButtonBuilder()
    .setCustomId(`elizabeth_bits_back_${interaction.user.id}`)
    .setLabel("Go Back")
    .setStyle(ButtonStyle.Secondary);
  const row2 = new ActionRowBuilder().addComponents(backButton);

  await interaction.update({
    embeds: [embed],
    components: [row1, row2],
  });
}

/**
 * Shows confirmation for a Bits purchase
 */
async function showElizabethBitsConfirm(
  interaction,
  character,
  selectedNPC,
  itemKey,
  quantity = 1
) {
  const allItems = configManager.getAllItems();
  const catalogEntry = getElizabethBitsCatalog().find((i) => i.key === itemKey);
  if (!catalogEntry || !allItems[itemKey]) {
    return interaction.update({
      content: "Invalid item.",
      embeds: [],
      components: [],
    });
  }
  const item = allItems[itemKey];
  const bitsCost = catalogEntry.cost * Math.max(1, quantity);
  const currentBits = character.bits || 0;
  const canAfford = currentBits >= bitsCost;
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}: Confirm Purchase`)
    .setDescription(
      `Buy ${item.emoji || ""} **${item.name}${quantity > 1 ? ` × ${quantity}` : ""}** for **${bitsCost}** ${BITS_EMOJI} Bits?\n\nYou have: ${BITS_EMOJI} ${currentBits.toLocaleString()} bits`
    );

  const confirmBtn = new ButtonBuilder()
    .setCustomId(
      `elizabeth_bits_confirm_${itemKey}_q${Math.max(1, quantity)}_${interaction.user.id}`
    )
    .setLabel("Confirm")
    .setStyle(ButtonStyle.Success)
    .setDisabled(!canAfford);

  const cancelBtn = new ButtonBuilder()
    .setCustomId(`elizabeth_bits_shop_${interaction.user.id}`)
    .setLabel("Cancel")
    .setStyle(ButtonStyle.Danger);

  const row = new ActionRowBuilder().addComponents(confirmBtn, cancelBtn);
  await interaction.update({ embeds: [embed], components: [row] });
}

/**
 * Processes a Bits item purchase securely
 */
async function processElizabethBitsPurchase(
  interaction,
  character,
  selectedNPC,
  itemKey,
  quantity = 1
) {
  const allItems = configManager.getAllItems();
  const catalogEntry = getElizabethBitsCatalog().find((i) => i.key === itemKey);
  const item = allItems[itemKey];
  if (!catalogEntry || !item) {
    return interaction.update({
      content: "Invalid item.",
      embeds: [],
      components: [],
    });
  }
  const qty = Math.max(1, parseInt(quantity, 10) || 1);
  const cost = catalogEntry.cost * qty;
  try {
    const { updateBitsAtomically } = require("../utils/playerDataManager");
    const bitsResult = await updateBitsAtomically(interaction.user.id, -cost);
    if (!bitsResult) {
      const failEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.RED)
        .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
        .setDescription(`Transaction failed. You may not have enough bits.`);
      const backBtn = new ButtonBuilder()
        .setCustomId(`elizabeth_bits_shop_${interaction.user.id}`)
        .setLabel("Back")
        .setStyle(ButtonStyle.Secondary);
      return interaction.update({
        embeds: [failEmbed],
        components: [new ActionRowBuilder().addComponents(backBtn)],
      });
    }

    // grant the item using atomic inventory update based on item uniqueness/type
    try {
      const { updateInventoryAtomically } = require("../utils/inventory");
      const isEquipment =
        item.unique === true &&
        ["ARMOR", "WEAPON", "TOOL", "EQUIPMENT"].includes(item.type);
      if (isEquipment) {
        // unique items purchased as 1 regardless of quantity
        await updateInventoryAtomically(
          interaction.user.id,
          0,
          [],
          [{ itemKey }]
        );
      } else if (item.type === "ACCESSORY") {
        const accessoriesToAdd = Array.from({ length: qty }, () => ({
          itemKey,
        }));
        await updateInventoryAtomically(
          interaction.user.id,
          0,
          [],
          [],
          [],
          0,
          [],
          null,
          null,
          false,
          accessoriesToAdd
        );
      } else {
        await updateInventoryAtomically(
          interaction.user.id,
          0,
          [{ itemKey, amount: qty }],
          []
        );
      }
    } catch (invErr) {
      console.error(
        "[processElizabethBitsPurchase] inventory update failed:",
        invErr
      );
      // refund bits if inventory failure
      await updateBitsAtomically(interaction.user.id, cost);
      const refundEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.RED)
        .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
        .setDescription(
          "Purchase failed while delivering the item. Your bits were refunded."
        );
      const backBtn = new ButtonBuilder()
        .setCustomId(`elizabeth_bits_shop_${interaction.user.id}`)
        .setLabel("Back")
        .setStyle(ButtonStyle.Secondary);
      return interaction.update({
        embeds: [refundEmbed],
        components: [new ActionRowBuilder().addComponents(backBtn)],
      });
    }

    const successEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
      .setDescription(
        `Successfully purchased ${item.emoji || ""} **${item.name}${qty > 1 ? ` × ${qty}` : ""}** for ${cost} ${BITS_EMOJI} Bits!\nRemaining Bits: ${BITS_EMOJI} ${bitsResult.bits.toLocaleString()}`
      );

    const backMenuBtn = new ButtonBuilder()
      .setCustomId(`elizabeth_bits_shop_${interaction.user.id}`)
      .setLabel("Back to Bits Shop")
      .setStyle(ButtonStyle.Secondary);
    const mainMenuBtn = new ButtonBuilder()
      .setCustomId(`elizabeth_bits_back_${interaction.user.id}`)
      .setLabel("Main Menu")
      .setStyle(ButtonStyle.Primary);
    const row = new ActionRowBuilder().addComponents(backMenuBtn, mainMenuBtn);
    await interaction.update({ embeds: [successEmbed], components: [row] });
  } catch (err) {
    console.error("[processElizabethBitsPurchase] Error:", err);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "An error occurred processing your purchase.",
        ephemeral: true,
      });
    }
  }
}

/**
 * Persistent handler: Elizabeth Bits select menu
 */
async function handleElizabethBitsSelectMenu(selectInteraction) {
  try {
    if (!selectInteraction.isStringSelectMenu()) return false;
    const cid = selectInteraction.customId;
    if (!cid.startsWith("elizabeth_bits_select_")) return false;

    const ownerMatch = cid.match(/_(\d+)$/);
    if (!ownerMatch || ownerMatch[1] !== selectInteraction.user.id) {
      await selectInteraction.reply({
        content: "You cannot use another player's menu.",
        ephemeral: true,
      });
      return true;
    }

    const selectedKey = selectInteraction.values[0];
    const allItems = configManager.getAllItems();
    const selectedItem = allItems[selectedKey];
    const isEquipment =
      selectedItem &&
      selectedItem.unique === true &&
      ["ARMOR", "WEAPON", "TOOL", "EQUIPMENT"].includes(selectedItem.type);

    // Get latest character and NPC context
    const character = await getPlayerData(selectInteraction.user.id);
    const selectedNPC = NPCS.ELIZABETH;

    if (!selectedItem) {
      await selectInteraction.update({
        content: "Invalid item.",
        embeds: [],
        components: [],
      });
      return true;
    }

    if (isEquipment) {
      await showElizabethBitsConfirm(
        selectInteraction,
        character,
        selectedNPC,
        selectedKey,
        1
      );
      return true;
    }

    // Non-unique: show quantity modal (supports "max")
    const modalCustomId = `elizabeth_bits_qty_${selectedKey}_${selectInteraction.user.id}`;
    const modal = new ModalBuilder()
      .setCustomId(modalCustomId)
      .setTitle(`${BITS_EMOJI} Purchase Quantity`);

    const qtyInput = new TextInputBuilder()
      .setCustomId("quantity")
      .setLabel('Enter quantity or "max"')
      .setStyle(TextInputStyle.Short)
      .setPlaceholder('Enter quantity or "max"')
      .setRequired(true)
      .setMinLength(1)
      .setMaxLength(10);

    modal.addComponents(new ActionRowBuilder().addComponents(qtyInput));
    await selectInteraction.showModal(modal);
    return true;
  } catch (error) {
    console.error("[handleElizabethBitsSelectMenu] Error:", error);
    try {
      if (!selectInteraction.replied && !selectInteraction.deferred) {
        await selectInteraction.reply({
          content: "An error occurred while handling your selection.",
          ephemeral: true,
        });
      }
    } catch {}
    return true;
  }
}

/**
 * Persistent handler: Elizabeth Bits quantity modal submit
 */
async function handleElizabethBitsQuantityModal(modalInteraction) {
  try {
    if (!modalInteraction.isModalSubmit()) return false;
    const cid = modalInteraction.customId;
    if (!cid.startsWith("elizabeth_bits_qty_")) return false;

    const parts = cid.split("_");
    // pattern: elizabeth_bits_qty_{ITEM_KEY}_{userId}
    const userId = parts[parts.length - 1];
    const itemKey = parts.slice(3, parts.length - 1).join("_");

    if (userId !== modalInteraction.user.id) {
      await modalInteraction.reply({
        content: "You cannot use another player's menu.",
        ephemeral: true,
      });
      return true;
    }

    // Pull catalog to get per-unit cost
    const catalogEntry = getElizabethBitsCatalog().find(
      (i) => i.key === itemKey
    );
    const allItems = configManager.getAllItems();
    const selectedItem = allItems[itemKey];
    if (!catalogEntry || !selectedItem) {
      await modalInteraction.update({
        content: "Invalid item.",
        embeds: [],
        components: [],
      });
      return true;
    }

    // Ensure latest bits collected before computing max
    try {
      const {
        processBitsCollection,
      } = require("../utils/backgroundBitsCollection");
      const characterForBits = await getPlayerData(userId);
      await processBitsCollection(userId, characterForBits);
    } catch (e) {
      console.error(
        "[handleElizabethBitsQuantityModal] bits collection error:",
        e
      );
    }

    const character = await getPlayerData(userId);
    const selectedNPC = NPCS.ELIZABETH;
    const currentBits = character?.bits || 0;

    const qtyStr = modalInteraction.fields.getTextInputValue("quantity").trim();
    let qty;
    if (qtyStr.toLowerCase() === "max") {
      const perUnit = catalogEntry.cost;
      qty = perUnit > 0 ? Math.floor(currentBits / perUnit) : 0;
      // hard cap to prevent abuse if something goes wrong
      const MAX_QTY = 1000000000;
      if (qty > MAX_QTY) qty = MAX_QTY;
    } else {
      qty = parseInt(qtyStr, 10);
    }

    if (!Number.isInteger(qty) || qty <= 0) {
      await modalInteraction.reply({
        content: 'Invalid quantity. Please enter a positive number or "max".',
        ephemeral: true,
      });
      return true;
    }

    await showElizabethBitsConfirm(
      modalInteraction,
      character,
      selectedNPC,
      itemKey,
      qty
    );
    return true;
  } catch (error) {
    console.error("[handleElizabethBitsQuantityModal] Error:", error);
    try {
      if (!modalInteraction.replied && !modalInteraction.deferred) {
        await modalInteraction.reply({
          content: "An error occurred while processing your input.",
          ephemeral: true,
        });
      }
    } catch {}
    return true;
  }
}

/**
 * Handles gem package selection
 * @param {object} interaction - Discord button interaction
 * @param {object} character - Player character data
 * @param {object} selectedNPC - Elizabeth NPC data
 */
async function handleGemPurchaseSelection(interaction, character, selectedNPC) {
  try {
    const { GEM_PACKAGES } = require("../utils/stripeUtils");

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("gem_package_select")
      .setPlaceholder("Choose a gem package...")
      .addOptions(
        Object.values(GEM_PACKAGES).map((pkg) => ({
          label: `${pkg.name} - $${(pkg.price / 100).toFixed(2)}`,
          description: `${pkg.gems} Gems`,
          value: pkg.id,
          emoji: pkg.emoji,
        }))
      );

    // add gift button
    const giftButton = new ButtonBuilder()
      .setCustomId(`gem_gift_${interaction.user.id}`)
      .setLabel("Gift Gems")
      .setStyle(ButtonStyle.Secondary)
      .setEmoji("🎁");

    // add go back button
    const goBackButton = new ButtonBuilder()
      .setCustomId(`elizabeth_gems_back_${interaction.user.id}`)
      .setLabel("Go Back")
      .setStyle(ButtonStyle.Secondary);

    const row1 = new ActionRowBuilder().addComponents(selectMenu);
    const row2 = new ActionRowBuilder().addComponents(giftButton, goBackButton);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle(`${selectedNPC.emoji} Select Gem Package`)
      .setDescription(
        "Choose the gem package you'd like to purchase from the menu below, or gift gems to another player:"
      );

    await interaction.update({
      embeds: [embed],
      components: [row1, row2],
    });

    // create collector for select menu
    const selectCollector = interaction.message.createMessageComponentCollector(
      {
        filter: (i) =>
          i.user.id === interaction.user.id &&
          i.customId === "gem_package_select",
        time: 300000, // 5 minutes
        componentType: ComponentType.StringSelect,
      }
    );

    selectCollector.on("collect", async (selectInteraction) => {
      const selectedPackageId = selectInteraction.values[0];
      await handleGemPurchaseCheckout(
        selectInteraction,
        character,
        selectedPackageId
      );
    });
  } catch (error) {
    console.error("[handleGemPurchaseSelection] Error:", error);
    await interaction.update({
      content: "An error occurred while loading gem packages.",
      components: [],
    });
  }
}

/**
 * Handles gem purchase checkout
 * @param {object} interaction - Discord select interaction
 * @param {object} character - Player character data
 * @param {string} packageId - Selected gem package ID
 */
async function handleGemPurchaseCheckout(interaction, character, packageId) {
  try {
    const {
      createGemCheckoutSession,
      GEM_PACKAGES,
    } = require("../utils/stripeUtils");

    const selectedPackage = GEM_PACKAGES[packageId];
    if (!selectedPackage) {
      return interaction.update({
        content: "Invalid gem package selected.",
        components: [],
      });
    }

    // Defer an ephemeral reply so we can send the checkout link privately
    await interaction.deferReply({ ephemeral: true });

    // create stripe checkout session
    const checkoutUrl = await createGemCheckoutSession(
      interaction.user.id,
      packageId,
      interaction.user.username,
      interaction.channel.id, // pass channel ID for success message
      interaction.guild?.id // pass guild ID for proper Discord URL (null for DMs)
    );

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("<:emerald:1375550740654981230> Complete Your Purchase")
      .setDescription(
        `You've selected: **${selectedPackage.emoji} ${selectedPackage.name}**\n` +
          `**${selectedPackage.gems} Gems** for **$${(selectedPackage.price / 100).toFixed(2)}**\n\n` +
          `Click the button below to complete your purchase securely through Stripe.\n` +
          `Your gems will be automatically added to your account after payment.`
      )
      .setFooter({ text: "This link will expire in 30 minutes." });

    const checkoutButton = new ButtonBuilder()
      .setLabel("Complete Purchase")
      .setStyle(ButtonStyle.Link)
      .setURL(checkoutUrl)
      .setEmoji("<:emerald:1375550740654981230>");

    const row = new ActionRowBuilder().addComponents(checkoutButton);

    // Send the checkout link via the deferred ephemeral reply
    await interaction.editReply({
      embeds: [embed],
      components: [row],
    });

    // Update the original message to remove the select menu and show completion status
    const updatedEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle("<:npc_elizabeth:1399296177601773609> Select Gem Package")
      .setDescription("Proceed with the purchase in the message below");

    await interaction.message.edit({
      embeds: [updatedEmbed],
      components: [],
    });
  } catch (error) {
    console.error("[handleGemPurchaseCheckout] Error:", error);
    if (interaction.deferred || interaction.replied) {
      await interaction.editReply({
        content:
          "An error occurred while creating the checkout session. Please try again later.",
      });
    } else {
      await interaction.reply({
        content:
          "An error occurred while creating the checkout session. Please try again later.",
        ephemeral: true,
      });
    }
  }
}

/**
 * Handles gem gift modal display
 * @param {object} interaction - Discord button interaction
 * @param {object} _character - Player character data (unused)
 * @param {object} _selectedNPC - Elizabeth NPC data (unused)
 */
async function handleGemGiftModal(interaction, _character, _selectedNPC) {
  try {
    const modal = new ModalBuilder()
      .setCustomId(`gem_gift_modal_${interaction.user.id}`)
      .setTitle("🎁 Gift Gems to Another Player");

    const recipientInput = new TextInputBuilder()
      .setCustomId("recipient_name")
      .setLabel("Recipient's In-Game Name")
      .setStyle(TextInputStyle.Short)
      .setPlaceholder("Enter the exact in-game name...")
      .setRequired(true)
      .setMinLength(1)
      .setMaxLength(32);

    const recipientRow = new ActionRowBuilder().addComponents(recipientInput);
    modal.addComponents(recipientRow);

    await interaction.showModal(modal);
  } catch (error) {
    console.error("[handleGemGiftModal] Error:", error);
    await interaction.reply({
      content: "An error occurred while opening the gift modal.",
      ephemeral: true,
    });
  }
}

/**
 * Handles gem gift modal submission
 * @param {object} interaction - Discord modal interaction
 * @param {object} character - Player character data
 */
async function handleGemGiftModalSubmission(interaction, character) {
  try {
    const { getPlayerByUsername } = require("../utils/playerCache");
    const { GEM_PACKAGES } = require("../utils/stripeUtils");

    const recipientName = interaction.fields
      .getTextInputValue("recipient_name")
      .trim();

    // validate the name format first (same validation as character creation)
    if (!isValidCharacterName(recipientName)) {
      return interaction.update({
        content: `❌ Invalid name format. Player names can only contain letters and numbers (1-24 characters).`,
        embeds: [],
        components: [],
      });
    }

    // find the recipient player using exact in-game name
    const recipient = getPlayerByUsername(recipientName);
    if (!recipient) {
      return interaction.update({
        content: `❌ Player "${recipientName}" not found. Please make sure you entered the exact in-game name (case-sensitive).`,
        embeds: [],
        components: [],
      });
    }

    // check if trying to gift to themselves
    if (recipient.discord_id === interaction.user.id) {
      return interaction.update({
        content: "❌ You cannot gift gems to yourself!",
        embeds: [],
        components: [],
      });
    }

    // show gem package selection for gifting
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(
        `gem_gift_package_select_${recipient.discord_id}_${interaction.user.id}`
      )
      .setPlaceholder("Choose a gem package to gift...")
      .addOptions(
        Object.values(GEM_PACKAGES).map((pkg) => ({
          label: `${pkg.name} - $${(pkg.price / 100).toFixed(2)}`,
          description: `${pkg.gems} Gems`,
          value: pkg.id,
          emoji: pkg.emoji,
        }))
      );

    const row = new ActionRowBuilder().addComponents(selectMenu);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("🎁 Select Gem Package to Gift")
      .setDescription(
        `You're about to gift gems to **${recipient.name}**.\nChoose the gem package you'd like to purchase for them:`
      );

    await interaction.update({
      embeds: [embed],
      components: [row],
    });

    // create collector for gift package selection - use the updated message
    const response = interaction.message;
    const collector = response.createMessageComponentCollector({
      filter: (i) =>
        i.user.id === interaction.user.id &&
        i.customId.startsWith("gem_gift_package_select_"),
      time: 300000, // 5 minutes
      componentType: ComponentType.StringSelect,
    });

    collector.on("collect", async (selectInteraction) => {
      const selectedPackageId = selectInteraction.values[0];
      await handleGemGiftCheckout(
        selectInteraction,
        character,
        selectedPackageId,
        recipient
      );
    });
  } catch (error) {
    console.error("[handleGemGiftModalSubmission] Error:", error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.update({
        content: "An error occurred while processing your gift request.",
        embeds: [],
        components: [],
      });
    }
  }
}

/**
 * Handles gem gift checkout
 * @param {object} interaction - Discord select interaction
 * @param {object} character - Player character data
 * @param {string} packageId - Selected gem package ID
 * @param {object} recipient - Recipient player data
 */
async function handleGemGiftCheckout(
  interaction,
  character,
  packageId,
  recipient
) {
  try {
    const {
      createGemGiftCheckoutSession,
      GEM_PACKAGES,
    } = require("../utils/stripeUtils");

    const selectedPackage = GEM_PACKAGES[packageId];
    if (!selectedPackage) {
      return interaction.update({
        content: "Invalid gem package selected.",
        components: [],
      });
    }

    // defer the update first
    await interaction.deferUpdate();

    // create stripe checkout session for gift
    const checkoutUrl = await createGemGiftCheckoutSession(
      interaction.user.id,
      recipient.discord_id,
      packageId,
      character.name, // use buyer's in-game name instead of Discord username
      recipient.name,
      interaction.channel?.id,
      interaction.guild?.id
    );

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("<:emerald:1375550740654981230> Complete Your Gift Purchase")
      .setDescription(
        `🎁 **Gifting to:** ${recipient.name}\n` +
          `**Package:** ${selectedPackage.emoji} ${selectedPackage.name}\n` +
          `**${selectedPackage.gems} Gems** for **$${(selectedPackage.price / 100).toFixed(2)}**\n\n` +
          `Click the button below to complete your purchase securely through Stripe.\n` +
          `The gems will be automatically added to ${recipient.name}'s account after payment.`
      )
      .setFooter({ text: "This link will expire in 30 minutes." });

    const checkoutButton = new ButtonBuilder()
      .setLabel("Complete Gift Purchase")
      .setStyle(ButtonStyle.Link)
      .setURL(checkoutUrl)
      .setEmoji("🎁");

    const row = new ActionRowBuilder().addComponents(checkoutButton);

    // send the checkout link as an ephemeral follow-up
    await interaction.followUp({
      embeds: [embed],
      components: [row],
      ephemeral: true,
    });
  } catch (error) {
    console.error("[handleGemGiftCheckout] Error:", error);
    await interaction.followUp({
      content:
        "An error occurred while creating the gift checkout session. Please try again later.",
      ephemeral: true,
    });
  }
}

/**
 * Handles Elizabeth Community Shop button interactions
 * @param {object} interaction - Discord button interaction
 * @returns {boolean} - Whether the interaction was handled
 */
async function handleElizabethButtonInteraction(interaction) {
  try {
    const customId = interaction.customId;

    // Extract user ID from custom ID and verify ownership
    const userIdMatch = customId.match(/_(\d+)$/);
    if (!userIdMatch || userIdMatch[1] !== interaction.user.id) {
      await interaction.reply({
        content: "You cannot use another player's menu.",
        ephemeral: true,
      });
      return true;
    }

    // Get the latest character data
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      await interaction.reply({
        content: "Character not found. Please create a character first.",
        ephemeral: true,
      });
      return true;
    }

    // Get Elizabeth NPC data
    const selectedNPC = NPCS.ELIZABETH;

    // Handle different button interactions
    if (customId.startsWith("elizabeth_buy_gems_")) {
      await handleGemPurchaseSelection(interaction, character, selectedNPC);
    } else if (customId.startsWith("elizabeth_gems_back_")) {
      await showElizabethMainMenu(interaction, character, selectedNPC, true);
    } else if (customId.startsWith("gem_gift_")) {
      await handleGemGiftModal(interaction, character, selectedNPC);
    } else if (customId.startsWith("elizabeth_buy_cookies_")) {
      await showElizabethCookieMenu(interaction, character, selectedNPC);
    } else if (customId.startsWith("elizabeth_claim_moldy_cookie_")) {
      // One-time free Moldy Booster Cookie claim (Hub-only)
      try {
        const { updateInventoryAtomically } = require("../utils/inventory");
        const { updatePlayerSetting } = require("../utils/playerDataManager");
        const region = character?.current_region;
        const isInHub = region === "the_hub" || region === "The Hub";
        const hasClaimed = character?.settings?.hasClaimedMoldyCookie === true;
        if (!isInHub) {
          const denied = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
            .setDescription("You can only claim this reward while in The Hub.");
          await interaction.update({ embeds: [denied], components: [] });
          return true;
        }
        if (hasClaimed) {
          const already = new EmbedBuilder()
            .setColor(EMBED_COLORS.ORANGE)
            .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
            .setDescription(
              "You have already claimed your Moldy Booster Cookie."
            );
          await interaction.update({ embeds: [already], components: [] });
          return true;
        }

        await updateInventoryAtomically(
          interaction.user.id,
          0,
          [{ itemKey: "MOLDY_BOOSTER_COOKIE", amount: 1 }],
          []
        );
        await updatePlayerSetting(
          interaction.user.id,
          "hasClaimedMoldyCookie",
          true
        );

        const success = new EmbedBuilder()
          .setColor(EMBED_COLORS.GREEN)
          .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
          .setDescription(
            "You claimed a free <a:booster_cookie:1400058756183887932> Moldy Booster Cookie! Use /use to consume it."
          );
        const backBtn = new ButtonBuilder()
          .setCustomId(`elizabeth_gems_back_${interaction.user.id}`)
          .setLabel("Main Menu")
          .setStyle(ButtonStyle.Secondary);
        await interaction.update({
          embeds: [success],
          components: [new ActionRowBuilder().addComponents(backBtn)],
        });
      } catch (e) {
        console.error("[Elizabeth Claim Moldy Cookie] Error:", e);
        try {
          await interaction.reply({
            content: "Failed to claim cookie. Please try again.",
            ephemeral: true,
          });
        } catch {}
      }
    } else if (customId.startsWith("elizabeth_cookie_buy_")) {
      const amountMatch = customId.match(/elizabeth_cookie_buy_(\d+)_/);
      if (amountMatch) {
        const amount = parseInt(amountMatch[1]);
        await showElizabethCookieConfirmation(
          interaction,
          character,
          selectedNPC,
          amount
        );
      }
    } else if (customId.startsWith("elizabeth_cookie_confirm_")) {
      const amountMatch = customId.match(/elizabeth_cookie_confirm_(\d+)_/);
      if (amountMatch) {
        const amount = parseInt(amountMatch[1]);
        await processCookiePurchase(
          interaction,
          character,
          selectedNPC,
          amount
        );
      }
    } else if (customId.startsWith("elizabeth_cookie_back_")) {
      await showElizabethMainMenu(interaction, character, selectedNPC, true);
    } else if (customId.startsWith("elizabeth_cookie_menu_")) {
      await showElizabethCookieMenu(interaction, character, selectedNPC);
    } else if (customId.startsWith("elizabeth_cookie_perks_")) {
      await showElizabethCookiePerks(interaction, character, selectedNPC);
    } else if (customId.startsWith("elizabeth_bits_shop_")) {
      await showElizabethBitsShop(interaction, character, selectedNPC);
    } else if (customId.startsWith("elizabeth_bits_back_")) {
      await showElizabethMainMenu(interaction, character, selectedNPC, true);
    } else if (customId.startsWith("elizabeth_bits_buy_")) {
      const itemMatch = customId.match(/^elizabeth_bits_buy_(.+)_(\d+)$/);
      if (itemMatch) {
        const itemKey = itemMatch[1];
        await showElizabethBitsConfirm(
          interaction,
          character,
          selectedNPC,
          itemKey
        );
      }
    } else if (customId.startsWith("elizabeth_bits_confirm_")) {
      // format: elizabeth_bits_confirm_<ITEM>_q<qty>_<userId>
      const match = customId.match(
        /^elizabeth_bits_confirm_(.+)_q(\d+)_(\d+)$/
      );
      if (match) {
        const itemKey = match[1];
        const qty = parseInt(match[2], 10) || 1;
        await processElizabethBitsPurchase(
          interaction,
          character,
          selectedNPC,
          itemKey,
          qty
        );
      }
    } else {
      return false; // Not an Elizabeth interaction
    }

    return true; // Successfully handled
  } catch (error) {
    console.error("[handleElizabethButtonInteraction] Error:", error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "An error occurred while processing your request.",
        ephemeral: true,
      });
    }
    return true; // Still handled, even if there was an error
  }
}

/**
 * Processes the actual cookie purchase
 */
async function processCookiePurchase(
  interaction,
  character,
  selectedNPC,
  amount
) {
  const cookiePrice = 325;
  const totalCost = cookiePrice * amount;
  const currentGems = character.gems || 0;

  // Check if user has enough gems
  if (currentGems < totalCost) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
      .setDescription(
        `You don't have enough gems to purchase **${amount}** <a:booster_cookie:1400058756183887932> Booster Cookie${amount > 1 ? "s" : ""}!\n\n**Required:** ${GEMS_EMOJI} ${totalCost.toLocaleString()} Gems\n**You have:** ${GEMS_EMOJI} ${currentGems.toLocaleString()} Gems`
      );

    const goBackButton = new ButtonBuilder()
      .setCustomId(`elizabeth_cookie_menu_${interaction.user.id}`)
      .setLabel("Go Back")
      .setStyle(ButtonStyle.Secondary);

    const actionRow = new ActionRowBuilder().addComponents(goBackButton);

    await interaction.update({
      embeds: [embed],
      components: [actionRow],
    });
    return;
  }

  // Process the purchase
  try {
    // Atomically deduct gems (prevents race conditions)
    const { updateGemsAtomically } = require("../utils/playerDataManager");
    const gemsResult = await updateGemsAtomically(
      interaction.user.id,
      -totalCost
    );

    if (!gemsResult) {
      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
        .setDescription(
          `Transaction failed. You may not have enough gems or there was a database error.`
        );

      const goBackButton = new ButtonBuilder()
        .setCustomId(`elizabeth_cookie_menu_${interaction.user.id}`)
        .setLabel("Go Back")
        .setStyle(ButtonStyle.Secondary);

      const actionRow = new ActionRowBuilder().addComponents(goBackButton);

      await interaction.update({
        embeds: [embed],
        components: [actionRow],
      });
      return;
    }

    // Add booster cookies to inventory
    await updateInventoryAtomically(interaction.user.id, 0, [
      { itemKey: "BOOSTER_COOKIE", amount: amount },
    ]);

    // Update character with new gems balance
    character.gems = gemsResult.gems;

    // Show success message
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
      .setDescription(
        `Successfully purchased **${amount}** <a:booster_cookie:1400058756183887932> Booster Cookie${amount > 1 ? "s" : ""}!\n\n**Cost:** ${GEMS_EMOJI} ${totalCost.toLocaleString()} Gems\n**Remaining Gems:** ${GEMS_EMOJI} ${character.gems.toLocaleString()} Gems`
      );

    const backToMenuButton = new ButtonBuilder()
      .setCustomId(`elizabeth_cookie_menu_${interaction.user.id}`)
      .setLabel("Back to Menu")
      .setStyle(ButtonStyle.Secondary);

    const backToMainButton = new ButtonBuilder()
      .setCustomId(`elizabeth_cookie_back_${interaction.user.id}`)
      .setLabel("Main Menu")
      .setStyle(ButtonStyle.Primary);

    const actionRow = new ActionRowBuilder().addComponents(
      backToMenuButton,
      backToMainButton
    );

    await interaction.update({
      embeds: [embed],
      components: [actionRow],
    });
  } catch (error) {
    console.error("[processCookiePurchase] Error:", error);

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
      .setDescription(
        `An error occurred while processing your purchase. Please try again later.`
      );

    const goBackButton = new ButtonBuilder()
      .setCustomId(`elizabeth_cookie_menu_${interaction.user.id}`)
      .setLabel("Go Back")
      .setStyle(ButtonStyle.Secondary);

    const actionRow = new ActionRowBuilder().addComponents(goBackButton);

    await interaction.update({
      embeds: [embed],
      components: [actionRow],
    });
  }
}

/**
 * Handles gem gift modal submission
 * @param {object} interaction - Discord modal interaction
 */
async function handleGemGiftModalSubmit(interaction) {
  if (!interaction.isModalSubmit()) return false;

  if (!interaction.customId.startsWith("gem_gift_modal_")) {
    return false;
  }

  try {
    const { getPlayerData } = require("../utils/playerDataManager");

    // get player data
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      await interaction.reply({
        content: "Character not found. Please create a character first.",
        ephemeral: true,
      });
      return true;
    }

    await handleGemGiftModalSubmission(interaction, character);
    return true;
  } catch (error) {
    console.error("[handleGemGiftModalSubmit] Error:", error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "An error occurred while processing your request.",
        ephemeral: true,
      });
    }
    return true;
  }
}

// Export functions for use in other modules
module.exports.handleShadyAkinModalSubmit = handleShadyAkinModalSubmit;
module.exports.autoStartSlayerQuest = autoStartSlayerQuest;
module.exports.handleSellQuantityModal = handleSellQuantityModal;
module.exports.handleElizabethButtonInteraction =
  handleElizabethButtonInteraction;
module.exports.handleGemGiftModalSubmit = handleGemGiftModalSubmit;
module.exports.handleElizabethBitsSelectMenu = handleElizabethBitsSelectMenu;
module.exports.handleElizabethBitsQuantityModal =
  handleElizabethBitsQuantityModal;
