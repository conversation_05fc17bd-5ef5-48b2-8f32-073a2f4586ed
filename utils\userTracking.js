const { dbRunQueued } = require("./dbUtils");

/**
 * Updates a user's last active channel for notification purposes
 */
async function updateUserActiveChannel(userId, channelId) {
  const now = Date.now();
  try {
    await dbRunQueued(
      "INSERT INTO player_last_active_channel (discord_id, channel_id, updated_at) VALUES (?, ?, ?) ON CONFLICT(discord_id) DO UPDATE SET channel_id = ?, updated_at = ?",
      [userId, channelId, now, channelId, now]
    );
  } catch (err) {
    console.error("[User Tracking] Error updating active channel:", err);
  }
}

module.exports = {
  updateUserActiveChannel,
};
