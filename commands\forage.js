const { SlashCommandBuilder } = require("discord.js");
const { skillResourceAutocomplete } = require("../utils/commandUtils");
const { handleSkillActionExecution } = require("../utils/skillActionUtils.js");
const { foragingSkillConfig } = require("../data/skillConfigs");
const {
  getAbortSignal,
  cancellableDelay,
} = require("../utils/instantStopUtils");

const foragingAnimation = async (
  message,
  embed,
  foragingTime,
  stopRequestMap,
  actionId,
  auxResourceConsumed,
  resourceEmoji
) => {
  const axeEmoji = "🪓";
  const displayEmoji = resourceEmoji || "🌳";
  const steps = 5;
  const trackLength = 15;

  // keep original dynamic timing based on tree toughness and foraging sweep as akin designed
  const stepDuration = foragingTime / steps;

  for (let i = 0; i <= steps; i++) {
    // Check for stop before each animation step (immediate responsiveness)
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Forage] Stop requested before animation step ${i} for action ${actionId}.`
      );
      break;
    }

    const axePosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(axePosition)}${axeEmoji}${" ".repeat(trackLength - axePosition)}`;

    embed.setDescription(`\`${movingPart}\` ${displayEmoji}`);

    try {
      await message.edit({ embeds: [embed] });
    } catch (editError) {
      console.log(
        `[Animation][Forage] Minor error editing animation message (action ${actionId}): ${editError.message}`
      );
      break;
    }

    // Check for stop after each animation step (immediate responsiveness)
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Forage] Stop requested after animation step ${i} for action ${actionId}.`
      );
      break;
    }

    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Forage] Animation cancelled during delay for action ${actionId}.`
          );
          break;
        }
        throw error;
      }
    }
  }
};

async function handleForageAction(
  interaction,
  resourceKey,
  amount,
  isAgainCommandOrResumption = false,
  originalTotalAmountFromResumption = null,
  character
) {
  const isResumption = interaction.isResumption || false;

  let actualAmount = amount;

  if (isResumption) {
    actualAmount = originalTotalAmountFromResumption;
  }

  const normalizedResourceKey =
    typeof resourceKey === "string" ? resourceKey.toUpperCase() : "";

  return handleSkillActionExecution(
    interaction,
    character,
    normalizedResourceKey,
    actualAmount,
    isResumption ? false : isAgainCommandOrResumption,
    foragingSkillConfig
  );
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("forage")
    .setDescription("Forage for resources in your current region")
    .addStringOption((option) =>
      option
        .setName("resource")
        .setDescription("Type of resource to forage")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'Number of repetitions (e.g., 5, or type "max"). Defaults to your max allowed amount.'
        )
        .setRequired(false)
    ),

  async execute(interaction) {
    // Extract and validate parameters before delegation
    const {
      extractAndValidateSkillCommandParams,
    } = require("../utils/commandUtils");
    const params = await extractAndValidateSkillCommandParams(
      interaction,
      foragingSkillConfig
    );

    if (!params) {
      // extractAndValidateSkillCommandParams already sent error response
      return;
    }

    // Delegate to worker bot
    const { workerManager } = require("../utils/workerManager");
    await workerManager.delegateAction(
      interaction,
      "forage",
      params.amount,
      params.resourceKey,
      params.wasMax
    );
  },

  foragingAnimation,
  handleForageAction,

  async autocomplete(interaction) {
    return skillResourceAutocomplete(interaction, "Foraging", "resource");
  },
};
