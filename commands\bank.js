const {
  <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
} = require("discord.js");
// eslint-disable-next-line no-unused-vars
const { CURRENCY, COPPER, EMBED_COLORS } = require("../gameConfig");
const { parseShorthandAmount } = require("../utils/formatUtils");
const NPCS = require("../data/npcs.js");
const { checkRankPermission } = require("../utils/permissionUtils");
const {
  getPlayerData,
  updateCurrencyAtomically,
} = require("../utils/playerDataManager");
const { formatNumber } = require("../utils/displayUtils");
const { isBoosterCookieActive } = require("../utils/boosterCookieManager");

// Pre-process Banker NPCs by region for quick lookup (computed once at startup)
const npcsByRegion = Object.values(NPCS).reduce((acc, npc) => {
  if (npc.type === "BANKER" && npc.foundInRegions) {
    npc.foundInRegions.forEach((region) => {
      if (!acc[region]) acc[region] = [];
      acc[region].push(npc);
    });
  }
  return acc;
}, {});

// Helper to reply or edit, preferring edit
async function replyOrEdit(interaction, message, options) {
  if (message?.editable) {
    try {
      await message.edit({ ...options, components: options.components || [] });
      if (interaction.isModalSubmit() && !interaction.deferred) {
        await interaction.deferUpdate();
      }
      return;
    } catch (editError) {
      console.error(
        `[replyOrEdit] Edit FAILED for message ${message.id}:`,
        editError
      );
    }
  }

  try {
    await interaction.reply({ ...options, ephemeral: true, components: [] });
  } catch (replyError) {
    console.error(
      `[replyOrEdit] Failed to reply to interaction ${interaction.id}:`,
      replyError
    );
  }
}

async function handleTransaction(modalInteraction, originalMessage) {
  const userId = modalInteraction.user.id;
  const isDeposit = modalInteraction.customId === "deposit_modal";

  const playerData = await getPlayerData(userId);
  if (!playerData) {
    return replyOrEdit(modalInteraction, originalMessage, {
      content: "Could not load your character data.",
      embeds: [],
    });
  }

  const { coins: purse = 0, bank = 0 } = playerData;
  const amountStr = modalInteraction.fields.getTextInputValue("amount_input");
  const balanceToCheck = isDeposit ? purse : bank;
  const amount = parseShorthandAmount(amountStr, balanceToCheck);

  if (isNaN(amount) || amount <= 0) {
    return replyOrEdit(modalInteraction, originalMessage, {
      content:
        'Invalid amount. Please enter a positive number, shorthand (k/m), or "all".',
      embeds: [],
    });
  }

  if (amount > balanceToCheck) {
    const balanceType = isDeposit ? "purse" : "bank";
    return replyOrEdit(modalInteraction, originalMessage, {
      content: `You only have ${formatNumber(balanceToCheck)} ${CURRENCY.name} in your ${balanceType}.`,
      embeds: [],
    });
  }

  try {
    const updatedBalances = await updateCurrencyAtomically(
      userId,
      isDeposit ? -amount : amount, // Purse change
      isDeposit ? amount : -amount // Bank change
    );

    if (!updatedBalances) {
      throw new Error(
        "Transaction failed - insufficient funds or database error."
      );
    }

    const successEmbed = new EmbedBuilder()
      .setColor(EMBED_COLORS.BLUE)
      .setTitle("Bank Transaction Successful")
      .setDescription(
        `${CURRENCY.purseEmoji} **${isDeposit ? "Deposited" : "Withdrew"} ${amount.toLocaleString()} coins!**`
      )
      .addFields(
        {
          name: "New Purse Balance",
          value: `${CURRENCY.purseEmoji} **${formatNumber(updatedBalances.coins)}**`,
          inline: true,
        },
        {
          name: "New Bank Balance",
          value: `${CURRENCY.bankEmoji} **${formatNumber(updatedBalances.bank)}**`,
          inline: true,
        }
      );

    await replyOrEdit(modalInteraction, originalMessage, {
      embeds: [successEmbed],
    });
  } catch (error) {
    console.error("[Bank Transaction] Error:", error);
    await replyOrEdit(modalInteraction, originalMessage, {
      content: `Transaction failed: ${error.message}. Your coins are safe.`,
      embeds: [],
    });
  }
}

async function handleBankInteraction(interaction) {
  try {
    const playerData = await getPlayerData(interaction.user.id);
    if (!playerData) {
      return interaction.reply({
        content: "You need to create a character first.",
        ephemeral: true,
      });
    }

    if (!checkRankPermission({ rank: playerData.rank }, "MEMBER")) {
      return interaction.reply({
        content: "You don't have permission to use the bank.",
        ephemeral: true,
      });
    }

    // check if player has active booster cookie
    const hasActiveBoosterCookie = isBoosterCookieActive(playerData);

    // if no booster cookie, check for banker in current region
    let bankerNPC;
    if (!hasActiveBoosterCookie) {
      bankerNPC = (npcsByRegion[playerData.current_region] || [])[0];
      if (!bankerNPC) {
        const { coins: purse = 0, bank = 0 } = playerData;
        const errorEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("No Banker Here")
          .setDescription(
            "✖ There doesn't seem to be a Banker in this area.\n You can use the bank from anywhere with an active <a:booster_cookie:1400058756183887932> **Booster Cookie**!"
          )
          .addFields([
            {
              name: "Purse",
              value: `${CURRENCY.purseEmoji} **${formatNumber(purse)}** Coins`,
              inline: true,
            },
            {
              name: "Bank",
              value: `${CURRENCY.bankEmoji} **${formatNumber(bank)}** Coins`,
              inline: true,
            },
          ]);

        await interaction.reply({ embeds: [errorEmbed] });
        return;
      }
    } else {
      // with booster cookie, use the hub banker from anywhere
      bankerNPC = NPCS.HUB_BANKER;
    }

    await showBankingMenu(interaction, playerData, bankerNPC);
  } catch (error) {
    console.error("[handleBankInteraction] Error:", error);
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: "An error occurred while accessing the bank.",
        ephemeral: true,
      });
    }
  }
}

async function showBankingMenu(interaction, playerData, selectedNPC) {
  const { coins: purse = 0, bank = 0 } = playerData;
  // eslint-disable-next-line no-unused-vars
  const copper = playerData.copper || 0;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${selectedNPC.emoji} ${selectedNPC.name}`)
    .setDescription(selectedNPC.greeting || "How can I help you today?")
    .addFields([
      {
        name: "Purse",
        value: `${CURRENCY.purseEmoji} **${formatNumber(purse)}** Coins`,
        inline: true,
      },
      {
        name: "Bank",
        value: `${CURRENCY.bankEmoji} **${formatNumber(bank)}** Coins`,
        inline: true,
      },
      //      { name: "Copper", value: `${COPPER.emoji} **${formatNumber(copper)}** Copper`, inline: true },
    ]);

  const components = [
    new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("deposit_coins")
        .setLabel("Deposit")
        .setStyle(ButtonStyle.Primary)
        .setEmoji("📥"),
      new ButtonBuilder()
        .setCustomId("withdraw_coins")
        .setLabel("Withdraw")
        .setStyle(ButtonStyle.Primary)
        .setEmoji("📤")
    ),
  ];

  const message = await interaction.reply({
    embeds: [embed],
    components,
    fetchReply: true,
  });

  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId === "deposit_coins" || i.customId === "withdraw_coins"),
    time: 300000, // 5 minutes
  });

  collector.on("collect", async (i) => {
    const isDeposit = i.customId === "deposit_coins";
    const modal = new ModalBuilder()
      .setCustomId(isDeposit ? "deposit_modal" : "withdraw_modal")
      .setTitle(isDeposit ? "Deposit Coins" : "Withdraw Coins")
      .addComponents(
        new ActionRowBuilder().addComponents(
          new TextInputBuilder()
            .setCustomId("amount_input")
            .setLabel(
              `Amount to ${isDeposit ? "deposit" : "withdraw"} (e.g., 500, 10k, 1m, all)`
            )
            .setStyle(TextInputStyle.Short)
            .setRequired(true)
        )
      );

    await i.showModal(modal);

    try {
      const modalInteraction = await i.awaitModalSubmit({
        filter: (mi) => mi.user.id === i.user.id,
        time: 120000, // 2 minutes
      });
      await handleTransaction(modalInteraction, message);
    } catch (error) {
      if (error.code !== "InteractionCollectorError") {
        console.error("[Bank Modal] Error awaiting modal submission:", error);
      }
    }
  });

  collector.on("end", () => {
    if (message.editable) {
      embed.setFooter({ text: "This interaction has expired." });
      message.edit({ embeds: [embed], components: [] }).catch((e) => {
        if (e.code !== 10008)
          console.error("[Bank Collector End] Failed to edit message:", e);
      });
    }
  });
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("bank")
    .setDescription("Access banking services"),
  execute: handleBankInteraction,
  showBankingMenu,
};
