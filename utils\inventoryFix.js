// Utility to detect and fix broken character inventory structures
// This fixes existing players who were affected by the character creation bug

/**
 * Detects and fixes broken inventory structures in character data
 * This is a READ-ONLY check that doesn't modify anything - just validates structure
 * @param {string} userId - Discord user ID
 * @param {object} character - Character data object
 * @returns {Promise<boolean>} - Whether the character structure is valid
 */
async function detectAndFixBrokenInventory(userId, character) {
  if (!character) {
    return false;
  }

  // just validate structure exists - don't actually "fix" anything
  // the real fix happens during getPlayerData() assembly
  let isValid = true;

  // Check if inventory structure is missing or broken
  if (!character.inventory) {
    console.log(
      `[InventoryFix] Detected missing inventory structure for user ${userId}`
    );
    isValid = false;
  } else {
    // Check if items object is missing
    if (
      !character.inventory.items ||
      typeof character.inventory.items !== "object"
    ) {
      console.log(
        `[InventoryFix] Detected missing/invalid items object for user ${userId}`
      );
      isValid = false;
    }

    // Check if equipment array is missing
    if (
      !character.inventory.equipment ||
      !Array.isArray(character.inventory.equipment)
    ) {
      console.log(
        `[InventoryFix] Detected missing/invalid equipment array for user ${userId}`
      );
      isValid = false;
    }

    // Check accessories array
    if (
      !character.inventory.accessories ||
      !Array.isArray(character.inventory.accessories)
    ) {
      console.log(
        `[InventoryFix] Detected missing accessories array for user ${userId}`
      );
      isValid = false;
    }
  }

  return isValid;
}

/**
 * Wrapper function that can be called before skill commands to ensure valid inventory
 * This does NOT modify data - it just re-fetches it properly if needed
 * @param {string} userId - Discord user ID
 * @param {object} character - Character data object
 * @returns {Promise<object>} - The character object with guaranteed valid inventory
 */
async function ensureValidInventoryStructure(userId, character) {
  if (!character) {
    return character;
  }

  try {
    const isValid = await detectAndFixBrokenInventory(userId, character);
    if (!isValid) {
      console.log(
        `[InventoryFix] Re-fetching character data to ensure valid inventory structure for user ${userId}`
      );
      // Re-fetch the character data - this will properly assemble inventory from database
      const { getPlayerData } = require("./playerDataManager");
      const refreshedCharacter = await getPlayerData(userId);
      if (refreshedCharacter && refreshedCharacter.inventory) {
        console.log(
          `[InventoryFix] Successfully refreshed inventory structure for user ${userId}`
        );
        return refreshedCharacter;
      } else {
        console.log(
          `[InventoryFix] Warning: Re-fetch did not resolve inventory for user ${userId}`
        );
        // As fallback, ensure basic structure exists
        if (!character.inventory) {
          character.inventory = { items: {}, equipment: [], accessories: [] };
        }
        if (!character.inventory.items) character.inventory.items = {};
        if (!Array.isArray(character.inventory.equipment))
          character.inventory.equipment = [];
        if (!Array.isArray(character.inventory.accessories))
          character.inventory.accessories = [];
        console.log(
          `[InventoryFix] Applied fallback inventory structure for user ${userId}`
        );
      }
    }
    return character;
  } catch (error) {
    console.error(
      `[InventoryFix] Error during inventory validation for user ${userId}:`,
      error
    );
    return character; // Return original character if validation fails
  }
}

module.exports = {
  detectAndFixBrokenInventory,
  ensureValidInventoryStructure,
};
