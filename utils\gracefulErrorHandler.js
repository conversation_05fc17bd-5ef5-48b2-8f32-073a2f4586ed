/**
 * Graceful Error Handler Utility
 * Provides robust error handling that prevents unnecessary process crashes
 * while still maintaining system stability and logging critical issues.
 */

const { EventEmitter } = require("events");
const fs = require("fs").promises;
const path = require("path");
let Sentry = null;
try {
  Sentry = require("@sentry/node");
} catch {
  /* Sentry optional */
}

class GracefulErrorHandler extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      maxCriticalErrors: options.maxCriticalErrors || 5,
      criticalErrorWindow: options.criticalErrorWindow || 300000, // 5 minutes
      gracefulShutdownTimeout: options.gracefulShutdownTimeout || 30000, // 30 seconds
      logDirectory:
        options.logDirectory || path.join(__dirname, "../logs/errors"),
      enableProcessRestart: options.enableProcessRestart || false,
      ...options,
    };

    this.criticalErrors = [];
    this.isShuttingDown = false;
    this.activeConnections = new Set();
    this.cleanupHandlers = [];

    this.setupErrorHandlers();
  }

  /**
   * Setup global error handlers with graceful degradation
   */
  setupErrorHandlers() {
    if (global.__graceful_error_handlers_initialized) {
      return;
    }
    // Handle uncaught exceptions more gracefully
    process.on("uncaughtException", async (error) => {
      await this.handleCriticalError("uncaughtException", error);
    });

    // Handle unhandled promise rejections gracefully
    process.on("unhandledRejection", async (reason, _promise) => {
      // ensure we pass an Error instance for Sentry grouping consistency
      const err =
        reason instanceof Error
          ? reason
          : new Error(
              typeof reason === "string" ? reason : JSON.stringify(reason)
            );
      await this.handleCriticalError("unhandledRejection", err);
    });

    // Handle graceful shutdown signals
    process.on("SIGINT", () => this.gracefulShutdown("SIGINT"));
    process.on("SIGTERM", () => this.gracefulShutdown("SIGTERM"));

    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log("[GracefulErrorHandler] Error handlers initialized");
    global.__graceful_error_handlers_initialized = true;
  }

  /**
   * Handle critical errors with recovery attempts
   */
  async handleCriticalError(type, error) {
    const timestamp = new Date().toISOString();
    const errorInfo = {
      type,
      error: error.message || error.toString(),
      stack: error.stack,
      timestamp,
      pid: process.pid,
      memory: process.memoryUsage(),
      uptime: process.uptime(),
    };

    console.error(`[GracefulErrorHandler] Critical ${type}:`, error);
    if (Sentry) {
      Sentry.captureException(error, {
        tags: {
          handler: "GracefulErrorHandler",
          type,
          processName: this.options.processName || "unknown",
        },
        extra: { isShuttingDown: this.isShuttingDown },
      });
    }

    // Record crash in monitor
    try {
      const { crashMonitor } = require("./crashMonitor");
      await crashMonitor.recordCrash(type, error, {
        processName: this.options.processName,
      });
    } catch (monitorError) {
      console.warn(
        "[GracefulErrorHandler] Failed to record crash in monitor:",
        monitorError.message
      );
    }

    // Log the error to file
    await this.logError(errorInfo);

    // Track critical errors
    this.criticalErrors.push(timestamp);
    this.cleanupOldErrors();

    // Determine if this is a main bot or worker bot
    const isMainBot =
      this.options.processName && this.options.processName.includes("MainBot");
    const isWorkerBot =
      this.options.processName && this.options.processName.includes("Worker");

    // Main bot should NEVER shut down automatically - just log and continue
    if (isMainBot) {
      console.warn(
        `[GracefulErrorHandler] Critical error in main bot (${this.criticalErrors.length} recent). Logging but NOT shutting down.`
      );
      // Just emit the error for monitoring, don't shut down
      this.emit("criticalError", errorInfo);
      return;
    }

    // Worker bots can shut down and restart
    const effectiveThreshold = isWorkerBot
      ? this.options.maxCriticalErrors + 2
      : this.options.maxCriticalErrors;

    // Check if we've exceeded the critical error threshold (only for worker bots)
    if (this.criticalErrors.length >= effectiveThreshold) {
      console.error(
        `[GracefulErrorHandler] Too many critical errors (${this.criticalErrors.length}) within time window. Initiating graceful shutdown.`
      );
      await this.gracefulShutdown("CRITICAL_ERROR_THRESHOLD");
      return;
    }

    // Emit error event for other systems to handle
    this.emit("criticalError", errorInfo);

    // Attempt recovery based on error type
    await this.attemptRecovery(type, error);
  }

  /**
   * Attempt to recover from specific error types
   */
  async attemptRecovery(type, error) {
    try {
      switch (type) {
        case "uncaughtException":
          // For uncaught exceptions, check if it's a known recoverable error
          if (this.isRecoverableError(error)) {
            console.log(
              "[GracefulErrorHandler] Attempting recovery from recoverable uncaught exception"
            );
            this.emit("recovery", { type, success: true });
          } else {
            console.warn(
              "[GracefulErrorHandler] Uncaught exception may not be recoverable"
            );
            this.emit("recovery", { type, success: false });
          }
          break;

        case "unhandledRejection":
          // For unhandled rejections, we can usually continue
          console.log(
            "[GracefulErrorHandler] Continuing after unhandled rejection"
          );
          this.emit("recovery", { type, success: true });
          break;

        default:
          console.log(
            `[GracefulErrorHandler] No specific recovery for type: ${type}`
          );
          this.emit("recovery", { type, success: false });
      }
    } catch (recoveryError) {
      console.error(
        "[GracefulErrorHandler] Error during recovery attempt:",
        recoveryError
      );
      this.emit("recovery", { type, success: false, error: recoveryError });
    }
  }

  /**
   * Check if an error is potentially recoverable
   */
  isRecoverableError(error) {
    const recoverablePatterns = [
      /EADDRINUSE/, // Port already in use
      /ECONNRESET/, // Connection reset
      /EPIPE/, // Broken pipe
      /ETIMEDOUT/, // Timeout
      /Unknown interaction/, // Discord API timeout
      /Missing Access/, // Discord permissions
      /ENOTFOUND/, // DNS resolution failure
      /getaddrinfo/, // Network resolution
    ];

    const errorString = error.message || error.toString();
    return recoverablePatterns.some((pattern) => pattern.test(errorString));
  }

  /**
   * Log error to file for debugging
   */
  async logError(errorInfo) {
    try {
      // Ensure log directory exists
      await fs.mkdir(this.options.logDirectory, { recursive: true });

      const filename = `error_${new Date().toISOString().split("T")[0]}.log`;
      const filepath = path.join(this.options.logDirectory, filename);

      const logEntry = `${errorInfo.timestamp} [${errorInfo.type}] ${errorInfo.error}\n${errorInfo.stack}\n---\n`;

      await fs.appendFile(filepath, logEntry);
    } catch (logError) {
      console.error(
        "[GracefulErrorHandler] Failed to log error to file:",
        logError
      );
      if (Sentry) {
        Sentry.captureException(logError, {
          tags: { handler: "GracefulErrorHandler", phase: "logError" },
        });
      }
    }
  }

  /**
   * Clean up old error entries outside the time window
   */
  cleanupOldErrors() {
    const cutoff = Date.now() - this.options.criticalErrorWindow;
    this.criticalErrors = this.criticalErrors.filter(
      (timestamp) => new Date(timestamp).getTime() > cutoff
    );
  }

  /**
   * Register a cleanup handler for graceful shutdown
   */
  registerCleanupHandler(handler, name = "unnamed") {
    this.cleanupHandlers.push({ handler, name });
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(`[GracefulErrorHandler] Registered cleanup handler: ${name}`);
  }

  /**
   * Track active connections for graceful shutdown
   */
  trackConnection(connection, id = "unknown") {
    this.activeConnections.add({ connection, id });

    // Auto-remove when connection closes
    connection.on("close", () => {
      this.activeConnections.delete({ connection, id });
    });
  }

  /**
   * Perform graceful shutdown
   */
  async gracefulShutdown(signal) {
    if (this.isShuttingDown) {
      console.log(
        "[GracefulErrorHandler] Shutdown already in progress, forcing exit..."
      );
      process.exit(1);
    }

    this.isShuttingDown = true;
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(
        `[GracefulErrorHandler] Initiating graceful shutdown (${signal})...`
      );

    const shutdownTimeout = setTimeout(() => {
      console.error(
        "[GracefulErrorHandler] Graceful shutdown timeout exceeded, forcing exit"
      );
      process.exit(1);
    }, this.options.gracefulShutdownTimeout);

    try {
      // Run cleanup handlers
      console.log(
        `[GracefulErrorHandler] Running ${this.cleanupHandlers.length} cleanup handlers...`
      );
      await Promise.allSettled(
        this.cleanupHandlers.map(({ handler, name }) =>
          Promise.resolve(handler()).catch((err) =>
            console.error(
              `[GracefulErrorHandler] Cleanup handler '${name}' failed:`,
              err
            )
          )
        )
      );

      // Close active connections
      console.log(
        `[GracefulErrorHandler] Closing ${this.activeConnections.size} active connections...`
      );
      for (const { connection, id } of this.activeConnections) {
        try {
          if (connection && typeof connection.close === "function") {
            connection.close();
          } else if (connection && typeof connection.destroy === "function") {
            connection.destroy();
          }
        } catch (closeError) {
          console.warn(
            `[GracefulErrorHandler] Failed to close connection ${id}:`,
            closeError
          );
        }
      }

      clearTimeout(shutdownTimeout);
      const QUIET_BOOT = process.env.QUIET_BOOT === "true";
      if (!QUIET_BOOT)
        console.log("[GracefulErrorHandler] Graceful shutdown completed");

      // Flush Sentry (best-effort)
      if (Sentry) {
        try {
          await Sentry.flush(2000);
        } catch {
          /* ignore */
        }
      }
      // Only exit if this is a terminal condition
      if (
        signal === "CRITICAL_ERROR_THRESHOLD" ||
        signal === "SIGTERM" ||
        signal === "SIGINT"
      ) {
        process.exit(0);
      }
    } catch (shutdownError) {
      clearTimeout(shutdownTimeout);
      console.error(
        "[GracefulErrorHandler] Error during graceful shutdown:",
        shutdownError
      );
      process.exit(1);
    }
  }

  /**
   * Check system health
   */
  getHealthStatus() {
    const now = Date.now();
    const recentErrors = this.criticalErrors.filter(
      (timestamp) =>
        now - new Date(timestamp).getTime() < this.options.criticalErrorWindow
    );

    return {
      healthy: recentErrors.length < this.options.maxCriticalErrors,
      recentErrorCount: recentErrors.length,
      maxAllowed: this.options.maxCriticalErrors,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      activeConnections: this.activeConnections.size,
      isShuttingDown: this.isShuttingDown,
    };
  }
}

// Create singleton instance
const gracefulErrorHandler = new GracefulErrorHandler({
  maxCriticalErrors: 3, // Allow 3 critical errors in 5 minutes before shutdown
  criticalErrorWindow: 300000, // 5 minutes
  gracefulShutdownTimeout: 30000, // 30 seconds
  enableProcessRestart: false, // Don't auto-restart in production
});

module.exports = {
  GracefulErrorHandler,
  gracefulErrorHandler,
};
