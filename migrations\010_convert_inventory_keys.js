const configManager = require("../utils/configManager"); // Adjust path as needed
const { safeJsonParse } = require("../utils/playerDataManager"); // Adjust path as needed

// Helper to run DB commands asynchronously (assuming db connection is passed)
function dbAllAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve({ lastID: this.lastID, changes: this.changes });
    });
  });
}

module.exports = {
  version: 10,
  async up(db) {
    console.log(
      "[Migration 10] Starting: Convert inventory item keys from names to itemKeys...",
    );

    const liveItems = configManager.getAllItems();
    if (!liveItems || Object.keys(liveItems).length === 0) {
      throw new Error(
        "[Migration 10] Failed to load liveItems from configManager. Cannot perform migration.",
      );
    }

    // Create a map from lowercase item name to itemKey for efficient lookup
    const nameToKeyMap = new Map();
    for (const [key, itemData] of Object.entries(liveItems)) {
      if (itemData && itemData.name) {
        nameToKeyMap.set(itemData.name.toLowerCase(), key);
      }
    }
    console.log(
      `[Migration 10] Created name-to-key map with ${nameToKeyMap.size} entries.`,
    );

    const players = await dbAllAsync(
      db,
      "SELECT discord_id, inventory_items_json FROM players WHERE inventory_items_json IS NOT NULL AND inventory_items_json != '{}'",
    );
    console.log(
      `[Migration 10] Found ${players.length} players with inventory data to process.`,
    );

    let updatedCount = 0;
    let errorCount = 0;

    for (const player of players) {
      try {
        const oldInventory = safeJsonParse(player.inventory_items_json, null);
        if (!oldInventory || typeof oldInventory !== "object") {
          console.warn(
            `[Migration 10] Skipping player ${player.discord_id}: Invalid inventory JSON.`,
          );
          continue;
        }

        const newInventory = {};
        let changed = false;
        for (const [oldKey, quantity] of Object.entries(oldInventory)) {
          // Check if the oldKey is likely a name (contains lowercase or spaces perhaps - adjust if needed)
          // A simpler check: is the key *not* an existing itemKey?
          if (!liveItems[oldKey]) {
            // Assume it's a name that needs converting
            const itemKey = nameToKeyMap.get(oldKey.toLowerCase());
            if (itemKey) {
              // Found corresponding itemKey, add to new inventory with correct key
              newInventory[itemKey] = (newInventory[itemKey] || 0) + quantity;
              changed = true;
              // Silent key conversion
            } else {
              // Couldn't find a match, might be old/invalid data
              console.warn(
                `[Migration 10] Player ${player.discord_id}: Could not find itemKey for old inventory key "${oldKey}". Skipping this item.`,
              );
              // Optionally, add it back with the old key if you want to preserve unknown items
              // newInventory[oldKey] = quantity;
            }
          } else {
            // The key is already a valid itemKey, keep it as is
            newInventory[oldKey] = (newInventory[oldKey] || 0) + quantity;
          }
        }

        if (changed) {
          const newInventoryJson = JSON.stringify(newInventory);
          await dbRunAsync(
            db,
            "UPDATE players SET inventory_items_json = ? WHERE discord_id = ?",
            [newInventoryJson, player.discord_id],
          );
          updatedCount++;
        }
      } catch (err) {
        console.error(
          `[Migration 10] Error processing player ${player.discord_id}:`,
          err,
        );
        errorCount++;
      }
    }

    console.log(
      `[Migration 10] Finished. Updated inventories for ${updatedCount} players. Encountered ${errorCount} errors.`,
    );
  },
};
