const { SlashCommandBuilder } = require("discord.js");
// Removed old stop system imports - using simple abort system now
const {
  getAbortSignal,
  cancellableDelay,
} = require("../utils/instantStopUtils");
const { miningSkillConfig } = require("../data/skillConfigs");
const { startOrResumeAction } = require("../utils/actionHandlers");

const miningAnimation = async (
  message,
  embed,
  miningTime,
  stopRequestMap,
  actionId,
  auxConsumed,
  resourceEmoji,
  forceAnimation = false
) => {
  const pickaxeEmoji = "⛏️";
  const displayEmoji = resourceEmoji || "🪨";
  const steps = 5;
  const trackLength = 15;

  // fixed timing: 1000ms per step as akin wants (matching farming)
  const stepDuration = 1000;

  if (forceAnimation) {
    try {
      await message.edit({ embeds: [embed] });
    } catch (editError) {
      console.log(
        `[Animation][Mine] Minor error editing animation message (action ${actionId}): ${editError.message}`
      );
    }
  }

  for (let i = 0; i <= steps; i++) {
    const pickaxePosition = Math.floor((i / steps) * trackLength);
    const movingPart = `${" ".repeat(pickaxePosition)}${pickaxeEmoji}${" ".repeat(trackLength - pickaxePosition)}`;

    embed.setDescription(`\`${movingPart}\` ${displayEmoji}`);

    // Check for instant abort
    const signal = getAbortSignal(actionId);
    if (signal && signal.aborted) {
      console.log(
        `[Animation][Mine] Action ${actionId} cancelled instantly during animation`
      );
      return;
    }

    try {
      await message.edit({ embeds: [embed] });
    } catch (editError) {
      console.log(
        `[Animation][Mine] Minor error editing animation message (action ${actionId}): ${editError.message}`
      );
      break;
    }

    if (i < steps) {
      // Use cancellable delay that can be interrupted immediately
      try {
        await cancellableDelay(stepDuration, signal);
      } catch (error) {
        if (error.name === "AbortError") {
          console.log(
            `[Animation][Mine] Animation cancelled during delay for action ${actionId}.`
          );
          break;
        }
        throw error;
      }
    }
  }
};

function calculateMiningTime(
  itemKey = null,
  _preCalculatedStatsOrCharacter = null
) {
  const BASE_TIME = 3000; // Base time in milliseconds (3 seconds)
  const MIN_TIME = 3000; // Minimum mining time (3.00 seconds)

  // If no item key provided, return base time (backward compatibility)
  if (!itemKey) {
    return BASE_TIME;
  }

  // Get toughness for the item from items.json
  const configManager = require("../utils/configManager");
  const toughness = configManager.getAllItems()[itemKey]?.miningToughness || 0;

  // Calculate base time using hybrid scaling for balanced progression
  // Items with 0 toughness should always take BASE_TIME (3 seconds)
  // Additional time is added on top of the base 3 seconds
  // Linear scaling for early toughness (0-10), logarithmic for higher values
  const additionalTime =
    toughness <= 10
      ? toughness * 100
      : 1000 + (toughness - 10) * 40 * Math.log(1 + (toughness - 10) / 10);
  const baseMineTime = BASE_TIME + additionalTime;

  // For now, we don't have mining sweep like foraging, so return the base time
  // Future enhancement could add mining speed stats here

  return Math.max(baseMineTime, MIN_TIME);
}

/**
 * Handler function for mine actions
 * Uses the unified action system
 *
 * @param {import('discord.js').CommandInteraction} interaction - The interaction object or simulated interaction
 * @param {string} resourceKey - The resource key to mine
 * @param {number|string} amount - Number of resources to mine or "max"
 * @param {number} originalTotalAmountFromResumption - Original total amount from action record
 * @param {object} character - The player character data
 * @returns {Promise<void>}
 */
async function handleMineAction(
  interaction,
  resourceKey,
  amount,
  isAgain,
  originalTotalAmountFromResumption = null,
  character
) {
  if (!character) {
    console.error("[handleMineAction] Character object is missing!");
    return;
  }

  const isResumption = interaction.isResumption || false;

  let actualAmount = amount;
  if (isResumption) {
    actualAmount = originalTotalAmountFromResumption;
  }

  const normalizedResourceKey =
    typeof resourceKey === "string" ? resourceKey.toUpperCase() : "";

  return startOrResumeAction(interaction, "mining", {
    character,
    resourceKey: normalizedResourceKey,
    amount: actualAmount,
    isResumption,
    actionId: interaction.actionId,
    pendingLoot: interaction.pendingLoot,
    startingCycle: interaction.startingCycle || 0,
  });
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("mine")
    .setDescription("Mine resources in your current region")
    .addStringOption((option) =>
      option
        .setName("resource")
        .setDescription("Type of resource to mine")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'Number of repetitions (e.g., 5, or type "max"). Defaults to your max allowed amount.'
        )
        .setRequired(false)
    ),

  async execute(interaction) {
    // Extract and validate parameters before delegation
    const {
      extractAndValidateSkillCommandParams,
    } = require("../utils/commandUtils");
    const params = await extractAndValidateSkillCommandParams(
      interaction,
      miningSkillConfig
    );

    if (!params) {
      // extractAndValidateSkillCommandParams already sent error response
      return;
    }

    // Delegate to worker bot
    const { workerManager } = require("../utils/workerManager");
    await workerManager.delegateAction(
      interaction,
      "mine",
      params.amount,
      params.resourceKey,
      params.wasMax
    );
  },

  handleMineAction,

  async autocomplete(interaction) {
    const { skillResourceAutocomplete } = require("../utils/commandUtils");
    return skillResourceAutocomplete(interaction, "Mining", "resource");
  },

  calculateMiningTime,
  miningAnimation,
};
