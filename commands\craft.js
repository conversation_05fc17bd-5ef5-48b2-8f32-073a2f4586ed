const { EMBED_COLORS } = require("../gameConfig.js");
const {
  SlashCommandBuilder,
  EmbedBuilder,
  StringSelectMenuBuilder,
  ActionRowBuilder,
  ComponentType,
} = require("discord.js");
const craftingUtils = require("../utils/craftingUtils.js");
const { getInventory } = require("../utils/inventory.js");
const { getPlayerAccessories } = require("../utils/accessoryManager.js");
const configManager = require("../utils/configManager.js");
// Using embeds + standard components only

const MAX_CRAFT_LIMIT = 10000000;
const DEFAULT_CRAFT_AMOUNT = 1;

const MULTI_PET_RECIPE_MSG = (petName) =>
  `This pet has multiple recipes. Please use the **/recipe** command for **${petName}** to choose which egg to craft.`;

/**
 * formats a breakdown of crafting materials showing what the player has vs what they need
 * similar to the recipe command but focused on showing why crafting failed
 * @param {Object} recipe - The recipe object with ingredients
 * @param {Object} playerInventory - Player's inventory data
 * @param {Object} allItems - All items config data
 * @param {number} craftAmount - Amount being crafted
 * @param {Object} selectedUniqueItems - Map of selected unique items (itemKey -> count)
 */
function formatCraftingMaterialsBreakdown(
  recipe,
  playerInventory,
  allItems,
  craftAmount = 1,
  selectedUniqueItems = {}
) {
  if (!recipe?.ingredients?.length) {
    return "No ingredients found for this recipe.";
  }

  let output = "";

  for (const ingredient of recipe.ingredients) {
    if (!ingredient?.itemKey) continue;

    const itemData = allItems[ingredient.itemKey];
    const neededAmount = ingredient.amount * craftAmount;
    const isUnique = itemData?.unique === true;

    let hasEnough = false;
    let quantityDisplay = "";

    if (isUnique) {
      // for unique items, check if they're selected rather than in inventory
      const selectedCount = selectedUniqueItems[ingredient.itemKey] || 0;
      hasEnough = selectedCount >= neededAmount;
      quantityDisplay = hasEnough ? " (Selected)" : " (Not selected)";
    } else {
      // for regular items, check inventory as normal
      const playerAmount = playerInventory?.items?.[ingredient.itemKey] || 0;
      hasEnough = playerAmount >= neededAmount;

      if (playerAmount > 0) {
        quantityDisplay = ` (You have ${playerAmount.toLocaleString()})`;
      }
    }

    // determine if player has enough
    const checkmark = hasEnough ? "✅" : "❌";

    // format the display
    const emoji = itemData?.emoji || "❓";
    const itemName = itemData?.name || ingredient.itemKey;

    output += `${checkmark} ${emoji} \`${neededAmount.toLocaleString()}x ${itemName}\`${quantityDisplay}\n`;
  }

  return output.trim();
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("craft")
    .setDescription("Craft an item using its recipe.")
    .addStringOption((option) =>
      option
        .setName("item_key")
        .setDescription("The item you want to craft")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("amount")
        .setDescription(
          'How many to craft? (Enter a number or "max". Default: 1)'
        )
        .setRequired(false)
    ),

  async execute(interaction) {
    await interaction.deferReply();
    const userId = interaction.user.id;

    try {
      // Remove activity check for craft command

      const allItems = configManager.getAllItems();
      const { findItemByKeyOrName } = require("../utils/itemSearchUtils");

      // Try to find the item by key or name (handles both direct lookup and flexible matching)
      const foundItemKey = findItemByKeyOrName(
        interaction.options.getString("item_key"),
        allItems
      );

      if (!foundItemKey) {
        return interaction.editReply({
          content: `Item "${interaction.options.getString("item_key")}" not found. Please check the spelling or use the autocomplete suggestions.`,
        });
      }

      let itemKeyToCraft = foundItemKey;
      const amountInput = interaction.options.getString("amount");
      let craftAmount = DEFAULT_CRAFT_AMOUNT;
      let amountWasMax = false;

      if (amountInput) {
        if (amountInput.toLowerCase() === "max") {
          amountWasMax = true; // defer calculation until after recipe selection (so we use the chosen recipe, not always index 0)
        } else {
          craftAmount = parseInt(amountInput, 10);
          if (!Number.isFinite(craftAmount) || craftAmount <= 0) {
            const reply = await interaction.editReply({
              content:
                'Invalid amount. Please enter a positive number or "max".',
            });
            setTimeout(() => reply.delete().catch(console.error), 5000);
            return;
          }
          if (craftAmount > MAX_CRAFT_LIMIT) {
            craftAmount = MAX_CRAFT_LIMIT;
            await interaction.editReply({
              content: `⚠️ You can only craft up to ${MAX_CRAFT_LIMIT} at once. Setting amount to ${MAX_CRAFT_LIMIT}.`,
            });
          }
        }
      }

      let itemData = allItems[itemKeyToCraft];

      if (!itemData) {
        await interaction.editReply({
          content: `Unknown item: ${itemKeyToCraft}`,
        });
        return;
      }

      // Pets can now be crafted directly, no need for mystery egg logic

      // Multi-recipe selection / auto-pick
      const recipes = Array.isArray(itemData.recipes) ? itemData.recipes : [];
      if (!recipes.length) {
        await interaction.editReply({
          content: "No crafting recipe found for this item.",
        });
        return;
      }
      let chosenRecipeIndex = 0;
      if (recipes.length > 1) {
        const feasibilityInventory = await getInventory(userId);
        const feasibleData = recipes.map((r, i) => ({
          i,
          max: craftingUtils.calculateMaxCraftable(
            {
              items: feasibilityInventory.items,
              equipment: feasibilityInventory.equipment || [],
            },
            r,
            allItems
          ),
        }));
        const positives = feasibleData.filter((f) => f.max > 0);
        if (positives.length === 1) {
          chosenRecipeIndex = positives[0].i; // auto-pick unique feasible recipe
        } else {
          const options = recipes.map((r, i) => {
            const preview = r.ingredients
              .slice(0, 3)
              .map((ing) => {
                const ingData = allItems[ing.itemKey];
                return `${ing.amount || 1}x ${ingData?.name || ing.itemKey}`;
              })
              .join(", ");
            const maxTxt =
              feasibleData[i].max > 0 ? ` (max ${feasibleData[i].max})` : "";
            // attach target item emoji for quick identification
            let optEmoji = null;
            try {
              const raw = itemData.emoji;
              if (raw) {
                if (/^<a?:/.test(raw)) {
                  const match = raw.match(/^<a?:([^:]+):(\d+)>$/);
                  if (match)
                    optEmoji = {
                      name: match[1],
                      id: match[2],
                      animated: raw.startsWith("<a:"),
                    };
                } else if (/^\p{Emoji}$/u.test(raw) || raw.length <= 3) {
                  optEmoji = { name: raw };
                }
              }
            } catch {
              /* ignore emoji parse error */
            }
            const base = {
              label: `Recipe ${i + 1}${maxTxt}`,
              description: preview.substring(0, 95) || "Select",
              value: `r:${i}`,
            };
            if (optEmoji) base.emoji = optEmoji;
            return base;
          });
          const select = new StringSelectMenuBuilder()
            .setCustomId(`craft_pick::item=${itemKeyToCraft}`)
            .setPlaceholder("Select a recipe")
            .addOptions(options);
          const row = new ActionRowBuilder().addComponents(select);
          const pickEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.MID_BLUE || 0x3498db)
            .setTitle("Choose Recipe")
            .setDescription(
              "Multiple recipes available. Pick one to continue."
            );
          await interaction.editReply({
            embeds: [pickEmbed],
            components: [row],
            content: null,
          });
          const replyMsg = await interaction.fetchReply();
          try {
            const filter = (i) =>
              i.user.id === userId &&
              i.customId === `craft_pick::item=${itemKeyToCraft}`;
            const collected = await replyMsg.awaitMessageComponent({
              filter,
              componentType: ComponentType.StringSelect,
              time: 60000,
            });
            const m = collected.values[0].match(/r:(\d+)/);
            chosenRecipeIndex = m ? parseInt(m[1], 10) : 0;
            await collected.deferUpdate().catch(() => {});
          } catch {
            await interaction.editReply({
              content: "Recipe selection timed out.",
              components: [],
            });
            return;
          }
        }
      }
      const recipe = recipes[chosenRecipeIndex];

      // Now that the user has selected (or we auto-selected) the recipe, if they asked for 'max' compute it for THIS recipe
      if (amountWasMax) {
        try {
          const playerInventory = await getInventory(userId);
          let computedMax = craftingUtils.calculateMaxCraftable(
            {
              items: playerInventory.items,
              equipment: playerInventory.equipment || [],
            },
            recipe,
            allItems
          );
          if (!Number.isFinite(computedMax) || computedMax <= 0) {
            const reply = await interaction.editReply({
              content: "You don't have the required ingredients to craft any.",
            });
            setTimeout(() => reply.delete().catch(console.error), 5000);
            return;
          }
          if (computedMax > MAX_CRAFT_LIMIT) {
            computedMax = MAX_CRAFT_LIMIT;
            await interaction.editReply({
              content: `⚠️ You can only craft up to ${MAX_CRAFT_LIMIT} at once. Setting amount to ${MAX_CRAFT_LIMIT}.`,
            });
          }
          craftAmount = computedMax;
        } catch (e) {
          console.error(
            "[Craft Command] Error computing max craft amount for selected recipe:",
            e
          );
          const reply = await interaction.editReply({
            content:
              "Error calculating max craftable amount for the selected recipe.",
          });
          setTimeout(() => reply.delete().catch(console.error), 5000);
          return;
        }
      }

      const hasUniqueIngredient = recipe.ingredients.some((ing) => {
        const ingData = allItems[ing.itemKey];
        return ingData?.unique === true;
      });

      if (hasUniqueIngredient) {
        // You can only craft one at a time when upgrading a unique item
        if (craftAmount > 1) {
          if (amountWasMax) {
            // silently fall back to 1 for 'max' requests so user isn't blocked unnecessarily
            craftAmount = 1;
          } else {
            await interaction.editReply({
              content:
                "You can only craft one at a time when upgrading a unique item.",
            });
            return;
          }
        }

        const uniqueIngredients = recipe.ingredients
          .map((ing) => ({
            ...ing,
            itemData: allItems[ing.itemKey],
          }))
          .filter((ing) => ing.itemData?.unique === true);

        if (uniqueIngredients.length === 0) {
          await interaction.editReply({
            content: "Error: No unique ingredients found in recipe.",
          });
          return;
        }

        const playerInventory = await getInventory(userId);
        const playerAccessories = await getPlayerAccessories(userId);

        // Calculate total unique items needed and build ingredient info
        const uniqueIngredientInfo = [];
        const allEligibleItems = [];
        let totalItemsNeeded = 0;

        for (const uniqueIngredient of uniqueIngredients) {
          const needed = uniqueIngredient.amount * craftAmount;
          totalItemsNeeded += needed;

          const eligibleEquipment = (playerInventory.equipment || []).filter(
            (eq) => eq.itemKey === uniqueIngredient.itemKey
          );

          const eligibleAccessories = (playerAccessories || []).filter(
            (acc) => acc.itemKey === uniqueIngredient.itemKey
          );

          const eligibleItems = [...eligibleEquipment, ...eligibleAccessories];

          if (eligibleItems.length < needed) {
            const itemName =
              allItems[uniqueIngredient.itemKey]?.name ||
              uniqueIngredient.itemKey;
            const emoji = allItems[uniqueIngredient.itemKey]?.emoji || "";

            // Create a breakdown for unique ingredients too
            let breakdownMessage = `You don't have enough ${emoji} **${itemName}** to craft this item.\n\n`;
            breakdownMessage += `**Required Unique Items:**\n`;
            breakdownMessage += `❌ ${emoji} \`${needed}x ${itemName}\` (You have ${eligibleItems.length} available)\n`;

            // Show regular ingredients too if any
            const regularIngredients = recipe.ingredients.filter((ing) => {
              const itemData = allItems[ing.itemKey];
              return itemData?.unique !== true;
            });

            if (regularIngredients.length > 0) {
              breakdownMessage += `\n**Other Required Materials:**\n`;
              const regularBreakdown = formatCraftingMaterialsBreakdown(
                { ingredients: regularIngredients },
                playerInventory,
                allItems,
                craftAmount
              );
              breakdownMessage += regularBreakdown;
            }

            await interaction.editReply({
              content: breakdownMessage,
            });
            return;
          }

          uniqueIngredientInfo.push({
            itemKey: uniqueIngredient.itemKey,
            itemName:
              allItems[uniqueIngredient.itemKey]?.name ||
              uniqueIngredient.itemKey,
            emoji: allItems[uniqueIngredient.itemKey]?.emoji || "",
            needed: needed,
            available: eligibleItems.length,
          });

          allEligibleItems.push(
            ...eligibleItems.map((item) => ({
              ...item,
              requiredForIngredient: uniqueIngredient.itemKey,
            }))
          );
        }

        // Check if we have multiple unique ingredient types OR any ingredient with quantity > 1
        const needsMultiSelect =
          uniqueIngredients.length > 1 ||
          uniqueIngredients.some((ing) => ing.amount > 1);

        if (needsMultiSelect) {
          // Multi-select approach for multiple unique ingredients or quantities

          // Check total item count (Discord limit is 25 per select menu)
          if (allEligibleItems.length > 25) {
            await interaction.editReply({
              content:
                "You have too many eligible items. Please reduce your inventory to 25 or fewer to craft.",
            });
            return;
          }

          const options = allEligibleItems
            .sort((a, b) => {
              // Sort by ingredient type first, then by item name
              if (a.requiredForIngredient !== b.requiredForIngredient) {
                return a.requiredForIngredient.localeCompare(
                  b.requiredForIngredient
                );
              }
              const nameA = allItems[a.itemKey]?.name || a.itemKey;
              const nameB = allItems[b.itemKey]?.name || b.itemKey;
              return nameA.localeCompare(nameB) || a.id.localeCompare(b.id);
            })
            .map((item) => {
              const itemName = allItems[item.itemKey]?.name || item.itemKey;
              const requiredForName =
                allItems[item.requiredForIngredient]?.name ||
                item.requiredForIngredient;
              return {
                label: `${itemName} #${item.id.slice(0, 6)}`,
                description: requiredForName,
                value: `${item.requiredForIngredient}:${item.id}`,
              };
            });

          const selectionId = `craft_multi_select_${Date.now()}`;
          const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(selectionId)
            .setPlaceholder(`Select ${totalItemsNeeded} items total`)
            .addOptions(options)
            .setMinValues(totalItemsNeeded)
            .setMaxValues(totalItemsNeeded);

          const row = new ActionRowBuilder().addComponents(selectMenu);

          const selectionEmbed =
            craftingUtils.createMultiUniqueItemSelectionEmbed(
              itemData.name,
              itemData.emoji,
              uniqueIngredientInfo
            );

          const replyMessage = await interaction.editReply({
            embeds: [selectionEmbed],
            components: [row],
          });

          const collectorFilter = (i) =>
            i.customId === selectionId && i.user.id === userId;
          let interactionHandled = false;

          try {
            const collectedInteraction =
              await replyMessage.awaitMessageComponent({
                filter: collectorFilter,
                componentType: ComponentType.StringSelect,
                time: 60000, // 60 seconds
              });

            // Parse selected items and validate
            const selectedItems = collectedInteraction.values.map((value) => {
              const [ingredientKey, itemId] = value.split(":");
              return { ingredientKey, itemId };
            });

            // Validate that we have the correct number of each unique ingredient
            const selectedIngredientCounts = new Map();
            for (const item of selectedItems) {
              const current =
                selectedIngredientCounts.get(item.ingredientKey) || 0;
              selectedIngredientCounts.set(item.ingredientKey, current + 1);
            }

            const requiredIngredientCounts = new Map();
            for (const ing of uniqueIngredients) {
              requiredIngredientCounts.set(
                ing.itemKey,
                ing.amount * craftAmount
              );
            }

            const errors = [];
            for (const [itemKey, required] of requiredIngredientCounts) {
              const selected = selectedIngredientCounts.get(itemKey) || 0;
              if (selected !== required) {
                const itemName = allItems[itemKey]?.name || itemKey;
                errors.push(
                  `${itemName}: need ${required}, selected ${selected}`
                );
              }
            }

            if (errors.length > 0) {
              await collectedInteraction.reply({
                content: `❌ Incorrect selection quantities:\n${errors.join("\n")}`,
                ephemeral: true,
              });
              return;
            }

            try {
              await collectedInteraction.deferUpdate();
              interactionHandled = true;
            } catch (deferError) {
              console.error(
                "[Craft Command] Error deferring update on multi-select interaction:",
                deferError
              );
              if (deferError.code === 10062 || deferError.code === 40060) {
                console.log(
                  `[Craft Command] Multi-select interaction unavailable (${deferError.code}), falling back to original interaction`
                );
              } else {
                throw deferError;
              }
            }

            // Craft using multiple selected items
            const craftResult = await craftingUtils.attemptCraft(
              userId,
              itemKeyToCraft,
              1,
              selectedItems, // Pass array of selected items
              interaction, // Pass interaction for notifications
              { recipeIndex: chosenRecipeIndex }
            );

            const replyInteraction = interactionHandled
              ? collectedInteraction
              : interaction;

            if (craftResult.success) {
              // Get updated character data to show skill progress
              const { getPlayerData } = require("../utils/playerDataManager");
              const updatedCharacter = await getPlayerData(userId);

              // Create proper action result embed with skill progress bar
              const {
                buildFinalSkillResultEmbed,
              } = require("../utils/displayUtils");

              const resultObject = {
                title: "Crafted",
                color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
                skillName: "crafting",
                exp: craftResult.craftingXpGained || 0,
                petExp: craftResult.petXpGained || 0,
                tamingExp: craftResult.tamingXpGained || 0,
                finalCharacterData: updatedCharacter,
                finalSkillData: {
                  exp: updatedCharacter.skills?.crafting?.exp || 0,
                },
                items: {
                  [craftResult.itemKey]: craftResult.amount,
                },
                consumedItems: craftResult.consumedItems || {},
              };

              const successEmbed = buildFinalSkillResultEmbed(resultObject);

              try {
                await replyInteraction.editReply({
                  embeds: [successEmbed],
                  components: [],
                  content: null,
                });

                if (craftResult.uniqueCraftEmbed) {
                  await replyInteraction.followUp({
                    embeds: [craftResult.uniqueCraftEmbed],
                  });
                }
              } catch (editError) {
                console.error(
                  "[Craft Command] Error editing reply after successful multi-craft:",
                  editError
                );
              }
            } else {
              let errorDescription = craftResult.message;

              // Apply the same enhanced error handling for multi-select unique items
              if (
                craftResult.needsDetailedBreakdown ||
                (craftResult.message &&
                  (craftResult.message.includes(
                    "You don't have enough ingredients"
                  ) ||
                    (craftResult.message.includes("You need") &&
                      craftResult.message.includes("but you only have"))))
              ) {
                try {
                  const playerInventory = await getInventory(userId);
                  const recipe = itemData?.recipes?.[0];

                  if (recipe && playerInventory) {
                    // Create map of selected unique items for the breakdown
                    const selectedUniqueItems = {};
                    if (selectedItems && selectedItems.length > 0) {
                      // Count selected items by ingredient key
                      for (const item of selectedItems) {
                        const count =
                          selectedUniqueItems[item.ingredientKey] || 0;
                        selectedUniqueItems[item.ingredientKey] = count + 1;
                      }
                    }

                    const breakdown = formatCraftingMaterialsBreakdown(
                      recipe,
                      playerInventory,
                      allItems,
                      craftAmount,
                      selectedUniqueItems
                    );
                    errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
                  }
                } catch (breakdownError) {
                  console.error(
                    "[Craft Command] Error generating material breakdown in multi-select:",
                    breakdownError
                  );
                }
              }

              const errorEmbed = new EmbedBuilder()
                .setColor(EMBED_COLORS.LIGHT_RED)
                .setTitle("❌ Craft Failed")
                .setDescription(errorDescription);

              try {
                await replyInteraction.editReply({
                  embeds: [errorEmbed],
                  content: null,
                  components: [],
                });
              } catch (editError) {
                console.error(
                  "[Craft Command] Error editing reply after failed multi-craft:",
                  editError
                );
              }
            }
          } catch (error) {
            console.error(
              "[Craft Command] Error in multi-select collector:",
              error
            );
            await interaction.editReply({
              content: "❌ Selection timed out or failed. Please try again.",
              components: [],
            });
          }
          return;
        }

        // Single unique ingredient handling (existing logic)
        const uniqueIngredient = uniqueIngredients[0];
        const eligibleEquipment = (playerInventory.equipment || []).filter(
          (eq) => eq.itemKey === uniqueIngredient.itemKey
        );

        const eligibleAccessories = (playerAccessories || []).filter(
          (acc) => acc.itemKey === uniqueIngredient.itemKey
        );

        const eligibleItems = [...eligibleEquipment, ...eligibleAccessories];

        console.log(
          `[Craft] Found ${eligibleEquipment.length} eligible equipment items and ${eligibleAccessories.length} eligible accessories for ${uniqueIngredient.itemKey}`
        );

        if (eligibleItems.length === 0) {
          const itemName =
            allItems[uniqueIngredient.itemKey]?.name ||
            uniqueIngredient.itemKey;
          const emoji = allItems[uniqueIngredient.itemKey]?.emoji || "";
          await interaction.editReply({
            content: `You don't have any available ${emoji} ${itemName} to use for this craft.`,
          });
          return;
        }
        if (eligibleItems.length > 25) {
          await interaction.editReply({
            content:
              "You have too many eligible items. Please reduce your inventory to 25 or fewer to craft.",
          });
          return;
        }

        const options = eligibleItems
          .sort((a, b) => {
            const nameA = allItems[a.itemKey]?.name || a.itemKey;
            const nameB = allItems[b.itemKey]?.name || b.itemKey;
            return nameA.localeCompare(nameB) || a.id.localeCompare(b.id);
          })
          .map((item) => ({
            label: `${allItems[item.itemKey]?.name || item.itemKey} #${item.id.slice(
              0,
              6
            )}`,
            description: item.isEquipped
              ? "Equipped"
              : eligibleAccessories.some(
                    (acc) =>
                      acc.accessory_id === item.id ||
                      acc.accessory_id === item.accessory_id
                  )
                ? "Accessory"
                : undefined,
            value: item.id,
          }));

        const selectionId = `craft_select_${Date.now()}`;

        const selectMenu = new StringSelectMenuBuilder()
          .setCustomId(selectionId)
          .setPlaceholder("Select which item to use")
          .addOptions(options);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        const itemName =
          allItems[uniqueIngredient.itemKey]?.name || uniqueIngredient.itemKey;
        const emoji = allItems[uniqueIngredient.itemKey]?.emoji || "";
        const selectionEmbed = craftingUtils.createUniqueItemSelectionEmbed(
          itemName,
          emoji
        );

        const replyMessage = await interaction.editReply({
          embeds: [selectionEmbed],
          components: [row],
        });

        // Create a filter that only matches this specific selection
        const collectorFilter = (i) =>
          i.customId === selectionId && i.user.id === userId;
        let interactionHandled = false;

        try {
          const collectedInteraction = await replyMessage.awaitMessageComponent(
            {
              filter: collectorFilter,
              componentType: ComponentType.StringSelect,
              time: 60000, // 60 seconds
            }
          );

          // Single selection handling
          const selectedEquipmentId = collectedInteraction.values[0];

          // Try to defer the update to avoid "This interaction has already been acknowledged"
          try {
            await collectedInteraction.deferUpdate();
            interactionHandled = true;
          } catch (deferError) {
            console.error(
              "[Craft Command] Error deferring update on select interaction:",
              deferError
            );

            // Check if this is a known "interaction no longer available" error
            if (deferError.code === 10062 || deferError.code === 40060) {
              // Log and continue with the original interaction
              console.log(
                `[Craft Command] Select interaction unavailable (${deferError.code}), falling back to original interaction`
              );
            } else {
              throw deferError;
            }
          }

          // Attempt to craft the item
          const craftResult = await craftingUtils.attemptCraft(
            userId,
            itemKeyToCraft,
            1,
            selectedEquipmentId,
            interaction,
            { recipeIndex: chosenRecipeIndex }
          );

          const replyInteraction = interactionHandled
            ? collectedInteraction
            : interaction;

          if (craftResult.success) {
            // Get updated character data to show skill progress
            const { getPlayerData } = require("../utils/playerDataManager");
            const updatedCharacter = await getPlayerData(userId);

            // Create proper action result embed with skill progress bar
            const {
              buildFinalSkillResultEmbed,
            } = require("../utils/displayUtils");

            const resultObject = {
              title: "Crafted",
              color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
              skillName: "crafting",
              exp: craftResult.craftingXpGained || 0,
              petExp: craftResult.petXpGained || 0,
              tamingExp: craftResult.tamingXpGained || 0,
              finalCharacterData: updatedCharacter,
              finalSkillData: {
                exp: updatedCharacter.skills?.crafting?.exp || 0,
              },
              items: {
                [craftResult.itemKey]: craftResult.amount,
              },
              consumedItems: craftResult.consumedItems || {},
            };

            const successEmbed = buildFinalSkillResultEmbed(resultObject);

            try {
              await replyInteraction.editReply({
                embeds: [successEmbed],
                components: [],
                content: null,
              });

              if (craftResult.uniqueCraftEmbed) {
                await replyInteraction.followUp({
                  embeds: [craftResult.uniqueCraftEmbed],
                });
              }
            } catch (editError) {
              console.error(
                "[Craft Command] Error editing reply after successful single-craft:",
                editError
              );
            }
          } else {
            let errorDescription = craftResult.message;

            // Apply the same enhanced error handling for single-select unique items
            if (
              craftResult.needsDetailedBreakdown ||
              (craftResult.message &&
                (craftResult.message.includes(
                  "You don't have enough ingredients"
                ) ||
                  (craftResult.message.includes("You need") &&
                    craftResult.message.includes("but you only have"))))
            ) {
              try {
                const playerInventory = await getInventory(userId);
                const recipe = itemData?.recipes?.[0];

                if (recipe && playerInventory) {
                  // Create map of selected unique items for the breakdown
                  const selectedUniqueItems = {};
                  if (uniqueIngredients && uniqueIngredients.length > 0) {
                    // For single-select, we know one unique item was selected
                    const uniqueIngredient = uniqueIngredients[0];
                    selectedUniqueItems[uniqueIngredient.itemKey] = 1; // 1 item selected
                  }

                  const breakdown = formatCraftingMaterialsBreakdown(
                    recipe,
                    playerInventory,
                    allItems,
                    craftAmount,
                    selectedUniqueItems
                  );
                  errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
                }
              } catch (breakdownError) {
                console.error(
                  "[Craft Command] Error generating material breakdown in single-select:",
                  breakdownError
                );
              }
            }

            const errorEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.LIGHT_RED)
              .setTitle("❌ Craft Failed")
              .setDescription(errorDescription);

            try {
              await replyInteraction.editReply({
                embeds: [errorEmbed],
                content: null,
                components: [],
              });
            } catch (editError) {
              console.error(
                "[Craft Command] Error editing reply after failed single-craft:",
                editError
              );
            }
          }
        } catch (error) {
          console.error(
            "[Craft Command] Error in single-select collector:",
            error
          );
          await interaction.editReply({
            content: "❌ Selection timed out or failed. Please try again.",
            components: [],
          });
        }
      }

      // BEGIN FIX: Only run this default crafting logic if there are no unique ingredients.
      if (!hasUniqueIngredient) {
        try {
          const craftResult = await craftingUtils.attemptCraft(
            userId,
            itemKeyToCraft,
            craftAmount,
            null,
            interaction,
            { recipeIndex: chosenRecipeIndex }
          );

          if (craftResult.success) {
            // Get updated character data to show skill progress
            const { getPlayerData } = require("../utils/playerDataManager");
            const updatedCharacter = await getPlayerData(userId);

            // Create proper action result embed with skill progress bar
            const {
              buildFinalSkillResultEmbed,
            } = require("../utils/displayUtils");

            const resultObject = {
              title: "Crafted",
              color: require("../gameConfig").EMBED_COLORS.LIGHT_GREEN,
              skillName: "crafting",
              exp: craftResult.craftingXpGained || 0,
              petExp: craftResult.petXpGained || 0,
              tamingExp: craftResult.tamingXpGained || 0,
              finalCharacterData: updatedCharacter,
              finalSkillData: {
                exp: updatedCharacter.skills?.crafting?.exp || 0,
              },
              items: {
                [craftResult.itemKey]: craftResult.amount,
              },
              consumedItems: craftResult.consumedItems || {},
            };

            const successEmbed = buildFinalSkillResultEmbed(resultObject);

            await interaction.editReply({
              embeds: [successEmbed],
              components: [],
            });

            if (craftResult.uniqueCraftEmbed) {
              await interaction.followUp({
                embeds: [craftResult.uniqueCraftEmbed],
              });
            }
          } else {
            let errorDescription = craftResult.message;

            // If it's an insufficient materials error, show detailed breakdown
            if (
              craftResult.needsDetailedBreakdown ||
              (craftResult.message &&
                (craftResult.message.includes(
                  "You don't have enough ingredients"
                ) ||
                  (craftResult.message.includes("You need") &&
                    craftResult.message.includes("but you only have"))))
            ) {
              try {
                const playerInventory = await getInventory(userId);
                const recipe = itemData?.recipes?.[0];

                if (recipe && playerInventory) {
                  const breakdown = formatCraftingMaterialsBreakdown(
                    recipe,
                    playerInventory,
                    allItems,
                    craftAmount
                  );
                  errorDescription = `You don't have enough materials to craft this item.\n\n**Required Materials:**\n${breakdown}`;
                } else {
                  console.log(
                    "[Craft Command] Missing recipe or inventory for breakdown:",
                    { hasRecipe: !!recipe, hasInventory: !!playerInventory }
                  );
                  // fallback to original message
                  errorDescription = craftResult.message;
                }
              } catch (breakdownError) {
                console.error(
                  "[Craft Command] Error generating material breakdown:",
                  breakdownError
                );
                // Fall back to original error message
                errorDescription = craftResult.message;
              }
            }

            const errorEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.LIGHT_RED) // Red for error
              .setTitle("❌ Craft Failed")
              .setDescription(errorDescription);

            await interaction.editReply({
              embeds: [errorEmbed],
            });
          }
        } catch (error) {
          console.error("[Craft Command] Error in attemptCraft:", error);
          await interaction.editReply({
            content: "An unexpected error occurred while trying to craft.",
          });
        }
      }
      // END FIX
    } catch (error) {
      return interaction.editReply({
        content: error.message,
      });
    }
  },

  async autocomplete(interaction) {
    try {
      const focusedValue = interaction.options.getFocused().toLowerCase();
      const allItems = configManager.getAllItems();
      const { itemMatchesSearch } = require("../utils/itemSearchUtils");

      const filteredChoices = Object.entries(allItems)
        .filter(([itemKey, itemData]) => {
          if (!itemData || !itemData.name) return false;

          // Exclude tier/variant entries
          if (
            /\(Tier \d+\)/i.test(itemData.name) ||
            /_TIER_\d+$/i.test(itemKey)
          )
            return false;

          // Handle pets
          if (itemData.type === "PET") {
            // Only include the base pet (not eggs/variants)
            const isBasePet = !itemData.resultPetKey && !!itemData.rarities;
            if (!isBasePet) return false;

            // Check if any variant (egg) has a recipe for this pet
            const hasVariantWithRecipe = Object.entries(allItems).some(
              ([otherKey, otherData]) => {
                if (otherKey === itemKey) return false;
                if (otherData?.resultPetKey !== itemKey) return false;
                return (
                  otherData?.recipes &&
                  Array.isArray(otherData.recipes) &&
                  otherData.recipes.length > 0
                );
              }
            );

            if (hasVariantWithRecipe) {
              return itemMatchesSearch(itemKey, itemData, focusedValue);
            }
            return false;
          }

          // Check if item has a recipe
          const hasRecipe =
            itemData?.recipes &&
            Array.isArray(itemData.recipes) &&
            itemData.recipes.length > 0;
          if (hasRecipe) {
            return itemMatchesSearch(itemKey, itemData, focusedValue);
          }

          return false;
        })
        .slice(0, 25)
        .map(([itemKey, itemData]) => ({
          name: itemData.name,
          value: itemKey,
        }));

      try {
        if (!interaction.responded) {
          await interaction.respond(filteredChoices);
        }
      } catch (respErr) {
        // Swallow double-ack/expired interaction errors for autocomplete
        if (respErr?.code === 40060 || respErr?.code === 10062) return;
        console.error("[Craft Autocomplete] Respond error:", respErr);
      }
    } catch (error) {
      console.error("[Craft Autocomplete] Error:", error);
      try {
        if (!interaction.responded) {
          await interaction.respond([]);
        }
      } catch (respErr) {
        if (respErr?.code === 40060 || respErr?.code === 10062) return;
        console.error("[Craft Autocomplete] Fallback respond error:", respErr);
      }
    }
  },
};
