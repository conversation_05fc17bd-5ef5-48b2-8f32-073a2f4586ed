/**
 * Minion Operation Mutex System
 * Prevents race conditions in minion operations by ensuring only one operation
 * per user can modify minion data at a time.
 */

const activeMinionOperations = new Map(); // userId -> Set of operation types
const minionLocks = new Map(); // userId -> Promise for current operation

/**
 * Acquires a lock for minion operations for a specific user
 * @param {string} userId - Discord user ID
 * @param {string} operationType - Type of operation (collect, upgrade, fuel, etc.)
 * @returns {Promise<function>} - Release function to call when operation is complete
 */
async function acquireMinionLock(userId, operationType) {
  // Wait for any existing operation to complete
  if (minionLocks.has(userId)) {
    try {
      await minionLocks.get(userId);
    } catch (error) {
      // Previous operation failed, but we can continue
      console.warn(
        `[MinionMutex] Previous operation failed for ${userId}:`,
        error.message
      );
    }
  }

  // Create a new operation promise
  let resolveOperation;
  let rejectOperation;

  const operationPromise = new Promise((resolve, reject) => {
    resolveOperation = resolve;
    rejectOperation = reject;
  });

  minionLocks.set(userId, operationPromise);

  // Track active operations
  if (!activeMinionOperations.has(userId)) {
    activeMinionOperations.set(userId, new Set());
  }
  activeMinionOperations.get(userId).add(operationType);

  // Lock acquired silently

  // Return release function
  const release = (error = null) => {
    try {
      // Remove from active operations
      const userOps = activeMinionOperations.get(userId);
      if (userOps) {
        userOps.delete(operationType);
        if (userOps.size === 0) {
          activeMinionOperations.delete(userId);
        }
      }

      // Remove the lock
      minionLocks.delete(userId);

      // Resolve or reject the operation
      if (error) {
        rejectOperation(error);
      } else {
        resolveOperation();
      }

      // Lock released silently
    } catch (releaseError) {
      console.error(
        `[MinionMutex] Error releasing lock for ${userId}:`,
        releaseError
      );
    }
  };

  return release;
}

/**
 * Checks if a user has any active minion operations
 * @param {string} userId - Discord user ID
 * @returns {boolean} - True if user has active operations
 */
function hasActiveMinionOperations(userId) {
  return (
    activeMinionOperations.has(userId) &&
    activeMinionOperations.get(userId).size > 0
  );
}

/**
 * Gets active operation types for a user
 * @param {string} userId - Discord user ID
 * @returns {Array<string>} - Array of active operation types
 */
function getActiveMinionOperations(userId) {
  const userOps = activeMinionOperations.get(userId);
  return userOps ? Array.from(userOps) : [];
}

/**
 * Wrapper function to execute minion operations with automatic locking
 * @param {string} userId - Discord user ID
 * @param {string} operationType - Type of operation
 * @param {function} operation - Async function to execute
 * @returns {Promise<any>} - Result of the operation
 */
async function withMinionLock(userId, operationType, operation) {
  const release = await acquireMinionLock(userId, operationType);

  try {
    const result = await operation();
    release(); // Success
    return result;
  } catch (error) {
    release(error); // Error
    throw error;
  }
}

module.exports = {
  acquireMinionLock,
  hasActiveMinionOperations,
  getActiveMinionOperations,
  withMinionLock,
};
