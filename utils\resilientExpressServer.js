/**
 * Express Server Resilience Utility
 * Provides robust Express server management with automatic recovery,
 * health monitoring, and graceful error handling.
 */

const express = require("express");
const { EventEmitter } = require("events");
let Sentry = null;
try {
  Sentry = require("@sentry/node");
} catch {
  /* optional */
}

class ResilientExpressServer extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      port: options.port || 3000,
      host: options.host || "0.0.0.0",
      maxRestartAttempts: options.maxRestartAttempts || 5,
      restartDelay: options.restartDelay || 5000, // 5 seconds
      healthCheckInterval: options.healthCheckInterval || 30000, // 30 seconds
      ...options,
    };

    this.app = null;
    this.server = null;
    this.isRunning = false;
    this.restartAttempts = 0;
    this.lastError = null;
    this.startTime = null;
    this.healthCheckTimer = null;
    this.requestCount = 0;
    this.errorCount = 0;

    this.setupApp();
  }

  /**
   * Setup Express application with error handling
   */
  setupApp() {
    this.app = express();

    // Basic middleware
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request counting middleware
    this.app.use((req, res, next) => {
      this.requestCount++;
      next();
    });

    // Health check endpoint
    this.app.get("/health-check", (req, res) => {
      res.json(this.getHealthStatus());
    });

    // Error handling middleware (must be last)
    this.app.use(this.errorHandler.bind(this));

    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log("[ResilientExpressServer] Express app configured");
  }

  /**
   * Express error handling middleware
   */
  errorHandler(err, req, res, _next) {
    this.errorCount++;
    console.error("[ResilientExpressServer] Express error:", err);
    if (Sentry) {
      Sentry.captureException(err, {
        tags: { handler: "ResilientExpressServer", route: req?.path },
        extra: { method: req?.method, requestId: req?.id },
      });
    }

    // Emit error event for monitoring
    this.emit("error", err);

    // Don't crash on errors, respond gracefully
    if (!res.headersSent) {
      res.status(500).json({
        error: "Internal server error",
        timestamp: new Date().toISOString(),
        requestId: req.id || "unknown",
      });
    }
  }

  /**
   * Add routes to the Express app
   */
  addRoutes(setupFunction) {
    if (typeof setupFunction !== "function") {
      throw new Error("setupFunction must be a function");
    }

    setupFunction(this.app);
    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log("[ResilientExpressServer] Routes added to Express app");
  }

  /**
   * Start the server with automatic restart capability
   */
  async start() {
    if (this.isRunning) {
      console.warn("[ResilientExpressServer] Server is already running");
      return;
    }

    try {
      await this.startServer();
      this.restartAttempts = 0; // Reset on successful start
      this.startHealthMonitoring();
      const QUIET_BOOT = process.env.QUIET_BOOT === "true";
      if (!QUIET_BOOT)
        console.log(
          `[ResilientExpressServer] Server successfully started on ${this.options.host}:${this.options.port}`
        );
    } catch (error) {
      console.error("[ResilientExpressServer] Failed to start server:", error);
      if (Sentry)
        Sentry.captureException(error, {
          tags: { handler: "ResilientExpressServer", phase: "start" },
        });
      await this.handleStartupError(error);
    }
  }

  /**
   * Start the actual server
   */
  startServer() {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(
        this.options.port,
        this.options.host,
        () => {
          this.isRunning = true;
          this.startTime = new Date();
          console.log(
            `[ResilientExpressServer] Server listening on ${this.options.host}:${this.options.port}`
          );
          resolve();
        }
      );

      // Align server keep-alive behavior to reduce client-side ECONNRESET on reused sockets
      try {
        // Defaults are sometimes short; make them explicit and slightly generous
        this.server.keepAliveTimeout = this.options.keepAliveTimeout || 65_000; // 65s
        this.server.headersTimeout =
          this.options.headersTimeout || this.server.keepAliveTimeout + 1_000; // > keepAliveTimeout
      } catch (timeoutConfigError) {
        console.warn(
          "[ResilientExpressServer] Failed to set server timeouts:",
          timeoutConfigError?.message
        );
      }

      // Handle server errors
      this.server.on("error", async (err) => {
        console.error("[ResilientExpressServer] Server error:", err);
        this.lastError = err;
        if (Sentry)
          Sentry.captureException(err, {
            tags: {
              handler: "ResilientExpressServer",
              phase: "server.on(error)",
            },
          });

        if (err.code === "EADDRINUSE") {
          reject(new Error(`Port ${this.options.port} is already in use`));
        } else {
          // For other errors, try to restart
          await this.handleServerError(err);
        }
      });

      // Handle server close
      this.server.on("close", () => {
        console.log("[ResilientExpressServer] Server closed");
        this.isRunning = false;
        this.emit("close");
      });

      // Set timeout for server startup
      setTimeout(() => {
        if (!this.isRunning) {
          reject(new Error("Server startup timeout"));
        }
      }, 10000); // 10 second timeout
    });
  }

  /**
   * Handle startup errors with retry logic
   */
  async handleStartupError(error) {
    this.restartAttempts++;

    if (this.restartAttempts >= this.options.maxRestartAttempts) {
      console.error(
        `[ResilientExpressServer] Max restart attempts (${this.options.maxRestartAttempts}) exceeded`
      );
      this.emit("maxRestartsExceeded", error);
      return;
    }

    console.log(
      `[ResilientExpressServer] Attempting restart ${this.restartAttempts}/${this.options.maxRestartAttempts} in ${this.options.restartDelay}ms...`
    );

    setTimeout(async () => {
      try {
        await this.start();
      } catch (retryError) {
        console.error("[ResilientExpressServer] Retry failed:", retryError);
      }
    }, this.options.restartDelay);
  }

  /**
   * Handle server runtime errors
   */
  async handleServerError(error) {
    console.error("[ResilientExpressServer] Handling server error:", error);
    this.lastError = error;
    this.emit("serverError", error);

    // Try to restart the server if it's a critical error
    if (this.isCriticalError(error)) {
      console.log(
        "[ResilientExpressServer] Critical error detected, restarting server..."
      );
      await this.restart();
    }
  }

  /**
   * Check if an error is critical enough to warrant a restart
   */
  isCriticalError(error) {
    const criticalCodes = ["ECONNRESET", "EPIPE", "ENOTFOUND", "ETIMEDOUT"];
    return (
      criticalCodes.includes(error.code) ||
      error.message.includes("socket hang up") ||
      error.message.includes("connection refused")
    );
  }

  /**
   * Restart the server
   */
  async restart() {
    console.log("[ResilientExpressServer] Restarting server...");

    try {
      await this.stop();
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait 1 second
      await this.start();
      console.log("[ResilientExpressServer] Server restarted successfully");
    } catch (restartError) {
      console.error(
        "[ResilientExpressServer] Failed to restart server:",
        restartError
      );
      if (Sentry)
        Sentry.captureException(restartError, {
          tags: { handler: "ResilientExpressServer", phase: "restart" },
        });
    }
  }

  /**
   * Stop the server gracefully
   */
  async stop() {
    if (!this.isRunning || !this.server) {
      console.log("[ResilientExpressServer] Server is not running");
      return;
    }

    return new Promise((resolve) => {
      this.stopHealthMonitoring();

      this.server.close(() => {
        console.log("[ResilientExpressServer] Server stopped gracefully");
        this.isRunning = false;
        this.server = null;
        resolve();
      });

      // Force close after timeout
      setTimeout(() => {
        if (this.isRunning) {
          console.warn(
            "[ResilientExpressServer] Force closing server after timeout"
          );
          this.server = null;
          this.isRunning = false;
          resolve();
        }
      }, 5000); // 5 second timeout
    });
  }

  /**
   * Start health monitoring
   */
  startHealthMonitoring() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(() => {
      const status = this.getHealthStatus();

      // Log health status periodically
      if (!status.healthy) {
        console.warn(
          "[ResilientExpressServer] Server health check failed:",
          status
        );
        this.emit("unhealthy", status);
      }

      // Reset error count periodically to prevent accumulation
      if (this.errorCount > 100) {
        console.log(
          `[ResilientExpressServer] Resetting error count (was ${this.errorCount})`
        );
        this.errorCount = 0;
      }
    }, this.options.healthCheckInterval);
  }

  /**
   * Stop health monitoring
   */
  stopHealthMonitoring() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }

  /**
   * Get current health status
   */
  getHealthStatus() {
    const uptime = this.startTime ? Date.now() - this.startTime.getTime() : 0;
    const errorRate =
      this.requestCount > 0 ? this.errorCount / this.requestCount : 0;

    return {
      healthy: this.isRunning && errorRate < 0.1, // Less than 10% error rate
      isRunning: this.isRunning,
      port: this.options.port,
      host: this.options.host,
      uptime,
      requestCount: this.requestCount,
      errorCount: this.errorCount,
      errorRate,
      restartAttempts: this.restartAttempts,
      lastError: this.lastError
        ? {
            message: this.lastError.message,
            code: this.lastError.code,
            timestamp: this.lastError.timestamp || new Date().toISOString(),
          }
        : null,
      memoryUsage: process.memoryUsage(),
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get the Express app instance
   */
  getApp() {
    return this.app;
  }

  /**
   * Get the server instance
   */
  getServer() {
    return this.server;
  }
}

module.exports = {
  ResilientExpressServer,
};
