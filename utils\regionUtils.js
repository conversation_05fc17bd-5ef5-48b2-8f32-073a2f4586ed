const regionMetadata = require("../data/regionMetadata.js"); // ADDED
const configManager = require("./configManager");
const { EmbedBuilder } = require("discord.js");
const { skillEmojis, EMBED_COLORS } = require("../gameConfig");

/**
 * Checks if a character can perform a specific skill action in their current region.
 *
 * @param {object} character - The player's character object.
 * @param {string} activityName - The name of the activity (e.g., 'farming', 'mining').
 * @param {string|null} resourceListKey - DEPRECATED - No longer used directly.
 * @param {string|null} resourceKey - The specific item key being gathered (e.g., 'WHEAT', 'COAL'), or null if not applicable (e.g., fishing).
 * @param {string} resourceNameSingular - The user-friendly singular name of the resource type (e.g., 'crop', 'ore', 'wood').
 * @returns {{allowed: boolean, message: string|null, embed: EmbedBuilder|null, errorType: string|null, errorContext: object|null}} - Object indicating if allowed and error details if not.
 */
function checkRegionPermissions(
  character,
  activityName,
  resourceListKey,
  resourceKey,
  resourceNameSingular
) {
  const currentRegionKey = character.current_region;
  const userId = character.discordId; // For logging

  // Check for Private Island first
  if (currentRegionKey === "Private Island") {
    const errorContext = { activityName };
    return {
      allowed: false,
      message: getRegionErrorMessage("private_island", errorContext),
      embed: createRegionErrorEmbed("private_island", errorContext),
      errorType: "private_island",
      errorContext,
    };
  }

  // Check if the region key itself is valid using metadata
  const currentRegionMeta = regionMetadata[currentRegionKey];
  if (!currentRegionMeta) {
    const errorContext = { activityName };
    return {
      allowed: false,
      message: getRegionErrorMessage("invalid_region", errorContext),
      embed: createRegionErrorEmbed("invalid_region", errorContext),
      errorType: "invalid_region",
      errorContext,
    };
  }
  const currentRegionName = currentRegionMeta.name;

  // --- Refactored Resource Check ---
  // If a specific resource is being targeted, check if *that resource* is available here.
  if (resourceKey) {
    // Special handling for combat - mobs are stored separately from items
    if (activityName === "combat") {
      const mobData = configManager.getMob(resourceKey);

      if (!mobData) {
        console.error(
          `[RegionCheck] Failed to find mob data for resourceKey: ${resourceKey} during permission check.`
        );
        const errorContext = { resourceNameSingular, activityName };
        return {
          allowed: false,
          message: getRegionErrorMessage("not_found", errorContext),
          embed: createRegionErrorEmbed("not_found", errorContext),
          errorType: "not_found",
          errorContext,
        };
      }

      if (
        !mobData.spawnRegions ||
        !mobData.spawnRegions.includes(currentRegionKey)
      ) {
        const resourceDisplayName =
          mobData.name || `that ${resourceNameSingular}`;
        console.log(
          `[RegionCheck] User ${userId} cannot fight ${resourceKey} in region ${currentRegionKey} (${currentRegionName}). SpawnRegions: ${mobData.spawnRegions}`
        );
        const errorContext = {
          resourceDisplayName,
          currentRegionName,
          activityName,
          resourceNameSingular,
        };
        return {
          allowed: false,
          message: getRegionErrorMessage("not_in_region", errorContext),
          embed: createRegionErrorEmbed("not_in_region", errorContext),
          errorType: "not_in_region",
          errorContext,
        };
      }
    } else {
      // Regular item validation for other activities (farming, mining, foraging)
      const resourceData = configManager.getItem(resourceKey);
      if (!resourceData) {
        // This shouldn't happen if commands validate input, but good failsafe
        console.error(
          `[RegionCheck] Failed to find item data for resourceKey: ${resourceKey} during permission check.`
        );
        const errorContext = { resourceNameSingular, activityName };
        return {
          allowed: false,
          message: getRegionErrorMessage("not_found", errorContext),
          embed: createRegionErrorEmbed("not_found", errorContext),
          errorType: "not_found",
          errorContext,
        };
      }

      // Enforce that the resource belongs to the same skill as the requested activity
      // e.g., prevent /farm DIAMOND (Mining resource) or /mine WHEAT (Farming resource)
      const resourceSourceSkill = (
        resourceData.sourceSkill || ""
      ).toLowerCase();
      const requestedSkill = (activityName || "").toLowerCase();
      if (resourceSourceSkill && resourceSourceSkill !== requestedSkill) {
        const resourceDisplayName =
          resourceData.name || `that ${resourceNameSingular}`;
        const errorContext = {
          resourceDisplayName,
          requestedSkill: requestedSkill,
          actualSkill: resourceSourceSkill,
        };
        return {
          allowed: false,
          message: getRegionErrorMessage("wrong_skill", errorContext),
          embed: createRegionErrorEmbed("wrong_skill", errorContext),
          errorType: "wrong_skill",
          errorContext,
        };
      }

      if (
        !resourceData.foundInRegions ||
        !resourceData.foundInRegions.includes(currentRegionKey)
      ) {
        const resourceDisplayName =
          resourceData.name || `that ${resourceNameSingular}`;
        const errorContext = {
          resourceDisplayName,
          currentRegionName,
          activityName,
          resourceNameSingular,
        };
        return {
          allowed: false,
          message: getRegionErrorMessage("not_in_region", errorContext),
          embed: createRegionErrorEmbed("not_in_region", errorContext),
          errorType: "not_in_region",
          errorContext,
        };
      }
    }
  } else if (activityName === "fishing") {
    // Check if fishing is allowed in this region using the allowsWaterFishing property
    if (!currentRegionMeta.allowsWaterFishing) {
      const errorContext = { currentRegionName, activityName };
      return {
        allowed: false,
        message: getRegionErrorMessage("fishing_not_allowed", errorContext),
        embed: createRegionErrorEmbed("fishing_not_allowed", errorContext),
        errorType: "fishing_not_allowed",
        errorContext,
      };
    }
    // Fishing allowed in this region
  } else {
    // If no resourceKey is provided, and it's not a special case like fishing,
    // how do we know if the *activity* is allowed? The old `regions.js` had an `activities` list.
    // We could potentially check if *any* item related to that skill type exists in the region,
    // but that's inefficient. For now, if no specific resource is checked,
    // we might have to assume it's allowed or disallow generic actions.
    // Disallow if no resourceKey is provided and it's not fishing, for safety.
    const errorContext = { activityName };
    return {
      allowed: false,
      message: getRegionErrorMessage("no_resource", errorContext),
      embed: createRegionErrorEmbed("no_resource", errorContext),
      errorType: "no_resource",
      errorContext,
    };
    // Alternative: Assume allowed if region is valid.
    // console.log(`[RegionCheck] Allowing generic activity '${activityName}' in region ${currentRegionName} as no specific resource was checked.`);
  }
  // --- End Refactored Resource Check ---

  // All relevant checks passed
  return {
    allowed: true,
    message: null,
    embed: null,
    errorType: null,
    errorContext: null,
  };
}

/**
 * Creates a standardized error embed for region validation failures.
 * @param {string} type - The type of error.
 * @param {object} context - Context data for the error message.
 * @returns {EmbedBuilder} The error embed.
 */
function createRegionErrorEmbed(type, context = {}) {
  const {
    activityName,
    resourceNameSingular,
    resourceDisplayName,
    currentRegionName,
    requestedSkill,
    actualSkill,
  } = context;

  // Get the appropriate skill emoji
  const skillEmoji = skillEmojis[activityName] || "❌";

  let title, description;

  switch (type) {
    case "not_found":
      title = `${skillEmoji} Invalid ${resourceNameSingular || "Resource"}`;
      description = `That ${resourceNameSingular || "resource"} does not exist or cannot be gathered here. Please select a valid option from the list.`;
      break;
    case "not_in_region":
      title = `${skillEmoji} Region Restriction`;
      description = `You cannot gather **${resourceDisplayName || "that resource"}** in **${currentRegionName || "this region"}**.`;
      break;
    case "invalid_region":
      title = `❌ Invalid Region`;
      description =
        "Invalid current region found in your data. Try moving to a known area.";
      break;
    case "private_island":
      title = `🏝️ Private Island Restriction`;
      description = `You cannot perform ${activityName || "this action"} on your Private Island.`;
      break;
    case "fishing_not_allowed":
      title = `${skillEmojis.fishing || "🎣"} No Water Available`;
      description = `You cannot fish in **${currentRegionName || "this region"}**. Try going to a region with water.`;
      break;
    case "no_resource":
      title = `${skillEmoji} Target Required`;
      description = `Cannot perform generic ${activityName || "this action"} here. Please specify a target.`;
      break;
    case "wrong_skill": {
      // Map skill to suggested command for helpful guidance
      const suggestCmdMap = {
        farming: "/farm",
        mining: "/mine",
        foraging: "/forage",
        alchemy: "/brew",
      };
      const suggested = suggestCmdMap[actualSkill] || "the correct command";
      title = `🚫 Wrong Skill`;
      description = `You cannot ${requestedSkill || "perform this action"} **${resourceDisplayName || "that resource"}**. Try ${suggested} instead.`;
      break;
    }
    default:
      title = `❌ Unknown Error`;
      description = "An unknown error occurred.";
  }

  return new EmbedBuilder()
    .setColor(EMBED_COLORS.ERROR) // Red color for errors
    .setTitle(title)
    .setDescription(description);
}

function getRegionErrorMessage(type, context = {}) {
  switch (type) {
    case "not_found":
      return `That ${context.resourceNameSingular || "resource"} does not exist or cannot be gathered here. Please select a valid option from the list.`;
    case "not_in_region":
      return `You cannot gather ${context.resourceDisplayName || "that resource"} in ${context.current_regionName || "this region"}.`;
    case "invalid_region":
      return "Invalid current region found in your data. Try moving to a known area.";
    case "private_island":
      return `You cannot perform ${context.activityName || "this action"} on your Private Island.`;
    case "fishing_not_allowed":
      return `You cannot fish in ${context.current_regionName || "this region"}. Try going to a region with water.`;
    case "no_resource":
      return `Cannot perform generic ${context.activityName || "this action"} here. Please specify a target.`;
    case "wrong_skill": {
      const suggestCmdMap = {
        farming: "/farm",
        mining: "/mine",
        foraging: "/forage",
        alchemy: "/brew",
      };
      const suggested =
        suggestCmdMap[context.actualSkill] || "the correct command";
      return `You cannot ${context.requestedSkill || "perform this action"} ${context.resourceDisplayName || "that resource"}. Try ${suggested} instead.`;
    }
    default:
      return "An unknown error occurred.";
  }
}

module.exports = { checkRegionPermissions, createRegionErrorEmbed };
