const { EMBED_COLORS } = require("../gameConfig.js");
const { getPlayerData } = require("./playerDataManager");
const {
  getAllPendingActions,
  completeAction,
  updateActionMessageId,
} = require("./actionPersistence");
// Removed sharedState imports - using simple abort system now
const { clearActivity } = require("./activityManager.js");
const { EmbedBuilder } = require("discord.js");
const {
  shouldHandleAction,
  isMessageFromCorrectWorker,
  isCorruptedAction,
  isValidWorkerId,
} = require("./workerIdMapping");

// Import unified action system
const { startOrResumeAction } = require("./actionHandlers.js");
// Import combat handler (still needs direct import for now)
const { handleCombatAction } = require("../commands/combat.js");

// Map action types to their core logic functions (Simplified)
const actionHandlers = {
  farming: (
    interaction,
    resourceKey,
    amount,
    isAgain,
    originalAmount,
    character
  ) => {
    return startOrResumeAction(interaction, "farming", {
      character,
      resourceKey,
      amount: interaction.isResumption ? originalAmount : amount,
      isResumption: interaction.isResumption || false,
      actionId: interaction.actionId,
      pendingLoot: interaction.pendingLoot,
      startingCycle: interaction.startingCycle || 0,
    });
  },
  mining: (
    interaction,
    resourceKey,
    amount,
    isAgain,
    originalAmount,
    character
  ) => {
    return startOrResumeAction(interaction, "mining", {
      character,
      resourceKey,
      amount: interaction.isResumption ? originalAmount : amount,
      isResumption: interaction.isResumption || false,
      actionId: interaction.actionId,
      pendingLoot: interaction.pendingLoot,
      startingCycle: interaction.startingCycle || 0,
    });
  },
  fishing: (
    interaction,
    resourceKey,
    amount,
    isAgain,
    originalAmount,
    character
  ) => {
    return startOrResumeAction(interaction, "fishing", {
      character,
      resourceKey,
      amount: interaction.isResumption ? originalAmount : amount,
      isResumption: interaction.isResumption || false,
      actionId: interaction.actionId,
      pendingLoot: interaction.pendingLoot,
      startingCycle: interaction.startingCycle || 0,
    });
  },
  foraging: (
    interaction,
    resourceKey,
    amount,
    isAgain,
    originalAmount,
    character
  ) => {
    return startOrResumeAction(interaction, "foraging", {
      character,
      resourceKey,
      amount: interaction.isResumption ? originalAmount : amount,
      isResumption: interaction.isResumption || false,
      actionId: interaction.actionId,
      pendingLoot: interaction.pendingLoot,
      startingCycle: interaction.startingCycle || 0,
    });
  },
  combat: handleCombatAction,
  alchemy: (
    interaction,
    resourceKey,
    amount,
    isAgain,
    originalAmount,
    character
  ) => {
    return startOrResumeAction(interaction, "alchemy", {
      character,
      resourceKey,
      amount: interaction.isResumption ? originalAmount : amount,
      isResumption: interaction.isResumption || false,
      actionId: interaction.actionId,
      pendingLoot: interaction.pendingLoot,
      startingCycle: interaction.startingCycle || 0,
    });
  },
};

/**
 * Resumes a single pending action loop by fetching the original message and re-invoking the appropriate handleMulti function.
 * @param {Client} client Discord client instance.
 * @param {object} actionRecord The action record from the database.
 * @param {Map} resumedStopRequests Map tracking stop requests for resumed actions.
 * @param {Map} activeResumedMessages Map tracking the message object for resumed actions.
 */
async function resumeActionLoop(client, actionRecord) {
  // Start resumption (startup log lines reduced for noise control)

  // CRITICAL SAFETY CHECK #1: Handle corrupted actions with missing worker_id
  if (isCorruptedAction(actionRecord.worker_id)) {
    console.error(
      `[resumeActionLoop] CORRUPTED ACTION DETECTED: Action ${actionRecord.id} has no worker_id - this indicates serious system corruption`
    );
    console.error(
      `[resumeActionLoop] Action details: User ${actionRecord.user_id}, Type: ${actionRecord.action_type}, Channel: ${actionRecord.channel_id}`
    );

    // Attempt to notify the user about the corrupted action
    try {
      const targetChannel = await client.channels.fetch(
        actionRecord.channel_id
      );
      if (targetChannel) {
        const corruptionEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle("🚨 Action System Error")
          .setDescription(
            `<@${actionRecord.user_id}> Your ${actionRecord.action_type || "unknown"} action encountered a critical system error and has been corrupted.\n\n` +
              `**Action ID:** ${actionRecord.id}\n` +
              `**Status:** Action has been terminated and cleaned up\n` +
              `**Next Steps:** Please contact a developer if this continues to happen\n\n` +
              `⚠️ **This is a serious system issue that requires developer attention.**`
          )
          .setFooter({ text: "You can start a new action when ready." });

        await targetChannel.send({ embeds: [corruptionEmbed] });
        console.log(
          `[resumeActionLoop] Notified user ${actionRecord.user_id} about corrupted action ${actionRecord.id}`
        );
      }
    } catch (notificationError) {
      console.error(
        `[resumeActionLoop] Failed to notify user about corrupted action ${actionRecord.id}:`,
        notificationError.message
      );
    }

    // Clean up the corrupted action immediately
    try {
      await completeAction(actionRecord.id);
      await clearActivity(actionRecord.user_id);
      console.log(
        `[resumeActionLoop] Cleaned up corrupted action ${actionRecord.id}`
      );
    } catch (cleanupError) {
      console.error(
        `[resumeActionLoop] Failed to clean up corrupted action ${actionRecord.id}:`,
        cleanupError.message
      );
    }

    return; // CRITICAL: Never process corrupted actions
  }

  // CRITICAL SAFETY CHECK #2: Use proper worker mapping to determine if this process should handle the action
  if (!shouldHandleAction(actionRecord.worker_id, client.user?.id)) {
    const currentIdentity = process.env.WORKER_BOT_ID
      ? `Worker ${process.env.WORKER_BOT_ID}`
      : `Main Bot ${client.user?.id}`;
    const expectedWorker = actionRecord.worker_id || "NO_WORKER_ID";

    console.log(
      `[resumeActionLoop] WORKER FILTERING: ${currentIdentity} skipping action ${actionRecord.id} - should be handled by ${expectedWorker}`
    );
    return; // Exit early - this action should be handled by a different process
  }

  console.log(
    `[resumeActionLoop] WORKER VALIDATION: Action ${actionRecord.id} correctly assigned to current process`
  );

  const {
    id: actionId,
    user_id: userId,
    action_type: actionType,
    resource_key,
    total_amount: totalAmount,
    completed_cycles: initialCompletedCycles,
    channel_id: channelId,
    message_id: messageId,
  } = actionRecord;

  // Validate critical action fields before proceeding
  if (
    !actionId ||
    !userId ||
    !actionType ||
    typeof totalAmount !== "number" ||
    typeof initialCompletedCycles !== "number"
  ) {
    console.error(
      `[resumeActionLoop] Invalid action record fields for action ${actionId || "unknown"}:`,
      { actionId, userId, actionType, totalAmount, initialCompletedCycles }
    );
    if (actionId) {
      await completeAction(actionId);
      await clearActivity(userId);
    }
    return;
  }

  // Validate that the action type is supported
  if (!actionType || typeof actionType !== "string") {
    console.error(
      `[resumeActionLoop] Invalid or missing action_type for action ${actionId}: ${actionType}`
    );
    await completeAction(actionId);
    await clearActivity(userId);
    return;
  }

  // Normalize action type to lowercase for consistent handler lookup (handles legacy uppercase data)
  const normalizedActionType = actionType.toLowerCase();

  // Get the handler function directly from the simplified map
  const handler = actionHandlers[normalizedActionType];

  // Log when we encounter uppercase legacy data
  if (actionType !== normalizedActionType) {
    console.warn(
      `[resumeActionLoop] Found legacy uppercase action_type "${actionType}" for action ${actionId}, normalized to "${normalizedActionType}"`
    );
  }
  // atomic activity check before resuming
  const { trySetActivity } = require("./activityManager.js");
  const activitySet = await trySetActivity(userId, normalizedActionType);
  if (!activitySet) {
    console.warn(
      `[resumeActionLoop] Skipping resumption for action ${actionId}: user ${userId} is already busy with another activity.`
    );
    return;
  }
  // Check if a handler exists and is a function
  if (!handler || typeof handler !== "function") {
    console.error(
      `[resumeActionLoop] Invalid or missing handler function for action type "${normalizedActionType}" (original: "${actionType}") (ID ${actionId}). Cannot resume.`
    );
    await completeAction(actionId); // Clean up invalid action type
    await clearActivity(userId); // Clear activity from active_actions table
    return;
  }
  if (!channelId) {
    console.error(
      `[resumeActionLoop] Missing channelId for action ID ${actionId}. Cannot resume.`
    );
    await completeAction(actionId);
    await clearActivity(userId); // Clear activity from active_actions table
    return;
  }
  let character;
  let targetChannel;
  let targetMessage;
  let persistedParams = {};
  try {
    // --- Try to load character data ---
    try {
      character = await getPlayerData(userId);

      // Enhanced character data validation for resumption
      if (!character) {
        throw new Error(`No character data found for user ${userId}`);
      }

      // Validate critical character properties for resumption
      const validationErrors = [];

      if (!character.discordId) {
        character.discordId = userId; // Fix missing discordId
      }

      if (!character.name || typeof character.name !== "string") {
        validationErrors.push("Missing or invalid character name");
      }

      if (
        !character.current_region ||
        typeof character.current_region !== "string"
      ) {
        character.current_region = "the_hub"; // Default region
        console.warn(
          `[resumeActionLoop] Fixed missing currentRegion for user ${userId}`
        );
      }

      if (
        typeof character.current_health !== "number" ||
        character.current_health < 0
      ) {
        character.current_health = 100; // Default health
        console.warn(
          `[resumeActionLoop] Fixed invalid currentHealth for user ${userId}`
        );
      }

      // Ensure coins and bank exist for compatibility
      if (typeof character.coins !== "number") {
        character.coins = 0;
        console.warn(
          `[resumeActionLoop] Fixed missing coins for user ${userId}`
        );
      }
      if (typeof character.bank !== "number") {
        character.bank = 0;
        console.warn(
          `[resumeActionLoop] Fixed missing bank for user ${userId}`
        );
      }

      if (!character.skills || typeof character.skills !== "object") {
        character.skills = {};
        console.warn(
          `[resumeActionLoop] Fixed missing skills for user ${userId}`
        );
      }

      if (!character.stats || typeof character.stats !== "object") {
        character.stats = {};
        console.warn(
          `[resumeActionLoop] Fixed missing stats for user ${userId}`
        );
      }

      if (!character.collections || typeof character.collections !== "object") {
        character.collections = {};
        console.warn(
          `[resumeActionLoop] Fixed missing collections for user ${userId}`
        );
      }

      if (!Array.isArray(character.pets)) {
        character.pets = [];
        console.warn(
          `[resumeActionLoop] Fixed invalid pets array for user ${userId}`
        );
      }

      if (!character.inventory || typeof character.inventory !== "object") {
        character.inventory = { items: {}, equipment: [] };
        console.warn(
          `[resumeActionLoop] Fixed missing inventory for user ${userId}`
        );
      }

      if (
        !character.inventory.items ||
        typeof character.inventory.items !== "object"
      ) {
        character.inventory.items = {};
        console.warn(
          `[resumeActionLoop] Fixed missing inventory items for user ${userId}`
        );
      }

      if (!Array.isArray(character.inventory.equipment)) {
        character.inventory.equipment = [];
        console.warn(
          `[resumeActionLoop] Fixed invalid equipment array for user ${userId}`
        );
      }

      // Validate action-specific requirements
      if (
        normalizedActionType === "fishing" ||
        normalizedActionType === "combat"
      ) {
        // Ensure stats are properly calculated for fishing/combat
        try {
          const { calculateAllStats } = require("./statCalculations");
          const allStats = calculateAllStats(character);
          if (!allStats || typeof allStats.HEALTH !== "number") {
            console.warn(
              `[resumeActionLoop] Stat calculation failed for user ${userId}, will recalculate during action`
            );
          }
        } catch (statError) {
          console.warn(
            `[resumeActionLoop] Stat validation failed for user ${userId}:`,
            statError.message
          );
        }
      }

      if (validationErrors.length > 0) {
        console.warn(
          `[resumeActionLoop] Character validation warnings for user ${userId}: ${validationErrors.join(", ")}`
        );
        // Save the corrected character data
        try {
          const { savePlayerData } = require("./playerDataManager");
          await savePlayerData(userId, character);
          console.log(
            `[resumeActionLoop] Saved corrected character data for user ${userId}`
          );
        } catch (saveError) {
          console.error(
            `[resumeActionLoop] Failed to save corrected character data for user ${userId}:`,
            saveError
          );
          // Continue anyway - the corrections are in memory
        }
      }

      console.log(
        `[resumeActionLoop] Character data validated successfully for user ${userId}`
      );
    } catch (characterError) {
      console.error(
        `[resumeActionLoop] Error loading/validating character data for action ${actionId}:`,
        characterError
      );
      await completeAction(actionId);
      await clearActivity(userId); // Clear activity from active_actions table
      return;
    }

    // --- Defensive check for parameters_json ---
    const MAX_PARAMS_JSON_LENGTH = 1 * 1024 * 1024; // 1 MB limit
    if (
      actionRecord.parameters_json &&
      typeof actionRecord.parameters_json === "string" &&
      actionRecord.parameters_json.length > MAX_PARAMS_JSON_LENGTH
    ) {
      console.error(
        `[resumeActionLoop][${actionId}] CRITICAL: parameters_json for action ID ${actionId} is excessively large (${actionRecord.parameters_json.length} bytes). Skipping parse and completing action.`
      );
      await completeAction(actionId);
      clearActivity(userId);
      return; // Stop processing this action
    }
    // --- End defensive check ---

    if (actionRecord.parameters_json) {
      try {
        persistedParams = JSON.parse(actionRecord.parameters_json);
      } catch (e) {
        console.error(
          `[resumeActionLoop][${actionId}] Error parsing parameters_json:`,
          e
        );
      }
    }
    if (!character && persistedParams?.pendingSkillAction?.character) {
      character = persistedParams.pendingSkillAction.character;
      console.warn(
        `[resumeActionLoop][${actionId}] Fallback: Using persisted character from action record for userId: ${userId}`
      );
    }
    console.log(
      `[resumeActionLoop][${actionId}] User ${userId} player data ${
        character ? "loaded" : "NOT LOADED"
      }.`
    );
    if (!character) {
      console.error(
        `[resumeActionLoop][${actionId}] CRITICAL: Character is null for userId: ${userId}. Action will be cleaned up.`
      );
      console.error(
        `[resumeActionLoop][${actionId}] ActionRecord dump:`,
        JSON.stringify(actionRecord, null, 2)
      );
      await completeAction(actionId);
      clearActivity(userId); // Ensure activity is cleared
      return;
    }
    targetChannel = await client.channels.fetch(channelId);
    console.log(`[resumeActionLoop] Fetched channel: ${channelId}`);
    if (!character)
      throw new Error(`No character data found for user ${userId}`);
    if (!targetChannel)
      throw new Error(`Target channel ${channelId} not found or inaccessible`);

    // --- Try to recover the original message first, create fresh one only if needed ---
    // First, get the most recent messageId from database in case it was updated by message cycling
    let currentMessageId = messageId;
    try {
      const { getActionById } = require("./actionPersistence");
      const currentActionRecord = await getActionById(actionId);
      if (
        currentActionRecord &&
        currentActionRecord.message_id &&
        currentActionRecord.message_id !== messageId
      ) {
        console.log(
          `[resumeActionLoop] Message ID was updated during action lifecycle: ${messageId} -> ${currentActionRecord.message_id}`
        );
        currentMessageId = currentActionRecord.message_id;
      }
    } catch (actionFetchError) {
      console.warn(
        `[resumeActionLoop] Could not fetch latest action record for message ID check:`,
        actionFetchError.message
      );
      // Continue with original messageId
    }

    try {
      if (currentMessageId) {
        console.log(
          `[resumeActionLoop] Attempting to recover message ${currentMessageId} for action ${actionId}`
        );
        targetMessage = await targetChannel.messages.fetch(currentMessageId);

        console.log(
          `[resumeActionLoop] Successfully recovered message ${currentMessageId} for action ${actionId}`
        );

        // CRITICAL MESSAGE OWNERSHIP CHECK: Use proper worker mapping to verify message ownership
        if (
          !isMessageFromCorrectWorker(
            targetMessage.author.id,
            actionRecord.worker_id
          )
        ) {
          console.error(
            `[resumeActionLoop] CRITICAL MESSAGE MISMATCH: Message ${currentMessageId} was created by Discord ID ${targetMessage.author.id}`
          );
          console.error(
            `[resumeActionLoop] But action ${actionId} is assigned to worker_id ${actionRecord.worker_id}`
          );
          console.error(
            `[resumeActionLoop] Current process: ${process.env.WORKER_BOT_ID ? `Worker ${process.env.WORKER_BOT_ID}` : `Main Bot ${client.user?.id}`}`
          );
          console.error(
            `[resumeActionLoop] This suggests a worker_id/Discord bot mapping issue. Creating a new message instead.`
          );

          // Instead of terminating, create a new message and continue processing
          // This handles cases where worker_id database mappings got mixed up
          targetMessage = null; // Force creation of new message below
        } else {
          // Edit original message to indicate resumption
          try {
            // Skip immediate edit here; the running action loop will refresh the message with the latest embed
            console.log(
              `[resumeActionLoop] Skipping message edit for ${currentMessageId} - action loop will refresh the message`
            );
          } catch (editError) {
            console.warn(
              `[resumeActionLoop] Could not edit message ${currentMessageId}:`,
              editError.message
            ); // CRITICAL FIX: If we can't edit the message (probably created by different worker),
            // DO NOT create a new message here - let the worker handle this action
            if (editError.code === 50005) {
              // Cannot edit message authored by another user
              console.error(
                `[resumeActionLoop] Message ${currentMessageId} was created by different worker. This action should be delegated to the correct worker, not processed locally.`
              );
              console.error(
                `[resumeActionLoop] Terminating local processing to prevent conflicts. Action ${actionId} should be handled by worker ${actionRecord.worker_id}.`
              );

              // Clear the activity to prevent the user from being stuck
              await clearActivity(userId);
              return;
            }
            // Continue with the message anyway for other errors - the action will update it
          }
        }
      } else {
        console.warn(
          `[resumeActionLoop] No messageId found for action ${actionId}. This should not happen unless the original message was never saved or was deleted. Creating a new message for resumption.`
        );
        targetMessage = null;
      }
    } catch (fetchError) {
      console.warn(
        `[resumeActionLoop] Failed to recover message ${currentMessageId}:`,
        fetchError.message
      );
      targetMessage = null;
    }

    // --- If original message recovery failed, create a fresh one ---
    if (!targetMessage) {
      console.warn(
        `[resumeActionLoop] Unable to recover original message for action ${actionId}. Creating a new message for resumption. This may indicate the original message was deleted or never saved.`
      );
      console.log(
        `[resumeActionLoop] Creating fresh resumption message for ${normalizedActionType} action ${actionId} in channel ${channelId}`
      );
      try {
        // Validate all variables before creating embed to prevent undefined errors
        const safeActionType = normalizedActionType || "unknown";
        const safeUserId = userId || "unknown";
        const safeInitialCycles =
          typeof initialCompletedCycles === "number"
            ? initialCompletedCycles
            : 0;
        const safeTotalAmount =
          typeof totalAmount === "number" ? totalAmount : 1;

        const resumeEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.YELLOW)
          .setTitle(
            `🔄 Resuming ${safeActionType.charAt(0).toUpperCase() + safeActionType.slice(1)} Action`
          )
          .setDescription(
            `<@${safeUserId}> Your ${safeActionType} action is being resumed from where it left off.\n\n` +
              `**Progress:** ${safeInitialCycles}/${safeTotalAmount} cycles completed\n` +
              `**Status:** Resuming...`
          )
          .setFooter({
            text: "The action will update this message with progress.",
          });
        targetMessage = await targetChannel.send({
          embeds: [resumeEmbed],
          components: [],
        });
        // Update the action record with the new message ID
        try {
          await updateActionMessageId(actionId, targetMessage.id);
          console.log(
            `[resumeActionLoop] Updated action ${actionId} with fresh message ID: ${targetMessage.id}`
          );
        } catch (updateError) {
          console.warn(
            `[resumeActionLoop] Failed to update message ID for action ${actionId}:`,
            updateError.message
          );
        }
        console.log(
          `[resumeActionLoop] Created fresh resumption message ${targetMessage.id} for action ${actionId}`
        );
        // Try to clean up the old message if we have its ID (best effort)
        if (currentMessageId && currentMessageId !== targetMessage.id) {
          try {
            const oldMessage =
              await targetChannel.messages.fetch(currentMessageId);
            if (oldMessage) {
              const supersededEmbed = new EmbedBuilder()
                .setColor(EMBED_COLORS.GRAY)
                .setDescription(
                  `~~This action message has been superseded by a fresh message due to resumption.~~`
                );
              await oldMessage.edit({
                embeds: [supersededEmbed],
                components: [],
              });
              console.log(
                `[resumeActionLoop] Marked old message ${currentMessageId} as superseded`
              );
            }
          } catch (cleanupError) {
            console.log(
              `[resumeActionLoop] Could not clean up old message ${currentMessageId} (expected):`,
              cleanupError.message
            );
          }
        }
      } catch (sendError) {
        console.error(
          `[resumeActionLoop] Failed to send fresh resumption message for action ${actionId}:`,
          sendError
        );
        // Handle specific channel errors
        if (sendError.code === 50013) {
          console.error(
            `[resumeActionLoop] Missing permissions to send messages in channel ${channelId}`
          );
        } else if (sendError.code === 10003) {
          console.error(
            `[resumeActionLoop] Channel ${channelId} no longer exists`
          );
        } else {
          console.error(
            `[resumeActionLoop] Unknown error sending message to channel ${channelId} (code: ${sendError.code})`
          );
        }

        // IMPORTANT: Instead of losing the action, try to continue without a message
        // The action can still process in the background and save progress
        console.warn(
          `[resumeActionLoop] Attempting to continue action ${actionId} without message display...`
        );

        // Create a mock message object for the action handler
        targetMessage = {
          id: `mock-${actionId}`,
          edit: async (options) => {
            console.log(
              `[resumeActionLoop] Mock message edit for action ${actionId}:`,
              options.embeds?.[0]?.data?.description || "No description"
            );
            return targetMessage;
          },
          channel: targetChannel,
        };

        console.log(
          `[resumeActionLoop] Created mock message for action ${actionId} - action will proceed without visual updates`
        );
      }
    }

    // Message tracking no longer needed - using simple abort system

    // --- Resume the actual action ---
    console.log(
      `[resumeActionLoop] Starting resumption of ${normalizedActionType} action ${actionId} for user ${userId}`
    );

    // CRITICAL FIX: Initialize AbortController FIRST so stop buttons work immediately
    const { createAbortController } = require("./instantStopUtils");
    createAbortController(actionId);
    console.log(
      `[InstantStop] Created AbortController for resumed ${normalizedActionType} action ${actionId}`
    );

    // --- Re-add appropriate buttons for resumed actions AFTER AbortController is ready ---
    // Note: Combat actions manage their own stop button persistence, so we skip them
    // Always try to add stop button for incomplete actions, regardless of message state
    if (
      initialCompletedCycles < totalAmount &&
      targetMessage &&
      normalizedActionType !== "combat" // Skip combat - it manages its own stop button
    ) {
      console.log(
        `[resumeActionLoop] Attempting to re-add unified stop button for ${normalizedActionType} action ${actionId} (${initialCompletedCycles}/${totalAmount} cycles completed)`
      );

      try {
        // Stop button integration is managed by the running action loop; nothing to add here
        console.log(
          `[resumeActionLoop] Stop button integration handled by action loop for ${normalizedActionType} action ${actionId}`
        );
      } catch (buttonError) {
        console.error(
          `[resumeActionLoop] Error adding unified stop button for ${normalizedActionType} action ${actionId}:`,
          buttonError.message
        );

        // Fallback: try to create a separate stop button message
        try {
          const {
            createNewMessageWithStopButton,
          } = require("./unifiedStopButtons");
          await createNewMessageWithStopButton(
            targetChannel,
            actionId,
            userId,
            normalizedActionType,
            "Button creation failed - using fallback"
          );
        } catch (fallbackError) {
          console.error(
            `[resumeActionLoop] Complete failure to create stop button for ${normalizedActionType} action ${actionId}:`,
            fallbackError.message
          );
        }
      }
    } else {
      console.log(
        `[resumeActionLoop] Skipping stop button for ${normalizedActionType} action ${actionId} - either completed (${initialCompletedCycles}/${totalAmount}), no message, or combat action`
      );
    }

    // Enhanced error handling for the actual action resumption
    try {
      await handler(
        {
          user: await client.users.fetch(userId).catch(() => ({ id: userId })),
          channel: targetChannel,
          channelId: channelId,
          client: client,
          isResumption: true,
          messageToEdit: targetMessage,
          actionId: actionId,
          initialCompletedCycles: initialCompletedCycles,
          totalAmount: totalAmount,
          notifiedAboutDeletedMessage:
            persistedParams?.notifiedAboutDeletedMessage || false,
          parameters: persistedParams,
          deferReply: async () => {
            return;
          },
          editReply: async (options) => {
            try {
              if (!targetMessage) {
                console.warn(
                  `[Resumption ${actionId}] No target message available for editReply, creating new message`
                );
                const newMessage = await targetChannel.send(options);
                targetMessage = newMessage; // Update our reference
                // Update database with new message ID
                try {
                  await updateActionMessageId(actionId, newMessage.id);
                } catch (dbError) {
                  console.warn(
                    `[Resumption ${actionId}] Failed to update message ID after editReply fallback:`,
                    dbError
                  );
                }
                return newMessage;
              }
              return await targetMessage.edit(options);
            } catch (editError) {
              // Simplified error handling - if editing fails for any reason, just send a new message
              console.log(
                `[Resumption ${actionId}] Edit failed (${editError.code || "unknown"}), sending new message instead`
              );

              try {
                const newMessage = await targetChannel.send(options);
                targetMessage = newMessage; // Update our reference
                // Update database with new message ID
                try {
                  await updateActionMessageId(actionId, newMessage.id);
                } catch (dbError) {
                  console.warn(
                    `[Resumption ${actionId}] Failed to update message ID after edit fallback:`,
                    dbError
                  );
                }
                return newMessage;
              } catch (sendError) {
                console.error(
                  `[Resumption ${actionId}] Both edit and send failed:`,
                  sendError
                );
                return { id: "edit-send-failed" }; // Return a placeholder
              }
            }
          },
          followUp: async (options) => {
            console.log(`[Resumption ${actionId}] Sending followup.`);
            try {
              return await targetChannel.send(options);
            } catch (followupError) {
              console.error(
                `[Resumption ${actionId}] Error sending followup:`,
                followupError
              );
            }
          },
          fetchReply: async () => {
            if (targetMessage) return targetMessage;
            // Attempt to refetch if somehow lost? Or just return placeholder
            console.warn(
              `[Resumption ${actionId}] fetchReply called but targetMessage is missing.`
            );
            return { id: "unknown-resumption-message" };
          },
        },
        resource_key,
        totalAmount,
        false, // isAgain = false for resumption
        totalAmount, // originalAmount
        character
      );

      console.log(
        `[resumeActionLoop] Successfully resumed ${normalizedActionType} action ${actionId} for user ${userId}`
      );

      // Final safety check - ensure action is properly tracked after completion
      try {
        const { getActionById } = require("./actionPersistence");
        const finalActionState = await getActionById(actionId);
        if (finalActionState) {
          console.log(
            `[resumeActionLoop] Action ${actionId} final state - completed: ${finalActionState.completed_cycles}/${finalActionState.total_amount}`
          );
        }
      } catch (finalCheckError) {
        console.warn(
          `[resumeActionLoop] Could not perform final state check for action ${actionId}:`,
          finalCheckError.message
        );
      }
    } catch (actionError) {
      console.error(
        `[resumeActionLoop] CRITICAL ERROR during ${normalizedActionType} action execution for action ${actionId}:`,
        actionError
      );

      // Handle specific types of resumption errors
      if (
        actionError.message &&
        actionError.message.includes("No valid fields to update")
      ) {
        console.error(
          `[resumeActionLoop] Database field mapping error for action ${actionId} - this indicates a resumption state corruption`
        );
        console.error(`[resumeActionLoop] Character state:`, {
          discordId: character.discordId,
          name: character.name,
          hasStats: !!character.stats,
          hasSkills: !!character.skills,
          hasInventory: !!character.inventory,
        });
        console.error(`[resumeActionLoop] Action parameters:`, {
          actionType: normalizedActionType,
          originalActionType: actionType,
          resourceKey: resource_key,
          totalAmount,
          hasPersistedParams: !!persistedParams,
          hasPreCalculatedStats: !!persistedParams.preCalculatedStats,
        });
      } else if (
        actionError.message &&
        actionError.message.includes("Unknown Message")
      ) {
        console.error(
          `[resumeActionLoop] Discord message reference error for action ${actionId} - message may have been deleted`
        );
        console.error(
          `[resumeActionLoop] Original message ID: ${messageId}, Current target message: ${targetMessage?.id || "none"}`
        );

        // Try to recover by marking this action for message ID refresh
        try {
          if (targetChannel) {
            // Validate variables before creating recovery embed
            const safeUserId = userId || "unknown";
            const safeActionType = normalizedActionType || "unknown";
            const safeActionId = actionId || "unknown";

            // Create a recovery message to continue the action
            const recoveryEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.YELLOW)
              .setTitle(`🔄 Action Recovery`)
              .setDescription(
                `<@${safeUserId}> Your ${safeActionType} action (ID: ${safeActionId}) encountered a message error but is being recovered.\n\n` +
                  `**Progress preserved:** The action will continue from where it left off.`
              );

            const recoveryMessage = await targetChannel.send({
              embeds: [recoveryEmbed],
            });

            // Update the action record with the new recovery message ID
            await updateActionMessageId(actionId, recoveryMessage.id);
            console.log(
              `[resumeActionLoop] Created recovery message ${recoveryMessage.id} for action ${actionId}`
            );
          }
        } catch (recoveryError) {
          console.error(
            `[resumeActionLoop] Failed to create recovery message for action ${actionId}:`,
            recoveryError
          );
        }
      } else if (actionError.code === 10008) {
        console.error(
          `[resumeActionLoop] Original message was deleted for action ${actionId}`
        );
      } else if (actionError.code === 50013) {
        console.error(
          `[resumeActionLoop] Missing permissions for action ${actionId}`
        );
      } else {
        console.error(
          `[resumeActionLoop] Unexpected error type for action ${actionId}:`,
          actionError
        );
      }

      // Try to notify the user about the resumption failure
      try {
        if (targetChannel && character) {
          // Validate variables before creating error embed
          const safeUserId = userId || "unknown";
          const safeActionType = normalizedActionType || "unknown";
          const safeActionId = actionId || "unknown";
          const safeErrorMessage =
            actionError.message || "Unknown error occurred";

          const errorEmbed = new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle(
              `⚠️ ${safeActionType.charAt(0).toUpperCase() + safeActionType.slice(1)} Action Resumption Failed`
            )
            .setDescription(
              `<@${safeUserId}> Your ${safeActionType} action (ID: ${safeActionId}) could not be resumed due to an error.\n\n` +
                `**Error Details:**\n` +
                `${safeErrorMessage}\n\n` +
                `**Action Status:** Cleaned up\n` +
                `**Progress:** Any progress made before the bot restart has been saved.`
            )
            .setFooter({ text: "You can start a new action when ready." });

          await targetChannel.send({ embeds: [errorEmbed] });
          console.log(
            `[resumeActionLoop] Sent error notification to user ${userId} for failed action ${actionId}`
          );
        } else {
          console.warn(
            `[resumeActionLoop] Could not send error notification for action ${actionId} - missing channel or character data`
          );
        }
      } catch (notificationError) {
        console.error(
          `[resumeActionLoop] Failed to send error notification for action ${actionId}:`,
          notificationError
        );
      }

      // Clean up the failed action
      await completeAction(actionId);
      clearActivity(userId);

      // CRITICAL FIX: Cleanup AbortController for failed resumed actions
      const { cleanupAbortController } = require("./instantStopUtils");
      cleanupAbortController(actionId);
      //console.log(`[InstantStop] Cleaned up AbortController for failed resumed action ${actionId}`);

      // Don't throw the error - we want to continue processing other actions
      console.log(
        `[resumeActionLoop] Cleaned up failed action ${actionId} and cleared activity for user ${userId}`
      );
    }
  } catch (error) {
    // Global error handler for the entire resumption process
    console.error(
      `[resumeActionLoop] CRITICAL FAILURE during resumption process for action ${actionId}:`,
      error
    );

    console.error(`[resumeActionLoop] Error stack:`, error.stack);
    console.error(`[resumeActionLoop] Action record at time of failure:`, {
      actionId,
      userId,
      actionType: normalizedActionType,
      originalActionType: actionType,
      channelId,
      messageId,
      totalAmount,
      completedCycles: initialCompletedCycles,
      status: actionRecord.status,
    });

    // Try to clean up the corrupted action
    try {
      await completeAction(actionId);
      clearActivity(userId);

      // CRITICAL FIX: Cleanup AbortController for emergency cleanup
      const { cleanupAbortController } = require("./instantStopUtils");
      cleanupAbortController(actionId);

      console.log(
        `[resumeActionLoop] Emergency cleanup completed for action ${actionId}`
      );
    } catch (cleanupError) {
      console.error(
        `[resumeActionLoop] Emergency cleanup failed for action ${actionId}:`,
        cleanupError
      );
    }

    // Try to send emergency notification to user
    try {
      if (targetChannel && userId) {
        // Validate variables before creating emergency embed
        const safeUserId = userId || "unknown";
        const safeActionType = normalizedActionType || "unknown";
        const safeActionId = actionId || "unknown";

        const emergencyEmbed = new EmbedBuilder()
          .setColor(EMBED_COLORS.ERROR)
          .setTitle(`🚨 Action Resumption System Error`)
          .setDescription(
            `<@${safeUserId}> A critical error occurred while trying to resume your ${safeActionType} action (ID: ${safeActionId}).\n\n` +
              `**Status:** Action has been cleaned up\n` +
              `**Next Steps:** Please try starting a new action\n\n` +
              `If this error persists, please contact support.`
          )
          .setFooter({ text: "Emergency cleanup completed" });

        await targetChannel.send({ embeds: [emergencyEmbed] });
        console.log(
          `[resumeActionLoop] Sent emergency notification to user ${userId}`
        );
      }
    } catch (emergencyNotificationError) {
      console.error(
        `[resumeActionLoop] Emergency notification failed for action ${actionId}:`,
        emergencyNotificationError
      );
    }

    // Don't re-throw the error - we want the resumption system to continue with other actions
    console.log(
      `[resumeActionLoop] Resumption process completed with errors for action ${actionId}`
    );
  }
}

/**
 * Fetches all pending actions from the database and starts the resumption loop for each.
 * @param {Client} client Discord client instance.
 * @param {Map} resumedStopRequests Map tracking stop requests for resumed actions.
 * @param {Map} activeResumedMessages Map tracking the new message object for resumed actions.
 */
/**
 * Simplified resumption system - main bot NEVER handles any actions
 * Workers self-resume by checking for their own pending actions
 * @param {Client} client Discord client instance.
 * @param {Function} logSync Logging function.
 */
async function resumeAllPendingActions(client, logSync = console.log) {
  const QUIET_BOOT = process.env.QUIET_BOOT === "true";
  if (!QUIET_BOOT)
    logSync(
      "Starting improved resumption system with proper worker filtering..."
    );

  // Get all pending actions first for analysis
  const pendingActions = await getAllPendingActions();

  if (!pendingActions || pendingActions.length === 0) {
    logSync("No pending actions found.");
    return;
  }

  // Filter actions based on proper worker mapping and detect corrupted actions
  const actionsToHandle = [];
  const actionsToSkip = [];
  const corruptedActions = [];
  const orphanedActions = [];

  for (const action of pendingActions) {
    if (isCorruptedAction(action.worker_id)) {
      corruptedActions.push(action);
    } else if (!isValidWorkerId(action.worker_id)) {
      orphanedActions.push(action);
    } else if (shouldHandleAction(action.worker_id, client.user?.id)) {
      actionsToHandle.push(action);
    } else {
      actionsToSkip.push(action);
    }
  }

  const currentIdentity = process.env.WORKER_BOT_ID
    ? `Worker ${process.env.WORKER_BOT_ID}`
    : `Main Bot ${client.user?.id}`;

  logSync(
    `[${currentIdentity}] Found ${pendingActions.length} total pending actions`
  );
  logSync(
    `[${currentIdentity}] Will handle: ${actionsToHandle.length} actions`
  );
  logSync(`[${currentIdentity}] Will skip: ${actionsToSkip.length} actions`);
  logSync(
    `[${currentIdentity}] CORRUPTED (will clean up): ${corruptedActions.length} actions`
  );
  logSync(
    `[${currentIdentity}] ORPHANED (invalid worker_id, will clean up): ${orphanedActions.length} actions`
  );

  // Handle corrupted actions first - ANY process can clean these up since they're corrupted
  if (corruptedActions.length > 0) {
    logSync(
      `[${currentIdentity}] Cleaning up ${corruptedActions.length} corrupted actions with missing worker_id`
    );

    for (const corruptedAction of corruptedActions) {
      try {
        logSync(
          `[${currentIdentity}] Processing corrupted action ${corruptedAction.id} (no worker_id)`
        );

        // Use resumeActionLoop which now handles corrupted action cleanup and user notification
        await resumeActionLoop(client, corruptedAction);

        logSync(
          `[${currentIdentity}] Cleaned up corrupted action ${corruptedAction.id}`
        );
      } catch (cleanupError) {
        logSync(
          `[${currentIdentity}] Failed to clean up corrupted action ${corruptedAction.id}: ${cleanupError.message}`
        );
      }
    }
  }

  // Handle orphaned actions - actions with invalid worker_ids (not in config)
  if (orphanedActions.length > 0) {
    logSync(
      `[${currentIdentity}] Cleaning up ${orphanedActions.length} orphaned actions with invalid worker_id`
    );

    for (const orphanedAction of orphanedActions) {
      try {
        logSync(
          `[${currentIdentity}] Processing orphaned action ${orphanedAction.id} (invalid worker_id: ${orphanedAction.worker_id})`
        );

        // Notify user about orphaned action and clean it up
        try {
          const targetChannel = await client.channels.fetch(
            orphanedAction.channel_id
          );
          if (targetChannel) {
            const orphanedEmbed = new EmbedBuilder()
              .setColor(EMBED_COLORS.ERROR)
              .setTitle("🚨 Action Configuration Error")
              .setDescription(
                `<@${orphanedAction.user_id}> Your ${orphanedAction.action_type || "unknown"} action was assigned to a worker that no longer exists in the system configuration.\n\n` +
                  `**Action ID:** ${orphanedAction.id}\n` +
                  `**Invalid Worker ID:** ${orphanedAction.worker_id}\n` +
                  `**Status:** Action has been terminated and cleaned up\n` +
                  `**Cause:** System configuration change or worker removal\n\n` +
                  `⚠️ **Please contact a developer if this continues to happen.**`
              )
              .setFooter({ text: "You can start a new action when ready." });

            await targetChannel.send({ embeds: [orphanedEmbed] });
            logSync(
              `[${currentIdentity}] Notified user ${orphanedAction.user_id} about orphaned action ${orphanedAction.id}`
            );
          }
        } catch (notificationError) {
          logSync(
            `[${currentIdentity}] Failed to notify user about orphaned action ${orphanedAction.id}: ${notificationError.message}`
          );
        }

        // Clean up the orphaned action
        await completeAction(orphanedAction.id);
        await clearActivity(orphanedAction.user_id);
        logSync(
          `[${currentIdentity}] Cleaned up orphaned action ${orphanedAction.id}`
        );
      } catch (cleanupError) {
        logSync(
          `[${currentIdentity}] Failed to clean up orphaned action ${orphanedAction.id}: ${cleanupError.message}`
        );
      }
    }
  }

  // Log which actions are being skipped for debugging (exclude corrupted ones as they're handled separately)
  if (actionsToSkip.length > 0) {
    const skipsByWorker = {};
    for (const action of actionsToSkip) {
      const wid = action.worker_id || "NO_WORKER_ID";
      if (!skipsByWorker[wid]) skipsByWorker[wid] = [];
      skipsByWorker[wid].push(action.id);
    }

    for (const [workerId, actionIds] of Object.entries(skipsByWorker)) {
      logSync(
        `[${currentIdentity}] Skipping ${actionIds.length} actions for ${workerId}: [${actionIds.join(", ")}]`
      );
    }
  }

  // Process actions that should be handled by this process
  if (actionsToHandle.length > 0) {
    logSync(
      `[${currentIdentity}] Processing ${actionsToHandle.length} assigned actions`
    );

    for (const action of actionsToHandle) {
      try {
        logSync(
          `[${currentIdentity}] Resuming action ${action.id} (${action.action_type})`
        );

        await resumeActionLoop(client, action);

        logSync(
          `[${currentIdentity}] Successfully resumed action ${action.id}`
        );
      } catch (resumeError) {
        logSync(
          `[${currentIdentity}] Failed to resume action ${action.id}: ${resumeError.message}`
        );
      }
    }
  } else {
    logSync(`[${currentIdentity}] No actions assigned to this process`);
  }

  logSync(`[${currentIdentity}] Resumption process completed`);
}

// Function to handle stop requests for resumed actions
async function handleResumedStop(
  interaction,
  actionId /* Remove maps from parameters */
) {
  // Use simple abort system instead of maps
  const { abortAction } = require("./instantStopUtils");
  abortAction(actionId);

  // Action will stop immediately with abort signal - no message editing needed

  // Acknowledge the button press
  try {
    await interaction.deferUpdate();
  } catch (updateError) {
    // Handle common Discord API errors silently
    if (updateError.code === 10062) {
      console.log(
        `[Resumption Stop] Interaction expired for action ${actionId}, stop request noted`
      );
    } else if (updateError.code === 500 || updateError.code === 50027) {
      console.log(
        `[Resumption Stop] Invalid webhook token for button interaction (action ${actionId}), stop request noted`
      );
    } else if (updateError.code === 40060) {
      console.log(
        `[Resumption Stop] Interaction already acknowledged for action ${actionId}, stop request noted`
      );
    } else {
      console.warn(
        `[Resumption Stop] Failed to defer update for stop button interaction (action ${actionId}):`,
        updateError.message
      );
    }
  }
  // The loop inside handleMulti (when called via resumeActionLoop) will check resumedStopRequests
  // and handle the actual termination and final message edit.
}

module.exports = {
  resumeAllPendingActions,
  handleResumedStop,
  resumeActionLoop,
};
