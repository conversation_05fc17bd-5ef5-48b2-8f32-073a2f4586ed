const { PermissionFlagsBits } = require("discord.js");
const config = require("../config.json");
const { dbAll } = require("./dbUtils");
const { getPlayerData } = require("./playerDataManager");

/**
 * Update setup channel permissions to hide it from players who already have channels
 * @param {Client} client - Discord client
 */
async function updateSetupChannelPermissions(client) {
  try {
    const setupChannelId = config.channelCreation?.setupChannelId;
    if (!setupChannelId) {
      return;
    }

    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      console.warn("[SetupChannelManager] Guild not found");
      return;
    }

    const setupChannel = await guild.channels
      .fetch(setupChannelId)
      .catch(() => null);
    if (!setupChannel) {
      console.warn("[SetupChannelManager] Setup channel not found");
      return;
    }

    // get all players who have personal channels
    const playersWithChannels = await dbAll(
      "SELECT discord_id, rank FROM players WHERE personal_channel_id IS NOT NULL"
    );

    // simple approach: for each player with a channel, check if they're already hidden from setup
    let newDenyCount = 0;
    let adminSkipCount = 0;
    let adminUnhiddenCount = 0;

    for (const player of playersWithChannels) {
      const { discord_id, rank } = player;

      // check if this is an admin or owner
      if (
        rank &&
        (rank.toUpperCase() === "ADMIN" || rank.toUpperCase() === "OWNER")
      ) {
        adminSkipCount++;

        // check if this admin/owner is currently hidden and unhide them
        const existingOverwrite =
          setupChannel.permissionOverwrites.cache.get(discord_id);
        const isCurrentlyHidden =
          existingOverwrite &&
          existingOverwrite.deny.has(PermissionFlagsBits.ViewChannel);

        if (isCurrentlyHidden) {
          try {
            // remove the deny permission to unhide the setup channel
            await setupChannel.permissionOverwrites.delete(discord_id);
            adminUnhiddenCount++;
          } catch (error) {
            console.warn(
              `[SetupChannelManager] Failed to unhide setup channel for admin/owner ${discord_id}: ${error.message}`
            );
          }
        }

        continue;
      }

      // check if this user already has a deny permission for ViewChannel
      const existingOverwrite =
        setupChannel.permissionOverwrites.cache.get(discord_id);
      const alreadyHidden =
        existingOverwrite &&
        existingOverwrite.deny.has(PermissionFlagsBits.ViewChannel);

      if (!alreadyHidden) {
        try {
          // validate user exists before trying to edit permissions
          await client.users.fetch(discord_id);

          // hide the setup channel from this user
          await setupChannel.permissionOverwrites.edit(discord_id, {
            ViewChannel: false,
          });
          newDenyCount++;
        } catch (error) {
          // handle specific "unknown user" errors more gracefully
          if (
            error.code === 10013 ||
            error.message.includes("Unknown User") ||
            error.message.includes("Unknown Overwrite")
          ) {
            console.log(
              `[SetupChannelManager] ⚠️ User ${discord_id} no longer exists, skipping permission update`
            );
          } else {
            console.warn(
              `[SetupChannelManager] Failed to hide setup channel from user ${discord_id}: ${error.message}`
            );
          }
        }
      }
    }

    if (newDenyCount > 0 || adminUnhiddenCount > 0) {
      console.log(
        `[SetupChannelManager] ✅ ${playersWithChannels.length} players checked, ${newDenyCount} newly hidden, ${adminUnhiddenCount} admins/owners unhidden, ${adminSkipCount} admins/owners kept visible`
      );
    } else {
      const QUIET_BOOT = process.env.QUIET_BOOT === "true";
      if (!QUIET_BOOT)
        console.log(
          `[SetupChannelManager] ✅ ${playersWithChannels.length} players checked, all permissions already correct, ${adminSkipCount} admins/owners kept visible`
        );
    }
  } catch (error) {
    console.error(
      "[SetupChannelManager] Error updating setup channel permissions:",
      error
    );
  }
}

/**
 * Hide setup channel from a specific user who just got a personal channel
 * @param {Client} client - Discord client
 * @param {string} userId - Discord user ID
 */
async function hideSetupChannelFromUser(client, userId) {
  try {
    const setupChannelId = config.channelCreation?.setupChannelId;
    if (!setupChannelId) {
      return;
    }

    const guild = client.guilds.cache.get(config.guildId);
    if (!guild) {
      return;
    }

    const setupChannel = await guild.channels
      .fetch(setupChannelId)
      .catch(() => null);
    if (!setupChannel) {
      return;
    }

    // check if this user is an admin or owner - don't hide setup channel from them
    const playerData = await getPlayerData(userId);
    if (
      playerData &&
      playerData.rank &&
      (playerData.rank.toUpperCase() === "ADMIN" ||
        playerData.rank.toUpperCase() === "OWNER")
    ) {
      console.log(
        `[SetupChannelManager] ✅ Skipping setup channel hide for ${playerData.rank} user ${userId}`
      );
      return;
    }

    // add a deny permission for this specific user
    await setupChannel.permissionOverwrites.edit(userId, {
      ViewChannel: false,
    });

    console.log(
      `[SetupChannelManager] ✅ Hidden setup channel from user ${userId}`
    );
  } catch (error) {
    console.error(
      `[SetupChannelManager] Error hiding setup channel from user ${userId}:`,
      error
    );
  }
}

module.exports = {
  updateSetupChannelPermissions,
  hideSetupChannelFromUser,
};
