const { dbRun, dbAll } = require("../utils/dbUtils");

async function up() {
  console.log("[Migration 046] Creating wardrobe_presets table...");

  try {
    // Check if table already exists
    const tables = await dbAll(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='wardrobe_presets'",
    );

    if (tables.length > 0) {
      console.log(
        "[Migration 046] wardrobe_presets table already exists, skipping...",
      );
      return;
    }

    console.log("[Migration 046] Creating wardrobe_presets table...");
    await dbRun(`
      CREATE TABLE wardrobe_presets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        slot_number INTEGER NOT NULL,
        helmet_item_id TEXT,
        chestplate_item_id TEXT,
        leggings_item_id TEXT,
        boots_item_id TEXT,
        weapon_item_id TEXT,
        axe_item_id TEXT,
        pickaxe_item_id TEXT,
        shovel_item_id TEXT,
        hoe_item_id TEXT,
        fishing_rod_item_id TEXT,
        necklace_item_id TEXT,
        cloak_item_id TEXT,
        belt_item_id TEXT,
        gloves_item_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, slot_number)
      )
    `);

    console.log("[Migration 046] Successfully created wardrobe_presets table");
  } catch (error) {
    console.error(
      "[Migration 046] Error creating wardrobe_presets table:",
      error,
    );
    throw error;
  }
}

async function down() {
  console.log("[Migration 046] Dropping wardrobe_presets table...");

  try {
    await dbRun("DROP TABLE IF EXISTS wardrobe_presets");
    console.log("[Migration 046] Successfully dropped wardrobe_presets table");
  } catch (error) {
    console.error(
      "[Migration 046] Error dropping wardrobe_presets table:",
      error,
    );
    throw error;
  }
}

module.exports = { up, down };
