const { STATS } = require("../gameConfig");
const configManager = require("./configManager");
const { getLevelFromExp } = require("./expFunctions");
const { skillRewards } = require("../data/skillRewards");
const { calculateItemStats } = require("./itemStatCalculator");

/**
 * Calculates the total value of a stat by summing all its components
 * @param {Object} statObject - The stat object containing various components
 * @returns {number} The total calculated stat value
 */
const calculateTotalStat = (statObject) => {
  if (!statObject) return 0;
  return (
    (statObject.base || 0) +
    (statObject.fromLevels || 0) +
    (statObject.fromSlayers || 0) +
    (statObject.fromEquipment || 0) +
    (statObject.fromPet || 0) +
    (statObject.fromSetBonus || 0) +
    (statObject.fromAccessories || 0) +
    (statObject.fromMagicalPower || 0) +
    (statObject.fromPetScore || 0)
  );
};

/**
 * Calculates stat bonuses from skill levels using the data-driven configuration.
 * Returns an object with stat keys and their total bonus from skills.
 */
function calculateSkillStats(character) {
  const skillStats = {};
  if (!character || !character.skills) return skillStats;

  for (const [skillName, skillData] of Object.entries(character.skills)) {
    if (!skillRewards[skillName]?.rewards) continue;

    const skillLevel = getLevelFromExp(skillData.exp || 0).level;

    for (const rewardDef of skillRewards[skillName].rewards) {
      const applicableLevels = rewardDef.levels.filter(
        (l) => l <= skillLevel
      ).length;
      if (applicableLevels > 0) {
        const totalBonus = applicableLevels * rewardDef.value;
        skillStats[rewardDef.stat] =
          (skillStats[rewardDef.stat] || 0) + totalBonus;
      }
    }
  }

  return skillStats;
}

const calculateAllStats = (character) => {
  const totalStats = {};
  for (const [stat, values] of Object.entries(character.stats)) {
    let statValue = calculateTotalStat(values);

    // Cap farming sweep at 1 when not in garden
    if (stat === "FARMING_SWEEP" && character.current_region !== "garden") {
      statValue = 1;
    }

    totalStats[stat] = statValue;
  }

  // Add potion effects as temporary bonuses
  const potionUtils = require("./potionEffects");
  const godActive = potionUtils.isGodPotionActive(character);
  if (godActive) {
    const { computeGodPotionAggregatedEffects } = potionUtils;
    const aggregated = computeGodPotionAggregatedEffects(
      require("./configManager")
    );
    Object.entries(aggregated.effects).forEach(([stat, value]) => {
      totalStats[stat] = (totalStats[stat] || 0) + value;
    });
  } else if (character.activeEffects) {
    // fallback to individual effects when God Potion not active
    const currentTime = Date.now();
    Object.values(character.activeEffects).forEach((effect) => {
      if (effect.expiresAt > currentTime && effect.effects) {
        Object.entries(effect.effects).forEach(([stat, value]) => {
          totalStats[stat] = (totalStats[stat] || 0) + value;
        });
      }
    });
  }

  // Add booster cookie stat bonuses
  const {
    isBoosterCookieActive,
    getBoosterCookieStatBonuses,
  } = require("./boosterCookieManager");
  if (isBoosterCookieActive(character)) {
    const boosterBonuses = getBoosterCookieStatBonuses();
    Object.entries(boosterBonuses).forEach(([stat, value]) => {
      totalStats[stat] = (totalStats[stat] || 0) + value;
    });
  }

  // Apply vitality doubler effect from equipped items
  if (totalStats.VITALITY) {
    const { checkForVitalityDoubler } = require("./healingUtils");
    const hasVitalityDoubler = checkForVitalityDoubler(character);
    if (hasVitalityDoubler) {
      totalStats.VITALITY *= 2;
    }
  }

  // ADDITIVE_DAMAGE_PERCENTAGES is now handled by the damage calculation context system
  // No need to calculate it as a stat anymore

  const allItems = configManager.getAllItems();
  const hasWeaponEquipped = character.inventory?.equipment?.some((eq) => {
    if (!eq.isEquipped) return false;
    const itemDef = allItems[eq.itemKey];
    return itemDef?.type === "WEAPON" || itemDef?.type?.name === "WEAPON";
  });

  if (!hasWeaponEquipped) {
    totalStats.DAMAGE = Math.max(totalStats.DAMAGE, STATS.DAMAGE.base);
  }

  // Calculate display damage (base damage calculation for UI display)
  const damageFromBase = character.stats.DAMAGE?.base || 0;
  const damageFromEquipment = character.stats.DAMAGE?.fromEquipment || 0;
  const otherDamage =
    (character.stats.DAMAGE?.fromPet || 0) +
    (character.stats.DAMAGE?.fromSetBonus || 0);

  totalStats.DAMAGE = damageFromBase + damageFromEquipment + otherDamage;
  totalStats.WEAPON_DAMAGE = damageFromEquipment;

  // Store derived stats for compatibility
  if (!character.derivedStats) character.derivedStats = {};
  character.derivedStats.baseWeaponDamage = damageFromEquipment;
  character.derivedStats.enhancedWeaponDamage = damageFromEquipment; // No longer enhanced here
  character.derivedStats.enhancementBonus = 0; // Enchantments handled in damage context now

  return totalStats;
};

// Calculate SUM of BASE stats from an array of currently equipped items
const calculateEquipmentStats = (equipmentArray, character = null) => {
  const equipmentStats = {}; // e.g., { DAMAGE: 15, STRENGTH: 5, ... }

  equipmentArray.forEach((eq) => {
    if (eq.isEquipped) {
      const itemDetails = configManager.getAllItems()[eq.itemKey];

      // use the comprehensive calculateItemStats function that handles everything
      const itemStatsResult = calculateItemStats(eq, itemDetails, character);

      // add all stats from this item to the equipment totals
      for (const [stat, value] of Object.entries(itemStatsResult.totalStats)) {
        // if it's a weapon, do not add its DAMAGE stat to the general equipmentStats.DAMAGE
        // that will be handled separately as weaponBaseDamage in the combat engine.
        // other stats (STRENGTH, CRIT_DAMAGE, etc.) from the weapon are fine.
        const isWeapon =
          itemDetails?.type === "WEAPON" ||
          (itemDetails?.type && itemDetails.type.name === "WEAPON");
        if (isWeapon && stat === "DAMAGE") {
          // skip adding weapon's own base DAMAGE to the aggregate equipmentStats.DAMAGE
          continue;
        }
        equipmentStats[stat] = (equipmentStats[stat] || 0) + value;
      }
    }
  });

  return equipmentStats;
};

// Calculate stats from the currently active pet
const calculatePetStats = (character) => {
  const petStats = {}; // e.g., { STRENGTH: 10, INTELLIGENCE: 50 }
  if (
    !character?.active_pet_id ||
    !character.pets ||
    character.pets.length === 0
  ) {
    return petStats; // No active pet or no pets array
  }

  const activePet = character.pets.find(
    (p) => p.id === character.active_pet_id
  );
  if (!activePet) {
    return petStats; // Active pet ID set, but pet not found in array
  }

  const petDefinition = configManager.getAllItems()[activePet.petKey];
  if (
    !petDefinition ||
    !petDefinition.rarities ||
    !petDefinition.rarities[activePet.rarity]
  ) {
    console.warn(
      `[calculatePetStats] Pet definition or rarity info missing for petKey: ${activePet.petKey}, rarity: ${activePet.rarity}`
    );
    return petStats; // Missing definition or info for this rarity
  }

  const rarityData = petDefinition.rarities[activePet.rarity];

  // Check if the new 'stats' structure exists for this rarity
  if (!rarityData.stats || typeof rarityData.stats !== "object") {
    console.warn(
      `[calculatePetStats] Missing 'stats' object in rarity data for ${activePet.petKey}, rarity: ${activePet.rarity}`
    );
    return petStats; // No stats defined for this rarity
  }

  // Iterate through the stats defined for this rarity
  for (const [statKey, statFormula] of Object.entries(rarityData.stats)) {
    // Validate the formula structure
    if (typeof statFormula !== "object" || statFormula === null) {
      console.warn(
        `[calculatePetStats] Invalid stat formula for ${statKey} in ${activePet.petKey}, rarity: ${activePet.rarity}`
      );
      continue; // Skip this stat
    }

    // Extract formula components, providing defaults
    const base = statFormula.base || 0;
    const perLevel = statFormula.perLevel || 0;
    const cap = statFormula.cap === undefined ? 100 : statFormula.cap; // Default cap or use defined cap
    const petLevel = require("./petUtils").getPetLevel(activePet);

    // Calculate the effective level based on the cap
    const effectiveLevel = Math.min(petLevel, cap);

    // Calculate the bonus for this stat
    const calculatedBonus = base + perLevel * effectiveLevel;

    // Add the calculated bonus to the results
    if (calculatedBonus !== 0) {
      // Only add if there's a non-zero bonus
      petStats[statKey] = (petStats[statKey] || 0) + calculatedBonus;
    }
  }

  return petStats;
};

// DRY: Helper to sum all bonus sources for a stat (excluding base)
function calculateBonusStat(statObject) {
  if (!statObject) return 0;
  return (
    (statObject.fromLevels || 0) +
    (statObject.fromEquipment || 0) +
    (statObject.fromPet || 0) +
    (statObject.fromSetBonus || 0) +
    (statObject.fromAccessories || 0)
  );
}

module.exports = {
  calculateTotalStat,
  calculateAllStats,
  calculateEquipmentStats,
  calculatePetStats,
  calculateSkillStats,
  calculateBonusStat,
  getLevelFromExp,
};
