// Defines the Non-Player Characters (NPCs) in the game.
const { getTopPlayerForSkill } = require("../utils/skillLeaderboardUtils");

const NPCS = {
  // BANKERS
  HUB_BANKER: {
    key: "HUB_BANKER",
    name: "<PERSON><PERSON> Banker",
    emoji: "<:npc_hub_banker:1368683797498957944>",
    type: "BANKE<PERSON>",
    greeting: "Welcome to the Bank of The Hub.",
    foundInRegions: ["the_hub"],
  },
  BANKER_BONSAI: {
    key: "BANKER_BONSAI",
    name: "<PERSON><PERSON>",
    emoji: "<:npc_banker_bonsai:1388827502939476008>",
    type: "BANKER",
    greeting: "Welcome to the Bank of Galatea.",
    foundInRegions: ["galatea"],
  },

  // MERCHANTS
  BEA: {
    key: "BEA",
    name: "<PERSON><PERSON>",
    emoji: "<:npc_bea:1368683794197909595>",
    type: "MERCHANT",
    greeting: "Hello there! Looking for some basic farming supplies?",
    foundInRegions: ["the_hub"],
    shopInventory: [{ itemKey: "BEE_PET", price: 5000 }],
  },
  FISH_MERCHANT: {
    key: "FISH_MERCHANT",
    name: "Fish Merchant",
    emoji: "<:npc_fish_merchant:1368683796047593523>",
    type: "MERCHANT",
    greeting: "Fresh fish! Caught fresh by our local fisherman {TOP_FISHING}.",
    foundInRegions: ["the_hub"],
    shopInventory: [
      { itemKey: "FISHING_ROD", price: 100 },
      { itemKey: "RAW_FISH", price: 20 },
      { itemKey: "RAW_SALMON", price: 30 },
      { itemKey: "PUFFERFISH", price: 40 },
      { itemKey: "CLOWNFISH", price: 100 },
      { itemKey: "ANGLER_HELMET", price: 100 },
      { itemKey: "ANGLER_CHESTPLATE", price: 100 },
      { itemKey: "ANGLER_LEGGINGS", price: 100 },
      { itemKey: "ANGLER_BOOTS", price: 100 },
      { itemKey: "ANGLER_NECKLACE", price: 100 },
      { itemKey: "ANGLER_CLOAK", price: 100 },
      { itemKey: "ANGLER_BRACELET", price: 100 },
      { itemKey: "ANGLER_BELT", price: 100 },
    ],
  },
  ROSETTA: {
    key: "ROSETTA",
    name: "Rosetta",
    emoji: "<:npc_rosetta:1370380213489504318>",
    type: "MERCHANT",
    greeting:
      "Hello! I sell gear for aspiring adventurers. Looking for a Squire Sword?",
    foundInRegions: ["the_hub"],
    shopInventory: [
      {
        itemKey: "SQUIRE_SWORD",
        price: 5000,
        emoji: "<:squire_sword:1370379663242952748>",
      },
      { itemKey: "MERCENARY_AXE", price: 30000 },
    ],
  },
  JAKE: {
    key: "JAKE",
    name: "Jake",
    emoji: "<:npc_jake:1376187325825945680>",
    type: "MERCHANT",
    greeting:
      "Hello! I sell special axes that help you get more drops from farm animals. Bring me an animal and I'll sell you the matching axe!",
    foundInRegions: ["the_farming_islands"],
    shopInventory: [
      { itemKey: "SHEEP_AXE", price: 5000 },
      { itemKey: "CHICKEN_AXE", price: 5000 },
      { itemKey: "COW_AXE", price: 5000 },
      { itemKey: "PIG_AXE", price: 5000 },
      { itemKey: "RABBIT_AXE", price: 5000 },
    ],
  },
  MINE_MERCHANT: {
    key: "MINE_MERCHANT",
    name: "Mine Merchant",
    emoji: "<:npc_mine_merchant:1376249775363588137>",
    type: "MERCHANT",
    greeting:
      "Great ores! My buddy {TOP_MINING} supplies all the ores you see here.",
    foundInRegions: ["the_hub"],
    shopInventory: [
      { itemKey: "COAL", price: 4 },
      { itemKey: "IRON_INGOT", price: 5 },
      { itemKey: "GOLD_INGOT", price: 6 },
      { itemKey: "GRAVEL", price: 6 },
      { itemKey: "COBBLESTONE", price: 3 },
    ],
  },
  FARM_MERCHANT: {
    key: "FARM_MERCHANT",
    name: "Farm Merchant",
    emoji: "<:npc_farm_merchant:1376249676520751144>",
    type: "MERCHANT",
    greeting: "Fresh crops! Fresh off {TOP_FARMING}'s Garden.",
    foundInRegions: ["the_hub"],
    shopInventory: [
      { itemKey: "WHEAT", price: 10 },
      { itemKey: "CARROT", price: 10 },
      { itemKey: "POTATO", price: 10 },
      { itemKey: "MELON", price: 4 },
      { itemKey: "SUGAR_CANE", price: 10 },
      { itemKey: "CACTUS", price: 4 },
      { itemKey: "PUMPKIN", price: 25 },
      { itemKey: "RED_MUSHROOM", price: 25 },
      { itemKey: "BROWN_MUSHROOM", price: 25 },
      { itemKey: "COCOA_BEANS", price: 5 },
      { itemKey: "ROOKIE_HOE", price: 10 },
      { itemKey: "SAND", price: 4 },
    ],
  },
  LUMBER_MERCHANT: {
    key: "LUMBER_MERCHANT",
    name: "Lumber Merchant",
    emoji: "<:npc_lumber_merchant:1376249703800242369>",
    type: "MERCHANT",
    greeting: "Quality wood for all your needs, recommended by {TOP_FORAGING}!",
    foundInRegions: ["the_hub"],
    shopInventory: [
      { itemKey: "OAK_LOG", price: 5 },
      { itemKey: "SPRUCE_LOG", price: 5 },
      { itemKey: "JUNGLE_LOG", price: 5 },
    ],
  },
  PEARL_DEALER: {
    key: "PEARL_DEALER",
    name: "Pearl Dealer",
    emoji: "<:npc_pearl_dealer:1377742141308276846>",
    type: "MERCHANT",
    greeting: "You have reached The End, though this is only the beginning.",
    foundInRegions: ["the_end"],
    shopInventory: [
      { itemKey: "END_STONE", price: 10 },
      { itemKey: "OBSIDIAN", price: 50 },
      { itemKey: "STONK", price: 499999 },
    ],
  },
  ADVENTURER: {
    key: "ADVENTURER",
    name: "Adventurer",
    emoji: "<:npc_adventurer:1384162058194387034>",
    type: "MERCHANT",
    greeting: "Welcome, fellow adventurer!",
    foundInRegions: ["the_hub"],
    shopInventory: [
      { itemKey: "ZOMBIE_TALISMAN", price: 500 },
      { itemKey: "SKELETON_TALISMAN", price: 500 },
      { itemKey: "VILLAGE_AFFINITY_TALISMAN", price: 2500 },
      { itemKey: "INTIMIDATION_TALISMAN", price: 10000 },
      { itemKey: "SCAVENGER_TALISMAN", price: 10000 },
      { itemKey: "ROTTEN_FLESH", price: 8 },
      { itemKey: "BONE", price: 8 },
      { itemKey: "STRING", price: 10 },
      { itemKey: "SLIMEBALL", price: 14 },
      { itemKey: "GUNPOWDER", price: 10 },
    ],
  },
  AMAURY: {
    key: "AMAURY",
    name: "Amaury",
    emoji: "<:npc_amaury:1388796541023682701>",
    type: "MERCHANT",
    greeting:
      "Welcome to my canopy gear shop! These items will help you forage more efficiently.",
    foundInRegions: ["galatea"],
    shopInventory: [
      { itemKey: "CANOPY_MASK", price: 120000 },
      { itemKey: "CANOPY_SHIRT", price: 140000 },
      { itemKey: "CANOPY_PANTS", price: 130000 },
      { itemKey: "CANOPY_SANDALS", price: 110000 },
      { itemKey: "SERIOUSLY_DAMAGED_AXE", price: 25000 },
      { itemKey: "DECENT_AXE", price: 1250000 },
    ],
  },
  WEAPONSMITH: {
    key: "WEAPONSMITH",
    name: "Weaponsmith",
    emoji: "<:npc_weaponsmith:1390122701506609162>",
    type: "MERCHANT",
    greeting:
      "You'll need some strong weapons to survive out there.\nLucky for you, I've got some!",
    foundInRegions: ["the_hub"],
    shopInventory: [
      { itemKey: "SPIDER_SWORD", price: 100 },
      { itemKey: "DIAMOND_SWORD", price: 60 },
      { itemKey: "UNDEAD_SWORD", price: 100 },
    ],
  },
  SKYMART_MERCHANT: {
    key: "SKYMART_MERCHANT",
    name: "SkyMart Merchant",
    emoji: "<:diamond_hoe:1373220303458795642>",
    type: "MERCHANT",
    greeting:
      "Welcome to SkyMart! Your one-stop shop for premium garden supplies and tools!",
    foundInRegions: [],
    currency: "copper",
    shopInventory: [
      { itemKey: "BASIC_GARDENING_HOE", price: 5 },
      { itemKey: "ADVANCED_GARDENING_HOE", price: 25 },
      { itemKey: "BASIC_GARDENING_AXE", price: 5 },
      { itemKey: "ADVANCED_GARDENING_AXE", price: 25 },
      { itemKey: "SUNDER_1_BOOK", price: 10 },
    ],
  },
  MELANCHOLIC_VIKING: {
    key: "MELANCHOLIC_VIKING",
    name: "Melancholic Viking",
    emoji: "<:npc_melancholic_viking:1399346174585995366>",
    type: "MERCHANT",
    greeting:
      "Greetings, warrior. I have forged a special sword... \nperhaps it will find purpose in your hands.",
    foundInRegions: ["the_park"],
    shopInventory: [{ itemKey: "RAIDER_SWORD", price: 130000 }],
  },

  // SPECIAL NPCS
  BAKER: {
    key: "BAKER",
    name: "Baker",
    emoji: "<:npc_baker:1371675630839533638>",
    type: "EVENT_MERCHANT",
    greeting: "Happy Almost New Year! Would you like a celebratory cake?",
    foundInRegions: ["the_hub"],
    appearance_conditions: {
      isBakerSpawnWindow: true,
    },
    dialogue: {
      default: [
        "Happy New Year! Did you come to collect your New Year Cake from Year {year}?",
      ],
      already_claimed: [
        "You've already collected your cake for Year {year}! Come back next year!",
      ],
      not_spawn_window: [
        "The aroma of baking is in the air, but I'm not quite ready. Come back a bit closer to the New Year!",
      ],
    },
    shopInventory: [
      {
        itemKey: "NEW_YEAR_CAKE_BASE",
        price: 0,
        unique: true,
        emoji: "<:new_year_cake:1372914056641511484>",
      },
    ],
  },
  KAT: {
    key: "KAT",
    name: "Kat",
    emoji: "<:npc_kat:1375244764575826011>",
    type: "PET_SITTER",
    greeting:
      "Hello! I can upgrade your pets to a higher rarity. Would you like me to help?",
    foundInRegions: ["the_hub"],
    dialogue: {
      default: ["Hi there! I'm Kat, the Pet Sitter."],
      no_pets: ["Oh! You don't seem to have any pets that I can upgrade."],
      already_upgrading: ["I'm already taking care of one of your pets."],
      pet_ready: ["Great news! I've finished upgrading your pet!"],
      pet_not_ready: ["I'm still working on your pet - Come back later!"],
      insufficient_coins: ["You don't have enough coins for this upgrade."],
    },
  },
  MAXWELL: {
    key: "MAXWELL",
    name: "Maxwell",
    emoji: "<:npc_maxwell:138595251**********>",
    type: "MAGICAL_POWER_MASTER",
    greeting:
      "Accessories are magical pieces of gear. To truly harness their power, collect as many as possible and store them in your Accessory Bag!",
    foundInRegions: ["the_hub"],
    dialogue: {
      default: [
        "On top of their existing abilities, each accessory makes your Accessory Bag more powerful!",
        "Accessories add some MAGICAL POWER to the bag depending on their rarity.",
        "The more Magical Power, the more stats like Health or Intelligence you get from your Accessory Bag.",
        "Even better, YOU choose what stats you get!",
      ],
    },
  },
  SHADY_AKIN: {
    key: "SHADY_AKIN",
    name: "Shady Akin",
    emoji: "<:developer_akinsoft:1369169613442650204>",
    type: "IDENTITY_CHANGER",
    greeting: "*Hey, I can fabricate you a whole new **identity**...*",
    foundInRegions: ["spiders_den"],
    dialogue: {
      default: [
        "*Hey, I can fabricate you a whole new **identity**...*",
        "**Only** <:purse_coins:1367849116033482772> **50,000 Coins**!",
        "Interested?",
      ],
      success: [
        "*Pleasure doing business...*",
        "<:purse_coins:1367849116033482772> `-50,000 Coins`",
      ],
      cooldown: [
        "*Hey, get outta here before someone*",
        "*sees us together...*",
      ],
    },
  },
  MADDOX: {
    key: "MADDOX",
    name: "Maddox",
    emoji: "<:npc_maddox:1389642069861208201>",
    type: "SLAYER_NPC",
    greeting: "Hi, I'm Maddox :)",
    foundInRegions: ["the_hub"],
  },
  BLACKSMITH: {
    key: "BLACKSMITH",
    name: "Blacksmith",
    emoji: "<:npc_blacksmith:1378078835752439879>",
    type: "REFORGE_MASTER",
    greeting:
      "Welcome to my forge! I can reforge your weapons, armor, and tools to enhance their power. Each reforge will cost coins based on the item's rarity.",
    foundInRegions: ["the_hub"],
    dialogue: {
      default: [
        "I can reforge your equipment to give it new properties. What would you like me to work on?",
      ],
      about_reforging: [
        "Reforging applies magical enhancements to your equipment, giving them stat bonuses based on their rarity. The process costs coins, but the results are worth it!",
      ],
      reforge_types: [
        "I can apply different reforges depending on your equipment type:\n\n**Weapons**: Fair, Epic, Fast, Gentle, Heroic, Legendary, Odd, Rich, Sharp, Spicy\n**Armor**: Clean, Fierce, Heavy, Light, Mythic, Pure, Smart, Titanic, Wise\n**Pickaxes**: Unyielding, Excellent, Fortunate, Sturdy\n**Axes**: Great, Lush, Rugged, Double-Bit\n**Hoes**: Green Thumb, Robust",
      ],
    },
  },
  ELIZABETH: {
    key: "ELIZABETH",
    name: "Elizabeth",
    emoji: "<:npc_elizabeth:1399296177601773609>",
    type: "COMMUNITY_SHOP",
    greeting:
      "Welcome to the Community Shop, here you can purchase gems and use your Bits!",
    foundInRegions: ["the_hub"],
    currency: "gems",
    dialogue: {
      default: [
        "Welcome to the Community Shop! Here you can purchase premium items with Gems.",
        "Gems can be purchased with real money to support the server and unlock exclusive content!",
      ],
      buy_gems: [
        "Select the gem package you'd like to purchase:",
        "",
        "💎 **Starter Pack** - 675 Gems - $4.99",
        "💎 **Value Pack** - 1,400 Gems - $9.99",
        "💎 **Premium Pack** - 3,600 Gems - $24.99",
        "💎 **Ultimate Pack** - 7,300 Gems - $49.99",
        "💎 **Mega Pack** - 16,400 Gems - $99.99",
        "",
        "All payments are secure and processed through Stripe.",
      ],
    },
    shopInventory: [],
  },
};

/**
 * gets the greeting for an NPC with top player names substituted
 * @param {string} npcKey - the NPC key
 * @returns {Promise<string>} the greeting text with top players substituted
 */
async function getNPCGreeting(npcKey) {
  const npc = NPCS[npcKey];
  if (!npc) return "How can I help you?";

  let greeting = npc.greeting || "How can I help you?";

  // replace placeholders with actual top players
  if (greeting.includes("{TOP_FISHING}")) {
    const topPlayer = await getTopPlayerForSkill("fishing");
    greeting = greeting.replace("{TOP_FISHING}", topPlayer);
  }
  if (greeting.includes("{TOP_MINING}")) {
    const topPlayer = await getTopPlayerForSkill("mining");
    greeting = greeting.replace("{TOP_MINING}", topPlayer);
  }
  if (greeting.includes("{TOP_FARMING}")) {
    const topPlayer = await getTopPlayerForSkill("farming");
    greeting = greeting.replace("{TOP_FARMING}", topPlayer);
  }
  if (greeting.includes("{TOP_FORAGING}")) {
    const topPlayer = await getTopPlayerForSkill("foraging");
    greeting = greeting.replace("{TOP_FORAGING}", topPlayer);
  }

  return greeting;
}

module.exports = NPCS;
module.exports.getNPCGreeting = getNPCGreeting;
