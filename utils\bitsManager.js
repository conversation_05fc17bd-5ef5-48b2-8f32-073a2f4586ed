/**
 * Bits Manager
 * Handles bits earning, collection, and background processing
 */

const { savePlayerData } = require("./playerDataManager");
const { BITS_EMOJI } = require("../gameConfig");
const {
  isBoosterCookieActive,
  recalculateBitsFromMultiplier,
  BITS_COLLECTION_INTERVAL_MS,
} = require("./boosterCookieManager");
const { getPlayerAccessories } = require("./accessoryManager");

/**
 * Collects pending bits for a player based on time elapsed
 * @param {string} userId - The player's Discord ID
 * @param {Object} character - The player's character data
 * @returns {Object} Result with bits collected and updated character
 */
async function collectPendingBits(userId, character) {
  // check if booster cookie is active
  if (!isBoosterCookieActive(character)) {
    return {
      bitsCollected: 0,
      character,
      collectionsProcessed: 0,
    };
  }

  // recalculate bits available based on current multiplier (retroactive)
  const originalBitsAvailable = character.bits_available || 0;
  character = recalculateBitsFromMultiplier(character);
  const newBitsAvailable = character.bits_available || 0;
  const bitsAvailableChanged = originalBitsAvailable !== newBitsAvailable;

  // save the recalculated bits available if it changed, even if no collection happens
  if (bitsAvailableChanged) {
    await savePlayerData(userId, character, ["bits_available"]);
  }

  // check if player has bits available
  const bitsAvailable = character.bits_available || 0;
  if (bitsAvailable <= 0) {
    return {
      bitsCollected: 0,
      character,
      collectionsProcessed: 0,
    };
  }

  const currentTime = Date.now();
  const lastCollection = character.last_bits_collection || currentTime;
  const timeElapsed = currentTime - lastCollection;

  // calculate how many collection intervals have passed
  const intervalsCompleted = Math.floor(
    timeElapsed / BITS_COLLECTION_INTERVAL_MS
  );

  if (intervalsCompleted <= 0) {
    return {
      bitsCollected: 0,
      character,
      collectionsProcessed: 0,
    };
  }

  // calculate bits to collect per interval (250 * current multiplier)
  const bitsMultiplier = character.bits_multiplier || 1.0;
  const bitsPerCollection = 250 * bitsMultiplier;

  let totalBitsCollected = 0;
  let collectionsProcessed = 0;
  let remainingBitsAvailable = bitsAvailable;

  // check if player has Bits Talisman equipped for potential doubles
  let hasBitsTalisman = false;
  try {
    const accessories = await getPlayerAccessories(userId);
    hasBitsTalisman = accessories.some(
      (acc) =>
        acc.isEquipped && String(acc.itemKey).toUpperCase() === "BITS_TALISMAN"
    );
  } catch {
    hasBitsTalisman = false; // fail closed if accessory fetch fails
  }

  // process each collection interval
  for (let i = 0; i < intervalsCompleted && remainingBitsAvailable > 0; i++) {
    // base collection for this interval
    const baseCollect = Math.min(bitsPerCollection, remainingBitsAvailable);

    // roll for Bits Talisman double; bonus must come from remaining available bits
    let intervalGain = baseCollect;
    if (hasBitsTalisman && Math.random() < 0.1) {
      // how much extra can we actually take? limited by what's left after base
      const extraPossible = Math.max(0, remainingBitsAvailable - baseCollect);
      const bonus = Math.min(baseCollect, extraPossible);
      intervalGain += bonus; // award the bonus (up to base amount)
    }

    totalBitsCollected += intervalGain;
    remainingBitsAvailable -= intervalGain; // consume available bits including any bonus
    collectionsProcessed++;
  }

  // update character data
  character.bits_available = remainingBitsAvailable;
  character.last_bits_collection = currentTime;

  // add collected bits to inventory
  const currentBits = character.bits || 0;
  character.bits = currentBits + totalBitsCollected;

  // save to database if any bits were collected
  if (totalBitsCollected > 0) {
    await savePlayerData(userId, character, [
      "bits_available",
      "last_bits_collection",
      "bits",
    ]);
  }

  return {
    bitsCollected: totalBitsCollected,
    character,
    collectionsProcessed,
    bitsRemaining: remainingBitsAvailable,
  };
}

/**
 * Gets bits earning rate information for a player
 * @param {Object} character - The player's character data
 * @returns {Object} Information about bits earning
 */
function getBitsEarningInfo(character) {
  if (!isBoosterCookieActive(character)) {
    return {
      isEarning: false,
      bitsPerCollection: 0,
      collectionIntervalMinutes: 30,
      bits_available: 0,
      timeUntilNextCollection: 0,
    };
  }

  // recalculate bits available to show current state with multiplier
  const updatedCharacter = recalculateBitsFromMultiplier(character);

  const bitsMultiplier = updatedCharacter.bits_multiplier || 1.0;
  const bitsPerCollection = 250 * bitsMultiplier;
  const bitsAvailable = updatedCharacter.bits_available || 0;

  const currentTime = Date.now();
  const lastCollection = updatedCharacter.last_bits_collection || currentTime;
  const timeSinceLastCollection = currentTime - lastCollection;
  const timeUntilNext = Math.max(
    0,
    BITS_COLLECTION_INTERVAL_MS - timeSinceLastCollection
  );

  return {
    isEarning: bitsAvailable > 0,
    bitsPerCollection,
    collectionIntervalMinutes: 30,
    bitsAvailable,
    timeUntilNextCollection: timeUntilNext,
    bitsMultiplier,
    base_bits_from_cookies: updatedCharacter.base_bits_from_cookies || 0,
  };
}

/**
 * Formats bits earning information for display
 * @param {Object} character - The player's character data
 * @returns {string} Formatted bits earning text
 */
function formatBitsEarningDisplay(character) {
  const info = getBitsEarningInfo(character);

  if (!info.isEarning) {
    if (!isBoosterCookieActive(character)) {
      return "⚠️ No Booster Cookie active";
    } else {
      return `⚠️ No ${BITS_EMOJI} Bits Available to earn`;
    }
  }

  const timeUntilNextFormatted = formatTimeRemaining(
    info.timeUntilNextCollection
  );

  let displayText = `🔄 Earning ${info.bitsPerCollection.toLocaleString()} ${BITS_EMOJI} Bits every 30 minutes\n`;
  displayText += `📦 ${info.bits_available.toLocaleString()} ${BITS_EMOJI} Bits Available\n`;

  // Show base bits and multiplier info for context
  if (info.base_bits_from_cookies > 0) {
    displayText += `💎 ${info.base_bits_from_cookies.toLocaleString()} Base ${BITS_EMOJI} Bits × ${info.bits_multiplier}x Multiplier\n`;
  }

  displayText += `⏰ Next collection: ${timeUntilNextFormatted}`;

  return displayText;
}

/**
 * Formats time for display
 * @param {number} timeMs - Time in milliseconds
 * @returns {string} Formatted time string
 */
function formatTimeRemaining(timeMs) {
  if (timeMs <= 0) return "now";

  const totalSeconds = Math.floor(timeMs / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
}

module.exports = {
  collectPendingBits,
  getBitsEarningInfo,
  formatBitsEarningDisplay,
  formatTimeRemaining,
};
