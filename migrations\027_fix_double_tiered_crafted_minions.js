// Migration Script: 027_fix_double_tiered_crafted_minions.js
// Purpose: Fixes craftedMinions keys with double tier suffixes (e.g., COBBLESTONE_MINION_T2_T1 -> COBBLESTONE_MINION_T1), and deduplicates the array.

const DOUBLE_TIER_REGEX = /^(.+)_T\d+_T(\d+)$/;

async function up(db, playerDataUtils) {
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("Running double-tiered craftedMinions fix migration (027)...");
  let processedCount = 0;
  let migratedCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log("No player IDs found. Migration step 027 complete.");
    return;
  }
  console.log(`Found ${playerIds.length} players to check for migration 027.`);

  for (const userId of playerIds) {
    processedCount++;
    try {
      const characterData = await getPlayerData(userId);
      if (
        !characterData ||
        !Array.isArray(characterData.craftedMinions) ||
        characterData.craftedMinions.length === 0
      ) {
        continue; // Skip players with no or empty craftedMinions array
      }

      const oldCraftedMinions = characterData.craftedMinions;
      let changed = false;
      const cleaned = oldCraftedMinions.map((key) => {
        const match = key.match(DOUBLE_TIER_REGEX);
        if (match) {
          changed = true;
          return `${match[1]}_T${match[2]}`;
        }
        return key;
      });
      // Deduplicate
      const deduped = Array.from(new Set(cleaned));
      if (changed || deduped.length !== oldCraftedMinions.length) {
        console.log(
          `  - Player ${userId}: Fixed double-tiered keys and/or deduplicated. Before: ${JSON.stringify(oldCraftedMinions)}, After: ${JSON.stringify(deduped)}`,
        );
        characterData.craftedMinions = deduped;
        await savePlayerData(userId, characterData);
        migratedCount++;
      }
    } catch (error) {
      console.error(
        `  - Error processing player ${userId} for migration 027:`,
        error,
      );
      errorCount++;
    }
  }

  console.log(
    `Migration 027 complete. Processed: ${processedCount}, Migrated: ${migratedCount}, Errors: ${errorCount}`,
  );
}

module.exports = { up };
