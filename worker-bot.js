// Worker Bo<PERSON> - Handles action execution to distribute Discord API load
// @ts-nocheck
require("./instrument.js");

// Load environment variables early and mark as worker bot
require("dotenv").config();
process.env.IS_WORKER_BOT = "true"; // Mark this process as a worker bot for database proxying

// Worker configuration (must be defined before any other code)
const WORKER_BOT_ID = process.env.WORKER_BOT_ID || "worker-1";
const WORKER_PORT = process.env.WORKER_PORT || 3001;
const MAIN_BOT_API_URL =
  process.env.MAIN_BOT_API_URL || "http://127.0.0.1:3000";

// Log worker startup for debugging
const QUIET_BOOT = process.env.QUIET_BOOT === "true";
if (!QUIET_BOOT)
  console.log(
    `[Worker ${WORKER_BOT_ID}] Worker bot starting with ID: ${WORKER_BOT_ID}, Port: ${WORKER_PORT}`
  );

const express = require("express");
const axios = require("axios");
const http = require("http");
const https = require("https");
const { Client, GatewayIntentBits } = require("discord.js");
const config = require("./config.json");

// Initialize the same systems as main bot (but skip database-related ones for workers)
async function initializeWorkerBot() {
  try {
    if (!QUIET_BOOT)
      console.log(
        `[Worker ${WORKER_BOT_ID}] Initializing worker bot systems...`
      );

    // 1. Initialize ConfigManager (loads items/mobs data)
    const configManager = require("./utils/configManager");
    await configManager.initialize();
    console.log(`[Worker ${WORKER_BOT_ID}] ConfigManager initialized`);

    // 2. Initialize CollectionsManager (loads collection data)
    const collectionsManager = require("./utils/collectionsManager");
    await collectionsManager.initialize();
    console.log(`[Worker ${WORKER_BOT_ID}] CollectionsManager initialized`);

    // 3. Skip field mappings and database setup for workers
    console.log(
      `[Worker ${WORKER_BOT_ID}] Skipping database initialization (delegated to main bot)`
    );

    // 4. Skip database migrations on worker; assume main bot ran them
    console.log(
      `[Worker ${WORKER_BOT_ID}] Skipping migrations (assumed already applied)`
    );

    console.log(
      `[Worker ${WORKER_BOT_ID}] All systems initialized successfully`
    );
    return true;
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Failed to initialize systems:`,
      error
    );
    process.exit(1);
  }
}

// Initialize Express for receiving delegated actions from main bot
const app = express();
app.use(express.json({ limit: "10mb" }));

// track recently processed repeat interactions to prevent duplicates
const recentRepeatInteractions = new Set();

// Worker bot client - only needs to send messages
const client = new Client({
  intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages],
});

// Set up graceful error handling for worker bot (no database cleanup needed)
const { GracefulErrorHandler } = require("./utils/gracefulErrorHandler");
const gracefulErrorHandler = new GracefulErrorHandler({
  processName: `Worker-${WORKER_BOT_ID}`,
  maxCriticalErrors: 3,
  criticalErrorWindow: 300000, // 5 minutes
  gracefulShutdownTimeout: 10000, // 10 seconds
});

// Flush any pending batched progress (sent via main bot API) before exit
gracefulErrorHandler.registerCleanupHandler(async () => {
  try {
    const { batcher } = require("./utils/actionProgressBatcher");
    await batcher.flushAllBatches();
    console.log(
      `[Worker ${WORKER_BOT_ID}] Flushed action progress batches on shutdown`
    );
  } catch (e) {
    console.warn(
      `[Worker ${WORKER_BOT_ID}] Failed to flush action progress batches on shutdown:`,
      e?.message || e
    );
  }
}, "worker-flush-batches");

if (!QUIET_BOOT)
  console.log(
    `[Worker ${WORKER_BOT_ID}] Starting worker bot on port ${WORKER_PORT}`
  );

// Shared internal token and axios factory for main bot calls
const INTERNAL_TOKEN = process.env.INTERNAL_API_TOKEN || null;
function createAxiosMain(timeoutMs = 15000) {
  const client = axios.create({
    baseURL: MAIN_BOT_API_URL,
    timeout: timeoutMs,
    httpAgent: new http.Agent({
      keepAlive: true,
      maxSockets: 32,
      maxFreeSockets: 16,
      timeout: 60000,
      keepAliveMsecs: 10000,
    }),
    httpsAgent: new https.Agent({
      keepAlive: true,
      maxSockets: 32,
      maxFreeSockets: 16,
    }),
  });
  if (INTERNAL_TOKEN)
    client.defaults.headers.common["x-internal-token"] = INTERNAL_TOKEN;
  return client;
}

// Worker ready - register with main bot
client.once("ready", async () => {
  console.log(`[Worker ${WORKER_BOT_ID}] Logged in as ${client.user.tag}!`);

  // Enhanced worker registration with retry logic
  await registerWithMainBot();

  // NEW: Self-resumption - worker checks for its own pending actions
  await performSelfResumption();
});

// Enhanced worker registration function with retry logic
async function registerWithMainBot() {
  const maxRetries = 5;
  let lastError;
  const axiosMain = createAxiosMain(15000);

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // console.log(`[Worker ${WORKER_BOT_ID}] Attempting to register with main bot (attempt ${attempt}/${maxRetries})`);

      const response = await axiosMain.post(
        `/register-worker`,
        {
          workerId: WORKER_BOT_ID,
          port: WORKER_PORT,
          capabilities: ["farm", "mine", "fish", "combat", "alchemy", "forage"],
          status: "ready",
        },
        {
          timeout: 15000, // 15 second timeout
        }
      );

      if (response.status === 200) {
        // console.log(`[Worker ${WORKER_BOT_ID}] Successfully registered with main bot on attempt ${attempt}`);

        // Start heartbeat timer after successful registration
        startHeartbeatTimer();
        return;
      } else {
        throw new Error(`Registration failed with status ${response.status}`);
      }
    } catch (error) {
      lastError = error;
      console.warn(
        `[Worker ${WORKER_BOT_ID}] Registration attempt ${attempt} failed:`,
        error.message
      );

      if (attempt < maxRetries) {
        // exponential backoff: 2^attempt seconds
        const delayMs =
          Math.pow(2, attempt) * 1000 + Math.floor(Math.random() * 500);
        // console.log(`[Worker ${WORKER_BOT_ID}] Retrying registration in ${delayMs/1000}s...`);
        await new Promise((resolve) => setTimeout(resolve, delayMs));
      }
    }
  }

  // All attempts failed
  console.error(
    `[Worker ${WORKER_BOT_ID}] Failed to register with main bot after ${maxRetries} attempts:`,
    lastError?.message
  );
  console.warn(
    `[Worker ${WORKER_BOT_ID}] Worker will continue to operate but may not receive delegated actions.`
  );

  // Start heartbeat timer anyway to keep trying to connect
  startHeartbeatTimer();
}

/**
 * Self-resumption function - worker checks for its own pending actions
 * This eliminates the need for main bot HTTP delegation and reduces failure points
 */
async function performSelfResumption() {
  try {
    console.log(
      `[Worker ${WORKER_BOT_ID}] Starting self-resumption process...`
    );

    const {
      getPendingActionsForWorker,
    } = require("./utils/workerDatabaseProxy");
    const { resumeActionLoop } = require("./utils/actionResumption");

    // Announce resumption start to main bot for monitoring
    await announceResumptionStatus("starting");

    // Get this worker's pending actions directly from main bot
    const pendingActions = await getPendingActionsForWorker();

    if (!pendingActions || pendingActions.length === 0) {
      console.log(
        `[Worker ${WORKER_BOT_ID}] No pending actions found for self-resumption`
      );
      await announceResumptionStatus("completed", { actionsResumed: 0 });
      return;
    }

    console.log(
      `[Worker ${WORKER_BOT_ID}] Found ${pendingActions.length} pending actions for self-resumption: [${pendingActions.map((a) => a.id).join(", ")}]`
    );

    let successCount = 0;
    let failureCount = 0;

    // Resume each action using the existing resumption logic
    for (const actionRecord of pendingActions) {
      try {
        // SAFETY: Double-check worker ownership before attempting resumption
        if (
          actionRecord.worker_id &&
          actionRecord.worker_id !== WORKER_BOT_ID
        ) {
          console.error(
            `[Worker ${WORKER_BOT_ID}] CRITICAL: Received action ${actionRecord.id} with wrong worker_id: ${actionRecord.worker_id}`
          );
          console.error(
            `[Worker ${WORKER_BOT_ID}] This indicates a serious bug in the worker assignment system. Skipping this action.`
          );
          continue; // Skip this action completely
        }

        console.log(
          `[Worker ${WORKER_BOT_ID}] Self-resuming action ${actionRecord.id} (${actionRecord.action_type} for user ${actionRecord.user_id}) - worker_id: ${actionRecord.worker_id}`
        );

        // Use existing resumeActionLoop but DON'T await it - let it run concurrently
        // Each action will run independently in the background
        resumeActionLoop(client, actionRecord)
          .then(() => {
            successCount++;
            console.log(
              `[Worker ${WORKER_BOT_ID}] Successfully self-resumed action ${actionRecord.id}`
            );
          })
          .catch((resumeError) => {
            failureCount++;
            console.error(
              `[Worker ${WORKER_BOT_ID}] Failed to self-resume action ${actionRecord.id}:`,
              resumeError.message
            );
          });

        console.log(
          `[Worker ${WORKER_BOT_ID}] Started self-resumption for action ${actionRecord.id}`
        );
      } catch (resumeError) {
        failureCount++;
        console.error(
          `[Worker ${WORKER_BOT_ID}] Failed to start self-resumption for action ${actionRecord.id}:`,
          resumeError.message
        );

        // Continue with other actions - don't let one failure stop everything
      }
    }

    console.log(
      `[Worker ${WORKER_BOT_ID}] Self-resumption process started for ${pendingActions.length} actions. Actions will complete independently.`
    );
    await announceResumptionStatus("completed", {
      actionsStarted: pendingActions.length,
      totalActionsFound: pendingActions.length,
    });
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Self-resumption process failed:`,
      error.message
    );
    console.error(
      `[Worker ${WORKER_BOT_ID}] Worker will continue operating but some actions may not have resumed`
    );

    await announceResumptionStatus("failed", { error: error.message });
  }
}

/**
 * Announces resumption status to main bot for monitoring and debugging
 */
async function announceResumptionStatus(status, data = {}) {
  try {
    const axiosMain = createAxiosMain(5000);
    await axiosMain.post(`/worker-notification`, {
      eventType: "resumption",
      workerId: WORKER_BOT_ID,
      status: status,
      data: {
        timestamp: new Date().toISOString(),
        ...data,
      },
    });
  } catch (notificationError) {
    // Don't let notification failures stop resumption
    console.warn(
      `[Worker ${WORKER_BOT_ID}] Failed to announce resumption status '${status}':`,
      notificationError.message
    );
  }
}

// Enhanced heartbeat timer with better error handling
function startHeartbeatTimer() {
  console.log(`[Worker ${WORKER_BOT_ID}] Starting heartbeat timer`);
  let consecutiveFailures = 0;
  const MAX_CONSECUTIVE_FAILURES = 3;
  const axiosMain = createAxiosMain(10000);

  setInterval(async () => {
    try {
      await axiosMain.post(`/worker-notification`, {
        eventType: "heartbeat",
        workerId: WORKER_BOT_ID,
        data: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
        },
      });

      // successful heartbeat - reset failure counter
      if (consecutiveFailures > 0) {
        // console.log(`[Worker ${WORKER_BOT_ID}] Heartbeat recovered after ${consecutiveFailures} failures`);
        consecutiveFailures = 0;
      }
    } catch (error) {
      consecutiveFailures++;
      console.warn(
        `[Worker ${WORKER_BOT_ID}] Heartbeat failed (${consecutiveFailures}/${MAX_CONSECUTIVE_FAILURES}):`,
        error.message
      );

      // If too many consecutive failures, try to re-register
      if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
        // console.log(`[Worker ${WORKER_BOT_ID}] Too many heartbeat failures, attempting re-registration...`);
        consecutiveFailures = 0; // reset to prevent spam

        // attempt re-registration (don't await to avoid blocking heartbeat timer)
        registerWithMainBot().catch((err) => {
          console.warn(
            `[Worker ${WORKER_BOT_ID}] Re-registration failed:`,
            err.message
          );
        });
      }
    }
  }, 30000); // every 30 seconds
}

// Handle action delegation from main bot
app.post("/api/execute-action", async (req, res) => {
  try {
    const { actionType, interaction, duration, amount, targetItem, wasMax } =
      req.body;

    // Import action utilities for getting player data (worker-safe version)
    const {
      getPlayerData,
      axiosClient,
    } = require("./utils/workerDatabaseProxy");

    // Get player data first to get proper character name
    let character = await getPlayerData(interaction.user.id);
    if (!character) {
      throw new Error("Player data not found");
    }

    // console.log(`[Worker ${WORKER_BOT_ID}] Received action: ${actionType} for user ${character.display_name || character.displayName || interaction.user?.username || interaction.user?.id || 'unknown'}.`);

    // enforce actionId presence
    if (!interaction.actionId) {
      console.error(
        `[Worker ${WORKER_BOT_ID}] ERROR: No actionId provided for new action! This will break action tracking.`
      );
      return res
        .status(400)
        .json({ success: false, error: "No actionId provided" });
    }

    console.log(
      `[Worker ${WORKER_BOT_ID}] Processing action ${interaction.actionId}`
    );

    // CRITICAL FIX: Update the worker_id in the database to match the actual executing worker
    // This fixes the message mismatch issue during resumption
    const { updateActionWorkerId } = require("./utils/workerDatabaseProxy");
    try {
      await updateActionWorkerId(interaction.actionId, WORKER_BOT_ID);
      console.log(
        `[Worker ${WORKER_BOT_ID}] Updated action ${interaction.actionId} worker_id to ${WORKER_BOT_ID}`
      );
    } catch (updateError) {
      console.error(
        `[Worker ${WORKER_BOT_ID}] Failed to update worker_id for action ${interaction.actionId}:`,
        updateError.message
      );
      // Continue processing - this shouldn't be fatal
    }

    // Fetch the real channel object using the worker bot's Discord client
    const realChannel = await client.channels.fetch(interaction.channelId);
    // Create mock interaction object for the worker bot
    const mockInteraction = {
      id: `worker_${WORKER_BOT_ID}_${Date.now()}`,
      user: interaction.user,
      guildId: interaction.guildId,
      channelId: interaction.channelId,
      channel: realChannel, // Use the real channel object
      replied: false,
      deferred: true, // Main bot already deferred the interaction
      actionId: interaction.actionId, // Pass the actionId from main bot delegation
      workerId: interaction.workerId || WORKER_BOT_ID, // ensure workerId is set for DB persistence
      // Mock interaction methods to send messages through Discord
      reply: async (content) => {
        try {
          return await realChannel.send(content);
        } catch (error) {
          console.error(`[Worker ${WORKER_BOT_ID}] Failed to reply:`, error);
          throw error;
        }
      },
      editReply: async (content) => {
        try {
          // if resuming, use the original messageId from the action record
          if (interaction.messageId) {
            const originalMsg = await realChannel.messages.fetch(
              interaction.messageId
            );
            return await originalMsg.edit(content);
          } else {
            // fallback: send new message if no messageId
            return await realChannel.send(content);
          }
        } catch (error) {
          console.error(
            `[Worker ${WORKER_BOT_ID}] Failed to editReply:`,
            error
          );
          throw error;
        }
      },
      followUp: async (content) => {
        try {
          return await realChannel.send(content);
        } catch (error) {
          console.error(`[Worker ${WORKER_BOT_ID}] Failed to followUp:`, error);
          throw error;
        }
      },
    };

    // Ensure abort controller is created in the worker for combat actions
    if (actionType === "combat" && interaction.actionId) {
      const { createAbortController } = require("./utils/instantStopUtils");
      createAbortController(interaction.actionId);
      // console.log(`[Worker ${WORKER_BOT_ID}] Created AbortController for combat action ${interaction.actionId}`);
    }

    // Import action utilities for direct execution
    const { handleSkillActionExecution } = require("./utils/skillActionUtils");

    // Character data already loaded above for proper logging

    // Get the appropriate skill config
    const skillConfigs = {
      farming: require("./data/skillConfigs").farmingSkillConfig,
      mining: require("./data/skillConfigs").miningSkillConfig,
      fishing: require("./data/skillConfigs").fishingSkillConfig,
      combat: require("./data/skillConfigs").combatSkillConfig,
      alchemy: require("./data/skillConfigs").alchemySkillConfig,
      foraging: require("./data/skillConfigs").foragingSkillConfig,
    };

    const skillConfig = skillConfigs[actionType];
    if (!skillConfig) {
      console.error(
        `[Worker ${WORKER_BOT_ID}] Unknown action type received: ${actionType}`
      );
      res
        .status(400)
        .json({ success: false, error: `Unknown action type: ${actionType}` });
      return;
    }

    // recalculate amount if this was originally a "max" action
    let finalAmount = amount || duration;
    if (wasMax) {
      const {
        getPlayerSkillLevel,
        getPlayerData,
      } = require("./utils/workerDatabaseProxy");
      const { calculateMaxActionAmount } = require("./utils/skillLimits");
      // refresh character data to ensure we have the latest skill levels
      const freshCharacter = await getPlayerData(interaction.user.id);

      // get current skill level and recalculate max amount
      const currentSkillLevel =
        getPlayerSkillLevel(freshCharacter, actionType) || 0;
      const newMaxAmount = calculateMaxActionAmount(currentSkillLevel);
      finalAmount = newMaxAmount;
    } else {
      finalAmount = amount || duration;
    }

    // Normalize the resource key
    const normalizedResourceKey =
      typeof targetItem === "string" ? targetItem.toUpperCase() : "";

    // debug: log the resource key for brewing actions
    // if (actionType === 'alchemy') {
    //   console.log(`[Worker ${WORKER_BOT_ID}] Brewing action received with resourceKey:`, normalizedResourceKey);
    // }
    // RESPOND IMMEDIATELY to prevent main bot timeout
    res.status(200).json({ success: true, workerId: WORKER_BOT_ID });

    // Execute the action asynchronously after responding
    processActionAsync(
      mockInteraction,
      character,
      normalizedResourceKey,
      finalAmount,
      skillConfig,
      wasMax,
      interaction.actionId
    );
  } catch (error) {
    console.error(`[Worker ${WORKER_BOT_ID}] Error executing action:`, error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Process action asynchronously after responding to HTTP request
async function processActionAsync(
  mockInteraction,
  character,
  normalizedResourceKey,
  finalAmount,
  skillConfig,
  wasMax,
  actionId
) {
  try {
    const { handleSkillActionExecution } = require("./utils/skillActionUtils");

    // Execute the action directly, bypassing validation since main bot already validated
    await handleSkillActionExecution(
      mockInteraction,
      character,
      normalizedResourceKey,
      finalAmount,
      false, // isAgainCommandOrResumption
      skillConfig,
      wasMax
    );
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error in async action processing:`,
      error
    );
  }
}

// Stop action endpoint for routed stop requests from main bot
app.post("/api/stop-action", async (req, res) => {
  try {
    const { actionId, userId, interactionId, channelId } = req.body;

    if (!actionId || !userId) {
      return res
        .status(400)
        .json({ success: false, error: "actionId and userId are required" });
    }

    console.log(
      `[Worker ${WORKER_BOT_ID}] Received stop request for action ${actionId} from user ${userId}`
    );

    // Use the instant stop system to abort the action
    const { abortAction } = require("./utils/instantStopUtils");
    const aborted = abortAction(actionId);

    if (aborted) {
      console.log(
        `[Worker ${WORKER_BOT_ID}] Successfully aborted action ${actionId} via API request`
      );
      res.json({ success: true, message: "Action stopped successfully" });
    } else {
      console.warn(
        `[Worker ${WORKER_BOT_ID}] Action ${actionId} not found in active controllers (may already be stopped)`
      );
      res.json({
        success: true,
        message: "Action was not running or already stopped",
      });
    }
  } catch (error) {
    console.error(
      `[Worker ${WORKER_BOT_ID}] Error handling stop action API request:`,
      error
    );
    res.status(500).json({ success: false, error: error.message });
  }
});

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.json({
    workerId: WORKER_BOT_ID,
    status: "healthy",
    uptime: process.uptime(),
    discordReady: client.readyAt !== null,
    memoryUsage: process.memoryUsage(),
    timestamp: new Date().toISOString(),
  });
});

// Debug endpoint for monitoring worker state
app.get("/api/debug", (req, res) => {
  res.json({
    workerId: WORKER_BOT_ID,
    status: "running",
    uptime: process.uptime(),
    discordReady: client.readyAt !== null,
    discordUser: client.user
      ? {
          id: client.user.id,
          tag: client.user.tag,
        }
      : null,
    memoryUsage: process.memoryUsage(),
    timestamp: new Date().toISOString(),
    mainBotUrl: MAIN_BOT_API_URL,
    workerPort: WORKER_PORT,
  });
});

// Get the appropriate worker token
function getWorkerToken() {
  const workerNumber = WORKER_BOT_ID.replace("worker-", "");

  // Try to get token from multiBot.workerBots structure
  if (
    config.multiBot &&
    config.multiBot.workerBots &&
    config.multiBot.workerBots[workerNumber]
  ) {
    console.log(
      `[Worker ${WORKER_BOT_ID}] Using token for worker ${workerNumber}`
    );
    return config.multiBot.workerBots[workerNumber].token;
  }

  // Fallback to old structure if it exists
  if (
    config.workerBotTokens &&
    config.workerBotTokens[`worker${workerNumber}`]
  ) {
    console.log(
      `[Worker ${WORKER_BOT_ID}] Using fallback token for worker${workerNumber}`
    );
    return config.workerBotTokens[`worker${workerNumber}`];
  }

  // CRITICAL FIX: Don't fallback to main bot token - this causes message ownership conflicts
  console.error(
    `[Worker ${WORKER_BOT_ID}] CRITICAL ERROR: No worker token found for worker ${workerNumber}`
  );
  console.error(
    `[Worker ${WORKER_BOT_ID}] Available workers in config: ${config.multiBot?.workerBots ? Object.keys(config.multiBot.workerBots).join(", ") : "none"}`
  );
  console.error(
    `[Worker ${WORKER_BOT_ID}] This worker should not be launched if it's not properly configured.`
  );
  console.error(
    `[Worker ${WORKER_BOT_ID}] Exiting to prevent Discord API conflicts...`
  );
  process.exit(1); // Exit rather than use wrong token
}

// Initialize and start the worker bot
async function startWorkerBot() {
  // Initialize all game systems first
  await initializeWorkerBot();

  // Start Express server with keep-alive aligned to main bot
  const server = app.listen(WORKER_PORT, () => {
    console.log(
      `[Worker ${WORKER_BOT_ID}] Express server listening on port ${WORKER_PORT}`
    );
  });
  try {
    server.keepAliveTimeout = 65000;
    server.headersTimeout = 66000;
  } catch {}

  // Send shutdown notification on exit
  gracefulErrorHandler.registerCleanupHandler(async () => {
    try {
      const axiosMain = createAxiosMain(5000);
      await axiosMain.post("/worker-notification", {
        eventType: "worker-shutdown",
        workerId: WORKER_BOT_ID,
        data: { reason: "graceful-exit" },
      });
    } catch {}
  }, "worker-shutdown-notify");

  // Listen for button interactions (repeat, unified stop, and fishing bait buttons)
  client.on("interactionCreate", async (interaction) => {
    if (!interaction.isButton() && !interaction.isStringSelectMenu()) return;

    // --- Unified Stop Button Handler ---
    if (
      interaction.isButton() &&
      interaction.customId.startsWith("unified_stop:")
    ) {
      // Only handle if this worker sent the message
      if (interaction.message.author?.id !== client.user.id) return;
      const { handleUnifiedStopButton } = require("./utils/unifiedStopButtons");
      const handled = await handleUnifiedStopButton(interaction);
      if (handled) return;
    }

    // --- Legacy Stop Action Button Handler (for fishing) ---
    if (
      interaction.isButton() &&
      interaction.customId.startsWith("stop_action:")
    ) {
      // Only handle if this worker sent the message
      if (interaction.message.author?.id !== client.user.id) return;

      try {
        const [, actionIdStr, userId] = interaction.customId.split(":");
        const actionId = parseInt(actionIdStr, 10);

        // Verify user ownership
        if (interaction.user.id !== userId) {
          // Send warning directly to channel instead of using interaction
          try {
            await interaction.channel.send({
              content: `<@${interaction.user.id}> You cannot stop another player's action.`,
            });
          } catch (sendError) {
            console.warn(
              `[Worker ${WORKER_BOT_ID}] Failed to send ownership warning:`,
              sendError.message
            );
          }
          return;
        }

        // Use the instant stop system to stop the action
        const { abortAction } = require("./utils/instantStopUtils");
        const stopped = abortAction(actionId);

        if (stopped) {
          // Edit the message directly instead of using interaction.update()
          try {
            await interaction.message.edit({
              content: "⏹️ Stopping action...",
              components: [],
            });
          } catch (editError) {
            // If message edit fails, just log it - the action is still stopped
            console.warn(
              `[Worker ${WORKER_BOT_ID}] Failed to edit stop message, but action was stopped:`,
              editError.message
            );
          }
          console.log(
            `[Worker ${WORKER_BOT_ID}] User ${userId} stopped action ${actionId} via stop_action button`
          );
        } else {
          // For failure case, try to send a new message instead of using interaction
          try {
            await interaction.channel.send({
              content: `<@${userId}> Unable to stop action. It may have already completed.`,
              ephemeral: false, // Can't be ephemeral with channel.send
            });
          } catch (sendError) {
            console.warn(
              `[Worker ${WORKER_BOT_ID}] Failed to send stop failure message:`,
              sendError.message
            );
          }
        }
        return;
      } catch (error) {
        console.error(
          `[Worker ${WORKER_BOT_ID}] Error handling stop_action button:`,
          error
        );
        // Send error message directly to channel instead of using interaction
        try {
          await interaction.channel.send({
            content: `<@${userId}> An error occurred while stopping the action.`,
          });
        } catch (sendError) {
          console.warn(
            `[Worker ${WORKER_BOT_ID}] Failed to send error message:`,
            sendError.message
          );
        }
        return;
      }
    }

    // --- Fishing Bait Button Handlers ---
    if (
      interaction.isButton() &&
      interaction.customId.startsWith("bait_swap:")
    ) {
      // Only handle if this worker sent the message
      if (interaction.message.author?.id !== client.user.id) return;
      const { handleBaitSwapInteraction } = require("./utils/fishingButtons");
      const handled = await handleBaitSwapInteraction(interaction);
      if (handled) return;
    }

    if (
      interaction.isStringSelectMenu() &&
      interaction.customId.startsWith("bait_select:")
    ) {
      // Only handle if this worker sent the message
      if (interaction.message.author?.id !== client.user.id) return;
      const {
        handleBaitSelectionInteraction,
      } = require("./utils/fishingButtons");
      const handled = await handleBaitSelectionInteraction(interaction);
      if (handled) return;
    }

    // --- Repeat Button Handler (forward to main bot for re-delegation) ---
    if (
      interaction.isButton() &&
      interaction.customId.startsWith("repeat_skill:")
    ) {
      if (interaction.message.author?.id !== client.user.id) return; // only handle our own messages

      const parts = interaction.customId.split(":");
      const hasOwner = parts.length >= 5;
      const hasMaxFlag = parts.length >= 6;
      const ownerId = hasOwner ? parts[1] : null;
      const skillName = hasOwner ? parts[2] : parts[1];
      const resourceKey = hasOwner ? parts[3] : parts[2] || null;
      const amount = hasOwner
        ? parts[4]
          ? parseInt(parts[4], 10)
          : 1
        : parts[3]
          ? parseInt(parts[3], 10)
          : 1;
      const wasMax = hasMaxFlag ? parts[5] === "max" : false;
      const userId = interaction.user.id;

      if (ownerId && ownerId !== userId) {
        try {
          await interaction.channel.send({
            content: `<@${userId}> You cannot use another player's Repeat button.`,
          });
        } catch {
          /* ignore */
        }
        return;
      }

      try {
        await interaction.deferUpdate();
      } catch {
        return;
      }

      const dedupeKey = `${interaction.message.id}:${userId}`;
      if (recentRepeatInteractions.has(dedupeKey)) return;
      recentRepeatInteractions.add(dedupeKey);
      setTimeout(() => recentRepeatInteractions.delete(dedupeKey), 5000);

      // Disable buttons optimistically
      if (interaction.message.components?.length > 0) {
        const {
          ActionRowBuilder,
          ButtonBuilder,
          ComponentType,
        } = require("discord.js");
        const newComponents = [];
        for (const row of interaction.message.components) {
          if (row.type !== ComponentType.ActionRow) {
            newComponents.push(row);
            continue;
          }
          const newRow = new ActionRowBuilder();
          for (const c of row.components) {
            if (c.type === ComponentType.Button) {
              const b = ButtonBuilder.from(c);
              b.setDisabled(true);
              newRow.addComponents(b);
            } else newRow.addComponents(c);
          }
          if (newRow.components.length) newComponents.push(newRow);
        }
        try {
          await interaction.message.edit({ components: newComponents });
        } catch {
          /* ignore */
        }
      }

      // Ask main bot to process repeat via delegation
      const axios = require("axios");
      const { MAIN_BOT_API_URL = "http://127.0.0.1:3000" } = process.env;
      try {
        const resp = await axios.post(
          `${MAIN_BOT_API_URL}/api/repeat-action-request`,
          {
            userId,
            skillName,
            resourceKey,
            amount,
            channelId: interaction.channel.id,
            wasMax,
          },
          { timeout: 15000 }
        );

        const data = resp.data || {};
        if (!data.success) {
          // Re-enable buttons on failure
          if (interaction.message.components?.length > 0) {
            const {
              ActionRowBuilder,
              ButtonBuilder,
              ComponentType,
            } = require("discord.js");
            const newComponents = [];
            for (const row of interaction.message.components) {
              if (row.type !== ComponentType.ActionRow) {
                newComponents.push(row);
                continue;
              }
              const newRow = new ActionRowBuilder();
              for (const c of row.components) {
                if (c.type === ComponentType.Button) {
                  const b = ButtonBuilder.from(c);
                  b.setDisabled(false);
                  newRow.addComponents(b);
                } else newRow.addComponents(c);
              }
              if (newRow.components.length) newComponents.push(newRow);
            }
            try {
              await interaction.message.edit({ components: newComponents });
            } catch {
              /* ignore */
            }
          }
          const reasonText =
            data.reason === "busy"
              ? "You are already busy."
              : data.reason === "region_locked"
                ? "You cannot perform that action in this region."
                : data.error || "Failed to repeat action.";
          try {
            await interaction.channel.send({
              content: `<@${userId}> ${reasonText}`,
            });
          } catch {
            /* ignore */
          }
        }
      } catch (apiErr) {
        console.error(
          `[Worker ${WORKER_BOT_ID}] repeat-action forward failed:`,
          apiErr.message
        );
        try {
          await interaction.channel.send({
            content: `<@${userId}> Failed to start repeat action: ${apiErr.message}`,
          });
        } catch {
          /* ignore */
        }
      }
    }
  });

  // Login to Discord
  const workerToken = getWorkerToken();
  await client.login(workerToken);
}

// Start the worker bot
startWorkerBot().catch(console.error);
