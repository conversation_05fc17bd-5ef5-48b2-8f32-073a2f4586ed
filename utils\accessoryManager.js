const { v4: uuidv4 } = require("uuid");
const { dbGet, dbAll, dbRunQueued } = require("./dbUtils");
const {
  getPlayerData,
  recalculateAndSaveStats,
} = require("./playerDataManager");
const { checkAndNotifyDisblockXP } = require("./disblockXpSystem");
const { initializeCoinGeneration } = require("./coinGeneration");
const configManager = require("./configManager");

/**
 * Get numeric value for rarity comparison
 * Higher numbers = higher rarity
 * @param {string} rarityName - The rarity name (Common, Uncommon, Rare, Epic, etc.)
 * @returns {number} - Numeric value for comparison
 */
function getRarityValue(rarityName) {
  const rarityMap = {
    Common: 1,
    Uncommon: 2,
    Rare: 3,
    Epic: 4,
    Legendary: 5,
    Mythic: 6,
    Divine: 7,
    Special: 8,
  };

  return rarityMap[rarityName] || 0; // Default to 0 for unknown rarities
}

/**
 * Add a new accessory to a player's accessories
 * @param {string} userId - Discord user ID
 * @param {string} itemKey - Item key for the accessory
 * @param {Object} dataJson - Additional data for the accessory (reforge, etc.)
 * @returns {Promise<Object>} - Result object with success status and accessory ID
 */
async function addAccessoryAtomically(userId, itemKey, dataJson = {}) {
  try {
    const accessoryId = uuidv4();
    const insertSql = `
      INSERT INTO player_accessories (accessory_id, discord_id, item_key, is_equipped, data_json, equipped_timestamp)
      VALUES (?, ?, ?, 0, ?, ?)
    `;

    await dbRunQueued(insertSql, [
      accessoryId,
      userId,
      itemKey,
      JSON.stringify(dataJson),
      Date.now(), // equipped_timestamp
    ]);

    return {
      success: true,
      accessoryId: accessoryId,
      message: "Accessory added successfully.",
    };
  } catch {
    return {
      success: false,
      message: "Failed to add accessory.",
    };
  }
}

/**
 * Remove an accessory from a player's accessories
 * @param {string} userId - Discord user ID
 * @param {string} accessoryId - Accessory ID to remove
 * @returns {Promise<Object>} - Result object with success status
 */
async function removeAccessoryAtomically(userId, accessoryId) {
  try {
    const deleteSql = `
      DELETE FROM player_accessories 
      WHERE discord_id = ? AND accessory_id = ?
    `;

    const result = await dbRunQueued(deleteSql, [userId, accessoryId]);

    if (result.changes === 0) {
      return {
        success: false,
        message: "Accessory not found.",
      };
    }

    // Recalculate player stats after removing accessory
    const player = await getPlayerData(userId);
    if (player) {
      await recalculateAndSaveStats(userId, player);
    }

    return {
      success: true,
      message: "Accessory removed successfully.",
    };
  } catch {
    return {
      success: false,
      message: "Failed to remove accessory.",
    };
  }
}

/**
 * Equip an accessory
 * @param {string} userId - Discord user ID
 * @param {string} accessoryId - Accessory ID to equip
 * @returns {Promise<Object>} - Result object with success status
 */
async function equipAccessoryAtomically(
  userId,
  accessoryId,
  interaction = null
) {
  try {
    // Check if accessory exists and belongs to user
    const getAccessorySql = `
      SELECT item_key FROM player_accessories 
      WHERE accessory_id = ? AND discord_id = ?
    `;
    const accessoryRow = await dbGet(getAccessorySql, [accessoryId, userId]);

    if (!accessoryRow) {
      return {
        success: false,
        message: "Accessory not found.",
      };
    }

    // Check if the same accessory type is already equipped
    const duplicateCheckSql = `
      SELECT COUNT(*) as count FROM player_accessories 
      WHERE discord_id = ? AND item_key = ? AND is_equipped = 1
    `;
    const duplicateResult = await dbGet(duplicateCheckSql, [
      userId,
      accessoryRow.item_key,
    ]);

    if (duplicateResult.count > 0) {
      return {
        success: false,
        message: "You already have this accessory equipped!",
      };
    }

    // Check for accessory line conflicts and handle rarity-based priority
    const allItems = configManager.getAllItems();
    const itemData = allItems[accessoryRow.item_key];

    if (itemData && itemData.accessoryLine) {
      // Find all equipped accessories with the same accessory line
      const conflictingSql = `
        SELECT accessory_id, item_key FROM player_accessories 
        WHERE discord_id = ? AND is_equipped = 1
      `;
      const equippedAccessories = await dbAll(conflictingSql, [userId]);

      for (const equippedAcc of equippedAccessories) {
        const equippedItemData = allItems[equippedAcc.item_key];
        if (
          equippedItemData &&
          equippedItemData.accessoryLine === itemData.accessoryLine
        ) {
          // Compare rarities - only unequip if new item has higher or equal rarity
          const newRarity = getRarityValue(itemData.rarity?.name);
          const equippedRarity = getRarityValue(equippedItemData.rarity?.name);

          if (newRarity >= equippedRarity) {
            // Unequip the conflicting accessory (new item has higher/equal rarity)
            const unequipSql = `
              UPDATE player_accessories 
              SET is_equipped = 0 
              WHERE discord_id = ? AND accessory_id = ?
            `;
            await dbRunQueued(unequipSql, [userId, equippedAcc.accessory_id]);
          } else {
            // New item has lower rarity, don't equip it
            return {
              success: false,
              message: `Cannot equip ${itemData.name} - you already have a higher rarity ${equippedItemData.name} equipped in the same accessory line.`,
            };
          }
        }
      }
    }

    // Check accessory bag limit
    const player = await getPlayerData(userId);
    if (!player) {
      return {
        success: false,
        message: "Player not found.",
      };
    }

    const { getAccessoryBagSize } = require("../commands/accessories");
    const bagInfo = getAccessoryBagSize(player);

    // Count currently equipped accessories
    const countSql = `
      SELECT COUNT(*) as count FROM player_accessories 
      WHERE discord_id = ? AND is_equipped = 1
    `;
    const countResult = await dbGet(countSql, [userId]);
    const equippedCount = countResult.count || 0;

    if (equippedCount >= bagInfo.slots) {
      return {
        success: false,
        message: `You can only equip ${bagInfo.slots} accessories. Unequip an accessory first.`,
      };
    }

    // Equip the accessory
    const updateSql = `
      UPDATE player_accessories 
      SET is_equipped = 1 
      WHERE discord_id = ? AND accessory_id = ?
    `;

    const result = await dbRunQueued(updateSql, [userId, accessoryId]);

    if (result.changes === 0) {
      return {
        success: false,
        message: "Failed to equip accessory.",
      };
    }

    // Recalculate player stats
    await recalculateAndSaveStats(userId, player);

    // Initialize coin generation for this player if they have coin-generating accessories
    await initializeCoinGeneration(userId);

    // Check for Disblock XP updates (Magical Power changes)
    await checkAndNotifyDisblockXP(userId, interaction);

    // Verify the accessory is equipped

    return {
      success: true,
      message: "Accessory equipped successfully.",
    };
  } catch {
    return {
      success: false,
      message: "Failed to equip accessory.",
    };
  }
}

/**
 * Unequip an accessory
 * @param {string} userId - Discord user ID
 * @param {string} accessoryId - Accessory ID to unequip
 * @returns {Promise<Object>} - Result object with success status
 */
async function unequipAccessoryAtomically(
  userId,
  accessoryId,
  interaction = null
) {
  try {
    const updateSql = `
      UPDATE player_accessories 
      SET is_equipped = 0 
      WHERE discord_id = ? AND accessory_id = ?
    `;
    const result = await dbRunQueued(updateSql, [userId, accessoryId]);

    if (result.changes === 0) {
      return {
        success: false,
        message: "Accessory not found or already unequipped.",
      };
    }

    // Recalculate player stats
    const player = await getPlayerData(userId);
    if (player) {
      await recalculateAndSaveStats(userId, player);

      // Check for Disblock XP updates (Magical Power changes)
      await checkAndNotifyDisblockXP(userId, interaction);
    }

    // Verify the accessory is unequipped

    return {
      success: true,
      message: "Accessory unequipped successfully.",
    };
  } catch {
    return {
      success: false,
      message: "Failed to unequip accessory.",
    };
  }
}

/**
 * Get all accessories for a player
 * @param {string} userId - Discord user ID
 * @returns {Promise<Array>} - Array of accessory objects
 */
async function getPlayerAccessories(userId) {
  try {
    const sql = `
      SELECT accessory_id, item_key, is_equipped, data_json 
      FROM player_accessories 
      WHERE discord_id = ?
      ORDER BY is_equipped DESC, item_key ASC
    `;

    const rows = await dbAll(sql, [userId]);

    const accessories = rows.map((row) => {
      let dataJson = {};
      if (row.data_json) {
        try {
          dataJson =
            typeof row.data_json === "string"
              ? JSON.parse(row.data_json)
              : row.data_json;
        } catch {
          dataJson = {};
        }
      }

      // Convert is_equipped to boolean properly
      const isEquipped = row.is_equipped === 1 || row.is_equipped === true;

      return {
        id: row.accessory_id,
        itemKey: row.item_key,
        isEquipped: isEquipped,
        data_json: dataJson,
      };
    });

    return accessories;
  } catch {
    return [];
  }
}

/**
 * Update accessory data (for reforging, etc.)
 * @param {string} userId - Discord user ID
 * @param {string} accessoryId - Accessory ID to update
 * @param {Object} dataJson - New data for the accessory
 * @returns {Promise<Object>} - Result object with success status
 */
async function updateAccessoryData(userId, accessoryId, dataJson) {
  try {
    const updateSql = `
      UPDATE player_accessories 
      SET data_json = ? 
      WHERE discord_id = ? AND accessory_id = ?
    `;

    const result = await dbRunQueued(updateSql, [
      JSON.stringify(dataJson),
      userId,
      accessoryId,
    ]);

    if (result.changes === 0) {
      return {
        success: false,
        message: "Accessory not found.",
      };
    }

    return {
      success: true,
      message: "Accessory data updated successfully.",
    };
  } catch {
    return {
      success: false,
      message: "Failed to update accessory data.",
    };
  }
}

module.exports = {
  addAccessoryAtomically,
  removeAccessoryAtomically,
  equipAccessoryAtomically,
  unequipAccessoryAtomically,
  getPlayerAccessories,
  updateAccessoryData,
};
