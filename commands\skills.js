const {
  <PERSON>lash<PERSON><PERSON>mandBuilder,
  EmbedBuilder,
  MessageFlags,
} = require("discord.js");
const { SKILLS, skillEmojis, EMBED_COLORS } = require("../gameConfig.js");
const { getFilteredPlayers } = require("../utils/playerCache");
const { getLevelFromExp } = require("../utils/expFunctions.js");
const { createProgressBar } = require("../utils/displayUtils");
const { checkRankPermission } = require("../utils/permissionUtils.js");
const { dbGet } = require("../utils/dbUtils.js");
const { formatXP } = require("../utils/formatUtils");

const SKILLS_AVERAGE_EXCLUDED = ["cooking", "dungeoneering", "alchemy"];

function capitalizeSkillName(skillName) {
  return skillName.charAt(0).toUpperCase() + skillName.slice(1);
}

function generateOverviewDisplay(characterSkills) {
  let totalSkillLevel = 0;
  let includedSkillsCount = 0;
  const skillFieldsArray = [];
  const skillsExcludedSet = new Set(SKILLS_AVERAGE_EXCLUDED);

  const processedSkills = SKILLS.LIST.map((skillName) => {
    const skillData = characterSkills?.[skillName] || { exp: 0 };
    const exp = skillData.exp || 0;
    const levelInfo = getLevelFromExp(exp);

    if (!skillsExcludedSet.has(skillName)) {
      totalSkillLevel += levelInfo.level;
      includedSkillsCount++;
    }

    return {
      name: skillName,
      levelInfo,
      emoji: skillEmojis[skillName] || "❓",
    };
  });

  const averageSkillLevel =
    includedSkillsCount > 0
      ? (totalSkillLevel / includedSkillsCount).toFixed(2)
      : "0.00";

  processedSkills.forEach((skill, index) => {
    const { name, levelInfo, emoji } = skill;
    const progressBar = createProgressBar(
      levelInfo.currentLevelExp,
      levelInfo.requiredExpForNextLevel,
      {
        size: 10,
        showXpText: true,
        formatFn: formatXP,
        useEmojis: true,
      }
    );

    skillFieldsArray.push({
      name: `${emoji} ${capitalizeSkillName(name)} (Level ${levelInfo.level})`,
      value: progressBar,
      inline: true,
    });

    if ((index + 1) % 2 === 0 && index + 1 < SKILLS.LIST.length) {
      skillFieldsArray.push({
        name: "\u200b",
        value: "\u200b",
        inline: true,
      });
    }
  });

  return { skillFieldsArray, averageSkillLevel };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("skills")
    .setDescription("Displays your current skill levels and experience.")
    .addStringOption((option) =>
      option
        .setName("player")
        .setDescription("Name of the player whose skills you want to view")
        .setRequired(false)
        .setAutocomplete(true)
    ),

  async autocomplete(interaction) {
    const focusedOption = interaction.options.getFocused(true);

    if (focusedOption.name === "player") {
      const focusedValue = focusedOption.value.toLowerCase();

      try {
        const choices = getFilteredPlayers(focusedValue, 25);
        await interaction.respond(choices);
      } catch (error) {
        console.error("Error in skills player autocomplete:", error);
        await interaction.respond([]);
      }
    }
  },

  async execute(interaction) {
    try {
      const targetUsername = interaction.options.getString("player");
      let targetUserId = interaction.user.id;
      let isViewingSelf = true;

      // If a username was provided, look up the target user
      if (targetUsername) {
        const targetUserData = await dbGet(
          "SELECT discord_id FROM players WHERE name = ?",
          [targetUsername]
        );
        if (!targetUserData) {
          return interaction.reply({
            content: `Player "${targetUsername}" not found.`,
            flags: [MessageFlags.Ephemeral],
          });
        }
        targetUserId = targetUserData.discord_id;
        isViewingSelf = targetUserId === interaction.user.id;
      }

      const { getPlayerData } = require("../utils/playerDataManager");

      const [executorData, playerData] = await Promise.all([
        dbGet("SELECT rank FROM players WHERE discord_id = ?", [
          interaction.user.id,
        ]),
        getPlayerData(targetUserId, ["name", "rank", "skills"]),
      ]);

      if (!executorData) {
        return interaction.reply({
          content:
            "Your character data could not be loaded to verify permissions.",
          flags: [MessageFlags.Ephemeral],
        });
      }

      const requiredRank = isViewingSelf ? "MEMBER" : "ADMIN";
      if (!checkRankPermission({ rank: executorData.rank }, requiredRank)) {
        const errorMessage = isViewingSelf
          ? "You don't have permission to view your own skills (Rank Error)."
          : "You don't have permission to view other players' skills.";
        return interaction.reply({
          content: errorMessage,
          flags: [MessageFlags.Ephemeral],
        });
      }

      if (!playerData) {
        const replyContent = isViewingSelf
          ? "You don't have a character yet! Visit the setup channel to create one."
          : `${targetUsername || "That user"} does not have a character yet.`;
        return interaction.reply({
          content: replyContent,
          flags: [MessageFlags.Ephemeral],
        });
      }

      const skills = playerData.skills || {};
      const rankPrefix =
        playerData.rank && playerData.rank !== "PLAYER"
          ? `[${playerData.rank}] `
          : "";

      const { skillFieldsArray, averageSkillLevel } =
        generateOverviewDisplay(skills);

      const embed = new EmbedBuilder()
        .setColor(EMBED_COLORS.BLUE)
        .setTitle(
          `${rankPrefix}${playerData.name}'s Skills (Average: ${averageSkillLevel})`
        )
        .addFields(skillFieldsArray);

      try {
        await interaction.reply({
          embeds: [embed],
        });
      } catch (replyError) {
        console.error(
          `[Skills Command] Error sending embed reply for ${targetUserId}:`,
          replyError
        );
        await interaction
          .followUp({
            content:
              "[SKL-RPL-ERR] An error occurred trying to display skills. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    } catch (error) {
      console.error("[Skills Command Error]", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction
          .reply({
            content:
              "[SKL-EXE-ERR] An unexpected error occurred. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      } else {
        await interaction
          .followUp({
            content:
              "[SKL-EXE-ERR] An unexpected error occurred. Please report this error code to an Admin.",
            flags: [MessageFlags.Ephemeral],
          })
          .catch(() => {});
      }
    }
  },
};
