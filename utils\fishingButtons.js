const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  StringSelectMenuBuilder,
} = require("discord.js");
const { enhancedBaitManager } = require("./enhancedBaitManager");
const configManager = require("./configManager");

// quick formatter so large counts stay compact on the button
function formatAmount(n) {
  if (n == null) return "0";
  if (n < 1000) return n.toString();
  if (n < 1_000_000) return +(n / 1000).toFixed(1).replace(/\.0$/, "") + "k";
  return +(n / 1_000_000).toFixed(1).replace(/\.0$/, "") + "M";
}

/**
 * Ensures fishing buttons (Stop + Bait Swap) exist on a message
 * @param {import('discord.js').Message} message Discord message to edit
 * @param {number} actionId Database action ID
 * @param {string} userId Player Discord ID
 * @param {string} [stopLabel="Stop Fishing"] Label for the stop button
 */
async function ensureFishingButtons(
  message,
  actionId,
  userId,
  stopLabel = "Stop Fishing"
) {
  try {
    console.log(
      `[FishingButtons] ensureFishingButtons called for action ${actionId}, user ${userId}`
    );

    if (!message || !message.edit || !actionId || !userId) {
      console.warn(
        `[FishingButtons] Invalid parameters: message=${!!message}, message.edit=${!!message?.edit}, actionId=${actionId}, userId=${userId}`
      );
      return;
    }

    // Check if fishing buttons already exist
    const hasFishingButtons = (message.components || []).some((row) =>
      row.components.some(
        (c) =>
          c.customId &&
          (c.customId.startsWith(`stop_action:${actionId}`) ||
            c.customId.startsWith(`bait_swap:${actionId}`))
      )
    );

    if (hasFishingButtons) {
      console.log(
        `[FishingButtons] Fishing buttons already exist for action ${actionId}, skipping`
      );
      return; // Nothing to do
    }

    console.log(
      `[FishingButtons] Adding fishing buttons for action ${actionId}`
    );

    // Get current active bait for button display
    const currentBaitInfo = await getCurrentBaitInfo(userId);
    console.log(
      `[FishingButtons] Current bait info for user ${userId}:`,
      currentBaitInfo
    );

    const stopButton = new ButtonBuilder()
      .setCustomId(`stop_action:${actionId}:${userId}`)
      .setLabel(stopLabel)
      .setStyle(ButtonStyle.Danger);

    const baitSwapButton = new ButtonBuilder()
      .setCustomId(`bait_swap:${actionId}:${userId}`)
      .setEmoji(currentBaitInfo.emoji)
      // only show quantity if we actually have a bait selected
      .setStyle(ButtonStyle.Secondary);
    if (currentBaitInfo.baitKey) {
      baitSwapButton.setLabel(formatAmount(currentBaitInfo.amount));
    }

    const row = new ActionRowBuilder().addComponents(
      stopButton,
      baitSwapButton
    );

    console.log(
      `[FishingButtons] Attempting to edit message ${message.id} with fishing buttons`
    );
    await message.edit({ components: [...message.components, row] });
    console.log(
      `[FishingButtons] Successfully added fishing buttons to message ${message.id} for action ${actionId}`
    );
  } catch (err) {
    // Handle specific message edit errors gracefully during resumption
    if (err.code === 50005) {
      console.log(
        `[FishingButtons] Cannot edit message authored by another user (resumption scenario) - skipping button update for action ${actionId}`
      );
      return; // Don't treat this as an error - it's expected during resumption
    }

    console.error(
      `[FishingButtons] Failed to ensure fishing buttons for action ${actionId}:`,
      err
    );
    console.error(`[FishingButtons] Error details:`, {
      messageId: message?.id,
      messageAuthor: message?.author?.id,
      messageChannel: message?.channel?.id,
      actionId,
      userId,
      errorMessage: err.message,
      errorCode: err.code,
    });
  }
}

/**
 * Updates the bait swap button with new bait info
 * @param {import('discord.js').Message} message Discord message to edit
 * @param {number} actionId Database action ID
 * @param {string} userId Player Discord ID
 * @param {object} [actualBaitInfo] Optional bait info to use instead of current preference
 */
async function updateBaitSwapButton(
  message,
  actionId,
  userId,
  actualBaitInfo = null
) {
  try {
    if (!message || !message.edit || !actionId || !userId) return;

    const currentBaitInfo =
      actualBaitInfo || (await getCurrentBaitInfo(userId));
    // If we have a baitKey but no amount (older caller), fetch amount so label persists
    if (
      currentBaitInfo.baitKey &&
      (currentBaitInfo.amount === undefined || currentBaitInfo.amount === null)
    ) {
      try {
        const available = await enhancedBaitManager.getAvailableBaits(userId);
        currentBaitInfo.amount = available[currentBaitInfo.baitKey] ?? 0;
      } catch {
        /* ignore */
      }
    }

    // Find and update the bait swap button
    const components = [...message.components];
    let updated = false;

    for (const row of components) {
      for (const component of row.components) {
        if (
          component.customId &&
          component.customId.startsWith(`bait_swap:${actionId}`)
        ) {
          // Update the button emoji
          const newButton = ButtonBuilder.from(component).setEmoji(
            currentBaitInfo.emoji
          );
          if (currentBaitInfo.baitKey) {
            newButton.setLabel(formatAmount(currentBaitInfo.amount));
          } else {
            // remove label if previously had one and now no bait
            if (newButton.data) {
              delete newButton.data.label;
            }
          }

          // Replace in the row
          const rowIndex = components.indexOf(row);
          const buttonIndex = row.components.indexOf(component);
          const newRow = new ActionRowBuilder();

          row.components.forEach((comp, idx) => {
            if (idx === buttonIndex) {
              newRow.addComponents(newButton);
            } else {
              newRow.addComponents(comp);
            }
          });

          components[rowIndex] = newRow;
          updated = true;
          break;
        }
      }
      if (updated) break;
    }

    if (updated) {
      await message.edit({ components });
    }
  } catch (err) {
    // Handle specific message edit errors gracefully during resumption
    if (err.code === 50005) {
      console.log(
        `[FishingButtons] Cannot edit message authored by another user (resumption scenario) - skipping bait swap button update for action ${actionId}`
      );
      return; // Don't treat this as an error - it's expected during resumption
    }

    console.warn(
      `[FishingButtons] Failed to update bait swap button for action ${actionId}:`,
      err
    );
  }
}

/**
 * Gets current bait info for display
 * @param {string} userId Player Discord ID
 * @returns {Promise<{emoji: string, name: string}>}
 */
async function getCurrentBaitInfo(userId) {
  try {
    const baitInfo = await enhancedBaitManager.getPreferredBait(userId);
    if (baitInfo.baitKey) {
      const allItems = configManager.getAllItems();
      const baitData = allItems[baitInfo.baitKey];
      // get live amount so we can show it
      const available = await enhancedBaitManager.getAvailableBaits(userId);
      return {
        emoji: baitData?.emoji || "🎣",
        name: baitData?.name || baitInfo.baitKey,
        baitKey: baitInfo.baitKey,
        amount: available[baitInfo.baitKey] ?? 0,
      };
    }
    return {
      emoji: "🚫",
      name: "No Bait",
      baitKey: null,
      amount: 0,
    };
  } catch (error) {
    console.error("[FishingButtons] Error getting current bait info:", error);
    return { emoji: "🎣", name: "Unknown", baitKey: null, amount: 0 };
  }
}

/**
 * Handles bait swap button interactions
 * @param {import('discord.js').ButtonInteraction} interaction
 * @returns {Promise<boolean>} Whether the interaction was handled
 */
async function handleBaitSwapInteraction(interaction) {
  if (!interaction.customId.startsWith("bait_swap:")) return false;

  try {
    const [, actionIdStr, userId] = interaction.customId.split(":");

    // Verify user
    if (interaction.user.id !== userId) {
      await interaction.reply({
        content: "❌ You can only change your own bait!",
      });

      // Set a timeout to delete the message after 5 seconds
      setTimeout(async () => {
        try {
          await interaction.deleteReply();
        } catch (deleteError) {
          // Message might already be deleted or interaction expired
          console.log(
            "[FishingButtons] Could not delete user verification error message:",
            deleteError.message
          );
        }
      }, 5000);
      return true;
    }

    // Get available baits
    const availableBaits = await enhancedBaitManager.getAvailableBaits(userId);
    const allItems = configManager.getAllItems();

    const baitOptions = [];

    // Add available baits
    for (const [baitKey, amount] of Object.entries(availableBaits)) {
      if (amount > 0) {
        const baitData = allItems[baitKey];
        if (baitData) {
          const baitConfig =
            require("./enhancedBaitManager").BAIT_PRIORITIES.find(
              (b) => b.key === baitKey
            );
          baitOptions.push({
            label: `${baitData.name} (${amount.toLocaleString()} available)`,
            description: `+${baitConfig?.fishingSpeed || 0} Fishing Speed`,
            value: baitKey,
            emoji: baitData.emoji,
          });
        }
      }
    }

    // Add "No Bait" option
    baitOptions.push({
      label: "No Bait",
      description: "Disable bait usage",
      value: "NONE",
      emoji: "🚫",
    });

    if (baitOptions.length === 1) {
      // Only "No Bait" option
      await interaction.reply({
        content: "❌ You don't have any bait in your inventory!",
      });

      // Set a timeout to delete the message after 5 seconds
      setTimeout(async () => {
        try {
          await interaction.deleteReply();
        } catch (deleteError) {
          // Message might already be deleted or interaction expired
          console.log(
            "[FishingButtons] Could not delete no bait error message:",
            deleteError.message
          );
        }
      }, 5000);
      return true;
    }

    // Create select menu for bait selection
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId(`bait_select:${actionIdStr}:${userId}`)
      .setPlaceholder("Choose bait to use...")
      .addOptions(baitOptions.slice(0, 25)); // Discord limit

    const row = new ActionRowBuilder().addComponents(selectMenu);

    await interaction.reply({
      content: " ",
      components: [row],
    });

    // Set a timeout to delete the message after 5 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        // Message might already be deleted or interaction expired
        console.log(
          "[FishingButtons] Could not delete bait selection message:",
          error.message
        );
      }
    }, 5000);

    return true;
  } catch (error) {
    console.error(
      "[FishingButtons] Error handling bait swap interaction:",
      error
    );
    await interaction.reply({
      content: "❌ An error occurred while changing bait.",
    });

    // Set a timeout to delete the message after 5 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (deleteError) {
        // Message might already be deleted or interaction expired
        console.log(
          "[FishingButtons] Could not delete error message:",
          deleteError.message
        );
      }
    }, 5000);
    return true;
  }
}

/**
 * Handles bait selection from the select menu
 * @param {import('discord.js').StringSelectMenuInteraction} interaction
 * @returns {Promise<boolean>} Whether the interaction was handled
 */
async function handleBaitSelectionInteraction(interaction) {
  if (!interaction.customId.startsWith("bait_select:")) return false;

  try {
    const [, _actionIdStr, userId] = interaction.customId.split(":");

    // Verify user
    if (interaction.user.id !== userId) {
      await interaction.reply({
        content: "❌ You can only change your own bait!",
      });

      // Set a timeout to delete the message after 5 seconds
      setTimeout(async () => {
        try {
          await interaction.deleteReply();
        } catch (deleteError) {
          // Message might already be deleted or interaction expired
          console.log(
            "[FishingButtons] Could not delete user verification error message:",
            deleteError.message
          );
        }
      }, 5000);
      return true;
    }

    const selectedBait = interaction.values[0];
    console.log(
      `[FishingButtons] User ${userId} selected bait: ${selectedBait}`
    );

    // Update player's active bait setting using atomic update
    const { updatePlayerSetting } = require("./playerDataManager");
    await updatePlayerSetting(
      userId,
      "activeBait",
      selectedBait === "NONE" ? null : selectedBait
    );
    console.log(
      `[FishingButtons] Updated activeBait setting for user ${userId} to: ${selectedBait === "NONE" ? null : selectedBait}`
    );

    // Invalidate bait manager cache to reflect the change immediately
    enhancedBaitManager.invalidateCache(userId);

    // Don't update UI immediately - let the next fishing cycle handle it
    // This ensures button emoji and description only update when the new bait is actually used

    // Respond with success
    if (selectedBait === "NONE") {
      await interaction.update({
        content:
          "🚫 **Bait setting updated.** Changes will apply on the next cycle.",
        components: [],
      });
    } else {
      const allItems = configManager.getAllItems();
      const baitData = allItems[selectedBait];
      await interaction.update({
        content: `${baitData?.emoji || "🎣"} **Bait setting updated to ${baitData?.name || selectedBait}.** Changes will apply on the next cycle.`,
        components: [],
      });
    }

    // Set a timeout to delete the message after 5 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        // Message might already be deleted or interaction expired
        console.log(
          "[FishingButtons] Could not delete bait selection result:",
          error.message
        );
      }
    }, 5000);

    return true;
  } catch (error) {
    console.error(
      "[FishingButtons] Error handling bait selection interaction:",
      error
    );
    await interaction.reply({
      content: "❌ An error occurred while selecting bait.",
    });

    // Set a timeout to delete the message after 5 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (deleteError) {
        // Message might already be deleted or interaction expired
        console.log(
          "[FishingButtons] Could not delete error message:",
          deleteError.message
        );
      }
    }, 5000);
    return true;
  }
}

module.exports = {
  ensureFishingButtons,
  updateBaitSwapButton,
  getCurrentBaitInfo,
  handleBaitSwapInteraction,
  handleBaitSelectionInteraction,
};
