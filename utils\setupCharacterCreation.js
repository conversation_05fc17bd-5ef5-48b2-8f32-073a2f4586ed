const {
  Mo<PERSON><PERSON><PERSON>er,
  TextInputBuilder,
  TextInputStyle,
  ActionRowBuilder,
  EmbedBuilder,
  MessageFlags,
} = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");
const { getPlayerData, savePlayerData } = require("./playerDataManager");
const { getCurrentActivity } = require("./activityManager");
const { getDefaultPlayerData } = require("./playerDefaults");
const { createPersonalChannel } = require("./channelManager");
const { hideSetupChannelFromUser } = require("./setupChannelManager");
const db = require("./database");

/**
 * Handle the setup character creation button click
 * @param {ButtonInteraction} interaction - The button interaction
 * @param {Client} client - Discord client
 * @returns {boolean} - Whether the interaction was handled
 */
async function handleSetupCharacterCreation(interaction, _client) {
  try {
    const userId = interaction.user.id;

    // check if user is currently busy with another activity
    if (await getCurrentActivity(userId)) {
      await interaction.reply({
        content:
          "You cannot create a character while you are busy with another activity!",
        flags: [MessageFlags.Ephemeral],
      });
      return true;
    }

    // check if user already has a character
    if (await getPlayerData(userId)) {
      await interaction.reply({
        embeds: [
          new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Character Already Exists")
            .setDescription(
              "You already have a character! You can't create another one."
            ),
        ],
        flags: [MessageFlags.Ephemeral],
      });
      return true;
    }

    console.log(`[SetupCharacterCreation] Button clicked by user ${userId}`);

    // create and show the modal
    const modal = new ModalBuilder()
      .setCustomId("setup_character_modal_" + userId)
      .setTitle("Create Your Character");

    const nameInput = new TextInputBuilder()
      .setCustomId("character_name")
      .setLabel("What's your character's name?")
      .setStyle(TextInputStyle.Short)
      .setRequired(true)
      .setMinLength(1)
      .setMaxLength(24)
      .setPlaceholder("Enter a name (letters and numbers only)");

    const firstActionRow = new ActionRowBuilder().addComponents(nameInput);
    modal.addComponents(firstActionRow);

    await interaction.showModal(modal);
    return true;
  } catch (error) {
    console.error(
      "[SetupCharacterCreation] Error handling button interaction:",
      error
    );

    try {
      await interaction.reply({
        embeds: [
          new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Error")
            .setDescription(
              "An error occurred while starting character creation. Please try again."
            ),
        ],
        flags: [MessageFlags.Ephemeral],
      });
    } catch (replyError) {
      console.error(
        "[SetupCharacterCreation] Error sending error reply:",
        replyError
      );
    }

    return true;
  }
}

/**
 * Handle the setup character creation modal submission
 * @param {ModalSubmitInteraction} interaction - The modal interaction
 * @param {Client} client - Discord client
 * @returns {boolean} - Whether the interaction was handled
 */
async function handleSetupCharacterModal(interaction, client) {
  try {
    const userId = interaction.user.id;
    const modalUserId = interaction.customId.split("_")[3];

    // ensure the user who submitted is the same user who initiated
    if (userId !== modalUserId) {
      await interaction.reply({
        content: "An error occurred with the character creation.",
        flags: [MessageFlags.Ephemeral],
      });
      return true;
    }

    console.log(`[SetupCharacterCreation] Modal submitted by user ${userId}`);

    // get the character name from the modal
    const characterName =
      interaction.fields.getTextInputValue("character_name");

    // validate character name (letters and numbers only)
    if (!isValidCharacterName(characterName)) {
      await interaction.reply({
        embeds: [
          new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Invalid Name")
            .setDescription(
              "Invalid character name. Please use only letters and numbers (1-24 characters)."
            ),
        ],
        flags: [MessageFlags.Ephemeral],
      });
      return true;
    }

    // check if name is taken in database
    const nameTaken = await dbGet(
      "SELECT 1 FROM players WHERE LOWER(name) = LOWER(?)",
      [characterName]
    );

    if (nameTaken) {
      await interaction.reply({
        embeds: [
          new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Name Taken")
            .setDescription(
              "This character name is already taken. Please choose a different name."
            ),
        ],
        flags: [MessageFlags.Ephemeral],
      });
      return true;
    }

    // defer the reply since we'll be doing multiple async operations
    await interaction.deferReply({ ephemeral: true });

    try {
      // create the personal channel first
      const personalChannel = await createPersonalChannel(
        client,
        characterName,
        userId
      );

      // create character with the provided name and channel ID
      await createCharacterWithChannel(
        userId,
        characterName,
        personalChannel.id
      );

      // hide the setup channel from this user since they now have a personal channel
      await hideSetupChannelFromUser(client, userId);

      // send welcome message in their new channel
      const welcomeEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GREEN)
        .setTitle(
          "<:mob_chicken:1367682990041464955> Welcome to Akin's DisBlock!"
        )
        .setDescription(
          `**Welcome ${characterName}**, you're now in **The Hub**!\n\n` +
            "" +
            "This is a parody of the game **Hypixel SkyBlock**, but\n" +
            "it's idle! And the combat is turn-based. If you've played\n" +
            "Hypixel Skyblock before, you'll find some familiar elements here!\n\n" +
            "**Some useful commands\n" +
            "• Use `/skills` and `/collections` to see your progress\n" +
            "• Use `/farm`, `/mine`, `/fish`, `/forage`, or `/combat` to start skilling\n" +
            "• Use `/inventory` to see your items & more\n" +
            "• Other commands: `/stats` `/storage` `/talk`\n\n" +
            `<:developer_akinsoft:1369169613442650204> Have fun ${characterName}**`
        )
        .setFooter({ text: "Your adventure begins now!" });

      await personalChannel.send({ embeds: [welcomeEmbed] });

      // reply to the modal submission
      await interaction.editReply({
        embeds: [
          new EmbedBuilder()
            .setColor(EMBED_COLORS.GREEN)
            .setTitle("✅ Character Created!")
            .setDescription(
              `Character **${characterName}** created successfully!\n\n` +
                `🏠 Your personal channel: <#${personalChannel.id}>\n\n` +
                "Head over to your channel to start playing!"
            ),
        ],
      });

      console.log(
        `[SetupCharacterCreation] Successfully created character "${characterName}" for user ${userId} with channel ${personalChannel.id}`
      );
      return true;
    } catch (channelError) {
      console.error(
        "[SetupCharacterCreation] Error creating channel or character:",
        channelError
      );

      await interaction.editReply({
        embeds: [
          new EmbedBuilder()
            .setColor(EMBED_COLORS.ERROR)
            .setTitle("❌ Creation Failed")
            .setDescription(
              "An error occurred while creating your character and channel. Please contact an administrator.\n\n" +
                "**Error:** " +
                (channelError.message || "Unknown error")
            ),
        ],
      });
      return true;
    }
  } catch (error) {
    console.error(
      "[SetupCharacterCreation] Error handling modal submission:",
      error
    );

    try {
      if (interaction.deferred) {
        await interaction.editReply({
          embeds: [
            new EmbedBuilder()
              .setColor(EMBED_COLORS.ERROR)
              .setTitle("❌ Error")
              .setDescription(
                "An error occurred during character creation. Please try again."
              ),
          ],
        });
      } else {
        await interaction.reply({
          embeds: [
            new EmbedBuilder()
              .setColor(EMBED_COLORS.ERROR)
              .setTitle("❌ Error")
              .setDescription(
                "An error occurred during character creation. Please try again."
              ),
          ],
          flags: [MessageFlags.Ephemeral],
        });
      }
    } catch (replyError) {
      console.error(
        "[SetupCharacterCreation] Error sending error reply:",
        replyError
      );
    }

    return true;
  }
}

/**
 * Validate character name - letters and numbers only
 * @param {string} name - Character name to validate
 * @returns {boolean} - Whether the name is valid
 */
function isValidCharacterName(name) {
  return /^[a-zA-Z0-9]{1,24}$/.test(name);
}

/**
 * Create a new character with personal channel
 * @param {string} discordId - Discord user ID
 * @param {string} characterName - Character name
 * @param {string} channelId - Personal channel ID
 */
async function createCharacterWithChannel(discordId, characterName, channelId) {
  try {
    console.log(
      `[SetupCharacterCreation] Creating character "${characterName}" for user ${discordId} with channel ${channelId}`
    );

    // create default character data with the correct structure
    const newCharacter = getDefaultPlayerData(discordId, characterName);

    // set specific starting values
    newCharacter.current_region = "the_hub"; // starting region
    newCharacter.current_health = 100; // starting health
    newCharacter.coins = 10; // starting coins
    newCharacter.personal_channel_id = channelId; // set their personal channel

    // CRITICAL FIX: Ensure inventory structure is properly initialized
    // This fixes the bug where new characters don't gain items from actions
    if (!newCharacter.inventory) {
      newCharacter.inventory = {
        items: {},
        equipment: [],
      };
    }
    if (!newCharacter.inventory.items) {
      newCharacter.inventory.items = {};
    }
    if (!newCharacter.inventory.equipment) {
      newCharacter.inventory.equipment = [];
    }

    // save the character data
    await savePlayerData(discordId, newCharacter);

    console.log(
      `[SetupCharacterCreation] Character "${characterName}" created for user ${discordId} with channel ${channelId}`
    );
    return true;
  } catch (error) {
    console.error(
      `[SetupCharacterCreation] Error creating character for user ${discordId}:`,
      error
    );
    throw error;
  }
}

/**
 * Helper function to run db.get as a Promise
 * @param {string} query - SQL query
 * @param {Array} params - Query parameters
 * @returns {Promise} - Query result
 */
function dbGet(query, params) {
  return new Promise((resolve, reject) => {
    db.get(query, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

module.exports = {
  handleSetupCharacterCreation,
  handleSetupCharacterModal,
  createCharacterWithChannel,
};
