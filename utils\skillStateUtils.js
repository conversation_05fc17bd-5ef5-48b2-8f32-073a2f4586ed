const {
  getActionParameters,
  updateActionParameters,
} = require("./actionPersistence");

// Helper to persist pending skill action state
async function persistPendingSkillAction(userId, actionId, pendingState) {
  try {
    const existingParams = await getActionParameters(actionId);
    const newParamsToSave = {
      ...existingParams,
      pendingSkillAction: pendingState,
    };
    await updateActionParameters(actionId, newParamsToSave);
  } catch (error) {
    console.error(
      `[ERROR][persistPendingSkillAction] actionId=${actionId}, userId=${userId}:`,
      error
    );
  }
}

// Helper to clear pending skill action state
async function clearPendingSkillAction(actionId) {
  try {
    const existingParams = await getActionParameters(actionId);
    // Create a new object with all properties except pendingSkillAction
    const paramsToSave = Object.keys(existingParams).reduce((acc, key) => {
      if (key !== "pendingSkillAction") {
        acc[key] = existingParams[key];
      }
      return acc;
    }, {});
    await updateActionParameters(actionId, paramsToSave);
  } catch (error) {
    console.error(
      `[ERROR][clearPendingSkillAction] actionId=${actionId}:`,
      error
    );
  }
}

module.exports = {
  persistPendingSkillAction,
  clearPendingSkillAction,
};
