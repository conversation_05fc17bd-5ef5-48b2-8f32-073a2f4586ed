const {
  <PERSON><PERSON><PERSON>ommandBuilder,
  MessageFlags,
  EmbedBuilder,
} = require("discord.js");
const {
  getPlayerData,
  updateCurrencyAtomically,
  updatePlayerJsonFields,
} = require("../utils/playerDataManager");
const { updateInventoryAtomically } = require("../utils/inventory");
const configManager = require("../utils/configManager"); // Import the manager
const { CURRENCY, skillEmojis, EMBED_COLORS } = require("../gameConfig"); // Keep CURRENCY and add skillEmojis

const { checkRankPermission } = require("../utils/permissionUtils"); // Import rank checker
const { v4: uuidv4 } = require("uuid"); // Import uuid
const {
  getFilteredPlayers,
  getPlayerByUsername,
} = require("../utils/playerCache");
const { createPetInstance } = require("../utils/petUtils"); // Import pet creation function
const { addSkillExp } = require("../utils/skillExpManager"); // Import skill XP manager
const { COLLECTIONS } = require("../data/collections"); // Import collections data
const {
  addCollectionProgress,
  findCollectionKeyForItem,
} = require("../utils/collectionUtils"); // Import collection progress function

// Helper function to get all collections with their display names
function getAllCollections() {
  const allCollections = [];

  for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
    for (const [itemKey, collectionData] of Object.entries(
      categoryCollections
    )) {
      allCollections.push({
        name: `${collectionData.name} Collection`,
        value: `collection_${itemKey}`,
        itemKey: itemKey,
        displayName: collectionData.name,
      });
    }
  }

  return allCollections.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * Send a notification to the recipient's personal channel about receiving something
 * @param {Object} interaction - Discord interaction
 * @param {string} targetUserId - Target player's Discord ID
 * @param {string} targetName - Target player's name
 * @param {string} emoji - Emoji for the thing given
 * @param {number} quantity - Quantity given (can be negative for removal)
 * @param {string} thingName - Name of the thing given
 */
async function sendRecipientNotification(
  interaction,
  targetUserId,
  targetName,
  emoji,
  quantity,
  thingName
) {
  try {
    const targetCharacter = await getPlayerData(targetUserId);
    if (targetCharacter && targetCharacter.personal_channel_id) {
      const targetChannel = interaction.client.channels.cache.get(
        targetCharacter.personal_channel_id
      );
      if (targetChannel) {
        const action = quantity >= 0 ? "received" : "lost";
        const absQuantity = Math.abs(quantity);
        const embed = new EmbedBuilder()
          .setColor(quantity >= 0 ? EMBED_COLORS.RED : EMBED_COLORS.YELLOW)
          .setDescription(
            quantity >= 0
              ? `You have ${action} **${emoji} ${absQuantity}x ${thingName}** from an Admin`
              : `**${emoji} ${absQuantity}x ${thingName}** has been taken from you by an Admin`
          );

        await targetChannel.send({
          content: `<@${targetUserId}>`,
          embeds: [embed],
        });
      }
    }
  } catch (error) {
    console.error(`[GiveCommand] Error sending recipient notification:`, error);
    // Continue even if notification fails
  }
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("give")
    .setDescription("give stuff")
    .addStringOption((option) =>
      option
        .setName("username")
        .setDescription("username")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addStringOption((option) =>
      option
        .setName("item")
        .setDescription("the thing")
        .setRequired(true)
        .setAutocomplete(true)
    )
    .addIntegerOption((option) =>
      option
        .setName("quantity")
        .setDescription("The amount to give (negative values will remove)")
        .setRequired(true)
    )
    .addIntegerOption((option) =>
      option
        .setName("tier")
        .setDescription("The tier of the minion (only applies to minions)")
        .setMinValue(1)
        .setMaxValue(12)
    )
    .addStringOption((option) =>
      option
        .setName("rarity")
        .setDescription("The rarity of the pet (only applies to pets)")
        .addChoices(
          { name: "Common", value: "COMMON" },
          { name: "Uncommon", value: "UNCOMMON" },
          { name: "Rare", value: "RARE" },
          { name: "Epic", value: "EPIC" },
          { name: "Legendary", value: "LEGENDARY" }
        )
    ),

  async autocomplete(interaction) {
    const focusedOption = interaction.options.getFocused(true);
    let choices = [];

    if (focusedOption.name === "item") {
      const focusedValue = focusedOption.value.toLowerCase();
      const allItems = configManager.getAllItems(); // Get live items

      // Add item choices
      const itemChoices = Object.entries(allItems)
        .filter(([, itemData]) =>
          itemData.name.toLowerCase().startsWith(focusedValue)
        )
        .map(([, itemData]) => ({ name: itemData.name, value: itemData.name })); // Use Name for both display and value

      // Add Coins choice if it matches
      if (CURRENCY.name.toLowerCase().startsWith(focusedValue)) {
        choices.push({ name: CURRENCY.name, value: CURRENCY.name });
      }

      // Add skill XP choices
      const skillChoices = Object.keys(skillEmojis)
        .filter((skillName) => skillName !== "npcs" && skillName !== "default") // exclude non-skill entries
        .filter((skillName) => {
          const skillExpName = `${skillName.charAt(0).toUpperCase() + skillName.slice(1)} EXP`;
          return skillExpName.toLowerCase().startsWith(focusedValue);
        })
        .map((skillName) => {
          const skillExpName = `${skillName.charAt(0).toUpperCase() + skillName.slice(1)} EXP`;
          return {
            name: skillExpName,
            value: skillExpName,
          };
        });

      // Add collection choices
      const collectionChoices = getAllCollections()
        .filter((collection) =>
          collection.name.toLowerCase().startsWith(focusedValue)
        )
        .map((collection) => ({
          name: collection.name,
          value: collection.value,
        }));

      // Combine and limit
      choices = choices
        .concat(itemChoices)
        .concat(skillChoices)
        .concat(collectionChoices)
        .slice(0, 25);
    } else if (focusedOption.name === "username") {
      const focusedValue = focusedOption.value.toLowerCase();

      try {
        // Use cached player data for autocomplete
        choices = getFilteredPlayers(focusedValue, 25);
      } catch (error) {
        console.error(
          "[Give Command] Error fetching player names for autocomplete:",
          error
        );
        // Return empty array on error
        choices = [];
      }
    }

    await interaction.respond(choices);
  },

  async execute(interaction) {
    // 1. Permission Check using Rank System
    const senderCharacter = await getPlayerData(interaction.user.id);
    if (!senderCharacter) {
      return interaction.reply({
        content: "Could not verify your character data for permissions.",
        flags: [MessageFlags.Ephemeral],
      });
    }
    if (!checkRankPermission(senderCharacter, "OWNER")) {
      // OWNER only
      return interaction.reply({
        content:
          "You do not have permission (Rank Required: OWNER) to use this command.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // 2. Get Options
    const targetUsername = interaction.options.getString("username");
    const itemNameOrCoinOrSkillExp = interaction.options.getString("item");
    const quantity = interaction.options.getInteger("quantity");
    const tier = interaction.options.getInteger("tier") || 1; // Default to tier 1 if not specified
    const rarity = interaction.options.getString("rarity") || "COMMON"; // Default to COMMON if not specified

    // --- Get Target Player ID by username using cache ---
    const targetPlayer = getPlayerByUsername(targetUsername);

    if (!targetPlayer) {
      return interaction.reply({
        content: `Could not find a character named '${targetUsername}'. Please check the spelling (note: search is case-insensitive).`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const targetUserId = targetPlayer.discord_id;
    const targetName = targetPlayer.name; // Use the actual name with correct capitalization

    // 3. Handle Coins, Skill XP, or Item
    if (
      itemNameOrCoinOrSkillExp.toLowerCase() === CURRENCY.name.toLowerCase()
    ) {
      // Handle coins giving/taking
      try {
        const result = await updateCurrencyAtomically(
          targetUserId,
          quantity,
          0
        ); // Add to purse (can be negative), no bank change

        if (!result) {
          const action = quantity >= 0 ? "give" : "remove";
          return interaction.reply({
            content: `Failed to ${action} coins ${quantity >= 0 ? "to" : "from"} ${targetName}. ${quantity < 0 ? "They may not have enough coins." : "The transaction was rejected."}`,
            flags: [MessageFlags.Ephemeral],
          });
        }

        // Send notification to recipient's personal channel
        await sendRecipientNotification(
          interaction,
          targetUserId,
          targetName,
          CURRENCY.emoji,
          quantity,
          CURRENCY.name
        );

        const action = quantity >= 0 ? "gave" : "removed";
        const absQuantity = Math.abs(quantity);
        await interaction.reply({
          content: `Successfully ${action} ${absQuantity} ${CURRENCY.emoji} **${CURRENCY.name}** ${quantity >= 0 ? "to" : "from"} ${targetName}.`,
          flags: [MessageFlags.Ephemeral],
        });
      } catch (error) {
        console.error("[GiveCommand] Error updating coins atomically:", error);
        await interaction.reply({
          content: "An error occurred while modifying coins.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    } else if (itemNameOrCoinOrSkillExp.endsWith(" EXP")) {
      // Handle skill XP giving
      const skillDisplayName = itemNameOrCoinOrSkillExp.replace(" EXP", "");
      const skillName = skillDisplayName.toLowerCase();

      // Validate skill name
      if (
        !skillEmojis[skillName] ||
        skillName === "npcs" ||
        skillName === "default"
      ) {
        return interaction.reply({
          content: `Invalid skill name "${skillDisplayName}". Please use the autocomplete suggestions.`,
        });
      }

      try {
        // Respond to admin IMMEDIATELY - don't wait for anything else
        const action = quantity >= 0 ? "Giving" : "Removing";
        const absQuantity = Math.abs(quantity);
        await interaction.reply({
          content: `${action} ${absQuantity} ${skillEmojis[skillName]} **${skillDisplayName} XP** ${quantity >= 0 ? "to" : "from"} ${targetName}...`,
          flags: [MessageFlags.Ephemeral],
        });

        // Now do all the work asynchronously without blocking the interaction
        (async () => {
          try {
            // Send notification to recipient's personal channel
            const skillEmoji = skillEmojis[skillName];
            await sendRecipientNotification(
              interaction,
              targetUserId,
              targetName,
              skillEmoji,
              quantity,
              `${skillDisplayName} XP`
            );

            // Award skill XP with logging (can be negative for removal)
            console.log(
              `[GiveCommand] About to ${quantity >= 0 ? "award" : "remove"} ${Math.abs(quantity)} ${skillName} XP ${quantity >= 0 ? "to" : "from"} ${targetUserId}`
            );
            const skillResult = await addSkillExp(
              targetUserId,
              skillName,
              quantity
            );
            console.log(`[GiveCommand] addSkillExp returned:`, skillResult);

            if (skillResult.exp === undefined) {
              console.error(
                `[GiveCommand] Failed to modify skill XP for ${targetUserId} - skillResult was:`,
                skillResult
              );
              const failAction = quantity >= 0 ? "award" : "remove";
              await interaction.editReply({
                content: `❌ Failed to ${failAction} ${absQuantity} ${skillEmoji} **${skillDisplayName} XP** ${quantity >= 0 ? "to" : "from"} ${targetName}.`,
              });
              return;
            }

            console.log(
              `[GiveCommand] Successfully modified XP for ${targetUserId}`
            );

            // Send level-up notifications if any (only for positive XP gains)
            if (
              quantity > 0 &&
              skillResult.levelUpEmbeds &&
              skillResult.levelUpEmbeds.length > 0
            ) {
              const targetCharacter = await getPlayerData(targetUserId);
              if (targetCharacter && targetCharacter.personal_channel_id) {
                const targetChannel = interaction.client.channels.cache.get(
                  targetCharacter.personal_channel_id
                );
                if (targetChannel) {
                  // Send notifications with 1-second delays
                  for (let i = 0; i < skillResult.levelUpEmbeds.length; i++) {
                    const levelUpItem = skillResult.levelUpEmbeds[i];

                    if (levelUpItem && levelUpItem.embed) {
                      // Send as embed (fallback)
                      await targetChannel.send({ embeds: [levelUpItem.embed] });
                    } else {
                      // Fallback for unexpected structure
                      console.warn(
                        `[GiveCommand] Unexpected levelUpEmbed structure:`,
                        levelUpItem
                      );
                      if (levelUpItem && levelUpItem.title) {
                        // Treat as raw embed
                        await targetChannel.send({ embeds: [levelUpItem] });
                      }
                    }

                    // Add delay between notifications except after the last one
                    if (i < skillResult.levelUpEmbeds.length - 1) {
                      await new Promise((resolve) => setTimeout(resolve, 1000));
                    }
                  }
                }
              }
            }

            // Wait a moment to ensure save operations complete
            await new Promise((resolve) => setTimeout(resolve, 200));

            // Check for DisBlock XP gains for the recipient (only for positive XP)
            if (quantity > 0) {
              try {
                const {
                  checkAndNotifyDisblockXP,
                } = require("../utils/disblockXpSystem");
                // CRITICAL: Re-fetch character data AFTER skill XP save to get updated values
                const updatedTargetCharacter =
                  await getPlayerData(targetUserId);

                // Create minimal interaction for notifications to personal channel
                let mockInteraction = null;
                if (
                  updatedTargetCharacter &&
                  updatedTargetCharacter.personal_channel_id
                ) {
                  const targetChannel = interaction.client.channels.cache.get(
                    updatedTargetCharacter.personal_channel_id
                  );
                  if (targetChannel) {
                    mockInteraction = {
                      channel: targetChannel,
                      user: { id: targetUserId },
                    };

                    // Add guild and member info for nickname updates if available
                    if (interaction.guild) {
                      try {
                        const targetMember =
                          await interaction.guild.members.fetch(targetUserId);
                        if (targetMember) {
                          mockInteraction.guild = interaction.guild;
                          mockInteraction.member = targetMember;
                        }
                      } catch (memberFetchError) {
                        console.log(
                          `[GiveCommand] Could not fetch member for nickname update: ${memberFetchError.message}`
                        );
                      }
                    }
                  }
                }

                console.log(
                  `[GiveCommand] Checking DisBlock XP for recipient ${targetUserId}`
                );
                await checkAndNotifyDisblockXP(
                  targetUserId,
                  mockInteraction,
                  updatedTargetCharacter
                );
                console.log(
                  `[GiveCommand] Completed DisBlock XP check for recipient ${targetUserId}`
                );
              } catch (disblockError) {
                console.error(
                  `[GiveCommand] Error checking DisBlock XP for recipient:`,
                  disblockError
                );
                // Don't fail the whole operation for DisBlock XP issues
              }
            }

            // Wait a moment to ensure save operations complete
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Verify the XP was actually saved by checking the database
            console.log(
              `[GiveCommand] Verifying XP save for ${targetUserId}...`
            );
            const verifyCharacter = await getPlayerData(targetUserId);
            const currentSkillXP =
              verifyCharacter?.skills?.[skillName]?.exp || 0;
            console.log(
              `[GiveCommand] Current ${skillName} XP in database: ${currentSkillXP}`
            );

            // Update admin with final result
            const finalAction = quantity >= 0 ? "gave" : "removed";
            await interaction.editReply({
              content: `✅ Successfully ${finalAction} ${absQuantity} ${skillEmoji} **${skillDisplayName} XP** ${quantity >= 0 ? "to" : "from"} ${targetName}. (Current: ${currentSkillXP} XP)`,
            });
          } catch (asyncError) {
            console.error(
              `[GiveCommand] Error in async skill XP processing:`,
              asyncError
            );
            const errorAction = quantity >= 0 ? "Gave" : "Removed";
            await interaction.editReply({
              content: `⚠️ ${errorAction} ${absQuantity} ${skillEmojis[skillName]} **${skillDisplayName} XP** ${quantity >= 0 ? "to" : "from"} ${targetName}, but there may have been issues with notifications.`,
            });
          }
        })();
      } catch (error) {
        console.error(
          `[GiveCommand] Error giving skill XP ${skillName} to ${targetUserId}:`,
          error
        );
        await interaction.reply({
          content: `An error occurred while giving skill XP: ${error.message}`,
          flags: [MessageFlags.Ephemeral],
        });
      }
    } else if (itemNameOrCoinOrSkillExp.startsWith("collection_")) {
      // Handle collection giving
      const collectionItemKey = itemNameOrCoinOrSkillExp.replace(
        "collection_",
        ""
      );

      // Find the collection data
      let collectionData = null;
      let collectionDisplayName = collectionItemKey;
      let itemEmoji = "📦";

      for (const [, categoryCollections] of Object.entries(COLLECTIONS)) {
        if (categoryCollections[collectionItemKey]) {
          collectionData = categoryCollections[collectionItemKey];
          collectionDisplayName = collectionData.name;
          break;
        }
      }

      if (!collectionData) {
        return interaction.reply({
          content: `Collection "${collectionItemKey}" not found. Please use the autocomplete suggestions.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      // Get the item emoji from the item data
      const itemData = configManager.getItem(collectionItemKey);
      if (itemData && itemData.emoji) {
        itemEmoji = itemData.emoji;
      }

      try {
        // get the target character
        let targetCharacter = await getPlayerData(targetUserId);
        if (!targetCharacter) {
          return interaction.reply({
            content: `Could not find or load character data for ${targetName}.`,
            flags: [MessageFlags.Ephemeral],
          });
        }

        // Ensure collections_json is an object, not a string
        if (typeof targetCharacter.collections === "string") {
          try {
            targetCharacter.collections = JSON.parse(
              targetCharacter.collections
            );
          } catch (e) {
            console.error(`[GiveCommand] Failed to parse collections_json:`, e);
            targetCharacter.collections = {};
          }
        } else if (!targetCharacter.collections) {
          targetCharacter.collections = {};
        }

        // respond to admin immediately
        const action = quantity >= 0 ? "Giving" : "Removing";
        const absQuantity = Math.abs(quantity);
        await interaction.reply({
          content: `${action} ${absQuantity} **${collectionDisplayName} Collection Progress** ${quantity >= 0 ? "to" : "from"} ${targetName}...`,
          flags: [MessageFlags.Ephemeral],
        });

        // handle collection progress asynchronously
        (async () => {
          try {
            // Send notification to recipient's personal channel first
            await sendRecipientNotification(
              interaction,
              targetUserId,
              targetName,
              itemEmoji,
              quantity,
              `${collectionDisplayName} Collection`
            );

            if (quantity > 0) {
              // add collection progress
              const progressResult = await addCollectionProgress(
                targetUserId,
                collectionItemKey,
                quantity,
                targetCharacter
              );

              if (progressResult.character) {
                targetCharacter = progressResult.character;
              }

              // send collection tier notifications to their personal channel if any
              if (
                progressResult.notifications &&
                progressResult.notifications.length > 0
              ) {
                if (targetCharacter.personal_channel_id) {
                  const targetChannel = interaction.client.channels.cache.get(
                    targetCharacter.personal_channel_id
                  );
                  if (targetChannel) {
                    for (
                      let i = 0;
                      i < progressResult.notifications.length;
                      i++
                    ) {
                      const embed = progressResult.notifications[i];
                      await targetChannel.send({ embeds: [embed] });

                      // add delay between notifications except after the last one
                      if (i < progressResult.notifications.length - 1) {
                        await new Promise((resolve) =>
                          setTimeout(resolve, 1000)
                        );
                      }
                    }
                  }
                }
              }

              // Save the collections data atomically to avoid conflicts
              await updatePlayerJsonFields(targetUserId, {
                collections: targetCharacter.collections,
              });

              // Check for DisBlock XP gains for the recipient (only for positive progress)
              if (quantity > 0) {
                try {
                  const {
                    checkAndNotifyDisblockXP,
                  } = require("../utils/disblockXpSystem");
                  // CRITICAL: Re-fetch character data AFTER the atomic save to get updated collections
                  const freshCharacterData = await getPlayerData(targetUserId);

                  // Create minimal interaction for notifications to personal channel
                  let mockInteraction = null;
                  if (
                    freshCharacterData &&
                    freshCharacterData.personal_channel_id
                  ) {
                    const targetChannel = interaction.client.channels.cache.get(
                      freshCharacterData.personal_channel_id
                    );
                    if (targetChannel) {
                      mockInteraction = {
                        channel: targetChannel,
                        user: { id: targetUserId },
                      };

                      // Add guild and member info for nickname updates if available
                      if (interaction.guild) {
                        try {
                          const targetMember =
                            await interaction.guild.members.fetch(targetUserId);
                          if (targetMember) {
                            mockInteraction.guild = interaction.guild;
                            mockInteraction.member = targetMember;
                          }
                        } catch (memberFetchError) {
                          console.log(
                            `[GiveCommand] Could not fetch member for nickname update: ${memberFetchError.message}`
                          );
                        }
                      }
                    }
                  }

                  console.log(
                    `[GiveCommand] Checking DisBlock XP for collection progress recipient ${targetUserId} with fresh data`
                  );
                  await checkAndNotifyDisblockXP(
                    targetUserId,
                    mockInteraction,
                    freshCharacterData
                  );
                  console.log(
                    `[GiveCommand] Completed DisBlock XP check for collection progress recipient ${targetUserId}`
                  );
                } catch (disblockError) {
                  console.error(
                    `[GiveCommand] Error checking DisBlock XP for collection progress recipient:`,
                    disblockError
                  );
                  // Don't fail the whole operation for DisBlock XP issues
                }
              }
            } else {
              // handle removal - directly modify collections_json
              targetCharacter.collections = targetCharacter.collections || {};
              const actualCollectionKey =
                findCollectionKeyForItem(collectionItemKey);
              const collectionKeyUpper = actualCollectionKey
                ? actualCollectionKey.toUpperCase()
                : collectionItemKey.toUpperCase();
              const currentAmount =
                targetCharacter.collections[collectionKeyUpper] || 0;
              const newAmount = Math.max(0, currentAmount + quantity); // quantity is negative for removal
              targetCharacter.collections[collectionKeyUpper] = newAmount;

              // Save the collections data atomically to avoid conflicts
              await updatePlayerJsonFields(targetUserId, {
                collections: targetCharacter.collections,
              });
            }

            // get final collection amount for display
            const finalCharacter = await getPlayerData(targetUserId);

            // Parse collections_json if it's a string
            let finalCollections = finalCharacter?.collections;
            if (typeof finalCollections === "string") {
              try {
                finalCollections = JSON.parse(finalCollections);
              } catch (e) {
                console.error(
                  `[GiveCommand] Failed to parse final collections:`,
                  e
                );
                finalCollections = {};
              }
            }

            const actualCollectionKey =
              findCollectionKeyForItem(collectionItemKey);
            const collectionKeyUpper = actualCollectionKey
              ? actualCollectionKey.toUpperCase()
              : collectionItemKey.toUpperCase();
            const finalAmount = finalCollections?.[collectionKeyUpper] || 0;

            // update admin with final result
            const finalAction = quantity >= 0 ? "gave" : "removed";
            await interaction.editReply({
              content: `✅ Successfully ${finalAction} ${absQuantity} ${itemEmoji} **${collectionDisplayName} Collection Progress** ${quantity >= 0 ? "to" : "from"} ${targetName}. (Current: ${finalAmount.toLocaleString()})`,
            });
          } catch (asyncError) {
            console.error(
              `[GiveCommand] Error in async collection processing:`,
              asyncError
            );
            const errorAction = quantity >= 0 ? "Gave" : "Removed";
            await interaction.editReply({
              content: `⚠️ ${errorAction} ${absQuantity} ${itemEmoji} **${collectionDisplayName} Collection Progress** ${quantity >= 0 ? "to" : "from"} ${targetName}, but there may have been issues with processing.`,
            });
          }
        })();
      } catch (error) {
        console.error(
          `[GiveCommand] Error giving collection progress ${collectionItemKey} to ${targetUserId}:`,
          error
        );
        await interaction.reply({
          content: `An error occurred while giving collection progress: ${error.message}`,
          flags: [MessageFlags.Ephemeral],
        });
      }
    } else {
      // Handle item giving
      const allItems = configManager.getAllItems();
      const itemEntry = Object.entries(allItems).find(
        ([, data]) =>
          data.name.toLowerCase() === itemNameOrCoinOrSkillExp.toLowerCase()
      );

      if (!itemEntry) {
        return interaction.reply({
          content: `Item "${itemNameOrCoinOrSkillExp}" not found. Please use the autocomplete suggestions.`,
        });
      }

      const [itemKey, itemDetails] = itemEntry;

      if (itemDetails.type === "MINION") {
        if (quantity <= 0) {
          return interaction.reply({
            content:
              "Cannot give negative or zero quantities of minions. Use other methods to remove minions.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        if (quantity > 1) {
          return interaction.reply({
            content: "You can only give minions one at a time.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        try {
          const targetCharacter = await getPlayerData(targetUserId);
          if (!targetCharacter) {
            return interaction.reply({
              content: `Could not find or load character data for ${targetName}.`,
              flags: [MessageFlags.Ephemeral],
            });
          }

          if (tier > 1) {
            if (!itemDetails.tiers || !itemDetails.tiers[tier]) {
              return interaction.reply({
                content: `Invalid tier ${tier} for ${itemDetails.name}. This minion only has tiers 1-${itemDetails.tiers?.length || 1}.`,
                flags: [MessageFlags.Ephemeral],
              });
            }
          }

          const newMinion = {
            id: uuidv4(),
            itemKey: itemKey,
            tier: tier,
            resourcesStored: {},
            lastCollectionTimestamp: null,
            createdAt: Date.now(),
          };
          if (!targetCharacter.minionStorage)
            targetCharacter.minionStorage = [];
          targetCharacter.minionStorage.push(newMinion);

          // Save minion data atomically to avoid conflicts
          await updatePlayerJsonFields(targetUserId, {
            minionStorage: targetCharacter.minionStorage,
          });

          // Send notification to recipient's personal channel
          await sendRecipientNotification(
            interaction,
            targetUserId,
            targetName,
            itemDetails.emoji || "❓",
            1,
            `${itemDetails.name} [T${tier}]`
          );

          await interaction.reply({
            content: `Successfully gave 1x ${itemDetails.emoji || "❓"} **${itemDetails.name} [T${tier}]** to ${targetName}.`,
            flags: [MessageFlags.Ephemeral],
          });
        } catch (error) {
          console.error(
            `[GiveCommand] Error giving minion ${itemKey} to ${targetUserId}:`,
            error
          );
          await interaction.reply({
            content: `An error occurred while giving the minion: ${error.message}`,
            flags: [MessageFlags.Ephemeral],
          });
        }
        return;
      }

      if (itemDetails.type === "PET") {
        if (quantity <= 0) {
          return interaction.reply({
            content:
              "Cannot give negative or zero quantities of pets. Use other methods to remove pets.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        if (quantity > 1) {
          return interaction.reply({
            content: "You can only give pets one at a time.",
            flags: [MessageFlags.Ephemeral],
          });
        }
        try {
          const targetCharacter = await getPlayerData(targetUserId);
          if (!targetCharacter) {
            return interaction.reply({
              content: `Could not find or load character data for ${targetName}.`,
              flags: [MessageFlags.Ephemeral],
            });
          }

          const newPet = createPetInstance(itemKey, rarity, 0);
          if (!newPet) {
            return interaction.reply({
              content: `Failed to create pet instance for ${itemDetails.name}.`,
              flags: [MessageFlags.Ephemeral],
            });
          }

          if (!targetCharacter.pets) targetCharacter.pets = [];
          targetCharacter.pets.push(newPet);

          // Save pets data atomically to avoid conflicts
          await updatePlayerJsonFields(targetUserId, {
            pets: targetCharacter.pets,
          });

          // Send notification to recipient's personal channel
          await sendRecipientNotification(
            interaction,
            targetUserId,
            targetName,
            itemDetails.emoji || "🐾",
            1,
            `${rarity.charAt(0) + rarity.slice(1).toLowerCase()} ${itemDetails.name}`
          );

          await interaction.reply({
            content: `Successfully gave 1x ${itemDetails.emoji || "🐾"} **${rarity.charAt(0) + rarity.slice(1).toLowerCase()} ${itemDetails.name}** to ${targetName}.`,
            flags: [MessageFlags.Ephemeral],
          });
        } catch (error) {
          console.error(
            `[GiveCommand] Error giving pet ${itemKey} to ${targetUserId}:`,
            error
          );
          await interaction.reply({
            content: `An error occurred while giving the pet: ${error.message}`,
            flags: [MessageFlags.Ephemeral],
          });
        }
        return;
      }

      // Check for negative quantities on unique items
      if (quantity < 0 && itemDetails.unique) {
        return interaction.reply({
          content: `Cannot remove unique items (${itemDetails.name}) using negative quantities. Unique items must be removed individually by ID.`,
          flags: [MessageFlags.Ephemeral],
        });
      }

      const itemsToChange = [];
      const equipmentToAdd = [];
      const accessoriesToAdd = [];

      if (itemDetails.unique && quantity > 0) {
        if (itemDetails.type === "ACCESSORY") {
          for (let i = 0; i < quantity; i++) {
            accessoriesToAdd.push({ itemKey: itemKey });
          }
        } else {
          for (let i = 0; i < quantity; i++) {
            equipmentToAdd.push({ itemKey: itemKey });
          }
        }
      } else {
        itemsToChange.push({ itemKey: itemKey, amount: quantity });
      }

      try {
        await updateInventoryAtomically(
          targetUserId,
          0,
          itemsToChange,
          equipmentToAdd,
          [],
          0,
          [],
          null,
          null,
          false,
          accessoriesToAdd
        );

        // Send notification to recipient's personal channel
        await sendRecipientNotification(
          interaction,
          targetUserId,
          targetName,
          itemDetails.emoji || "❓",
          quantity,
          itemDetails.name
        );

        const action = quantity >= 0 ? "gave" : "removed";
        const absQuantity = Math.abs(quantity);
        await interaction.reply({
          content: `Successfully ${action} ${absQuantity}x ${itemDetails.emoji || "❓"} **${itemDetails.name}** ${quantity >= 0 ? "to" : "from"} ${targetName}.`,
          flags: [MessageFlags.Ephemeral],
        });
      } catch (error) {
        console.error(
          "[GiveCommand] Error updating item inventory atomically:",
          error
        );

        if (error.message && error.message.includes("Player not found")) {
          await interaction.reply({
            content: `Player ${targetName} does not have a character or could not be updated.`,
            flags: [MessageFlags.Ephemeral],
          });
        } else if (error.message && error.message.includes("Insufficient")) {
          await interaction.reply({
            content: `${targetName} does not have enough ${itemDetails.name} to remove ${Math.abs(quantity)}.`,
            flags: [MessageFlags.Ephemeral],
          });
        } else {
          const action = quantity >= 0 ? "giving" : "removing";
          await interaction.reply({
            content: `An error occurred while ${action} the item.`,
            flags: [MessageFlags.Ephemeral],
          });
        }
      }
    }
  },
};
