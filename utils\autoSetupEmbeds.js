const config = require("../config.json");
const { getSetupEmbed } = require("./channelManager");

/**
 * Check and post setup embeds in configured channels on bot startup
 * @param {Client} client - Discord client
 */
async function ensureSetupEmbedsExist(client) {
  try {
    const setupChannelId = config.channelCreation?.setupChannelId;

    if (!setupChannelId) {
      console.log("[AutoSetup] No setup channel configured");
      return;
    }

    const QUIET_BOOT = process.env.QUIET_BOOT === "true";
    if (!QUIET_BOOT)
      console.log(`[AutoSetup] Checking setup channel for setup embed...`);

    try {
      const guild = client.guilds.cache.get(config.guildId);
      if (!guild) {
        console.warn(`[AutoSetup] Guild ${config.guildId} not found`);
        return;
      }

      const channel =
        guild.channels.cache.get(setupChannelId) ||
        (await guild.channels.fetch(setupChannelId));

      if (!channel || !channel.isTextBased()) {
        console.warn(
          `[AutoSetup] Setup channel ${setupChannelId} not found or not a text channel`
        );
        return;
      }

      // check if setup embed already exists
      const messages = await channel.messages.fetch({ limit: 50 });
      const existingSetupMessage = messages.find(
        (msg) =>
          msg.author.id === client.user.id &&
          msg.components.length > 0 &&
          msg.components.some((row) =>
            row.components.some(
              (component) => component.customId === "create_character_setup"
            )
          )
      );

      if (existingSetupMessage) {
        if (!QUIET_BOOT)
          console.log(
            `[AutoSetup] Setup embed already exists in #${channel.name} (${setupChannelId})`
          );
        return;
      }

      // post the setup embed
      const { embed, row } = getSetupEmbed();
      await channel.send({
        embeds: [embed],
        components: [row],
      });

      console.log(
        `[AutoSetup] Posted setup embed in #${channel.name} (${setupChannelId})`
      );
    } catch (channelError) {
      console.error(
        `[AutoSetup] Error handling setup channel ${setupChannelId}:`,
        channelError.message
      );
    }

    if (!QUIET_BOOT) console.log("[AutoSetup] Setup embed check completed");
  } catch (error) {
    console.error("[AutoSetup] Error during setup embed check:", error);
  }
}

module.exports = {
  ensureSetupEmbedsExist,
};
