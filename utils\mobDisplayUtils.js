const configManager = require("./configManager");

/**
 * Expands defeated mobs data from either optimized or detailed format to detailed format for display
 * @param {Object} defeatedMobs - Either optimized format {mobKey: count} or detailed format {key: {name, emoji, count}}
 * @returns {Object} - Always returns detailed format {key: {name, emoji, count}}
 */
function expandDefeatedMobsForDisplay(defeatedMobs) {
  if (!defeatedMobs || typeof defeatedMobs !== "object") {
    return {};
  }

  const expanded = {};

  for (const [key, value] of Object.entries(defeatedMobs)) {
    if (typeof value === "number") {
      // Optimized format: key is mobKey, value is count
      const mobData = configManager.getMob(key);
      if (mobData) {
        const displayKey = `${mobData.name}|${mobData.emoji || ""}`;
        expanded[displayKey] = {
          name: mobData.name,
          emoji: mobData.emoji || "",
          count: value,
        };
      } else {
        // Fallback for missing mob data
        expanded[key] = {
          name: key,
          emoji: "❓",
          count: value,
        };
      }
    } else if (
      typeof value === "object" &&
      value.name &&
      typeof value.count === "number"
    ) {
      // Already detailed format, pass through
      expanded[key] = value;
    }
  }

  return expanded;
}

/**
 * Determines if defeated mobs data should use optimized storage
 * @param {Object} defeatedMobs - Current defeated mobs tracking object
 * @returns {boolean} - True if can be optimized (single mob type, no special cases)
 */
function canOptimizeDefeatedMobsStorage(defeatedMobs) {
  if (!defeatedMobs || typeof defeatedMobs !== "object") {
    return true; // Empty case can be optimized
  }

  const mobKeys = Object.keys(defeatedMobs);

  // If more than one mob type, keep detailed format
  if (mobKeys.length > 1) {
    return false;
  }

  // Check if any mob names suggest they're special (bosses, mini-bosses, etc.)
  for (const [_key, mobData] of Object.entries(defeatedMobs)) {
    if (mobData.name) {
      const name = mobData.name.toLowerCase();
      // Keep detailed format for special mobs
      if (
        name.includes("boss") ||
        name.includes("horror") ||
        name.includes("seraph") ||
        name.includes("revenant") ||
        name.includes("tarantula") ||
        name.includes("sven")
      ) {
        return false;
      }
    }
  }

  return true; // Single regular mob, can optimize
}

/**
 * Creates a lookup map for all mobs
 * @param {Object} allMobs - Object containing all mob data
 * @returns {Map} - Map with mob names as keys and mob IDs as values
 */
function createMobLookupMap(allMobs) {
  const mobLookup = new Map();
  for (const [key, mob] of Object.entries(allMobs)) {
    mobLookup.set(mob.name, key);
  }
  return mobLookup;
}

/**
 * Converts defeated mobs from detailed format to optimized format when appropriate
 * @param {Object} defeatedMobs - Detailed format defeated mobs
 * @returns {Object} - Optimized format if appropriate, otherwise original format
 */
function optimizeDefeatedMobsForStorage(defeatedMobs) {
  if (!canOptimizeDefeatedMobsStorage(defeatedMobs)) {
    return defeatedMobs; // Keep detailed format
  }

  if (!defeatedMobs || Object.keys(defeatedMobs).length === 0) {
    return {}; // Empty case
  }

  const allMobs = configManager.getAllMobs();
  const mobLookup = createMobLookupMap(allMobs);
  const optimized = {};

  for (const [_key, mobData] of Object.entries(defeatedMobs)) {
    const mobKey =
      mobLookup.get(mobData.name) ||
      mobData.name.toUpperCase().replace(/\s+/g, "_");
    optimized[mobKey] = mobData.count;
  }

  return optimized;
}

module.exports = {
  expandDefeatedMobsForDisplay,
  canOptimizeDefeatedMobsStorage,
  optimizeDefeatedMobsForStorage,
};
