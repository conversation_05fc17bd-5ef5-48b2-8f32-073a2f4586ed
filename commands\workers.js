const { SlashCommandBuilder, EmbedBuilder } = require("discord.js");
const { EMBED_COLORS } = require("../gameConfig");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("workers")
    .setDescription("🔧 Admin: View worker bot status and statistics"),

  async execute(interaction) {
    // Check if user has admin permissions
    const { getPlayerData } = require("../utils/playerDataManager");

    try {
      const character = await getPlayerData(interaction.user.id);

      if (!character) {
        return interaction.reply({
          content:
            "❌ You need to create a character first! Use the setup channel.",
          ephemeral: true,
        });
      }

      // Check if user is admin (you can modify this check as needed)
      if (character.rank !== "OWNER" && character.rank !== "ADMIN") {
        return interaction.reply({
          content: "❌ You don't have permission to use this command.",
          ephemeral: true,
        });
      }

      // Get worker stats
      const { workerManager } = require("../utils/workerManager");
      const stats = workerManager.getWorkerStats();

      // Create embed
      const embed = new EmbedBuilder()
        .setTitle("🤖 Worker Bot Status")
        .setColor(EMBED_COLORS.BLUE)
        .setTimestamp();

      // Add overall stats
      embed.addFields({
        name: "📊 Overview",
        value: [
          `**Total Workers:** ${stats.totalWorkers}`,
          `**Healthy Workers:** ${stats.healthyWorkers}`,
          `**Active Users:** ${stats.totalActiveUsers}`,
          `**Load Distribution:** ${stats.healthyWorkers > 0 ? Math.round((stats.totalActiveUsers / stats.healthyWorkers) * 100) / 100 : 0} users/worker avg`,
          `**Resumption System:** Self-Resuming ✅`,
        ].join("\n"),
        inline: false,
      });

      // Add individual worker stats
      if (Object.keys(stats.workers).length === 0) {
        embed.addFields({
          name: "⚠️ No Workers Connected",
          value:
            "No worker bots are currently registered. Make sure to start the worker bots first.",
          inline: false,
        });
      } else {
        for (const [workerId, workerStats] of Object.entries(stats.workers)) {
          const healthIcon = workerStats.healthy ? "🟢" : "🔴";
          const lastSeen = workerStats.healthy
            ? "Just now"
            : `${Math.floor((Date.now() - workerStats.lastHeartbeat) / 1000)}s ago`;

          embed.addFields({
            name: `${healthIcon} Worker ${workerId}`,
            value: [
              `**Status:** ${workerStats.status}`,
              `**Active Users:** ${workerStats.activeUsers}`,
              `**Total Actions:** ${workerStats.totalActionsHandled}`,
              `**Last Seen:** ${lastSeen}`,
              `**Capabilities:** ${workerStats.capabilities.join(", ")}`,
            ].join("\n"),
            inline: true,
          });
        }
      }

      // Add performance tips
      if (stats.totalActiveUsers > 0) {
        const avgLoad =
          stats.totalActiveUsers / Math.max(stats.healthyWorkers, 1);
        let performanceNote = "";

        if (avgLoad > 40) {
          performanceNote =
            "⚠️ **High Load**: Consider adding more worker bots";
        } else if (avgLoad > 25) {
          performanceNote = "⚡ **Moderate Load**: Performance is good";
        } else {
          performanceNote = "✅ **Low Load**: Excellent performance";
        }

        embed.addFields({
          name: "📈 Performance",
          value: performanceNote,
          inline: false,
        });
      }

      // Add usage instructions
      embed.setFooter({
        text: "💡 Tip: Each worker can handle ~50 concurrent actions. Rate limits are per-bot (50 API calls/sec each).",
      });

      await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
      console.error("[Workers Command] Error:", error);

      await interaction.reply({
        content: "❌ An error occurred while fetching worker statistics.",
        ephemeral: true,
      });
    }
  },
};
