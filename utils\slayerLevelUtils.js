const { EMBED_COLORS } = require("../gameConfig.js");
// Slayer Level Calculation System
// Define the CUMULATIVE experience required to reach each slayer level
// Index 0 is unused (level 0), index 1 corresponds to Level 1, etc.
// These are TOTAL XP amounts required to reach each level

const slayerXpTable = [
  0, // Level 0 (Unused)
  15, // Level 1 - 15 XP total
  50, // Level 2 - 50 XP total
  200, // Level 3 - 200 XP total
  1000, // Level 4 - 1,000 XP total
  5000, // Level 5 - 5,000 XP total
  20000, // Level 6 - 20,000 XP total
  100000, // Level 7 - 100,000 XP total
  400000, // Level 8 - 400,000 XP total
  1000000, // Level 9 - 1,000,000 XP total
];

// Slayer emoji mapping
const SLAYER_EMOJIS = {
  zombie: "<:revenant_horror:1389646540460658970>",
  spider: "<:mob_spider:1370526342927618078>",
};

const { EmbedBuilder } = require("discord.js");
const { getStatEmoji } = require("./statUtils");

/**
 * Gets the experience required to reach the NEXT slayer level.
 * @param {number} currentLevel - The current completed level (e.g., 0, 1, 2...).
 * @returns {number} Experience required for the *next* level up, or Infinity if max level.
 */
function getRequiredSlayerExp(currentLevel) {
  const nextLevelIndex = currentLevel + 1;
  if (nextLevelIndex >= slayerXpTable.length) {
    return Infinity; // Max level reached
  }
  return slayerXpTable[nextLevelIndex] || Infinity;
}

/**
 * Calculates the player's current slayer level and progress towards the next level based on total accumulated slayer experience.
 * @param {number} totalSlayerExp - The total accumulated slayer experience points.
 * @returns {object} An object containing { level: number, currentLevelExp: number, requiredExpForNextLevel: number }.
 *                  level: The current completed level.
 *                  currentLevelExp: Experience points accumulated *into* the current level (progress towards next).
 *                  requiredExpForNextLevel: Experience points needed to complete the current level and reach the next level.
 */
function getSlayerLevelFromExp(totalSlayerExp) {
  if (totalSlayerExp < 0) totalSlayerExp = 0;

  let currentLevel = 0;

  // Find the highest level where totalSlayerExp >= slayerXpTable[level]
  for (let level = 1; level < slayerXpTable.length; level++) {
    const expRequiredForThisLevel = slayerXpTable[level];
    if (expRequiredForThisLevel === undefined) break; // Stop if table ends

    if (totalSlayerExp >= expRequiredForThisLevel) {
      currentLevel = level;
    } else {
      break; // Not enough XP for this level
    }
  }

  // Calculate progress into the current level
  let currentLevelExp = 0;
  let requiredExpForNextLevel = Infinity;

  if (currentLevel === 0) {
    // Level 0: progress is total XP, requirement is for level 1
    currentLevelExp = totalSlayerExp;
    requiredExpForNextLevel = slayerXpTable[1] || Infinity;
  } else {
    // Calculate XP into current level (progress towards next level)
    const expRequiredForCurrentLevel = slayerXpTable[currentLevel] || 0;
    currentLevelExp = totalSlayerExp - expRequiredForCurrentLevel;

    // Get total cumulative XP requirement for next level
    const totalExpForNextLevel = slayerXpTable[currentLevel + 1];

    if (totalExpForNextLevel !== undefined) {
      // Calculate XP needed to complete the current level (for progress bar display)
      requiredExpForNextLevel =
        totalExpForNextLevel - expRequiredForCurrentLevel;
    } else {
      // Max level reached
      requiredExpForNextLevel = Infinity;
    }
  }

  return {
    level: currentLevel, // The completed level number
    currentLevelExp: currentLevelExp, // XP progress into the current level (towards next)
    requiredExpForNextLevel: requiredExpForNextLevel, // XP needed to complete the current level and reach the next level
  };
}

/**
 * Get slayer levels for both zombie and spider slayers from player's slayer XP data
 * @param {object} slayerXpData - Object containing slayer XP data (e.g., { zombie: 150, spider: 75 })
 * @returns {object} Object containing level information for each slayer type
 */
function getAllSlayerLevels(slayerXpData) {
  const result = {};

  if (!slayerXpData || typeof slayerXpData !== "object") {
    return {
      zombie: getSlayerLevelFromExp(0),
      spider: getSlayerLevelFromExp(0),
    };
  }

  // Calculate zombie slayer level
  const zombieXp = slayerXpData.zombie || 0;
  result.zombie = getSlayerLevelFromExp(zombieXp);

  // Calculate spider slayer level
  const spiderXp = slayerXpData.spider || 0;
  result.spider = getSlayerLevelFromExp(spiderXp);

  return result;
}

/**
 * Get a formatted string representation of a slayer level
 * @param {number} totalSlayerExp - Total slayer XP
 * @param {string} slayerType - Type of slayer (zombie/spider)
 * @returns {string} Formatted level string (e.g., "Zombie Slayer 3")
 */
function formatSlayerLevel(totalSlayerExp, slayerType) {
  const levelInfo = getSlayerLevelFromExp(totalSlayerExp);
  const capitalizedType =
    slayerType.charAt(0).toUpperCase() + slayerType.slice(1);
  return `${capitalizedType} Slayer ${levelInfo.level}`;
}

function getSlayerStatRewards(slayerType) {
  // Use the centralized slayer rewards system
  const { getStatRewardsForLevel } = require("./slayerRewards");
  const rewards = {};

  // Build rewards map for this slayer type (levels 1-9)
  for (let level = 1; level <= 9; level++) {
    const levelRewards = getStatRewardsForLevel(slayerType, level);
    if (levelRewards.length > 0) {
      rewards[level] = levelRewards;
    }
  }

  return rewards;
}

/**
 * Check for slayer level ups and create notification embeds
 * @param {string} userId - Discord user ID
 * @param {string} slayerType - Type of slayer (zombie/spider)
 * @param {number} oldSlayerXp - Previous slayer XP amount
 * @param {number} newSlayerXp - New slayer XP amount after gain
 * @returns {Array<EmbedBuilder>|null} Array of level up embeds or null if no level ups
 */
async function checkSlayerLevelUp(
  userId,
  slayerType,
  oldSlayerXp,
  newSlayerXp
) {
  // Calculate old and new levels
  const oldLevelInfo = getSlayerLevelFromExp(oldSlayerXp);
  const newLevelInfo = getSlayerLevelFromExp(newSlayerXp);

  const oldLevel = oldLevelInfo.level;
  const newLevel = newLevelInfo.level;

  // Check if player leveled up
  if (newLevel <= oldLevel) {
    return null; // No level up occurred
  }

  const levelUpEmbeds = [];
  const processedLevels = new Set();

  // Load items data to check for unlocked recipes
  const configManager = require("./configManager");
  const itemsData = configManager.getAllItems();

  // Create level up embeds for each level gained
  for (let level = oldLevel + 1; level <= newLevel; level++) {
    // Skip if we already processed this level (prevents duplicates)
    if (processedLevels.has(level)) {
      continue;
    }

    processedLevels.add(level);

    // Get slayer emoji and capitalize type
    const slayerEmoji = SLAYER_EMOJIS[slayerType.toLowerCase()] || "⭐";
    const capitalizedType =
      slayerType.charAt(0).toUpperCase() + slayerType.slice(1);

    // Create the level up embed (following the same format as skill level ups)
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GOLD)
      .setTitle(`${slayerEmoji} Slayer Level Up!`)
      .setDescription(
        `<@${userId}> You are now **${capitalizedType} Slayer ${level}!**`
      );

    // Note: Disblock XP for slayer levels is handled by the reconciliation system in checkAndNotifyDisblockXP

    // Check for unlocked recipes at this level
    const unlockedRecipes = [];
    for (const [, itemData] of Object.entries(itemsData)) {
      if (itemData.craftingRequirements?.slayers?.[slayerType] === level) {
        unlockedRecipes.push({
          name: itemData.name,
          emoji: itemData.emoji || "❓",
          rarity: itemData.rarity?.name || "Common",
        });
      }
    }

    // Check for item rewards at specific levels
    const itemRewards = [];
    if (slayerType === "zombie" && level === 5) {
      itemRewards.push({
        itemKey: "BEHEADED_HORROR",
        amount: 1,
        name: "Beheaded Horror",
        emoji: itemsData.BEHEADED_HORROR?.emoji || "❓",
      });
    }
    if (slayerType === "zombie" && level === 6) {
      itemRewards.push({
        itemKey: "REVENANT_CATALYST",
        amount: 1,
        name: "Revenant Catalyst",
        emoji: itemsData.REVENANT_CATALYST?.emoji || "❓",
      });
    }

    // Check for stat rewards at specific levels
    const statRewards = [];
    const statRewardsMap = getSlayerStatRewards(slayerType);
    if (statRewardsMap[level]) {
      statRewards.push(...statRewardsMap[level]);
    }

    // Give item rewards to player
    if (itemRewards.length > 0) {
      try {
        const { updateInventoryAtomically } = require("./inventory");

        for (const reward of itemRewards) {
          await updateInventoryAtomically(userId, [
            {
              type: "add",
              itemKey: reward.itemKey,
              amount: reward.amount,
            },
          ]);
        }

        console.log(
          `[SlayerLevelUtils] Gave ${itemRewards.length} item reward(s) for ${slayerType} slayer level ${level}`
        );
      } catch (error) {
        console.error(
          `[SlayerLevelUtils] Error giving item rewards for ${slayerType} slayer level ${level}:`,
          error
        );
      }
    }

    // Apply stat rewards to player
    if (statRewards.length > 0) {
      try {
        const {
          getPlayerData,
          savePlayerData,
        } = require("./playerDataManager");
        const playerData = await getPlayerData(userId);

        if (playerData && playerData.stats) {
          // Apply each stat reward
          for (const reward of statRewards) {
            // Initialize stat if it doesn't exist
            if (!playerData.stats[reward.stat]) {
              playerData.stats[reward.stat] = {
                base: 0,
                fromLevels: 0,
                fromSlayers: 0,
                fromEquipment: 0,
                fromPet: 0,
                fromSetBonus: 0,
                fromAccessories: 0,
                fromMagicalPower: 0,
              };
            }

            // Initialize fromSlayers if it doesn't exist
            if (!playerData.stats[reward.stat].fromSlayers) {
              playerData.stats[reward.stat].fromSlayers = 0;
            }

            // Add the stat bonus
            playerData.stats[reward.stat].fromSlayers += reward.value;
          }

          // Save the updated stats
          await savePlayerData(userId, { stats: playerData.stats }, ["stats"]);

          console.log(
            `[SlayerLevelUtils] Applied ${statRewards.length} stat reward(s) for ${slayerType} slayer level ${level}`
          );
        }
      } catch (statError) {
        console.error(
          `[SlayerLevelUtils] Error applying stat rewards for ${slayerType} slayer level ${level}:`,
          statError
        );
      }
    }

    // Add rewards section if there are unlocked recipes, item rewards, or stat rewards
    const rewardsText = [];

    if (unlockedRecipes.length > 0) {
      rewardsText.push(
        ...unlockedRecipes.map(
          (recipe) => `${recipe.emoji} **${recipe.name}** Recipe`
        )
      );
    }

    if (itemRewards.length > 0) {
      rewardsText.push(
        ...itemRewards.map(
          (reward) => `${reward.emoji} **${reward.name}** x${reward.amount}`
        )
      );
    }

    if (statRewards.length > 0) {
      rewardsText.push(
        ...statRewards.map((reward) => {
          const emoji = getStatEmoji(reward.stat);
          const statName = reward.stat
            .replace("_", " ")
            .toLowerCase()
            .replace(/\b\w/g, (l) => l.toUpperCase());
          return `${emoji} **+${reward.value}** ${statName}`;
        })
      );
    }

    if (rewardsText.length > 0) {
      embed.addFields({
        name: "Rewards",
        value: rewardsText.join("\n"),
        inline: false,
      });
    }

    levelUpEmbeds.push(embed);
  }

  return levelUpEmbeds.length > 0 ? levelUpEmbeds : null;
}

module.exports = {
  slayerXpTable,
  getRequiredSlayerExp,
  getSlayerLevelFromExp,
  getAllSlayerLevels,
  formatSlayerLevel,
  checkSlayerLevelUp,
  getSlayerStatRewards,
};
