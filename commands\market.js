const {
  <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  MessageFlags,
} = require("discord.js");
const { dbGet, dbAll, dbRunQueued } = require("../utils/dbUtils");
const { getPlayerData } = require("../utils/playerDataManager");
const { getInventory } = require("../utils/inventory");
const {
  executeInstantBuyTransaction,
  executeInstantSellTransaction,
  withMarketLock,
  createConfirmationToken,
  validateAndConsumeToken,
  checkRateLimit,
  createUserItemLock,
} = require("../utils/marketTransactions");
const {
  processNewSellOrder,
  processNewBuyOrder,
} = require("../utils/orderMatching");
const configManager = require("../utils/configManager");
const {
  EMBED_COLORS,
  CURRENCY,
  MARKET_EMOJI,
  MARKET_LIMITS,
} = require("../gameConfig");
const {
  parseShorthandAmount,
  formatPrice,
  parsePrice,
} = require("../utils/formatUtils");
const { checkRankPermission } = require("../utils/permissionUtils");
const { wrapText } = require("../utils/textUtils");
const { updateUserActiveChannel } = require("../utils/userTracking");
const { isBoosterCookieActive } = require("../utils/boosterCookieManager");
const {
  fuzzySearchItems,
  isLikelyExactMatch,
} = require("../utils/fuzzySearch");

// map to track market interaction message IDs per user
const marketInteractionMap = new Map();
// map to track active collectors per user
const activeCollectors = new Map();

module.exports = {
  data: new SlashCommandBuilder()
    .setName("market")
    .setDescription("Browse and trade items on the Player Market")
    .addStringOption((option) =>
      option
        .setName("item")
        .setDescription("Search for a specific item (optional)")
        .setRequired(false)
    ),

  async execute(interaction) {
    await interaction.deferReply();

    // Track user's active channel for notifications
    await updateUserActiveChannel(interaction.user.id, interaction.channel.id);

    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      return interaction.editReply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
      });
    }

    if (!checkRankPermission(character, "MEMBER")) {
      return interaction.editReply({
        content:
          "You don't have permission (Rank Required: MEMBER) to access the market.",
      });
    }

    // check if player is in The Hub or has active booster cookie
    const hasActiveBoosterCookie = isBoosterCookieActive(character);
    const isInHub = character.current_region === "the_hub";

    if (!isInHub && !hasActiveBoosterCookie) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Market Not Available")
        .setDescription(
          "The market is only accessible from **The Hub**.\n\n💡 **Tip:** You can access the market from anywhere with an active Booster Cookie!"
        );

      const response = await interaction.editReply({ embeds: [errorEmbed] });
      setTimeout(() => {
        response.delete().catch(() => {});
      }, 10000);
      return;
    }

    // check if an item search term was provided
    const itemSearchTerm = interaction.options.getString("item");

    if (itemSearchTerm) {
      // directly show search results for the specified item
      await showSearchResults(interaction, character, 0, itemSearchTerm);
    } else {
      // show main market menu
      await showMarketMenu(interaction, character);
    }
  },

  async handleModalSubmit(interaction) {
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      return interaction.reply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // check if player is in The Hub or has active booster cookie
    const hasActiveBoosterCookie = isBoosterCookieActive(character);
    const isInHub = character.current_region === "the_hub";

    if (!isInHub && !hasActiveBoosterCookie) {
      const errorEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.ERROR)
        .setTitle("Market Not Available")
        .setDescription(
          "The market is only accessible from **The Hub**.\n\n💡 **Tip:** You can access the market from anywhere with an active Booster Cookie!"
        );

      const response = await interaction.reply({
        embeds: [errorEmbed],
        flags: [MessageFlags.Ephemeral],
      });
      setTimeout(() => {
        response.delete().catch(() => {});
      }, 10000);
      return;
    }

    try {
      if (interaction.customId.startsWith("instant_buy_modal:")) {
        await handleInstantBuyModal(interaction, character);
      } else if (interaction.customId.startsWith("instant_sell_modal:")) {
        await handleInstantSellModal(interaction, character);
      } else if (interaction.customId === "market_search_modal") {
        await handleSearchModal(interaction, character);
      } else if (interaction.customId.startsWith("buy_order_modal:")) {
        await handleBuyOrderModal(interaction, character);
      } else if (interaction.customId.startsWith("sell_order_modal:")) {
        await handleSellOrderModal(interaction, character);
      } else if (interaction.customId === "create_buy_order_modal") {
        await handleCreateBuyOrderModal(interaction, character);
      } else if (interaction.customId.startsWith("create_sell_order_modal:")) {
        await handleCreateSellOrderModal(interaction, character);
      } else {
        return interaction.reply({
          content: "Unknown market modal interaction.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    } catch (error) {
      console.error("[Market Modal] Error handling modal submission:", error);
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: "An error occurred while processing your request.",
          flags: [MessageFlags.Ephemeral],
        });
      }
    }
  },
};

/**
 * shows the main market menu with buy/sell options
 */
async function showMarketMenu(interaction, character) {
  const userId = interaction.user.id;

  // get player's orders to display
  const sellOrders = await dbAll(
    "SELECT * FROM market_sell_orders WHERE seller_id = ? AND quantity > 0 ORDER BY created_at DESC LIMIT 3",
    [userId]
  );

  const buyOrders = await dbAll(
    "SELECT * FROM market_buy_orders WHERE buyer_id = ? AND quantity > 0 ORDER BY created_at DESC LIMIT 3",
    [userId]
  );

  const allItems = configManager.getAllItems();

  // build description with user orders
  let description = wrapText(
    "Welcome to the Player Market! Here you can trade goods with other players easily!"
  );

  description += `\n\n**Your Orders:** (${sellOrders.length}/${MARKET_LIMITS.MAX_SELL_ORDERS} sell, ${buyOrders.length}/${MARKET_LIMITS.MAX_BUY_ORDERS} buy)\n`;

  const hasOrders = sellOrders.length > 0 || buyOrders.length > 0;

  if (!hasOrders) {
    description += "No active orders";
  } else {
    // show sell orders
    if (sellOrders.length > 0) {
      description += "🔴 **Selling:**\n";
      sellOrders.forEach((order) => {
        const itemData = allItems[order.item_key];
        const itemEmoji = itemData?.emoji || "📦";
        const itemName = itemData?.name || order.item_key;
        const filled =
          (order.original_quantity || order.quantity) - order.quantity;
        const total = order.original_quantity || order.quantity;
        const progressText = filled > 0 ? ` (**${filled}/${total}**)` : "";
        description += `${itemEmoji} **${itemName}** ${order.quantity.toLocaleString()}x @ ${formatPrice(order.price_per_unit)} coins${progressText}\n`;
      });
    }

    // show buy orders
    if (buyOrders.length > 0) {
      description += "🟢 **Buying:**\n";
      buyOrders.forEach((order) => {
        const itemData = allItems[order.item_key];
        const itemEmoji = itemData?.emoji || "📦";
        const itemName = itemData?.name || order.item_key;
        const filled =
          (order.original_quantity || order.quantity) - order.quantity;
        const total = order.original_quantity || order.quantity;
        const progressText = filled > 0 ? ` (**${filled}/${total}**)` : "";
        description += `${itemEmoji} **${itemName}** ${order.quantity.toLocaleString()}x @ ${formatPrice(order.price_per_unit)} coins${progressText}\n`;
      });
    }
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${MARKET_EMOJI} Player Market`)
    .setDescription(description)
    .setFooter({
      text: `Your Purse: ${formatPrice(character.coins || 0)} coins`,
    });

  // create the button layout with all options
  const topRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_search")
      .setLabel("Search")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("market_orders")
      .setLabel("Your Orders")
      .setStyle(ButtonStyle.Secondary)
  );

  const bottomRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_create_buy_order")
      .setLabel("Buy Order")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("market_create_sell_order")
      .setLabel("Sell Order")
      .setStyle(ButtonStyle.Primary)
  );

  const message = await interaction.editReply({
    embeds: [embed],
    components: [topRow, bottomRow],
  });

  // store message ID for cleanup
  marketInteractionMap.set(interaction.user.id, message.id);

  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  // set up collector for button interactions
  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id && i.customId.startsWith("market_"),
    time: 300000, // 5 minutes
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      // check if interaction is already handled
      if (i.replied || i.deferred) {
        return;
      }

      // check interaction age
      const interactionAge = Date.now() - i.createdTimestamp;
      if (interactionAge > 14 * 60 * 1000) {
        return;
      }

      await handleMarketInteraction(i, character);
    } catch (error) {
      console.error("[Market Main] Error handling interaction:", error);
    }
  });

  collector.on("end", () => {
    marketInteractionMap.delete(interaction.user.id);
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * sets up a collector for market button interactions on any message
 * used for confirmation screens and other market interactions
 */
async function setupMarketCollector(interaction, character) {
  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  // get the message from the interaction
  const message = await interaction.fetchReply();

  // set up collector for button interactions
  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id && i.customId.startsWith("market_"),
    time: 900000, // 15 minutes (longer timeout to prevent expiration)
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      // check if interaction is already handled
      if (i.replied || i.deferred) {
        return;
      }

      // check interaction age
      const interactionAge = Date.now() - i.createdTimestamp;
      if (interactionAge > 14 * 60 * 1000) {
        return;
      }

      await handleMarketInteraction(i, character);
    } catch (error) {
      console.error("[Market Setup] Error handling interaction:", error);
    }
  });

  collector.on("end", () => {
    marketInteractionMap.delete(interaction.user.id);
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * handles button interactions from the market menu
 */
async function handleMarketInteraction(interaction, character) {
  // check if interaction is still valid
  if (interaction.replied || interaction.deferred) {
    return;
  }

  // check if interaction is expired (more than 14.5 seconds old)
  const interactionAge = Date.now() - interaction.createdTimestamp;
  if (interactionAge > 14500) {
    return;
  }

  try {
    switch (interaction.customId) {
      case "market_orders":
        await interaction.deferUpdate();
        await showPlayerOrders(interaction, character);
        break;
      case "market_cancel_orders":
        await interaction.deferUpdate();
        await showCancelOrdersMenu(interaction, character);
        break;
      case "market_search":
        await handleMarketSearch(interaction, character);
        break;
      case "market_create_buy_order":
        await showCreateBuyOrderMenu(interaction, character);
        break;
      case "market_create_sell_order":
        await showCreateSellOrderMenu(interaction, character);
        break;
      case "create_sell_order_select":
        await handleCreateSellOrderSelect(interaction, character);
        break;
      case "cancel_order_select":
        await handleCancelOrderSelect(interaction, character);
        break;
      case "quick_buy_select":
        await handleQuickBuySelect(interaction, character);
        break;
      case "quick_sell_select":
        await handleQuickSellSelect(interaction, character);
        break;
      case "market_sell_select":
        await handleMarketSellSelect(interaction, character);
        break;
      case "market_view_orderbook":
        await handleMarketViewOrderbook(interaction, character);
        break;
      case "market_back":
        await interaction.deferUpdate();
        await showMarketMenu(interaction, character);
        break;
      default:
        // handle specific item interactions
        if (interaction.customId.startsWith("market_")) {
          await handleSpecificMarketAction(interaction, character);
        }
        break;
    }
  } catch (error) {
    console.error(
      `[Market] Error in handleMarketInteraction for ${interaction.customId}:`,
      error
    );
    throw error; // re-throw to be caught by the collector
  }
}

/**
 * shows search results interface with order books
 */
async function showSearchResults(
  interaction,
  character,
  page = 0,
  searchQuery = ""
) {
  const allItems = configManager.getAllItems();
  let itemResults = [];

  if (searchQuery) {
    // use fuzzy search to find matching items
    const fuzzyResults = fuzzySearchItems(allItems, searchQuery, {
      excludeUnique: true,
      excludePets: true,
      maxResults: 50,
    });

    // check if we have a perfect exact match (score 1000) that should go directly to orderbook
    if (fuzzyResults.length > 0 && fuzzyResults[0].score === 1000) {
      // this is a perfect exact match - go directly to the orderbook
      const exactMatch = fuzzyResults[0];
      return await showOrderBook(
        interaction,
        character,
        exactMatch.itemKey,
        searchQuery,
        page
      );
    }

    // check if we have a very strong match (like exact key or name start) and only 1-2 results
    if (
      fuzzyResults.length <= 2 &&
      fuzzyResults.length > 0 &&
      fuzzyResults[0].score >= 900
    ) {
      const bestMatch = fuzzyResults[0];
      // if the query looks like an exact match attempt, go directly to orderbook
      if (isLikelyExactMatch(searchQuery)) {
        return await showOrderBook(
          interaction,
          character,
          bestMatch.itemKey,
          searchQuery,
          page
        );
      }
    }

    // if there's only one result, automatically select it
    if (fuzzyResults.length === 1) {
      const singleResult = fuzzyResults[0];
      return await showOrderBook(
        interaction,
        character,
        singleResult.itemKey,
        searchQuery,
        page
      );
    }

    // convert to the format expected by the rest of the function
    itemResults = fuzzyResults.map((result) => ({ item_key: result.itemKey }));
  } else {
    // require a search query - don't show items without searching
    itemResults = [];
  }

  // Filter out untradeable items from results
  itemResults = itemResults.filter((r) => !allItems[r.item_key]?.untradeable);

  if (itemResults.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ORANGE)
      .setTitle("🔍 Market Search")
      .setDescription(
        searchQuery
          ? `No items found matching "${searchQuery}".`
          : "Enter a search term to find items on the market."
      )
      .addFields({
        name: "💡 Tip",
        value: searchQuery
          ? "Try a different search term or check your spelling. You can search by item name (e.g., 'Enchanted String') or item key (e.g., 'ENCHANTED_STRING')."
          : "Search by item name or key to browse available items!",
      });

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  // paginate items
  const itemsPerPage = 10;
  const totalPages = Math.ceil(itemResults.length / itemsPerPage);
  const startIndex = page * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, itemResults.length);
  const pageItems = itemResults.slice(startIndex, endIndex);

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${MARKET_EMOJI} Market Search`);

  if (searchQuery) {
    let description = `Search results for "${searchQuery}" (${itemResults.length} items)`;

    // add helpful context based on search quality
    if (itemResults.length > 0) {
      // check if we used fuzzy matching to find results
      const allItems = configManager.getAllItems();
      const exactKeyMatch = allItems[searchQuery.toUpperCase()];
      const exactNameMatches = Object.values(allItems).filter(
        (item) => item?.name?.toLowerCase() === searchQuery.toLowerCase()
      );

      if (!exactKeyMatch && exactNameMatches.length === 0) {
        description += "\n*Results include fuzzy matches*";
      }
    }

    embed.setDescription(description);
  } else {
    // this shouldn't happen since we require search queries now
    embed.setDescription(`Found ${itemResults.length} items`);
  }

  // get order book data for each item
  for (const itemResult of pageItems) {
    const itemKey = itemResult.item_key;
    const itemData = allItems[itemKey];
    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "❓";

    // get best prices
    const bestSell = await dbGet(
      "SELECT MIN(price_per_unit) as price FROM market_sell_orders WHERE item_key = ? AND quantity > 0",
      [itemKey]
    );
    const bestBuy = await dbGet(
      "SELECT MAX(price_per_unit) as price FROM market_buy_orders WHERE item_key = ? AND quantity > 0",
      [itemKey]
    );

    const sellPrice = bestSell?.price
      ? `${formatPrice(bestSell.price)} coins`
      : "❌";
    const buyPrice = bestBuy?.price
      ? `${formatPrice(bestBuy.price)} coins`
      : "❌";

    embed.addFields({
      name: `${itemEmoji} ${itemName}`,
      value: `**Sell Price:** ${sellPrice}\n` + `**Buy Price:** ${buyPrice}`,
      inline: true,
    });
  }

  if (totalPages > 1) {
    embed.setFooter({ text: `Page ${page + 1}/${totalPages}` });
  }

  // create action buttons
  const components = [];

  // pagination buttons
  if (totalPages > 1) {
    const paginationRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`market_search_prev:${page}:${searchQuery}`)
        .setLabel("Previous")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === 0),
      new ButtonBuilder()
        .setCustomId(`market_search_next:${page}:${searchQuery}`)
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page >= totalPages - 1)
    );
    components.push(paginationRow);
  }

  // item selection menu
  if (pageItems.length > 0) {
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("market_view_orderbook")
      .setPlaceholder("Select an item")
      .addOptions(
        pageItems.map((itemResult) => {
          const itemData = allItems[itemResult.item_key];
          const itemName = itemData?.name || itemResult.item_key;
          const itemEmoji = itemData?.emoji || "❓";

          return {
            label: itemName,
            description: `View order book for ${itemName}`,
            value: itemResult.item_key,
            emoji: itemEmoji,
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    components.push(selectRow);
  }

  // control buttons
  const controlRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_search")
      .setLabel("Search")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Menu")
      .setStyle(ButtonStyle.Secondary)
  );
  components.push(controlRow);

  await interaction.editReply({
    embeds: [embed],
    components: components,
  });

  // handle additional interactions
  const message = await interaction.fetchReply();

  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId.startsWith("market_search_") ||
        i.customId === "market_view_orderbook" ||
        i.customId === "market_search" ||
        i.customId.startsWith("market_")),
    time: 300000,
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      // check if interaction is already handled
      if (i.replied || i.deferred) {
        return;
      }

      if (i.customId.startsWith("market_search_")) {
        // handle pagination
        const [action, currentPage, search] = i.customId.split(":");
        const newPage =
          action === "market_search_prev"
            ? parseInt(currentPage) - 1
            : parseInt(currentPage) + 1;
        await i.deferUpdate();
        await showSearchResults(i, character, newPage, search || "");
      } else if (i.customId === "market_view_orderbook") {
        // handle order book viewing - don't pass to main handler
        const itemKey = i.values[0];
        await i.deferUpdate();
        // pass search context so we can navigate back to search results
        await showOrderBook(i, character, itemKey, searchQuery, page);
        return; // important: return here to prevent further processing
      } else if (i.customId === "market_search") {
        await handleMarketSearch(i, character);
      } else {
        // only pass other interactions to main handler
        await handleMarketInteraction(i, character);
      }
    } catch (error) {
      console.error("[Market Browse] Error handling interaction:", error);
      // only try to respond if interaction hasn't been acknowledged yet
      if (!i.replied && !i.deferred) {
        try {
          await i.reply({
            content:
              "An error occurred while processing your request. Please try again.",
            flags: [MessageFlags.Ephemeral],
          });
        } catch (replyError) {
          console.error(
            "[Market Browse] Failed to send error reply:",
            replyError
          );
        }
      }
    }
  });

  collector.on("end", () => {
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * shows the order book for a specific item
 */
async function showOrderBook(
  interaction,
  character,
  itemKey,
  fromSearchQuery = null,
  fromSearchPage = 0
) {
  const allItems = configManager.getAllItems();
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;
  const itemEmoji = itemData?.emoji || "❓";

  // Block viewing orderbook for untradeable items
  if (itemData?.untradeable) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle(`${itemEmoji} ${itemName}`)
      .setDescription("This item cannot be traded on the market.");
    const navRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(
          fromSearchQuery !== null
            ? `market_back_to_search:${fromSearchQuery}:${fromSearchPage}`
            : "market_back"
        )
        .setLabel(fromSearchQuery !== null ? "Back" : "Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );
    await interaction.editReply({ embeds: [embed], components: [navRow] });
    return;
  }

  // get sell orders (ascending price)
  const sellOrders = await dbAll(
    `SELECT so.*, p.name as seller_name 
     FROM market_sell_orders so 
     JOIN players p ON so.seller_id = p.discord_id 
     WHERE so.item_key = ? AND so.quantity > 0 
     ORDER BY so.price_per_unit ASC 
     LIMIT 10`,
    [itemKey]
  );

  // get buy orders (descending price)
  const buyOrders = await dbAll(
    `SELECT bo.*, p.name as buyer_name 
     FROM market_buy_orders bo 
     JOIN players p ON bo.buyer_id = p.discord_id 
     WHERE bo.item_key = ? AND bo.quantity > 0 
     ORDER BY bo.price_per_unit DESC 
     LIMIT 10`,
    [itemKey]
  );

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${itemEmoji} ${itemName}`);

  // build the description with side-by-side layout
  let description = "```\n";
  description += "Sell Orders:                  Buy Orders:\n";

  // get max number of orders to display (up to 5 each for better formatting)
  const maxOrders = Math.max(sellOrders.length, buyOrders.length, 1);
  const displayLimit = Math.min(maxOrders, 5);

  for (let i = 0; i < displayLimit; i++) {
    let leftSide = "";
    let rightSide = "";

    if (i < sellOrders.length) {
      const order = sellOrders[i];
      leftSide = `${order.quantity.toLocaleString()}x @ ${formatPrice(order.price_per_unit)} coins`;
    } else if (i === 0 && sellOrders.length === 0) {
      leftSide = "No orders";
    }

    if (i < buyOrders.length) {
      const order = buyOrders[i];
      rightSide = `${order.quantity.toLocaleString()}x @ ${formatPrice(order.price_per_unit)} coins`;
    } else if (i === 0 && buyOrders.length === 0) {
      rightSide = "No orders";
    }

    // pad left side to 30 characters for alignment
    const paddedLeft = leftSide.padEnd(30);
    description += `${paddedLeft}${rightSide}\n`;
  }

  description += "```\n";

  // calculate market prices
  const bestSellPrice =
    sellOrders.length > 0 ? sellOrders[0].price_per_unit : null;
  const bestBuyPrice =
    buyOrders.length > 0 ? buyOrders[0].price_per_unit : null;

  if (bestSellPrice)
    description += `**Insta Buy Price:** ${formatPrice(bestSellPrice)} coins\n`;
  if (bestBuyPrice)
    description += `**Insta Sell Price:** ${formatPrice(bestBuyPrice)} coins\n`;

  embed.setDescription(description);

  // action buttons - first row for instant actions
  const instantRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`market_instant_buy:${itemKey}`)
      .setLabel("Insta Buy")
      .setStyle(ButtonStyle.Danger)
      .setDisabled(!bestSellPrice),
    new ButtonBuilder()
      .setCustomId(`market_instant_sell:${itemKey}`)
      .setLabel("Insta Sell")
      .setStyle(ButtonStyle.Success)
      .setDisabled(!bestBuyPrice)
  );

  // second row for order creation
  const orderRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`market_create_buy_order:${itemKey}`)
      .setLabel("Buy Order")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId(`market_create_sell_order:${itemKey}`)
      .setLabel("Sell Order")
      .setStyle(ButtonStyle.Primary)
  );

  // navigation row
  const navRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(
        fromSearchQuery !== null
          ? `market_back_to_search:${fromSearchQuery}:${fromSearchPage}`
          : "market_back"
      )
      .setLabel(fromSearchQuery !== null ? "Back" : "Back to Menu")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [instantRow, orderRow, navRow],
  });

  // handle order book interactions
  const message = await interaction.fetchReply();

  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id && i.customId.startsWith("market_"),
    time: 300000,
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      if (i.customId.startsWith("market_instant_buy:")) {
        const targetItemKey = i.customId.split(":")[1];
        await handleInstantBuy(i, character, targetItemKey);
      } else if (i.customId.startsWith("market_instant_sell:")) {
        const targetItemKey = i.customId.split(":")[1];
        await handleInstantSell(i, character, targetItemKey);
      } else if (i.customId.startsWith("market_create_buy_order:")) {
        // reformat the customId to match the expected format for handleCreateBuyOrder
        const targetItemKey = i.customId.split(":")[1];
        i.customId = `create_buy_order:${targetItemKey}`;
        await handleCreateBuyOrder(i, character);
      } else if (i.customId.startsWith("market_create_sell_order:")) {
        // reformat the customId to match the expected format for handleCreateSellOrder
        const targetItemKey = i.customId.split(":")[1];
        i.customId = `create_sell_order:${targetItemKey}`;
        await handleCreateSellOrder(i, character);
      } else if (i.customId.startsWith("market_back_to_search:")) {
        // handle going back to search results
        const [, searchQuery, searchPage] = i.customId.split(":");
        await i.deferUpdate();
        await showSearchResults(
          i,
          character,
          parseInt(searchPage) || 0,
          searchQuery || ""
        );
      } else {
        await handleMarketInteraction(i, character);
      }
    } catch (error) {
      console.error("[Order Book] Error:", error);
    }
  });

  collector.on("end", () => {
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * shows player's active orders
 */
async function showPlayerOrders(interaction, _character) {
  const userId = interaction.user.id;

  // get player's sell orders
  const sellOrders = await dbAll(
    "SELECT * FROM market_sell_orders WHERE seller_id = ? AND quantity > 0 ORDER BY created_at DESC",
    [userId]
  );

  // get player's buy orders
  const buyOrders = await dbAll(
    "SELECT * FROM market_buy_orders WHERE buyer_id = ? AND quantity > 0 ORDER BY created_at DESC",
    [userId]
  );

  const allItems = configManager.getAllItems();

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${MARKET_EMOJI} Your Active Orders`)
    .setDescription(
      `You have ${sellOrders.length}/${MARKET_LIMITS.MAX_SELL_ORDERS} sell orders and ${buyOrders.length}/${MARKET_LIMITS.MAX_BUY_ORDERS} buy orders`
    );

  // add sell orders
  if (sellOrders.length > 0) {
    const sellText = sellOrders
      .slice(0, 10) // limit display
      .map((order) => {
        const itemData = allItems[order.item_key];
        const itemName = itemData?.name || order.item_key;
        const filled =
          (order.original_quantity || order.quantity) - order.quantity;
        const total = order.original_quantity || order.quantity;
        const progressText = filled > 0 ? ` (**${filled}/${total}**)` : "";
        return `${itemName}: ${order.quantity.toLocaleString()}x @ ${formatPrice(order.price_per_unit)} coins${progressText}`;
      })
      .join("\n");

    embed.addFields({
      name: "🔴 Your Sell Orders",
      value:
        sellText.length > 1024 ? sellText.substring(0, 1021) + "..." : sellText,
      inline: false,
    });
  }

  // add buy orders
  if (buyOrders.length > 0) {
    const buyText = buyOrders
      .slice(0, 10) // limit display
      .map((order) => {
        const itemData = allItems[order.item_key];
        const itemName = itemData?.name || order.item_key;
        const filled =
          (order.original_quantity || order.quantity) - order.quantity;
        const total = order.original_quantity || order.quantity;
        const progressText = filled > 0 ? ` (**${filled}/${total}**)` : "";
        return `${itemName}: ${order.quantity.toLocaleString()}x @ ${formatPrice(order.price_per_unit)} coins${progressText}`;
      })
      .join("\n");

    embed.addFields({
      name: "🟢 Your Buy Orders",
      value:
        buyText.length > 1024 ? buyText.substring(0, 1021) + "..." : buyText,
      inline: false,
    });
  }

  if (sellOrders.length === 0 && buyOrders.length === 0) {
    embed.setDescription("You don't have any active orders.").addFields({
      name: "💡 Get Started",
      value: "Use 'Quick Trade' to place your first orders!",
    });
  }

  const actionRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_cancel_orders")
      .setLabel("Cancel Orders")
      .setStyle(ButtonStyle.Danger)
      .setDisabled(sellOrders.length === 0 && buyOrders.length === 0),
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Menu")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [actionRow],
  });
}

/**
 * shows cancel orders menu with list of player's orders
 */
async function showCancelOrdersMenu(interaction, character) {
  const userId = interaction.user.id;

  // get player's orders
  const sellOrders = await dbAll(
    "SELECT *, 'sell' as order_type FROM market_sell_orders WHERE seller_id = ? AND quantity > 0 ORDER BY created_at DESC",
    [userId]
  );

  const buyOrders = await dbAll(
    "SELECT *, 'buy' as order_type FROM market_buy_orders WHERE buyer_id = ? AND quantity > 0 ORDER BY created_at DESC",
    [userId]
  );

  const allOrders = [...sellOrders, ...buyOrders].sort(
    (a, b) => new Date(b.created_at) - new Date(a.created_at)
  );

  if (allOrders.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ No Orders to Cancel")
      .setDescription("You don't have any active orders to cancel.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_orders")
        .setLabel("Back to Orders")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  const allItems = configManager.getAllItems();

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ORANGE)
    .setTitle("🗑️ Cancel Orders")
    .setDescription(
      "Select an order to cancel. Items and coins will be returned to you."
    );

  // create select menu with orders
  const orderOptions = allOrders.slice(0, 25).map((order) => {
    const itemData = allItems[order.item_key];
    const itemName = itemData?.name || order.item_key;
    const itemEmoji = itemData?.emoji || "❓";
    const orderType = order.order_type === "sell" ? "Sell" : "Buy";
    const totalValue = order.quantity * order.price_per_unit;

    return {
      label: `${orderType}: ${itemName} (${order.quantity.toLocaleString()}x)`,
      description: `${formatPrice(order.price_per_unit)} coins each • Total: ${formatPrice(totalValue)}`,
      value: `cancel:${order.order_type}:${order.id}`,
      emoji: itemEmoji,
    };
  });

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("cancel_order_select")
    .setPlaceholder("Choose an order to cancel...")
    .addOptions(orderOptions);

  const selectRow = new ActionRowBuilder().addComponents(selectMenu);
  const backRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_orders")
      .setLabel("Back to Orders")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [selectRow, backRow],
  });

  // handle order selection with simple pattern
  const message = await interaction.fetchReply();

  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId === "cancel_order_select" ||
        i.customId.startsWith("market_")),
    time: 300000,
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      // check if interaction is already handled
      if (i.replied || i.deferred) {
        return;
      }

      // check interaction age
      const interactionAge = Date.now() - i.createdTimestamp;
      if (interactionAge > 14 * 60 * 1000) {
        return;
      }

      if (i.customId === "cancel_order_select") {
        const [action, orderType, orderId] = i.values[0].split(":");
        if (action === "cancel") {
          await i.deferUpdate();
          await confirmCancelOrder(i, character, orderType, parseInt(orderId));
        }
      } else {
        // handle other interactions through main handler
        await i.deferUpdate();
        await handleMarketInteraction(i, character);
      }
    } catch (error) {
      console.error(
        "[Market Cancel Orders] Error handling interaction:",
        error
      );
    }
  });

  collector.on("end", () => {
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * shows confirmation for canceling a specific order
 */
async function confirmCancelOrder(interaction, character, orderType, orderId) {
  // interaction should already be deferred by the calling function

  // get the specific order
  let order;
  if (orderType === "sell") {
    order = await dbGet(
      "SELECT * FROM market_sell_orders WHERE id = ? AND seller_id = ?",
      [orderId, interaction.user.id]
    );
  } else {
    order = await dbGet(
      "SELECT * FROM market_buy_orders WHERE id = ? AND buyer_id = ?",
      [orderId, interaction.user.id]
    );
  }

  if (!order) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ Order Not Found")
      .setDescription("This order no longer exists or doesn't belong to you.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_cancel_orders")
        .setLabel("Back to Cancel Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  const allItems = configManager.getAllItems();
  const itemData = allItems[order.item_key];
  const itemName = itemData?.name || order.item_key;
  const itemEmoji = itemData?.emoji || "❓";
  const totalValue = order.quantity * order.price_per_unit;

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ORANGE)
    .setTitle("🗑️ Confirm Cancellation")
    .setDescription(
      `Are you sure you want to cancel this ${orderType} order?\n\n` +
        `**Item:** ${itemEmoji} ${itemName}\n` +
        `**Quantity:** ${order.quantity.toLocaleString()}\n` +
        `**Price:** ${formatPrice(order.price_per_unit)} coins each\n` +
        `**Total Value:** ${formatPrice(totalValue)} coins\n\n` +
        `${orderType === "sell" ? "Items" : "Coins"} will be returned to your ${orderType === "sell" ? "inventory" : "purse"}.`
    );

  const actionRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`confirm_order_cancel:${orderType}:${orderId}`)
      .setLabel("Cancel Order")
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId("market_cancel_orders")
      .setLabel("Keep Order")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [actionRow],
  });

  // handle confirmation - stop any existing collector first
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  const message = await interaction.fetchReply();
  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId.startsWith("confirm_order_cancel:") ||
        i.customId === "market_cancel_orders"),
    time: 900000, // 15 minutes (longer timeout to prevent expiration)
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      // check if interaction is already handled
      if (i.replied || i.deferred) {
        return;
      }

      // check interaction age
      const interactionAge = Date.now() - i.createdTimestamp;
      if (interactionAge > 14 * 60 * 1000) {
        return;
      }

      if (i.customId.startsWith("confirm_order_cancel:")) {
        const [, type, id] = i.customId.split(":");
        await i.deferUpdate();
        await processCancelOrder(i, character, type, parseInt(id));
      } else if (i.customId === "market_cancel_orders") {
        await i.deferUpdate();
        await showCancelOrdersMenu(i, character);
      }
    } catch (error) {
      console.error(
        "[Market Confirm Cancel] Error handling interaction:",
        error
      );
    }
  });

  collector.on("end", () => {
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * processes the actual cancellation of an order and returns escrowed items/coins
 */
async function processCancelOrder(interaction, character, orderType, orderId) {
  // interaction should already be deferred by the calling function

  try {
    // get the order details
    let order;
    if (orderType === "sell") {
      order = await dbGet(
        "SELECT * FROM market_sell_orders WHERE id = ? AND seller_id = ?",
        [orderId, interaction.user.id]
      );
    } else {
      order = await dbGet(
        "SELECT * FROM market_buy_orders WHERE id = ? AND buyer_id = ?",
        [orderId, interaction.user.id]
      );
    }

    if (!order) {
      return interaction.followUp({
        content: "Order not found or already canceled.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    const allItems = configManager.getAllItems();
    const itemData = allItems[order.item_key];
    const itemName = itemData?.name || order.item_key;
    const itemEmoji = itemData?.emoji || "❓";

    // for buy orders, use the actual locked coins; for sell orders, calculate value
    const totalValue =
      orderType === "buy"
        ? order.total_coins_locked
        : order.quantity * order.price_per_unit;

    if (orderType === "sell") {
      // return items to seller's inventory
      const { updateInventoryAtomically } = require("../utils/inventory");
      await updateInventoryAtomically(
        interaction.user.id,
        0, // no coins
        [{ itemKey: order.item_key, amount: order.quantity }], // return items
        [], // no equipment to add
        [], // no equipment to remove
        0 // no bank coins
      );

      // delete the sell order
      await dbRunQueued("DELETE FROM market_sell_orders WHERE id = ?", [
        orderId,
      ]);

      // show success message with back button
      const successEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GREEN)
        .setTitle("✅ Sell Order Canceled")
        .setDescription(
          `${itemEmoji} Returned **${order.quantity.toLocaleString()}x ${itemName}** to your inventory.`
        );

      const backButton = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId("market_back")
          .setLabel("Back to Market")
          .setStyle(ButtonStyle.Secondary)
      );

      await interaction.editReply({
        embeds: [successEmbed],
        components: [backButton],
      });

      // set up collector to handle the "Back to Market" button
      await setupMarketCollector(interaction, character);
    } else {
      // return coins to buyer's purse
      const { updateInventoryAtomically } = require("../utils/inventory");
      await updateInventoryAtomically(
        interaction.user.id,
        totalValue, // return coins
        [], // no items to add
        [], // no equipment to add
        [], // no equipment to remove
        0 // no bank coins
      );

      // delete the buy order
      await dbRunQueued("DELETE FROM market_buy_orders WHERE id = ?", [
        orderId,
      ]);

      // show success message with back button
      const successEmbed = new EmbedBuilder()
        .setColor(EMBED_COLORS.GREEN)
        .setTitle("✅ Buy Order Canceled")
        .setDescription(
          `💰 Returned **${formatPrice(totalValue)} coins** to your purse.\n` +
            `Order: ${order.quantity.toLocaleString()}x ${itemEmoji} ${itemName} @ ${formatPrice(order.price_per_unit)} coins each`
        );

      const backButton = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId("market_back")
          .setLabel("Back to Market")
          .setStyle(ButtonStyle.Secondary)
      );

      await interaction.editReply({
        embeds: [successEmbed],
        components: [backButton],
      });

      // set up collector to handle the "Back to Market" button
      await setupMarketCollector(interaction, character);
    }

    console.log(
      `[Market] ${character.name} canceled ${orderType} order ${orderId}: ${order.quantity}x ${order.item_key} @ ${order.price_per_unit} coins each`
    );
  } catch (error) {
    console.error("[Market] Error canceling order:", error);
    await interaction.followUp({
      content: "An error occurred while canceling your order.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * shows quick buy menu for selecting items to buy instantly
 */
async function showQuickBuyMenu(interaction, character) {
  // get items with sell orders available
  const sellOrderItems = await dbAll(`
    SELECT DISTINCT so.item_key, MIN(so.price_per_unit) as best_price
    FROM market_sell_orders so 
    WHERE so.quantity > 0 
    GROUP BY so.item_key 
    ORDER BY so.item_key
  `);

  const allItems = configManager.getAllItems();
  // remove untradeable items from sell-order set
  const filtered = sellOrderItems.filter(
    (i) => !allItems[i.item_key]?.untradeable
  );

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`${MARKET_EMOJI} Instant Buy`)
    .setDescription(
      "Select an item to buy instantly at the best available price.\n\n" +
        `${CURRENCY.purseEmoji} **Your Purse:** ${formatPrice(character.coins || 0)} coins`
    );

  if (filtered.length === 0) {
    embed.setDescription(
      "No items are currently available for instant purchase."
    );
    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );
    return interaction.editReply({ embeds: [embed], components: [backButton] });
  }

  // show up to 25 items in select menu
  const displayItems = filtered.slice(0, 25);

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("quick_buy_select")
    .setPlaceholder("Select an item to buy instantly...")
    .addOptions(
      displayItems.map((item) => {
        const itemData = allItems[item.item_key];
        const itemName = itemData?.name || item.item_key;
        const itemEmoji = itemData?.emoji || "❓";

        return {
          label: itemName,
          description: `Best price: ${formatPrice(item.best_price)} coins each`,
          value: item.item_key,
          emoji: itemEmoji,
        };
      })
    );

  const selectRow = new ActionRowBuilder().addComponents(selectMenu);
  const backRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Market")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [selectRow, backRow],
  });

  // handle selection
  const message = await interaction.fetchReply();
  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id && i.customId === "quick_buy_select",
    time: 300000,
  });

  collector.on("collect", async (i) => {
    const itemKey = i.values[0];
    await handleInstantBuy(i, character, itemKey);
  });
}

/**
 * shows quick sell menu for selecting items to sell instantly
 */
async function showQuickSellMenu(interaction, character) {
  // get player's inventory using proper inventory function
  const playerInventory = await getInventory(interaction.user.id);
  const playerInv = playerInventory.items || {};
  const allItems = configManager.getAllItems();
  const inventoryItems = Object.keys(playerInv).filter(
    (key) => (playerInv[key] || 0) > 0 && !allItems[key]?.untradeable
  );

  if (inventoryItems.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ORANGE)
      .setTitle(`${MARKET_EMOJI} Instant Sell`)
      .setDescription("You don't have any items in your inventory to sell.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({ embeds: [embed], components: [backButton] });
  }

  // check which items have buy orders
  const itemsWithBuyOrders = [];
  for (const itemKey of inventoryItems) {
    const bestBuy = await dbGet(
      "SELECT MAX(price_per_unit) as best_price FROM market_buy_orders WHERE item_key = ? AND quantity > 0",
      [itemKey]
    );

    if (bestBuy?.best_price) {
      itemsWithBuyOrders.push({
        itemKey,
        quantity: playerInv[itemKey],
        bestPrice: bestBuy.best_price,
      });
    }
  }

  // allItems already fetched above

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GREEN)
    .setTitle(`${MARKET_EMOJI} Instant Sell`)
    .setDescription(
      "Select an item to sell instantly at the best available price.\n\n" +
        `${CURRENCY.purseEmoji} **Your Purse:** ${formatPrice(character.coins || 0)} coins`
    );

  if (itemsWithBuyOrders.length === 0) {
    embed.setDescription("None of your items have active buy orders.");
    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );
    return interaction.editReply({ embeds: [embed], components: [backButton] });
  }

  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("quick_sell_select")
    .setPlaceholder("Select an item to sell instantly...")
    .addOptions(
      itemsWithBuyOrders.slice(0, 25).map((item) => {
        const itemData = allItems[item.itemKey];
        const itemName = itemData?.name || item.itemKey;
        const itemEmoji = itemData?.emoji || "❓";

        return {
          label: `${itemName} (${item.quantity.toLocaleString()})`,
          description: `Best price: ${formatPrice(item.bestPrice)} coins each`,
          value: item.itemKey,
          emoji: itemEmoji,
        };
      })
    );

  const selectRow = new ActionRowBuilder().addComponents(selectMenu);
  const backRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Market")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [selectRow, backRow],
  });

  // handle selection
  const message = await interaction.fetchReply();
  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id && i.customId === "quick_sell_select",
    time: 300000,
  });

  collector.on("collect", async (i) => {
    const itemKey = i.values[0];
    await handleInstantSell(i, character, itemKey);
  });
}

/**
 * shows menu for creating a buy order with item selection
 */
async function showCreateBuyOrderMenu(interaction, _character) {
  // check if interaction is already handled
  if (interaction.replied || interaction.deferred) {
    return;
  }

  // check buy order limit before showing modal
  const currentBuyOrders = await dbAll(
    "SELECT COUNT(*) as count FROM market_buy_orders WHERE buyer_id = ? AND quantity > 0",
    [interaction.user.id]
  );

  if (currentBuyOrders[0]?.count >= MARKET_LIMITS.MAX_BUY_ORDERS) {
    await interaction.reply({
      content: `❌ You can only have a maximum of **${MARKET_LIMITS.MAX_BUY_ORDERS} active buy orders**. Cancel some existing orders first.`,
      flags: [MessageFlags.Ephemeral],
    });
    return;
  } // show modal for item input
  const modal = new ModalBuilder()
    .setCustomId("create_buy_order_modal")
    .setTitle("Create Buy Order");

  const itemInput = new TextInputBuilder()
    .setCustomId("item_name")
    .setLabel("Item Name")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("Enter the exact item name...")
    .setRequired(true);

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel("Quantity")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("How many do you want to buy?")
    .setRequired(true);

  const priceInput = new TextInputBuilder()
    .setCustomId("price")
    .setLabel("Price per unit (coins)")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("Maximum price you'll pay per item")
    .setRequired(true);

  const itemRow = new ActionRowBuilder().addComponents(itemInput);
  const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
  const priceRow = new ActionRowBuilder().addComponents(priceInput);

  modal.addComponents(itemRow, quantityRow, priceRow);
  await interaction.showModal(modal);
}

/**
 * shows menu for creating a sell order with player's inventory
 */
async function showCreateSellOrderMenu(interaction, _character, page = 0) {
  // for paginated calls, the interaction is already deferred
  const isInitialCall =
    page === 0 && !interaction.deferred && !interaction.replied;

  if (isInitialCall) {
    await interaction.deferUpdate();
  }

  // check sell order limit before proceeding
  const currentSellOrders = await dbAll(
    "SELECT COUNT(*) as count FROM market_sell_orders WHERE seller_id = ? AND quantity > 0",
    [interaction.user.id]
  );

  if (currentSellOrders[0]?.count >= MARKET_LIMITS.MAX_SELL_ORDERS) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle(`${MARKET_EMOJI} Order Limit Reached`)
      .setDescription(
        `❌ You can only have a maximum of **${MARKET_LIMITS.MAX_SELL_ORDERS} active sell orders**. Cancel some existing orders first.`
      );

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({ embeds: [embed], components: [backButton] });
  }

  // get player's inventory using proper inventory function
  const playerInventory = await getInventory(interaction.user.id);
  const playerInv = playerInventory.items || {};

  // filter out pets and unique items like the existing filter
  const allItems = configManager.getAllItems();
  const sellableItems = Object.entries(playerInv)
    .filter(([itemKey, quantity]) => {
      const itemData = allItems[itemKey];
      return (
        quantity > 0 &&
        itemData &&
        !itemData.unique &&
        itemData.type !== "PET" &&
        !itemData.untradeable
      );
    })
    .map(([itemKey, quantity]) => ({ itemKey, quantity }))
    .sort((a, b) => {
      // sort by quantity (highest first), then by name
      if (a.quantity !== b.quantity) {
        return b.quantity - a.quantity;
      }
      const aName = allItems[a.itemKey]?.name || a.itemKey;
      const bName = allItems[b.itemKey]?.name || b.itemKey;
      return aName.localeCompare(bName);
    });

  if (sellableItems.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ORANGE)
      .setTitle(`${MARKET_EMOJI} Create Sell Order`)
      .setDescription("You don't have any sellable items in your inventory.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({ embeds: [embed], components: [backButton] });
  }

  // pagination setup
  const itemsPerPage = 25;
  const totalPages = Math.ceil(sellableItems.length / itemsPerPage);
  const startIndex = page * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, sellableItems.length);
  const pageItems = sellableItems.slice(startIndex, endIndex);

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ERROR)
    .setTitle(`${MARKET_EMOJI} Create Sell Order`)
    .setDescription(
      `Select an item from your inventory to create a sell order (${sellableItems.length} items total):`
    );

  if (totalPages > 1) {
    embed.setFooter({ text: `Page ${page + 1}/${totalPages}` });
  }

  const components = [];

  // pagination buttons if needed
  if (totalPages > 1) {
    const paginationRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`create_sell_order_prev:${page}`)
        .setLabel("Previous")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page === 0),
      new ButtonBuilder()
        .setCustomId(`create_sell_order_next:${page}`)
        .setLabel("Next")
        .setStyle(ButtonStyle.Secondary)
        .setDisabled(page >= totalPages - 1)
    );
    components.push(paginationRow);
  }

  // item selection menu
  const selectMenu = new StringSelectMenuBuilder()
    .setCustomId("create_sell_order_select")
    .setPlaceholder("Choose an item to sell...")
    .addOptions(
      pageItems.map((item) => {
        const itemData = allItems[item.itemKey];
        const itemName = itemData?.name || item.itemKey;
        const itemEmoji = itemData?.emoji || "❓";

        return {
          label: `${itemName} (${item.quantity.toLocaleString()})`,
          description: `Create sell order for ${itemName}`,
          value: item.itemKey,
          emoji: itemEmoji,
        };
      })
    );

  const selectRow = new ActionRowBuilder().addComponents(selectMenu);
  components.push(selectRow);

  // back button
  const backRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Market")
      .setStyle(ButtonStyle.Secondary)
  );
  components.push(backRow);

  await interaction.editReply({
    embeds: [embed],
    components: components,
  });

  // set up collector for pagination and selection - only on initial call
  if (isInitialCall) {
    const message = await interaction.fetchReply();

    // stop any existing collector for this user
    const existingCollector = activeCollectors.get(interaction.user.id);
    if (existingCollector) {
      existingCollector.stop();
    }

    const collector = message.createMessageComponentCollector({
      filter: (i) =>
        i.user.id === interaction.user.id &&
        i.customId.startsWith("create_sell_order_"),
      time: 300000,
    });

    // store the collector
    activeCollectors.set(interaction.user.id, collector);

    collector.on("collect", async (i) => {
      try {
        if (i.replied || i.deferred) {
          console.log("[Sell Order Menu] Interaction already acknowledged");
          return;
        }

        if (i.customId.startsWith("create_sell_order_prev:")) {
          const currentPage = parseInt(i.customId.split(":")[1]);
          const newPage = currentPage - 1;
          await i.deferUpdate();
          await showCreateSellOrderMenu(i, _character, newPage);
        } else if (i.customId.startsWith("create_sell_order_next:")) {
          const currentPage = parseInt(i.customId.split(":")[1]);
          const newPage = currentPage + 1;
          await i.deferUpdate();
          await showCreateSellOrderMenu(i, _character, newPage);
        } else if (i.customId === "create_sell_order_select") {
          await handleCreateSellOrderSelect(i, _character);
          return; // important: return here to prevent further processing
        } else {
          // pass other interactions to main handler
          await handleMarketInteraction(i, _character);
        }
      } catch (error) {
        console.error("[Sell Order Menu] Error handling interaction:", error);
        if (!i.replied && !i.deferred) {
          try {
            await i.reply({
              content:
                "An error occurred while processing your request. Please try again.",
              flags: [MessageFlags.Ephemeral],
            });
          } catch (replyError) {
            console.error(
              "[Sell Order Menu] Failed to send error reply:",
              replyError
            );
          }
        }
      }
    });

    collector.on("end", () => {
      activeCollectors.delete(interaction.user.id);
    });
  }
}

/**
 * handles selection from the sell order item select menu
 */
async function handleCreateSellOrderSelect(interaction, _character) {
  if (interaction.replied || interaction.deferred) {
    return;
  }

  const itemKey = interaction.values[0];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.reply({
      content: "This item cannot be traded on the market.",
      flags: [MessageFlags.Ephemeral],
    });
  }

  // get fresh inventory data for the modal
  const freshInventory = await getInventory(interaction.user.id);
  const freshPlayerInv = freshInventory.items || {};

  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;
  const maxQuantity = freshPlayerInv[itemKey] || 0;

  // show modal for sell order details
  const modal = new ModalBuilder()
    .setCustomId(`create_sell_order_modal:${itemKey}`)
    .setTitle(
      `Sell: ${itemName.length > 39 ? itemName.substring(0, 36) + "..." : itemName}`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel(`Quantity (Max: ${maxQuantity.toLocaleString()})`)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("Enter amount or 'all'")
    .setRequired(true);

  const priceInput = new TextInputBuilder()
    .setCustomId("price")
    .setLabel("Price per unit (coins)")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("Minimum price you'll accept per item")
    .setRequired(true);

  const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
  const priceRow = new ActionRowBuilder().addComponents(priceInput);

  modal.addComponents(quantityRow, priceRow);
  await interaction.showModal(modal);
}

/**
 * handles selection from the market view orderbook select menu
 */
async function handleMarketViewOrderbook(interaction, character) {
  if (interaction.replied || interaction.deferred) {
    return;
  }

  const itemKey = interaction.values[0];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    await interaction.deferUpdate();
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("Not Tradable")
      .setDescription("This item cannot be traded on the market.");
    const backRow = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );
    return interaction.editReply({ embeds: [embed], components: [backRow] });
  }
  await interaction.deferUpdate();
  await showOrderBook(interaction, character, itemKey);
}

/**
 * handles selection from the cancel order select menu
 */
async function handleCancelOrderSelect(interaction, _character) {
  // check if interaction is still valid before processing
  if (interaction.replied || interaction.deferred) {
    return;
  }

  // check if interaction is expired (more than 14.5 seconds old)
  const interactionAge = Date.now() - interaction.createdTimestamp;
  if (interactionAge > 14500) {
    return;
  }

  try {
    const [action, orderType, orderId] = interaction.values[0].split(":");
    if (action === "cancel") {
      await interaction.deferUpdate();
      await confirmCancelOrder(
        interaction,
        _character,
        orderType,
        parseInt(orderId)
      );
    }
  } catch (error) {
    console.error("[Market] Error in handleCancelOrderSelect:", error);

    // only try to respond if interaction hasn't been acknowledged yet and isn't expired
    if (
      !interaction.replied &&
      !interaction.deferred &&
      Date.now() - interaction.createdTimestamp <= 14500
    ) {
      try {
        await interaction.reply({
          content:
            "An error occurred while processing your request. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch (replyError) {
        console.error(
          "[Market] Failed to send error reply in handleCancelOrderSelect:",
          replyError
        );
      }
    }
  }
}

/**
 * handles selection from the quick buy select menu
 */
async function handleQuickBuySelect(interaction, character) {
  // check if interaction is still valid before processing
  if (interaction.replied || interaction.deferred) {
    return;
  }

  // check if interaction is expired (more than 14.5 seconds old)
  const interactionAge = Date.now() - interaction.createdTimestamp;
  if (interactionAge > 14500) {
    return;
  }

  try {
    const itemKey = interaction.values[0];
    const allItems = configManager.getAllItems();
    if (allItems[itemKey]?.untradeable) {
      return interaction.reply({
        content: "This item cannot be traded on the market.",
        flags: [MessageFlags.Ephemeral],
      });
    }
    await handleInstantBuy(interaction, character, itemKey);
  } catch (error) {
    console.error("[Market] Error in handleQuickBuySelect:", error);

    // only try to respond if interaction hasn't been acknowledged yet and isn't expired
    if (
      !interaction.replied &&
      !interaction.deferred &&
      Date.now() - interaction.createdTimestamp <= 14500
    ) {
      try {
        await interaction.reply({
          content:
            "An error occurred while processing your request. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch (replyError) {
        console.error(
          "[Market] Failed to send error reply in handleQuickBuySelect:",
          replyError
        );
      }
    }
  }
}

/**
 * handles selection from the quick sell select menu
 */
async function handleQuickSellSelect(interaction, character) {
  // check if interaction is still valid before processing
  if (interaction.replied || interaction.deferred) {
    return;
  }

  // check if interaction is expired (more than 14.5 seconds old)
  const interactionAge = Date.now() - interaction.createdTimestamp;
  if (interactionAge > 14500) {
    return;
  }

  try {
    const itemKey = interaction.values[0];
    const allItems = configManager.getAllItems();
    if (allItems[itemKey]?.untradeable) {
      return interaction.reply({
        content: "This item cannot be traded on the market.",
        flags: [MessageFlags.Ephemeral],
      });
    }
    await handleInstantSell(interaction, character, itemKey);
  } catch (error) {
    console.error("[Market] Error in handleQuickSellSelect:", error);

    // only try to respond if interaction hasn't been acknowledged yet and isn't expired
    if (
      !interaction.replied &&
      !interaction.deferred &&
      Date.now() - interaction.createdTimestamp <= 14500
    ) {
      try {
        await interaction.reply({
          content:
            "An error occurred while processing your request. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch (replyError) {
        console.error(
          "[Market] Failed to send error reply in handleQuickSellSelect:",
          replyError
        );
      }
    }
  }
}

/**
 * handles selection from the market sell select menu
 */
async function handleMarketSellSelect(interaction, character) {
  if (interaction.replied || interaction.deferred) {
    console.log("[Market] handleMarketSellSelect: Interaction already handled");
    return;
  }

  const itemKey = interaction.values[0];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.reply({
      content: "This item cannot be traded on the market.",
      flags: [MessageFlags.Ephemeral],
    });
  }
  await handleItemSale(interaction, character, itemKey);
}

/**
 * handles instant buy functionality
 */
async function handleInstantBuy(interaction, character, itemKey) {
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.reply({
      content: "This item cannot be traded on the market.",
      flags: [MessageFlags.Ephemeral],
    });
  }
  // check if interaction is already handled
  if (interaction.replied || interaction.deferred) {
    console.log("[Market] Instant buy interaction already handled");
    return;
  }

  // get all available sell orders for this item
  const availableSells = await dbAll(
    `SELECT so.*, p.name as seller_name 
     FROM market_sell_orders so 
     JOIN players p ON so.seller_id = p.discord_id 
     WHERE so.item_key = ? AND so.quantity > 0 
     ORDER BY so.price_per_unit ASC`,
    [itemKey]
  );

  if (availableSells.length === 0) {
    try {
      return await interaction.reply({
        content: "No sell orders available for this item.",
        flags: [MessageFlags.Ephemeral],
      });
    } catch (error) {
      console.error(
        "[Market] Error replying to instant buy - no sell orders:",
        error
      );
      return;
    }
  }

  // calculate total available quantity and show user the price breakdown
  let totalAvailable = 0;
  let priceBreakdown = "";
  const cheapestPrice = availableSells[0].price_per_unit;
  const mostExpensivePrice =
    availableSells[availableSells.length - 1].price_per_unit;

  for (const order of availableSells) {
    if (order.seller_id !== character.discordId) {
      // can't buy from yourself
      totalAvailable += order.quantity;
    }
  }

  if (totalAvailable === 0) {
    try {
      return await interaction.reply({
        content: "No sell orders available from other players for this item.",
        flags: [MessageFlags.Ephemeral],
      });
    } catch (error) {
      console.error(
        "[Market] Error replying to instant buy - only own orders:",
        error
      );
      return;
    }
  }

  // create price summary for the modal
  if (cheapestPrice === mostExpensivePrice) {
    priceBreakdown = `${formatPrice(cheapestPrice)} coins each`;
  } else {
    priceBreakdown = `${formatPrice(cheapestPrice)} - ${formatPrice(mostExpensivePrice)} coins each`;
  }

  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;

  // show quantity modal
  const modal = new ModalBuilder()
    .setCustomId(`instant_buy_modal:${itemKey}`)
    .setTitle(
      `Buy: ${itemName.length > 40 ? itemName.substring(0, 37) + "..." : itemName}`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel(`Quantity (Max: ${totalAvailable.toLocaleString()})`)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(`${priceBreakdown} - Enter quantity...`)
    .setRequired(true);

  const actionRow = new ActionRowBuilder().addComponents(quantityInput);
  modal.addComponents(actionRow);

  try {
    await interaction.showModal(modal);
  } catch (error) {
    console.error("[Market] Error showing instant buy modal:", error);
    // try to reply if we can
    if (!interaction.replied && !interaction.deferred) {
      try {
        await interaction.reply({
          content: "An error occurred while setting up the instant buy.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch (replyError) {
        console.error("[Market] Error replying after modal error:", replyError);
      }
    }
  }
}

/**
 * handles instant sell functionality
 */
async function handleInstantSell(interaction, character, itemKey) {
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.reply({
      content: "This item cannot be traded on the market.",
      flags: [MessageFlags.Ephemeral],
    });
  }
  // check if interaction is already handled
  if (interaction.replied || interaction.deferred) {
    console.log("[Market] Instant sell interaction already handled");
    return;
  }

  // check player inventory using proper inventory function
  const playerInventory = await getInventory(interaction.user.id);
  const playerInv = playerInventory.items || {};
  const playerAmount = playerInv[itemKey] || 0;

  if (playerAmount === 0) {
    try {
      return await interaction.reply({
        content: "You don't have any of this item to sell.",
        flags: [MessageFlags.Ephemeral],
      });
    } catch (error) {
      console.error(
        "[Market] Error replying to instant sell - no items:",
        error
      );
      return;
    }
  }

  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;

  // show quantity modal for how much they want to sell
  const modal = new ModalBuilder()
    .setCustomId(`instant_sell_modal:${itemKey}`)
    .setTitle(
      `Sell: ${itemName.length > 39 ? itemName.substring(0, 36) + "..." : itemName}`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel(`Quantity (Max: ${playerAmount.toLocaleString()})`)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(`Max: ${playerAmount.toLocaleString()}`)
    .setMaxLength(playerAmount.toString().length + 2)
    .setRequired(true);

  const actionRow = new ActionRowBuilder().addComponents(quantityInput);
  modal.addComponents(actionRow);

  try {
    await interaction.showModal(modal);
  } catch (error) {
    console.error("[Market] Error showing instant sell modal:", error);
    // try to reply if we can
    if (!interaction.replied && !interaction.deferred) {
      try {
        await interaction.reply({
          content: "Sorry, this interaction has expired. Please try again.",
          flags: [MessageFlags.Ephemeral],
        });
      } catch (replyError) {
        console.error("[Market] Failed to send expiry reply:", replyError);
      }
    }
  }
}

/**
 * handles placing custom orders
 */
async function _handlePlaceOrder(interaction, character, itemKey) {
  const allItems = configManager.getAllItems();
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;

  // show order type selection
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle(`📝 Place Order: ${itemData?.emoji || "❓"} ${itemName}`)
    .setDescription("Choose the type of order you want to place:");

  const actionRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`create_buy_order:${itemKey}`)
      .setLabel("Buy Order")
      .setStyle(ButtonStyle.Success),
    new ButtonBuilder()
      .setCustomId(`create_sell_order:${itemKey}`)
      .setLabel("Sell Order")
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId(`market_orderbook:${itemKey}`)
      .setLabel("Back to Order Book")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.reply({
    embeds: [embed],
    components: [actionRow],
    flags: [MessageFlags.Ephemeral],
  });
}

/**
 * handles market search functionality
 */
async function handleMarketSearch(interaction, _character) {
  const modal = new ModalBuilder()
    .setCustomId("market_search_modal")
    .setTitle("Search Market");

  const searchInput = new TextInputBuilder()
    .setCustomId("search_query")
    .setLabel("Search for items")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(
      "e.g., 'enchanted string', 'ench string', or 'ENCHANTED_STRING'"
    )
    .setRequired(false);

  const actionRow = new ActionRowBuilder().addComponents(searchInput);
  modal.addComponents(actionRow);

  await interaction.showModal(modal);
}

/**
 * handles purchasing an item from a market listing (legacy - unused)
 */
async function _handleItemPurchase(interaction, character, listingId) {
  // get the specific listing
  const listing = await dbGet(
    "SELECT * FROM market_listings WHERE id = ? AND quantity > 0",
    [listingId]
  );

  if (!listing) {
    return interaction.reply({
      content: "This item is no longer available for purchase.",
      flags: [MessageFlags.Ephemeral],
    });
  }

  // can't buy from yourself
  if (listing.seller_id === interaction.user.id) {
    return interaction.reply({
      content: "You cannot purchase your own listings.",
      flags: [MessageFlags.Ephemeral],
    });
  }

  const allItems = configManager.getAllItems();
  const itemData = allItems[listing.item_key];
  const itemName = itemData?.name || listing.item_key;

  // show purchase modal
  const modal = new ModalBuilder()
    .setCustomId(`market_purchase:${listingId}`)
    .setTitle(
      `Purchase ${itemName.length > 36 ? itemName.substring(0, 33) + "..." : itemName}`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel(`Quantity (max: ${listing.quantity.toLocaleString()})`)
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setPlaceholder("Enter amount or 'all'");

  const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
  modal.addComponents(quantityRow);

  await interaction.showModal(modal);

  // handle modal submission
  try {
    const modalSubmission = await interaction.awaitModalSubmit({
      filter: (i) =>
        i.customId === `market_purchase:${listingId}` &&
        i.user.id === interaction.user.id,
      time: 120000,
    });

    const quantityStr = modalSubmission.fields.getTextInputValue("quantity");
    const quantity = parseShorthandAmount(quantityStr, listing.quantity);

    if (isNaN(quantity) || quantity <= 0 || quantity > listing.quantity) {
      return modalSubmission.reply({
        content: `Invalid quantity. Please enter a number between 1 and ${listing.quantity.toLocaleString()}.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const totalCost = quantity * listing.price_per_unit;

    // check if player has enough coins
    if ((character.coins || 0) < totalCost) {
      return modalSubmission.reply({
        content: `You need ${formatPrice(totalCost)} coins but only have ${formatPrice(character.coins || 0)}.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // process the purchase
    await processPurchase(
      modalSubmission,
      character,
      listing,
      quantity,
      totalCost,
      itemData
    );
  } catch (error) {
    console.error("[Market Purchase] Modal timeout or error:", error);
  }
}

/**
 * processes a market purchase transaction
 */
async function processPurchase(
  interaction,
  buyer,
  listing,
  quantity,
  totalCost,
  itemData
) {
  await interaction.deferUpdate();

  try {
    // get seller data
    const seller = await getPlayerData(listing.seller_id);
    if (!seller) {
      return interaction.editReply({
        content: "Error: Unable to find seller data.",
      });
    }

    // start database transaction
    const queries = [];

    // update buyer's coins
    const newBuyerCoins = (buyer.coins || 0) - totalCost;
    queries.push({
      sql: "UPDATE players SET coins = ? WHERE discord_id = ?",
      params: [newBuyerCoins, buyer.discordId],
    });

    // update seller's coins
    const newSellerCoins = (seller.coins || 0) + totalCost;
    queries.push({
      sql: "UPDATE players SET coins = ? WHERE discord_id = ?",
      params: [newSellerCoins, listing.seller_id],
    });

    // add item to buyer's inventory
    queries.push({
      sql: `INSERT INTO player_inventory_items (discord_id, item_name, amount)
            VALUES (?, ?, ?)
            ON CONFLICT(discord_id, item_name) DO UPDATE SET amount = amount + excluded.amount`,
      params: [buyer.discordId, listing.item_key, quantity],
    });

    // update or remove listing
    const newQuantity = listing.quantity - quantity;
    if (newQuantity > 0) {
      queries.push({
        sql: "UPDATE market_listings SET quantity = ? WHERE id = ?",
        params: [newQuantity, listing.id],
      });
    } else {
      queries.push({
        sql: "DELETE FROM market_listings WHERE id = ?",
        params: [listing.id],
      });
    }

    // execute all queries atomically via the write queue
    await Promise.all(queries.map((q) => dbRunQueued(q.sql, q.params)));

    const itemName = itemData?.name || listing.item_key;
    const itemEmoji = itemData?.emoji || "❓";

    await interaction.editReply({
      content:
        `✅ **Purchase Successful!**\n\n` +
        `${itemEmoji} Purchased **${quantity.toLocaleString()}x ${itemName}**\n` +
        `💰 Total cost: **${formatPrice(totalCost)} coins**\n` +
        `${CURRENCY.purseEmoji} Remaining coins: **${newBuyerCoins.toLocaleString()}**`,
    });

    console.log(
      `[Market] ${buyer.name} purchased ${quantity}x ${listing.item_key} for ${totalCost} coins from seller ${listing.seller_id}`
    );
  } catch (error) {
    console.error("[Market] Error processing purchase:", error);
    await interaction.editReply({
      content:
        "An error occurred while processing your purchase. Please try again.",
    });
  }
}

/**
 * shows the player's current market listings
 */
async function showPlayerListings(interaction, character) {
  const listings = await dbAll(
    "SELECT * FROM market_listings WHERE seller_id = ? ORDER BY created_at DESC",
    [character.discordId]
  );

  const allItems = configManager.getAllItems();

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GOLD)
    .setTitle("💰 Your Market Listings")
    .setDescription(`You have ${listings.length} active listings`);

  if (listings.length === 0) {
    embed.setDescription("You don't have any active market listings.");
    embed.addFields({
      name: "💡 Start Selling",
      value: "Use the 'Sell Items' option to list items for sale!",
    });
  } else {
    for (const listing of listings) {
      const itemData = allItems[listing.item_key];
      const itemName = itemData?.name || listing.item_key;
      const itemEmoji = itemData?.emoji || "❓";
      const totalValue = listing.quantity * listing.price_per_unit;

      embed.addFields({
        name: `${itemEmoji} ${itemName}`,
        value:
          `**Price:** ${formatPrice(listing.price_per_unit)} coins each\n` +
          `**Quantity:** ${listing.quantity.toLocaleString()}\n` +
          `**Total Value:** ${totalValue.toLocaleString()} coins`,
        inline: true,
      });
    }
  }

  const components = [];

  // listing management menu
  if (listings.length > 0) {
    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("market_manage_listing")
      .setPlaceholder("Select a listing to manage...")
      .addOptions(
        listings.map((listing) => {
          const itemData = allItems[listing.item_key];
          const itemName = itemData?.name || listing.item_key;
          const itemEmoji = itemData?.emoji || "❓";

          return {
            label: `${itemName} - ${formatPrice(listing.price_per_unit)} coins`,
            description: `${listing.quantity} remaining`,
            value: `manage:${listing.id}`,
            emoji: itemEmoji,
          };
        })
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    components.push(selectRow);
  }

  // control buttons
  const controlRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Menu")
      .setStyle(ButtonStyle.Secondary)
  );
  components.push(controlRow);

  await interaction.editReply({
    embeds: [embed],
    components: components,
  });

  // handle listing management
  if (listings.length > 0) {
    const message = await interaction.fetchReply();
    const collector = message.createMessageComponentCollector({
      filter: (i) =>
        i.user.id === interaction.user.id &&
        i.customId === "market_manage_listing",
      time: 300000,
    });

    collector.on("collect", async (i) => {
      const [action, listingId] = i.values[0].split(":");
      if (action === "manage") {
        await handleListingManagement(i, character, parseInt(listingId));
      }
    });
  }
}

/**
 * shows the sell menu for listing items (legacy - unused)
 */
async function _showSellMenu(interaction, character) {
  // get player's non-unique stackable items
  const inventory = character.inventory?.items || {};
  const allItems = configManager.getAllItems();

  const sellableItems = Object.entries(inventory)
    .map(([itemKey, quantity]) => {
      const itemData = allItems[itemKey];
      return {
        itemKey,
        quantity,
        itemData,
        name: itemData?.name || itemKey,
        emoji: itemData?.emoji || "❓",
      };
    })
    .filter(
      (item) =>
        item.itemData &&
        !item.itemData.unique &&
        item.itemData.type !== "PET" &&
        item.quantity > 0
    )
    .sort((a, b) => b.quantity - a.quantity);

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GREEN)
    .setTitle("📦 Sell Items")
    .setDescription(
      sellableItems.length > 0
        ? "Select an item from your inventory to list for sale:"
        : "You don't have any sellable items in your inventory."
    );

  const components = [];

  if (sellableItems.length > 0) {
    // create pages if there are many items
    const itemsPerPage = 25;
    const pageItems = sellableItems.slice(0, itemsPerPage);

    const selectMenu = new StringSelectMenuBuilder()
      .setCustomId("market_sell_select")
      .setPlaceholder("Choose an item to sell...")
      .addOptions(
        pageItems.map((item) => ({
          label: `${item.name} (${item.quantity.toLocaleString()})`,
          description: `List ${item.name} for sale`,
          value: item.itemKey,
          emoji: item.emoji,
        }))
      );

    const selectRow = new ActionRowBuilder().addComponents(selectMenu);
    components.push(selectRow);
  }

  const controlRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId("market_back")
      .setLabel("Back to Menu")
      .setStyle(ButtonStyle.Secondary)
  );
  components.push(controlRow);

  await interaction.editReply({
    embeds: [embed],
    components: components,
  });

  // handle item selection for selling
  if (sellableItems.length > 0) {
    const message = await interaction.fetchReply();
    const collector = message.createMessageComponentCollector({
      filter: (i) =>
        i.user.id === interaction.user.id &&
        i.customId === "market_sell_select",
      time: 300000,
    });

    collector.on("collect", async (i) => {
      const itemKey = i.values[0];
      await handleItemSale(i, character, itemKey);
    });
  }
}

/**
 * handles setting up a sale for a specific item
 */
async function handleItemSale(interaction, character, itemKey) {
  // get player's inventory using proper inventory function
  const playerInventory = await getInventory(interaction.user.id);
  const inventory = playerInventory.items || {};
  const availableQuantity = inventory[itemKey] || 0;

  if (availableQuantity <= 0) {
    return interaction.reply({
      content: "You no longer have this item in your inventory.",
      flags: [MessageFlags.Ephemeral],
    });
  }

  const allItems = configManager.getAllItems();
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;

  // show selling modal
  const modal = new ModalBuilder()
    .setCustomId(`market_list_item:${itemKey}`)
    .setTitle(
      `List ${itemName.length > 35 ? itemName.substring(0, 30) + "..." : itemName} for Sale`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel(`Quantity (max: ${availableQuantity.toLocaleString()})`)
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setPlaceholder("Enter amount or 'all'");

  const priceInput = new TextInputBuilder()
    .setCustomId("price")
    .setLabel("Price per unit (in coins)")
    .setStyle(TextInputStyle.Short)
    .setRequired(true)
    .setPlaceholder("Enter price per item");

  const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
  const priceRow = new ActionRowBuilder().addComponents(priceInput);
  modal.addComponents(quantityRow, priceRow);

  await interaction.showModal(modal);

  // handle modal submission
  try {
    const modalSubmission = await interaction.awaitModalSubmit({
      filter: (i) =>
        i.customId === `market_list_item:${itemKey}` &&
        i.user.id === interaction.user.id,
      time: 120000,
    });

    const quantityStr = modalSubmission.fields.getTextInputValue("quantity");
    const priceStr = modalSubmission.fields.getTextInputValue("price");

    const quantity = parseShorthandAmount(quantityStr, availableQuantity);
    const price = parsePrice(priceStr);

    if (isNaN(quantity) || quantity <= 0 || quantity > availableQuantity) {
      return modalSubmission.reply({
        content: `Invalid quantity. Please enter a number between 1 and ${availableQuantity.toLocaleString()}.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (isNaN(price) || price <= 0) {
      return modalSubmission.reply({
        content:
          "Invalid price. Please enter a positive number with up to 1 decimal place.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (price > MARKET_LIMITS.MAX_PRICE_PER_ITEM) {
      return modalSubmission.reply({
        content: `Price too high! Maximum price per item is ${formatPrice(MARKET_LIMITS.MAX_PRICE_PER_ITEM)} coins.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // process the listing
    await processItemListing(
      modalSubmission,
      character,
      itemKey,
      quantity,
      price,
      itemData
    );
  } catch (error) {
    console.error("[Market Sell] Modal timeout or error:", error);
  }
}

/**
 * processes creating a new market listing
 */
async function processItemListing(
  interaction,
  character,
  itemKey,
  quantity,
  pricePerUnit,
  itemData
) {
  await interaction.deferUpdate();

  try {
    // check if player still has the items using proper inventory function
    const updatedInventory = await getInventory(character.discordId);
    const currentQuantity = updatedInventory.items?.[itemKey] || 0;

    if (currentQuantity < quantity) {
      return interaction.editReply({
        content: `You only have ${currentQuantity.toLocaleString()} of this item available.`,
      });
    }

    const queries = [];

    // remove items from player's inventory
    const newQuantity = currentQuantity - quantity;
    if (newQuantity > 0) {
      queries.push({
        sql: "UPDATE player_inventory_items SET amount = ? WHERE discord_id = ? AND item_name = ?",
        params: [newQuantity, character.discordId, itemKey],
      });
    } else {
      queries.push({
        sql: "DELETE FROM player_inventory_items WHERE discord_id = ? AND item_name = ?",
        params: [character.discordId, itemKey],
      });
    }

    // create market listing
    queries.push({
      sql: `INSERT INTO market_listings (seller_id, item_key, quantity, price_per_unit, created_at)
            VALUES (?, ?, ?, ?, datetime('now'))`,
      params: [character.discordId, itemKey, quantity, pricePerUnit],
    });

    // execute atomically via the write queue
    await Promise.all(queries.map((q) => dbRunQueued(q.sql, q.params)));

    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "❓";
    const totalValue = quantity * pricePerUnit;

    await interaction.editReply({
      content:
        `✅ **Item Listed Successfully!**\n\n` +
        `${itemEmoji} Listed **${quantity.toLocaleString()}x ${itemName}**\n` +
        `💰 Price: **${formatPrice(pricePerUnit)} coins each**\n` +
        `📊 Total Value: **${totalValue.toLocaleString()} coins**\n\n` +
        `Your item is now available for other players to purchase!`,
    });

    console.log(
      `[Market] ${character.name} listed ${quantity}x ${itemKey} for ${pricePerUnit} coins each`
    );
  } catch (error) {
    console.error("[Market] Error creating listing:", error);
    await interaction.editReply({
      content:
        "An error occurred while creating your listing. Please try again.",
    });
  }
}

/**
 * handles listing management (cancel listing)
 */
async function handleListingManagement(interaction, character, listingId) {
  const listing = await dbGet(
    "SELECT * FROM market_listings WHERE id = ? AND seller_id = ?",
    [listingId, character.discordId]
  );

  if (!listing) {
    return interaction.reply({
      content: "Listing not found or you don't own this listing.",
      flags: [MessageFlags.Ephemeral],
    });
  }

  const allItems = configManager.getAllItems();
  const itemData = allItems[listing.item_key];
  const itemName = itemData?.name || listing.item_key;

  // confirm cancellation
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.ORANGE)
    .setTitle("🗑️ Cancel Listing")
    .setDescription(
      `Are you sure you want to cancel this listing?\n\n` +
        `**Item:** ${itemData?.emoji || "❓"} ${itemName}\n` +
        `**Quantity:** ${listing.quantity.toLocaleString()}\n` +
        `**Price:** ${formatPrice(listing.price_per_unit)} coins each\n\n` +
        `The items will be returned to your inventory.`
    );

  const buttons = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`confirm_cancel:${listingId}`)
      .setLabel("Cancel Listing")
      .setStyle(ButtonStyle.Danger),
    new ButtonBuilder()
      .setCustomId("market_listings")
      .setLabel("Keep Listing")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.reply({
    embeds: [embed],
    components: [buttons],
    flags: [MessageFlags.Ephemeral],
  });

  // handle confirmation
  const message = await interaction.fetchReply();
  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId.startsWith("confirm_cancel:") ||
        i.customId === "market_listings"),
    time: 60000,
  });

  collector.on("collect", async (i) => {
    if (i.customId.startsWith("confirm_cancel:")) {
      await processCancelListing(i, character, listingId);
    } else {
      await i.deferUpdate();
      await showPlayerListings(i, character);
    }
  });
}

/**
 * processes canceling a market listing
 */
async function processCancelListing(interaction, character, listingId) {
  await interaction.deferUpdate();

  try {
    const listing = await dbGet(
      "SELECT * FROM market_listings WHERE id = ? AND seller_id = ?",
      [listingId, character.discordId]
    );

    if (!listing) {
      return interaction.followUp({
        content: "Listing not found or already removed.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    const queries = [];

    // return items to inventory
    queries.push({
      sql: `INSERT INTO player_inventory_items (discord_id, item_name, amount)
            VALUES (?, ?, ?)
            ON CONFLICT(discord_id, item_name) DO UPDATE SET amount = amount + excluded.amount`,
      params: [character.discordId, listing.item_key, listing.quantity],
    });

    // remove listing
    queries.push({
      sql: "DELETE FROM market_listings WHERE id = ?",
      params: [listingId],
    });

    await Promise.all(queries.map((q) => dbRunQueued(q.sql, q.params)));

    const allItems = configManager.getAllItems();
    const itemData = allItems[listing.item_key];
    const itemName = itemData?.name || listing.item_key;

    await interaction.followUp({
      content:
        `✅ **Listing Canceled**\n\n` +
        `${itemData?.emoji || "❓"} Returned **${listing.quantity.toLocaleString()}x ${itemName}** to your inventory.`,
      flags: [MessageFlags.Ephemeral],
    });

    // refresh listings view
    await showPlayerListings(interaction, character);
  } catch (error) {
    console.error("[Market] Error canceling listing:", error);
    await interaction.followUp({
      content: "An error occurred while canceling your listing.",
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * handles specific market actions like buying items
 */
async function handleSpecificMarketAction(interaction, character) {
  // check if interaction is still valid before processing
  if (interaction.replied || interaction.deferred) {
    console.log(
      "[Market] Interaction already acknowledged in handleSpecificMarketAction"
    );
    return;
  }

  try {
    // handle button interactions for instant trading
    if (interaction.customId.startsWith("market_instant_buy:")) {
      const itemKey = interaction.customId.split(":")[1];
      await handleInstantBuy(interaction, character, itemKey);
    } else if (interaction.customId.startsWith("market_instant_sell:")) {
      const itemKey = interaction.customId.split(":")[1];
      await handleInstantSell(interaction, character, itemKey);
    } else if (interaction.customId.startsWith("market_create_buy_order:")) {
      // reformat the customId to match the expected format for handleCreateBuyOrder
      const itemKey = interaction.customId.split(":")[1];
      interaction.customId = `create_buy_order:${itemKey}`;
      await handleCreateBuyOrder(interaction, character);
    } else if (interaction.customId.startsWith("market_create_sell_order:")) {
      // reformat the customId to match the expected format for handleCreateSellOrder
      const itemKey = interaction.customId.split(":")[1];
      interaction.customId = `create_sell_order:${itemKey}`;
      await handleCreateSellOrder(interaction, character);
    } else if (interaction.customId.startsWith("market_orderbook:")) {
      const itemKey = interaction.customId.split(":")[1];
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate();
      }
      await showOrderBook(interaction, character, itemKey);
    } else if (interaction.customId === "market_search") {
      await handleMarketSearch(interaction, character);
    }
    // handle quick trade buttons from main menu
    else if (interaction.customId === "market_quick_buy") {
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate();
      }
      await showQuickBuyMenu(interaction, character);
    } else if (interaction.customId === "market_quick_sell") {
      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate();
      }
      await showQuickSellMenu(interaction, character);
    }
    // handle modal submissions for instant trading
    else if (interaction.customId.startsWith("instant_buy_modal:")) {
      await handleInstantBuyModal(interaction, character);
    } else if (interaction.customId.startsWith("instant_sell_modal:")) {
      await handleInstantSellModal(interaction, character);
    } else if (interaction.customId.startsWith("create_buy_order:")) {
      await handleCreateBuyOrder(interaction, character);
    } else if (interaction.customId.startsWith("create_sell_order:")) {
      await handleCreateSellOrder(interaction, character);
    } else if (interaction.customId === "market_search_modal") {
      await handleSearchModal(interaction, character);
    } else if (interaction.customId.startsWith("buy_order_modal:")) {
      await handleBuyOrderModal(interaction, character);
    } else if (interaction.customId.startsWith("sell_order_modal:")) {
      await handleSellOrderModal(interaction, character);
    } else if (interaction.customId === "create_buy_order_modal") {
      await handleCreateBuyOrderModal(interaction, character);
    } else if (interaction.customId.startsWith("create_sell_order_modal:")) {
      await handleCreateSellOrderModal(interaction, character);
    }
    // handle search pagination
    else if (interaction.customId.startsWith("market_search_")) {
      const [action, currentPage, searchQuery] =
        interaction.customId.split(":");
      const page = parseInt(currentPage);
      const newPage = action === "market_search_prev" ? page - 1 : page + 1;

      if (!interaction.deferred && !interaction.replied) {
        await interaction.deferUpdate();
      }
      await showSearchResults(
        interaction,
        character,
        newPage,
        searchQuery || ""
      );
    } else {
      console.log(`[Market] Unhandled interaction: ${interaction.customId}`);
    }
  } catch (error) {
    console.error(
      `[Market] Error in handleSpecificMarketAction for ${interaction.customId}:`,
      error
    );
    throw error;
  }
}

/**
 * handles instant buy modal submission
 */
async function handleInstantBuyModal(interaction, character) {
  await interaction.deferUpdate();

  const [, itemKey] = interaction.customId.split(":");
  const quantity = parseInt(interaction.fields.getTextInputValue("quantity"));

  if (isNaN(quantity) || quantity <= 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ Invalid Quantity")
      .setDescription("Please enter a positive number for the quantity.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`market_instant_buy:${itemKey}`)
        .setLabel("Try Again")
        .setStyle(ButtonStyle.Primary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  await showInstantBuyConfirmation(interaction, character, itemKey, quantity);
}

/**
 * shows confirmation screen for instant buy
 */
async function showInstantBuyConfirmation(
  interaction,
  character,
  itemKey,
  requestedQuantity
) {
  // get all available sell orders for this item
  const availableSells = await dbAll(
    "SELECT * FROM market_sell_orders WHERE item_key = ? AND quantity > 0 ORDER BY price_per_unit ASC",
    [itemKey]
  );

  if (availableSells.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ No Orders Available")
      .setDescription("No sell orders are currently available for this item.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  // calculate what we can actually buy and the cost
  let remainingQuantity = requestedQuantity;
  let totalCost = 0;
  let actualQuantity = 0;
  const trades = [];

  for (const sellOrder of availableSells) {
    if (remainingQuantity <= 0) break;
    if (sellOrder.seller_id === character.discordId) continue; // can't buy from yourself

    const tradeQuantity = Math.min(remainingQuantity, sellOrder.quantity);
    const tradeCost = tradeQuantity * sellOrder.price_per_unit;

    trades.push({ sellOrder, quantity: tradeQuantity, cost: tradeCost });
    remainingQuantity -= tradeQuantity;
    totalCost += tradeCost;
    actualQuantity += tradeQuantity;
  }

  if (actualQuantity === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ No Items Available")
      .setDescription(
        "No items are available for purchase from other players."
      );

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  // check if player has enough coins
  const playerCoins = character.coins || 0;
  const canAfford = playerCoins >= totalCost;

  const allItems = configManager.getAllItems();
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;
  const itemEmoji = itemData?.emoji || "❓";

  // build confirmation embed
  const embed = new EmbedBuilder()
    .setColor(canAfford ? EMBED_COLORS.GREEN : EMBED_COLORS.RED)
    .setTitle(`${itemEmoji} ${itemName}`)
    .setDescription(
      `Do you want to buy **${actualQuantity.toLocaleString()}x ${itemName}** for **${formatPrice(totalCost)} coins**?`
    );

  // warn if partial fill
  if (actualQuantity < requestedQuantity) {
    embed.addFields({
      name: "⚠️ Partial Fill",
      value: `Only ${actualQuantity.toLocaleString()} of ${requestedQuantity.toLocaleString()} items are available.`,
      inline: false,
    });
  }

  // warn if can't afford
  if (!canAfford) {
    embed.addFields({
      name: "❌ Insufficient Coins",
      value: `You need ${formatPrice(totalCost - playerCoins)} more coins to complete this purchase.`,
      inline: false,
    });
  }

  // create confirmation token for security
  const confirmationToken = createConfirmationToken(
    character.discordId,
    "instant_buy",
    { itemKey, quantity: actualQuantity, totalCost }
  );

  // create buttons
  const actionRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`confirm_instant_buy:${confirmationToken}`)
      .setLabel("Confirm Purchase")
      .setStyle(ButtonStyle.Success)
      .setDisabled(!canAfford),
    new ButtonBuilder()
      .setCustomId(`back_to_orderbook:${itemKey}`)
      .setLabel("Back")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [actionRow],
  });

  // handle confirmation buttons
  const message = await interaction.fetchReply();

  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId.startsWith("confirm_instant_buy:") ||
        i.customId.startsWith("back_to_orderbook:")),
    time: 300000,
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      if (i.customId.startsWith("confirm_instant_buy:")) {
        const token = i.customId.split(":")[1];
        await handleConfirmedInstantBuy(i, character, token);
      } else if (i.customId.startsWith("back_to_orderbook:")) {
        const targetItemKey = i.customId.split(":")[1];
        await i.deferUpdate();
        await showOrderBook(i, character, targetItemKey);
      }
    } catch (error) {
      console.error("[Instant Buy Confirmation] Error:", error);
    }
  });

  collector.on("end", () => {
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * handles confirmed instant buy after user clicks confirm
 */
async function handleConfirmedInstantBuy(
  interaction,
  character,
  confirmationToken
) {
  await interaction.deferUpdate();

  try {
    // validate and consume the confirmation token
    const tokenData = validateAndConsumeToken(confirmationToken);

    if (tokenData.userId !== character.discordId) {
      return interaction.editReply({
        content: "❌ Invalid confirmation token - user mismatch.",
      });
    }

    if (tokenData.action !== "instant_buy") {
      return interaction.editReply({
        content: "❌ Invalid confirmation token - action mismatch.",
      });
    }

    const { itemKey, quantity, totalCost } = tokenData.params;

    // check rate limiting (prevent spam)
    checkRateLimit(character.discordId, "instant_buy", 2000); // 2 second cooldown

    // re-fetch fresh data to ensure orders are still available
    const result = await processInstantBuyTransaction(
      character,
      itemKey,
      quantity,
      totalCost,
      interaction.client
    );

    if (!result.success) {
      return interaction.editReply({
        content: result.error,
      });
    }

    const allItems = configManager.getAllItems();
    const itemData = allItems[itemKey];
    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "❓";

    // create success embed
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Purchase Complete")
      .setDescription(
        `Successfully bought **${result.itemsTransferred.toLocaleString()}x** ${itemEmoji} **${itemName}**\n` +
          `for **${formatPrice(result.totalCost)} coins**`
      );

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    await interaction.editReply({ embeds: [embed], components: [backButton] });

    // set up collector to handle the "Back to Market" button
    await setupMarketCollector(interaction, character);
  } catch (error) {
    console.error("[Confirmed Instant Buy] Error:", error);

    // handle specific error types
    if (error.message.includes("wait before performing")) {
      return interaction.editReply({
        content: "⏰ " + error.message,
      });
    }

    if (error.message.includes("confirmation token")) {
      return interaction.editReply({
        content: error.message,
      });
    }

    await interaction.editReply({
      content: "An error occurred while processing your purchase.",
    });
  }
}

/**
 * handles instant sell modal submission
 */
async function handleInstantSellModal(interaction, character) {
  await interaction.deferUpdate();

  const [, itemKey] = interaction.customId.split(":");
  const quantity = parseInt(interaction.fields.getTextInputValue("quantity"));

  if (isNaN(quantity) || quantity <= 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ Invalid Quantity")
      .setDescription("Please enter a positive number for the quantity.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`market_instant_sell:${itemKey}`)
        .setLabel("Try Again")
        .setStyle(ButtonStyle.Primary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  await showInstantSellConfirmation(interaction, character, itemKey, quantity);
}

/**
 * shows confirmation screen for instant sell
 */
async function showInstantSellConfirmation(
  interaction,
  character,
  itemKey,
  requestedQuantity
) {
  // get all available buy orders for this item (highest price first)
  const availableBuys = await dbAll(
    "SELECT * FROM market_buy_orders WHERE item_key = ? AND quantity > 0 AND buyer_id != ? ORDER BY price_per_unit DESC",
    [itemKey, character.discordId]
  );

  if (availableBuys.length === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ No Orders Available")
      .setDescription("No buy orders are currently available for this item.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  // check player inventory
  const playerInventory = await getInventory(character.discordId);
  const playerInv = playerInventory.items || {};
  const playerAmount = playerInv[itemKey] || 0;

  if (playerAmount === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ No Items to Sell")
      .setDescription("You don't have any of this item to sell.");

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  // calculate what we can actually sell and the revenue
  const maxCanSell = Math.min(requestedQuantity, playerAmount);
  let remainingQuantity = maxCanSell;
  let totalRevenue = 0;
  let actualQuantity = 0;
  const trades = [];

  for (const buyOrder of availableBuys) {
    if (remainingQuantity <= 0) break;

    const tradeQuantity = Math.min(remainingQuantity, buyOrder.quantity);
    const tradeRevenue = tradeQuantity * buyOrder.price_per_unit;

    trades.push({ buyOrder, quantity: tradeQuantity, revenue: tradeRevenue });
    remainingQuantity -= tradeQuantity;
    totalRevenue += tradeRevenue;
    actualQuantity += tradeQuantity;
  }

  if (actualQuantity === 0) {
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.ERROR)
      .setTitle("❌ No Orders Available")
      .setDescription(
        "No buy orders are available for this item from other players."
      );

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Menu")
        .setStyle(ButtonStyle.Secondary)
    );

    return interaction.editReply({
      embeds: [embed],
      components: [backButton],
    });
  }

  const allItems = configManager.getAllItems();
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;
  const itemEmoji = itemData?.emoji || "❓";

  // build confirmation embed
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GREEN)
    .setTitle(`${itemEmoji} ${itemName}`)
    .setDescription(
      `Do you want to sell **${actualQuantity.toLocaleString()}x ${itemName}** for **${totalRevenue.toLocaleString()} coins**?`
    );

  // warn if partial fill
  if (actualQuantity < requestedQuantity) {
    embed.addFields({
      name: "⚠️ Partial Fill",
      value: `Only ${actualQuantity.toLocaleString()} of ${requestedQuantity.toLocaleString()} items can be sold to available orders.`,
      inline: false,
    });
  }

  // create confirmation token for security
  const confirmationToken = createConfirmationToken(
    character.discordId,
    "instant_sell",
    { itemKey, quantity: actualQuantity, totalRevenue }
  );

  // create buttons
  const actionRow = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`confirm_instant_sell:${confirmationToken}`)
      .setLabel("Confirm Sale")
      .setStyle(ButtonStyle.Success),
    new ButtonBuilder()
      .setCustomId(`back_to_orderbook:${itemKey}`)
      .setLabel("Back")
      .setStyle(ButtonStyle.Secondary)
  );

  await interaction.editReply({
    embeds: [embed],
    components: [actionRow],
  });

  // handle confirmation buttons
  const message = await interaction.fetchReply();

  // stop any existing collector for this user
  const existingCollector = activeCollectors.get(interaction.user.id);
  if (existingCollector) {
    existingCollector.stop();
  }

  const collector = message.createMessageComponentCollector({
    filter: (i) =>
      i.user.id === interaction.user.id &&
      (i.customId.startsWith("confirm_instant_sell:") ||
        i.customId.startsWith("back_to_orderbook:")),
    time: 300000,
  });

  // store the collector
  activeCollectors.set(interaction.user.id, collector);

  collector.on("collect", async (i) => {
    try {
      if (i.customId.startsWith("confirm_instant_sell:")) {
        const token = i.customId.split(":")[1];
        await handleConfirmedInstantSell(i, character, token);
      } else if (i.customId.startsWith("back_to_orderbook:")) {
        const targetItemKey = i.customId.split(":")[1];
        await i.deferUpdate();
        await showOrderBook(i, character, targetItemKey);
      }
    } catch (error) {
      console.error("[Instant Sell Confirmation] Error:", error);
    }
  });

  collector.on("end", () => {
    activeCollectors.delete(interaction.user.id);
  });
}

/**
 * handles confirmed instant sell after user clicks confirm
 */
async function handleConfirmedInstantSell(
  interaction,
  character,
  confirmationToken
) {
  await interaction.deferUpdate();

  try {
    // validate and consume the confirmation token
    const tokenData = validateAndConsumeToken(confirmationToken);

    if (tokenData.userId !== character.discordId) {
      return interaction.editReply({
        content: "❌ Invalid confirmation token - user mismatch.",
      });
    }

    if (tokenData.action !== "instant_sell") {
      return interaction.editReply({
        content: "❌ Invalid confirmation token - action mismatch.",
      });
    }

    const { itemKey, quantity, totalRevenue } = tokenData.params;

    // check rate limiting (prevent spam)
    checkRateLimit(character.discordId, "instant_sell", 2000); // 2 second cooldown

    // re-fetch fresh data to ensure orders are still available
    const result = await processInstantSellTransaction(
      character,
      itemKey,
      quantity,
      totalRevenue,
      interaction.client
    );

    if (!result.success) {
      return interaction.editReply({
        content: result.error,
      });
    }

    const allItems = configManager.getAllItems();
    const itemData = allItems[itemKey];
    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "❓";

    // create success embed
    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Sale Complete")
      .setDescription(
        `Successfully sold **${result.itemsTransferred.toLocaleString()}x** ${itemEmoji} **${itemName}**\n` +
          `for **${formatPrice(result.totalCost)} coins**`
      );

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    await interaction.editReply({ embeds: [embed], components: [backButton] });

    // set up collector to handle the "Back to Market" button
    await setupMarketCollector(interaction, character);
  } catch (error) {
    console.error("[Confirmed Instant Sell] Error:", error);

    // handle specific error types
    if (error.message.includes("wait before performing")) {
      return interaction.editReply({
        content: "⏰ " + error.message,
      });
    }

    if (error.message.includes("confirmation token")) {
      return interaction.editReply({
        content: error.message,
      });
    }

    await interaction.editReply({
      content: "An error occurred while processing your sale.",
    });
  }
}

/**
 * handles search modal submission
 */
async function handleSearchModal(interaction, character) {
  await interaction.deferUpdate();
  const searchQuery = interaction.fields.getTextInputValue("search_query");
  await showSearchResults(interaction, character, 0, searchQuery);
}

/**
 * executes an instant buy transaction and returns result
 */
async function processInstantBuyTransaction(
  buyer,
  itemKey,
  requestedQuantity,
  expectedCost,
  client = null
) {
  console.log(
    `[Market] Starting instant buy transaction: ${buyer.name} buying ${requestedQuantity}x ${itemKey} for ~${expectedCost} coins`
  );

  try {
    const allItems = configManager.getAllItems();
    if (allItems[itemKey]?.untradeable) {
      return {
        success: false,
        error: "This item cannot be traded on the market.",
      };
    }
    const buyerId = buyer.discord_id || buyer.discordId;

    // create user-item specific lock to prevent concurrent buys of same item
    const lockKey = createUserItemLock(buyerId, "instant_buy", itemKey);

    // process the transaction with proper locking
    const result = await withMarketLock(lockKey, async () => {
      // get best available sell orders (fresh fetch inside lock)
      const availableSells = await dbAll(
        `SELECT * FROM market_sell_orders 
         WHERE item_key = ? AND quantity > 0 AND seller_id != ?
         ORDER BY price_per_unit ASC`,
        [itemKey, buyerId]
      );

      if (availableSells.length === 0) {
        return {
          success: false,
          error: "No sell orders available for this item.",
        };
      }

      let totalQuantityBought = 0;
      let totalSpent = 0;
      let remainingQuantity = requestedQuantity;

      // process orders in sequence
      for (const sellOrder of availableSells) {
        if (remainingQuantity <= 0) break;

        const quantityFromThisOrder = Math.min(
          remainingQuantity,
          sellOrder.quantity
        );
        const costFromThisOrder =
          quantityFromThisOrder * sellOrder.price_per_unit;

        // call the utils function for this specific transaction
        const transactionResult = await executeInstantBuyTransaction(
          buyerId,
          sellOrder.seller_id,
          itemKey,
          quantityFromThisOrder,
          costFromThisOrder,
          sellOrder.id,
          client
        );

        if (!transactionResult.success) {
          console.error(
            `[Market] Failed to process sell order ${sellOrder.id}:`,
            transactionResult.error
          );
          continue; // try next order if this one fails
        }

        // the transaction result has nested data in .result
        totalQuantityBought += transactionResult.result.itemsTransferred;
        totalSpent += transactionResult.result.totalCost;
        remainingQuantity -= quantityFromThisOrder;
      }

      return {
        success: true,
        itemsTransferred: totalQuantityBought,
        totalCost: totalSpent,
      };
    });

    if (result.success) {
      console.log(
        `[Market] Instant buy completed: ${buyer.name} bought ${result.itemsTransferred}x ${itemKey} for ${result.totalCost} coins total`
      );
    } else {
      console.log(`[Market] Instant buy failed: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error("[Instant Buy Transaction] Error:", error);
    return {
      success: false,
      error: "An error occurred while processing the transaction.",
    };
  }
}

/**
 * executes an instant sell transaction and returns result
 */
async function processInstantSellTransaction(
  seller,
  itemKey,
  requestedQuantity,
  expectedRevenue,
  client = null
) {
  console.log(
    `[Market] Starting instant sell transaction: ${seller.name} selling ${requestedQuantity}x ${itemKey} for ~${expectedRevenue} coins`
  );

  try {
    const allItems = configManager.getAllItems();
    if (allItems[itemKey]?.untradeable) {
      return {
        success: false,
        error: "This item cannot be traded on the market.",
      };
    }
    const sellerId = seller.discord_id || seller.discordId;

    // create user-item specific lock to prevent concurrent sells of same item
    const lockKey = createUserItemLock(sellerId, "instant_sell", itemKey);

    // process the transaction with proper locking
    const result = await withMarketLock(lockKey, async () => {
      // get best available buy orders (fresh fetch inside lock)
      const availableBuys = await dbAll(
        `SELECT * FROM market_buy_orders 
         WHERE item_key = ? AND quantity > 0 AND buyer_id != ?
         ORDER BY price_per_unit DESC`,
        [itemKey, sellerId]
      );

      if (availableBuys.length === 0) {
        return {
          success: false,
          error: "No buy orders available for this item.",
        };
      }

      let totalQuantitySold = 0;
      let totalEarned = 0;
      let remainingQuantity = requestedQuantity;

      // process orders in sequence
      for (const buyOrder of availableBuys) {
        if (remainingQuantity <= 0) break;

        const quantityToThisOrder = Math.min(
          remainingQuantity,
          buyOrder.quantity
        );
        const revenueFromThisOrder =
          quantityToThisOrder * buyOrder.price_per_unit;

        // call the utils function for this specific transaction
        const transactionResult = await executeInstantSellTransaction(
          sellerId,
          buyOrder.buyer_id,
          itemKey,
          quantityToThisOrder,
          revenueFromThisOrder,
          buyOrder.id,
          client
        );

        if (!transactionResult.success) {
          console.error(
            `[Market] Failed to process buy order ${buyOrder.id}:`,
            transactionResult.error
          );
          continue; // try next order if this one fails
        }

        // the transaction result has nested data in .result
        totalQuantitySold += transactionResult.result.itemsTransferred;
        totalEarned += transactionResult.result.totalCost;
        remainingQuantity -= quantityToThisOrder;
      }

      return {
        success: true,
        itemsTransferred: totalQuantitySold,
        totalCost: totalEarned,
      };
    });

    if (result.success) {
      console.log(
        `[Market] Instant sell completed: ${seller.name} sold ${result.itemsTransferred}x ${itemKey} for ${result.totalCost} coins total`
      );
    } else {
      console.log(`[Market] Instant sell failed: ${result.error}`);
    }

    return result;
  } catch (error) {
    console.error("[Instant Sell Transaction] Error:", error);
    return {
      success: false,
      error: "An error occurred while processing the transaction.",
    };
  }
}

/**
 * handles creating a buy order
 */
async function handleCreateBuyOrder(interaction, _character) {
  const itemKey = interaction.customId.split(":")[1];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.reply({
      content: "This item cannot be traded on the market.",
      flags: [MessageFlags.Ephemeral],
    });
  }
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;

  const modal = new ModalBuilder()
    .setCustomId(`buy_order_modal:${itemKey}`)
    .setTitle(
      `Buy: ${itemName.length > 40 ? itemName.substring(0, 37) + "..." : itemName}`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel("Quantity")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("How many do you want to buy?")
    .setRequired(true);

  const priceInput = new TextInputBuilder()
    .setCustomId("price")
    .setLabel("Price per unit (coins)")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("Maximum price you'll pay per item")
    .setRequired(true);

  const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
  const priceRow = new ActionRowBuilder().addComponents(priceInput);
  modal.addComponents(quantityRow, priceRow);

  await interaction.showModal(modal);
}

/**
 * handles creating a sell order
 */
async function handleCreateSellOrder(interaction, _character) {
  const itemKey = interaction.customId.split(":")[1];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.reply({
      content: "This item cannot be traded on the market.",
      flags: [MessageFlags.Ephemeral],
    });
  }
  const itemData = allItems[itemKey];
  const itemName = itemData?.name || itemKey;

  // check if player has the item using proper inventory function
  const playerInventory = await getInventory(interaction.user.id);
  const playerInv = playerInventory.items || {};
  const playerAmount = playerInv[itemKey] || 0;

  if (playerAmount === 0) {
    return interaction.reply({
      content: "You don't have any of this item to sell.",
      flags: [MessageFlags.Ephemeral],
    });
  }

  const modal = new ModalBuilder()
    .setCustomId(`sell_order_modal:${itemKey}`)
    .setTitle(
      `Sell: ${itemName.length > 39 ? itemName.substring(0, 36) + "..." : itemName}`
    );

  const quantityInput = new TextInputBuilder()
    .setCustomId("quantity")
    .setLabel(`Quantity (Max: ${playerAmount.toLocaleString()})`)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("How many do you want to sell?")
    .setRequired(true);

  const priceInput = new TextInputBuilder()
    .setCustomId("price")
    .setLabel("Price per unit (coins)")
    .setStyle(TextInputStyle.Short)
    .setPlaceholder("Minimum price you'll accept per item")
    .setRequired(true);

  const quantityRow = new ActionRowBuilder().addComponents(quantityInput);
  const priceRow = new ActionRowBuilder().addComponents(priceInput);
  modal.addComponents(quantityRow, priceRow);

  await interaction.showModal(modal);
}

// Additional order processing functions
async function handleBuyOrderModal(interaction, character) {
  await interaction.deferUpdate();

  const itemKey = interaction.customId.split(":")[1];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.editReply({
      content: "This item cannot be traded on the market.",
    });
  }
  const quantity = parseInt(interaction.fields.getTextInputValue("quantity"));
  const pricePerUnit = parsePrice(
    interaction.fields.getTextInputValue("price")
  );

  if (isNaN(quantity) || quantity <= 0) {
    return interaction.editReply({
      content: "Invalid quantity. Please enter a positive number.",
    });
  }

  if (isNaN(pricePerUnit) || pricePerUnit <= 0) {
    return interaction.editReply({
      content:
        "Invalid price. Please enter a positive number with up to 1 decimal place.",
    });
  }

  const totalCost = quantity * pricePerUnit;

  if ((character.coins || 0) < totalCost) {
    return interaction.editReply({
      content: `You need ${formatPrice(totalCost)} coins but only have ${formatPrice(character.coins || 0)}.`,
    });
  }

  await createBuyOrder(interaction, character, itemKey, quantity, pricePerUnit);
}

async function handleSellOrderModal(interaction, character) {
  await interaction.deferUpdate();

  const itemKey = interaction.customId.split(":")[1];
  const allItems = configManager.getAllItems();
  if (allItems[itemKey]?.untradeable) {
    return interaction.editReply({
      content: "This item cannot be traded on the market.",
    });
  }
  const quantity = parseInt(interaction.fields.getTextInputValue("quantity"));
  const pricePerUnit = parsePrice(
    interaction.fields.getTextInputValue("price")
  );

  if (isNaN(quantity) || quantity <= 0) {
    return interaction.editReply({
      content: "Invalid quantity. Please enter a positive number.",
    });
  }

  if (isNaN(pricePerUnit) || pricePerUnit <= 0) {
    return interaction.editReply({
      content:
        "Invalid price. Please enter a positive number with up to 1 decimal place.",
    });
  }

  if (pricePerUnit > MARKET_LIMITS.MAX_PRICE_PER_ITEM) {
    return interaction.editReply({
      content: `Price too high! Maximum price per item is ${formatPrice(MARKET_LIMITS.MAX_PRICE_PER_ITEM)} coins.`,
    });
  }

  // use proper inventory function to get current inventory
  const playerInventory = await getInventory(interaction.user.id);
  const playerInv = playerInventory.items || {};
  const playerAmount = playerInv[itemKey] || 0;

  if (playerAmount < quantity) {
    return interaction.editReply({
      content: `You only have ${playerAmount.toLocaleString()} of this item but tried to sell ${quantity.toLocaleString()}.`,
    });
  }

  await createSellOrder(
    interaction,
    character,
    itemKey,
    quantity,
    pricePerUnit
  );
}

async function createBuyOrder(
  interaction,
  buyer,
  itemKey,
  quantity,
  pricePerUnit
) {
  const { updateCurrencyAtomically } = require("../utils/playerDataManager");

  try {
    // check rate limiting (prevent spam order creation)
    checkRateLimit(buyer.discordId, "create_buy_order", 3000); // 3 second cooldown

    // check buy order limit (8 max)
    const currentBuyOrders = await dbAll(
      "SELECT COUNT(*) as count FROM market_buy_orders WHERE buyer_id = ? AND quantity > 0",
      [buyer.discordId]
    );

    if (currentBuyOrders[0]?.count >= MARKET_LIMITS.MAX_BUY_ORDERS) {
      return interaction.editReply({
        content: `❌ You can only have a maximum of **${MARKET_LIMITS.MAX_BUY_ORDERS} active buy orders**. Cancel some existing orders first.`,
      });
    }

    const totalCost = quantity * pricePerUnit;

    // deduct coins atomically for the buy order
    const updateResult = await updateCurrencyAtomically(
      buyer.discordId,
      -totalCost,
      0
    );
    if (!updateResult) {
      return interaction.editReply({
        content: `❌ Insufficient coins or transaction failed. You need **${formatPrice(totalCost)} coins** but only have **${formatPrice(buyer.coins || 0)} coins**.`,
      });
    }

    // create the buy order in database
    const result = await dbRunQueued(
      "INSERT INTO market_buy_orders (buyer_id, item_key, quantity, price_per_unit, total_coins_locked, original_quantity, created_at) VALUES (?, ?, ?, ?, ?, ?, datetime('now'))",
      [buyer.discordId, itemKey, quantity, pricePerUnit, totalCost, quantity]
    );

    const buyOrderId = result.lastID;

    // now process automatic matching for this new buy order
    const matchingResult = await processNewBuyOrder(
      buyOrderId,
      buyer.discordId,
      itemKey,
      quantity,
      pricePerUnit,
      interaction.client
    );

    const allItems = configManager.getAllItems();
    const itemData = allItems[itemKey];
    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "❓";

    let description =
      `Created Buy Order for **${quantity.toLocaleString()}x** ${itemEmoji} **${itemName}**\n` +
      `at **${formatPrice(pricePerUnit)} coins** each (**${formatPrice(totalCost)} total**)`;

    if (matchingResult.matches > 0) {
      const totalMatched = matchingResult.completedTrades.reduce(
        (sum, trade) => sum + trade.quantity,
        0
      );
      const avgPrice =
        matchingResult.completedTrades.reduce(
          (sum, trade) => sum + trade.totalValue,
          0
        ) / totalMatched;

      description +=
        `\n\n🎉 **${matchingResult.matches} automatic matches found!**\n` +
        `• **${totalMatched.toLocaleString()}x** items purchased immediately\n` +
        `• Average price: **${formatPrice(Math.round(avgPrice * 10) / 10)} coins** each`;

      if (matchingResult.remainingQuantity > 0) {
        description += `\n• **${matchingResult.remainingQuantity.toLocaleString()}x** remaining in order book`;
      } else {
        description += `\n• Order **completely filled!**`;
      }
    }

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Buy Order Created")
      .setDescription(description);

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    await interaction.editReply({ embeds: [embed], components: [backButton] });

    // set up collector to handle the "Back to Market" button
    await setupMarketCollector(interaction, buyer);
  } catch (error) {
    console.error("[Create Buy Order] Error:", error);

    // handle specific error types
    if (error.message.includes("wait before performing")) {
      return interaction.editReply({
        content: "⏰ " + error.message,
      });
    }

    await interaction.editReply({
      content: "An error occurred while creating your buy order.",
    });
  }
}

async function createSellOrder(
  interaction,
  seller,
  itemKey,
  quantity,
  pricePerUnit
) {
  const { updateInventoryAtomically } = require("../utils/inventory");

  try {
    // check rate limiting (prevent spam order creation)
    checkRateLimit(seller.discordId, "create_sell_order", 3000); // 3 second cooldown

    // check sell order limit (8 max)
    const currentSellOrders = await dbAll(
      "SELECT COUNT(*) as count FROM market_sell_orders WHERE seller_id = ? AND quantity > 0",
      [interaction.user.id]
    );

    if (currentSellOrders[0]?.count >= MARKET_LIMITS.MAX_SELL_ORDERS) {
      return interaction.editReply({
        content: `❌ You can only have a maximum of **${MARKET_LIMITS.MAX_SELL_ORDERS} active sell orders**. Cancel some existing orders first.`,
      });
    }

    // validate that seller still has the items
    const sellerInventory = await getInventory(interaction.user.id);
    const currentAmount = sellerInventory.items[itemKey] || 0;

    if (currentAmount < quantity) {
      return interaction.editReply({
        content: `You only have ${currentAmount.toLocaleString()} of this item available.`,
      });
    }

    // remove items from seller's inventory first
    await updateInventoryAtomically(
      interaction.user.id,
      0, // no coins yet - will be added when orders match
      [{ itemKey, amount: -quantity }], // remove items being sold (negative amount)
      [], // no equipment additions
      [], // no equipment removals
      0 // no bank coins
    );

    // create the sell order in database
    const result = await dbRunQueued(
      "INSERT INTO market_sell_orders (seller_id, item_key, quantity, price_per_unit, original_quantity, created_at) VALUES (?, ?, ?, ?, ?, datetime('now'))",
      [interaction.user.id, itemKey, quantity, pricePerUnit, quantity]
    );

    const sellOrderId = result.lastID;

    // now process automatic matching for this new sell order
    const matchingResult = await processNewSellOrder(
      sellOrderId,
      interaction.user.id,
      itemKey,
      quantity,
      pricePerUnit,
      interaction.client
    );

    const allItems = configManager.getAllItems();
    const itemData = allItems[itemKey];
    const itemName = itemData?.name || itemKey;
    const itemEmoji = itemData?.emoji || "❓";

    let description =
      `Created Sell Order for **${quantity.toLocaleString()}x** ${itemEmoji} **${itemName}**\n` +
      `at **${formatPrice(pricePerUnit)} coins** each (**${formatPrice(quantity * pricePerUnit)} total**)`;

    if (matchingResult.matches > 0) {
      const totalMatched = matchingResult.completedTrades.reduce(
        (sum, trade) => sum + trade.quantity,
        0
      );
      const totalEarned = matchingResult.completedTrades.reduce(
        (sum, trade) => sum + trade.totalValue,
        0
      );
      const avgPrice = totalEarned / totalMatched;

      description +=
        `\n\n🎉 **${matchingResult.matches} automatic matches found!**\n` +
        `• **${totalMatched.toLocaleString()}x** items sold immediately\n` +
        `• Total earned: **${formatPrice(totalEarned)} coins**\n` +
        `• Average price: **${formatPrice(Math.round(avgPrice * 10) / 10)} coins** each`;

      if (matchingResult.remainingQuantity > 0) {
        description += `\n• **${matchingResult.remainingQuantity.toLocaleString()}x** remaining in order book`;
      } else {
        description += `\n• Order **completely filled!**`;
      }
    }

    const embed = new EmbedBuilder()
      .setColor(EMBED_COLORS.GREEN)
      .setTitle("Sell Order Created")
      .setDescription(description);

    const backButton = new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId("market_back")
        .setLabel("Back to Market")
        .setStyle(ButtonStyle.Secondary)
    );

    await interaction.editReply({ embeds: [embed], components: [backButton] });

    // set up collector to handle the "Back to Market" button
    await setupMarketCollector(interaction, seller);
  } catch (error) {
    console.error("[Create Sell Order] Error:", error);

    // handle specific error types
    if (error.message.includes("wait before performing")) {
      return interaction.editReply({
        content: "⏰ " + error.message,
      });
    }

    await interaction.editReply({
      content: "An error occurred while creating your sell order.",
    });
  }
}

/**
 * handles create buy order modal submission
 */
async function handleCreateBuyOrderModal(interaction, character) {
  await interaction.deferUpdate();

  const itemName = interaction.fields.getTextInputValue("item_name").trim();
  const quantityStr = interaction.fields.getTextInputValue("quantity");
  const priceStr = interaction.fields.getTextInputValue("price");

  // find item by name or key (case insensitive)
  const allItems = configManager.getAllItems();
  const searchTerm = itemName.toLowerCase();

  // first try to find by exact key match (case insensitive)
  let itemKey = Object.keys(allItems).find(
    (key) => key.toLowerCase() === searchTerm
  );

  // if not found by key, try by item name (case insensitive)
  if (!itemKey) {
    itemKey = Object.keys(allItems).find((key) => {
      const item = allItems[key];
      return item.name && item.name.toLowerCase() === searchTerm;
    });
  }

  // if still not found, try flexible matching (handles spaces, underscores, etc.)
  if (!itemKey) {
    const normalizedSearch = searchTerm.replace(/[_\s-]/g, "").toLowerCase();
    itemKey = Object.keys(allItems).find((key) => {
      const item = allItems[key];
      // check if key matches (normalize underscores/spaces)
      const normalizedKey = key.replace(/[_\s-]/g, "").toLowerCase();
      if (normalizedKey === normalizedSearch) return true;

      // check if name matches (normalize underscores/spaces)
      if (item.name) {
        const normalizedName = item.name.replace(/[_\s-]/g, "").toLowerCase();
        if (normalizedName === normalizedSearch) return true;
      }

      return false;
    });
  }

  if (!itemKey) {
    return interaction.editReply({
      content: `Item "${itemName}" not found. Please check the spelling and try again. You can use either the item name (e.g., "Rotten Flesh") or item key (e.g., "ROTTEN_FLESH").`,
    });
  }

  const quantity = parseInt(quantityStr.replace(/,/g, ""));
  const pricePerUnit = parsePrice(priceStr);

  if (isNaN(quantity) || quantity <= 0) {
    return interaction.editReply({
      content: "Invalid quantity. Please enter a positive number.",
    });
  }

  if (isNaN(pricePerUnit) || pricePerUnit <= 0) {
    return interaction.editReply({
      content:
        "Invalid price. Please enter a positive number with up to 1 decimal place.",
    });
  }

  // calculate total cost
  const totalCost = quantity * pricePerUnit;

  if ((character.coins || 0) < totalCost) {
    return interaction.editReply({
      content: `You need ${formatPrice(totalCost)} coins to place this buy order, but you only have ${formatPrice(character.coins || 0)}.`,
    });
  }

  try {
    // process the buy order
    if (allItems[itemKey]?.untradeable) {
      return interaction.editReply({
        content: "This item cannot be traded on the market.",
      });
    }
    await createBuyOrder(
      interaction,
      character,
      itemKey,
      quantity,
      pricePerUnit
    );
  } catch (error) {
    console.error("[Create Buy Order Modal] Error:", error);
    await interaction.editReply({
      content: "An error occurred while creating your buy order.",
    });
  }
}

/**
 * handles create sell order modal submission
 */
async function handleCreateSellOrderModal(interaction, character) {
  await interaction.deferUpdate();

  const itemKey = interaction.customId.split(":")[1];
  const quantityStr = interaction.fields.getTextInputValue("quantity");
  const priceStr = interaction.fields.getTextInputValue("price");

  // get player's inventory using proper inventory function
  const playerInventory = await getInventory(interaction.user.id);
  const playerInv = playerInventory.items || {};
  const availableQuantity = playerInv[itemKey] || 0;

  const allItems2 = configManager.getAllItems();
  if (allItems2[itemKey]?.untradeable) {
    return interaction.editReply({
      content: "This item cannot be traded on the market.",
    });
  }

  if (availableQuantity === 0) {
    return interaction.editReply({
      content: "You no longer have this item in your inventory.",
    });
  }

  const quantity = parseShorthandAmount(quantityStr, availableQuantity);
  const pricePerUnit = parsePrice(priceStr);

  if (isNaN(quantity) || quantity <= 0 || quantity > availableQuantity) {
    return interaction.editReply({
      content: `Invalid quantity. Please enter a number between 1 and ${availableQuantity.toLocaleString()}.`,
    });
  }

  if (isNaN(pricePerUnit) || pricePerUnit <= 0) {
    return interaction.editReply({
      content:
        "Invalid price. Please enter a positive number with up to 1 decimal place.",
    });
  }

  if (pricePerUnit > MARKET_LIMITS.MAX_PRICE_PER_ITEM) {
    return interaction.editReply({
      content: `Price too high! Maximum price per item is ${formatPrice(MARKET_LIMITS.MAX_PRICE_PER_ITEM)} coins.`,
    });
  }

  try {
    // process the sell order
    await createSellOrder(
      interaction,
      character,
      itemKey,
      quantity,
      pricePerUnit
    );
  } catch (error) {
    console.error("[Create Sell Order Modal] Error:", error);
    await interaction.editReply({
      content: "An error occurred while creating your sell order.",
    });
  }
}
