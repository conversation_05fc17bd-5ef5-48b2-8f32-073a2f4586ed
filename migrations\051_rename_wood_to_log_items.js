// Migration 051: Rename wood items to log items
// Purpose: Update item keys from *_WOOD to *_LOG format while preserving player inventories

const fs = require("fs");
const path = require("path");

// Helper function to run db.all as a Promise
function dbAllAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

// Helper function to run db.run as a Promise
function dbRunAsync(db, query, params = []) {
  return new Promise((resolve, reject) => {
    db.run(query, params, function (err) {
      if (err) reject(err);
      else resolve({ lastID: this.lastID, changes: this.changes });
    });
  });
}

// Safely parse JSON, returning null on error
function safeJsonParse(jsonString) {
  if (!jsonString) return null;
  try {
    return JSON.parse(jsonString);
  } catch {
    return null;
  }
}

module.exports = {
  version: 51,
  description:
    "Rename wood items to log items while preserving player inventories",

  async up(db) {
    console.log("[Migration 51] Starting: Rename wood items to log items...");

    // Define the mapping from old wood keys to new log keys
    const woodToLogMapping = {
      PALE_OAK_WOOD: "FIG_LOG",
      MANGROVE_WOOD: "MANGROVE_LOG",
      OAK_WOOD: "OAK_LOG",
      SPRUCE_WOOD: "SPRUCE_LOG",
      JUNGLE_WOOD: "JUNGLE_LOG",
      DARK_OAK_WOOD: "DARK_OAK_LOG",
      CHERRY_WOOD: "CHERRY_LOG",
      BIRCH_WOOD: "BIRCH_LOG",
      ACACIA_WOOD: "ACACIA_LOG",
      // Enchanted wood items
      ENCHANTED_OAK_WOOD: "ENCHANTED_OAK_LOG",
      ENCHANTED_SPRUCE_WOOD: "ENCHANTED_SPRUCE_LOG",
      ENCHANTED_JUNGLE_WOOD: "ENCHANTED_JUNGLE_LOG",
      ENCHANTED_BIRCH_WOOD: "ENCHANTED_BIRCH_LOG",
    };

    console.log(
      `[Migration 51] Will rename ${Object.keys(woodToLogMapping).length} wood items to log items`,
    );

    try {
      // Step 1: Update items.json file
      console.log("[Migration 51] Step 1: Updating items.json...");
      const itemsPath = path.join(__dirname, "../data/items.json");
      const backupPath = path.join(__dirname, "../data/items.json.bak.051");

      // Create backup
      if (!fs.existsSync(backupPath)) {
        fs.copyFileSync(itemsPath, backupPath);
        console.log(`[Migration 51] Backup created: ${backupPath}`);
      }

      // Read and update items.json
      const itemsData = JSON.parse(fs.readFileSync(itemsPath, "utf8"));
      const updatedItems = {};

      for (const [oldKey, itemData] of Object.entries(itemsData)) {
        if (woodToLogMapping[oldKey]) {
          const newKey = woodToLogMapping[oldKey];
          const updatedItem = { ...itemData };

          // Update the name (Wood -> Log, except for Fig)
          if (newKey === "FIG_LOG") {
            updatedItem.name = "Fig Log";
          } else {
            updatedItem.name = itemData.name.replace(" Wood", " Log");
          }

          // Update itemKey in drops array
          if (updatedItem.drops && Array.isArray(updatedItem.drops)) {
            updatedItem.drops = updatedItem.drops.map((drop) => {
              if (drop.itemKey === oldKey) {
                return { ...drop, itemKey: newKey };
              }
              return drop;
            });
          }

          updatedItems[newKey] = updatedItem;
          console.log(
            `[Migration 51] Renamed ${oldKey} -> ${newKey} (${updatedItem.name})`,
          );
        } else {
          updatedItems[oldKey] = itemData;
        }
      }

      // Write updated items.json
      fs.writeFileSync(
        itemsPath,
        JSON.stringify(updatedItems, null, 2),
        "utf8",
      );
      console.log("[Migration 51] items.json updated successfully");

      // Step 2: Update player inventories in player_inventory_items table
      console.log("[Migration 51] Step 2: Updating player inventories...");
      let inventoryUpdates = 0;

      for (const [oldKey, newKey] of Object.entries(woodToLogMapping)) {
        const result = await dbRunAsync(
          db,
          "UPDATE player_inventory_items SET item_name = ? WHERE item_name = ?",
          [newKey, oldKey],
        );
        if (result.changes > 0) {
          console.log(
            `[Migration 51] Updated ${result.changes} inventory entries: ${oldKey} -> ${newKey}`,
          );
          inventoryUpdates += result.changes;
        }
      }

      console.log(
        `[Migration 51] Total inventory updates: ${inventoryUpdates}`,
      );

      // Step 3: Update collections
      console.log("[Migration 51] Step 3: Updating collections...");
      const players = await dbAllAsync(
        db,
        "SELECT discord_id, collections_json FROM players WHERE collections_json IS NOT NULL",
      );
      let collectionUpdates = 0;

      for (const player of players) {
        const collections = safeJsonParse(player.collections_json);
        if (!collections) continue;

        let updated = false;
        for (const [oldKey, newKey] of Object.entries(woodToLogMapping)) {
          if (collections[oldKey] !== undefined) {
            collections[newKey] = collections[oldKey];
            delete collections[oldKey];
            updated = true;
          }
        }

        if (updated) {
          await dbRunAsync(
            db,
            "UPDATE players SET collections_json = ? WHERE discord_id = ?",
            [JSON.stringify(collections), player.discord_id],
          );
          collectionUpdates++;
        }
      }

      console.log(
        `[Migration 51] Updated collections for ${collectionUpdates} players`,
      );

      // Step 3.5: Update disblock milestones collections
      console.log("[Migration 51] Step 3.5: Updating disblock milestones...");
      const playersWithMilestones = await dbAllAsync(
        db,
        "SELECT discord_id, disblockMilestones FROM players WHERE disblockMilestones IS NOT NULL",
      );
      let milestoneUpdates = 0;

      for (const player of playersWithMilestones) {
        const milestones = safeJsonParse(player.disblockMilestones);
        if (!milestones || !milestones.collections) continue;

        let updated = false;
        for (const [oldKey, newKey] of Object.entries(woodToLogMapping)) {
          if (milestones.collections[oldKey] !== undefined) {
            milestones.collections[newKey] = milestones.collections[oldKey];
            delete milestones.collections[oldKey];
            updated = true;
          }
        }

        if (updated) {
          await dbRunAsync(
            db,
            "UPDATE players SET disblockMilestones = ? WHERE discord_id = ?",
            [JSON.stringify(milestones), player.discord_id],
          );
          milestoneUpdates++;
        }
      }

      console.log(
        `[Migration 51] Updated disblock milestones for ${milestoneUpdates} players`,
      );

      // Step 4: Update minion data
      console.log("[Migration 51] Step 4: Updating minion data...");
      const minions = await dbAllAsync(
        db,
        "SELECT discord_id, island_json, minion_storage_json, crafted_minions_json FROM players WHERE island_json IS NOT NULL OR minion_storage_json IS NOT NULL OR crafted_minions_json IS NOT NULL",
      );
      let minionUpdates = 0;

      for (const player of minions) {
        let playerUpdated = false;

        // Update placed minions in island_json
        if (player.island_json) {
          const islandData = safeJsonParse(player.island_json);
          if (
            islandData &&
            islandData.placedMinions &&
            Array.isArray(islandData.placedMinions)
          ) {
            let islandUpdated = false;
            for (const minion of islandData.placedMinions) {
              if (minion.storage && Array.isArray(minion.storage)) {
                for (const item of minion.storage) {
                  if (item.itemKey && woodToLogMapping[item.itemKey]) {
                    item.itemKey = woodToLogMapping[item.itemKey];
                    islandUpdated = true;
                  }
                }
              }
            }

            if (islandUpdated) {
              await dbRunAsync(
                db,
                "UPDATE players SET island_json = ? WHERE discord_id = ?",
                [JSON.stringify(islandData), player.discord_id],
              );
              playerUpdated = true;
            }
          }
        }

        // Update minion storage
        if (player.minion_storage_json) {
          const minionStorage = safeJsonParse(player.minion_storage_json);
          if (minionStorage && Array.isArray(minionStorage)) {
            let storageUpdated = false;
            for (const minion of minionStorage) {
              if (minion.storage && Array.isArray(minion.storage)) {
                for (const item of minion.storage) {
                  if (item.itemKey && woodToLogMapping[item.itemKey]) {
                    item.itemKey = woodToLogMapping[item.itemKey];
                    storageUpdated = true;
                  }
                }
              }
            }

            if (storageUpdated) {
              await dbRunAsync(
                db,
                "UPDATE players SET minion_storage_json = ? WHERE discord_id = ?",
                [JSON.stringify(minionStorage), player.discord_id],
              );
              playerUpdated = true;
            }
          }
        }

        // Update crafted minions
        if (player.crafted_minions_json) {
          const craftedMinions = safeJsonParse(player.crafted_minions_json);
          if (craftedMinions && typeof craftedMinions === "object") {
            let craftedUpdated = false;
            for (const [, tiers] of Object.entries(craftedMinions)) {
              if (typeof tiers === "object" && tiers !== null) {
                for (const [, minion] of Object.entries(tiers)) {
                  if (
                    minion &&
                    minion.storage &&
                    Array.isArray(minion.storage)
                  ) {
                    for (const item of minion.storage) {
                      if (item.itemKey && woodToLogMapping[item.itemKey]) {
                        item.itemKey = woodToLogMapping[item.itemKey];
                        craftedUpdated = true;
                      }
                    }
                  }
                }
              }
            }

            if (craftedUpdated) {
              await dbRunAsync(
                db,
                "UPDATE players SET crafted_minions_json = ? WHERE discord_id = ?",
                [JSON.stringify(craftedMinions), player.discord_id],
              );
              playerUpdated = true;
            }
          }
        }

        if (playerUpdated) {
          minionUpdates++;
        }
      }

      console.log(
        `[Migration 51] Updated minion data for ${minionUpdates} players`,
      );

      console.log("[Migration 51] Migration completed successfully!");
      console.log("[Migration 51] Summary:");
      console.log(
        `  - Updated ${Object.keys(woodToLogMapping).length} items in items.json`,
      );
      console.log(`  - Updated ${inventoryUpdates} inventory entries`);
      console.log(`  - Updated collections for ${collectionUpdates} players`);
      console.log(
        `  - Updated disblock milestones for ${milestoneUpdates} players`,
      );
      console.log(`  - Updated minion data for ${minionUpdates} players`);
    } catch (error) {
      console.error("[Migration 51] Migration failed:", error);
      throw error;
    }
  },

  async down() {
    console.log(
      "[Migration 51] Rollback not implemented - restore from backup if needed",
    );
    // Rollback would be complex and risky, better to restore from backup
  },
};
