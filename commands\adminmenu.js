const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  MessageFlags,
} = require("discord.js");
const { getPlayerData } = require("../utils/playerDataManager");
const { checkRankPermission } = require("../utils/permissionUtils");
const commandToggleManager = require("../utils/commandToggleManager");
const { EMBED_COLORS } = require("../gameConfig");

module.exports = {
  data: new SlashCommandBuilder()
    .setName("adminmenu")
    .setDescription("[Admin] Access the admin menu to manage game systems."),

  async execute(interaction) {
    // check if user is admin/owner
    const character = await getPlayerData(interaction.user.id);
    if (!character) {
      return interaction.reply({
        content: "Could not find your character data.",
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (!checkRankPermission(character, "ADMIN")) {
      return interaction.reply({
        content:
          "You do not have permission to use this command. (Rank Required: ADMIN)",
        flags: [MessageFlags.Ephemeral],
      });
    }

    // show main admin menu
    await showMainMenu(interaction);
  },
};

async function showMainMenu(interaction) {
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle("Admin Menu")
    .setDescription("Hello world");

  const row = new ActionRowBuilder().addComponents(
    new ButtonBuilder()
      .setCustomId(`adminmenu_players_${interaction.user.id}`)
      .setLabel("Players")
      .setStyle(ButtonStyle.Primary),
    new ButtonBuilder()
      .setCustomId(`adminmenu_systems_${interaction.user.id}`)
      .setLabel("Systems")
      .setStyle(ButtonStyle.Primary)
  );

  if (interaction.replied || interaction.deferred) {
    await interaction.editReply({
      embeds: [embed],
      components: [row],
    });
  } else {
    await interaction.reply({
      embeds: [embed],
      components: [row],
    });
  }
}

async function showSystemsMenu(interaction) {
  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.BLUE)
    .setTitle("Admin Menu")
    .setDescription("Select systems to toggle");

  // get all commands and their current states
  const allCommands = commandToggleManager.getAllCommands();
  const commandEntries = Object.entries(allCommands).sort(([a], [b]) =>
    a.localeCompare(b)
  );

  // create select menu options
  const options = commandEntries.map(([commandName, isDisabled]) => ({
    label: commandName,
    value: commandName,
    description: isDisabled ? "Currently disabled" : "Currently enabled",
    default: !isDisabled, // selected if enabled
  }));

  // split into chunks of 25 (discord limit)
  const selectMenus = [];
  for (let i = 0; i < options.length; i += 25) {
    const chunk = options.slice(i, i + 25);
    selectMenus.push(
      new StringSelectMenuBuilder()
        .setCustomId(`adminmenu_toggle_${i}_${interaction.user.id}`)
        .setPlaceholder(
          `Select commands to enable (${i + 1}-${Math.min(i + 25, options.length)})`
        )
        .setMinValues(0)
        .setMaxValues(chunk.length)
        .addOptions(chunk)
    );
  }

  const components = [];

  // add select menus
  selectMenus.forEach((menu) => {
    components.push(new ActionRowBuilder().addComponents(menu));
  });

  // add back button
  components.push(
    new ActionRowBuilder().addComponents(
      new ButtonBuilder()
        .setCustomId(`adminmenu_back_${interaction.user.id}`)
        .setLabel("Back")
        .setStyle(ButtonStyle.Secondary)
    )
  );

  await interaction.editReply({
    embeds: [embed],
    components: components,
  });
}

// handle button interactions for admin menu
async function handleAdminMenuInteraction(interaction) {
  const [, action, ...rest] = interaction.customId.split("_");
  const userId = rest[rest.length - 1]; // user id is always last

  // verify the user clicking is either the original user or an admin/owner
  const character = await getPlayerData(interaction.user.id);
  if (!character) {
    await interaction.reply({
      content: "Could not find your character data.",
      flags: [MessageFlags.Ephemeral],
    });
    return true; // handled
  }

  const isOriginalUser = interaction.user.id === userId;
  const isAdmin = checkRankPermission(character, "ADMIN");

  if (!isOriginalUser && !isAdmin) {
    await interaction.reply({
      content: "You can only interact with your own admin menu.",
      flags: [MessageFlags.Ephemeral],
    });
    return true; // handled
  }

  switch (action) {
    case "players":
      // placeholder for future players management
      await interaction.reply({
        content: "Players management coming soon!",
        flags: [MessageFlags.Ephemeral],
      });
      return true;

    case "systems":
      await interaction.deferUpdate();
      await showSystemsMenu(interaction);
      return true;

    case "back":
      await interaction.deferUpdate();
      await showMainMenu(interaction);
      return true;

    case "toggle":
      await interaction.deferUpdate();
      await handleCommandToggle(interaction, rest);
      return true;

    default:
      await interaction.reply({
        content: "Unknown admin menu action.",
        flags: [MessageFlags.Ephemeral],
      });
      return true;
  }
}

async function handleCommandToggle(interaction, rest) {
  const chunkIndex = parseInt(rest[0]);

  // get selected values (these are the commands that should be ENABLED)
  const selectedCommands = interaction.values || [];

  // get all commands in this chunk
  const allCommands = commandToggleManager.getAllCommands();
  const commandEntries = Object.entries(allCommands).sort(([a], [b]) =>
    a.localeCompare(b)
  );
  const chunkCommands = commandEntries
    .slice(chunkIndex, chunkIndex + 25)
    .map(([name]) => name);

  // prepare the new state
  const newStates = {};
  for (const commandName of chunkCommands) {
    // if command is in selectedCommands, it should be enabled (false)
    // if command is not in selectedCommands, it should be disabled (true)
    newStates[commandName] = !selectedCommands.includes(commandName);
  }

  // apply the changes
  commandToggleManager.toggleMultipleCommands(newStates);

  // refresh the systems menu to show updated states
  await showSystemsMenu(interaction);

  // send a confirmation message
  const enabledCount = selectedCommands.length;
  const disabledCount = chunkCommands.length - enabledCount;

  await interaction.followUp({
    content: `✅ Updated command states: ${enabledCount} enabled, ${disabledCount} disabled`,
    flags: [MessageFlags.Ephemeral],
  });
}

// export the interaction handler for bot.js to use
module.exports.handleAdminMenuInteraction = handleAdminMenuInteraction;
