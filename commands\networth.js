const {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  StringSelectMenuBuilder,
  ComponentType,
} = require("discord.js");
const {
  getPlayerData,
  safeJsonParse,
} = require("../utils/playerDataManager.js");
const { formatDisblockLevel } = require("../utils/disblockXpSystem");
const configManager = require("../utils/configManager");
const {
  calculatePetValue,
  calculateMinionValue,
} = require("../utils/networthCalculator");
const { dbAll } = require("../utils/dbUtils");
const { ENCHANTMENTS, EMBED_COLORS } = require("../gameConfig");

function getItemValueDetailed(
  itemObject,
  allItems,
  enchantmentsData,
  isPet = false
) {
  const itemKeyToLookup = itemObject.item_key || itemObject.itemKey;
  if (!itemKeyToLookup) return { value: 0, details: "No item key" };

  const itemDef = allItems[itemKeyToLookup];
  if (!itemDef) return { value: 0, details: "Item not found" };

  const { getItemValue } = require("../utils/networthCalculator");

  const totalValue = getItemValue(
    itemObject,
    allItems,
    enchantmentsData,
    isPet
  );

  return {
    value: totalValue,
    itemDef: itemDef,
    details: "Calculated value",
  };
}

async function getNetworthBreakdown(discordId) {
  try {
    let allItems = configManager.getAllItems();

    if (Object.keys(allItems).length === 0) {
      await configManager.initialize();
      allItems = configManager.getAllItems();
    }

    const playerData = await dbAll(
      "SELECT coins, bank, pets_json, island_json, minion_storage_json, cakeBag, pet_upgrade_json FROM players WHERE discord_id = ?",
      [discordId]
    );
    if (!playerData || playerData.length === 0)
      return { total: 0, breakdown: [] };

    const player = playerData[0];
    const breakdown = [];

    const purseCoins = player.coins || 0;
    const bankCoins = player.bank || 0;

    if (purseCoins > 0) {
      breakdown.push({
        category: "coins",
        name: "Purse",
        value: purseCoins,
        emoji: "<:purse_coins:1367849116033482772>",
        rarity: "SPECIAL",
      });
    }

    if (bankCoins > 0) {
      breakdown.push({
        category: "coins",
        name: "Bank",
        value: bankCoins,
        emoji: "<:bank_coins:1367849114242383987>",
        rarity: "SPECIAL",
      });
    }

    const playerEquipment = await dbAll(
      "SELECT equipment_id, item_key, is_equipped, data_json FROM player_equipment WHERE discord_id = ?",
      [discordId]
    );
    playerEquipment.forEach((equipItem) => {
      const itemDetails = getItemValueDetailed(
        equipItem,
        allItems,
        ENCHANTMENTS
      );
      if (itemDetails.value > 0 && itemDetails.itemDef) {
        let displayName = itemDetails.itemDef.name;
        let uuid = null;

        if (equipItem.data_json) {
          try {
            const dataJson =
              typeof equipItem.data_json === "string"
                ? JSON.parse(equipItem.data_json)
                : equipItem.data_json;
            if (dataJson.reforge) {
              const {
                getDynamicReforgeName,
              } = require("../utils/dynamicReforgeStats");
              const dynamicReforgeName = getDynamicReforgeName(
                dataJson.reforge,
                itemDetails.itemDef
              );
              displayName = `${dynamicReforgeName} ${displayName}`;
            }
          } catch (error) {
            console.error("Error parsing equipment reforge data:", error);
          }
        }

        if (equipItem.equipment_id && itemDetails.itemDef.unique) {
          uuid = equipItem.equipment_id.slice(0, 4);
        }

        let category = "items";
        if (itemDetails.itemDef.type === "WEAPON") {
          category = "weapons";
        } else if (itemDetails.itemDef.type === "ARMOR") {
          category = "armor";
        } else if (itemDetails.itemDef.type === "TOOL") {
          category = "tools";
        }

        breakdown.push({
          category: category,
          name: displayName,
          value: itemDetails.value,
          emoji: itemDetails.itemDef.emoji || "❓",
          rarity: itemDetails.itemDef.rarity?.name?.toUpperCase() || "COMMON",
          uuid: uuid,
        });
      }
    });

    const playerAccessories = await dbAll(
      "SELECT item_key, is_equipped, data_json, accessory_id FROM player_accessories WHERE discord_id = ?",
      [discordId]
    );
    playerAccessories.forEach((accessory) => {
      const itemDetails = getItemValueDetailed(
        accessory,
        allItems,
        ENCHANTMENTS
      );
      if (itemDetails.value > 0 && itemDetails.itemDef) {
        let displayName = itemDetails.itemDef.name;
        let uuid = null;

        if (accessory.data_json) {
          try {
            const dataJson =
              typeof accessory.data_json === "string"
                ? JSON.parse(accessory.data_json)
                : accessory.data_json;
            if (dataJson.reforge) {
              const {
                getDynamicReforgeName,
              } = require("../utils/dynamicReforgeStats");
              const dynamicReforgeName = getDynamicReforgeName(
                dataJson.reforge,
                itemDetails.itemDef
              );
              displayName = `${dynamicReforgeName} ${displayName}`;
            }
            if (dataJson.uuid && itemDetails.itemDef.unique) {
              uuid = dataJson.uuid.slice(0, 4);
            }
          } catch (error) {
            console.error("Error parsing accessory data:", error);
          }
        }

        if (!uuid && accessory.accessory_id) {
          uuid = accessory.accessory_id.slice(0, 4);
        }

        breakdown.push({
          category: "accessories",
          name: displayName,
          value: itemDetails.value,
          emoji: itemDetails.itemDef.emoji || "❓",
          rarity: itemDetails.itemDef.rarity?.name?.toUpperCase() || "COMMON",
          uuid: uuid,
        });
      }
    });

    const playerStackableItems = await dbAll(
      "SELECT item_name, amount FROM player_inventory_items WHERE discord_id = ?",
      [discordId]
    );
    playerStackableItems.forEach((stackableItem) => {
      const itemDef = allItems[stackableItem.item_name];
      if (
        itemDef &&
        typeof itemDef.sellPrice === "number" &&
        stackableItem.amount > 0
      ) {
        const totalValue = itemDef.sellPrice * stackableItem.amount;
        breakdown.push({
          category: "items",
          name: `${itemDef.name} x${stackableItem.amount.toLocaleString()}`,
          value: totalValue,
          emoji: itemDef.emoji || "❓",
          rarity: itemDef.rarity?.name?.toUpperCase() || "COMMON",
        });
      }
    });

    const pets = safeJsonParse(player.pets, []);
    if (Array.isArray(pets)) {
      pets.forEach((pet) => {
        if (pet && (pet.itemKey || pet.petKey) && pet.rarity) {
          const normalizedPet = { ...pet, itemKey: pet.itemKey || pet.petKey };
          const petValue = calculatePetValue(normalizedPet, allItems);
          if (petValue > 0) {
            const petItemKey = pet.itemKey || pet.petKey;
            const itemDef = allItems[petItemKey];
            const petLevel = pet.level || 1;

            breakdown.push({
              category: "pets",
              name: `${pet.rarity} ${itemDef?.name || petItemKey} Lvl ${petLevel}`,
              value: petValue,
              emoji: itemDef?.emoji || "🐾",
              rarity: pet.rarity,
              uuid: pet.id ? pet.id.slice(0, 4) : null,
            });
          }
        }
      });
    }

    const islandData = safeJsonParse(player.island, {});
    const placedMinions = islandData.placedMinions || [];
    placedMinions.forEach((minion) => {
      if (minion && minion.itemKey && minion.tier) {
        const minionValue = calculateMinionValue(minion, allItems);
        if (minionValue > 0) {
          const minionItem = allItems[minion.itemKey];
          const minionName = minionItem ? minionItem.name : minion.itemKey;
          let displayName = `${minionName} (Tier ${minion.tier})`;
          let uuid = null;

          if (minion.id) {
            uuid = minion.id.slice(0, 4);
            displayName += ` \`${uuid}\``;
          }

          breakdown.push({
            category: "minions",
            name: displayName,
            value: minionValue,
            emoji: minionItem ? minionItem.emoji || "⚙️" : "⚙️",
            rarity: minionItem
              ? minionItem.rarity?.name?.toUpperCase() || "COMMON"
              : "COMMON",
            uuid: uuid,
          });
        }
      }
    });

    const minionStorage = safeJsonParse(player.minionStorage, []);
    minionStorage.forEach((minion) => {
      if (minion && minion.itemKey && minion.tier) {
        const minionValue = calculateMinionValue(minion, allItems);
        if (minionValue > 0) {
          const minionItem = allItems[minion.itemKey];
          const minionName = minionItem ? minionItem.name : minion.itemKey;
          let displayName = `${minionName} (Tier ${minion.tier})`;
          let uuid = null;

          if (minion.id) {
            uuid = minion.id.slice(0, 4);
            displayName += ` \`${uuid}\``;
          }

          breakdown.push({
            category: "minions",
            name: displayName,
            value: minionValue,
            emoji: minionItem ? minionItem.emoji || "⚙️" : "⚙️",
            rarity: minionItem
              ? minionItem.rarity?.name?.toUpperCase() || "COMMON"
              : "COMMON",
            uuid: uuid,
          });
        }
      }
    });

    breakdown.sort((a, b) => b.value - a.value);

    const total = breakdown.reduce((sum, item) => sum + item.value, 0);

    return { total, breakdown };
  } catch (error) {
    console.error("[Networth] Error calculating breakdown:", error);
    return { total: 0, breakdown: [] };
  }
}

function filterBreakdownByCategory(breakdown, categories) {
  if (!Array.isArray(categories)) {
    if (categories === "all") return breakdown;
    return breakdown.filter((item) => item.category === categories);
  }
  return breakdown.filter((item) => categories.includes(item.category));
}

function createNetworthEmbed(
  character,
  breakdown,
  categories = [
    "weapons",
    "armor",
    "tools",
    "items",
    "accessories",
    "pets",
    "minions",
    "coins",
  ],
  page = 0
) {
  const disblockLevel = formatDisblockLevel(character.disblock_xp || 0);
  const username = character.name || "Unknown";

  const filteredBreakdown = filterBreakdownByCategory(breakdown, categories);
  const totalValue = filteredBreakdown.reduce(
    (sum, item) => sum + item.value,
    0
  );

  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredBreakdown.length / itemsPerPage) || 1;
  const currentPage = Math.max(0, Math.min(page, totalPages - 1));
  const startIdx = currentPage * itemsPerPage;
  const pageItems = filteredBreakdown.slice(startIdx, startIdx + itemsPerPage);

  const categoryNames = {
    all: "All Items",
    weapons: "Weapons",
    armor: "Armor",
    tools: "Tools",
    items: "Items",
    accessories: "Accessories",
    pets: "Pets",
    minions: "Minions",
    coins: "Coins",
  };

  let categoryText = "All Items";
  if (Array.isArray(categories)) {
    if (categories.length === 8) {
      categoryText = "All Items";
    } else {
      categoryText = categories.map((cat) => categoryNames[cat]).join(", ");
    }
  } else {
    categoryText = categoryNames[categories] || categories;
  }

  const embed = new EmbedBuilder()
    .setColor(EMBED_COLORS.GOLD)
    .setTitle(`[${Math.floor(disblockLevel)}] ${username}'s Networth Breakdown`)
    .setDescription(
      `**Category:** ${categoryText}\n**Total Value:** <:purse_coins:1367849116033482772> **${totalValue.toLocaleString()}**`
    );

  if (pageItems.length > 0) {
    const itemList = pageItems
      .map((item, index) => {
        const position = startIdx + index + 1;
        const uuidText = item.uuid ? ` \`${item.uuid}\`` : "";
        return `**${position}.** ${item.emoji} **${item.name}${uuidText}**: **${item.value.toLocaleString()}**`;
      })
      .join("\n");

    embed.addFields({
      name: "\u200B",
      value: itemList,
      inline: false,
    });
  } else {
    embed.addFields({
      name: "No Items",
      value: "No items found in this category.",
      inline: false,
    });
  }

  embed.setFooter({ text: `Page ${currentPage + 1}/${totalPages}` });

  return { embed, totalPages, currentPage };
}

module.exports = {
  data: new SlashCommandBuilder()
    .setName("networth")
    .setDescription("View your networth breakdown by categories"),

  getNetworthBreakdown,

  async execute(interaction) {
    await interaction.deferReply();

    const userId = interaction.user.id;
    const character = await getPlayerData(userId);

    if (!character) {
      return interaction.editReply({
        content:
          "You don't have a character yet! Visit the setup channel to create one.",
      });
    }

    const { breakdown } = await getNetworthBreakdown(userId);

    if (breakdown.length === 0) {
      return interaction.editReply({
        content: "You don't have any items with value yet!",
      });
    }

    let selectedCategories = [
      "weapons",
      "armor",
      "tools",
      "items",
      "accessories",
      "pets",
      "minions",
      "coins",
    ];
    let currentPage = 0;

    const { embed, totalPages } = createNetworthEmbed(
      character,
      breakdown,
      selectedCategories,
      currentPage
    );

    const categorySelect = new StringSelectMenuBuilder()
      .setCustomId("networth_category")
      .setPlaceholder("Select categories to filter...")
      .setMinValues(1)
      .setMaxValues(8)
      .addOptions([
        {
          label: "Weapons",
          description: "Show all weapons",
          value: "weapons",
          emoji: "<:stone_sword:1369433285582786722>",
          default: true,
        },
        {
          label: "Armor",
          description: "Show all armor pieces",
          value: "armor",
          emoji: "<:diamond_chestplate:1375560534359019520>",
          default: true,
        },
        {
          label: "Tools",
          description: "Show all tools",
          value: "tools",
          emoji: "<:diamond_pickaxe:1373220304586801252>",
          default: true,
        },
        {
          label: "Items",
          description: "Show stackable items",
          value: "items",
          emoji: "📦",
          default: true,
        },
        {
          label: "Accessories",
          description: "Show all accessories",
          value: "accessories",
          emoji: "<:accessory_bag:1384159284123664516>",
          default: true,
        },
        {
          label: "Pets",
          description: "Show all pets",
          value: "pets",
          emoji: "<:pet_squid:1370249324650565715> ",
          default: true,
        },
        {
          label: "Minions",
          description: "Show all minions",
          value: "minions",
          emoji: "<:minion_cobblestone:1367698365449506826>",
          default: true,
        },
        {
          label: "Coins",
          description: "Show purse and bank coins",
          value: "coins",
          emoji: "<:purse_coins:1367849116033482772>",
          default: true,
        },
      ]);

    const prevButton = new ButtonBuilder()
      .setCustomId("networth_prev")
      .setLabel("Prev")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage === 0);

    const nextButton = new ButtonBuilder()
      .setCustomId("networth_next")
      .setLabel("Next")
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(currentPage >= totalPages - 1);

    const components = [
      new ActionRowBuilder().addComponents(categorySelect),
      new ActionRowBuilder().addComponents(prevButton, nextButton),
    ];

    const response = await interaction.editReply({
      embeds: [embed],
      components: components,
    });

    const selectCollector = response.createMessageComponentCollector({
      componentType: ComponentType.StringSelect,
      time: 300000, // 5 minutes
    });

    const buttonCollector = response.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: 300000, // 5 minutes
    });

    selectCollector.on("collect", async (selectInteraction) => {
      if (selectInteraction.user.id !== userId) {
        return selectInteraction.reply({
          content: "You can only manage your own networth display!",
          ephemeral: true,
        });
      }

      await selectInteraction.deferUpdate();

      selectedCategories = selectInteraction.values;
      currentPage = 0;

      const { embed: newEmbed, totalPages: newTotalPages } =
        createNetworthEmbed(
          character,
          breakdown,
          selectedCategories,
          currentPage
        );

      const newCategorySelect = new StringSelectMenuBuilder()
        .setCustomId("networth_category")
        .setPlaceholder("Select categories to filter...")
        .setMinValues(1)
        .setMaxValues(8)
        .addOptions([
          {
            label: "Weapons",
            description: "Show all weapons",
            value: "weapons",
            emoji: "<:stone_sword:1369433285582786722>",
            default: selectedCategories.includes("weapons"),
          },
          {
            label: "Armor",
            description: "Show all armor pieces",
            value: "armor",
            emoji: "<:diamond_chestplate:1375560534359019520>",
            default: selectedCategories.includes("armor"),
          },
          {
            label: "Tools",
            description: "Show all tools",
            value: "tools",
            emoji: "<:diamond_pickaxe:1373220304586801252>",
            default: selectedCategories.includes("tools"),
          },
          {
            label: "Items",
            description: "Show stackable items",
            value: "items",
            emoji: "📦",
            default: selectedCategories.includes("items"),
          },
          {
            label: "Accessories",
            description: "Show all accessories",
            value: "accessories",
            emoji: "<:accessory_bag:1384159284123664516>",
            default: selectedCategories.includes("accessories"),
          },
          {
            label: "Pets",
            description: "Show all pets",
            value: "pets",
            emoji: "<:pet_squid:1370249324650565715> ",
            default: selectedCategories.includes("pets"),
          },
          {
            label: "Minions",
            description: "Show all minions",
            value: "minions",
            emoji: "<:minion_cobblestone:1367698365449506826>",
            default: selectedCategories.includes("minions"),
          },
          {
            label: "Coins",
            description: "Show purse and bank coins",
            value: "coins",
            emoji: "<:purse_coins:1367849116033482772>",
            default: selectedCategories.includes("coins"),
          },
        ]);

      const newPrevButton = ButtonBuilder.from(prevButton).setDisabled(
        currentPage === 0
      );
      const newNextButton = ButtonBuilder.from(nextButton).setDisabled(
        currentPage >= newTotalPages - 1
      );

      const newComponents = [
        new ActionRowBuilder().addComponents(newCategorySelect),
        new ActionRowBuilder().addComponents(newPrevButton, newNextButton),
      ];

      await selectInteraction.editReply({
        embeds: [newEmbed],
        components: newComponents,
      });
    });

    buttonCollector.on("collect", async (buttonInteraction) => {
      if (buttonInteraction.user.id !== userId) {
        return buttonInteraction.reply({
          content: "You can only manage your own networth display!",
          ephemeral: true,
        });
      }

      await buttonInteraction.deferUpdate();

      if (buttonInteraction.customId === "networth_prev") {
        currentPage = Math.max(0, currentPage - 1);
      } else if (buttonInteraction.customId === "networth_next") {
        const filteredBreakdown = filterBreakdownByCategory(
          breakdown,
          selectedCategories
        );
        const maxPages = Math.ceil(filteredBreakdown.length / 10) || 1;
        currentPage = Math.min(maxPages - 1, currentPage + 1);
      }

      const { embed: newEmbed, totalPages: newTotalPages } =
        createNetworthEmbed(
          character,
          breakdown,
          selectedCategories,
          currentPage
        );

      const currentCategorySelect = new StringSelectMenuBuilder()
        .setCustomId("networth_category")
        .setPlaceholder("Select categories to filter...")
        .setMinValues(1)
        .setMaxValues(8)
        .addOptions([
          {
            label: "Weapons",
            description: "Show all weapons",
            value: "weapons",
            emoji: "<:stone_sword:1369433285582786722>",
            default: selectedCategories.includes("weapons"),
          },
          {
            label: "Armor",
            description: "Show all armor pieces",
            value: "armor",
            emoji: "<:diamond_chestplate:1375560534359019520>",
            default: selectedCategories.includes("armor"),
          },
          {
            label: "Tools",
            description: "Show all tools",
            value: "tools",
            emoji: "<:diamond_pickaxe:1373220304586801252>",
            default: selectedCategories.includes("tools"),
          },
          {
            label: "Items",
            description: "Show stackable items",
            value: "items",
            emoji: "📦",
            default: selectedCategories.includes("items"),
          },
          {
            label: "Accessories",
            description: "Show all accessories",
            value: "accessories",
            emoji: "<:accessory_bag:1384159284123664516>",
            default: selectedCategories.includes("accessories"),
          },
          {
            label: "Pets",
            description: "Show all pets",
            value: "pets",
            emoji: "<:pet_squid:1370249324650565715> ",
            default: selectedCategories.includes("pets"),
          },
          {
            label: "Minions",
            description: "Show all minions",
            value: "minions",
            emoji: "<:minion_cobblestone:1367698365449506826>",
            default: selectedCategories.includes("minions"),
          },
          {
            label: "Coins",
            description: "Show purse and bank coins",
            value: "coins",
            emoji: "<:purse_coins:1367849116033482772>",
            default: selectedCategories.includes("coins"),
          },
        ]);

      const newPrevButton = ButtonBuilder.from(prevButton).setDisabled(
        currentPage === 0
      );
      const newNextButton = ButtonBuilder.from(nextButton).setDisabled(
        currentPage >= newTotalPages - 1
      );

      const newComponents = [
        new ActionRowBuilder().addComponents(currentCategorySelect),
        new ActionRowBuilder().addComponents(newPrevButton, newNextButton),
      ];

      await buttonInteraction.editReply({
        embeds: [newEmbed],
        components: newComponents,
      });
    });

    const handleCollectorEnd = () => {
      const disabledComponents = components.map((row) => {
        const newRow = ActionRowBuilder.from(row);
        newRow.components.forEach((component) => {
          component.setDisabled(true);
        });
        return newRow;
      });

      interaction.editReply({ components: disabledComponents }).catch(() => {});
    };

    selectCollector.on("end", handleCollectorEnd);
    buttonCollector.on("end", handleCollectorEnd);
  },
};
