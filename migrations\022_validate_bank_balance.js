// Migration Script: 022_validate_bank_balance.js
// Purpose: Ensures the player bank balance is stored as a number,
//          correcting any non-numeric values found (e.g., strings from past bugs).

// Function to perform the migration
async function up(db, playerDataUtils) {
  const { getPlayerData, savePlayerData, getAllPlayerIds } = playerDataUtils;

  console.log("--- Migration 022 START --- Validating bank balance types...");
  let processedCount = 0;
  let correctedPlayerCount = 0;
  let errorCount = 0;

  const playerIds = await getAllPlayerIds();
  if (!playerIds || playerIds.length === 0) {
    console.log(
      "--- Migration 022 INFO --- No player IDs found. Migration step 022 complete.",
    );
    return;
  }
  console.log(
    `--- Migration 022 INFO --- Found ${playerIds.length} players to check.`,
  );

  for (const userId of playerIds) {
    processedCount++;
    let playerDataChanged = false;
    try {
      const characterData = await getPlayerData(userId);
      // Skip if player has no data or no currencies object
      if (
        !characterData ||
        typeof characterData.currencies !== "object" ||
        characterData.currencies === null
      ) {
        continue;
      }

      const currentBankValue = characterData.currencies.bank;
      const currentValueType = typeof currentBankValue;

      if (currentValueType !== "number") {
        console.warn(
          `  - Player ${userId}: Found non-numeric bank balance. Type: ${currentValueType}, Value:`,
          currentBankValue,
        );
        let correctedValue = 0; // Default to 0 on error

        // Attempt to parse an integer from the beginning of the value if it's a string
        if (currentValueType === "string") {
          const parsedInt = parseInt(currentBankValue, 10);
          if (!isNaN(parsedInt)) {
            correctedValue = parsedInt;
            console.log(`    - Parsed integer ${correctedValue} from string.`);
          } else {
            console.error(
              "    - Failed to parse integer from string value. Resetting to 0.",
            );
          }
        } else {
          console.error(
            "    - Bank value is not a string or number. Resetting to 0.",
          );
        }

        characterData.currencies.bank = correctedValue;
        playerDataChanged = true;
      }

      // Save player data ONLY if changes were made
      if (playerDataChanged) {
        console.log(
          `  - Player ${userId}: Corrected bank balance to ${characterData.currencies.bank}. Saving...`,
        );
        await savePlayerData(userId, characterData);
        console.log(`  - Player ${userId}: Save successful.`);
        correctedPlayerCount++;
      }
    } catch (error) {
      console.error(
        `  - ERROR processing player ${userId} for migration 022:`,
        error,
      );
      errorCount++;
    }
  }

  console.log(
    `--- Migration 022 END --- Players Processed: ${processedCount}, Players Corrected: ${correctedPlayerCount}, Errors: ${errorCount}`,
  );
}

// Export the migration function
module.exports = { up }; // Only exporting 'up' as 'down' isn't practical
